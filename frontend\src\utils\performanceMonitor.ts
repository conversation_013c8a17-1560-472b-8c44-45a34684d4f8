/**
 * 性能监控工具
 * 监控API调用性能、缓存命中率和错误率
 */

import type { PerformanceMetrics } from '../types/apiEnhanced'

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    apiCalls: 0,
    averageResponseTime: 0,
    errorRate: 0,
    cacheHitRate: 0,
    slowQueries: []
  }

  private responseTimes: number[] = []
  private errorCount = 0
  private cacheHits = 0
  private cacheMisses = 0
  private maxSlowQueries = 50

  /**
   * 记录API调用
   * @param url 请求URL
   * @param responseTime 响应时间（毫秒）
   * @param fromCache 是否来自缓存
   * @param hasError 是否有错误
   */
  recordApiCall(url: string, responseTime: number, fromCache: boolean = false, hasError: boolean = false): void {
    this.metrics.apiCalls++

    // 记录响应时间
    this.responseTimes.push(responseTime)
    this.updateAverageResponseTime()

    // 记录缓存统计
    if (fromCache) {
      this.cacheHits++
    } else {
      this.cacheMisses++
    }
    this.updateCacheHitRate()

    // 记录错误统计
    if (hasError) {
      this.errorCount++
    }
    this.updateErrorRate()

    // 记录慢查询
    if (responseTime > 2000) { // 超过2秒的请求
      this.recordSlowQuery(url, responseTime)
    }
  }

  /**
   * 更新平均响应时间
   */
  private updateAverageResponseTime(): void {
    if (this.responseTimes.length === 0) {
      this.metrics.averageResponseTime = 0
      return
    }

    const sum = this.responseTimes.reduce((acc, time) => acc + time, 0)
    this.metrics.averageResponseTime = Math.round(sum / this.responseTimes.length)
  }

  /**
   * 更新缓存命中率
   */
  private updateCacheHitRate(): void {
    const totalCacheRequests = this.cacheHits + this.cacheMisses
    if (totalCacheRequests === 0) {
      this.metrics.cacheHitRate = 0
      return
    }

    this.metrics.cacheHitRate = Math.round((this.cacheHits / totalCacheRequests) * 100) / 100
  }

  /**
   * 更新错误率
   */
  private updateErrorRate(): void {
    if (this.metrics.apiCalls === 0) {
      this.metrics.errorRate = 0
      return
    }

    this.metrics.errorRate = Math.round((this.errorCount / this.metrics.apiCalls) * 100) / 100
  }

  /**
   * 记录慢查询
   * @param url 请求URL
   * @param responseTime 响应时间
   */
  private recordSlowQuery(url: string, responseTime: number): void {
    const slowQuery = {
      url,
      responseTime,
      timestamp: Date.now()
    }

    this.metrics.slowQueries.unshift(slowQuery)

    // 限制慢查询记录数量
    if (this.metrics.slowQueries.length > this.maxSlowQueries) {
      this.metrics.slowQueries = this.metrics.slowQueries.slice(0, this.maxSlowQueries)
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 获取详细统计信息
   */
  getDetailedStats(): Record<string, any> {
    const now = Date.now()
    const oneHourAgo = now - 60 * 60 * 1000

    // 最近一小时的慢查询
    const recentSlowQueries = this.metrics.slowQueries.filter(
      query => query.timestamp > oneHourAgo
    )

    // 响应时间分布
    const responseTimeDistribution = this.getResponseTimeDistribution()

    // 最近的响应时间趋势
    const recentResponseTimes = this.responseTimes.slice(-20)

    return {
      ...this.metrics,
      recentSlowQueries,
      responseTimeDistribution,
      recentResponseTimes,
      totalCacheRequests: this.cacheHits + this.cacheMisses,
      cacheHits: this.cacheHits,
      cacheMisses: this.cacheMisses,
      errorCount: this.errorCount,
      successCount: this.metrics.apiCalls - this.errorCount
    }
  }

  /**
   * 获取响应时间分布
   */
  private getResponseTimeDistribution(): Record<string, number> {
    const distribution = {
      'fast': 0,      // < 500ms
      'normal': 0,    // 500ms - 1s
      'slow': 0,      // 1s - 2s
      'very_slow': 0  // > 2s
    }

    for (const time of this.responseTimes) {
      if (time < 500) {
        distribution.fast++
      } else if (time < 1000) {
        distribution.normal++
      } else if (time < 2000) {
        distribution.slow++
      } else {
        distribution.very_slow++
      }
    }

    return distribution
  }

  /**
   * 重置统计信息
   */
  reset(): void {
    this.metrics = {
      apiCalls: 0,
      averageResponseTime: 0,
      errorRate: 0,
      cacheHitRate: 0,
      slowQueries: []
    }

    this.responseTimes = []
    this.errorCount = 0
    this.cacheHits = 0
    this.cacheMisses = 0
  }

  /**
   * 导出性能报告
   */
  exportReport(): string {
    const stats = this.getDetailedStats()
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalApiCalls: stats.apiCalls,
        averageResponseTime: `${stats.averageResponseTime}ms`,
        errorRate: `${(stats.errorRate * 100).toFixed(2)}%`,
        cacheHitRate: `${(stats.cacheHitRate * 100).toFixed(2)}%`
      },
      performance: {
        responseTimeDistribution: stats.responseTimeDistribution,
        slowQueriesCount: stats.recentSlowQueries.length,
        recentTrend: stats.recentResponseTimes
      },
      cache: {
        totalRequests: stats.totalCacheRequests,
        hits: stats.cacheHits,
        misses: stats.cacheMisses
      },
      errors: {
        totalErrors: stats.errorCount,
        successfulRequests: stats.successCount
      },
      slowQueries: stats.recentSlowQueries.slice(0, 10) // 最近10个慢查询
    }

    return JSON.stringify(report, null, 2)
  }

  /**
   * 检查性能健康状态
   */
  checkHealth(): {
    status: 'healthy' | 'warning' | 'critical'
    issues: string[]
    recommendations: string[]
  } {
    const issues: string[] = []
    const recommendations: string[] = []
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'

    // 检查平均响应时间
    if (this.metrics.averageResponseTime > 2000) {
      issues.push('平均响应时间过长')
      recommendations.push('考虑优化API性能或增加缓存')
      status = 'critical'
    } else if (this.metrics.averageResponseTime > 1000) {
      issues.push('平均响应时间较长')
      recommendations.push('建议优化API性能')
      if (status === 'healthy') status = 'warning'
    }

    // 检查错误率
    if (this.metrics.errorRate > 0.1) { // 10%
      issues.push('错误率过高')
      recommendations.push('检查API稳定性和错误处理')
      status = 'critical'
    } else if (this.metrics.errorRate > 0.05) { // 5%
      issues.push('错误率较高')
      recommendations.push('关注API错误情况')
      if (status === 'healthy') status = 'warning'
    }

    // 检查缓存命中率
    if (this.metrics.cacheHitRate < 0.3) { // 30%
      issues.push('缓存命中率较低')
      recommendations.push('优化缓存策略或增加缓存时间')
      if (status === 'healthy') status = 'warning'
    }

    // 检查慢查询
    const recentSlowQueries = this.metrics.slowQueries.filter(
      query => query.timestamp > Date.now() - 60 * 60 * 1000
    )
    if (recentSlowQueries.length > 10) {
      issues.push('慢查询过多')
      recommendations.push('优化慢查询或增加索引')
      if (status === 'healthy') status = 'warning'
    }

    return { status, issues, recommendations }
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()

// 默认导出
export default performanceMonitor
