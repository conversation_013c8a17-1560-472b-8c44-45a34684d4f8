"""
Health check router for master node.
"""

from datetime import datetime

from fastapi import APIRouter

from config.validation import check_rule_registration_health
from core.logging.logging_system import log as logger
from models import ApiResponse

# Health check router (no authentication required)
health_router = APIRouter(tags=["Health Check"])


@health_router.get("/health")
async def health_check():
    """
    Health check endpoint for master node.

    Returns:
        ApiResponse: Health status response
    """
    return ApiResponse(success=True, message="Master node is healthy")


@health_router.get("/health/registration")
async def registration_health_check():
    """
    规则注册服务健康检查端点

    检查规则注册相关的配置和外部服务状态，
    用于运维监控和故障排查。

    Returns:
        ApiResponse: 规则注册健康状态响应
    """
    try:
        health_result = await check_rule_registration_health()

        # 构建响应消息
        if health_result["overall_healthy"]:
            message = "规则注册服务健康"
        else:
            issues = []
            if not health_result["configuration"]["healthy"]:
                issues.append(f"配置问题({health_result['configuration']['total_errors']}个错误)")
            if not health_result["service"]["healthy"]:
                issues.append("外部服务不可用")
            message = f"规则注册服务异常: {', '.join(issues)}"

        return ApiResponse(
            success=health_result["overall_healthy"],
            message=message,
            data={
                "overall_healthy": health_result["overall_healthy"],
                "configuration": health_result["configuration"],
                "service": health_result["service"],
                "check_time": datetime.now().isoformat(),
            },
        )

    except Exception as e:
        return ApiResponse(
            success=False,
            message=f"健康检查失败: {e}",
            data={"error": str(e), "check_time": datetime.now().isoformat()},
        )


@health_router.get("/ready")
async def readiness_check():
    """
    就绪状态检查 - 检查应用是否已完成启动优化
    """
    try:
        from services.application_startup import app_startup

        is_ready = app_startup.is_ready()
        startup_status = app_startup.get_startup_status()

        if is_ready:
            return ApiResponse(
                success=True,
                message="应用已就绪",
                data={
                    "ready": True,
                    "startup_completed": startup_status["startup_completed"],
                    "startup_success": startup_status["startup_success"],
                    "startup_time": startup_status["startup_time"],
                    "check_time": datetime.now().isoformat(),
                },
            )
        else:
            return ApiResponse(
                success=False,
                message="应用未就绪",
                data={
                    "ready": False,
                    "startup_completed": startup_status["startup_completed"],
                    "startup_success": startup_status["startup_success"],
                    "startup_error": startup_status["startup_error"],
                    "check_time": datetime.now().isoformat(),
                },
            )

    except Exception as e:
        logger.error(f"就绪状态检查失败: {e}", exc_info=True)
        return ApiResponse(
            success=False,
            message=f"就绪状态检查失败: {str(e)}",
            data={"check_time": datetime.now().isoformat(), "error": str(e)},
        )


@health_router.get("/startup-progress")
async def get_startup_progress():
    """
    获取启动进度信息
    """
    try:
        from services.index_prebuilder import index_prebuilder
        from services.startup_optimizer import startup_optimizer
        from services.warmup_manager import warmup_manager

        progress_info = {
            "startup_optimizer": startup_optimizer.get_startup_status(),
            "warmup_manager": warmup_manager.get_warmup_status(),
            "index_prebuilder": index_prebuilder.get_build_status(),
            "check_time": datetime.now().isoformat(),
        }

        return ApiResponse(success=True, message="启动进度信息获取成功", data=progress_info)

    except Exception as e:
        logger.error(f"获取启动进度失败: {e}", exc_info=True)
        return ApiResponse(
            success=False,
            message=f"获取启动进度失败: {str(e)}",
            data={"check_time": datetime.now().isoformat(), "error": str(e)},
        )
