<template>
  <div class="test-connection">
    <el-card>
      <template #header>
        <h3>API连接测试</h3>
      </template>

      <div class="test-section">
        <h4>环境信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="前端地址">
            {{ window.location.origin }}
          </el-descriptions-item>
          <el-descriptions-item label="API地址">
            {{ apiUrl }}
          </el-descriptions-item>
          <el-descriptions-item label="API密钥">
            {{ apiKey ? '已配置' : '未配置' }}
          </el-descriptions-item>
          <el-descriptions-item label="调试模式">
            {{ debugMode ? '开启' : '关闭' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="test-section">
        <h4>连接测试</h4>
        <el-space direction="vertical" style="width: 100%">
          <el-button @click="testHealthCheck" :loading="healthLoading">
            测试健康检查 (/health)
          </el-button>
          <el-button @click="testRulesStatus" :loading="rulesLoading">
            测试规则状态 (/api/v1/rules/status)
          </el-button>
        </el-space>
      </div>

      <div class="test-section">
        <h4>测试结果</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(result, index) in testResults"
            :key="index"
            :type="result.success ? 'success' : 'danger'"
            :timestamp="result.timestamp"
          >
            <div class="test-result">
              <div class="result-title">{{ result.title }}</div>
              <div class="result-status" :class="result.success ? 'success' : 'error'">
                {{ result.success ? '成功' : '失败' }}
              </div>
              <div class="result-message">{{ result.message }}</div>
              <el-collapse v-if="result.data">
                <el-collapse-item title="详细信息">
                  <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

// 环境变量
const apiUrl = import.meta.env.VITE_API_URL || '/api'
const apiKey = import.meta.env.VITE_API_KEY
const debugMode = import.meta.env.VITE_DEBUG === 'true'

// 响应式状态
const healthLoading = ref(false)
const rulesLoading = ref(false)
const testResults = ref([])

// 添加测试结果
const addTestResult = (title, success, message, data = null) => {
  testResults.value.unshift({
    title,
    success,
    message,
    data,
    timestamp: new Date().toLocaleString()
  })
}

// 测试健康检查
const testHealthCheck = async () => {
  healthLoading.value = true
  try {
    const response = await axios.get('/health', {
      baseURL: apiUrl,
      timeout: 5000
    })

    addTestResult(
      '健康检查',
      true,
      `状态码: ${response.status}`,
      response.data
    )
    ElMessage.success('健康检查成功')
  } catch (error) {
    const message = error.response
      ? `HTTP ${error.response.status}: ${error.response.statusText}`
      : error.message

    addTestResult(
      '健康检查',
      false,
      message,
      error.response?.data
    )
    ElMessage.error('健康检查失败')
  } finally {
    healthLoading.value = false
  }
}

// 测试规则状态API
const testRulesStatus = async () => {
  rulesLoading.value = true
  try {
    const response = await axios.get('/api/v1/rules/status', {
      baseURL: apiUrl,
      headers: apiKey ? { 'X-API-Key': apiKey } : {},
      timeout: 10000
    })

    // 处理统一API响应格式
    let rulesData = []
    let count = 0

    if (response.data && typeof response.data === 'object') {
      if (response.data.success && response.data.data) {
        // 新的统一格式：{success: true, data: [...]}
        rulesData = response.data.data
        count = rulesData.length
      } else if (Array.isArray(response.data)) {
        // 旧格式：直接是数组
        rulesData = response.data
        count = rulesData.length
      }
    }

    addTestResult(
      '规则状态API',
      true,
      `获取到 ${count} 条规则`,
      rulesData
    )
    ElMessage.success('规则状态API测试成功')
  } catch (error) {
    const message = error.response
      ? `HTTP ${error.response.status}: ${error.response.statusText}`
      : error.message

    addTestResult(
      '规则状态API',
      false,
      message,
      error.response?.data
    )
    ElMessage.error('规则状态API测试失败')
  } finally {
    rulesLoading.value = false
  }
}
</script>

<style scoped>
.test-connection {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 24px;
}

.test-section h4 {
  margin-bottom: 16px;
  color: #303133;
}

.test-result {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.result-title {
  font-weight: 600;
  margin-bottom: 8px;
}

.result-status {
  font-size: 14px;
  margin-bottom: 8px;
}

.result-status.success {
  color: #67c23a;
}

.result-status.error {
  color: #f56c6c;
}

.result-message {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}
</style>
