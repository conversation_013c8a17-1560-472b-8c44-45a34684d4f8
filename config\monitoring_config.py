"""
监控配置
定义监控系统的配置参数和默认设置
"""


from config.settings import get_settings

# 获取设置实例
settings = get_settings()


class MonitoringConfig:
    """监控配置类"""

    # ===== 基础配置 =====
    # 监控功能开关
    MONITORING_ENABLED: bool = getattr(settings, 'MONITORING_ENABLED', True)

    # 指标收集配置
    METRICS_COLLECTION_INTERVAL: int = getattr(settings, 'METRICS_COLLECTION_INTERVAL', 30)  # 秒
    METRICS_HISTORY_SIZE: int = getattr(settings, 'METRICS_HISTORY_SIZE', 1000)

    # 健康检查配置
    HEALTH_CHECK_INTERVAL: int = getattr(settings, 'HEALTH_CHECK_INTERVAL', 60)  # 秒
    HEALTH_CHECK_TIMEOUT: int = getattr(settings, 'HEALTH_CHECK_TIMEOUT', 10)  # 秒

    # ===== 告警配置 =====
    # 告警功能开关
    ALERTING_ENABLED: bool = getattr(settings, 'ALERTING_ENABLED', True)

    # 告警评估配置
    ALERT_EVALUATION_INTERVAL: int = getattr(settings, 'ALERT_EVALUATION_INTERVAL', 10)  # 秒
    ALERT_HISTORY_SIZE: int = getattr(settings, 'ALERT_HISTORY_SIZE', 1000)

    # 告警规则文件路径
    ALERT_RULES_FILE: str = getattr(settings, 'ALERT_RULES_FILE', 'config/alert_rules.json')

    # ===== 健康状态阈值 =====
    # CPU阈值
    CPU_WARNING_THRESHOLD: float = getattr(settings, 'CPU_WARNING_THRESHOLD', 70.0)
    CPU_CRITICAL_THRESHOLD: float = getattr(settings, 'CPU_CRITICAL_THRESHOLD', 85.0)

    # 内存阈值
    MEMORY_WARNING_THRESHOLD: float = getattr(settings, 'MEMORY_WARNING_THRESHOLD', 75.0)
    MEMORY_CRITICAL_THRESHOLD: float = getattr(settings, 'MEMORY_CRITICAL_THRESHOLD', 90.0)

    # 数据库连接池阈值
    DB_POOL_WARNING_THRESHOLD: float = getattr(settings, 'DB_POOL_WARNING_THRESHOLD', 80.0)
    DB_POOL_CRITICAL_THRESHOLD: float = getattr(settings, 'DB_POOL_CRITICAL_THRESHOLD', 95.0)

    # 错误率阈值
    ERROR_RATE_WARNING_THRESHOLD: float = getattr(settings, 'ERROR_RATE_WARNING_THRESHOLD', 0.02)  # 2%
    ERROR_RATE_CRITICAL_THRESHOLD: float = getattr(settings, 'ERROR_RATE_CRITICAL_THRESHOLD', 0.05)  # 5%

    # 成功率阈值
    SUCCESS_RATE_WARNING_THRESHOLD: float = getattr(settings, 'SUCCESS_RATE_WARNING_THRESHOLD', 0.95)  # 95%
    SUCCESS_RATE_CRITICAL_THRESHOLD: float = getattr(settings, 'SUCCESS_RATE_CRITICAL_THRESHOLD', 0.90)  # 90%

    # ===== 通知配置 =====
    # 通知渠道配置
    NOTIFICATION_CHANNELS: dict[str, dict] = {
        "console": {
            "enabled": getattr(settings, 'NOTIFICATION_CONSOLE_ENABLED', True),
            "severity_filter": []  # 空列表表示所有严重程度
        },
        "log": {
            "enabled": getattr(settings, 'NOTIFICATION_LOG_ENABLED', True),
            "log_level": getattr(settings, 'NOTIFICATION_LOG_LEVEL', 'WARNING'),
            "severity_filter": []
        },
        "email": {
            "enabled": getattr(settings, 'NOTIFICATION_EMAIL_ENABLED', False),
            "smtp_host": getattr(settings, 'NOTIFICATION_EMAIL_SMTP_HOST', ''),
            "smtp_port": getattr(settings, 'NOTIFICATION_EMAIL_SMTP_PORT', 587),
            "smtp_user": getattr(settings, 'NOTIFICATION_EMAIL_SMTP_USER', ''),
            "smtp_password": getattr(settings, 'NOTIFICATION_EMAIL_SMTP_PASSWORD', ''),
            "from_email": getattr(settings, 'NOTIFICATION_EMAIL_FROM', ''),
            "to_emails": getattr(settings, 'NOTIFICATION_EMAIL_TO', []),
            "severity_filter": ["critical", "high"]
        },
        "webhook": {
            "enabled": getattr(settings, 'NOTIFICATION_WEBHOOK_ENABLED', False),
            "url": getattr(settings, 'NOTIFICATION_WEBHOOK_URL', ''),
            "timeout": getattr(settings, 'NOTIFICATION_WEBHOOK_TIMEOUT', 10),
            "severity_filter": ["critical", "high"]
        }
    }

    # ===== 默认告警规则 =====
    DEFAULT_ALERT_RULES: list[dict] = [
        {
            "name": "high_cpu_usage",
            "metric_name": "system.cpu.usage",
            "operator": ">",
            "threshold": CPU_CRITICAL_THRESHOLD,
            "severity": "high",
            "description": "CPU使用率过高",
            "duration_seconds": 120,
            "evaluation_interval": 30,
            "enabled": True
        },
        {
            "name": "high_memory_usage",
            "metric_name": "system.memory.usage",
            "operator": ">",
            "threshold": MEMORY_CRITICAL_THRESHOLD,
            "severity": "high",
            "description": "内存使用率过高",
            "duration_seconds": 120,
            "evaluation_interval": 30,
            "enabled": True
        },
        {
            "name": "low_memory_available",
            "metric_name": "system.memory.available",
            "operator": "<",
            "threshold": 500,  # MB
            "severity": "medium",
            "description": "可用内存不足",
            "duration_seconds": 60,
            "evaluation_interval": 30,
            "enabled": True
        },
        {
            "name": "high_error_rate",
            "metric_name": "errors.rate",
            "operator": ">",
            "threshold": ERROR_RATE_CRITICAL_THRESHOLD,
            "severity": "critical",
            "description": "错误率过高",
            "duration_seconds": 30,
            "evaluation_interval": 10,
            "enabled": True
        },
        {
            "name": "registration_task_failure",
            "metric_name": "registration.tasks.success.rate",
            "operator": "<",
            "threshold": SUCCESS_RATE_CRITICAL_THRESHOLD,
            "severity": "high",
            "description": "规则注册任务成功率过低",
            "duration_seconds": 180,
            "evaluation_interval": 60,
            "enabled": True
        },
        {
            "name": "degradation_mode_active",
            "metric_name": "degradation.status",
            "operator": ">",
            "threshold": 0,  # 0=正常, 1=降级, 2=失败
            "severity": "medium",
            "description": "系统进入降级模式",
            "duration_seconds": 30,
            "evaluation_interval": 10,
            "enabled": True
        },
        {
            "name": "circuit_breaker_open",
            "metric_name": "degradation.circuit.breaker.state",
            "operator": "==",
            "threshold": 2,  # 0=关闭, 1=半开, 2=开启
            "severity": "high",
            "description": "熔断器开启",
            "duration_seconds": 0,  # 立即告警
            "evaluation_interval": 10,
            "enabled": True
        },
        {
            "name": "database_pool_high_utilization",
            "metric_name": "database.pool.utilization",
            "operator": ">",
            "threshold": DB_POOL_CRITICAL_THRESHOLD,
            "severity": "high",
            "description": "数据库连接池使用率过高",
            "duration_seconds": 60,
            "evaluation_interval": 30,
            "enabled": True
        }
    ]

    # ===== 仪表板配置 =====
    # 仪表板刷新间隔
    DASHBOARD_REFRESH_INTERVAL: int = getattr(settings, 'DASHBOARD_REFRESH_INTERVAL', 30)  # 秒

    # 性能趋势数据点数量
    PERFORMANCE_TREND_DATA_POINTS: int = getattr(settings, 'PERFORMANCE_TREND_DATA_POINTS', 60)

    # 最近告警显示数量
    RECENT_ALERTS_LIMIT: int = getattr(settings, 'RECENT_ALERTS_LIMIT', 10)

    # ===== 日志分析配置 =====
    # 日志分析功能开关
    LOG_ANALYSIS_ENABLED: bool = getattr(settings, 'LOG_ANALYSIS_ENABLED', True)

    # 日志文件路径模式
    LOG_FILE_PATTERNS: list[str] = getattr(settings, 'LOG_FILE_PATTERNS', [
        'logs/*.log',
        'logs/app_*.log'
    ])

    # 错误日志关键词
    ERROR_LOG_KEYWORDS: list[str] = getattr(settings, 'ERROR_LOG_KEYWORDS', [
        'ERROR', 'CRITICAL', 'FATAL', 'Exception', 'Traceback'
    ])

    # 警告日志关键词
    WARNING_LOG_KEYWORDS: list[str] = getattr(settings, 'WARNING_LOG_KEYWORDS', [
        'WARNING', 'WARN', 'deprecated'
    ])

    # ===== 性能监控配置 =====
    # 性能监控功能开关
    PERFORMANCE_MONITORING_ENABLED: bool = getattr(settings, 'PERFORMANCE_MONITORING_ENABLED', True)

    # 性能基线记录
    PERFORMANCE_BASELINE_ENABLED: bool = getattr(settings, 'PERFORMANCE_BASELINE_ENABLED', True)

    # 性能异常检测阈值
    PERFORMANCE_ANOMALY_THRESHOLD: float = getattr(settings, 'PERFORMANCE_ANOMALY_THRESHOLD', 2.0)  # 标准差倍数

    # ===== 存储配置 =====
    # 监控数据存储路径
    MONITORING_DATA_PATH: str = getattr(settings, 'MONITORING_DATA_PATH', 'data/monitoring')

    # 指标数据保留时间（天）
    METRICS_RETENTION_DAYS: int = getattr(settings, 'METRICS_RETENTION_DAYS', 30)

    # 告警数据保留时间（天）
    ALERTS_RETENTION_DAYS: int = getattr(settings, 'ALERTS_RETENTION_DAYS', 90)

    @classmethod
    def get_health_thresholds(cls) -> dict[str, float]:
        """获取健康状态阈值配置"""
        return {
            "cpu_warning": cls.CPU_WARNING_THRESHOLD,
            "cpu_critical": cls.CPU_CRITICAL_THRESHOLD,
            "memory_warning": cls.MEMORY_WARNING_THRESHOLD,
            "memory_critical": cls.MEMORY_CRITICAL_THRESHOLD,
            "db_pool_warning": cls.DB_POOL_WARNING_THRESHOLD,
            "db_pool_critical": cls.DB_POOL_CRITICAL_THRESHOLD,
            "error_rate_warning": cls.ERROR_RATE_WARNING_THRESHOLD,
            "error_rate_critical": cls.ERROR_RATE_CRITICAL_THRESHOLD,
            "success_rate_warning": cls.SUCCESS_RATE_WARNING_THRESHOLD,
            "success_rate_critical": cls.SUCCESS_RATE_CRITICAL_THRESHOLD,
        }

    @classmethod
    def get_notification_config(cls, channel_name: str) -> dict:
        """获取指定通知渠道的配置"""
        return cls.NOTIFICATION_CHANNELS.get(channel_name, {})

    @classmethod
    def is_monitoring_enabled(cls) -> bool:
        """检查监控功能是否启用"""
        return cls.MONITORING_ENABLED

    @classmethod
    def is_alerting_enabled(cls) -> bool:
        """检查告警功能是否启用"""
        return cls.ALERTING_ENABLED and cls.MONITORING_ENABLED


# 创建配置实例
monitoring_config = MonitoringConfig()
