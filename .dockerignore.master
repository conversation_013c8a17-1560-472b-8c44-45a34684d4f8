# ===== 主节点专用 .dockerignore (优化版) =====
# 主节点需要完整的功能，但排除从节点专用和开发文件
# 优化构建上下文，减少不必要的文件传输

# ===== 版本控制 =====
.git
.gitignore
.gitattributes
.gitmodules

# ===== 环境变量文件 =====
.env
.env.local
.env.development
.env.test
.env.production
!.env.example

# ===== Python缓存和编译文件 =====
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ===== 虚拟环境 =====
venv/
env/
.venv/
.env/
ENV/
env.bak/
venv.bak/

# ===== IDE和编辑器 =====
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db
.roo
.cursorignore

# ===== 日志文件（运行时生成） =====
*.log
logs/
_log/
*.pid
*.seed
*.pid.lock

# ===== 数据库文件（开发用） =====
*.db
*.sqlite
*.sqlite3
test_*.db
*_test.db

# ===== 缓存目录 =====
.cache/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.ruff_cache/
rules_cache/
rules_cache.json*
rules_version.txt

# ===== 文档和演示 =====
documentation/
demo/
README.md
CHANGELOG.md
LICENSE
*.md
!requirements.txt

# ===== 测试文件 =====
tests/
validate_test.py
generate.py
test_*.db
*_test.py
*_test.db

# ===== 生成文件 =====
generated_templates/
*.xlsx
*.xls
333.json
_data/
tmp/
temp/
.tmp/

# ===== 前端文件（主节点Docker构建时不需要） =====
frontend/

# ===== Docker相关文件 =====
Dockerfile*
docker-compose*.yml
.dockerignore*

# ===== 配置文件（开发用） =====
pytest.ini
ruff.toml
.editorconfig
.prettierrc
.prettierignore
.eslintrc.*
jest.config.js
vitest.config.js

# ===== 依赖管理 =====
requirements.in
package-lock.json
yarn.lock
pnpm-lock.yaml

# ===== CI/CD =====
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
