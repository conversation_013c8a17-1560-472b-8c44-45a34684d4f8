"""
数据库配置功能测试
"""

from unittest.mock import MagicMock, patch

import pytest

from config.settings import Settings
from core.database_initializer import DatabaseInitializer


class TestDatabaseConfiguration:
    """数据库配置测试类"""

    def test_independent_params_priority(self):
        """测试独立参数优先级高于DATABASE_URL"""
        settings = Settings(
            DATABASE_URL="mysql+pymysql://old_user:old_pass@old_host:3306/old_db",
            DB_HOST="new_host",
            DB_PORT=3307,
            DB_USER="new_user",
            DB_PASSWORD="new_pass",
            DB_NAME="new_db",
            DB_DRIVER="pymysql",
        )

        url = settings.get_database_url()
        assert "new_host" in url
        assert "new_user" in url
        assert "new_pass" in url
        assert "new_db" in url
        assert "3307" in url

    def test_fallback_to_database_url(self):
        """测试回退到DATABASE_URL"""
        settings = Settings(
            DATABASE_URL="mysql+pymysql://user:pass@host:3306/db",
            DB_HOST=None,
            DB_USER=None,
            DB_PASSWORD=None,
            DB_NAME=None,
        )

        url = settings.get_database_url()
        assert url == "mysql+pymysql://user:pass@host:3306/db"

    def test_special_characters_in_password(self):
        """测试密码中的特殊字符处理"""
        settings = Settings(
            DB_HOST="localhost",
            DB_PORT=3306,
            DB_USER="user",
            DB_PASSWORD="pass@word:123#test",
            DB_NAME="testdb",
            DB_DRIVER="pymysql",
        )

        url = settings.get_database_url()
        # 验证特殊字符没有被编码
        assert "pass@word:123#test" in url

    def test_admin_database_url_generation(self):
        """测试管理员数据库URL生成"""
        settings = Settings(
            AUTO_CREATE_DATABASE=True,
            DB_HOST="localhost",
            DB_PORT=3306,
            DB_ADMIN_USER="admin",
            DB_ADMIN_PASSWORD="admin_pass",
            DB_DRIVER="pymysql",
        )

        admin_url = settings.get_admin_database_url()
        assert admin_url == "mysql+pymysql://admin:admin_pass@localhost:3306"

    def test_admin_url_disabled_when_auto_create_false(self):
        """测试禁用自动建库时管理员URL为None"""
        settings = Settings(
            AUTO_CREATE_DATABASE=False, DB_HOST="localhost", DB_ADMIN_USER="admin", DB_ADMIN_PASSWORD="admin_pass"
        )

        admin_url = settings.get_admin_database_url()
        assert admin_url is None

    def test_config_validation_success(self):
        """测试配置验证成功"""
        settings = Settings(DB_HOST="localhost", DB_USER="user", DB_PASSWORD="pass", DB_NAME="db")

        is_valid, error_msg = settings.validate_database_config()
        assert is_valid is True
        assert error_msg == ""

    def test_config_validation_missing_params(self):
        """测试配置验证失败 - 缺少参数"""
        settings = Settings(DATABASE_URL=None, DB_HOST=None, DB_USER=None, DB_PASSWORD=None, DB_NAME=None)

        is_valid, error_msg = settings.validate_database_config()
        assert is_valid is False
        assert "数据库配置缺失" in error_msg

    def test_config_validation_auto_create_missing_admin(self):
        """测试配置验证失败 - 启用自动建库但缺少管理员配置"""
        settings = Settings(
            AUTO_CREATE_DATABASE=True,
            DB_HOST="localhost",
            DB_USER="user",
            DB_PASSWORD="pass",
            DB_NAME="db",
            DB_ADMIN_USER=None,
            DB_ADMIN_PASSWORD=None,
        )

        is_valid, error_msg = settings.validate_database_config()
        assert is_valid is False
        assert "管理员账户" in error_msg


class TestDatabaseInitializer:
    """数据库初始化器测试类"""

    @patch("core.database_initializer.create_engine")
    def test_successful_connection(self, mock_create_engine):
        """测试成功连接数据库"""
        # Mock engine and connection
        mock_connection = MagicMock()
        mock_result = MagicMock()
        mock_result.scalar.return_value = 1
        mock_connection.execute.return_value = mock_result
        mock_connection.__enter__.return_value = mock_connection
        mock_connection.__exit__.return_value = None

        mock_engine = MagicMock()
        mock_engine.connect.return_value = mock_connection
        mock_create_engine.return_value = mock_engine

        initializer = DatabaseInitializer()
        success, message = initializer._test_database_connection("mysql+pymysql://user:pass@host:3306/db")

        assert success is True
        assert "数据库连接正常" in message

    @patch("core.database_initializer.create_engine")
    def test_database_not_exists(self, mock_create_engine):
        """测试数据库不存在错误"""
        from sqlalchemy.exc import OperationalError

        # 创建一个模拟的原始异常，直接设置字符串表示
        mock_orig = "Unknown database 'test_db'"

        mock_error = OperationalError("Unknown database 'test_db'", None, None)
        mock_error.orig = mock_orig

        mock_engine = MagicMock()
        mock_engine.connect.side_effect = mock_error
        mock_create_engine.return_value = mock_engine

        # Mock settings for the test
        with patch("core.database_initializer.settings") as mock_settings:
            mock_settings.DB_NAME = "test_db"

            initializer = DatabaseInitializer()
            success, message = initializer._test_database_connection("mysql+pymysql://user:pass@host:3306/test_db")

            assert success is False
            assert "数据库不存在" in message

    @patch("core.database_initializer.create_engine")
    def test_auto_create_database_success(self, mock_create_engine):
        """测试自动创建数据库成功"""
        # Mock admin connection
        mock_admin_connection = MagicMock()
        mock_check_result = MagicMock()
        mock_check_result.fetchone.return_value = None  # Database doesn't exist
        mock_admin_connection.execute.return_value = mock_check_result
        mock_admin_connection.__enter__.return_value = mock_admin_connection
        mock_admin_connection.__exit__.return_value = None

        mock_admin_engine = MagicMock()
        mock_admin_engine.connect.return_value = mock_admin_connection
        mock_create_engine.return_value = mock_admin_engine

        # Mock settings
        with patch("core.database_initializer.settings") as mock_settings:
            mock_settings.AUTO_CREATE_DATABASE = True
            mock_settings.DB_NAME = "test_db"
            mock_settings.get_admin_database_url.return_value = "mysql+pymysql://admin:pass@host:3306"
            mock_settings.DB_CONNECT_TIMEOUT = 30

            initializer = DatabaseInitializer()
            success, message = initializer._auto_create_database()

            assert success is True
            assert "创建成功" in message

    def test_get_database_info(self):
        """测试获取数据库信息"""
        with patch("core.database_initializer.settings") as mock_settings:
            mock_settings.DB_HOST = "localhost"
            mock_settings.DB_PORT = 3306
            mock_settings.DB_USER = "user"
            mock_settings.DB_NAME = "db"
            mock_settings.DB_DRIVER = "pymysql"
            mock_settings.AUTO_CREATE_DATABASE = True
            mock_settings.DB_CHECK_ON_STARTUP = True
            mock_settings.DB_CONNECT_TIMEOUT = 30

            initializer = DatabaseInitializer()
            info = initializer.get_database_info()

            assert info["host"] == "localhost"
            assert info["port"] == 3306
            assert info["user"] == "user"
            assert info["database"] == "db"
            assert info["driver"] == "pymysql"
            assert info["auto_create_enabled"] is True


if __name__ == "__main__":
    pytest.main([__file__])
