<template>
  <div class="degradation-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">降级状态监控</h1>
        <div class="header-actions">
          <el-button
            :icon="Refresh"
            :loading="isAnyLoading"
            @click="refreshAllData"
          >
            刷新数据
          </el-button>

          <el-dropdown @command="handleRefreshInterval">
            <el-button>
              刷新间隔
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="10000">10秒</el-dropdown-item>
                <el-dropdown-item command="30000">30秒</el-dropdown-item>
                <el-dropdown-item command="60000">1分钟</el-dropdown-item>
                <el-dropdown-item command="300000">5分钟</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：状态展示和控制 -->
      <div class="left-panel">
        <!-- 降级状态展示 -->
        <DegradationStatus />

        <!-- 手动控制 -->
        <DegradationControl />
      </div>

      <!-- 右侧：图表和历史 -->
      <div class="right-panel">
        <!-- 指标图表 -->
        <el-card class="metrics-chart" shadow="hover">
          <template #header>
            <span class="title">降级指标统计</span>
          </template>

          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">总降级次数</span>
                <el-icon class="metric-icon" color="#409EFF"><TrendCharts /></el-icon>
              </div>
              <div class="metric-value">{{ metrics.total_degradations }}</div>
              <div class="metric-detail">
                成功: {{ metrics.successful_degradations }} |
                失败: {{ metrics.failed_degradations }}
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">总恢复次数</span>
                <el-icon class="metric-icon" color="#67C23A"><CircleCheck /></el-icon>
              </div>
              <div class="metric-value">{{ metrics.total_recoveries }}</div>
              <div class="metric-detail">
                成功: {{ metrics.successful_recoveries }} |
                失败: {{ metrics.failed_recoveries }}
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">执行动作数</span>
                <el-icon class="metric-icon" color="#E6A23C"><Operation /></el-icon>
              </div>
              <div class="metric-value">{{ metrics.total_actions_executed }}</div>
              <div class="metric-detail">
                成功: {{ metrics.successful_actions }} |
                失败: {{ metrics.failed_actions }}
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-header">
                <span class="metric-title">平均持续时间</span>
                <el-icon class="metric-icon" color="#F56C6C"><Timer /></el-icon>
              </div>
              <div class="metric-value">{{ formatDuration(metrics.average_degradation_duration) }}</div>
              <div class="metric-detail">
                运行时间: {{ formatDuration(metrics.current_uptime) }}
              </div>
            </div>
          </div>
        </el-card>

        <!-- 事件时间线 -->
        <el-card class="events-timeline" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="title">最近事件</span>
              <el-button
                size="small"
                @click="loadMoreEvents"
                :loading="loading.events"
              >
                加载更多
              </el-button>
            </div>
          </template>

          <div class="timeline-container" v-loading="loading.events">
            <el-timeline v-if="events.length > 0">
              <el-timeline-item
                v-for="event in events.slice(0, 10)"
                :key="`${event.timestamp}-${event.event_type}`"
                :timestamp="formatTimestamp(event.timestamp)"
                :type="getEventTimelineType(event.event_type)"
                :icon="getEventIcon(event.event_type)"
              >
                <div class="event-content">
                  <div class="event-header">
                    <span class="event-type">{{ formatEventType(event.event_type) }}</span>
                    <el-tag
                      :type="getDegradationLevelColor(event.level)"
                      size="small"
                    >
                      {{ formatDegradationLevel(event.level) }}
                    </el-tag>
                  </div>

                  <div class="event-details" v-if="event.trigger_type || event.trigger_value">
                    <span v-if="event.trigger_type">
                      触发器: {{ formatTriggerType(event.trigger_type) }}
                    </span>
                    <span v-if="event.trigger_value">
                      值: {{ event.trigger_value }}
                    </span>
                  </div>

                  <div class="event-metadata" v-if="Object.keys(event.metadata).length > 0">
                    <div
                      v-for="(value, key) in event.metadata"
                      :key="key"
                      class="metadata-item"
                    >
                      <span class="metadata-key">{{ key }}:</span>
                      <span class="metadata-value">{{ value }}</span>
                    </div>
                  </div>

                  <div class="event-actions" v-if="event.actions_count > 0">
                    执行了 {{ event.actions_count }} 个动作
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>

            <el-empty v-else description="暂无事件记录" />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import {
  Refresh,
  ArrowDown,
  TrendCharts,
  CircleCheck,
  Operation,
  Timer,
  Warning,
  SuccessFilled,
  InfoFilled
} from '@element-plus/icons-vue'
import { useDegradationStore } from '../../stores/degradation'
import {
  formatDuration,
  formatTimestamp,
  formatEventType,
  formatTriggerType,
  formatDegradationLevel,
  getDegradationLevelColor
} from '../../api/degradation'
import DegradationStatus from '../../components/degradation/DegradationStatus.vue'
import DegradationControl from '../../components/degradation/DegradationControl.vue'

// 使用降级状态 store
const degradationStore = useDegradationStore()

// 从 store 中获取状态和方法
const {
  metrics,
  events,
  loading,
  isAnyLoading,
  fetchAllData,
  fetchEvents,
  setRefreshInterval,
  startAutoRefresh,
  stopAutoRefresh
} = degradationStore

// 刷新所有数据
const refreshAllData = async () => {
  await fetchAllData()
  await fetchEvents({ limit: 50 })
}

// 加载更多事件
const loadMoreEvents = async () => {
  await fetchEvents({ limit: 100 })
}

// 处理刷新间隔设置
const handleRefreshInterval = (interval) => {
  setRefreshInterval(parseInt(interval))
}

// 获取事件时间线类型
const getEventTimelineType = (eventType) => {
  const typeMap = {
    'degradation_triggered': 'warning',
    'degradation_recovered': 'success',
    'level_changed': 'primary',
    'manual_trigger': 'warning',
    'manual_recovery': 'success',
    'threshold_exceeded': 'danger',
    'threshold_recovered': 'success'
  }
  return typeMap[eventType] || 'info'
}

// 获取事件图标
const getEventIcon = (eventType) => {
  const iconMap = {
    'degradation_triggered': Warning,
    'degradation_recovered': SuccessFilled,
    'level_changed': InfoFilled,
    'manual_trigger': Warning,
    'manual_recovery': SuccessFilled,
    'threshold_exceeded': Warning,
    'threshold_recovered': SuccessFilled
  }
  return iconMap[eventType] || InfoFilled
}

// 组件挂载时初始化
onMounted(async () => {
  await refreshAllData()
  startAutoRefresh()
})

// 组件卸载时清理
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.degradation-monitor {
  padding: 20px;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: start;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.metric-card {
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s;
}

.metric-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.metric-title {
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.metric-icon {
  font-size: 20px;
}

.metric-value {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.metric-detail {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.timeline-container {
  max-height: 600px;
  overflow-y: auto;
}

.event-content {
  padding: 8px 0;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-type {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.event-details {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.event-metadata {
  margin-bottom: 8px;
}

.metadata-item {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-bottom: 2px;
}

.metadata-key {
  font-weight: 500;
}

.metadata-value {
  margin-left: 4px;
}

.event-actions {
  font-size: 12px;
  color: var(--el-color-primary);
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .degradation-monitor {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .page-title {
    text-align: center;
    font-size: 20px;
  }
}
</style>
