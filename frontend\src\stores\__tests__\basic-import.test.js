/**
 * 基础导入测试
 * 验证Store和组合式函数能够正确导入
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'

// Mock所有外部依赖
vi.mock('@/composables/core/useAsyncState', () => ({
  useAsyncState: vi.fn(() => ({
    isLoading: { value: false },
    execute: vi.fn()
  }))
}))

vi.mock('@/composables/core/useStateMachine', () => ({
  useStateMachine: vi.fn(() => ({
    start: vi.fn(),
    success: vi.fn(),
    error: vi.fn()
  }))
}))

vi.mock('@/composables/ui/useFeedback', () => ({
  useFeedback: vi.fn(() => ({
    toastSuccess: vi.fn(),
    toastError: vi.fn(),
    toastInfo: vi.fn()
  }))
}))

vi.mock('../app', () => ({
  useAppStore: vi.fn(() => ({
    addLoadingTask: vi.fn(),
    removeLoadingTask: vi.fn(),
    setError: vi.fn()
  }))
}))

vi.mock('@/api/enhancedRuleDetails', () => ({
  enhancedRuleDetailsApi: {
    initialize: vi.fn().mockResolvedValue(true),
    getCacheStats: vi.fn().mockResolvedValue({ size: 10 }),
    clearCache: vi.fn()
  }
}))

vi.mock('@/utils/enhancedErrorHandler', () => ({
  enhancedErrorHandler: {
    handleError: vi.fn()
  }
}))

vi.mock('../../api/rules', () => ({
  getRulesStatus: vi.fn().mockResolvedValue({
    success: true,
    data: []
  })
}))

describe('基础导入测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('应该能够导入RuleDetailsStore', async () => {
    const { useRuleDetailsStore } = await import('../ruleDetails.js')
    expect(useRuleDetailsStore).toBeDefined()
    expect(typeof useRuleDetailsStore).toBe('function')
  })

  it('应该能够导入RulesStore', async () => {
    const { useRulesStore } = await import('../rules.js')
    expect(useRulesStore).toBeDefined()
    expect(typeof useRulesStore).toBe('function')
  })

  it('应该能够创建RuleDetailsStore实例', async () => {
    const { useRuleDetailsStore } = await import('../ruleDetails.js')
    const store = useRuleDetailsStore()
    expect(store).toBeDefined()
  })

  it('应该能够创建RulesStore实例', async () => {
    const { useRulesStore } = await import('../rules.js')
    const store = useRulesStore()
    expect(store).toBeDefined()
  })

  it('应该能够导入useRuleDetailsState', async () => {
    const { useRuleDetailsState } = await import('../../composables/useRuleDetailsState.js')
    expect(useRuleDetailsState).toBeDefined()
    expect(typeof useRuleDetailsState).toBe('function')
  })

  it('应该能够导入useRulesCache', async () => {
    const { useRulesCache } = await import('../../composables/useRulesCache.js')
    expect(useRulesCache).toBeDefined()
    expect(typeof useRulesCache).toBe('function')
  })

  it('应该能够导入缓存策略枚举', async () => {
    const { CacheStrategy, CachePriority } = await import('../../composables/useRulesCache.js')

    expect(CacheStrategy).toBeDefined()
    expect(CacheStrategy.MEMORY_ONLY).toBe('memory_only')
    expect(CacheStrategy.HYBRID).toBe('hybrid')

    expect(CachePriority).toBeDefined()
    expect(CachePriority.LOW).toBe(1)
    expect(CachePriority.HIGH).toBe(3)
  })

  it('RuleDetailsStore应该有基本状态', async () => {
    const { useRuleDetailsStore } = await import('../ruleDetails.js')
    const store = useRuleDetailsStore()

    expect(store.detailsList).toBeDefined()
    expect(store.currentDetail).toBeDefined()
    expect(store.selectedDetails).toBeDefined()
    expect(store.pagination).toBeDefined()
    expect(store.filters).toBeDefined()
  })

  it('RulesStore应该有基本状态', async () => {
    const { useRulesStore } = await import('../rules.js')
    const store = useRulesStore()

    expect(store.rules).toBeDefined()
    expect(store.loading).toBeDefined()
    expect(store.currentRule).toBeDefined()
  })

  it('应该能够创建状态管理实例', async () => {
    const { useRuleDetailsState } = await import('../../composables/useRuleDetailsState.js')

    const stateManager = useRuleDetailsState({
      ruleKey: 'test-rule',
      autoLoad: false
    })

    expect(stateManager).toBeDefined()
    expect(stateManager.currentRuleKey).toBeDefined()
    expect(stateManager.isInitialized).toBeDefined()
  })

  it('应该能够创建缓存管理实例', async () => {
    const { useRulesCache, CacheStrategy } = await import('../../composables/useRulesCache.js')

    const cacheManager = useRulesCache({
      strategy: CacheStrategy.MEMORY_ONLY,
      maxMemorySize: 20
    })

    expect(cacheManager).toBeDefined()
    expect(cacheManager.strategy).toBe(CacheStrategy.MEMORY_ONLY)
    expect(cacheManager.maxMemorySize).toBe(20)
  })
})
