/**
 * 数据上传界面重构组件测试
 * 测试重构后的组件功能和集成
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'

// 导入重构后的组件
import UploadModeSelector from '../UploadModeSelector.vue'
import FileUploadArea from '../FileUploadArea.vue'
import DataPreviewTabs from '../DataPreviewTabs.vue'
import SubmissionConfirm from '../SubmissionConfirm.vue'
import UploadProgress from '../UploadProgress.vue'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  }
}))

describe('数据上传界面重构组件测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('UploadModeSelector 组件', () => {
    it('应该正确渲染上传模式选择器', () => {
      const wrapper = mount(UploadModeSelector, {
        props: {
          modelValue: 'full'
        }
      })

      expect(wrapper.find('.upload-mode-section').exists()).toBe(true)
      expect(wrapper.find('.section-title').text()).toBe('选择上传模式')
      expect(wrapper.findAll('.upload-mode-option')).toHaveLength(2)
    })

    it('应该正确处理模式切换', async () => {
      const wrapper = mount(UploadModeSelector, {
        props: {
          modelValue: 'full'
        }
      })

      // 直接调用组件的方法来模拟模式切换
      await wrapper.vm.handleModeChange('incremental')

      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('change')).toBeTruthy()
    })

    it('应该显示正确的模式说明', async () => {
      const wrapper = mount(UploadModeSelector, {
        props: {
          modelValue: 'incremental'
        }
      })

      expect(wrapper.find('.mode-alert').exists()).toBe(true)
      expect(wrapper.find('.mode-alert').text()).toContain('系统将自动对比现有数据')
    })
  })

  describe('FileUploadArea 组件', () => {
    it('应该正确渲染文件上传区域', () => {
      const wrapper = mount(FileUploadArea, {
        props: {
          selectedFile: null,
          processing: false,
          fileStatus: 'ready'
        }
      })

      expect(wrapper.find('.file-upload-area').exists()).toBe(true)
      expect(wrapper.find('.upload-dragger').exists()).toBe(true)
    })

    it('应该在选择文件后显示文件信息', () => {
      const mockFile = {
        name: 'test.xlsx',
        size: 1024 * 1024 // 1MB
      }

      const wrapper = mount(FileUploadArea, {
        props: {
          selectedFile: mockFile,
          processing: false,
          fileStatus: 'ready'
        }
      })

      expect(wrapper.find('.file-info-section').exists()).toBe(true)
      expect(wrapper.find('.file-name').text()).toBe('test.xlsx')
      expect(wrapper.find('.file-size').text()).toBe('1 MB')
    })

    it('应该在处理中显示进度', () => {
      const wrapper = mount(FileUploadArea, {
        props: {
          selectedFile: { name: 'test.xlsx', size: 1024 },
          processing: true,
          fileStatus: 'processing'
        }
      })

      expect(wrapper.find('.processing-section').exists()).toBe(true)
      expect(wrapper.find('.processing-title').text()).toBe('正在解析文件...')
    })
  })

  describe('DataPreviewTabs 组件', () => {
    const mockValidRows = [
      { rule_id: '1', rule_name: '规则1', description: '描述1' },
      { rule_id: '2', rule_name: '规则2', description: '描述2' }
    ]

    const mockInvalidRows = [
      {
        id: 'invalid1',
        rowNumber: 3,
        error: '必填字段缺失',
        data: { rule_name: '', description: '描述3' }
      }
    ]

    const mockColumns = [
      { prop: 'rule_name', label: '规则名称' },
      { prop: 'description', label: '描述' }
    ]

    it('应该正确渲染数据预览标签页', () => {
      const wrapper = mount(DataPreviewTabs, {
        props: {
          validRows: mockValidRows,
          invalidRows: mockInvalidRows,
          displayColumns: mockColumns
        }
      })

      expect(wrapper.find('.data-preview-tabs').exists()).toBe(true)
      expect(wrapper.find('.preview-tabs').exists()).toBe(true)
    })

    it('应该显示正确的数据统计', () => {
      const wrapper = mount(DataPreviewTabs, {
        props: {
          validRows: mockValidRows,
          invalidRows: mockInvalidRows,
          displayColumns: mockColumns
        }
      })

      const validTab = wrapper.find('[name="valid"]')
      const invalidTab = wrapper.find('[name="invalid"]')

      expect(validTab.attributes('label')).toContain('有效数据 (2)')
      expect(invalidTab.attributes('label')).toContain('无效数据 (1)')
    })

    it('应该支持重新校验无效数据', async () => {
      const wrapper = mount(DataPreviewTabs, {
        props: {
          validRows: mockValidRows,
          invalidRows: mockInvalidRows,
          displayColumns: mockColumns
        }
      })

      // 直接调用组件的方法来模拟重新校验
      await wrapper.vm.handleRevalidate(mockInvalidRows[0])

      expect(wrapper.emitted('revalidate-row')).toBeTruthy()
    })
  })

  describe('SubmissionConfirm 组件', () => {
    it('应该正确渲染确认提交界面', () => {
      const wrapper = mount(SubmissionConfirm, {
        props: {
          validCount: 10,
          invalidCount: 0,
          totalCount: 10,
          ruleName: '测试规则',
          ruleKey: 'test_rule',
          uploadMode: 'full',
          submitting: false
        }
      })

      expect(wrapper.find('.submission-confirm').exists()).toBe(true)
      expect(wrapper.find('.confirm-alert').exists()).toBe(true)
      expect(wrapper.find('.data-statistics').exists()).toBe(true)
    })

    it('应该显示正确的数据统计', () => {
      const wrapper = mount(SubmissionConfirm, {
        props: {
          validCount: 15,
          invalidCount: 2,
          totalCount: 17,
          ruleName: '测试规则',
          uploadMode: 'full'
        }
      })

      const statCards = wrapper.findAll('.stat-card')
      expect(statCards).toHaveLength(4)

      const validStat = statCards[0]
      const invalidStat = statCards[1]
      const totalStat = statCards[2]

      expect(validStat.find('.stat-number').text()).toBe('15')
      expect(invalidStat.find('.stat-number').text()).toBe('2')
      expect(totalStat.find('.stat-number').text()).toBe('17')
    })

    it('应该在有无效数据时禁用提交按钮', () => {
      const wrapper = mount(SubmissionConfirm, {
        props: {
          validCount: 10,
          invalidCount: 2,
          totalCount: 12,
          uploadMode: 'full'
        }
      })

      // 检查组件的 props 是否正确传递了 invalidCount > 0
      expect(wrapper.props('invalidCount')).toBe(2)
      expect(wrapper.props('invalidCount') > 0).toBe(true)
    })
  })

  describe('UploadProgress 组件', () => {
    it('应该正确渲染上传进度界面', () => {
      const wrapper = mount(UploadProgress, {
        props: {
          status: 'uploading',
          progress: 50,
          progressMessage: '正在上传数据...'
        }
      })

      expect(wrapper.find('.upload-progress').exists()).toBe(true)
      expect(wrapper.find('.progress-card').exists()).toBe(true)
      expect(wrapper.find('.progress-section').exists()).toBe(true)
    })

    it('应该根据状态显示不同的图标和文本', () => {
      const wrapper = mount(UploadProgress, {
        props: {
          status: 'success',
          progress: 100
        }
      })

      expect(wrapper.find('.status-icon.status-success').exists()).toBe(true)
      expect(wrapper.find('.status-title').text()).toBe('上传成功')
    })

    it('应该在上传中显示进度条', () => {
      const wrapper = mount(UploadProgress, {
        props: {
          status: 'uploading',
          progress: 75,
          progressMessage: '正在处理数据...'
        }
      })

      expect(wrapper.find('.main-progress').exists()).toBe(true)
      expect(wrapper.find('.progress-message').text()).toBe('正在处理数据...')
    })

    it('应该显示任务信息', () => {
      const mockTaskInfo = {
        task_id: 'task123',
        status: 'running',
        created_at: '2024-01-01T10:00:00Z',
        updated_at: '2024-01-01T10:30:00Z'
      }

      const wrapper = mount(UploadProgress, {
        props: {
          status: 'uploading',
          taskInfo: mockTaskInfo
        }
      })

      expect(wrapper.find('.task-info').exists()).toBe(true)
      expect(wrapper.text()).toContain('task123')
    })
  })

  describe('组件集成测试', () => {
    it('应该支持完整的上传流程', () => {
      // 这里可以添加更复杂的集成测试
      // 测试组件之间的数据流和事件传递
      expect(true).toBe(true) // 占位符测试
    })
  })
})
