/**
 * useStateMachine Composable 单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { ref } from 'vue'
import { useStateMachine } from '@/composables/core/useStateMachine'

describe('useStateMachine Composable 测试', () => {
  let stateMachine

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基础功能', () => {
    it('应该正确初始化状态机', () => {
      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: { name: '加载中' },
          success: { name: '成功' },
          error: { name: '错误' }
        },
        transitions: {
          idle: ['loading'],
          loading: ['success', 'error'],
          success: ['idle', 'loading'],
          error: ['idle', 'loading']
        }
      }

      stateMachine = useStateMachine(config)

      expect(stateMachine.currentState.value).toBe('idle')
      expect(stateMachine.stateHistory.value).toHaveLength(1)
      expect(stateMachine.stateHistory.value[0]).toMatchObject({
        state: 'idle',
        event: null
      })
    })

    it('应该正确处理状态转换', async () => {
      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: { name: '加载中' }
        },
        transitions: {
          idle: ['loading'],
          loading: ['idle']
        }
      }

      stateMachine = useStateMachine(config)

      // 执行状态转换
      const result = await stateMachine.transition('loading', { reason: 'start loading' })

      expect(result.success).toBe(true)
      expect(stateMachine.currentState.value).toBe('loading')
      expect(stateMachine.stateHistory.value).toHaveLength(2)
    })

    it('应该拒绝无效的状态转换', async () => {
      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: { name: '加载中' }
        },
        transitions: {
          idle: ['loading']
        }
      }

      stateMachine = useStateMachine(config)

      // 尝试无效转换
      const result = await stateMachine.transition('error')

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid transition')
      expect(stateMachine.currentState.value).toBe('idle')
    })
  })

  describe('状态历史管理', () => {
    beforeEach(() => {
      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: { name: '加载中' },
          success: { name: '成功' }
        },
        transitions: {
          idle: ['loading'],
          loading: ['success'],
          success: ['idle']
        }
      }

      stateMachine = useStateMachine(config)
    })

    it('应该正确记录状态历史', async () => {
      await stateMachine.transition('loading')
      await stateMachine.transition('success')

      expect(stateMachine.stateHistory.value).toHaveLength(3)
      expect(stateMachine.stateHistory.value.map(h => h.state)).toEqual([
        'idle', 'loading', 'success'
      ])
    })

    it('应该支持获取上一个状态', async () => {
      await stateMachine.transition('loading')

      const previousState = stateMachine.getPreviousState()
      expect(previousState).toBe('idle')
    })

    it('应该支持状态回滚', async () => {
      await stateMachine.transition('loading')
      await stateMachine.transition('success')

      const rollbackResult = await stateMachine.rollback()

      expect(rollbackResult.success).toBe(true)
      expect(stateMachine.currentState.value).toBe('loading')
    })

    it('应该限制历史记录数量', async () => {
      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: { name: '加载中' }
        },
        transitions: {
          idle: ['loading'],
          loading: ['idle']
        },
        maxHistorySize: 3
      }

      stateMachine = useStateMachine(config)

      // 执行多次状态转换
      await stateMachine.transition('loading')
      await stateMachine.transition('idle')
      await stateMachine.transition('loading')
      await stateMachine.transition('idle')
      await stateMachine.transition('loading')

      // 历史记录应该被限制在3个以内
      expect(stateMachine.stateHistory.value.length).toBeLessThanOrEqual(3)
    })
  })

  describe('事件监听', () => {
    beforeEach(() => {
      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: { name: '加载中' }
        },
        transitions: {
          idle: ['loading'],
          loading: ['idle']
        }
      }

      stateMachine = useStateMachine(config)
    })

    it('应该支持监听状态变化', async () => {
      const listener = vi.fn()

      stateMachine.on('stateChange', listener)
      await stateMachine.transition('loading')

      expect(listener).toHaveBeenCalledWith({
        from: 'idle',
        to: 'loading',
        event: null,
        timestamp: expect.any(Number),
        context: expect.any(Object)
      })
    })

    it('应该支持监听特定状态进入', async () => {
      const listener = vi.fn()

      stateMachine.on('enter:loading', listener)
      await stateMachine.transition('loading')

      expect(listener).toHaveBeenCalledWith({
        state: 'loading',
        from: 'idle',
        timestamp: expect.any(Number)
      })
    })

    it('应该支持监听特定状态离开', async () => {
      const listener = vi.fn()

      await stateMachine.transition('loading')
      stateMachine.on('leave:loading', listener)
      await stateMachine.transition('idle')

      expect(listener).toHaveBeenCalledWith({
        state: 'loading',
        to: 'idle',
        timestamp: expect.any(Number)
      })
    })

    it('应该支持移除事件监听器', async () => {
      const listener = vi.fn()

      stateMachine.on('stateChange', listener)
      stateMachine.off('stateChange', listener)

      await stateMachine.transition('loading')

      expect(listener).not.toHaveBeenCalled()
    })
  })

  describe('状态守卫', () => {
    it('应该支持进入守卫', async () => {
      const enterGuard = vi.fn().mockResolvedValue(true)

      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: {
            name: '加载中',
            onEnter: enterGuard
          }
        },
        transitions: {
          idle: ['loading']
        }
      }

      stateMachine = useStateMachine(config)
      await stateMachine.transition('loading')

      expect(enterGuard).toHaveBeenCalled()
      expect(stateMachine.currentState.value).toBe('loading')
    })

    it('应该支持离开守卫', async () => {
      const leaveGuard = vi.fn().mockResolvedValue(true)

      const config = {
        initialState: 'idle',
        states: {
          idle: {
            name: '空闲',
            onLeave: leaveGuard
          },
          loading: { name: '加载中' }
        },
        transitions: {
          idle: ['loading']
        }
      }

      stateMachine = useStateMachine(config)
      await stateMachine.transition('loading')

      expect(leaveGuard).toHaveBeenCalled()
      expect(stateMachine.currentState.value).toBe('loading')
    })

    it('应该阻止被守卫拒绝的转换', async () => {
      const enterGuard = vi.fn().mockResolvedValue(false)

      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: {
            name: '加载中',
            onEnter: enterGuard
          }
        },
        transitions: {
          idle: ['loading']
        }
      }

      stateMachine = useStateMachine(config)
      const result = await stateMachine.transition('loading')

      expect(result.success).toBe(false)
      expect(stateMachine.currentState.value).toBe('idle')
    })
  })

  describe('并发控制', () => {
    beforeEach(() => {
      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: { name: '加载中' },
          success: { name: '成功' }
        },
        transitions: {
          idle: ['loading'],
          loading: ['success'],
          success: ['idle']
        }
      }

      stateMachine = useStateMachine(config)
    })

    it('应该防止并发状态转换', async () => {
      // 同时触发多个转换
      const promise1 = stateMachine.transition('loading')
      const promise2 = stateMachine.transition('loading')

      const [result1, result2] = await Promise.all([promise1, promise2])

      // 只有一个应该成功
      expect([result1.success, result2.success]).toContain(true)
      expect([result1.success, result2.success]).toContain(false)
    })

    it('应该正确处理转换锁定', async () => {
      expect(stateMachine.isTransitioning.value).toBe(false)

      // 开始转换
      const transitionPromise = stateMachine.transition('loading')

      // 在转换过程中应该被锁定
      // 注意：由于转换可能是同步的，我们需要检查转换后的状态
      await transitionPromise
      expect(stateMachine.isTransitioning.value).toBe(false)
    })
  })

  describe('计算属性', () => {
    beforeEach(() => {
      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: { name: '加载中' },
          success: { name: '成功' },
          error: { name: '错误' }
        },
        transitions: {
          idle: ['loading'],
          loading: ['success', 'error'],
          success: ['idle'],
          error: ['idle']
        }
      }

      stateMachine = useStateMachine(config)
    })

    it('应该正确计算当前状态配置', () => {
      expect(stateMachine.currentStateConfig.value).toEqual({
        name: '空闲'
      })
    })

    it('应该正确计算可用转换', () => {
      expect(stateMachine.availableTransitions.value).toEqual(['loading'])
    })

    it('应该正确检查状态转换是否可用', () => {
      expect(stateMachine.canTransitionTo('loading')).toBe(true)
      expect(stateMachine.canTransitionTo('success')).toBe(false)
      expect(stateMachine.canTransitionTo('error')).toBe(false)
    })
  })

  describe('错误处理', () => {
    it('应该处理无效的配置', () => {
      // 测试无效配置是否会抛出错误或返回错误状态
      const result = useStateMachine({
        initialState: 'invalid',
        states: {
          idle: { name: '空闲' }
        }
      })

      // 检查是否正确处理了无效配置
      expect(result).toBeDefined()
    })

    it('应该处理守卫函数中的错误', async () => {
      const errorGuard = vi.fn().mockRejectedValue(new Error('Guard error'))

      const config = {
        initialState: 'idle',
        states: {
          idle: { name: '空闲' },
          loading: {
            name: '加载中',
            onEnter: errorGuard
          }
        },
        transitions: {
          idle: ['loading']
        }
      }

      stateMachine = useStateMachine(config)
      const result = await stateMachine.transition('loading')

      expect(result.success).toBe(false)
      expect(result.error).toContain('Guard error')
    })
  })
})
