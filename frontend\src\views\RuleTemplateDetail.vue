<template>
  <div class="rule-template-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <el-button
          :icon="ArrowLeft"
          @click="goBack"
          class="back-button"
        >
          返回
        </el-button>
        <div class="title-section">
          <h1 class="page-title">{{ ruleDetail?.rule_name || '规则模板详情' }}</h1>
          <p class="page-description">查看规则模板的参数信息和生效规则详情</p>
        </div>
      </div>
      <div class="header-actions">
        <el-button
          :icon="Refresh"
          @click="handleRefresh"
          :loading="isLoading"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 错误状态显示 -->
    <div v-if="hasError" style="margin-bottom: 16px;">
      <el-alert
        :title="currentError?.message || '系统错误'"
        type="error"
        :description="currentError?.details?.error"
        show-icon
        :closable="true"
        @close="appStore.clearError()"
      />
    </div>

    <LoadingWrapper :loading="isLoading" min-height="400px">
      <div v-if="ruleDetail" class="detail-content">
        <!-- 规则基本信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <StatusTag :status="ruleDetail.status" />
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="规则名称">
              {{ ruleDetail.rule_name }}
            </el-descriptions-item>
            <el-descriptions-item label="规则状态">
              <StatusTag :status="ruleDetail.status" />
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDate(ruleDetail.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDate(ruleDetail.updated_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="生效规则数量">
              <el-tag type="success">{{ ruleDetail.active_rules_count }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="规则类型">
              <el-tag type="info">{{ ruleDetail.rule_key }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="规则描述" :span="2">
              <div class="description-content" v-html="formatDescription(ruleDetail.description)">
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 参数信息表格 -->
        <el-card class="parameters-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>参数信息</span>
              <el-tag type="info" size="small">{{ ruleDetail.parameters?.length || 0 }} 个参数</el-tag>
            </div>
          </template>

          <el-table
            :data="ruleDetail.parameters"
            border
            empty-text="暂无参数信息"
            class="parameters-table"
          >
            <el-table-column prop="name_cn" label="参数名称" width="200">
              <template #default="{ row }">
                <span class="name-cn">{{ row.name_cn }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name_en" label="英文名称" width="180">
              <template #default="{ row }">
                <el-tag type="info" size="small">{{ row.name_en }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="参数类型" width="200">
              <template #default="{ row }">
                <el-tag :type="getTypeTagType(row.type)" size="small">
                  {{ formatParameterType(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="required" label="是否必填" width="100" align="center">
              <template #default="{ row }">
                <el-icon v-if="row.required" color="#f56c6c" size="16">
                  <Check />
                </el-icon>
                <el-icon v-else color="#909399" size="16">
                  <Close />
                </el-icon>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 生效规则详情 -->
        <el-card class="active-rules-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>生效规则详情</span>
              <el-tag type="success" size="small">{{ ruleDetail.active_rules_count }} 条生效规则</el-tag>
            </div>
          </template>

          <el-table
            :data="paginatedActiveRules"
            border
            empty-text="暂无生效规则"
            class="active-rules-table"
          >
            <el-table-column prop="rule_id" label="规则ID" width="200">
              <template #default="{ row }">
                <el-tag type="primary" size="small">{{ row.rule_id }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="uploaded_by" label="上传者" width="120">
              <template #default="{ row }">
                {{ row.uploaded_by || '系统' }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="updated_at" label="更新时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.updated_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewRuleData(row)"
                  :icon="View"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div v-if="ruleDetail.active_rules_count > pageSize" class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="ruleDetail.active_rules_count"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>

        <!-- 规则明细管理区域 -->
        <el-card class="detail-management-card">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <h3>规则明细管理</h3>
                <p class="header-description">管理和配置规则的具体明细项目</p>
              </div>
              <div class="header-right">
                <el-tag
                  v-if="detailsCount !== undefined"
                  type="info"
                  size="large"
                >
                  {{ detailsCount }} 条明细
                </el-tag>
              </div>
            </div>
          </template>

          <div class="detail-management-content">
            <div class="management-stats">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-value">{{ detailsCount || 0 }}</div>
                    <div class="stat-label">总明细数</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-value">{{ activeDetailsCount || 0 }}</div>
                    <div class="stat-label">活跃明细</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-value">{{ inactiveDetailsCount || 0 }}</div>
                    <div class="stat-label">非活跃明细</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <div class="management-actions">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-button
                    type="primary"
                    :icon="List"
                    @click="handleViewAllDetails"
                    block
                  >
                    查看所有明细
                  </el-button>
                </el-col>
                <el-col :span="8">
                  <el-button
                    type="success"
                    :icon="Plus"
                    @click="handleCreateDetail"
                    block
                  >
                    新增明细
                  </el-button>
                </el-col>
                <el-col :span="8">
                  <el-button
                    type="info"
                    :icon="Setting"
                    @click="handleManageDetails"
                    block
                  >
                    管理明细
                  </el-button>
                </el-col>
              </el-row>
            </div>

            <div class="management-description">
              <el-alert
                title="明细管理说明"
                type="info"
                :closable="false"
                show-icon
              >
                <p>规则明细是规则模板的具体实现，包含详细的验证逻辑和参数配置。</p>
                <ul>
                  <li><strong>查看所有明细</strong>：浏览当前规则的所有明细项目</li>
                  <li><strong>新增明细</strong>：创建新的规则明细项目</li>
                  <li><strong>管理明细</strong>：进入完整的明细管理界面</li>
                </ul>
              </el-alert>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 空状态 -->
      <el-empty v-else description="未找到规则模板详情" />
    </LoadingWrapper>

    <!-- 规则数据详情对话框 -->
    <el-dialog
      v-model="ruleDataDialogVisible"
      title="规则数据详情"
      width="80%"
      :before-close="handleCloseRuleDataDialog"
    >
      <el-scrollbar height="400px">
        <pre class="rule-data-content">{{ JSON.stringify(selectedRuleData, null, 2) }}</pre>
      </el-scrollbar>
      <template #footer>
        <el-button @click="ruleDataDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="copyRuleData">复制数据</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh, Check, Close, View, List, Plus, Setting } from '@element-plus/icons-vue'

import LoadingWrapper from '../components/common/LoadingWrapper.vue'
import StatusTag from '../components/common/StatusTag.vue'
import { formatDate } from '../utils/dateUtils'
import { getRuleTemplateDetail } from '../api/rules'
import { getRuleDetailsList } from '../api/ruleDetails'
import { useAppStore } from '../stores/app'
import { storeToRefs } from 'pinia'

// 路由相关
const route = useRoute()
const router = useRouter()

// 全局状态管理
const appStore = useAppStore()
const { globalLoading, hasError, currentError } = storeToRefs(appStore)

// 统一错误处理
const handleError = (error, message = '操作失败') => {
  console.error(message, error)
  ElMessage.error(`${message}: ${error.message || '请稍后重试'}`)

  // 设置全局错误状态
  appStore.setError({
    type: 'TEMPLATE_ERROR',
    message: message,
    details: { error: error.message || error, ruleKey: route.params.ruleKey }
  })
}

// 响应式数据
const loading = ref(false)

// 统一加载状态
const isLoading = computed(() => loading.value || globalLoading.value)
const ruleDetail = ref(null)
const ruleDataDialogVisible = ref(false)
const selectedRuleData = ref(null)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)

// 明细数量相关
const detailsCount = ref(0)
const activeDetailsCount = ref(0)
const inactiveDetailsCount = ref(0)

// 计算属性
const paginatedActiveRules = computed(() => {
  if (!ruleDetail.value?.active_rules) return []

  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return ruleDetail.value.active_rules.slice(start, end)
})

// 方法
// 获取明细数量统计
const fetchDetailsCount = async (ruleKey) => {
  try {
    const response = await getRuleDetailsList(ruleKey, { page: 1, page_size: 1 })
    // 修复数据访问路径：adaptEnhancedResponse 已经返回了 data 部分
    detailsCount.value = response?.total || 0

    // 如果需要获取状态分布，可以调用统计API
    // 这里简化处理，假设大部分是活跃的
    activeDetailsCount.value = Math.floor(detailsCount.value * 0.8)
    inactiveDetailsCount.value = detailsCount.value - activeDetailsCount.value
  } catch (error) {
    console.error('获取明细数量失败:', error)
    detailsCount.value = 0
    activeDetailsCount.value = 0
    inactiveDetailsCount.value = 0
  }
}

// 明细管理操作
const handleViewAllDetails = () => {
  const ruleKey = route.params.ruleKey
  router.push(`/rules/${ruleKey}/details`)
}

const handleCreateDetail = () => {
  const ruleKey = route.params.ruleKey
  router.push(`/rules/${ruleKey}/details`)
}

const handleManageDetails = () => {
  const ruleKey = route.params.ruleKey
  router.push(`/rules/${ruleKey}/details`)
}

const fetchRuleDetail = async () => {
  const ruleKey = route.params.ruleKey
  if (!ruleKey || typeof ruleKey !== 'string' || ruleKey.trim() === '') {
    console.warn('规则键参数无效:', ruleKey)
    ElMessage.error('缺少规则键参数')
    return
  }

  loading.value = true
  try {
    console.log('正在获取规则详情:', ruleKey)
    const data = await getRuleTemplateDetail(ruleKey)
    ruleDetail.value = data

    // 同时获取明细数量
    await fetchDetailsCount(ruleKey)

    ElMessage.success('规则模板详情加载成功')
  } catch (error) {
    // 根据错误类型提供更具体的错误信息
    let errorMessage = '获取规则模板详情失败'
    if (error.response) {
      switch (error.response.status) {
        case 404:
          errorMessage = '规则模板不存在或已被删除'
          break
        case 403:
          errorMessage = '权限不足，无法访问规则模板详情'
          break
        case 500:
          errorMessage = '服务器内部错误，请稍后重试'
          break
        default:
          errorMessage = `请求失败 (${error.response.status})`
      }
    } else if (error.request) {
      errorMessage = '网络连接失败，请检查网络设置'
    }

    handleError(error, errorMessage)
    ruleDetail.value = null
  } finally {
    loading.value = false
  }
}

const handleRefresh = () => {
  fetchRuleDetail()
}

const goBack = () => {
  router.back()
}

const viewRuleData = (row) => {
  selectedRuleData.value = row.rule_data
  ruleDataDialogVisible.value = true
}

const handleCloseRuleDataDialog = () => {
  ruleDataDialogVisible.value = false
  selectedRuleData.value = null
}

const copyRuleData = async () => {
  try {
    await navigator.clipboard.writeText(JSON.stringify(selectedRuleData.value, null, 2))
    ElMessage.success('规则数据已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

// 格式化参数类型
const formatParameterType = (type) => {
  const typeMap = {
    "<class 'str'>": '字符串',
    "<class 'int'>": '整数',
    "<class 'float'>": '浮点数',
    "<class 'bool'>": '布尔值',
    "list[str]": '列表[字符串]',
    "list[int]": '列表[整数]',
    "str | None": '字符串(可选)',
    "int | None": '整数(可选)',
    "dict": '字典',
    "any": '任意类型'
  }

  // 直接匹配
  if (typeMap[type]) {
    return typeMap[type]
  }

  // 处理复杂类型
  const formattedType = type.toLowerCase()
  for (const [key, value] of Object.entries(typeMap)) {
    if (formattedType.includes(key.toLowerCase())) {
      return value
    }
  }

  // 简化显示复杂类型
  if (type.includes('class')) {
    const match = type.match(/'([^']+)'/);
    if (match) {
      return match[1];
    }
  }

  return type
}

// 获取类型标签颜色
const getTypeTagType = (type) => {
  const lowerType = type.toLowerCase()
  if (lowerType.includes('str')) return 'success'
  if (lowerType.includes('int') || lowerType.includes('float')) return 'warning'
  if (lowerType.includes('bool')) return 'danger'
  if (lowerType.includes('list')) return 'info'
  if (lowerType.includes('dict')) return 'primary'
  return ''
}

// 格式化描述文本，将换行符转换为HTML换行标签
const formatDescription = (description) => {
  if (!description) return '暂无描述'

  // 转义HTML特殊字符，防止XSS攻击
  const escapeHtml = (text) => {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  // 转义HTML后，将换行符转换为<br>标签
  return escapeHtml(description).replace(/\n/g, '<br>')
}

// 监听路由参数变化
watch(() => route.params.ruleKey, (newRuleKey) => {
  if (newRuleKey) {
    fetchRuleDetail()
  }
}, { immediate: true })
</script>

<style scoped>
.rule-template-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.back-button {
  border-radius: 6px;
  margin-right: auto;
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  text-align: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card,
.parameters-card,
.active-rules-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.description-content {
  max-width: 100%;
  word-break: break-word;
  line-height: 1.6;
}

.parameters-table,
.active-rules-table {
  margin-top: 16px;
}

.name-cn {
  font-weight: 500;
  color: #303133;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
}

.rule-data-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 明细管理区域样式 */
.detail-management-card {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-description {
  margin: 0;
  color: #909399;
  font-size: 13px;
}

.detail-management-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.management-stats {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.management-actions {
  margin-bottom: 16px;
}

.management-description {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rule-template-detail {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-content {
    width: 100%;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .page-title {
    font-size: 20px;
  }

  .parameters-table,
  .active-rules-table {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .param-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .pagination-wrapper {
    padding: 12px 0;
  }
}
</style>
