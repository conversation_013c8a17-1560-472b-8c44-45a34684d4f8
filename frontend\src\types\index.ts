/**
 * 类型定义统一导出文件
 * 基于规则详情表三表结构重构更新
 */

// ==================== 核心类型导出（显式导出避免冲突） ====================

// 数据库模型类型（三表结构）
export * from './database-models'

// 自动生成的字段类型（重命名避免冲突）
export type {
  CommonFields,
  SpecificFields,
  RuleDetail as GeneratedRuleDetail,
  CreateRuleDetailData as GeneratedCreateRuleDetailData,
  UpdateRuleDetailData as GeneratedUpdateRuleDetailData,
  RuleType
} from './generated-fields'

export {
  getFieldChineseName,
  isFieldRequired,
  FIELD_CHINESE_NAMES,
  getRuleTypeChineseName
} from './generated-fields'

// 规则明细相关类型（业务层 - 优先使用）
export type {
  RuleDetail, // 业务层的RuleDetail（优先）
  CreateRuleDetailData, // 业务层的CreateRuleDetailData（优先）
  UpdateRuleDetailData, // 业务层的UpdateRuleDetailData（优先）
  RuleDetailsQueryParams,
  PaginationInfo,
  RuleDetailsListResponse,
  RuleDetailWithRelationsResponse,
  RuleDetailsBatchResponse,
  RuleDetailsStats,
  BatchOperationResult,
  ApiResponse,
  RuleDetailEvent
} from './ruleDetails'

export {
  RuleDetailStatus,
  SortOrder,
  BatchOperationType,
  getRuleStatusChineseName,
  isRuleEditable,
  getRuleDisplayTitle
} from './ruleDetails'

// 规则相关类型（重命名避免冲突）
export type {
  Rule,
  RuleInfo, // 重命名后的RuleDetail
  RuleParameter,
  ParameterConstraints,
  ValidationRule,
  RuleExample,
  RuleStatistics,
  PerformanceMetrics,
  RuleSchema,
  RuleSearchParams,
  RuleSearchResult,
  RuleStatusCounts,
  RuleSummary,
  RulePermissions,
  RuleVersion,
  RuleDependency,
  RuleTag,
  RuleCategory,
  TemplateColumn
} from './rule'

export {
  RuleStatus,
  ParameterType
} from './rule'

// API相关类型
export * from './api'

// UI组件相关类型
export * from './ui'

// 函数类型
export type AsyncFunction<T = any, R = any> = (...args: T[]) => Promise<R>
export type SyncFunction<T = any, R = any> = (...args: T[]) => R
export type VoidFunction = () => void
export type AsyncVoidFunction = () => Promise<void>

// 事件处理器类型
export type EventHandler<T = Event> = (event: T) => void
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>

// 组件Props类型工具
export type ComponentProps<T> = T extends new (...args: any[]) => infer R
  ? R extends { $props: infer P }
    ? P
    : never
  : never

// 组件Emits类型工具
export type ComponentEmits<T> = T extends new (...args: any[]) => infer R
  ? R extends { $emit: infer E }
    ? E
    : never
  : never

// 状态管理相关类型
export interface StoreState {
  [key: string]: any
}

export interface StoreGetters {
  [key: string]: (state: StoreState) => any
}

export interface StoreMutations {
  [key: string]: (state: StoreState, payload?: any) => void
}

export interface StoreActions {
  [key: string]: (context: any, payload?: any) => any
}

// 路由相关类型
export interface RouteConfig {
  path: string
  name?: string
  component?: any
  redirect?: string
  alias?: string | string[]
  children?: RouteConfig[]
  meta?: RouteMeta
  props?: boolean | object | Function
  beforeEnter?: Function
}

export interface RouteMeta {
  title?: string
  icon?: string
  hidden?: boolean
  roles?: string[]
  permissions?: string[]
  cache?: boolean
  affix?: boolean
  breadcrumb?: boolean
  activeMenu?: string
}

// 环境变量类型
export interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_API_URL: string
  readonly VITE_API_KEY: string
  readonly VITE_BASE_URL: string
  readonly VITE_REQUEST_TIMEOUT: string
  readonly VITE_DEBUG: string
  readonly VITE_MOCK: string
  readonly MODE: string
  readonly DEV: boolean
  readonly PROD: boolean
  readonly SSR: boolean
}

export interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 配置相关类型
export interface AppConfig {
  title: string
  version: string
  description?: string
  author?: string
  homepage?: string
  repository?: string
  license?: string
  keywords?: string[]
}

export interface BuildConfig {
  outDir: string
  assetsDir: string
  sourcemap: boolean
  minify: boolean
  target: string
  rollupOptions?: any
}

export interface DevConfig {
  host: string
  port: number
  open: boolean
  cors: boolean
  proxy?: Record<string, any>
}

// 工具函数类型
export type Awaitable<T> = T | Promise<T>
export type Arrayable<T> = T | T[]
export type Fn<T = void> = () => T
export type AnyFn = (...args: any[]) => any

// 时间相关类型
export type DateLike = string | number | Date
export type TimeUnit = 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second' | 'millisecond'

// 文件相关类型
export interface FileInfo {
  name: string
  size: number
  type: string
  lastModified: number
  webkitRelativePath?: string
}

export interface UploadFile extends FileInfo {
  uid: string
  status: 'ready' | 'uploading' | 'success' | 'error'
  percentage: number
  raw?: File
  response?: any
  error?: any
  url?: string
}

// 表单相关类型
export interface FormData {
  [key: string]: any
}

export interface FormErrors {
  [key: string]: string | string[]
}

export interface FormValidation {
  valid: boolean
  errors: FormErrors
}

// 搜索相关类型
export interface SearchOptions {
  caseSensitive?: boolean
  wholeWord?: boolean
  regex?: boolean
  fields?: string[]
}

export interface SearchResult<T = any> {
  items: T[]
  total: number
  query: string
  took: number
}

// 缓存相关类型
export interface CacheItem<T = any> {
  key: string
  value: T
  expires?: number
  created: number
  accessed: number
  hits: number
}

export interface CacheOptions {
  ttl?: number
  maxSize?: number
  strategy?: 'LRU' | 'LFU' | 'FIFO'
}

// 日志相关类型
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal'

export interface LogMessage {
  level: LogLevel
  message: string
  timestamp: number
  context?: any
  error?: Error
}

// 性能相关类型
export interface PerformanceEntry {
  name: string
  startTime: number
  duration: number
  entryType: string
}

export interface PerformanceTiming {
  navigationStart: number
  unloadEventStart: number
  unloadEventEnd: number
  redirectStart: number
  redirectEnd: number
  fetchStart: number
  domainLookupStart: number
  domainLookupEnd: number
  connectStart: number
  connectEnd: number
  secureConnectionStart: number
  requestStart: number
  responseStart: number
  responseEnd: number
  domLoading: number
  domInteractive: number
  domContentLoadedEventStart: number
  domContentLoadedEventEnd: number
  domComplete: number
  loadEventStart: number
  loadEventEnd: number
}
