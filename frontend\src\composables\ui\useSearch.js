/**
 * 搜索功能的Composable
 * 提供通用的搜索逻辑，支持多字段搜索和实时过滤
 */
import { ref, computed } from 'vue'

/**
 * 使用搜索功能
 * @param {Ref} data - 要搜索的数据源
 * @param {Array} searchFields - 搜索字段数组
 * @param {Object} options - 配置选项
 * @returns {Object} 搜索相关的响应式数据和方法
 */
export function useSearch(data, searchFields = [], options = {}) {
  const {
    caseSensitive = false,
    debounceMs = 0,
    minLength = 0
  } = options

  // 搜索关键词
  const searchKeyword = ref('')

  // 搜索结果
  const searchResults = computed(() => {
    if (!searchKeyword.value || searchKeyword.value.length < minLength) {
      return data.value || []
    }

    const keyword = caseSensitive
      ? searchKeyword.value
      : searchKeyword.value.toLowerCase()

    return (data.value || []).filter(item => {
      // 如果没有指定搜索字段，则搜索所有字符串字段
      const fieldsToSearch = searchFields.length > 0
        ? searchFields
        : Object.keys(item).filter(key => typeof item[key] === 'string')

      return fieldsToSearch.some(field => {
        const value = item[field]
        if (!value) return false

        const searchValue = caseSensitive ? value : value.toLowerCase()
        return searchValue.includes(keyword)
      })
    })
  })

  // 搜索统计
  const searchStats = computed(() => ({
    total: data.value?.length || 0,
    filtered: searchResults.value.length,
    hasFilter: !!searchKeyword.value && searchKeyword.value.length >= minLength
  }))

  // 清空搜索
  const clearSearch = () => {
    searchKeyword.value = ''
  }

  // 设置搜索关键词
  const setSearchKeyword = (keyword) => {
    searchKeyword.value = keyword
  }

  return {
    // 响应式数据
    searchKeyword,
    searchResults,
    searchStats,

    // 方法
    clearSearch,
    setSearchKeyword
  }
}

/**
 * 高级搜索功能
 * 支持多条件搜索和复杂过滤逻辑
 */
export function useAdvancedSearch(data, options = {}) {
  const {
    filters = {},
    sorters = {},
    defaultSort = null
  } = options

  const searchKeyword = ref('')
  const activeFilters = ref({})
  const sortConfig = ref(defaultSort || { field: null, order: null })

  // 搜索和过滤结果
  const filteredData = computed(() => {
    let result = data.value || []

    // 关键词搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      result = result.filter(item => {
        return Object.values(item).some(value => {
          if (typeof value === 'string') {
            return value.toLowerCase().includes(keyword)
          }
          return false
        })
      })
    }

    // 应用过滤器
    Object.entries(activeFilters.value).forEach(([filterKey, filterValue]) => {
      if (filterValue !== null && filterValue !== undefined && filterValue !== '') {
        const filterFn = filters[filterKey]
        if (filterFn) {
          result = result.filter(item => filterFn(item, filterValue))
        } else {
          // 默认相等过滤
          result = result.filter(item => item[filterKey] === filterValue)
        }
      }
    })

    // 应用排序
    if (sortConfig.value.field) {
      const { field, order } = sortConfig.value
      const sorterFn = sorters[field]

      result.sort((a, b) => {
        let comparison = 0

        if (sorterFn) {
          comparison = sorterFn(a, b)
        } else {
          // 默认排序逻辑
          const aVal = a[field]
          const bVal = b[field]

          if (aVal < bVal) comparison = -1
          else if (aVal > bVal) comparison = 1
        }

        return order === 'desc' ? -comparison : comparison
      })
    }

    return result
  })

  // 设置过滤器
  const setFilter = (key, value) => {
    activeFilters.value[key] = value
  }

  // 清除过滤器
  const clearFilter = (key) => {
    delete activeFilters.value[key]
  }

  // 清除所有过滤器
  const clearAllFilters = () => {
    activeFilters.value = {}
    searchKeyword.value = ''
  }

  // 设置排序
  const setSort = (field, order = 'asc') => {
    sortConfig.value = { field, order }
  }

  // 清除排序
  const clearSort = () => {
    sortConfig.value = { field: null, order: null }
  }

  return {
    // 响应式数据
    searchKeyword,
    activeFilters,
    sortConfig,
    filteredData,

    // 方法
    setFilter,
    clearFilter,
    clearAllFilters,
    setSort,
    clearSort
  }
}
