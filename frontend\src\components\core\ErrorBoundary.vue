<template>
  <div class="error-boundary">
    <!-- 正常内容 -->
    <slot v-if="!hasError" />

    <!-- 错误显示 -->
    <div v-else class="error-boundary__content">
      <!-- 自定义错误插槽 -->
      <slot
        name="error"
        :error="currentError"
        :error-info="errorInfo"
        :retry="retry"
        :reset="reset"
      >
        <!-- 默认错误显示 -->
        <div class="error-boundary__default">
          <div class="error-boundary__icon">
            <el-icon :size="48" color="#f56c6c">
              <WarningFilled />
            </el-icon>
          </div>

          <div class="error-boundary__message">
            <h3 class="error-boundary__title">
              {{ errorTitle }}
            </h3>
            <p class="error-boundary__description">
              {{ errorDescription }}
            </p>
          </div>

          <div class="error-boundary__actions">
            <el-button
              type="primary"
              @click="retry"
              :loading="isRetrying"
            >
              重试
            </el-button>
            <el-button @click="reset">
              重置
            </el-button>
            <el-button
              v-if="showDetails"
              type="info"
              text
              @click="toggleDetails"
            >
              {{ showErrorDetails ? '隐藏详情' : '显示详情' }}
            </el-button>
          </div>

          <!-- 错误详情 -->
          <div
            v-if="showDetails && showErrorDetails"
            class="error-boundary__details"
          >
            <el-collapse>
              <el-collapse-item title="错误信息" name="error">
                <pre class="error-boundary__error-text">{{ errorMessage }}</pre>
              </el-collapse-item>
              <el-collapse-item title="错误堆栈" name="stack">
                <pre class="error-boundary__stack-trace">{{ errorStack }}</pre>
              </el-collapse-item>
              <el-collapse-item title="组件信息" name="component">
                <pre class="error-boundary__component-info">{{ componentInfo }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onErrorCaptured, onMounted, watch } from 'vue'
import { ElIcon, ElButton, ElCollapse, ElCollapseItem, ElMessage } from 'element-plus'
import { WarningFilled } from '@element-plus/icons-vue'
import { useAppStore } from '@/stores/app'
import { useErrorRecovery } from '@/composables/core/useErrorRecovery'

/**
 * 组件属性
 */
const props = defineProps({
  // 是否显示重试按钮
  showRetry: {
    type: Boolean,
    default: true
  },

  // 是否显示重置按钮
  showReset: {
    type: Boolean,
    default: true
  },

  // 是否显示错误详情
  showDetails: {
    type: Boolean,
    default: process.env.NODE_ENV === 'development'
  },

  // 自定义错误标题
  errorTitle: {
    type: String,
    default: '页面出现错误'
  },

  // 自定义错误描述
  errorDescription: {
    type: String,
    default: '抱歉，页面遇到了一些问题。您可以尝试刷新页面或联系技术支持。'
  },

  // 最大重试次数
  maxRetries: {
    type: Number,
    default: 3
  },

  // 是否自动上报错误
  autoReport: {
    type: Boolean,
    default: true
  },

  // 错误恢复策略
  recoveryStrategy: {
    type: String,
    default: 'auto'
  }
})

/**
 * 组件事件
 */
const emit = defineEmits([
  'error',      // 错误发生
  'retry',      // 重试
  'reset',      // 重置
  'recover'     // 恢复成功
])

// 依赖注入
const appStore = useAppStore()
const errorRecovery = useErrorRecovery()

// 错误状态
const hasError = ref(false)
const currentError = ref(null)
const errorInfo = ref(null)
const retryCount = ref(0)
const isRetrying = ref(false)
const showErrorDetails = ref(false)

// ==================== 计算属性 ====================

/**
 * 错误消息
 */
const errorMessage = computed(() => {
  if (!currentError.value) return ''
  return currentError.value.message || currentError.value.toString()
})

/**
 * 错误堆栈
 */
const errorStack = computed(() => {
  if (!currentError.value) return ''
  return currentError.value.stack || '无堆栈信息'
})

/**
 * 组件信息
 */
const componentInfo = computed(() => {
  if (!errorInfo.value) return ''
  return JSON.stringify(errorInfo.value, null, 2)
})

/**
 * 是否可以重试
 */
const canRetry = computed(() => {
  return props.showRetry && retryCount.value < props.maxRetries
})

// ==================== 错误处理 ====================

/**
 * 捕获错误
 */
onErrorCaptured((error, instance, info) => {
  console.error('[ErrorBoundary] Error captured:', error)

  // 设置错误状态
  hasError.value = true
  currentError.value = error
  errorInfo.value = {
    componentName: instance?.$options.name || 'Unknown',
    componentInfo: info,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  }

  // 触发错误事件
  emit('error', {
    error,
    errorInfo: errorInfo.value,
    retryCount: retryCount.value
  })

  // 自动上报错误
  if (props.autoReport) {
    reportError(error, errorInfo.value)
  }

  // 尝试自动恢复
  if (props.recoveryStrategy === 'auto') {
    attemptAutoRecovery(error)
  }

  // 阻止错误继续传播
  return false
})

/**
 * 上报错误
 */
const reportError = async (error, info) => {
  try {
    // 添加到全局错误状态
    const errorId = appStore.setError(error, {
      type: 'component_error',
      componentInfo: info,
      boundary: 'ErrorBoundary'
    })

    // 使用错误恢复系统分析错误
    const errorType = errorRecovery.analyzeErrorType(error, info)
    const severity = errorRecovery.getErrorSeverity(errorType)

    console.debug('[ErrorBoundary] Error reported:', {
      errorId,
      errorType,
      severity,
      error,
      info
    })

  } catch (reportError) {
    console.error('[ErrorBoundary] Failed to report error:', reportError)
  }
}

/**
 * 尝试自动恢复
 */
const attemptAutoRecovery = async (error) => {
  try {
    const recoveryResult = await errorRecovery.recover(error, {
      type: 'component_error',
      retryFunction: () => retry(),
      fallbackFunction: () => reset()
    })

    if (recoveryResult.success) {
      console.info('[ErrorBoundary] Auto recovery successful:', recoveryResult)
      emit('recover', recoveryResult)
    }

  } catch (recoveryError) {
    console.error('[ErrorBoundary] Auto recovery failed:', recoveryError)
  }
}

// ==================== 用户操作 ====================

/**
 * 重试操作
 */
const retry = async () => {
  if (!canRetry.value) {
    ElMessage.warning(`重试次数已达上限 (${props.maxRetries})`)
    return
  }

  isRetrying.value = true
  retryCount.value++

  try {
    // 触发重试事件
    emit('retry', {
      attempt: retryCount.value,
      maxRetries: props.maxRetries
    })

    // 等待一小段时间让用户看到重试状态
    await new Promise(resolve => setTimeout(resolve, 500))

    // 重置错误状态
    hasError.value = false
    currentError.value = null
    errorInfo.value = null
    showErrorDetails.value = false

    ElMessage.success('重试成功')

  } catch (retryError) {
    console.error('[ErrorBoundary] Retry failed:', retryError)
    ElMessage.error('重试失败，请稍后再试')

    // 如果重试失败，恢复错误状态
    hasError.value = true

  } finally {
    isRetrying.value = false
  }
}

/**
 * 重置操作
 */
const reset = () => {
  // 触发重置事件
  emit('reset', {
    retryCount: retryCount.value
  })

  // 重置所有状态
  hasError.value = false
  currentError.value = null
  errorInfo.value = null
  retryCount.value = 0
  isRetrying.value = false
  showErrorDetails.value = false

  ElMessage.info('页面已重置')
}

/**
 * 切换错误详情显示
 */
const toggleDetails = () => {
  showErrorDetails.value = !showErrorDetails.value
}

// ==================== 生命周期 ====================

/**
 * 组件挂载
 */
onMounted(() => {
  // 监听全局未捕获的错误
  const handleGlobalError = (event) => {
    console.error('[ErrorBoundary] Global error:', event.error)

    if (!hasError.value) {
      hasError.value = true
      currentError.value = event.error
      errorInfo.value = {
        type: 'global_error',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        timestamp: new Date().toISOString()
      }

      if (props.autoReport) {
        reportError(event.error, errorInfo.value)
      }
    }
  }

  const handleUnhandledRejection = (event) => {
    console.error('[ErrorBoundary] Unhandled promise rejection:', event.reason)

    if (!hasError.value) {
      const error = event.reason instanceof Error ? event.reason : new Error(event.reason)
      hasError.value = true
      currentError.value = error
      errorInfo.value = {
        type: 'unhandled_rejection',
        timestamp: new Date().toISOString()
      }

      if (props.autoReport) {
        reportError(error, errorInfo.value)
      }
    }
  }

  // 注册全局错误监听器
  window.addEventListener('error', handleGlobalError)
  window.addEventListener('unhandledrejection', handleUnhandledRejection)

  // 清理监听器
  return () => {
    window.removeEventListener('error', handleGlobalError)
    window.removeEventListener('unhandledrejection', handleUnhandledRejection)
  }
})

// 监听错误状态变化
watch(hasError, (newValue) => {
  if (newValue) {
    console.warn('[ErrorBoundary] Error boundary activated')
  } else {
    console.info('[ErrorBoundary] Error boundary cleared')
  }
})
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-boundary__content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
}

.error-boundary__default {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.error-boundary__icon {
  margin-bottom: 20px;
}

.error-boundary__message {
  margin-bottom: 30px;
}

.error-boundary__title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.error-boundary__description {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.error-boundary__actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.error-boundary__details {
  margin-top: 20px;
  text-align: left;
}

.error-boundary__error-text,
.error-boundary__stack-trace,
.error-boundary__component-info {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-boundary__content {
    padding: 16px;
    min-height: 300px;
  }

  .error-boundary__title {
    font-size: 20px;
  }

  .error-boundary__description {
    font-size: 14px;
  }

  .error-boundary__actions {
    flex-direction: column;
    align-items: center;
  }

  .error-boundary__actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
