<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :before-close="handleClose"
    class="batch-action-dialog"
  >
    <div class="dialog-content">
      <!-- 操作说明 -->
      <div class="action-description">
        <el-icon class="warning-icon" :size="20">
          <WarningFilled />
        </el-icon>
        <p class="description-text">{{ actionDescription }}</p>
      </div>

      <!-- 选中项目列表 -->
      <div class="selected-items">
        <h4 class="items-title">选中的项目 ({{ selectedItems.length }}个):</h4>
        <div class="items-list">
          <div
            v-for="item in displayItems"
            :key="item.id"
            class="item-row"
          >
            <span class="item-name">{{ item.rule_name || item.name }}</span>
            <el-tag :type="getStatusType(item.status)" size="small">
              {{ item.status }}
            </el-tag>
          </div>

          <!-- 显示更多按钮 -->
          <div v-if="selectedItems.length > maxDisplayItems" class="more-items">
            <el-button
              text
              type="primary"
              @click="showAllItems = !showAllItems"
              size="small"
            >
              {{ showAllItems ? '收起' : `还有 ${selectedItems.length - maxDisplayItems} 个项目...` }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 确认输入 -->
      <div v-if="requireConfirmation" class="confirmation-input">
        <p class="confirm-text">请输入 <strong>{{ confirmationText }}</strong> 来确认操作：</p>
        <el-input
          v-model="confirmInput"
          :placeholder="`请输入 ${confirmationText}`"
          class="confirm-input"
        />
      </div>

      <!-- 操作选项 -->
      <div v-if="action === 'delete'" class="action-options">
        <el-checkbox v-model="deleteOptions.permanent">
          永久删除（不可恢复）
        </el-checkbox>
        <el-checkbox v-model="deleteOptions.cascade">
          同时删除关联数据
        </el-checkbox>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          :type="confirmButtonType"
          @click="handleConfirm"
          :disabled="!canConfirm"
          :loading="loading"
        >
          {{ confirmButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { WarningFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  action: {
    type: String,
    required: true
  },
  selectedItems: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  maxDisplayItems: {
    type: Number,
    default: 5
  }
})

// Emits
const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 响应式状态
const dialogVisible = ref(props.visible)
const confirmInput = ref('')
const showAllItems = ref(false)
const deleteOptions = ref({
  permanent: false,
  cascade: false
})

// 计算属性
const dialogTitle = computed(() => {
  const actionMap = {
    delete: '批量删除确认',
    activate: '批量激活确认',
    deactivate: '批量停用确认',
    export: '批量导出确认'
  }
  return actionMap[props.action] || '批量操作确认'
})

const actionDescription = computed(() => {
  const count = props.selectedItems.length
  const descriptionMap = {
    delete: `您即将删除 ${count} 个规则明细，此操作不可撤销，请谨慎操作。`,
    activate: `您即将激活 ${count} 个规则明细，激活后这些规则将开始生效。`,
    deactivate: `您即将停用 ${count} 个规则明细，停用后这些规则将不再生效。`,
    export: `您即将导出 ${count} 个规则明细的数据。`
  }
  return descriptionMap[props.action] || `您即将对 ${count} 个项目执行批量操作。`
})

const confirmButtonText = computed(() => {
  const textMap = {
    delete: '确认删除',
    activate: '确认激活',
    deactivate: '确认停用',
    export: '确认导出'
  }
  return textMap[props.action] || '确认操作'
})

const confirmButtonType = computed(() => {
  const typeMap = {
    delete: 'danger',
    activate: 'success',
    deactivate: 'warning',
    export: 'primary'
  }
  return typeMap[props.action] || 'primary'
})

const requireConfirmation = computed(() => {
  return props.action === 'delete' && props.selectedItems.length > 10
})

const confirmationText = computed(() => {
  return '确认删除'
})

const canConfirm = computed(() => {
  if (requireConfirmation.value) {
    return confirmInput.value === confirmationText.value
  }
  return true
})

const displayItems = computed(() => {
  if (showAllItems.value) {
    return props.selectedItems
  }
  return props.selectedItems.slice(0, props.maxDisplayItems)
})

// 监听visible变化
watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue
  if (newValue) {
    // 重置状态
    confirmInput.value = ''
    showAllItems.value = false
    deleteOptions.value = {
      permanent: false,
      cascade: false
    }
  }
})

watch(dialogVisible, (newValue) => {
  emit('update:visible', newValue)
})

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    'ACTIVE': 'success',
    'INACTIVE': 'warning',
    'DELETED': 'danger'
  }
  return typeMap[status] || 'info'
}

// 处理确认
const handleConfirm = () => {
  const confirmData = {
    action: props.action,
    items: props.selectedItems,
    options: props.action === 'delete' ? deleteOptions.value : {}
  }

  emit('confirm', confirmData.action, confirmData.items, confirmData.options)
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
  emit('cancel')
}
</script>

<style scoped>
.batch-action-dialog {
  border-radius: 8px;
}

.dialog-content {
  padding: 0;
}

.action-description {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fef0f0;
  border-radius: 6px;
  margin-bottom: 20px;
}

.warning-icon {
  color: #f56c6c;
  margin-top: 2px;
  flex-shrink: 0;
}

.description-text {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

.selected-items {
  margin-bottom: 20px;
}

.items-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.items-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.item-row:hover {
  background: #f5f7fa;
}

.item-row:last-child {
  margin-bottom: 0;
}

.item-name {
  flex: 1;
  font-size: 13px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.more-items {
  text-align: center;
  padding: 8px;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

.confirmation-input {
  margin-bottom: 20px;
  padding: 16px;
  background: #fff6f6;
  border-radius: 6px;
  border: 1px solid #fbc4c4;
}

.confirm-text {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
}

.confirm-input {
  width: 100%;
}

.action-options {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.action-options .el-checkbox {
  display: block;
  margin-bottom: 8px;
}

.action-options .el-checkbox:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-action-dialog {
    width: 90% !important;
  }

  .item-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .item-name {
    margin-right: 0;
  }
}
</style>
