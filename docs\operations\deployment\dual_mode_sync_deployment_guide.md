# 双模式同步部署指南

## 概述

双模式同步机制支持在线同步和离线部署两种模式，适应不同网络环境和安全要求的部署场景。本指南详细介绍双模式同步的部署配置、操作流程和最佳实践。

**⚠️ 重要架构原则：**
- 主节点和从节点必须部署在不同的服务器上
- 主节点强制启用同步功能，不能通过配置关闭
- 从节点仅有一个离线包时，即使过期也不会删除
- 主节点启动时自动生成初始离线包

## 🏗️ 架构概述

### 双模式同步架构

```
┌─────────────────┐    在线同步  ┌─────────────────┐
│   主节点        │ ◄──────────► │   从节点        │
│   (Master)      │              │   (Slave)       │
│                 │              │                 │
│ • 规则管理      │              │ • 规则验证      │
│ • Web界面       │              │ • 本地缓存      │
│ • API服务       │              │ • 高性能处理    │
│ • 数据库连接    │              │                 │
└─────────────────┘              └─────────────────┘
         │                                │
         │ 离线包生成                     │ 离线包导入
         ▼                                ▼
┌─────────────────┐              ┌─────────────────┐
│   离线包        │              │   离线模式      │
│                 │              │                 │
│ • 规则数据      │ ────────────►│ • 无网络连接    │
│ • 版本信息      │   手动传输   │ • 本地验证      │
│ • 完整性校验    │              │ • 定期更新      │
└─────────────────┘              └─────────────────┘
```

### 核心组件

1. **SyncCoordinator**: 统一同步协调器
2. **OfflinePackageManager**: 离线包管理器
3. **SyncCacheManager**: 同步缓存管理器
4. **RuleSyncVersionManager**: 版本管理器

## 📦 部署模式

### 模式1：在线同步部署

适用于网络连通良好的环境，支持实时同步。

#### 主节点配置

```yaml
# docker-compose.master.yml
version: '3.8'
services:
  master:
    image: rule-validation:master
    environment:
      - MODE=master
      - ENABLE_RULE_SYNC=true
      - SYNC_INCREMENTAL_ENABLED=true
      - SYNC_CACHE_SIZE_MB=200
      - OFFLINE_PACKAGE_STORAGE_PATH=/data/packages
    volumes:
      - ./data:/data
      - ./logs:/app/logs
    ports:
      - "18001:18001"
```

#### 从节点配置

```yaml
# docker-compose.slave.yml
version: '3.8'
services:
  slave:
    image: rule-validation:slave
    environment:
      - MODE=slave
      - ENABLE_RULE_SYNC=true
      - MASTER_API_ENDPOINT=http://master:18001
      - SLAVE_API_KEY=your_secure_key
      - SYNC_INCREMENTAL_ENABLED=true
      - CACHE_VERSION_MAX_SIZE=20000
    volumes:
      - ./cache:/app/cache
      - ./logs:/app/logs
    ports:
      - "18002:18001"
```

### 模式2：离线部署

适用于完全封闭的内网环境，通过离线包进行数据同步。

#### 主节点配置（包生成）

```yaml
# docker-compose.offline-master.yml
version: '3.8'
services:
  master:
    image: rule-validation:master
    environment:
      - MODE=master
      - ENABLE_RULE_SYNC=false  # 禁用在线同步
      - OFFLINE_PACKAGE_STORAGE_PATH=/data/packages
      - OFFLINE_PACKAGE_MAX_SIZE_MB=500
      - OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS=90
    volumes:
      - ./data:/data
      - ./packages:/data/packages
```

#### 从节点配置（离线模式）

```yaml
# docker-compose.offline-slave.yml
version: '3.8'
services:
  slave:
    image: rule-validation:slave
    environment:
      - MODE=slave
      - ENABLE_RULE_SYNC=false  # 禁用在线同步
      - OFFLINE_PACKAGE_STORAGE_PATH=/data/packages
      - CACHE_VERSION_TTL_SECONDS=604800  # 7天
    volumes:
      - ./cache:/app/cache
      - ./packages:/data/packages
```

### 模式3：混合部署（不推荐）

**⚠️ 重要说明：主节点和从节点必须部署在不同的服务器上，不能在同一个docker-compose.yml文件中混合部署。**

以下配置仅用于说明不同节点的配置差异，实际部署时应分别在不同服务器上使用：

```yaml
# 主节点服务器 - docker-compose.master.yml
version: '3.8'
services:
  master:
    image: rule-validation:master
    environment:
      - MODE=master
      # 主节点强制启用同步功能，不可配置
      - SYNC_INCREMENTAL_ENABLED=true
      - OFFLINE_PACKAGE_STORAGE_PATH=/data/packages
      - OFFLINE_PACKAGE_CLEANUP_INTERVAL=7200
```

```yaml
# 从节点服务器1（在线模式） - docker-compose.slave-online.yml
version: '3.8'
services:
  slave-online:
    image: rule-validation:slave
    environment:
      - MODE=slave
      - ENABLE_RULE_SYNC=true
      - MASTER_API_ENDPOINT=http://master-server:18001
      - SYNC_AUTO_RECOVERY_ENABLED=true
```

```yaml
# 从节点服务器2（离线模式） - docker-compose.slave-offline.yml
version: '3.8'
services:
  slave-offline:
    image: rule-validation:slave
    environment:
      - MODE=slave
      - ENABLE_RULE_SYNC=false
      - OFFLINE_PACKAGE_STORAGE_PATH=/data/packages
```

## ⚙️ 配置详解

### 同步配置

```bash
# 双模式同步配置
SYNC_INCREMENTAL_ENABLED=true          # 启用增量同步
SYNC_CACHE_SIZE_MB=100                 # 同步缓存大小
SYNC_COMPRESSION_LEVEL=6               # 压缩级别(1-9)
SYNC_VERSION_CACHE_TTL=86400           # 版本缓存TTL
SYNC_CHANGES_CACHE_TTL=7200            # 变更缓存TTL
SYNC_AUTO_RECOVERY_ENABLED=true        # 自动恢复开关
```

### 离线包配置

```bash
# 离线包管理配置
OFFLINE_PACKAGE_STORAGE_PATH=/data/packages    # 存储路径
OFFLINE_PACKAGE_MAX_SIZE_MB=100               # 最大包大小
OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS=30        # 默认过期天数
OFFLINE_PACKAGE_MAX_COUNT=1000                # 最大包数量
OFFLINE_PACKAGE_INTEGRITY_CHECK_ENABLED=true  # 完整性校验
```

### 缓存配置

```bash
# 缓存配置
CACHE_VERSION_MAX_SIZE=10000           # 版本缓存大小
CACHE_VERSION_MAX_MEMORY_MB=20         # 版本缓存内存限制
CACHE_CHANGES_MAX_SIZE=1000            # 变更缓存大小
CACHE_PACKAGE_MAX_SIZE=500             # 包缓存大小
CACHE_PERFORMANCE_MONITORING_ENABLED=true  # 性能监控
```

## 🚀 部署流程

### 在线同步部署流程

1. **准备环境**
```bash
# 创建部署目录
mkdir -p /opt/rule-validation/{data,logs,config}
cd /opt/rule-validation

# 复制配置文件
cp .env.example .env
```

2. **配置环境变量**
```bash
# 编辑配置文件
vim .env

# 主节点配置
MODE=master
ENABLE_RULE_SYNC=true
DATABASE_URL=mysql://user:pass@db:3306/rules
MASTER_API_SECRET_KEY=your_secure_key

# 从节点配置
MODE=slave
MASTER_API_ENDPOINT=http://master-ip:18001
SLAVE_API_KEY=your_secure_key
```

3. **启动服务**
```bash
# 启动主节点
docker-compose -f docker-compose.master.yml up -d

# 启动从节点
docker-compose -f docker-compose.slave.yml up -d
```

4. **验证部署**
```bash
# 检查服务状态
docker-compose ps

# 测试同步功能
curl -H "X-API-KEY: your_key" http://slave-ip:18002/api/v1/rules/sync/status
```

### 离线部署流程

1. **主节点生成离线包**
```bash
# 生成离线包
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{
    "package_name": "rules_package_v1.0",
    "compression_level": 9,
    "expiry_days": 90,
    "description": "生产环境规则包"
  }' \
  http://master-ip:18001/api/v1/rules/offline/generate

# 下载离线包
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/download/package_id \
  -o rules_package.gz
```

2. **传输到从节点**
```bash
# 复制到从节点
scp rules_package.gz user@slave-ip:/data/packages/

# 或使用USB等物理介质传输
```

3. **从节点导入离线包**
```bash
# 导入离线包
curl -X POST -H "X-API-KEY: your_key" \
  -F "package=@/data/packages/rules_package.gz" \
  http://slave-ip:18002/api/v1/rules/offline/import

# 验证导入结果
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/offline/packages
```

## 🔍 监控和维护

### 同步状态监控

```bash
# 查看同步状态
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/status?node_id=slave_001

# 查看缓存统计
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/cache/stats
```

### 离线包管理

```bash
# 列出所有离线包
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/packages

# 清理过期包
curl -X POST -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/cleanup

# 获取存储统计
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/storage/stats
```

### 性能监控

```bash
# 查看同步性能指标
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/metrics

# 查看缓存性能
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/cache/performance
```

## 🛠️ 故障排除

### 常见问题

1. **同步失败**
   - 检查网络连通性
   - 验证API密钥配置
   - 查看同步日志

2. **离线包导入失败**
   - 验证包完整性
   - 检查存储空间
   - 确认包格式正确

3. **缓存问题**
   - 清理缓存数据
   - 调整缓存配置
   - 重启服务

### 日志分析

```bash
# 查看同步日志
docker logs rule-validation-slave | grep "sync"

# 查看离线包日志
docker logs rule-validation-master | grep "offline"

# 查看缓存日志
docker logs rule-validation-slave | grep "cache"
```

## 📋 部署检查清单

### 部署前检查

- [ ] 环境变量配置正确
- [ ] 网络连通性测试通过
- [ ] 存储空间充足
- [ ] 数据库连接正常
- [ ] API密钥配置一致

### 部署后验证

- [ ] 服务启动正常
- [ ] 健康检查通过
- [ ] 同步功能正常
- [ ] 离线包功能正常
- [ ] 缓存功能正常
- [ ] 监控指标正常

### 性能验证

- [ ] 同步性能达标
- [ ] 缓存命中率正常
- [ ] 内存使用合理
- [ ] CPU使用正常
- [ ] 网络带宽充足

## 🔐 安全考虑

### 网络安全

- 使用HTTPS加密传输
- 配置防火墙规则
- 限制API访问来源
- 定期更新API密钥

### 数据安全

- 启用离线包完整性校验
- 定期备份配置和数据
- 监控异常访问行为
- 实施访问控制策略

## 📈 性能优化

### 同步性能优化

- 启用增量同步
- 调整缓存大小
- 优化压缩级别
- 配置合适的TTL

### 离线包优化

- 使用高压缩级别
- 定期清理过期包
- 优化包大小限制
- 批量处理操作

---

**文档版本**: v1.0  
**更新时间**: 2025-08-02  
**适用版本**: 双模式同步机制 v1.0+
