# 数据库脚本管理

本目录包含数据库相关的管理脚本，用于数据库表结构管理、数据迁移和验证等操作。

## 📁 脚本分类

### 数据库表结构管理

#### `rebuild_database_tables.sql`
**用途**: 数据库表结构重建脚本
**功能**: 
- 删除所有旧表（base_rules, rule_data_sets, rule_details）
- 创建新的三表结构（rule_template, rule_detail, rule_field_metadata）
- 建立外键关联关系和索引优化

**使用方法**:
```bash
# 方法1：直接执行SQL文件
mysql -h 192.168.100.192 -P 3306 -u rule_user -pmysql_password rule_service < rebuild_database_tables.sql

# 方法2：使用Python执行脚本
python execute_rebuild.py
```

#### `execute_rebuild.py`
**用途**: Python数据库重建执行脚本
**功能**:
- 连接数据库并执行rebuild_database_tables.sql
- 提供详细的执行日志和错误处理
- 支持事务管理和回滚

**使用方法**:
```bash
python execute_rebuild.py
```

#### `verify_database.py`
**用途**: 数据库结构验证脚本
**功能**:
- 验证表结构是否正确创建
- 检查外键约束是否建立
- 验证索引策略是否生效
- 测试表关联查询功能

**使用方法**:
```bash
python verify_database.py
```

## 🔧 数据库配置

所有脚本使用统一的数据库连接配置：
- **主机**: 192.168.100.192
- **端口**: 3306
- **用户**: rule_user
- **密码**: mysql_password
- **数据库**: rule_service

## ⚠️ 使用注意事项

### 安全提醒
1. **生产环境慎用**: 这些脚本包含DROP TABLE操作，仅适用于开发环境
2. **数据备份**: 执行前请确保重要数据已备份
3. **权限检查**: 确保数据库用户具有足够的DDL权限

### 执行顺序
1. 首先执行 `execute_rebuild.py` 重建表结构
2. 然后执行 `verify_database.py` 验证结果
3. 最后运行相关测试确保功能正常

### 错误处理
- 如果执行失败，检查数据库连接配置
- 查看错误日志，确认具体失败原因
- 必要时手动清理残留的表结构

## 📋 维护记录

| 日期 | 脚本 | 变更内容 | 作者 |
|------|------|----------|------|
| 2025-07-24 | rebuild_database_tables.sql | 创建表结构重建脚本 | 开发团队 |
| 2025-07-24 | execute_rebuild.py | 创建Python执行脚本 | 开发团队 |
| 2025-07-24 | verify_database.py | 创建验证脚本 | 开发团队 |

## 🔗 相关文档

- [数据库表结构设计文档](../docs/database/数据库表结构设计文档-v2.0.md)
- [规则详情表重构实施文档](../docs/project/design/规则详情表重构实施文档.md)
- [数据库字段标准化文档](../docs/development/database/数据库字段标准化文档.md)

---

**维护者**: 规则验证系统开发团队  
**最后更新**: 2025-07-24
