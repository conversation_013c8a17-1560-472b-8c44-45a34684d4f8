"""
ValidationRuleEngine 测试
测试元数据驱动的校验引擎功能
"""

import json
from unittest.mock import Mock

import pytest

from services.rule_detail_service import ServiceError
from services.validation_rule_engine import (
    ValidationResult,
    ValidationRuleEngine,
    ValidationRuleType,
)


class TestValidationRuleEngine:
    """ValidationRuleEngine 测试类"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock()

    @pytest.fixture
    def validation_engine(self, mock_session):
        """创建校验引擎实例"""
        return ValidationRuleEngine(mock_session)

    @pytest.fixture
    def sample_field_metadata(self):
        """示例字段元数据"""
        metadata = Mock()
        metadata.rule_key = "test_rule"
        metadata.field_name = "patient_name"
        metadata.display_name = "患者姓名"
        metadata.is_required = True
        metadata.validation_rule = json.dumps([
            {"type": "max_length", "value": 50, "message": "患者姓名长度不能超过50个字符"},
            {"type": "min_length", "value": 2, "message": "患者姓名长度不能少于2个字符"}
        ])
        metadata.excel_column_order = 1
        return metadata

    def test_get_validation_rules_success(self, validation_engine, mock_session, sample_field_metadata):
        """测试成功获取校验规则"""
        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            sample_field_metadata
        ]

        # 执行测试
        rules = validation_engine.get_validation_rules("test_rule")

        # 验证结果
        assert len(rules) == 3  # 1个必填 + 2个长度校验
        assert any(rule.rule_type == ValidationRuleType.REQUIRED for rule in rules)
        assert any(rule.rule_type == ValidationRuleType.MAX_LENGTH for rule in rules)
        assert any(rule.rule_type == ValidationRuleType.MIN_LENGTH for rule in rules)

    def test_get_validation_rules_no_metadata(self, validation_engine, mock_session):
        """测试没有元数据的情况"""
        # 模拟数据库查询返回空结果
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = []

        # 执行测试
        rules = validation_engine.get_validation_rules("nonexistent_rule")

        # 验证结果
        assert rules == []

    def test_validate_data_success(self, validation_engine, mock_session, sample_field_metadata):
        """测试数据校验成功"""
        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            sample_field_metadata
        ]

        # 测试数据
        test_data = {
            "patient_name": "张三"
        }

        # 执行测试
        result = validation_engine.validate_data("test_rule", test_data)

        # 验证结果
        assert isinstance(result, ValidationResult)
        assert result.valid is True
        assert len(result.errors) == 0
        assert result.field_count == 1
        assert "patient_name" in result.validated_fields

    def test_validate_data_required_field_missing(self, validation_engine, mock_session, sample_field_metadata):
        """测试必填字段缺失"""
        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            sample_field_metadata
        ]

        # 测试数据（缺少必填字段）
        test_data = {}

        # 执行测试
        result = validation_engine.validate_data("test_rule", test_data)

        # 验证结果
        assert result.valid is False
        assert len(result.errors) == 1
        assert result.errors[0].error_code == "REQUIRED_FIELD_MISSING"
        assert result.errors[0].field_name == "patient_name"

    def test_validate_data_max_length_exceeded(self, validation_engine, mock_session, sample_field_metadata):
        """测试最大长度超限"""
        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            sample_field_metadata
        ]

        # 测试数据（超过最大长度）
        test_data = {
            "patient_name": "a" * 51  # 超过50个字符
        }

        # 执行测试
        result = validation_engine.validate_data("test_rule", test_data)

        # 验证结果
        assert result.valid is False
        assert len(result.errors) == 1
        assert result.errors[0].error_code == "MAX_LENGTH_EXCEEDED"

    def test_validate_data_min_length_not_met(self, validation_engine, mock_session, sample_field_metadata):
        """测试最小长度不足"""
        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            sample_field_metadata
        ]

        # 测试数据（少于最小长度）
        test_data = {
            "patient_name": "a"  # 少于2个字符
        }

        # 执行测试
        result = validation_engine.validate_data("test_rule", test_data)

        # 验证结果
        assert result.valid is False
        assert len(result.errors) == 1
        assert result.errors[0].error_code == "MIN_LENGTH_NOT_MET"

    def test_validate_numeric_field(self, validation_engine, mock_session):
        """测试数值字段校验"""
        # 创建数值字段元数据
        numeric_metadata = Mock()
        numeric_metadata.rule_key = "test_rule"
        numeric_metadata.field_name = "age"
        numeric_metadata.display_name = "年龄"
        numeric_metadata.is_required = True
        numeric_metadata.validation_rule = json.dumps([
            {"type": "integer"},
            {"type": "min_value", "value": 0},
            {"type": "max_value", "value": 150}
        ])
        numeric_metadata.excel_column_order = 1

        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            numeric_metadata
        ]

        # 测试有效数值
        test_data = {"age": 25}
        result = validation_engine.validate_data("test_rule", test_data)
        assert result.valid is True

        # 测试无效数值（非整数）
        test_data = {"age": "abc"}
        result = validation_engine.validate_data("test_rule", test_data)
        assert result.valid is False
        assert any(error.error_code == "INVALID_INTEGER_VALUE" for error in result.errors)

        # 测试数值超出范围
        test_data = {"age": 200}
        result = validation_engine.validate_data("test_rule", test_data)
        assert result.valid is False
        assert any(error.error_code == "MAX_VALUE_EXCEEDED" for error in result.errors)

    def test_validate_email_field(self, validation_engine, mock_session):
        """测试邮箱字段校验"""
        # 创建邮箱字段元数据
        email_metadata = Mock()
        email_metadata.rule_key = "test_rule"
        email_metadata.field_name = "email"
        email_metadata.display_name = "邮箱"
        email_metadata.is_required = False
        email_metadata.validation_rule = json.dumps([
            {"type": "email"}
        ])
        email_metadata.excel_column_order = 1

        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            email_metadata
        ]

        # 测试有效邮箱
        test_data = {"email": "<EMAIL>"}
        result = validation_engine.validate_data("test_rule", test_data)
        assert result.valid is True

        # 测试无效邮箱
        test_data = {"email": "invalid-email"}
        result = validation_engine.validate_data("test_rule", test_data)
        assert result.valid is False
        assert any(error.error_code == "INVALID_EMAIL_FORMAT" for error in result.errors)

    def test_validate_date_field(self, validation_engine, mock_session):
        """测试日期字段校验"""
        # 创建日期字段元数据
        date_metadata = Mock()
        date_metadata.rule_key = "test_rule"
        date_metadata.field_name = "birth_date"
        date_metadata.display_name = "出生日期"
        date_metadata.is_required = False
        date_metadata.validation_rule = json.dumps([
            {"type": "date"}
        ])
        date_metadata.excel_column_order = 1

        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            date_metadata
        ]

        # 测试有效日期
        test_data = {"birth_date": "1990-01-01"}
        result = validation_engine.validate_data("test_rule", test_data)
        assert result.valid is True

        # 测试无效日期
        test_data = {"birth_date": "invalid-date"}
        result = validation_engine.validate_data("test_rule", test_data)
        assert result.valid is False
        assert any(error.error_code == "INVALID_DATE_FORMAT" for error in result.errors)

    def test_validate_enum_field(self, validation_engine, mock_session):
        """测试枚举字段校验"""
        # 创建枚举字段元数据
        enum_metadata = Mock()
        enum_metadata.rule_key = "test_rule"
        enum_metadata.field_name = "gender"
        enum_metadata.display_name = "性别"
        enum_metadata.is_required = True
        enum_metadata.validation_rule = json.dumps([
            {"type": "enum", "value": ["男", "女", "未知"]}
        ])
        enum_metadata.excel_column_order = 1

        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            enum_metadata
        ]

        # 测试有效枚举值
        test_data = {"gender": "男"}
        result = validation_engine.validate_data("test_rule", test_data)
        assert result.valid is True

        # 测试无效枚举值
        test_data = {"gender": "其他"}
        result = validation_engine.validate_data("test_rule", test_data)
        assert result.valid is False
        assert any(error.error_code == "INVALID_ENUM_VALUE" for error in result.errors)

    def test_cache_functionality(self, validation_engine, mock_session, sample_field_metadata):
        """测试缓存功能"""
        # 模拟数据库查询
        mock_query = mock_session.query.return_value.filter.return_value.order_by.return_value.all
        mock_query.return_value = [sample_field_metadata]

        # 第一次调用
        rules1 = validation_engine.get_validation_rules("test_rule")
        assert mock_query.call_count == 1

        # 第二次调用（应该使用缓存）
        rules2 = validation_engine.get_validation_rules("test_rule")
        assert mock_query.call_count == 1  # 没有增加调用次数
        assert len(rules1) == len(rules2)

        # 强制重新加载
        rules3 = validation_engine.get_validation_rules("test_rule", force_reload=True)
        assert mock_query.call_count == 2  # 增加了调用次数

    def test_clear_cache(self, validation_engine, mock_session, sample_field_metadata):
        """测试清除缓存"""
        # 模拟数据库查询
        mock_query = mock_session.query.return_value.filter.return_value.order_by.return_value.all
        mock_query.return_value = [sample_field_metadata]

        # 加载规则到缓存
        validation_engine.get_validation_rules("test_rule")

        # 清除特定规则的缓存
        validation_engine.clear_cache("test_rule")

        # 再次获取规则（应该重新查询数据库）
        validation_engine.get_validation_rules("test_rule")
        assert mock_query.call_count == 2

        # 清除所有缓存
        validation_engine.clear_cache()
        cache_stats = validation_engine.get_cache_stats()
        assert cache_stats["rule_cache_size"] == 0

    def test_database_error_handling(self, validation_engine, mock_session):
        """测试数据库错误处理"""
        # 模拟数据库查询异常
        mock_session.query.side_effect = Exception("Database connection failed")

        # 执行测试
        with pytest.raises(ServiceError) as exc_info:
            validation_engine.get_validation_rules("test_rule")

        # 验证异常
        assert exc_info.value.error_code == "VALIDATION_RULES_LOAD_FAILED"
        assert "Database connection failed" in str(exc_info.value)

    def test_validation_exception_handling(self, validation_engine, mock_session):
        """测试校验异常处理"""
        # 创建会导致异常的元数据
        problematic_metadata = Mock()
        problematic_metadata.rule_key = "test_rule"
        problematic_metadata.field_name = "test_field"
        problematic_metadata.display_name = "测试字段"
        problematic_metadata.is_required = False
        problematic_metadata.validation_rule = "invalid_json"  # 无效的JSON
        problematic_metadata.excel_column_order = 1

        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            problematic_metadata
        ]

        # 执行测试（应该能处理JSON解析错误）
        rules = validation_engine.get_validation_rules("test_rule")

        # 验证结果（应该返回空规则列表，而不是抛出异常）
        assert isinstance(rules, list)

    def test_get_cache_stats(self, validation_engine, mock_session, sample_field_metadata):
        """测试获取缓存统计信息"""
        # 模拟数据库查询
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [
            sample_field_metadata
        ]

        # 加载一些规则到缓存
        validation_engine.get_validation_rules("test_rule")

        # 获取缓存统计
        stats = validation_engine.get_cache_stats()

        # 验证统计信息
        assert "rule_cache_size" in stats
        assert "metadata_cache_size" in stats
        assert "cache_ttl" in stats
        assert "cached_rule_keys" in stats
        assert stats["rule_cache_size"] >= 1
        assert "test_rule" in stats["cached_rule_keys"]
