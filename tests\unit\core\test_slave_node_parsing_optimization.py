"""
从节点索引构建器文件解析性能优化测试
验证流式处理、数据完整性验证、性能统计等功能
"""

import gzip
import json
import os
import tempfile
import unittest
from unittest.mock import Mock, patch

from core.slave_node_index_builder import SlaveNode<PERSON>ndexBuilder, FileParsingStats


class TestSlaveNodeParsingOptimization(unittest.TestCase):
    """从节点文件解析性能优化测试类"""

    def setUp(self):
        """设置测试环境"""
        # 模拟环境变量
        self.env_patcher = patch.dict(
            "os.environ",
            {
                "SLAVE_INDEX_MEMORY_LIMIT_MB": "300",
                "SLAVE_INDEX_GC_THRESHOLD": "500",
            },
        )
        self.env_patcher.start()

        # 创建临时缓存文件
        self.temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".json.gz", delete=False)
        self.temp_file.close()

        # 模拟rule_index_manager
        self.mock_rule_index_manager = Mock()

    def tearDown(self):
        """清理测试环境"""
        self.env_patcher.stop()
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def _create_test_cache_file(self, rule_count: int = 100):
        """创建测试用的缓存文件"""
        test_data = {
            "rule_details": [
                {
                    "rule_id": f"test_rule_{i}",
                    "yb_code": f"Y{i:03d}",
                    "diag_whole_code": f"D{i:03d}",
                    "diag_code_prefix": f"DP{i:03d}",
                    "fee_whole_code": f"F{i:03d}",
                    "fee_code_prefix": f"FP{i:03d}",
                    "extended_fields": {"test_field": f"value_{i}"},
                }
                for i in range(rule_count)
            ]
        }

        with gzip.open(self.temp_file.name, "wt", encoding="utf-8") as f:
            json.dump(test_data, f)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_data_integrity_validation(self, mock_memory_optimizer_class):
        """测试数据完整性验证"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 测试有效数据
        valid_data = {"rule_details": [{"rule_id": "test_1", "yb_code": "Y001"}, {"rule_id": "test_2", "yb_code": "Y002"}]}
        is_valid, msg = builder._validate_cache_data_integrity(valid_data)
        self.assertTrue(is_valid)
        self.assertIn("数据完整性验证通过", msg)

        # 测试无效数据 - 非字典格式
        invalid_data = "not a dict"
        is_valid, msg = builder._validate_cache_data_integrity(invalid_data)
        self.assertFalse(is_valid)
        self.assertIn("不是有效的字典格式", msg)

        # 测试无效数据 - 缺少规则数据
        empty_data = {}
        is_valid, msg = builder._validate_cache_data_integrity(empty_data)
        self.assertFalse(is_valid)
        self.assertIn("未找到有效的规则数据字段", msg)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_optimal_batch_size_calculation(self, mock_memory_optimizer_class):
        """测试最优批次大小计算"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 测试不同规模的数据
        self.assertEqual(builder._calculate_optimal_batch_size(100), 100)  # 小数据集
        self.assertEqual(builder._calculate_optimal_batch_size(5000), 500)  # 中等数据集 (min(5000//5, 500) = 500)
        self.assertEqual(builder._calculate_optimal_batch_size(50000), 1000)  # 大数据集
        self.assertEqual(builder._calculate_optimal_batch_size(200000), 2000)  # 超大数据集

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_parsing_performance_stats(self, mock_memory_optimizer_class):
        """测试解析性能统计"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        # 模拟内存状态
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 创建测试数据
        self._create_test_cache_file(100)

        # 执行解析
        rule_details = builder._load_rule_details_from_cache()

        # 验证解析结果
        self.assertEqual(len(rule_details), 100)

        # 验证性能统计
        stats = builder.get_parsing_stats()
        self.assertIsInstance(stats, FileParsingStats)
        self.assertEqual(stats.total_rules, 100)
        self.assertEqual(stats.valid_rules, 100)
        self.assertEqual(stats.invalid_rules, 0)
        self.assertGreater(stats.parse_time_ms, 0)
        self.assertGreater(stats.file_size_mb, 0)
        self.assertEqual(stats.get_success_rate(), 100.0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_safe_string_extraction(self, mock_memory_optimizer_class):
        """测试安全字符串提取"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 测试正常情况
        data = {"test_key": "test_value"}
        result = builder._safe_get_string(data, "test_key", "default")
        self.assertEqual(result, "test_value")

        # 测试缺少键的情况
        result = builder._safe_get_string(data, "missing_key", "default")
        self.assertEqual(result, "default")

        # 测试None值的情况
        data_with_none = {"test_key": None}
        result = builder._safe_get_string(data_with_none, "test_key", "default")
        self.assertEqual(result, "default")

        # 测试数字转字符串
        data_with_number = {"test_key": 123}
        result = builder._safe_get_string(data_with_number, "test_key", "default")
        self.assertEqual(result, "123")

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_rule_detail_creation_optimization(self, mock_memory_optimizer_class):
        """测试规则对象创建优化"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 测试正常规则数据
        rule_data = {"rule_id": "test_123", "yb_code": "Y123", "extended_fields": {"key": "value"}}

        rule_detail = builder._create_rule_detail_from_dict(rule_data)
        self.assertIsNotNone(rule_detail)
        self.assertEqual(rule_detail.rule_id, "test_123")
        self.assertEqual(rule_detail.yb_code, "Y123")
        self.assertIsInstance(rule_detail.extended_fields, str)

        # 测试无效数据
        invalid_data = "not a dict"
        rule_detail = builder._create_rule_detail_from_dict(invalid_data)
        self.assertIsNone(rule_detail)

        # 测试缺少rule_id
        missing_id_data = {"yb_code": "Y123"}
        rule_detail = builder._create_rule_detail_from_dict(missing_id_data)
        self.assertIsNone(rule_detail)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_memory_optimization_during_parsing(self, mock_memory_optimizer_class):
        """测试解析过程中的内存优化"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        # 模拟内存压力情况
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = True
        mock_memory_stats.process_memory_mb = 350.0  # 超过300MB限制
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 创建大量测试数据触发内存优化
        self._create_test_cache_file(5000)

        # 执行解析
        rule_details = builder._load_rule_details_from_cache()

        # 验证内存优化被调用
        self.assertTrue(mock_memory_optimizer.optimize_memory.called)

        # 验证解析统计记录了内存优化次数
        stats = builder.get_parsing_stats()
        self.assertGreater(stats.memory_optimizations, 0)


if __name__ == "__main__":
    unittest.main()
