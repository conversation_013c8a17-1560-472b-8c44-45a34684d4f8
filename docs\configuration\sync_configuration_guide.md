# 双模式同步配置指南

## 概述

本文档详细介绍双模式同步机制的配置选项，包括在线同步、离线包管理、缓存配置等。所有配置项都支持通过环境变量进行设置。

## 配置分类

### 1. 双模式同步配置

#### 基础同步配置

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `SYNC_INCREMENTAL_ENABLED` | `true` | 是否启用增量同步 | `SYNC_INCREMENTAL_ENABLED` |
| `SYNC_CACHE_SIZE_MB` | `100` | 同步缓存大小（MB） | `SYNC_CACHE_SIZE_MB` |
| `SYNC_PACKAGE_RETENTION_DAYS` | `30` | 同步包保留天数 | `SYNC_PACKAGE_RETENTION_DAYS` |
| `SYNC_COMPRESSION_LEVEL` | `6` | 同步压缩级别（1-9） | `SYNC_COMPRESSION_LEVEL` |

#### 缓存TTL配置

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `SYNC_VERSION_CACHE_TTL` | `86400` | 版本缓存TTL（秒） | `SYNC_VERSION_CACHE_TTL` |
| `SYNC_CHANGES_CACHE_TTL` | `7200` | 变更缓存TTL（秒） | `SYNC_CHANGES_CACHE_TTL` |
| `SYNC_RESULT_CACHE_TTL` | `1800` | 同步结果缓存TTL（秒） | `SYNC_RESULT_CACHE_TTL` |

#### 同步控制配置

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `SYNC_STATUS_CHECK_INTERVAL` | `5.0` | 同步状态检查间隔（秒） | `SYNC_STATUS_CHECK_INTERVAL` |
| `SYNC_NETWORK_PARTITION_THRESHOLD` | `3` | 网络分区检测阈值 | `SYNC_NETWORK_PARTITION_THRESHOLD` |
| `SYNC_AUTO_RECOVERY_ENABLED` | `true` | 同步自动恢复开关 | `SYNC_AUTO_RECOVERY_ENABLED` |

### 2. 离线包管理配置

#### 存储配置

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `OFFLINE_PACKAGE_STORAGE_PATH` | `"data/offline_packages"` | 离线包存储路径 | `OFFLINE_PACKAGE_STORAGE_PATH` |
| `OFFLINE_PACKAGE_MAX_SIZE_MB` | `100` | 离线包最大大小（MB） | `OFFLINE_PACKAGE_MAX_SIZE_MB` |
| `OFFLINE_PACKAGE_MAX_COUNT` | `1000` | 离线包最大数量 | `OFFLINE_PACKAGE_MAX_COUNT` |

#### 生命周期配置

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `OFFLINE_PACKAGE_CLEANUP_INTERVAL` | `3600` | 离线包清理间隔（秒） | `OFFLINE_PACKAGE_CLEANUP_INTERVAL` |
| `OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS` | `30` | 离线包默认过期天数 | `OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS` |

#### 处理配置

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `OFFLINE_PACKAGE_DEFAULT_COMPRESSION_LEVEL` | `6` | 离线包默认压缩级别（1-9） | `OFFLINE_PACKAGE_DEFAULT_COMPRESSION_LEVEL` |
| `OFFLINE_PACKAGE_GENERATION_TIMEOUT` | `300.0` | 离线包生成超时（秒） | `OFFLINE_PACKAGE_GENERATION_TIMEOUT` |
| `OFFLINE_PACKAGE_DOWNLOAD_TIMEOUT` | `120.0` | 离线包下载超时（秒） | `OFFLINE_PACKAGE_DOWNLOAD_TIMEOUT` |
| `OFFLINE_PACKAGE_INTEGRITY_CHECK_ENABLED` | `true` | 离线包校验开关 | `OFFLINE_PACKAGE_INTEGRITY_CHECK_ENABLED` |

### 3. 缓存配置

#### 版本缓存

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `CACHE_VERSION_MAX_SIZE` | `10000` | 版本缓存最大条目数 | `CACHE_VERSION_MAX_SIZE` |
| `CACHE_VERSION_MAX_MEMORY_MB` | `20` | 版本缓存最大内存（MB） | `CACHE_VERSION_MAX_MEMORY_MB` |
| `CACHE_VERSION_TTL_SECONDS` | `86400` | 版本缓存TTL（秒） | `CACHE_VERSION_TTL_SECONDS` |
| `CACHE_VERSION_CLEANUP_INTERVAL` | `3600` | 版本缓存清理间隔（秒） | `CACHE_VERSION_CLEANUP_INTERVAL` |

#### 变更缓存

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `CACHE_CHANGES_MAX_SIZE` | `1000` | 变更缓存最大条目数 | `CACHE_CHANGES_MAX_SIZE` |
| `CACHE_CHANGES_MAX_MEMORY_MB` | `100` | 变更缓存最大内存（MB） | `CACHE_CHANGES_MAX_MEMORY_MB` |
| `CACHE_CHANGES_TTL_SECONDS` | `7200` | 变更缓存TTL（秒） | `CACHE_CHANGES_TTL_SECONDS` |
| `CACHE_CHANGES_CLEANUP_INTERVAL` | `600` | 变更缓存清理间隔（秒） | `CACHE_CHANGES_CLEANUP_INTERVAL` |

#### 包缓存

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `CACHE_PACKAGE_MAX_SIZE` | `500` | 包缓存最大条目数 | `CACHE_PACKAGE_MAX_SIZE` |
| `CACHE_PACKAGE_MAX_MEMORY_MB` | `50` | 包缓存最大内存（MB） | `CACHE_PACKAGE_MAX_MEMORY_MB` |
| `CACHE_PACKAGE_TTL_SECONDS` | `3600` | 包缓存TTL（秒） | `CACHE_PACKAGE_TTL_SECONDS` |
| `CACHE_PACKAGE_CLEANUP_INTERVAL` | `300` | 包缓存清理间隔（秒） | `CACHE_PACKAGE_CLEANUP_INTERVAL` |

#### 同步结果缓存

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `CACHE_SYNC_RESULT_MAX_SIZE` | `1000` | 同步结果缓存最大条目数 | `CACHE_SYNC_RESULT_MAX_SIZE` |
| `CACHE_SYNC_RESULT_MAX_MEMORY_MB` | `30` | 同步结果缓存最大内存（MB） | `CACHE_SYNC_RESULT_MAX_MEMORY_MB` |
| `CACHE_SYNC_RESULT_TTL_SECONDS` | `1800` | 同步结果缓存TTL（秒） | `CACHE_SYNC_RESULT_TTL_SECONDS` |
| `CACHE_SYNC_RESULT_CLEANUP_INTERVAL` | `180` | 同步结果缓存清理间隔（秒） | `CACHE_SYNC_RESULT_CLEANUP_INTERVAL` |

#### 缓存监控

| 配置项 | 默认值 | 说明 | 环境变量 |
|--------|--------|------|----------|
| `CACHE_PERFORMANCE_MONITORING_ENABLED` | `true` | 缓存性能监控开关 | `CACHE_PERFORMANCE_MONITORING_ENABLED` |
| `CACHE_STATS_COLLECTION_INTERVAL` | `60.0` | 缓存统计收集间隔（秒） | `CACHE_STATS_COLLECTION_INTERVAL` |

## 配置验证

### 自动验证

系统启动时会自动验证配置的有效性，包括：

1. **压缩级别验证**：确保压缩级别在1-9范围内
2. **缓存大小验证**：确保缓存大小配置为正数
3. **TTL验证**：确保TTL配置为正数
4. **路径验证**：确保存储路径不为空
5. **缓存配置验证**：确保所有缓存配置项为正数

### 手动验证

可以通过以下方式手动验证配置：

```python
from config.settings import Settings

settings = Settings()

# 验证同步配置
is_valid, error_msg = settings.validate_sync_config()
if not is_valid:
    print(f"同步配置错误: {error_msg}")

# 验证数据库配置
is_valid, error_msg = settings.validate_database_config()
if not is_valid:
    print(f"数据库配置错误: {error_msg}")
```

## 配置示例

### 开发环境配置

```bash
# 开发环境 - 更快的同步和较小的缓存
SYNC_INCREMENTAL_ENABLED=true
SYNC_CACHE_SIZE_MB=50
SYNC_COMPRESSION_LEVEL=3
SYNC_VERSION_CACHE_TTL=3600
SYNC_CHANGES_CACHE_TTL=1800

# 离线包配置
OFFLINE_PACKAGE_STORAGE_PATH=./dev_packages
OFFLINE_PACKAGE_MAX_SIZE_MB=50
OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS=7

# 缓存配置
CACHE_VERSION_MAX_SIZE=5000
CACHE_CHANGES_MAX_SIZE=500
CACHE_PACKAGE_MAX_SIZE=100
```

### 生产环境配置

```bash
# 生产环境 - 更大的缓存和更长的TTL
SYNC_INCREMENTAL_ENABLED=true
SYNC_CACHE_SIZE_MB=500
SYNC_COMPRESSION_LEVEL=9
SYNC_VERSION_CACHE_TTL=86400
SYNC_CHANGES_CACHE_TTL=14400

# 离线包配置
OFFLINE_PACKAGE_STORAGE_PATH=/data/offline_packages
OFFLINE_PACKAGE_MAX_SIZE_MB=1000
OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS=90

# 缓存配置
CACHE_VERSION_MAX_SIZE=50000
CACHE_CHANGES_MAX_SIZE=10000
CACHE_PACKAGE_MAX_SIZE=2000
```

### 离线模式配置

```bash
# 离线模式 - 禁用在线同步，依赖离线包
ENABLE_RULE_SYNC=false
SYNC_INCREMENTAL_ENABLED=false

# 离线包配置
OFFLINE_PACKAGE_STORAGE_PATH=/data/offline_packages
OFFLINE_PACKAGE_MAX_SIZE_MB=2000
OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS=180
OFFLINE_PACKAGE_INTEGRITY_CHECK_ENABLED=true

# 缓存配置 - 更长的TTL
CACHE_VERSION_TTL_SECONDS=604800  # 7天
CACHE_CHANGES_TTL_SECONDS=86400   # 1天
CACHE_PACKAGE_TTL_SECONDS=43200   # 12小时
```

## 最佳实践

### 1. 缓存配置优化

- **开发环境**：使用较小的缓存大小和较短的TTL，便于测试
- **生产环境**：使用较大的缓存大小和较长的TTL，提高性能
- **内存限制环境**：减少缓存大小，增加清理频率

### 2. 离线包配置优化

- **网络良好环境**：使用较高的压缩级别，减少传输时间
- **存储限制环境**：使用较短的过期时间，定期清理
- **安全要求高的环境**：启用完整性校验

### 3. 同步配置优化

- **实时性要求高**：启用增量同步，减少状态检查间隔
- **网络不稳定环境**：增加网络分区阈值，启用自动恢复
- **资源限制环境**：减少缓存大小，增加清理频率

## 故障排除

### 常见配置错误

1. **压缩级别超出范围**
   - 错误：`SYNC_COMPRESSION_LEVEL=10`
   - 解决：设置为1-9之间的值

2. **缓存大小为0或负数**
   - 错误：`SYNC_CACHE_SIZE_MB=0`
   - 解决：设置为正数

3. **存储路径为空**
   - 错误：`OFFLINE_PACKAGE_STORAGE_PATH=""`
   - 解决：设置有效的存储路径

4. **TTL配置为0**
   - 错误：`SYNC_VERSION_CACHE_TTL=0`
   - 解决：设置为正数

### 配置验证失败处理

当配置验证失败时，系统会：

1. 记录详细的错误信息到日志
2. 使用默认配置值继续运行
3. 在管理界面显示配置警告
4. 提供配置修复建议

### 性能调优建议

1. **监控缓存命中率**：通过缓存统计调整缓存大小
2. **监控内存使用**：根据实际内存使用调整缓存配置
3. **监控同步性能**：根据同步耗时调整压缩级别和缓存TTL
4. **定期清理**：根据存储使用情况调整清理间隔
