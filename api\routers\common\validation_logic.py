"""
统一的校验逻辑实现
主从节点共享相同的校验处理逻辑，确保行为一致性
集成超快速规则校验引擎，实现极致性能优化
"""

import asyncio
import time

from fastapi import HTTPException, Request

from config.settings import settings
from core.constants.error_codes import ErrorCodes
from core.logging.logging_system import log as logger
from core.middleware.request_tracking import request_tracker
from models.api import ApiResponse
from models.rule import RuleRequest, RuleResult


async def unified_validate_patient_data(
    http_request: Request,
    request: RuleRequest,
    request_queue: asyncio.Queue,
    node_type: str,  # "master" 或 "slave"
) -> ApiResponse[list[RuleResult]]:
    """
    统一的患者数据校验逻辑
    主从节点使用完全相同的实现
    Args:
        http_request: HTTP请求对象
        request: 规则校验请求
        request_queue: 请求队列
        node_type: 节点类型标识

    Returns:
        ApiResponse[list[RuleResult]]: 统一的校验响应
    """
    request_id = getattr(http_request.state, "request_id", None)

    # 统一的请求跟踪
    if request_id:
        request_tracker.add_event(
            request_id,
            "validation_request",
            {
                "endpoint": "/api/v1/validate",
                "source": node_type,
                "patient_bah": getattr(request.patientInfo, "bah", "unknown"),
            },
        )

    # 检查队列可用性
    if not request_queue:
        logger.error(f"Service not ready: {request_id}")
        raise HTTPException(
            status_code=ErrorCodes.SERVICE_UNAVAILABLE, 
            detail="Service not ready"
        )

    start_time = time.perf_counter()

    try:
        logger.info(f"Starting ultra-fast validation on {node_type} node: {request_id}")

        # 检查是否启用超快速校验
        use_ultra_fast = getattr(settings, "ENABLE_ULTRA_FAST_VALIDATION", True)

        if use_ultra_fast:
            # 【新增】规则预过滤逻辑
            if settings.ENABLE_RULE_PREFILTER:
                try:
                    # 导入规则预过滤器
                    from core.rule_prefilter import rule_prefilter

                    # 执行规则预过滤
                    filter_result = rule_prefilter.filter_rules_for_patient(request.patientInfo, request.ids)

                    # 更新请求中的规则ID列表
                    request.ids = filter_result.filtered_rule_ids

                    # 记录过滤统计
                    logger.info(
                        f"规则预过滤完成: {request_id} - "
                        f"原始规则: {filter_result.original_rule_count}, "
                        f"过滤后: {filter_result.filtered_rule_count}, "
                        f"过滤率: {filter_result.filter_rate:.1%}, "
                        f"耗时: {filter_result.filter_time:.2f}ms"
                    )

                    # 添加请求跟踪事件
                    if request_id:
                        request_tracker.add_event(
                            request_id,
                            "rule_prefilter_applied",
                            {
                                "original_rules": filter_result.original_rule_count,
                                "filtered_rules": filter_result.filtered_rule_count,
                                "filter_rate": filter_result.filter_rate,
                                "filter_time_ms": filter_result.filter_time,
                                "node_type": node_type,
                            },
                        )

                except Exception as e:
                    # 过滤失败时降级到原流程
                    logger.warning(f"规则预过滤失败，降级到全量校验: {request_id} - {e}")

                    # 添加降级事件跟踪
                    if request_id:
                        request_tracker.add_event(
                            request_id,
                            "rule_prefilter_fallback",
                            {"fallback_reason": str(e), "node_type": node_type},
                        )

            # 使用超快速校验引擎（直接处理，无需队列）
            from services.ultra_fast_rule_service import ultra_fast_rule_service

            violations, ultra_stats = await ultra_fast_rule_service.validate_rules_ultra_fast(
                request.patientInfo, request.ids
            )

            result_data = violations
            execution_time = ultra_stats.total_time_ms / 1000  # 转换为秒

            # 记录详细性能信息（包含预过滤信息）
            prefilter_info = ""
            if settings.ENABLE_RULE_PREFILTER:
                try:
                    from core.rule_prefilter import rule_prefilter

                    stats = rule_prefilter.get_performance_stats()
                    if stats["filter_count"] > 0:
                        prefilter_info = (
                            f"预过滤: 平均{stats['avg_filter_time_ms']:.2f}ms, 过滤率: {stats['avg_filter_rate']:.1%}, "
                        )
                except Exception:
                    pass  # 忽略统计获取失败

            logger.info(
                f"Ultra-fast validation stats: {request_id} - "
                f"{prefilter_info}"
                f"规则筛选: {ultra_stats.filtered_rules_count}/{ultra_stats.total_rules_candidate} "
                f"(减少 {ultra_stats.filter_reduction_percentage:.1f}%), "
                f"预处理: {ultra_stats.preprocessing_time_ms:.1f}ms, "
                f"筛选: {ultra_stats.filtering_time_ms:.1f}ms, "
                f"校验: {ultra_stats.validation_time_ms:.1f}ms, "
                f"内存使用: {ultra_stats.memory_usage_mb:.1f}MB"
            )

        else:
            # 传统队列处理逻辑
            future = asyncio.get_running_loop().create_future()
            await request_queue.put((request, future))

            logger.info(f"Request enqueued: {request_id} - Queue size: {request_queue.qsize()}")

            # 等待处理结果
            result_data = await asyncio.wait_for(future, timeout=settings.REQUEST_TIMEOUT)
            execution_time = time.perf_counter() - start_time

        logger.info(
            f"Validation complete: {request_id} - Time: {execution_time:.4f}s, "
            f"Violations: {len(result_data)}"
        )

        # 统一的完成事件跟踪
        if request_id:
            request_tracker.add_event(
                request_id,
                "validation_completed",
                {
                    "violations_count": len(result_data),
                    "execution_time": execution_time,
                    "success": True,
                },
            )
            request_tracker.complete_request(request_id, success=True)

        # 统一的响应格式
        return ApiResponse.success_response(
            data=result_data,  # list[RuleResult]
            message=f"验证完成，发现 {len(result_data)} 个违规项，耗时 {(execution_time * 1000):.2f}ms",
            request_id=request_id,
        )

    except asyncio.QueueFull:
        logger.warning(f"Request queue is full: {request_id}")

        # 统一的队列满错误处理
        if request_id:
            request_tracker.add_event(
                request_id, 
                "queue_full_error", 
                {"queue_size": request_queue.qsize()}
            )
            request_tracker.complete_request(request_id, success=False)

        raise HTTPException(
            status_code=ErrorCodes.TOO_MANY_REQUESTS,
            detail="Service is busy, please try again later.",
        ) from None

    except asyncio.TimeoutError:  # noqa: UP041
        execution_time = time.perf_counter() - start_time
        logger.error(f"Validation timeout: {request_id} - {execution_time:.4f}s")

        # 统一的超时错误处理
        if request_id:
            request_tracker.add_event(
                request_id,
                "validation_timeout",
                {"execution_time": execution_time}
            )
            request_tracker.complete_request(request_id, success=False)

        raise HTTPException(
            status_code=ErrorCodes.REQUEST_TIMEOUT,
            detail="Request timeout"
        ) from None

    except Exception as e:
        execution_time = time.perf_counter() - start_time
        logger.error(f"Validation failed on {node_type} node: {request_id} - {e}", exc_info=True)

        # 统一的异常错误处理
        if request_id:
            request_tracker.add_event(
                request_id,
                "validation_error",
                {"error": str(e), "execution_time": execution_time}
            )
            request_tracker.complete_request(request_id, success=False)

        raise HTTPException(
            status_code=ErrorCodes.INTERNAL_ERROR,
            detail=f"校验过程中发生错误: {str(e)}"
        ) from e


async def unified_validation_worker(
    queue: asyncio.Queue, 
    rule_service, 
    worker_name: str
):
    """
    统一的验证工作进程
    主从节点使用完全相同的工作进程逻辑

    Args:
        queue: 请求队列
        rule_service: 规则服务实例
        worker_name: 工作进程名称
    """
    logger.info(f"Validation worker {worker_name} started")

    while True:
        try:
            # 从队列获取请求
            request, future = await queue.get()

            try:
                # 执行统一的验证逻辑
                violations = await rule_service.validate_rules(request.patientInfo, request.ids)
                future.set_result(violations)
            except Exception as e:
                future.set_exception(e)
            finally:
                queue.task_done()

        except asyncio.CancelledError:
            logger.info(f"Validation worker {worker_name} cancelled")
            break
        except Exception as e:
            logger.error(f"Validation worker {worker_name} error: {e}", exc_info=True)
