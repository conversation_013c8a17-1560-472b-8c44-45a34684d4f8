"""
前端校验规则生成器
自动生成前端校验规则，确保前后端校验规则同步
"""

import time
from pathlib import Path
from typing import Any

from sqlalchemy.orm import Session

from core.db_session import get_db_session
from core.logging.logging_system import log as logger
from services.metadata_validation_service import MetadataValidationService


class FrontendValidationGenerator:
    """前端校验规则生成器"""

    def __init__(self, output_dir: str = "frontend/src/utils/validation"):
        """
        初始化生成器

        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def generate_all_rules(self, rule_keys: list[str] = None) -> dict[str, Any]:
        """
        生成所有规则的前端校验配置

        Args:
            rule_keys: 规则键列表，为空时生成所有规则

        Returns:
            Dict: 生成结果统计
        """
        start_time = time.perf_counter()
        results = {
            "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_rules": 0,
            "success_count": 0,
            "error_count": 0,
            "errors": [],
            "generated_files": [],
        }

        try:
            with get_db_session() as session:
                validation_service = MetadataValidationService(session)

                # 如果没有指定规则键，获取所有规则键
                if not rule_keys:
                    rule_keys = self._get_all_rule_keys(session)

                results["total_rules"] = len(rule_keys)

                # 生成每个规则的校验配置
                for rule_key in rule_keys:
                    try:
                        self._generate_rule_validation(validation_service, rule_key)
                        results["success_count"] += 1
                        logger.info(f"成功生成规则 '{rule_key}' 的前端校验配置")
                    except Exception as e:
                        error_msg = f"生成规则 '{rule_key}' 的前端校验配置失败: {str(e)}"
                        logger.error(error_msg)
                        results["error_count"] += 1
                        results["errors"].append({"rule_key": rule_key, "error": str(e)})

                # 生成统一的校验工具文件
                self._generate_validation_utils()
                results["generated_files"].append("validationUtils.js")

                # 生成索引文件
                self._generate_index_file(rule_keys)
                results["generated_files"].append("index.js")

                duration = time.perf_counter() - start_time
                logger.info(
                    f"前端校验规则生成完成: total={results['total_rules']}, "
                    f"success={results['success_count']}, errors={results['error_count']}, "
                    f"duration={duration:.3f}s"
                )

                return results

        except Exception as e:
            error_msg = f"生成前端校验规则失败: {str(e)}"
            logger.error(error_msg)
            results["errors"].append({"error": error_msg})
            return results

    def _generate_rule_validation(self, validation_service: MetadataValidationService, rule_key: str):
        """生成单个规则的校验配置"""
        # 获取前端校验规则
        frontend_rules = validation_service.get_frontend_validation_rules(rule_key)

        # 生成JavaScript文件内容
        js_content = self._generate_js_content(rule_key, frontend_rules)

        # 写入文件
        output_file = self.output_dir / f"{rule_key}Validation.js"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(js_content)

    def _generate_js_content(self, rule_key: str, frontend_rules: dict[str, Any]) -> str:
        """生成JavaScript文件内容"""
        fields = frontend_rules.get("fields", {})

        # 生成字段校验规则
        field_rules = []
        for field_name, field_config in fields.items():
            rules = field_config.get("rules", [])

            # 转换规则格式
            js_rules = []
            for rule in rules:
                rule_type = rule.get("type")
                message = rule.get("message")
                value = rule.get("value")

                if rule_type == "required":
                    js_rules.append(f"{{ required: true, message: '{message}', trigger: 'blur' }}")
                elif rule_type == "max_length":
                    js_rules.append(f"{{ max: {value}, message: '{message}', trigger: 'blur' }}")
                elif rule_type == "min_length":
                    js_rules.append(f"{{ min: {value}, message: '{message}', trigger: 'blur' }}")
                elif rule_type == "pattern":
                    js_rules.append(f"{{ pattern: /{value}/, message: '{message}', trigger: 'blur' }}")
                elif rule_type == "email":
                    js_rules.append(f"{{ type: 'email', message: '{message}', trigger: 'blur' }}")
                elif rule_type == "integer":
                    js_rules.append(f"{{ type: 'integer', message: '{message}', trigger: 'blur' }}")
                elif rule_type == "float":
                    js_rules.append(f"{{ type: 'number', message: '{message}', trigger: 'blur' }}")
                elif rule_type in ["min_value", "max_value"]:
                    # 自定义校验器
                    validator_name = f"validate{field_name.title()}{rule_type.title().replace('_', '')}"
                    js_rules.append(f"{{ validator: {validator_name}, trigger: 'blur' }}")

            if js_rules:
                field_rules.append(f"  {field_name}: [\n    {',\\n    '.join(js_rules)}\n  ]")

        # 生成自定义校验器
        validators = self._generate_validators(fields)

        # 生成完整的JavaScript内容
        js_content = f"""/**
 * {rule_key} 规则校验配置
 * 自动生成于 {frontend_rules.get("generated_at", "")}
 * 请勿手动修改此文件
 */

{validators}

/**
 * {rule_key} 表单校验规则
 */
export const {rule_key}ValidationRules = {{
{",\\n".join(field_rules)}
}}

/**
 * {rule_key} 字段中文名称映射
 */
export const {rule_key}FieldNames = {{
{",\\n".join([f'  {field_name}: "{config.get("chinese_name", field_name)}"' for field_name, config in fields.items()])}
}}

/**
 * 获取字段的中文名称
 * @param {{string}} fieldName - 字段名
 * @returns {{string}} 中文名称
 */
export function get{rule_key.title()}FieldName(fieldName) {{
  return {rule_key}FieldNames[fieldName] || fieldName
}}

/**
 * 验证单个字段
 * @param {{string}} fieldName - 字段名
 * @param {{any}} value - 字段值
 * @returns {{Promise<boolean>}} 验证结果
 */
export async function validate{rule_key.title()}Field(fieldName, value) {{
  const rules = {rule_key}ValidationRules[fieldName]
  if (!rules) return true
  
  for (const rule of rules) {{
    try {{
      if (rule.validator) {{
        await new Promise((resolve, reject) => {{
          rule.validator(null, value, (error) => {{
            if (error) reject(new Error(error.message || error))
            else resolve()
          }})
        }})
      }} else {{
        // 处理内置规则
        if (rule.required && (!value || String(value).trim() === '')) {{
          throw new Error(rule.message)
        }}
        if (rule.max && String(value).length > rule.max) {{
          throw new Error(rule.message)
        }}
        if (rule.min && String(value).length < rule.min) {{
          throw new Error(rule.message)
        }}
        if (rule.pattern && !rule.pattern.test(String(value))) {{
          throw new Error(rule.message)
        }}
      }}
    }} catch (error) {{
      console.warn(`字段 ${{fieldName}} 校验失败:`, error.message)
      return false
    }}
  }}
  
  return true
}}

/**
 * 验证整个表单数据
 * @param {{object}} formData - 表单数据
 * @returns {{Promise<{{valid: boolean, errors: Array}}>>}} 验证结果
 */
export async function validate{rule_key.title()}Form(formData) {{
  const errors = []
  
  for (const [fieldName, value] of Object.entries(formData)) {{
    const isValid = await validate{rule_key.title()}Field(fieldName, value)
    if (!isValid) {{
      errors.push({{
        field: fieldName,
        fieldName: get{rule_key.title()}FieldName(fieldName),
        message: `${{get{rule_key.title()}FieldName(fieldName)}}校验失败`
      }})
    }}
  }}
  
  return {{
    valid: errors.length === 0,
    errors
  }}
}}
"""  # noqa: W293
        return js_content

    def _generate_validators(self, fields: dict[str, Any]) -> str:
        """生成自定义校验器"""
        validators = []

        for field_name, field_config in fields.items():
            rules = field_config.get("rules", [])
            chinese_name = field_config.get("chinese_name", field_name)

            for rule in rules:
                rule_type = rule.get("type")
                message = rule.get("message")
                value = rule.get("value")

                if rule_type in ["min_value", "max_value"]:
                    validator_name = f"validate{field_name.title()}{rule_type.title().replace('_', '')}"

                    if rule_type == "min_value":
                        validator_code = f"""
/**
 * {chinese_name}最小值校验器
 */
const {validator_name} = (rule, value, callback) => {{
  if (value === null || value === undefined || value === '') {{
    callback()
    return
  }}
  
  const numValue = Number(value)
  if (isNaN(numValue)) {{
    callback(new Error('{chinese_name}必须是有效的数字'))
    return
  }}
  
  if (numValue < {value}) {{
    callback(new Error('{message}'))
    return
  }}
  
  callback()
}}"""  # noqa: W293
                    else:  # max_value
                        validator_code = f"""
/**
 * {chinese_name}最大值校验器
 */
const {validator_name} = (rule, value, callback) => {{
  if (value === null || value === undefined || value === '') {{
    callback()
    return
  }}
  
  const numValue = Number(value)
  if (isNaN(numValue)) {{
    callback(new Error('{chinese_name}必须是有效的数字'))
    return
  }}
  
  if (numValue > {value}) {{
    callback(new Error('{message}'))
    return
  }}
  
  callback()
}}"""  # noqa: W293

                    validators.append(validator_code)

        return "\n".join(validators)

    def _generate_validation_utils(self):
        """生成校验工具文件"""
        utils_content = """/**
 * 统一校验工具函数
 * 自动生成，请勿手动修改
 */

/**
 * 通用字段校验函数
 * @param {any} value - 字段值
 * @param {Array} rules - 校验规则数组
 * @returns {Promise<{valid: boolean, message: string}>} 校验结果
 */
export async function validateField(value, rules) {
  for (const rule of rules) {
    try {
      if (rule.required && (!value || String(value).trim() === '')) {
        return { valid: false, message: rule.message }
      }
      
      if (value && rule.max && String(value).length > rule.max) {
        return { valid: false, message: rule.message }
      }
      
      if (value && rule.min && String(value).length < rule.min) {
        return { valid: false, message: rule.message }
      }
      
      if (value && rule.pattern && !rule.pattern.test(String(value))) {
        return { valid: false, message: rule.message }
      }
      
      if (rule.validator) {
        await new Promise((resolve, reject) => {
          rule.validator(null, value, (error) => {
            if (error) reject(new Error(error.message || error))
            else resolve()
          })
        })
      }
    } catch (error) {
      return { valid: false, message: error.message }
    }
  }
  
  return { valid: true, message: '' }
}

/**
 * 批量校验表单数据
 * @param {object} formData - 表单数据
 * @param {object} validationRules - 校验规则配置
 * @returns {Promise<{valid: boolean, errors: Array}>} 校验结果
 */
export async function validateForm(formData, validationRules) {
  const errors = []
  
  for (const [fieldName, value] of Object.entries(formData)) {
    const rules = validationRules[fieldName]
    if (!rules) continue
    
    const result = await validateField(value, rules)
    if (!result.valid) {
      errors.push({
        field: fieldName,
        message: result.message
      })
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 错误信息格式化
 * @param {Array} errors - 错误数组
 * @returns {string} 格式化的错误信息
 */
export function formatValidationErrors(errors) {
  return errors.map(error => error.message).join('; ')
}
"""  # noqa: W293

        utils_file = self.output_dir / "validationUtils.js"
        with open(utils_file, "w", encoding="utf-8") as f:
            f.write(utils_content)

    def _generate_index_file(self, rule_keys: list[str]):
        """生成索引文件"""
        imports = []
        exports = []

        for rule_key in rule_keys:
            imports.append(
                f"import {{ {rule_key}ValidationRules, {rule_key}FieldNames, "
                f"validate{rule_key.title()}Form }} from './{rule_key}Validation.js'"
            )
            exports.extend(
                [f"  {rule_key}ValidationRules,", f"  {rule_key}FieldNames,", f"  validate{rule_key.title()}Form,"]
            )

        index_content = f"""/**
 * 校验规则统一导出
 * 自动生成于 {time.strftime("%Y-%m-%d %H:%M:%S")}
 * 请勿手动修改此文件
 */

{chr(10).join(imports)}
import {{ validateField, validateForm, formatValidationErrors }} from './validationUtils.js'

export {{
{chr(10).join(exports)}
  validateField,
  validateForm,
  formatValidationErrors
}}
"""

        index_file = self.output_dir / "index.js"
        with open(index_file, "w", encoding="utf-8") as f:
            f.write(index_content)

    def _get_all_rule_keys(self, session: Session) -> list[str]:
        """获取所有规则键"""
        from models.database import RuleTemplate

        templates = session.query(RuleTemplate).all()
        return [template.rule_key for template in templates]


def main():
    """命令行入口"""
    import argparse

    parser = argparse.ArgumentParser(description="生成前端校验规则")
    parser.add_argument("--rule-keys", nargs="*", help="指定规则键列表")
    parser.add_argument("--output-dir", default="frontend/src/utils/validation", help="输出目录")

    args = parser.parse_args()

    generator = FrontendValidationGenerator(args.output_dir)
    results = generator.generate_all_rules(args.rule_keys)

    print(f"生成完成: 成功 {results['success_count']}, 失败 {results['error_count']}")
    if results["errors"]:
        print("错误详情:")
        for error in results["errors"]:
            print(f"  - {error}")


if __name__ == "__main__":
    main()
