/**
 * 统一异步状态管理 Composable
 * 企业级异步操作管理，集成状态机、错误恢复、重试机制
 */

import { ref, computed, watch, onUnmounted } from 'vue'
import { useStateMachine } from './useStateMachine'
import { useAppStore } from '@/stores/app'
import { AsyncStates } from '@/types/state'

/**
 * 默认配置选项
 */
const DEFAULT_OPTIONS = {
  immediate: false,           // 是否立即执行
  resetOnExecute: true,      // 执行时是否重置状态
  retryCount: 3,             // 重试次数
  retryDelay: 1000,          // 重试延迟（毫秒）
  timeout: 30000,            // 超时时间（毫秒）
  throwOnError: false,       // 是否抛出错误
  onSuccess: null,           // 成功回调
  onError: null,             // 错误回调
  onFinally: null,           // 完成回调
  onRetry: null,             // 重试回调
  loadingTaskId: null,       // 全局加载任务ID
  enableGlobalLoading: true, // 是否启用全局加载状态
  enableErrorRecovery: true, // 是否启用错误恢复
  cacheKey: null,            // 缓存键
  cacheTTL: 5 * 60 * 1000   // 缓存TTL（5分钟）
}

/**
 * 简单缓存实现
 */
const cache = new Map()

/**
 * 清理过期缓存
 */
const cleanExpiredCache = () => {
  const now = Date.now()
  for (const [key, value] of cache.entries()) {
    if (value.expires && value.expires < now) {
      cache.delete(key)
    }
  }
}

// 定期清理缓存
setInterval(cleanExpiredCache, 60000) // 每分钟清理一次

/**
 * 统一异步状态管理
 * @param {Function} asyncFunction - 异步函数
 * @param {Object} options - 配置选项
 * @returns {Object} 异步状态管理实例
 */
export function useAsyncState(asyncFunction, options = {}) {
  const finalOptions = { ...DEFAULT_OPTIONS, ...options }
  const appStore = useAppStore()

  // 状态机实例
  const stateMachine = useStateMachine()

  // 数据状态
  const data = ref(null)
  const error = ref(null)
  const lastExecuteTime = ref(null)
  const executeCount = ref(0)
  const retryAttempts = ref(0)

  // 执行控制
  const abortController = ref(null)
  const timeoutId = ref(null)
  const retryTimeoutId = ref(null)

  // ==================== 计算属性 ====================

  /**
   * 是否正在加载
   */
  const isLoading = computed(() =>
    stateMachine.is.value.loading || stateMachine.is.value.retrying
  )

  /**
   * 是否成功
   */
  const isSuccess = computed(() => stateMachine.is.value.success)

  /**
   * 是否有错误
   */
  const isError = computed(() => stateMachine.is.value.error)

  /**
   * 是否正在重试
   */
  const isRetrying = computed(() => stateMachine.is.value.retrying)

  /**
   * 是否可以重试
   */
  const canRetry = computed(() =>
    isError.value && retryAttempts.value < finalOptions.retryCount
  )

  /**
   * 当前状态
   */
  const state = computed(() => stateMachine.state.value)

  // ==================== 核心执行方法 ====================

  /**
   * 执行异步操作
   * @param {...any} args - 传递给异步函数的参数
   * @returns {Promise} 执行结果
   */
  const execute = async (...args) => {
    // 检查缓存
    if (finalOptions.cacheKey) {
      const cached = getCachedData(finalOptions.cacheKey)
      if (cached) {
        data.value = cached
        await stateMachine.success({ fromCache: true })
        return cached
      }
    }

    // 重置状态（如果需要）
    if (finalOptions.resetOnExecute) {
      reset()
    }

    // 开始执行
    await stateMachine.start({ args })

    try {
      // 创建 AbortController
      abortController.value = new AbortController()

      // 设置超时
      if (finalOptions.timeout > 0) {
        timeoutId.value = setTimeout(() => {
          abortController.value?.abort()
        }, finalOptions.timeout)
      }

      // 添加全局加载任务
      let loadingTaskId = null
      if (finalOptions.enableGlobalLoading) {
        loadingTaskId = finalOptions.loadingTaskId || `async_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        appStore.addLoadingTask(loadingTaskId, '执行中...')
      }

      // 执行异步函数
      const result = await asyncFunction(...args, {
        signal: abortController.value.signal
      })

      // 清除超时
      if (timeoutId.value) {
        clearTimeout(timeoutId.value)
        timeoutId.value = null
      }

      // 移除全局加载任务
      if (loadingTaskId) {
        appStore.removeLoadingTask(loadingTaskId)
      }

      // 更新状态
      data.value = result
      lastExecuteTime.value = Date.now()
      executeCount.value++
      retryAttempts.value = 0

      // 缓存结果
      if (finalOptions.cacheKey) {
        setCachedData(finalOptions.cacheKey, result, finalOptions.cacheTTL)
      }

      // 状态转换为成功
      await stateMachine.success({ result, args })

      // 执行成功回调
      if (finalOptions.onSuccess) {
        try {
          await finalOptions.onSuccess(result)
        } catch (callbackError) {
          console.warn('Success callback error:', callbackError)
        }
      }

      return result

    } catch (executeError) {
      // 清除超时
      if (timeoutId.value) {
        clearTimeout(timeoutId.value)
        timeoutId.value = null
      }

      // 移除全局加载任务
      if (finalOptions.enableGlobalLoading && finalOptions.loadingTaskId) {
        appStore.removeLoadingTask(finalOptions.loadingTaskId)
      }

      // 处理取消操作
      if (executeError.name === 'AbortError') {
        console.debug('Async operation was cancelled')
        return null
      }

      // 更新错误状态
      error.value = executeError
      executeCount.value++

      // 添加到全局错误状态
      if (finalOptions.enableGlobalLoading) {
        appStore.setError(executeError, {
          function: asyncFunction.name,
          args,
          retryAttempts: retryAttempts.value
        })
      }

      // 状态转换为错误
      await stateMachine.error({ error: executeError, args })

      // 尝试重试
      if (finalOptions.enableErrorRecovery && canRetry.value) {
        return await retry(...args)
      }

      // 执行错误回调
      if (finalOptions.onError) {
        try {
          await finalOptions.onError(executeError)
        } catch (callbackError) {
          console.warn('Error callback error:', callbackError)
        }
      }

      // 是否抛出错误
      if (finalOptions.throwOnError) {
        throw executeError
      }

      return null
    } finally {
      // 清理资源
      abortController.value = null

      // 执行完成回调
      if (finalOptions.onFinally) {
        try {
          await finalOptions.onFinally()
        } catch (callbackError) {
          console.warn('Finally callback error:', callbackError)
        }
      }
    }
  }

  /**
   * 重试执行
   * @param {...any} args - 传递给异步函数的参数
   * @returns {Promise} 重试结果
   */
  const retry = async (...args) => {
    if (!canRetry.value) {
      console.warn('Cannot retry: retry limit reached or not in error state')
      return null
    }

    retryAttempts.value++

    // 状态转换为重试中
    await stateMachine.retry({
      attempt: retryAttempts.value,
      maxAttempts: finalOptions.retryCount,
      args
    })

    // 执行重试回调
    if (finalOptions.onRetry) {
      try {
        await finalOptions.onRetry(retryAttempts.value)
      } catch (callbackError) {
        console.warn('Retry callback error:', callbackError)
      }
    }

    // 计算重试延迟
    const delay = Array.isArray(finalOptions.retryDelay)
      ? finalOptions.retryDelay[retryAttempts.value - 1] || finalOptions.retryDelay[finalOptions.retryDelay.length - 1]
      : finalOptions.retryDelay

    // 延迟重试
    if (delay > 0) {
      await new Promise(resolve => {
        retryTimeoutId.value = setTimeout(resolve, delay)
      })
    }

    // 重新执行
    return await execute(...args)
  }

  /**
   * 取消执行
   */
  const cancel = () => {
    if (abortController.value) {
      abortController.value.abort()
    }

    if (timeoutId.value) {
      clearTimeout(timeoutId.value)
      timeoutId.value = null
    }

    if (retryTimeoutId.value) {
      clearTimeout(retryTimeoutId.value)
      retryTimeoutId.value = null
    }

    // 移除全局加载任务
    if (finalOptions.enableGlobalLoading && finalOptions.loadingTaskId) {
      appStore.removeLoadingTask(finalOptions.loadingTaskId)
    }
  }

  /**
   * 重置状态
   */
  const reset = () => {
    cancel()
    data.value = null
    error.value = null
    retryAttempts.value = 0
    stateMachine.reset()
  }

  // ==================== 缓存管理 ====================

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @returns {any} 缓存数据
   */
  const getCachedData = (key) => {
    const cached = cache.get(key)
    if (cached && (!cached.expires || cached.expires > Date.now())) {
      return cached.data
    }
    return null
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {any} data - 数据
   * @param {number} ttl - 生存时间
   */
  const setCachedData = (key, data, ttl) => {
    cache.set(key, {
      data,
      expires: ttl > 0 ? Date.now() + ttl : null,
      timestamp: Date.now()
    })
  }

  /**
   * 清除缓存
   * @param {string} key - 缓存键（可选）
   */
  const clearCache = (key) => {
    if (key) {
      cache.delete(key)
    } else {
      cache.clear()
    }
  }

  // ==================== 生命周期 ====================

  // 立即执行（如果需要）
  if (finalOptions.immediate) {
    execute()
  }

  // 清理资源
  onUnmounted(() => {
    cancel()
  })

  // ==================== 返回接口 ====================

  return {
    // 状态数据
    data,
    error,
    state,

    // 状态标志
    isLoading,
    isSuccess,
    isError,
    isRetrying,
    canRetry,

    // 执行信息
    lastExecuteTime,
    executeCount,
    retryAttempts,

    // 核心方法
    execute,
    retry,
    cancel,
    reset,

    // 缓存管理
    getCachedData,
    setCachedData,
    clearCache,

    // 状态机接口
    stateMachine
  }
}
