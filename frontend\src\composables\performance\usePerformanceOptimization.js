/**
 * 性能优化 Composable
 * 提供统一的性能优化功能，包括虚拟滚动、防抖节流、缓存等
 */

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import {
  debounce,
  throttle,
  rafThrottle,
  memoize,
  globalPerformanceMonitor,
  LRUCache
} from '@/utils/performanceUtils'

/**
 * 虚拟滚动优化
 * @param {Object} options - 配置选项
 * @returns {Object} 虚拟滚动相关的响应式数据和方法
 */
export function useVirtualScroll(options = {}) {
  const {
    itemHeight = 50,
    containerHeight = 400,
    buffer = 5,
    dynamicHeight = false
  } = options

  const containerRef = ref(null)
  const scrollTop = ref(0)
  const visibleStartIndex = ref(0)
  const visibleEndIndex = ref(0)
  const itemHeights = ref(new Map())

  // 计算可视区域
  const calculateVisibleRange = (items) => {
    if (!items || items.length === 0) return { start: 0, end: 0 }

    const viewportHeight = containerHeight
    const scrollPosition = scrollTop.value

    if (!dynamicHeight) {
      const start = Math.max(0, Math.floor(scrollPosition / itemHeight) - buffer)
      const visibleCount = Math.ceil(viewportHeight / itemHeight)
      const end = Math.min(items.length - 1, start + visibleCount + buffer * 2)
      return { start, end }
    }

    // 动态高度计算
    let accumulatedHeight = 0
    let start = 0
    let end = items.length - 1

    // 找到开始索引
    for (let i = 0; i < items.length; i++) {
      const height = itemHeights.value.get(i) || itemHeight
      if (accumulatedHeight + height > scrollPosition) {
        start = Math.max(0, i - buffer)
        break
      }
      accumulatedHeight += height
    }

    // 找到结束索引
    accumulatedHeight = 0
    for (let i = start; i < items.length; i++) {
      const height = itemHeights.value.get(i) || itemHeight
      accumulatedHeight += height
      if (accumulatedHeight > viewportHeight + buffer * itemHeight) {
        end = Math.min(items.length - 1, i + buffer)
        break
      }
    }

    return { start, end }
  }

  // 处理滚动事件
  const handleScroll = throttle((event) => {
    scrollTop.value = event.target.scrollTop
  }, 16)

  // 滚动到指定项
  const scrollToItem = (index, alignment = 'auto') => {
    if (!containerRef.value) return

    let targetScrollTop = 0

    if (!dynamicHeight) {
      targetScrollTop = index * itemHeight
    } else {
      for (let i = 0; i < index; i++) {
        targetScrollTop += itemHeights.value.get(i) || itemHeight
      }
    }

    // 根据对齐方式调整
    switch (alignment) {
      case 'center':
        targetScrollTop -= (containerHeight - itemHeight) / 2
        break
      case 'end':
        targetScrollTop -= containerHeight - itemHeight
        break
    }

    containerRef.value.scrollTop = Math.max(0, targetScrollTop)
  }

  return {
    containerRef,
    scrollTop,
    visibleStartIndex,
    visibleEndIndex,
    itemHeights,
    calculateVisibleRange,
    handleScroll,
    scrollToItem
  }
}

/**
 * 搜索优化
 * @param {Function} searchFn - 搜索函数
 * @param {Object} options - 配置选项
 * @returns {Object} 搜索相关的响应式数据和方法
 */
export function useOptimizedSearch(searchFn, options = {}) {
  const {
    debounceDelay = 300,
    cacheSize = 50,
    minQueryLength = 2
  } = options

  const searchQuery = ref('')
  const searchResults = ref([])
  const isSearching = ref(false)
  const searchError = ref(null)

  // 搜索结果缓存
  const searchCache = new LRUCache(cacheSize)

  // 防抖搜索函数
  const debouncedSearch = debounce(async (query) => {
    if (!query || query.length < minQueryLength) {
      searchResults.value = []
      return
    }

    // 检查缓存
    const cacheKey = `search_${query}`
    const cachedResult = searchCache.get(cacheKey)
    if (cachedResult) {
      searchResults.value = cachedResult
      return
    }

    isSearching.value = true
    searchError.value = null

    try {
      globalPerformanceMonitor.mark('search-start')
      const results = await searchFn(query)
      globalPerformanceMonitor.measure('search-duration', 'search-start')

      searchResults.value = results
      searchCache.set(cacheKey, results)
    } catch (error) {
      searchError.value = error.message
      console.error('Search error:', error)
    } finally {
      isSearching.value = false
    }
  }, debounceDelay)

  // 执行搜索
  const search = (query) => {
    searchQuery.value = query
    debouncedSearch(query)
  }

  // 清除搜索
  const clearSearch = () => {
    searchQuery.value = ''
    searchResults.value = []
    searchError.value = null
    debouncedSearch.cancel()
  }

  return {
    searchQuery,
    searchResults,
    isSearching,
    searchError,
    search,
    clearSearch
  }
}

/**
 * 大数据量渲染优化
 * @param {Array} data - 数据数组
 * @param {Object} options - 配置选项
 * @returns {Object} 渲染优化相关的响应式数据和方法
 */
export function useLargeDataRendering(data, options = {}) {
  const {
    pageSize = 100,
    renderThreshold = 1000,
    useVirtualization = true
  } = options

  const currentPage = ref(1)
  const isVirtualized = ref(false)
  const renderingMode = ref('normal')

  // 判断是否需要虚拟化
  const shouldVirtualize = computed(() => {
    return useVirtualization && data.length > renderThreshold
  })

  // 当前页数据
  const currentPageData = computed(() => {
    if (shouldVirtualize.value) {
      isVirtualized.value = true
      renderingMode.value = 'virtualized'
      return data // 虚拟滚动会处理数据切片
    }

    if (data.length > renderThreshold) {
      renderingMode.value = 'paginated'
      const start = (currentPage.value - 1) * pageSize
      const end = start + pageSize
      return data.slice(start, end)
    }

    renderingMode.value = 'normal'
    return data
  })

  // 总页数
  const totalPages = computed(() => {
    if (shouldVirtualize.value) return 1
    return Math.ceil(data.length / pageSize)
  })

  // 切换页面
  const changePage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }

  // 性能统计
  const getPerformanceStats = () => {
    return {
      totalItems: data.length,
      currentPageItems: currentPageData.value.length,
      renderingMode: renderingMode.value,
      isVirtualized: isVirtualized.value,
      currentPage: currentPage.value,
      totalPages: totalPages.value
    }
  }

  return {
    currentPage,
    currentPageData,
    totalPages,
    isVirtualized,
    renderingMode,
    shouldVirtualize,
    changePage,
    getPerformanceStats
  }
}

/**
 * 内存监控
 * @returns {Object} 内存监控相关的响应式数据和方法
 */
export function useMemoryMonitor() {
  const memoryUsage = ref({
    used: 0,
    total: 0,
    limit: 0
  })
  const memoryHistory = ref([])
  const isMonitoring = ref(false)

  let monitorInterval = null

  // 获取内存使用情况
  const getMemoryUsage = () => {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      }
    }
    return null
  }

  // 开始监控
  const startMonitoring = (interval = 5000) => {
    if (isMonitoring.value) return

    isMonitoring.value = true
    monitorInterval = setInterval(() => {
      const usage = getMemoryUsage()
      if (usage) {
        memoryUsage.value = usage
        memoryHistory.value.push({
          ...usage,
          timestamp: Date.now()
        })

        // 保持历史记录在合理范围内
        if (memoryHistory.value.length > 100) {
          memoryHistory.value = memoryHistory.value.slice(-50)
        }
      }
    }, interval)
  }

  // 停止监控
  const stopMonitoring = () => {
    if (monitorInterval) {
      clearInterval(monitorInterval)
      monitorInterval = null
    }
    isMonitoring.value = false
  }

  // 获取内存增长趋势
  const getMemoryTrend = () => {
    if (memoryHistory.value.length < 2) return 0

    const recent = memoryHistory.value.slice(-10)
    const first = recent[0]
    const last = recent[recent.length - 1]

    return last.used - first.used
  }

  onUnmounted(() => {
    stopMonitoring()
  })

  return {
    memoryUsage,
    memoryHistory,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    getMemoryUsage,
    getMemoryTrend
  }
}

/**
 * 性能优化综合方案
 * @param {Object} options - 配置选项
 * @returns {Object} 综合性能优化功能
 */
export function usePerformanceOptimization(options = {}) {
  const {
    enableMemoryMonitor = true,
    enablePerformanceTracking = true,
    autoOptimize = true
  } = options

  const performanceStats = ref({
    renderTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    optimizationLevel: 'normal'
  })

  // 内存监控
  const memoryMonitor = enableMemoryMonitor ? useMemoryMonitor() : null

  // 性能跟踪
  const trackPerformance = (name, fn) => {
    if (!enablePerformanceTracking) return fn()

    globalPerformanceMonitor.mark(`${name}-start`)
    const result = fn()

    if (result instanceof Promise) {
      return result.finally(() => {
        globalPerformanceMonitor.measure(`${name}-duration`, `${name}-start`)
      })
    } else {
      globalPerformanceMonitor.measure(`${name}-duration`, `${name}-start`)
      return result
    }
  }

  // 自动优化建议
  const getOptimizationSuggestions = () => {
    const suggestions = []

    if (memoryMonitor) {
      const memoryTrend = memoryMonitor.getMemoryTrend()
      if (memoryTrend > 10000000) { // 10MB增长
        suggestions.push({
          type: 'memory',
          message: '内存使用增长较快，建议启用虚拟滚动或分页',
          priority: 'high'
        })
      }
    }

    const measures = globalPerformanceMonitor.getAllMeasures()
    const slowOperations = measures.filter(m => m.duration > 1000)
    if (slowOperations.length > 0) {
      suggestions.push({
        type: 'performance',
        message: '检测到慢操作，建议使用缓存或优化算法',
        priority: 'medium'
      })
    }

    return suggestions
  }

  // 应用自动优化
  const applyAutoOptimizations = () => {
    if (!autoOptimize) return

    const suggestions = getOptimizationSuggestions()

    suggestions.forEach(suggestion => {
      switch (suggestion.type) {
        case 'memory':
          performanceStats.value.optimizationLevel = 'aggressive'
          break
        case 'performance':
          // 可以在这里应用具体的优化策略
          break
      }
    })
  }

  // 启动性能监控
  onMounted(() => {
    if (memoryMonitor) {
      memoryMonitor.startMonitoring()
    }
  })

  return {
    performanceStats,
    memoryMonitor,
    trackPerformance,
    getOptimizationSuggestions,
    applyAutoOptimizations
  }
}
