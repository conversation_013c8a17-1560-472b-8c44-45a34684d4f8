/**
 * 规则明细Store增强API集成测试
 * 验证Store层是否正确集成了增强API
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useRuleDetailsStore } from '../ruleDetails.js'

// Mock 增强API
vi.mock('@/api/enhancedRuleDetails', () => ({
  enhancedRuleDetailsApi: {
    initialize: vi.fn().mockResolvedValue(true),
    getRuleDetailsList: vi.fn(),
    getRuleDetailById: vi.fn(),
    createRuleDetail: vi.fn(),
    updateRuleDetail: vi.fn(),
    deleteRuleDetail: vi.fn(),
    batchCreateRuleDetails: vi.fn(),
    batchUpdateRuleDetails: vi.fn(),
    batchDeleteRuleDetails: vi.fn(),
    searchRuleDetails: vi.fn(),
    clearCache: vi.fn(),
    clearAllCache: vi.fn(),
    getCacheStats: vi.fn()
  }
}))

// Mock 增强错误处理
vi.mock('@/utils/enhancedErrorHandler', () => ({
  enhancedErrorHandler: {
    handle: vi.fn()
  }
}))

describe('RuleDetails Store - 增强API集成', () => {
  let store

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useRuleDetailsStore()
    vi.clearAllMocks()
  })

  describe('Store初始化', () => {
    it('应该正确初始化增强API', () => {
      expect(store).toBeDefined()
      // 验证增强API初始化被调用
      // 注意：由于是异步初始化，这里只验证store创建成功
    })
  })

  describe('API调用方法', () => {
    it('应该导出所有必需的CRUD方法', () => {
      expect(typeof store.fetchDetailsList).toBe('function')
      expect(typeof store.fetchDetailById).toBe('function')
      expect(typeof store.createDetail).toBe('function')
      expect(typeof store.updateDetail).toBe('function')
      expect(typeof store.deleteDetail).toBe('function')
    })

    it('应该导出批量操作方法', () => {
      expect(typeof store.batchCreateDetails).toBe('function')
      expect(typeof store.batchUpdateDetails).toBe('function')
      expect(typeof store.batchDeleteDetails).toBe('function')
    })

    it('应该导出搜索方法', () => {
      expect(typeof store.searchDetails).toBe('function')
    })
  })

  describe('缓存管理', () => {
    it('应该导出增强API缓存管理方法', () => {
      expect(typeof store.clearEnhancedCache).toBe('function')
      expect(typeof store.getEnhancedCacheStats).toBe('function')
    })

    it('不应该导出旧的手动缓存方法', () => {
      expect(store.clearDetailCache).toBeUndefined()
      expect(store.clearRuleCache).toBeUndefined()
      expect(store.clearAllCache).toBeUndefined()
      expect(store.getCacheStats).toBeUndefined()
    })
  })

  describe('状态管理', () => {
    it('应该有正确的初始状态', () => {
      expect(store.detailsList).toEqual([])
      expect(store.currentDetail).toBeNull()
      expect(store.selectedDetails).toEqual([])
      expect(store.loading).toBe(false)
    })

    it('应该有正确的计算属性', () => {
      expect(typeof store.detailsCount).toBe('number')
      expect(typeof store.hasDetails).toBe('boolean')
      expect(typeof store.selectedCount).toBe('number')
      expect(typeof store.hasSelected).toBe('boolean')
    })
  })

  describe('向后兼容性', () => {
    it('应该保持与现有Composables兼容的API', () => {
      // 验证关键方法签名保持不变
      expect(store.fetchDetailsList).toBeDefined()
      expect(store.updateFilters).toBeDefined()
      expect(store.updatePagination).toBeDefined()
      expect(store.resetStore).toBeDefined()
    })
  })
})
