/**
 * 视图切换功能的Composable
 * 提供多种视图模式的切换和状态管理
 */
import { ref, computed } from 'vue'

/**
 * 基础视图切换功能
 * @param {string} defaultView - 默认视图模式
 * @param {Array} availableViews - 可用的视图模式列表
 */
export function useViewToggle(defaultView = 'card', availableViews = ['card', 'table']) {
  // 当前视图模式
  const currentView = ref(defaultView)

  // 视图配置
  const viewConfig = {
    card: {
      name: '卡片视图',
      icon: 'Grid',
      description: '以卡片形式展示数据，适合浏览概览信息'
    },
    table: {
      name: '表格视图',
      icon: 'Tickets',
      description: '以表格形式展示数据，适合查看详细信息'
    },
    list: {
      name: '列表视图',
      icon: 'List',
      description: '以列表形式展示数据，紧凑显示'
    }
  }

  // 计算属性
  const isCardView = computed(() => currentView.value === 'card')
  const isTableView = computed(() => currentView.value === 'table')
  const isListView = computed(() => currentView.value === 'list')

  const currentViewConfig = computed(() => {
    return viewConfig[currentView.value] || viewConfig.card
  })

  const availableViewConfigs = computed(() => {
    return availableViews.map(view => ({
      key: view,
      ...viewConfig[view]
    }))
  })

  // 切换视图
  const switchView = (view) => {
    if (availableViews.includes(view)) {
      currentView.value = view
    }
  }

  // 切换到下一个视图
  const switchToNext = () => {
    const currentIndex = availableViews.indexOf(currentView.value)
    const nextIndex = (currentIndex + 1) % availableViews.length
    currentView.value = availableViews[nextIndex]
  }

  // 切换到上一个视图
  const switchToPrevious = () => {
    const currentIndex = availableViews.indexOf(currentView.value)
    const prevIndex = currentIndex === 0 ? availableViews.length - 1 : currentIndex - 1
    currentView.value = availableViews[prevIndex]
  }

  return {
    // 响应式数据
    currentView,
    isCardView,
    isTableView,
    isListView,
    currentViewConfig,
    availableViewConfigs,

    // 方法
    switchView,
    switchToNext,
    switchToPrevious
  }
}

/**
 * 响应式视图切换
 * 根据屏幕尺寸自动调整视图模式
 */
export function useResponsiveView(breakpoints = {}) {
  const defaultBreakpoints = {
    mobile: 768,
    tablet: 1024,
    desktop: 1200
  }

  const bp = { ...defaultBreakpoints, ...breakpoints }

  const screenWidth = ref(window.innerWidth)
  const currentView = ref('card')

  // 设备类型
  const deviceType = computed(() => {
    if (screenWidth.value < bp.mobile) return 'mobile'
    if (screenWidth.value < bp.tablet) return 'tablet'
    if (screenWidth.value < bp.desktop) return 'desktop'
    return 'large'
  })

  // 推荐视图
  const recommendedView = computed(() => {
    switch (deviceType.value) {
      case 'mobile':
        return 'list'
      case 'tablet':
        return 'card'
      default:
        return 'table'
    }
  })

  // 监听窗口大小变化
  const handleResize = () => {
    screenWidth.value = window.innerWidth
  }

  // 自动切换到推荐视图
  const autoSwitchView = () => {
    currentView.value = recommendedView.value
  }

  // 手动切换视图
  const manualSwitchView = (view) => {
    currentView.value = view
  }

  // 初始化
  const init = () => {
    window.addEventListener('resize', handleResize)
    autoSwitchView()
  }

  // 清理
  const cleanup = () => {
    window.removeEventListener('resize', handleResize)
  }

  return {
    // 响应式数据
    screenWidth,
    currentView,
    deviceType,
    recommendedView,

    // 方法
    autoSwitchView,
    manualSwitchView,
    init,
    cleanup
  }
}

/**
 * 视图状态持久化
 * 将视图偏好保存到localStorage
 */
export function useViewPersistence(storageKey = 'app_view_preference') {
  // 从localStorage读取视图偏好
  const loadViewPreference = () => {
    try {
      const saved = localStorage.getItem(storageKey)
      return saved ? JSON.parse(saved) : null
    } catch (error) {
      console.warn('Failed to load view preference:', error)
      return null
    }
  }

  // 保存视图偏好到localStorage
  const saveViewPreference = (viewData) => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(viewData))
    } catch (error) {
      console.warn('Failed to save view preference:', error)
    }
  }

  // 清除视图偏好
  const clearViewPreference = () => {
    try {
      localStorage.removeItem(storageKey)
    } catch (error) {
      console.warn('Failed to clear view preference:', error)
    }
  }

  return {
    loadViewPreference,
    saveViewPreference,
    clearViewPreference
  }
}
