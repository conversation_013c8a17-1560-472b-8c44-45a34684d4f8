"""
任务4.1：主进程过滤集成测试
验证规则预过滤功能在统一校验流程中的正确集成

测试内容：
1. 预过滤功能启用时的正常工作流程
2. 预过滤功能禁用时的兼容性
3. 预过滤异常时的降级机制
4. 主从节点的一致性行为
5. 性能监控和日志记录

注意：此测试文件遵循项目测试管理规范，放置在tests/integration/目录下
"""

import asyncio
import unittest
from unittest.mock import AsyncMock, Mock, patch

from fastapi import Request

from api.routers.common.validation_logic import unified_validate_patient_data
from core.rule_index_manager import FilterResult
from models.api import ApiResponse
from models.patient import PatientData, FeeItem, PatientBasicInfo
from models.rule import RuleRequest, RuleResult


class TestMainProcessFilterIntegration(unittest.TestCase):
    """主进程过滤集成测试"""

    def setUp(self):
        """设置测试环境"""
        # 创建模拟的HTTP请求
        self.mock_request = Mock(spec=Request)
        self.mock_request.state = Mock()
        self.mock_request.state.request_id = "test-request-123"

        # 创建测试患者数据
        self.test_patient_data = PatientData(
            bah="TEST001",
            basicInfo=PatientBasicInfo(name="测试患者", gender="M", age=35),
            fees=[
                FeeItem(ybdm="Y001", name="测试药品1", amount=100.0),
                FeeItem(ybdm="Y002", name="测试药品2", amount=200.0),
            ],
        )

        # 创建测试规则请求
        self.test_rule_request = RuleRequest(
            patientInfo=self.test_patient_data, ids=["rule1", "rule2", "rule3", "rule4", "rule5"]
        )

        # 创建模拟队列
        self.mock_queue = AsyncMock(spec=asyncio.Queue)

    @patch("api.routers.common.validation_logic.settings")
    @patch("api.routers.common.validation_logic.request_tracker")
    @patch("services.ultra_fast_rule_service.ultra_fast_rule_service")
    async def test_prefilter_enabled_success(self, mock_ultra_service, mock_tracker, mock_settings):
        """测试预过滤启用时的成功流程"""
        # 配置设置
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_settings.ENABLE_ULTRA_FAST_VALIDATION = True
        mock_settings.REQUEST_TIMEOUT = 30

        # 模拟预过滤结果
        mock_filter_result = FilterResult(
            original_rule_count=5,
            filtered_rule_count=2,
            filter_rate=0.6,
            filter_time=3.5,
            filtered_rule_ids=["rule1", "rule3"],
            fallback_reason=None,
        )

        # 模拟超快速校验结果
        mock_ultra_stats = Mock()
        mock_ultra_stats.total_time_ms = 50.0
        mock_ultra_stats.filtered_rules_count = 2
        mock_ultra_stats.total_rules_candidate = 5
        mock_ultra_stats.filter_reduction_percentage = 60.0
        mock_ultra_stats.preprocessing_time_ms = 10.0
        mock_ultra_stats.filtering_time_ms = 15.0
        mock_ultra_stats.validation_time_ms = 25.0
        mock_ultra_stats.memory_usage_mb = 12.5

        mock_violations = [
            RuleResult(id="rule1", output="违规信息1"),
        ]

        with patch("core.rule_prefilter.rule_prefilter") as mock_prefilter:
            mock_prefilter.filter_rules_for_patient.return_value = mock_filter_result
            mock_ultra_service.validate_rules_ultra_fast.return_value = (mock_violations, mock_ultra_stats)

            # 执行测试
            result = await unified_validate_patient_data(
                http_request=self.mock_request,
                request=self.test_rule_request,
                request_queue=self.mock_queue,
                node_type="master",
            )

            # 验证结果
            self.assertIsInstance(result, ApiResponse)
            self.assertTrue(result.success)
            self.assertEqual(len(result.data), 1)

            # 验证预过滤被调用
            mock_prefilter.filter_rules_for_patient.assert_called_once_with(
                self.test_patient_data, ["rule1", "rule2", "rule3", "rule4", "rule5"]
            )

            # 验证超快速校验使用过滤后的规则
            mock_ultra_service.validate_rules_ultra_fast.assert_called_once_with(self.test_patient_data, ["rule1", "rule3"])

            # 验证请求跟踪事件
            mock_tracker.add_event.assert_any_call(
                "test-request-123",
                "rule_prefilter_applied",
                {"original_rules": 5, "filtered_rules": 2, "filter_rate": 0.6, "filter_time_ms": 3.5, "node_type": "master"},
            )

    @patch("api.routers.common.validation_logic.settings")
    @patch("api.routers.common.validation_logic.request_tracker")
    @patch("services.ultra_fast_rule_service.ultra_fast_rule_service")
    async def test_prefilter_disabled_compatibility(self, mock_ultra_service, mock_tracker, mock_settings):
        """测试预过滤禁用时的兼容性"""
        # 配置设置
        mock_settings.ENABLE_RULE_PREFILTER = False
        mock_settings.ENABLE_ULTRA_FAST_VALIDATION = True
        mock_settings.REQUEST_TIMEOUT = 30

        # 模拟超快速校验结果
        mock_ultra_stats = Mock()
        mock_ultra_stats.total_time_ms = 45.0
        mock_ultra_stats.filtered_rules_count = 5
        mock_ultra_stats.total_rules_candidate = 5
        mock_ultra_stats.filter_reduction_percentage = 0.0
        mock_ultra_stats.preprocessing_time_ms = 10.0
        mock_ultra_stats.filtering_time_ms = 15.0
        mock_ultra_stats.validation_time_ms = 20.0
        mock_ultra_stats.memory_usage_mb = 15.0

        mock_violations = [
            RuleResult(id="rule1", output="违规信息1"),
            RuleResult(id="rule2", output="违规信息2"),
        ]

        mock_ultra_service.validate_rules_ultra_fast.return_value = (mock_violations, mock_ultra_stats)

        # 执行测试
        result = await unified_validate_patient_data(
            http_request=self.mock_request, request=self.test_rule_request, request_queue=self.mock_queue, node_type="slave"
        )

        # 验证结果
        self.assertIsInstance(result, ApiResponse)
        self.assertTrue(result.success)
        self.assertEqual(len(result.data), 2)

        # 验证超快速校验使用原始规则列表
        mock_ultra_service.validate_rules_ultra_fast.assert_called_once_with(
            self.test_patient_data, ["rule1", "rule2", "rule3", "rule4", "rule5"]
        )

        # 验证没有预过滤相关的跟踪事件
        call_args_list = [call[0] for call in mock_tracker.add_event.call_args_list]
        prefilter_events = [args for args in call_args_list if len(args) > 1 and "prefilter" in args[1]]
        self.assertEqual(len(prefilter_events), 0)

    @patch("api.routers.common.validation_logic.settings")
    @patch("api.routers.common.validation_logic.request_tracker")
    @patch("services.ultra_fast_rule_service.ultra_fast_rule_service")
    async def test_prefilter_exception_fallback(self, mock_ultra_service, mock_tracker, mock_settings):
        """测试预过滤异常时的降级机制"""
        # 配置设置
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_settings.ENABLE_ULTRA_FAST_VALIDATION = True
        mock_settings.REQUEST_TIMEOUT = 30

        # 模拟超快速校验结果
        mock_ultra_stats = Mock()
        mock_ultra_stats.total_time_ms = 50.0
        mock_ultra_stats.filtered_rules_count = 5
        mock_ultra_stats.total_rules_candidate = 5
        mock_ultra_stats.filter_reduction_percentage = 0.0
        mock_ultra_stats.preprocessing_time_ms = 10.0
        mock_ultra_stats.filtering_time_ms = 15.0
        mock_ultra_stats.validation_time_ms = 25.0
        mock_ultra_stats.memory_usage_mb = 12.5

        mock_violations = [
            RuleResult(id="rule1", output="违规信息1"),
        ]

        with patch("core.rule_prefilter.rule_prefilter") as mock_prefilter:
            # 模拟预过滤异常
            mock_prefilter.filter_rules_for_patient.side_effect = Exception("索引未就绪")
            mock_ultra_service.validate_rules_ultra_fast.return_value = (mock_violations, mock_ultra_stats)

            # 执行测试
            result = await unified_validate_patient_data(
                http_request=self.mock_request,
                request=self.test_rule_request,
                request_queue=self.mock_queue,
                node_type="master",
            )

            # 验证结果
            self.assertIsInstance(result, ApiResponse)
            self.assertTrue(result.success)

            # 验证超快速校验使用原始规则列表（降级）
            mock_ultra_service.validate_rules_ultra_fast.assert_called_once_with(
                self.test_patient_data, ["rule1", "rule2", "rule3", "rule4", "rule5"]
            )

            # 验证降级事件被记录
            mock_tracker.add_event.assert_any_call(
                "test-request-123", "rule_prefilter_fallback", {"fallback_reason": "索引未就绪", "node_type": "master"}
            )

    async def test_master_slave_consistency(self):
        """测试主从节点的一致性行为"""
        # 这个测试验证主从节点使用相同的函数，行为应该一致
        # 通过调用相同的函数但使用不同的node_type参数来验证

        with patch("api.routers.common.validation_logic.settings") as mock_settings:
            mock_settings.ENABLE_RULE_PREFILTER = True
            mock_settings.ENABLE_ULTRA_FAST_VALIDATION = True
            mock_settings.REQUEST_TIMEOUT = 30

            with patch("core.rule_prefilter.rule_prefilter") as mock_prefilter:
                with patch("services.ultra_fast_rule_service.ultra_fast_rule_service") as mock_ultra_service:
                    # 设置模拟返回值
                    mock_filter_result = FilterResult(
                        original_rule_count=5,
                        filtered_rule_count=3,
                        filter_rate=0.4,
                        filter_time=2.8,
                        filtered_rule_ids=["rule1", "rule2", "rule4"],
                        fallback_reason=None,
                    )
                    mock_prefilter.filter_rules_for_patient.return_value = mock_filter_result

                    mock_ultra_stats = Mock()
                    mock_ultra_stats.total_time_ms = 40.0
                    mock_violations = []
                    mock_ultra_service.validate_rules_ultra_fast.return_value = (mock_violations, mock_ultra_stats)

                    # 测试主节点
                    result_master = await unified_validate_patient_data(
                        http_request=self.mock_request,
                        request=self.test_rule_request,
                        request_queue=self.mock_queue,
                        node_type="master",
                    )

                    # 重置模拟对象
                    mock_prefilter.reset_mock()
                    mock_ultra_service.reset_mock()

                    # 测试从节点
                    result_slave = await unified_validate_patient_data(
                        http_request=self.mock_request,
                        request=self.test_rule_request,
                        request_queue=self.mock_queue,
                        node_type="slave",
                    )

                    # 验证主从节点行为一致
                    self.assertEqual(result_master.success, result_slave.success)
                    self.assertEqual(len(result_master.data), len(result_slave.data))

                    # 验证两次都调用了预过滤
                    self.assertEqual(mock_prefilter.filter_rules_for_patient.call_count, 2)
                    self.assertEqual(mock_ultra_service.validate_rules_ultra_fast.call_count, 2)


if __name__ == "__main__":
    unittest.main()
