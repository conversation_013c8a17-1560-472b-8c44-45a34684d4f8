# 项目文档导航

欢迎使用规则验证系统文档！本文档提供完整的导航指南，帮助您快速找到所需信息。

## 📚 文档结构概览

```
docs/                                    # 统一文档根目录
├── 文档导航.md                           # 本文档 - 主导航和使用指南
├── 变更记录.md                           # 文档变更记录
│
├── user-guide/                         # 用户指南
├── development/                        # 开发文档
├── operations/                         # 运维文档
├── project/                           # 项目管理文档
└── archive/                           # 归档文档
```

## 🎯 快速导航

### 👥 按角色查找文档

#### 🔧 系统管理员/运维人员
- [安装部署指南](user-guide/安装部署指南.md) - 系统安装和环境配置
- [Docker部署指南](operations/deployment/Docker部署指南.md) - 容器化部署
- [生产环境部署](operations/deployment/生产环境部署.md) - 生产环境配置
- [系统监控](operations/monitoring/系统监控.md) - 监控配置和告警
- [数据库维护](operations/maintenance/数据库维护.md) - 数据库运维

#### 👨‍💻 开发人员
- [系统架构概览](development/architecture/系统架构概览.md) - 整体架构设计
- [规则管理API](development/api/规则管理API.md) - 核心API接口
- [组件使用指南](development/frontend/组件使用指南.md) - 前端组件开发
- [数据校验前端实现使用指南](development/frontend/数据校验前端实现使用指南.md) - 前端校验系统使用
- [测试策略](development/testing/测试策略.md) - 测试规范和指南
- [数据库设计](development/architecture/数据库设计.md) - 数据模型设计

#### 📋 项目经理/产品经理
- [产品需求文档](project/requirements/产品需求文档.md) - 功能需求说明
- [任务分解与进度](project/tasks/任务分解与进度.md) - 项目进度管理
- [项目完成总结](project/reports/项目完成总结.md) - 项目成果总结
- [系统设计文档](project/design/系统设计文档.md) - 设计方案

#### 👤 最终用户
- [用户操作手册](user-guide/用户操作手册.md) - 系统使用指南
- [配置说明](user-guide/配置说明.md) - 参数配置说明
- [常见问题解决](user-guide/常见问题解决.md) - 故障排除指南

### 🔍 按功能查找文档

#### 🏗️ 系统架构
- [系统架构概览](development/architecture/系统架构概览.md)
- [主从架构设计](development/architecture/主从架构设计.md)
- [性能优化架构](development/architecture/性能优化架构.md)

#### 🔌 API接口
- [规则管理API](development/api/规则管理API.md)
- [规则详情API](development/api/规则详情API.md)
- [同步API](development/api/同步API.md)
- [降级API](development/api/降级API.md)

#### 🚀 部署运维
- [Docker部署指南](operations/deployment/Docker部署指南.md)
- [生产环境部署](operations/deployment/生产环境部署.md)
- [离线部署指南](operations/deployment/离线部署指南.md)
- [系统监控](operations/monitoring/系统监控.md)

#### 🧪 测试相关
- [测试策略](development/testing/测试策略.md)
- [单元测试](development/testing/单元测试.md)
- [集成测试](development/testing/集成测试.md)

## 📖 文档使用指南

### 🔍 如何查找文档
1. **按角色查找**：根据您的角色（开发/运维/管理/用户）选择对应分类
2. **按功能查找**：根据要解决的问题选择对应功能模块
3. **使用搜索**：在IDE中使用全局搜索功能查找关键词

### 📝 文档命名规范
- **目录路径**：英文小写字母和连字符（如：`user-guide/`）
- **文件名**：中文，简洁明了（如：`安装部署指南.md`）
- **内容语言**：中文为主，代码示例保持原有语言

### 🔄 文档更新流程
1. 修改文档内容
2. 更新 [变更记录.md](变更记录.md)
3. 如有结构变更，更新本导航文档
4. 验证相关链接有效性

## 📞 获取帮助

### 🆘 遇到问题时
1. 首先查看 [常见问题解决](user-guide/常见问题解决.md)
2. 查看相关模块的技术文档
3. 检查 [变更记录.md](变更记录.md) 了解最新变更

### 📋 文档反馈
如发现文档问题或有改进建议：
1. 记录问题详情和建议
2. 联系文档维护团队
3. 或直接修改文档并更新变更记录

---

**最后更新**: 2025-07-23  
**维护团队**: 项目开发团队  
**文档版本**: v2.0（重构版本）
