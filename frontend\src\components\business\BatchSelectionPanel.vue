<template>
  <div class="batch-selection-panel">
    <el-card shadow="never" class="selection-card">
      <template #header>
        <div class="selection-header">
          <div class="header-left">
            <el-icon class="header-icon"><Operation /></el-icon>
            <span class="header-title">智能选择</span>
          </div>
          <div class="header-right">
            <el-button
              size="small"
              text
              @click="togglePanel"
              :icon="isExpanded ? ArrowUp : ArrowDown"
            >
              {{ isExpanded ? '收起' : '展开' }}
            </el-button>
          </div>
        </div>
      </template>

      <div v-show="isExpanded" class="selection-content">
        <!-- 快速选择区域 -->
        <div class="quick-selection">
          <div class="section-title">
            <el-icon><Lightning /></el-icon>
            <span>快速选择</span>
          </div>
          <div class="quick-buttons">
            <el-button-group size="small">
              <el-button @click="selectAll" :icon="Check">
                全选 ({{ totalCount }})
              </el-button>
              <el-button @click="selectNone" :icon="Close">
                清空
              </el-button>
              <el-button @click="selectInverse" :icon="RefreshRight">
                反选
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 条件选择区域 -->
        <div class="condition-selection">
          <div class="section-title">
            <el-icon><Filter /></el-icon>
            <span>条件选择</span>
          </div>

          <div class="condition-form">
            <el-row :gutter="16">
              <!-- 状态选择 -->
              <el-col :span="8">
                <div class="form-item">
                  <label class="form-label">状态</label>
                  <el-select
                    v-model="selectionConditions.status"
                    placeholder="选择状态"
                    multiple
                    collapse-tags
                    size="small"
                    style="width: 100%"
                  >
                    <el-option label="激活" value="ACTIVE" />
                    <el-option label="停用" value="INACTIVE" />
                    <el-option label="已删除" value="DELETED" />
                  </el-select>
                </div>
              </el-col>

              <!-- 错误级别选择 -->
              <el-col :span="8">
                <div class="form-item">
                  <label class="form-label">错误级别</label>
                  <el-select
                    v-model="selectionConditions.errorLevel"
                    placeholder="选择错误级别"
                    multiple
                    collapse-tags
                    size="small"
                    style="width: 100%"
                  >
                    <el-option label="一级错误" value="level1" />
                    <el-option label="二级错误" value="level2" />
                    <el-option label="三级错误" value="level3" />
                  </el-select>
                </div>
              </el-col>

              <!-- 默认选中 -->
              <el-col :span="8">
                <div class="form-item">
                  <label class="form-label">默认选中</label>
                  <el-select
                    v-model="selectionConditions.defaultSelected"
                    placeholder="选择类型"
                    size="small"
                    style="width: 100%"
                  >
                    <el-option label="全部" value="" />
                    <el-option label="默认选中" value="true" />
                    <el-option label="非默认" value="false" />
                  </el-select>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="16" style="margin-top: 12px;">
              <!-- 创建时间范围 -->
              <el-col :span="12">
                <div class="form-item">
                  <label class="form-label">创建时间</label>
                  <el-date-picker
                    v-model="selectionConditions.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="small"
                    style="width: 100%"
                  />
                </div>
              </el-col>

              <!-- 关键词搜索 -->
              <el-col :span="12">
                <div class="form-item">
                  <label class="form-label">关键词</label>
                  <el-input
                    v-model="selectionConditions.keyword"
                    placeholder="搜索规则名称或ID"
                    size="small"
                    clearable
                    :prefix-icon="Search"
                  />
                </div>
              </el-col>
            </el-row>

            <!-- 操作按钮 -->
            <div class="condition-actions">
              <el-button
                type="primary"
                size="small"
                @click="selectByCondition"
                :loading="isSelecting"
                :icon="Check"
              >
                按条件选择
              </el-button>
              <el-button
                size="small"
                @click="resetConditions"
                :icon="RefreshLeft"
              >
                重置条件
              </el-button>
              <el-button
                size="small"
                @click="addToSelection"
                :disabled="!hasConditions"
                :icon="Plus"
              >
                添加到选择
              </el-button>
            </div>
          </div>
        </div>

        <!-- 选择统计区域 -->
        <div class="selection-stats">
          <div class="section-title">
            <el-icon><DataAnalysis /></el-icon>
            <span>选择统计</span>
          </div>

          <div class="stats-content">
            <div class="stats-item">
              <span class="stats-label">已选择:</span>
              <span class="stats-value primary">{{ selectedCount }}</span>
              <span class="stats-unit">项</span>
            </div>
            <div class="stats-item">
              <span class="stats-label">总计:</span>
              <span class="stats-value">{{ totalCount }}</span>
              <span class="stats-unit">项</span>
            </div>
            <div class="stats-item">
              <span class="stats-label">选择率:</span>
              <span class="stats-value" :class="selectionRateClass">
                {{ selectionRate }}%
              </span>
            </div>
          </div>

          <!-- 选择限制提示 -->
          <div v-if="hasSelectionLimit" class="selection-limit">
            <el-alert
              :title="`最多可选择 ${maxSelection} 项，当前已选择 ${selectedCount} 项`"
              type="info"
              :closable="false"
              show-icon
              size="small"
            />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  Operation,
  ArrowUp,
  ArrowDown,
  Lightning,
  Check,
  Close,
  RefreshRight,
  Filter,
  Search,
  RefreshLeft,
  Plus,
  DataAnalysis
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  // 数据列表
  dataList: {
    type: Array,
    default: () => []
  },
  // 已选择的项目
  selectedItems: {
    type: Array,
    default: () => []
  },
  // 最大选择数量限制
  maxSelection: {
    type: Number,
    default: 0 // 0 表示无限制
  },
  // 是否默认展开
  defaultExpanded: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'selection-change',
  'select-all',
  'select-none',
  'select-inverse',
  'select-by-condition'
])

// 响应式数据
const isExpanded = ref(props.defaultExpanded)
const isSelecting = ref(false)

// 选择条件
const selectionConditions = ref({
  status: [],
  errorLevel: [],
  defaultSelected: '',
  dateRange: null,
  keyword: ''
})

// 计算属性
const totalCount = computed(() => props.dataList.length)
const selectedCount = computed(() => props.selectedItems.length)

const selectionRate = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.round((selectedCount.value / totalCount.value) * 100)
})

const selectionRateClass = computed(() => {
  const rate = selectionRate.value
  if (rate === 0) return 'zero'
  if (rate < 30) return 'low'
  if (rate < 70) return 'medium'
  return 'high'
})

const hasSelectionLimit = computed(() => props.maxSelection > 0)

const hasConditions = computed(() => {
  return selectionConditions.value.status.length > 0 ||
         selectionConditions.value.errorLevel.length > 0 ||
         selectionConditions.value.defaultSelected !== '' ||
         selectionConditions.value.dateRange ||
         selectionConditions.value.keyword.trim() !== ''
})

// 方法定义
const togglePanel = () => {
  isExpanded.value = !isExpanded.value
}

const selectAll = () => {
  if (hasSelectionLimit.value && totalCount.value > props.maxSelection) {
    ElMessage.warning(`最多只能选择 ${props.maxSelection} 项`)
    return
  }
  emit('select-all')
}

const selectNone = () => {
  emit('select-none')
}

const selectInverse = () => {
  emit('select-inverse')
}

const selectByCondition = async () => {
  if (!hasConditions.value) {
    ElMessage.warning('请设置选择条件')
    return
  }

  isSelecting.value = true
  try {
    // 根据条件筛选数据
    const filteredItems = filterItemsByCondition()

    if (hasSelectionLimit.value && filteredItems.length > props.maxSelection) {
      ElMessage.warning(`按条件筛选出 ${filteredItems.length} 项，超过最大选择限制 ${props.maxSelection}`)
      isSelecting.value = false
      return
    }

    emit('select-by-condition', filteredItems)
    ElMessage.success(`已按条件选择 ${filteredItems.length} 项`)
  } catch (error) {
    ElMessage.error('按条件选择失败')
    console.error('条件选择错误:', error)
  } finally {
    isSelecting.value = false
  }
}

const addToSelection = () => {
  if (!hasConditions.value) {
    ElMessage.warning('请设置选择条件')
    return
  }

  const filteredItems = filterItemsByCondition()
  const newSelectionCount = selectedCount.value + filteredItems.length

  if (hasSelectionLimit.value && newSelectionCount > props.maxSelection) {
    ElMessage.warning(`添加后将超过最大选择限制 ${props.maxSelection}`)
    return
  }

  emit('select-by-condition', filteredItems, true) // true 表示添加到现有选择
  ElMessage.success(`已添加 ${filteredItems.length} 项到选择`)
}

const resetConditions = () => {
  selectionConditions.value = {
    status: [],
    errorLevel: [],
    defaultSelected: '',
    dateRange: null,
    keyword: ''
  }
}

const filterItemsByCondition = () => {
  let filtered = [...props.dataList]
  const conditions = selectionConditions.value

  // 按状态筛选
  if (conditions.status.length > 0) {
    filtered = filtered.filter(item => conditions.status.includes(item.status))
  }

  // 按错误级别筛选
  if (conditions.errorLevel.length > 0) {
    filtered = filtered.filter(item => {
      return conditions.errorLevel.some(level => {
        if (level === 'level1') return item.level1
        if (level === 'level2') return item.level2
        if (level === 'level3') return item.level3
        return false
      })
    })
  }

  // 按默认选中筛选
  if (conditions.defaultSelected !== '') {
    const isDefault = conditions.defaultSelected === 'true'
    filtered = filtered.filter(item => Boolean(item.default_use) === isDefault)
  }

  // 按创建时间筛选
  if (conditions.dateRange && conditions.dateRange.length === 2) {
    const [startDate, endDate] = conditions.dateRange
    filtered = filtered.filter(item => {
      const itemDate = new Date(item.created_at)
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  // 按关键词筛选
  if (conditions.keyword.trim()) {
    const keyword = conditions.keyword.trim().toLowerCase()
    filtered = filtered.filter(item => {
      return item.rule_name?.toLowerCase().includes(keyword) ||
             item.rule_detail_id?.toLowerCase().includes(keyword)
    })
  }

  return filtered
}

// 监听选择变化
watch(() => props.selectedItems, (newSelection) => {
  // 可以在这里添加选择变化的处理逻辑
}, { deep: true })
</script>

<style scoped>
.batch-selection-panel {
  margin-bottom: 16px;
}

.selection-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
}

.header-title {
  font-weight: 600;
  color: #303133;
}

.selection-content {
  padding: 0;
}

.quick-selection,
.condition-selection,
.selection-stats {
  padding: 16px 0;
  border-bottom: 1px solid #f0f2f5;
}

.selection-stats {
  border-bottom: none;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: #606266;
}

.quick-buttons {
  display: flex;
  gap: 8px;
}

.condition-form {
  background: #fafbfc;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.form-item {
  margin-bottom: 0;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.condition-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  gap: 8px;
}

.stats-content {
  display: flex;
  gap: 24px;
  align-items: center;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-label {
  font-size: 14px;
  color: #606266;
}

.stats-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stats-value.primary {
  color: #409eff;
}

.stats-value.zero {
  color: #909399;
}

.stats-value.low {
  color: #f56c6c;
}

.stats-value.medium {
  color: #e6a23c;
}

.stats-value.high {
  color: #67c23a;
}

.stats-unit {
  font-size: 12px;
  color: #909399;
}

.selection-limit {
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-content {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .condition-actions {
    flex-direction: column;
  }

  .quick-buttons .el-button-group {
    width: 100%;
  }

  .quick-buttons .el-button {
    flex: 1;
  }
}
</style>
