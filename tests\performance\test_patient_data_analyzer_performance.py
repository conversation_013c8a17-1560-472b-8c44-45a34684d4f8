"""
患者数据分析器性能基准测试
验证代码提取性能是否满足技术文档要求（<1毫秒）

测试内容：
1. 基础性能测试 - 验证提取时间<1毫秒
2. 复杂数据结构测试 - 测试嵌套数据处理性能
3. 大数据量测试 - 测试批量处理性能
4. 内存使用测试 - 验证内存效率
5. 准确性验证 - 确保提取结果正确
"""

import time
import statistics
from typing import Any
import unittest
from unittest.mock import Mock

from core.patient_data_analyzer import patient_data_analyzer
from core.rule_index_manager import PatientCodeExtraction
from models.patient import PatientData, FeeItem, DiagnoseInfo, DiagnosisList, PatientBasicInfo


class TestPatientDataAnalyzerPerformance(unittest.TestCase):
    """患者数据分析器性能测试"""

    def setUp(self):
        """测试前准备"""
        # 重置统计信息
        patient_data_analyzer.reset_stats()

    def test_basic_performance_requirement(self):
        """测试基础性能要求：代码提取时间<1毫秒"""
        # 创建标准测试患者数据
        patient_data = self._create_standard_patient_data()
        
        # 执行多次测试取平均值
        extraction_times = []
        for _ in range(100):
            start_time = time.perf_counter()
            result = patient_data_analyzer.extract_codes(patient_data)
            end_time = time.perf_counter()
            
            extraction_time_ms = (end_time - start_time) * 1000
            extraction_times.append(extraction_time_ms)
            
            # 验证返回结果类型
            self.assertIsInstance(result, PatientCodeExtraction)
        
        # 计算统计数据
        avg_time = statistics.mean(extraction_times)
        max_time = max(extraction_times)
        min_time = min(extraction_times)
        
        print(f"\n=== 基础性能测试结果 ===")
        print(f"平均提取时间: {avg_time:.3f}ms")
        print(f"最大提取时间: {max_time:.3f}ms")
        print(f"最小提取时间: {min_time:.3f}ms")
        print(f"标准差: {statistics.stdev(extraction_times):.3f}ms")
        
        # 验证性能要求：平均时间<1毫秒
        self.assertLess(avg_time, 1.0, f"平均提取时间{avg_time:.3f}ms超过1毫秒要求")
        # 验证稳定性：最大时间<2毫秒
        self.assertLess(max_time, 2.0, f"最大提取时间{max_time:.3f}ms过高，可能存在性能问题")

    def test_complex_nested_data_performance(self):
        """测试复杂嵌套数据结构的处理性能"""
        # 创建复杂嵌套结构的患者数据
        patient_data = self._create_complex_nested_patient_data()
        
        # 执行性能测试
        extraction_times = []
        for _ in range(50):
            start_time = time.perf_counter()
            result = patient_data_analyzer.extract_codes(patient_data)
            end_time = time.perf_counter()
            
            extraction_time_ms = (end_time - start_time) * 1000
            extraction_times.append(extraction_time_ms)
        
        avg_time = statistics.mean(extraction_times)
        
        print(f"\n=== 复杂嵌套数据测试结果 ===")
        print(f"平均提取时间: {avg_time:.3f}ms")
        print(f"提取的医保代码数量: {len(result.yb_codes)}")
        print(f"提取的诊断代码数量: {len(result.diag_codes)}")
        print(f"提取的手术代码数量: {len(result.surgery_codes)}")
        
        # 复杂数据结构的性能要求稍微放宽，但仍应<2毫秒
        self.assertLess(avg_time, 2.0, f"复杂数据提取时间{avg_time:.3f}ms过高")

    def test_large_dataset_performance(self):
        """测试大数据量的处理性能"""
        # 创建包含大量费用项目的患者数据
        patient_data = self._create_large_dataset_patient_data()
        
        # 执行性能测试
        extraction_times = []
        for _ in range(20):
            start_time = time.perf_counter()
            result = patient_data_analyzer.extract_codes(patient_data)
            end_time = time.perf_counter()
            
            extraction_time_ms = (end_time - start_time) * 1000
            extraction_times.append(extraction_time_ms)
        
        avg_time = statistics.mean(extraction_times)
        
        print(f"\n=== 大数据量测试结果 ===")
        print(f"费用项目数量: {len(patient_data.fees)}")
        print(f"平均提取时间: {avg_time:.3f}ms")
        print(f"提取的医保代码数量: {len(result.yb_codes)}")
        
        # 大数据量的性能要求：<5毫秒
        self.assertLess(avg_time, 5.0, f"大数据量提取时间{avg_time:.3f}ms过高")

    def test_extraction_accuracy(self):
        """测试代码提取的准确性"""
        patient_data = self._create_test_patient_with_known_codes()
        
        result = patient_data_analyzer.extract_codes(patient_data)
        
        # 验证医保代码提取准确性
        expected_yb_codes = {"Y001", "Y002", "Y003"}
        self.assertEqual(result.yb_codes, expected_yb_codes, "医保代码提取不准确")
        
        # 验证诊断代码提取准确性
        expected_diag_codes = {"I10", "E11.9", "Z51.1"}
        self.assertEqual(result.diag_codes, expected_diag_codes, "诊断代码提取不准确")
        
        # 验证手术代码提取准确性
        expected_surgery_codes = {"S001", "S002"}
        self.assertEqual(result.surgery_codes, expected_surgery_codes, "手术代码提取不准确")
        
        print(f"\n=== 准确性验证结果 ===")
        print(f"医保代码: {result.yb_codes}")
        print(f"诊断代码: {result.diag_codes}")
        print(f"手术代码: {result.surgery_codes}")

    def test_error_handling_performance(self):
        """测试异常处理的性能影响"""
        # 创建包含异常数据的患者数据
        patient_data = self._create_patient_with_invalid_data()
        
        extraction_times = []
        for _ in range(50):
            start_time = time.perf_counter()
            result = patient_data_analyzer.extract_codes(patient_data)
            end_time = time.perf_counter()
            
            extraction_time_ms = (end_time - start_time) * 1000
            extraction_times.append(extraction_time_ms)
        
        avg_time = statistics.mean(extraction_times)
        
        print(f"\n=== 异常处理性能测试结果 ===")
        print(f"平均提取时间: {avg_time:.3f}ms")
        print(f"错误计数: {patient_data_analyzer.get_performance_stats()['error_count']}")
        
        # 即使有异常数据，性能也不应显著下降
        self.assertLess(avg_time, 2.0, f"异常处理影响性能过大: {avg_time:.3f}ms")

    def _create_standard_patient_data(self) -> PatientData:
        """创建标准测试患者数据"""
        fees = [
            FeeItem(id="1", ybdm="Y001", sl=1.0, je=100.0, fymc="药品A"),
            FeeItem(id="2", ybdm="Y002", sl=2.0, je=200.0, fymc="检查B"),
            FeeItem(id="3", ybdm="Y003", sl=1.0, je=150.0, fymc="治疗C"),
        ]
        
        diagnosis_list = [
            DiagnosisList(diagnosisICDCode="I10", diagnosisName="高血压"),
            DiagnosisList(diagnosisICDCode="E11.9", diagnosisName="糖尿病"),
        ]
        
        diagnosis = DiagnoseInfo(
            outPatientDiagnosisICDCode="I10",
            diagnosis=diagnosis_list
        )
        
        return PatientData(
            bah="TEST001",
            fees=fees,
            Diagnosis=diagnosis,
            basic_information=PatientBasicInfo(age=45, gender="1")
        )

    def _create_complex_nested_patient_data(self) -> PatientData:
        """创建复杂嵌套结构的患者数据"""
        # 创建更多费用项目和复杂诊断结构
        fees = []
        for i in range(20):
            fees.append(FeeItem(
                id=str(i),
                ybdm=f"Y{i:03d}",
                sl=float(i % 5 + 1),
                je=float((i + 1) * 50),
                fymc=f"项目{i}"
            ))
        
        diagnosis_list = []
        for i in range(10):
            diagnosis_list.append(DiagnosisList(
                diagnosisICDCode=f"I{i:02d}",
                diagnosisName=f"诊断{i}"
            ))
        
        diagnosis = DiagnoseInfo(
            outPatientDiagnosisICDCode="I00",
            diagnoseICDCodeOnAdmission="I01",
            diagnosis=diagnosis_list
        )
        
        return PatientData(
            bah="COMPLEX001",
            fees=fees,
            Diagnosis=diagnosis,
            basic_information=PatientBasicInfo(age=55, gender="2")
        )

    def _create_large_dataset_patient_data(self) -> PatientData:
        """创建大数据量的患者数据"""
        # 创建100个费用项目
        fees = []
        for i in range(100):
            fees.append(FeeItem(
                id=str(i),
                ybdm=f"Y{i:04d}",
                sl=float(i % 10 + 1),
                je=float((i + 1) * 25),
                fymc=f"大数据项目{i}"
            ))
        
        return PatientData(
            bah="LARGE001",
            fees=fees,
            Diagnosis=DiagnoseInfo(outPatientDiagnosisICDCode="I10"),
            basic_information=PatientBasicInfo(age=35, gender="1")
        )

    def _create_test_patient_with_known_codes(self) -> PatientData:
        """创建包含已知代码的测试患者数据"""
        fees = [
            FeeItem(id="1", ybdm="Y001", sl=1.0, je=100.0, fymc="药品A"),
            FeeItem(id="2", ybdm="Y002", sl=2.0, je=200.0, fymc="检查B"),
            FeeItem(id="3", ybdm="Y003", sl=1.0, je=150.0, fymc="手术治疗", type="手术"),
            FeeItem(id="4", ybdm="S001", sl=1.0, je=500.0, fymc="手术操作", category="surgical"),
            FeeItem(id="5", ybdm="S002", sl=1.0, je=300.0, fymc="术后处理", name="手术相关"),
        ]
        
        diagnosis_list = [
            DiagnosisList(diagnosisICDCode="I10", diagnosisName="高血压"),
            DiagnosisList(diagnosisICDCode="E11.9", diagnosisName="糖尿病"),
            DiagnosisList(diagnosisICDCode="Z51.1", diagnosisName="化疗"),
        ]
        
        diagnosis = DiagnoseInfo(diagnosis=diagnosis_list)
        
        return PatientData(
            bah="KNOWN001",
            fees=fees,
            Diagnosis=diagnosis
        )

    def _create_patient_with_invalid_data(self) -> PatientData:
        """创建包含异常数据的患者数据"""
        # 创建包含None值和异常结构的数据
        fees = [
            FeeItem(id="1", ybdm="Y001", sl=1.0, je=100.0),
            FeeItem(id="2", ybdm=None, sl=2.0, je=200.0),  # None值
            FeeItem(id="3", ybdm="", sl=1.0, je=150.0),    # 空字符串
        ]
        
        return PatientData(
            bah="INVALID001",
            fees=fees,
            Diagnosis=None  # None诊断
        )


if __name__ == "__main__":
    unittest.main()
