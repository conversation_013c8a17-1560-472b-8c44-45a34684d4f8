import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useRuleDetailsStore } from '@/stores/ruleDetails'
import * as ruleDetailsAPI from '@/api/ruleDetails'

// Mock API 模块
vi.mock('@/api/ruleDetails', () => ({
  getRuleDetailsList: vi.fn(),
  getRuleDetailById: vi.fn(),
  createRuleDetail: vi.fn(),
  updateRuleDetail: vi.fn(),
  deleteRuleDetail: vi.fn(),
  batchCreateRuleDetails: vi.fn(),
  batchUpdateRuleDetails: vi.fn(),
  batchDeleteRuleDetails: vi.fn(),
  searchRuleDetails: vi.fn()
}))

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  }
}))

describe('useRuleDetailsStore', () => {
  let store

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useRuleDetailsStore()
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(store.detailsList).toEqual([])
      expect(store.currentDetail).toBeNull()
      expect(store.selectedDetails).toEqual([])
      expect(store.pagination.page).toBe(1)
      expect(store.pagination.pageSize).toBe(20)
      expect(store.loading).toBe(false)
      expect(store.detailLoading).toBe(false)
      expect(store.batchLoading).toBe(false)
      expect(store.searchLoading).toBe(false)
    })

    it('计算属性应该返回正确的值', () => {
      expect(store.detailsCount).toBe(0)
      expect(store.hasDetails).toBe(false)
      expect(store.selectedCount).toBe(0)
      expect(store.hasSelected).toBe(false)
      expect(store.isLoading).toBe(false)
    })
  })

  describe('fetchDetailsList', () => {
    const mockRuleKey = 'test-rule'
    const mockResponse = {
      success: true,
      data: {
        items: [
          { id: 1, rule_detail_id: 'R001', rule_name: '规则1' },
          { id: 2, rule_detail_id: 'R002', rule_name: '规则2' }
        ],
        page: 1,
        page_size: 20,
        total: 2,
        has_next: false,
        has_prev: false
      }
    }

    it('应该成功获取明细列表', async () => {
      ruleDetailsAPI.getRuleDetailsList.mockResolvedValue(mockResponse)

      const result = await store.fetchDetailsList(mockRuleKey)

      expect(ruleDetailsAPI.getRuleDetailsList).toHaveBeenCalledWith(
        mockRuleKey,
        expect.objectContaining({
          status: null,
          search: '',
          sortBy: 'created_at',
          sortOrder: 'desc'
        })
      )
      expect(store.detailsList).toEqual(mockResponse.data.items)
      expect(store.pagination.total).toBe(2)
      expect(result).toEqual(mockResponse)
    })

    it('应该处理 API 错误', async () => {
      const mockError = new Error('API Error')
      ruleDetailsAPI.getRuleDetailsList.mockRejectedValue(mockError)

      await expect(store.fetchDetailsList(mockRuleKey)).rejects.toThrow('API Error')
      expect(store.detailsList).toEqual([])
    })

    it('应该使用缓存', async () => {
      // 第一次调用
      ruleDetailsAPI.getRuleDetailsList.mockResolvedValue(mockResponse)
      await store.fetchDetailsList(mockRuleKey)

      // 第二次调用应该使用缓存
      const result = await store.fetchDetailsList(mockRuleKey, {}, true)
      expect(ruleDetailsAPI.getRuleDetailsList).toHaveBeenCalledTimes(1)
      expect(result).toEqual(mockResponse.data)
    })
  })

  describe('fetchDetailById', () => {
    const mockRuleKey = 'test-rule'
    const mockDetailId = 1
    const mockDetail = {
      id: 1,
      rule_detail_id: 'R001',
      rule_name: '规则1',
      status: 'ACTIVE'
    }

    it('应该成功获取单条明细', async () => {
      ruleDetailsAPI.getRuleDetailById.mockResolvedValue(mockDetail)

      const result = await store.fetchDetailById(mockRuleKey, mockDetailId)

      expect(ruleDetailsAPI.getRuleDetailById).toHaveBeenCalledWith(mockRuleKey, mockDetailId)
      expect(store.currentDetail).toEqual(mockDetail)
      expect(result).toEqual(mockDetail)
    })

    it('应该处理无效参数', async () => {
      const result = await store.fetchDetailById('', mockDetailId)
      expect(result).toBeNull()
      expect(ruleDetailsAPI.getRuleDetailById).not.toHaveBeenCalled()
    })
  })

  describe('createDetail', () => {
    const mockRuleKey = 'test-rule'
    const mockDetailData = {
      rule_detail_id: 'R003',
      rule_name: '新规则',
      status: 'ACTIVE'
    }
    const mockResult = { id: 3, ...mockDetailData }

    it('应该成功创建明细', async () => {
      ruleDetailsAPI.createRuleDetail.mockResolvedValue(mockResult)

      const result = await store.createDetail(mockRuleKey, mockDetailData)

      expect(ruleDetailsAPI.createRuleDetail).toHaveBeenCalledWith(mockRuleKey, mockDetailData)
      expect(store.operationStatus.lastOperation).toBe('create')
      expect(store.operationStatus.affectedCount).toBe(1)
      expect(result).toEqual(mockResult)
    })
  })

  describe('updateDetail', () => {
    const mockRuleKey = 'test-rule'
    const mockDetailId = 1
    const mockUpdateData = { rule_name: '更新的规则' }
    const mockResult = { id: 1, rule_name: '更新的规则' }

    beforeEach(() => {
      // 设置初始明细列表
      store.detailsList = [
        { id: 1, rule_detail_id: 'R001', rule_name: '原规则' },
        { id: 2, rule_detail_id: 'R002', rule_name: '规则2' }
      ]
    })

    it('应该成功更新明细', async () => {
      ruleDetailsAPI.updateRuleDetail.mockResolvedValue(mockResult)

      const result = await store.updateDetail(mockRuleKey, mockDetailId, mockUpdateData)

      expect(ruleDetailsAPI.updateRuleDetail).toHaveBeenCalledWith(
        mockRuleKey,
        mockDetailId,
        mockUpdateData
      )
      expect(store.detailsList[0].rule_name).toBe('更新的规则')
      expect(store.operationStatus.lastOperation).toBe('update')
      expect(result).toEqual(mockResult)
    })
  })

  describe('deleteDetail', () => {
    const mockRuleKey = 'test-rule'
    const mockDetailId = 1

    beforeEach(() => {
      // 设置初始明细列表
      store.detailsList = [
        { id: 1, rule_detail_id: 'R001', rule_name: '规则1' },
        { id: 2, rule_detail_id: 'R002', rule_name: '规则2' }
      ]
      store.selectedDetails = [{ id: 1, rule_detail_id: 'R001', rule_name: '规则1' }]
    })

    it('应该成功删除明细', async () => {
      ruleDetailsAPI.deleteRuleDetail.mockResolvedValue(true)

      const result = await store.deleteDetail(mockRuleKey, mockDetailId)

      expect(ruleDetailsAPI.deleteRuleDetail).toHaveBeenCalledWith(mockRuleKey, mockDetailId)
      expect(store.detailsList).toHaveLength(1)
      expect(store.detailsList[0].id).toBe(2)
      expect(store.selectedDetails).toHaveLength(0)
      expect(store.operationStatus.lastOperation).toBe('delete')
      expect(result).toBe(true)
    })
  })

  describe('批量操作', () => {
    const mockRuleKey = 'test-rule'

    describe('batchCreateDetails', () => {
      const mockDetailsData = [
        { rule_detail_id: 'R003', rule_name: '规则3' },
        { rule_detail_id: 'R004', rule_name: '规则4' }
      ]
      const mockResult = {
        successCount: 2,
        errors: []
      }

      it('应该成功批量创建明细', async () => {
        ruleDetailsAPI.batchCreateRuleDetails.mockResolvedValue(mockResult)

        const result = await store.batchCreateDetails(mockRuleKey, mockDetailsData)

        expect(ruleDetailsAPI.batchCreateRuleDetails).toHaveBeenCalledWith(
          mockRuleKey,
          mockDetailsData
        )
        expect(store.operationStatus.lastOperation).toBe('batchCreate')
        expect(store.operationStatus.affectedCount).toBe(2)
        expect(result).toEqual(mockResult)
      })

      it('应该处理空数据', async () => {
        const result = await store.batchCreateDetails(mockRuleKey, [])
        expect(result).toBeNull()
        expect(ruleDetailsAPI.batchCreateRuleDetails).not.toHaveBeenCalled()
      })
    })

    describe('batchUpdateDetails', () => {
      const mockUpdates = [
        { id: 1, data: { rule_name: '更新规则1' } },
        { id: 2, data: { rule_name: '更新规则2' } }
      ]
      const mockResult = {
        successCount: 2,
        errors: [],
        updatedDetails: [
          { id: 1, rule_name: '更新规则1' },
          { id: 2, rule_name: '更新规则2' }
        ]
      }

      beforeEach(() => {
        store.detailsList = [
          { id: 1, rule_detail_id: 'R001', rule_name: '规则1' },
          { id: 2, rule_detail_id: 'R002', rule_name: '规则2' }
        ]
      })

      it('应该成功批量更新明细', async () => {
        ruleDetailsAPI.batchUpdateRuleDetails.mockResolvedValue(mockResult)

        const result = await store.batchUpdateDetails(mockRuleKey, mockUpdates)

        expect(ruleDetailsAPI.batchUpdateRuleDetails).toHaveBeenCalledWith(
          mockRuleKey,
          mockUpdates
        )
        expect(store.detailsList[0].rule_name).toBe('更新规则1')
        expect(store.detailsList[1].rule_name).toBe('更新规则2')
        expect(store.operationStatus.lastOperation).toBe('batchUpdate')
        expect(result).toEqual(mockResult)
      })

      it('应该在失败时回滚乐观更新', async () => {
        const mockError = new Error('Batch update failed')
        ruleDetailsAPI.batchUpdateRuleDetails.mockRejectedValue(mockError)

        await expect(store.batchUpdateDetails(mockRuleKey, mockUpdates)).rejects.toThrow(
          'Batch update failed'
        )

        // 验证回滚
        expect(store.detailsList[0].rule_name).toBe('规则1')
        expect(store.detailsList[1].rule_name).toBe('规则2')
      })
    })

    describe('batchDeleteDetails', () => {
      const mockDetailIds = [1, 2]
      const mockResult = {
        successCount: 2,
        errors: []
      }

      beforeEach(() => {
        store.detailsList = [
          { id: 1, rule_detail_id: 'R001', rule_name: '规则1' },
          { id: 2, rule_detail_id: 'R002', rule_name: '规则2' },
          { id: 3, rule_detail_id: 'R003', rule_name: '规则3' }
        ]
        store.selectedDetails = [
          { id: 1, rule_detail_id: 'R001', rule_name: '规则1' },
          { id: 2, rule_detail_id: 'R002', rule_name: '规则2' }
        ]
      })

      it('应该成功批量删除明细', async () => {
        ruleDetailsAPI.batchDeleteRuleDetails.mockResolvedValue(mockResult)

        const result = await store.batchDeleteDetails(mockRuleKey, mockDetailIds)

        expect(ruleDetailsAPI.batchDeleteRuleDetails).toHaveBeenCalledWith(
          mockRuleKey,
          mockDetailIds
        )
        expect(store.detailsList).toHaveLength(1)
        expect(store.detailsList[0].id).toBe(3)
        expect(store.selectedDetails).toHaveLength(0)
        expect(store.operationStatus.lastOperation).toBe('batchDelete')
        expect(result).toEqual(mockResult)
      })
    })
  })

  describe('搜索和过滤', () => {
    const mockRuleKey = 'test-rule'
    const mockSearchParams = { search: '规则', page: 1 }
    const mockSearchResult = {
      items: [{ id: 1, rule_detail_id: 'R001', rule_name: '规则1' }],
      pagination: { page: 1, pageSize: 20, total: 1 }
    }

    it('应该成功搜索明细', async () => {
      ruleDetailsAPI.searchRuleDetails.mockResolvedValue(mockSearchResult)

      const result = await store.searchDetails(mockRuleKey, mockSearchParams)

      expect(ruleDetailsAPI.searchRuleDetails).toHaveBeenCalledWith(
        mockRuleKey,
        mockSearchParams
      )
      expect(store.detailsList).toEqual(mockSearchResult.items)
      expect(result).toEqual(mockSearchResult)
    })

    it('应该更新过滤条件', () => {
      const newFilters = { status: 'ACTIVE', search: '测试' }
      store.updateFilters(newFilters)

      expect(store.filters.status).toBe('ACTIVE')
      expect(store.filters.search).toBe('测试')
    })

    it('应该重置过滤条件', () => {
      store.filters.status = 'ACTIVE'
      store.filters.search = '测试'

      store.resetFilters()

      expect(store.filters.status).toBeNull()
      expect(store.filters.search).toBe('')
      expect(store.filters.sortBy).toBe('created_at')
      expect(store.filters.sortOrder).toBe('desc')
    })
  })

  describe('选择管理', () => {
    const mockDetails = [
      { id: 1, rule_detail_id: 'R001', rule_name: '规则1' },
      { id: 2, rule_detail_id: 'R002', rule_name: '规则2' },
      { id: 3, rule_detail_id: 'R003', rule_name: '规则3' }
    ]

    beforeEach(() => {
      store.detailsList = mockDetails
    })

    it('应该选择单条明细', () => {
      store.selectDetail(mockDetails[0])
      expect(store.selectedDetails).toHaveLength(1)
      expect(store.selectedDetails[0].id).toBe(1)
    })

    it('应该取消选择单条明细', () => {
      store.selectedDetails = [mockDetails[0]]
      store.unselectDetail(mockDetails[0])
      expect(store.selectedDetails).toHaveLength(0)
    })

    it('应该切换明细选择状态', () => {
      // 选择
      store.toggleDetailSelection(mockDetails[0])
      expect(store.selectedDetails).toHaveLength(1)

      // 取消选择
      store.toggleDetailSelection(mockDetails[0])
      expect(store.selectedDetails).toHaveLength(0)
    })

    it('应该全选/取消全选', () => {
      // 全选
      store.toggleSelectAll(true)
      expect(store.selectedDetails).toHaveLength(3)

      // 取消全选
      store.toggleSelectAll(false)
      expect(store.selectedDetails).toHaveLength(0)
    })

    it('应该清除所有选择', () => {
      store.selectedDetails = [...mockDetails]
      store.clearSelection()
      expect(store.selectedDetails).toHaveLength(0)
    })
  })

  describe('状态管理', () => {
    const mockDetail = { id: 1, rule_detail_id: 'R001', rule_name: '规则1' }

    it('应该设置当前明细', () => {
      store.setCurrentDetail(mockDetail)
      expect(store.currentDetail).toEqual(mockDetail)
    })

    it('应该清除当前明细', () => {
      store.currentDetail = mockDetail
      store.clearCurrentDetail()
      expect(store.currentDetail).toBeNull()
    })

    it('应该重置整个 Store 状态', () => {
      // 设置一些状态
      store.detailsList = [mockDetail]
      store.currentDetail = mockDetail
      store.selectedDetails = [mockDetail]
      store.pagination.page = 2
      store.filters.search = '测试'

      // 重置
      store.resetStore()

      // 验证重置结果
      expect(store.detailsList).toEqual([])
      expect(store.currentDetail).toBeNull()
      expect(store.selectedDetails).toEqual([])
      expect(store.pagination.page).toBe(1)
      expect(store.filters.search).toBe('')
      expect(store.loading).toBe(false)
    })
  })
})
