<template>
  <div class="submission-result">
    <div class="result-content">
      <!-- 成功状态头部 -->
      <div class="result-header">
        <div class="result-icon success">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="result-title">
          <h2>数据提交完成</h2>
          <p class="result-message">{{ result.message || '规则数据已成功处理并激活' }}</p>
        </div>
      </div>

      <!-- 详细统计信息 -->
      <div class="result-statistics">
        <div class="stats-grid">
          <!-- 新建记录 -->
          <div class="stat-card created">
            <div class="stat-icon">
              <el-icon><Plus /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ operationDetails.created_count || 0 }}</div>
              <div class="stat-label">新建记录</div>
            </div>
          </div>

          <!-- 更新记录 -->
          <div class="stat-card updated">
            <div class="stat-icon">
              <el-icon><Edit /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ operationDetails.updated_count || 0 }}</div>
              <div class="stat-label">更新记录</div>
            </div>
          </div>

          <!-- 跳过记录 -->
          <div class="stat-card skipped">
            <div class="stat-icon">
              <el-icon><Remove /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ operationDetails.skipped_count || 0 }}</div>
              <div class="stat-label">跳过重复</div>
            </div>
          </div>

          <!-- 失败记录 -->
          <div class="stat-card failed" v-if="operationDetails.failed_count > 0">
            <div class="stat-icon">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ operationDetails.failed_count || 0 }}</div>
              <div class="stat-label">处理失败</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 处理摘要 -->
      <div class="processing-summary">
        <el-card class="summary-card">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>处理摘要</span>
            </div>
          </template>

          <div class="summary-content">
            <div class="summary-item">
              <span class="summary-label">总处理数量：</span>
              <span class="summary-value">{{ result.total_items || 0 }} 条</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">成功处理：</span>
              <span class="summary-value success">{{ result.success_count || 0 }} 条</span>
            </div>
            <div class="summary-item" v-if="result.failed_count > 0">
              <span class="summary-label">处理失败：</span>
              <span class="summary-value failed">{{ result.failed_count || 0 }} 条</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">存储模式：</span>
              <span class="summary-value">{{ storageMode }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 错误信息（如果有） -->
      <div v-if="result.errors && result.errors.length > 0" class="error-details">
        <el-card class="error-card">
          <template #header>
            <div class="card-header error">
              <el-icon><Warning /></el-icon>
              <span>处理错误详情</span>
            </div>
          </template>

          <div class="error-list">
            <div v-for="(error, index) in result.errors" :key="index" class="error-item">
              <el-icon class="error-icon"><CircleClose /></el-icon>
              <span class="error-text">{{ error }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <div class="result-actions">
        <el-button @click="handleViewDetails" type="primary" size="large">
          <el-icon><View /></el-icon>
          查看规则详情
        </el-button>
        <el-button @click="handleNewUpload" size="large">
          <el-icon><Upload /></el-icon>
          继续上传
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  CircleCheck,
  CircleClose,
  Plus,
  Edit,
  Remove,
  DataAnalysis,
  Warning,
  View,
  Upload
} from '@element-plus/icons-vue'

const props = defineProps({
  result: {
    type: Object,
    required: true,
    default: () => ({})
  },
  ruleKey: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['view-details', 'new-upload'])

// 计算属性
const operationDetails = computed(() => {
  return props.result.operation_details || {}
})

const storageMode = computed(() => {
  const mode = props.result.storage_mode || 'structured'
  return mode === 'structured' ? '结构化存储' : '传统存储'
})

// 事件处理
const handleViewDetails = () => {
  emit('view-details', props.ruleKey)
}

const handleNewUpload = () => {
  emit('new-upload')
}
</script>

<style scoped>
.submission-result {
  padding: 20px;
}

.result-content {
  max-width: 800px;
  margin: 0 auto;
}

/* 结果头部 */
.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid #bae6fd;
}

.result-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 32px;
}

.result-icon.success {
  background-color: #dcfce7;
  color: #16a34a;
}

.result-title h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.result-message {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

/* 统计卡片网格 */
.result-statistics {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.stat-card.created .stat-icon {
  background-color: #dcfce7;
  color: #16a34a;
}

.stat-card.updated .stat-icon {
  background-color: #dbeafe;
  color: #2563eb;
}

.stat-card.skipped .stat-icon {
  background-color: #f3f4f6;
  color: #6b7280;
}

.stat-card.failed .stat-icon {
  background-color: #fee2e2;
  color: #dc2626;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* 处理摘要 */
.processing-summary {
  margin-bottom: 24px;
}

.summary-card {
  border: 1px solid #e5e7eb;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #1f2937;
}

.card-header .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.card-header.error {
  color: #dc2626;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  color: #6b7280;
  font-weight: 500;
}

.summary-value {
  font-weight: 600;
  color: #1f2937;
}

.summary-value.success {
  color: #16a34a;
}

.summary-value.failed {
  color: #dc2626;
}

/* 错误详情 */
.error-details {
  margin-bottom: 24px;
}

.error-card {
  border: 1px solid #fecaca;
  background-color: #fef2f2;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-item {
  display: flex;
  align-items: flex-start;
  padding: 8px;
  background-color: #ffffff;
  border-radius: 6px;
  border: 1px solid #fecaca;
}

.error-icon {
  color: #dc2626;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.error-text {
  color: #7f1d1d;
  font-size: 14px;
  line-height: 1.5;
}

/* 操作按钮 */
.result-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-header {
    flex-direction: column;
    text-align: center;
  }

  .result-icon {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .result-actions {
    flex-direction: column;
  }

  .result-actions .el-button {
    width: 100%;
  }
}
</style>
