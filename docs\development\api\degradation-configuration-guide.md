# 降级机制配置指南

## 1. 概述

本文档详细说明了降级机制的所有配置选项，包括基础配置、触发器配置、组件配置等。正确的配置是降级机制有效运行的关键。

## 2. 配置文件结构

### 2.1 主配置文件
配置文件位置：`config/degradation_config.py`

```python
from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class DegradationConfig:
    # 基础配置
    enabled: bool = True
    auto_start: bool = True
    monitoring_interval: float = 10.0
    
    # 触发器配置
    triggers: Dict = field(default_factory=dict)
    
    # 组件配置
    components: Dict = field(default_factory=dict)
    
    # 恢复配置
    recovery: Dict = field(default_factory=dict)
```

### 2.2 环境变量配置
```bash
# 基础配置
DEGRADATION_ENABLED=true
DEGRADATION_AUTO_START=true
DEGRADATION_MONITORING_INTERVAL=10.0

# API配置
DEGRADATION_API_ENABLED=true
DEGRADATION_API_KEY=your_secure_api_key

# 日志配置
DEGRADATION_LOG_LEVEL=INFO
DEGRADATION_LOG_FILE=/var/log/degradation.log
```

## 3. 基础配置

### 3.1 启用控制
```python
# 是否启用降级机制
enabled: bool = True

# 是否自动启动监控
auto_start: bool = True

# 监控间隔（秒）
monitoring_interval: float = 10.0

# 是否启用API接口
api_enabled: bool = True

# API密钥（用于管理接口认证）
api_key: str = "your_secure_api_key"
```

### 3.2 日志配置
```python
logging_config = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "file": "/var/log/degradation.log",
    "max_size": "10MB",
    "backup_count": 5,
    "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
}
```

## 4. 触发器配置

### 4.1 CPU使用率触发器
```python
cpu_usage_trigger = {
    "enabled": True,
    "thresholds": {
        "l1": 70.0,  # L1降级阈值（%）
        "l2": 80.0,  # L2降级阈值（%）
        "l3": 90.0   # L3降级阈值（%）
    },
    "duration": 30.0,  # 持续时间（秒）
    "check_interval": 5.0,  # 检查间隔（秒）
    "recovery_factor": 0.8  # 恢复阈值因子
}
```

### 4.2 内存使用率触发器
```python
memory_usage_trigger = {
    "enabled": True,
    "thresholds": {
        "l1": 75.0,  # L1降级阈值（%）
        "l2": 85.0,  # L2降级阈值（%）
        "l3": 95.0   # L3降级阈值（%）
    },
    "duration": 30.0,
    "check_interval": 5.0,
    "recovery_factor": 0.8
}
```

### 4.3 错误率触发器
```python
error_rate_trigger = {
    "enabled": True,
    "thresholds": {
        "l1": 2.0,   # L1降级阈值（%）
        "l2": 5.0,   # L2降级阈值（%）
        "l3": 10.0   # L3降级阈值（%）
    },
    "duration": 60.0,  # 错误率统计时间窗口
    "check_interval": 10.0,
    "recovery_factor": 0.5
}
```

### 4.4 响应时间触发器
```python
response_time_trigger = {
    "enabled": True,
    "thresholds": {
        "l1": 200.0,  # L1降级阈值（毫秒）
        "l2": 500.0,  # L2降级阈值（毫秒）
        "l3": 1000.0  # L3降级阈值（毫秒）
    },
    "duration": 60.0,
    "check_interval": 10.0,
    "recovery_factor": 0.7
}
```

## 5. 组件配置

### 5.1 动态进程池配置
```python
dynamic_process_pool_config = {
    "enabled": True,
    "degradation_factors": {
        "l1": 0.75,  # L1降级：保留75%进程
        "l2": 0.50,  # L2降级：保留50%进程
        "l3": 0.25   # L3降级：保留25%进程
    },
    "min_workers": 2,  # 最小工作进程数
    "disable_auto_adjustment": {
        "l1": False,
        "l2": False,
        "l3": True   # L3级别禁用自动调整
    }
}
```

### 5.2 智能缓存配置
```python
intelligent_cache_config = {
    "enabled": True,
    "degradation_factors": {
        "l1": 0.75,  # L1降级：保留75%缓存
        "l2": 0.50,  # L2降级：保留50%缓存
        "l3": 0.0    # L3降级：禁用缓存
    },
    "ttl_factors": {
        "l1": 1.0,   # L1：TTL不变
        "l2": 0.5,   # L2：TTL减半
        "l3": 0.0    # L3：立即过期
    },
    "clear_on_severe": True  # L3级别清空缓存
}
```

### 5.3 对象池配置
```python
object_pool_config = {
    "enabled": True,
    "degradation_factors": {
        "l1": 0.75,  # L1降级：保留75%对象
        "l2": 0.50,  # L2降级：保留50%对象
        "l3": 0.0    # L3降级：禁用对象池
    },
    "disable_on_severe": True,  # L3级别禁用对象池
    "cleanup_interval": 60.0    # 清理间隔（秒）
}
```

## 6. 恢复配置

### 6.1 自动恢复配置
```python
recovery_config = {
    "enabled": True,
    "threshold_factor": 0.8,  # 恢复阈值因子
    "duration_factor": 2.0,   # 恢复持续时间因子
    "check_interval": 30.0,   # 恢复检查间隔
    "gradual_recovery": True, # 是否逐级恢复
    "recovery_delay": 60.0    # 恢复延迟（秒）
}
```

### 6.2 手动恢复配置
```python
manual_recovery_config = {
    "require_reason": True,   # 是否要求提供恢复原因
    "confirmation_required": False,  # 是否需要二次确认
    "audit_log": True,        # 是否记录审计日志
    "notify_on_recovery": True  # 恢复时是否发送通知
}
```

## 7. 环境特定配置

### 7.1 开发环境配置
```python
development_config = {
    "enabled": True,
    "monitoring_interval": 5.0,  # 更频繁的监控
    "triggers": {
        "cpu_usage": {
            "thresholds": {"l1": 60.0, "l2": 70.0, "l3": 80.0},
            "duration": 10.0  # 更短的触发时间
        }
    },
    "logging": {
        "level": "DEBUG",
        "console_output": True
    }
}
```

### 7.2 生产环境配置
```python
production_config = {
    "enabled": True,
    "monitoring_interval": 10.0,
    "triggers": {
        "cpu_usage": {
            "thresholds": {"l1": 70.0, "l2": 80.0, "l3": 90.0},
            "duration": 30.0
        }
    },
    "logging": {
        "level": "INFO",
        "console_output": False,
        "file": "/var/log/degradation.log"
    },
    "alerts": {
        "enabled": True,
        "webhook_url": "https://your-alert-system.com/webhook"
    }
}
```

### 7.3 测试环境配置
```python
testing_config = {
    "enabled": False,  # 测试时通常禁用
    "mock_mode": True,  # 启用模拟模式
    "triggers": {
        "cpu_usage": {
            "thresholds": {"l1": 50.0, "l2": 60.0, "l3": 70.0},
            "duration": 5.0
        }
    }
}
```

## 8. 高级配置

### 8.1 Master-Slave配置
```python
master_slave_config = {
    "mode": "master",  # master 或 slave
    "sync_interval": 30.0,  # 状态同步间隔
    "master_url": "http://master-node:8000",
    "slave_nodes": [
        "http://slave-1:8000",
        "http://slave-2:8000"
    ],
    "sync_timeout": 10.0,
    "fallback_to_local": True  # 网络故障时使用本地状态
}
```

### 8.2 告警配置
```python
alert_config = {
    "enabled": True,
    "channels": {
        "webhook": {
            "url": "https://your-webhook.com/alert",
            "timeout": 10.0,
            "retry_count": 3
        },
        "email": {
            "smtp_server": "smtp.example.com",
            "smtp_port": 587,
            "username": "<EMAIL>",
            "password": "your_password",
            "recipients": ["<EMAIL>"]
        }
    },
    "rules": {
        "degradation_triggered": True,
        "degradation_failed": True,
        "recovery_failed": True,
        "frequent_degradation": True
    }
}
```

## 9. 配置验证

### 9.1 配置检查脚本
```python
def validate_degradation_config(config):
    """验证降级配置的有效性"""
    errors = []
    
    # 检查基础配置
    if not isinstance(config.enabled, bool):
        errors.append("enabled必须是布尔值")
    
    # 检查触发器配置
    for trigger_name, trigger_config in config.triggers.items():
        if "thresholds" not in trigger_config:
            errors.append(f"触发器{trigger_name}缺少thresholds配置")
    
    # 检查组件配置
    for component_name, component_config in config.components.items():
        if "degradation_factors" not in component_config:
            errors.append(f"组件{component_name}缺少degradation_factors配置")
    
    return errors
```

### 9.2 配置测试
```bash
# 验证配置文件
python -m config.degradation_config --validate

# 测试配置加载
python -c "from config.degradation_config import load_config; print(load_config())"
```

## 10. 配置最佳实践

### 10.1 阈值设置建议
- **CPU阈值**：根据系统正常负载设置，建议L1=70%, L2=80%, L3=90%
- **内存阈值**：考虑GC影响，建议L1=75%, L2=85%, L3=95%
- **错误率阈值**：根据业务要求，建议L1=2%, L2=5%, L3=10%
- **持续时间**：避免频繁触发，建议30-60秒

### 10.2 组件配置建议
- **进程池**：保留足够的最小进程数，避免完全停止
- **缓存**：L3级别可以完全禁用，但要考虑性能影响
- **对象池**：禁用时要确保直接创建对象的性能可接受

### 10.3 恢复配置建议
- **恢复阈值**：设置为触发阈值的80%，避免频繁切换
- **恢复时间**：设置为触发时间的2倍，确保系统稳定
- **逐级恢复**：启用逐级恢复，避免突然的性能冲击

### 10.4 监控配置建议
- **监控间隔**：平衡及时性和性能开销，建议10-30秒
- **日志级别**：生产环境使用INFO，调试时使用DEBUG
- **告警配置**：设置合理的告警规则，避免告警疲劳

## 11. 配置示例

### 11.1 完整配置示例
```python
# config/degradation_config.py
DEGRADATION_CONFIG = {
    "enabled": True,
    "auto_start": True,
    "monitoring_interval": 10.0,
    "api_enabled": True,
    "api_key": "your_secure_api_key",
    
    "triggers": {
        "cpu_usage": {
            "enabled": True,
            "thresholds": {"l1": 70.0, "l2": 80.0, "l3": 90.0},
            "duration": 30.0,
            "check_interval": 5.0,
            "recovery_factor": 0.8
        },
        "memory_usage": {
            "enabled": True,
            "thresholds": {"l1": 75.0, "l2": 85.0, "l3": 95.0},
            "duration": 30.0,
            "check_interval": 5.0,
            "recovery_factor": 0.8
        }
    },
    
    "components": {
        "dynamic_process_pool": {
            "enabled": True,
            "degradation_factors": {"l1": 0.75, "l2": 0.50, "l3": 0.25},
            "min_workers": 2
        },
        "intelligent_cache": {
            "enabled": True,
            "degradation_factors": {"l1": 0.75, "l2": 0.50, "l3": 0.0},
            "clear_on_severe": True
        }
    },
    
    "recovery": {
        "enabled": True,
        "threshold_factor": 0.8,
        "duration_factor": 2.0,
        "gradual_recovery": True
    }
}
```

### 11.2 Docker环境变量示例
```dockerfile
ENV DEGRADATION_ENABLED=true
ENV DEGRADATION_AUTO_START=true
ENV DEGRADATION_MONITORING_INTERVAL=10.0
ENV DEGRADATION_API_KEY=your_secure_api_key
ENV DEGRADATION_LOG_LEVEL=INFO
```
