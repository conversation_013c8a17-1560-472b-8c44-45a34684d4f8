# 双模式同步快速开始指南

## 概述

双模式同步机制支持在线实时同步和离线包部署两种模式，本指南将帮助您快速上手双模式同步功能。

## 🚀 5分钟快速体验

### 前提条件

- Docker 和 Docker Compose 已安装
- 至少2GB可用内存
- 网络连接正常（在线模式）

### 步骤1：获取项目代码

```bash
git clone <repository-url>
cd rule-validation-service
```

### 步骤2：配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env
```

**在线同步模式配置**：
```bash
# 主节点配置
MODE=master
ENABLE_RULE_SYNC=true
SYNC_INCREMENTAL_ENABLED=true
MASTER_API_SECRET_KEY=demo_api_key_12345

# 从节点配置
MODE=slave
MASTER_API_ENDPOINT=http://master:18001
SLAVE_API_KEY=demo_api_key_12345
```

**离线模式配置**：
```bash
# 主节点配置（用于生成离线包）
MODE=master
ENABLE_RULE_SYNC=false
OFFLINE_PACKAGE_STORAGE_PATH=/data/packages

# 从节点配置（离线运行）
MODE=slave
ENABLE_RULE_SYNC=false
OFFLINE_PACKAGE_STORAGE_PATH=/data/packages
```

### 步骤3：启动服务

**在线同步模式**：
```bash
# 启动主节点
docker-compose -f docker-compose.master.yml up -d

# 启动从节点
docker-compose -f docker-compose.slave.yml up -d
```

**离线模式**：
```bash
# 启动主节点（用于生成包）
docker-compose -f docker-compose.offline-master.yml up -d

# 启动从节点（离线运行）
docker-compose -f docker-compose.offline-slave.yml up -d
```

### 步骤4：验证部署

**检查服务状态**：
```bash
# 检查容器状态
docker-compose ps

# 检查服务健康状态
curl http://localhost:18001/health
curl http://localhost:18002/health
```

**测试在线同步**：
```bash
# 查看同步状态
curl -H "X-API-KEY: demo_api_key_12345" \
  "http://localhost:18002/api/v1/rules/sync/status?node_id=demo_slave"

# 手动触发同步
curl -X POST -H "X-API-KEY: demo_api_key_12345" \
  -H "Content-Type: application/json" \
  -d '{"node_id":"demo_slave","sync_mode":"full"}' \
  http://localhost:18001/api/v1/rules/sync/request
```

**测试离线包功能**：
```bash
# 生成离线包
curl -X POST -H "X-API-KEY: demo_api_key_12345" \
  -H "Content-Type: application/json" \
  -d '{
    "package_name": "demo_package",
    "compression_level": 6,
    "expiry_days": 30
  }' \
  http://localhost:18001/api/v1/rules/offline/generate

# 查看生成的包
curl -H "X-API-KEY: demo_api_key_12345" \
  http://localhost:18001/api/v1/rules/offline/packages
```

## 📋 常用操作

### 在线同步操作

```bash
# 查看同步状态
curl -H "X-API-KEY: your_key" \
  "http://slave-ip:18002/api/v1/rules/sync/status?node_id=your_node"

# 查看缓存统计
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/cache/stats

# 清理缓存
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"cache_types": ["version", "changes"]}' \
  http://slave-ip:18002/api/v1/rules/sync/cache/clear
```

### 离线包操作

```bash
# 生成离线包
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{
    "package_name": "production_rules_v1.0",
    "compression_level": 9,
    "expiry_days": 90,
    "description": "生产环境规则包"
  }' \
  http://master-ip:18001/api/v1/rules/offline/generate

# 下载离线包
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/download/package_id \
  -o rules_package.gz

# 查看存储统计
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/storage/stats

# 清理过期包
curl -X POST -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/cleanup
```

## 🔧 配置调优

### 性能优化配置

```bash
# 高性能配置
SYNC_CACHE_SIZE_MB=500
SYNC_COMPRESSION_LEVEL=9
CACHE_VERSION_MAX_SIZE=50000
CACHE_CHANGES_MAX_SIZE=10000

# 内存优化配置
SYNC_CACHE_SIZE_MB=100
CACHE_VERSION_MAX_MEMORY_MB=50
CACHE_CHANGES_MAX_MEMORY_MB=200
```

### 网络优化配置

```bash
# 网络不稳定环境
SYNC_AUTO_RECOVERY_ENABLED=true
SYNC_NETWORK_PARTITION_THRESHOLD=5
RULE_SYNC_MAX_RETRIES=5
RULE_SYNC_RETRY_INTERVAL=60.0
```

### 存储优化配置

```bash
# 存储空间限制环境
OFFLINE_PACKAGE_MAX_SIZE_MB=200
OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS=15
OFFLINE_PACKAGE_CLEANUP_INTERVAL=1800
```

## 🚨 常见问题

### Q1: 同步失败怎么办？

**检查步骤**：
1. 验证网络连通性：`ping master-ip`
2. 检查API密钥配置
3. 查看同步日志：`docker logs container-name | grep sync`
4. 强制全量同步

**解决方案**：
```bash
# 强制全量同步
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{
    "node_id": "your_node",
    "sync_mode": "full",
    "force_full_sync": true
  }' \
  http://master-ip:18001/api/v1/rules/sync/request
```

### Q2: 离线包生成失败？

**检查步骤**：
1. 检查存储空间：`df -h`
2. 检查权限：`ls -la /data/packages`
3. 查看生成日志

**解决方案**：
```bash
# 清理存储空间
curl -X POST -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/cleanup

# 检查存储统计
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/storage/stats
```

### Q3: 缓存命中率低？

**检查步骤**：
1. 查看缓存统计
2. 检查缓存配置
3. 分析访问模式

**解决方案**：
```bash
# 增加缓存大小
CACHE_VERSION_MAX_SIZE=20000
CACHE_VERSION_MAX_MEMORY_MB=100

# 延长缓存TTL
SYNC_VERSION_CACHE_TTL=172800  # 2天
```

## 📚 进阶学习

### 推荐阅读顺序

1. **基础概念**: [双模式同步部署指南](../operations/deployment/dual_mode_sync_deployment_guide.md)
2. **API参考**: [双模式同步API参考](../api/dual_mode_sync_api_reference.md)
3. **配置详解**: [同步配置指南](../configuration/sync_configuration_guide.md)
4. **运维指南**: [双模式同步运维手册](../operations/maintenance/dual_mode_sync_maintenance_guide.md)

### 实践建议

1. **开发环境**: 使用在线同步模式，便于调试和测试
2. **测试环境**: 混合模式，测试两种同步方式
3. **生产环境**: 根据网络环境选择合适的模式
4. **离线环境**: 使用离线包模式，定期更新规则数据

### 监控建议

1. **设置监控脚本**: 定期检查同步状态和缓存性能
2. **配置告警**: 同步失败率超过5%时告警
3. **性能监控**: 监控缓存命中率和响应时间
4. **存储监控**: 监控离线包存储使用率

## 🆘 获取帮助

- **文档**: 查看完整的部署和API文档
- **日志**: 检查应用日志获取详细错误信息
- **社区**: 提交Issue或参与讨论
- **支持**: 联系技术支持团队

---

**快速开始指南版本**: v1.0  
**更新时间**: 2025-08-02  
**适用版本**: 双模式同步机制 v1.0+
