/**
 * TypeScript类型定义使用示例
 * 展示如何使用基于三表结构重构后的类型定义
 *
 * 此文件仅用于演示和验证，不会被打包到生产环境
 */

// 导入需要的类型定义
import type {
  RuleTemplate,
  RuleFieldMetadata,
  CreateRuleTemplateData
} from './database-models'

import {
  RuleTemplateStatus,
  FieldDataType,
  getRuleTemplateStatusChineseName,
  getFieldDataTypeChineseName
} from './database-models'

import type {
  RuleDetail,
  CreateRuleDetailData,
  RuleDetailsQueryParams,
  RuleDetailsListResponse,
  RuleDetailWithRelationsResponse,
  BatchOperationResult,
  ApiResponse
} from './ruleDetails'

import {
  RuleDetailStatus,
  BatchOperationType,
  SortOrder
} from './ruleDetails'

import type {
  CommonFields,
  SpecificFields
} from './generated-fields'

import {
  getFieldChineseName,
  isFieldRequired,
  getRuleTypeChineseName
} from './generated-fields'

// ==================== 使用示例 ====================

/**
 * 示例1: 创建规则模板
 */
function createRuleTemplateExample(): CreateRuleTemplateData {
  return {
    rule_key: 'drug_limit_adult_and_diag_exact',
    rule_type: '药品限适应症',
    name: '药品限适应症+年龄（精确匹配诊断代码）',
    description: '限制特定年龄段患者使用特定药品，需要精确匹配诊断代码',
    status: RuleTemplateStatus.NEW
  }
}

/**
 * 示例2: 创建规则明细
 */
function createRuleDetailExample(): CreateRuleDetailData {
  return {
    rule_key: 'drug_limit_adult_and_diag_exact',
    rule_name: '阿司匹林限成人使用',
    level1: '用药安全',
    level2: '年龄限制',
    level3: '成人用药',
    error_reason: '该药品仅限成人使用，儿童禁用',
    degree: '严重',
    reference: '《药品说明书》第三条禁忌症',
    detail_position: '处方明细',
    prompted_fields1: 'drug_code',
    type: '药品限制',
    pos: '门诊',
    applicableArea: '全国',
    default_use: '是',
    start_date: '2024-01-01',
    end_date: '2024-12-31',
    yb_code: ['A01AA01'],

    // 扩展字段
    extended_fields: {
      age_threshold: 18,
      limit_days: 30
    },

    status: RuleDetailStatus.ACTIVE,
    created_by: 'admin'
  }
}

/**
 * 示例3: 字段元数据定义
 */
function createFieldMetadataExample(): RuleFieldMetadata {
  return {
    id: 1,
    rule_key: 'drug_limit_adult_and_diag_exact',
    field_name: 'age_threshold',
    field_type: FieldDataType.INTEGER,
    is_required: true,
    is_fixed_field: false,
    display_name: '年龄阈值',
    description: '限制使用的最小年龄',
    validation_rule: JSON.stringify({
      min: 0,
      max: 150,
      required: true
    }),
    default_value: '18',
    excel_column_order: 26,
    created_at: '2024-01-01T00:00:00Z'
  }
}

/**
 * 示例4: API查询参数
 */
function createQueryParamsExample(): RuleDetailsQueryParams {
  return {
    page: 1,
    page_size: 20,
    status: RuleDetailStatus.ACTIVE,
    search: '阿司匹林',
    level1: '用药安全',
    type: '药品限制',
    sort_by: 'created_at',
    sort_order: SortOrder.DESC
  }
}

/**
 * 示例5: 使用字段映射工具函数
 */
function useFieldMappingExample() {
  // 获取字段中文名称
  const chineseName = getFieldChineseName('level1') // "一级错误类型"

  // 检查字段是否必填
  const isRequired = isFieldRequired('rule_name') // true

  // 获取规则类型中文名称
  const ruleTypeName = getRuleTypeChineseName('drug_limit_adult_and_diag_exact')

  // 获取模板状态中文名称
  const statusName = getRuleTemplateStatusChineseName(RuleTemplateStatus.READY)

  // 获取字段数据类型中文名称
  const typeName = getFieldDataTypeChineseName(FieldDataType.INTEGER)

  return {
    chineseName,
    isRequired,
    ruleTypeName,
    statusName,
    typeName
  }
}

/**
 * 示例6: 处理API响应
 */
function handleApiResponseExample(response: ApiResponse<RuleDetailsListResponse>) {
  if (response.success) {
    const { items, pagination } = response.data

    // 处理规则明细列表
    items.forEach((rule: RuleDetail) => {
      console.log(`规则: ${rule.rule_name}`)
      console.log(`状态: ${rule.status}`)
      console.log(`模板: ${rule.rule_key}`)

      // 处理扩展字段
      if (rule.extended_fields) {
        Object.entries(rule.extended_fields).forEach(([key, value]) => {
          const chineseName = getFieldChineseName(key)
          console.log(`${chineseName}: ${value}`)
        })
      }
    })

    // 处理分页信息
    console.log(`总数: ${pagination.total}`)
    console.log(`当前页: ${pagination.page}`)
    console.log(`页大小: ${pagination.pageSize}`)
  } else {
    console.error('API请求失败:', response.message)
  }
}

/**
 * 示例7: 批量操作
 */
function batchOperationExample(): BatchOperationResult {
  return {
    successCount: 5,
    failedCount: 1,
    errors: [
      {
        id: 123,
        operation: BatchOperationType.UPDATE,
        error: '字段验证失败',
        details: {
          field: 'level1',
          message: '一级错误类型不能为空'
        }
      }
    ],
    updatedDetails: []
  }
}

/**
 * 示例8: 关联查询结果
 */
function handleRelationQueryExample(response: RuleDetailWithRelationsResponse) {
  const { detail, template, field_metadata } = response

  console.log(`规则明细: ${detail.rule_name}`)
  console.log(`关联模板: ${template.name}`)
  console.log(`字段数量: ${field_metadata.length}`)

  // 处理字段元数据
  field_metadata.forEach((field) => {
    console.log(`字段: ${field.display_name} (${field.field_name})`)
    console.log(`类型: ${getFieldDataTypeChineseName(field.field_type)}`)
    console.log(`必填: ${field.is_required ? '是' : '否'}`)
  })
}

/**
 * 示例9: 类型守卫函数
 */
function isRuleDetail(obj: any): obj is RuleDetail {
  return obj &&
         typeof obj.id === 'number' &&
         typeof obj.rule_key === 'string' &&
         typeof obj.rule_name === 'string' &&
         Object.values(RuleDetailStatus).includes(obj.status)
}

function isRuleTemplate(obj: any): obj is RuleTemplate {
  return obj &&
         typeof obj.id === 'number' &&
         typeof obj.rule_key === 'string' &&
         typeof obj.name === 'string' &&
         Object.values(RuleTemplateStatus).includes(obj.status)
}

/**
 * 示例10: 泛型使用
 */
function createApiResponse<T>(data: T, success: boolean = true, message: string = 'success'): ApiResponse<T> {
  return {
    code: success ? 200 : 400,
    success,
    message,
    data
  }
}

// 导出示例函数（仅用于演示）
export {
  createRuleTemplateExample,
  createRuleDetailExample,
  createFieldMetadataExample,
  createQueryParamsExample,
  useFieldMappingExample,
  handleApiResponseExample,
  batchOperationExample,
  handleRelationQueryExample,
  isRuleDetail,
  isRuleTemplate,
  createApiResponse
}

// ==================== 类型验证 ====================

// 编译时类型检查
const _typeCheck = {
  // 验证CommonFields接口
  commonFields: {} as CommonFields,

  // 验证SpecificFields接口
  specificFields: {} as SpecificFields,

  // 验证RuleDetail接口
  ruleDetail: {} as RuleDetail,

  // 验证RuleTemplate接口
  ruleTemplate: {} as RuleTemplate,

  // 验证RuleFieldMetadata接口
  fieldMetadata: {} as RuleFieldMetadata,

  // 验证API响应接口
  apiResponse: {} as ApiResponse<RuleDetailsListResponse>
}

// 防止未使用变量警告
void _typeCheck
