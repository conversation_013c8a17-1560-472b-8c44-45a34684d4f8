server {
    listen 18099;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html index.htm;

    # 安全头配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;
    
    # CSP配置（根据实际需求调整）
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'self';" always;

    # 隐藏nginx版本
    server_tokens off;

    # 静态资源缓存策略
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|webp|avif)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # 预压缩文件支持
        location ~* \.(js|css)$ {
            gzip_static on;
            # brotli_static on;
        }
        
        try_files $uri =404;
    }

    # 字体文件缓存
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        try_files $uri =404;
    }

    # HTML文件缓存策略
    location ~* \.(html|htm)$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        try_files $uri $uri/ /index.html;
    }

    # JSON和XML文件
    location ~* \.(json|xml)$ {
        expires 1h;
        add_header Cache-Control "public";
        try_files $uri =404;
    }

    # API代理配置（如果需要）
    location /api/ {
        # 移除/api前缀并代理到后端
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass http://localhost:18001;
        
        # 代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 30s;
    }

    # WebSocket支持（如果需要）
    location /ws/ {
        proxy_pass http://localhost:18001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket特定设置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
        
        # 防止缓存index.html
        location = /index.html {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 状态检查端点
    location /status {
        access_log off;
        return 200 '{"status":"ok","timestamp":"$time_iso8601"}';
        add_header Content-Type application/json;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问配置文件
    location ~* \.(conf|config|ini|log|bak|backup|old|tmp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # robots.txt
    location = /robots.txt {
        allow all;
        log_not_found off;
        access_log off;
        try_files $uri =404;
    }

    # favicon.ico
    location = /favicon.ico {
        log_not_found off;
        access_log off;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        internal;
        try_files /index.html =404;
    }
    
    location = /50x.html {
        internal;
        try_files /index.html =500;
    }

    # 限制请求方法
    if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE|OPTIONS)$) {
        return 405;
    }

    # 限制文件上传大小
    client_max_body_size 20M;
    client_body_buffer_size 128k;

    # 防止缓冲区溢出
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
}
