# 数据库配置优化指南

## 概述

本指南介绍了新的数据库配置方式，解决了密码特殊字符问题，并提供了可选的自动建库功能。

## 🎯 主要改进

### 1. 配置方式优化
- **新增独立参数配置方式**：解决密码特殊字符问题
- **保持向后兼容性**：仍支持传统的DATABASE_URL方式
- **优先级机制**：独立参数优先级高于DATABASE_URL

### 2. 自动建库功能
- **可选功能**：通过`AUTO_CREATE_DATABASE`控制
- **安全设计**：生产环境默认禁用
- **权限分离**：支持管理员账户和应用账户分离

## 📋 配置方式对比

### 方式1：独立参数（推荐）

```bash
# 数据库连接参数
DB_HOST=localhost
DB_PORT=3306
DB_USER=rule_user
DB_PASSWORD=complex_pass@word#123  # 支持特殊字符
DB_NAME=rule_management
DB_DRIVER=pymysql

# 自动建库配置（可选）
AUTO_CREATE_DATABASE=true
DB_ADMIN_USER=root
DB_ADMIN_PASSWORD=admin_password
```

**优点：**
- ✅ 支持密码中的特殊字符（@、:、#等）
- ✅ 配置清晰，易于理解
- ✅ 便于单独修改某个参数

### 方式2：DATABASE_URL（向后兼容）

```bash
# 传统URL方式
DATABASE_URL=mysql+pymysql://user:password@host:3306/database
```

**注意：**
- ⚠️ 密码包含特殊字符需要URL编码
- ⚠️ 配置相对复杂

## 🚀 部署指南

### 开发环境部署

1. **复制配置文件**
```bash
cp .env.development.example .env
```

2. **修改数据库配置**
```bash
# 编辑 .env 文件
DB_HOST=localhost
DB_USER=dev_user
DB_PASSWORD=your_dev_password
DB_NAME=rule_dev

# 启用自动建库（开发环境推荐）
AUTO_CREATE_DATABASE=true
DB_ADMIN_USER=root
DB_ADMIN_PASSWORD=root_password
```

3. **检查配置**
```bash
python tools/check_database.py
```

4. **启动应用**
```bash
python master.py
```

### 生产环境部署

1. **手动创建数据库**
```sql
-- 连接到MySQL服务器
mysql -u root -p -h prod-db-server

-- 创建数据库
CREATE DATABASE rule_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建应用用户
CREATE USER 'rule_prod_user'@'%' IDENTIFIED BY 'complex_prod_pass@word#123';
GRANT ALL PRIVILEGES ON rule_production.* TO 'rule_prod_user'@'%';
FLUSH PRIVILEGES;
```

2. **配置环境变量**
```bash
# 生产环境配置
DB_HOST=prod-db-server.company.com
DB_USER=rule_prod_user
DB_PASSWORD=complex_prod_pass@word#123
DB_NAME=rule_production

# 生产环境禁用自动建库
AUTO_CREATE_DATABASE=false
```

3. **验证配置**
```bash
python tools/check_database.py
```

4. **运行数据库迁移**
```bash
alembic upgrade head
```

5. **启动应用**
```bash
docker-compose -f docker-compose.master.yml up -d
```

## 🔧 配置参数说明

### 基础数据库配置

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `DB_HOST` | 数据库主机地址 | - | `localhost` |
| `DB_PORT` | 数据库端口 | `3306` | `3306` |
| `DB_USER` | 数据库用户名 | - | `rule_user` |
| `DB_PASSWORD` | 数据库密码 | - | `pass@word#123` |
| `DB_NAME` | 数据库名称 | - | `rule_management` |
| `DB_DRIVER` | 数据库驱动 | `pymysql` | `pymysql` |

### 自动建库配置

| 参数 | 说明 | 默认值 | 推荐设置 |
|------|------|--------|----------|
| `AUTO_CREATE_DATABASE` | 是否启用自动建库 | `false` | 开发:`true`, 生产:`false` |
| `DB_ADMIN_USER` | 管理员用户名 | - | `root` |
| `DB_ADMIN_PASSWORD` | 管理员密码 | - | 管理员密码 |

### 连接增强配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `DB_CONNECT_TIMEOUT` | 连接超时（秒） | `30` |
| `DB_CHECK_ON_STARTUP` | 启动时检查连接 | `true` |

## 🛠️ 故障排除

### 常见问题

1. **密码包含特殊字符导致连接失败**
   - 解决方案：使用独立参数配置方式
   - 或者：对DATABASE_URL中的密码进行URL编码

2. **数据库不存在错误**
   - 开发环境：启用`AUTO_CREATE_DATABASE=true`
   - 生产环境：手动创建数据库

3. **权限不足错误**
   - 检查数据库用户权限
   - 自动建库需要CREATE权限

### 检查工具

使用内置的数据库检查工具：
```bash
python tools/check_database.py
```

输出示例：
```
============================================================
数据库连接检查工具
============================================================

1. 数据库配置信息:
----------------------------------------
  config_method: independent_params
  host: localhost
  port: 3306
  database: rule_dev
  user: dev_user
  driver: pymysql
  auto_create_enabled: True

2. 配置验证:
----------------------------------------
  ✅ 配置验证通过

3. 数据库连接测试:
----------------------------------------
  ✅ 数据库连接成功: 数据库连接正常

4. 部署建议:
----------------------------------------
  ✅ 开发/测试环境已启用自动建库
  ✅ 使用独立参数配置方式（推荐）
```

## 📚 迁移指南

### 从DATABASE_URL迁移到独立参数

1. **解析现有DATABASE_URL**
```bash
# 原配置
DATABASE_URL=mysql+pymysql://user:pass@word@host:3306/dbname

# 新配置
DB_HOST=host
DB_PORT=3306
DB_USER=user
DB_PASSWORD=pass@word  # 无需编码
DB_NAME=dbname
DB_DRIVER=pymysql
```

2. **测试新配置**
```bash
python tools/check_database.py
```

3. **移除旧配置**
```bash
# 注释或删除DATABASE_URL
# DATABASE_URL=mysql+pymysql://user:pass@word@host:3306/dbname
```

## 🔒 安全建议

1. **生产环境**
   - 禁用`AUTO_CREATE_DATABASE`
   - 使用专用数据库用户，避免使用root
   - 定期轮换数据库密码

2. **开发环境**
   - 可以启用`AUTO_CREATE_DATABASE`简化开发
   - 使用不同的数据库名称区分环境

3. **密码管理**
   - 使用强密码
   - 避免在日志中记录密码
   - 考虑使用密钥管理服务
