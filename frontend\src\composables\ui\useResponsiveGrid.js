/**
 * 响应式网格布局Composable
 * 提供统一的CSS Grid响应式布局管理，消除重复代码
 */
import { computed, ref } from 'vue'

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
  columns: {
    xl: 3,    // ≥1200px: 3列（用户要求最多3列）
    lg: 3,    // 768-1199px: 3列
    md: 2,    // 576-767px: 2列
    sm: 1     // <576px: 1列
  },
  gap: {
    xl: '20px',
    lg: '18px',
    md: '16px',
    sm: '12px'
  },
  minItemWidth: '300px',
  breakpoints: {
    sm: 576,
    md: 768,
    lg: 1200,
    xl: 1200
  }
}

/**
 * 响应式网格布局功能
 * @param {Object} options - 配置选项
 * @param {Object} options.columns - 各断点下的列数配置
 * @param {Object} options.gap - 各断点下的间距配置
 * @param {string} options.minItemWidth - 网格项最小宽度
 * @param {Object} options.breakpoints - 断点配置
 * @returns {Object} 响应式网格相关的状态和方法
 */
export function useResponsiveGrid(options = {}) {
  // 合并配置
  const config = {
    columns: { ...DEFAULT_CONFIG.columns, ...options.columns },
    gap: { ...DEFAULT_CONFIG.gap, ...options.gap },
    minItemWidth: options.minItemWidth || DEFAULT_CONFIG.minItemWidth,
    breakpoints: { ...DEFAULT_CONFIG.breakpoints, ...options.breakpoints }
  }

  // 当前屏幕宽度
  const screenWidth = ref(window.innerWidth)

  // 当前设备类型
  const deviceType = computed(() => {
    const width = screenWidth.value
    if (width < config.breakpoints.sm) return 'xs'
    if (width < config.breakpoints.md) return 'sm'
    if (width < config.breakpoints.lg) return 'md'
    return 'xl'
  })

  // 当前列数
  const currentColumns = computed(() => {
    const device = deviceType.value
    return config.columns[device] || config.columns.xl
  })

  // 当前间距
  const currentGap = computed(() => {
    const device = deviceType.value
    return config.gap[device] || config.gap.xl
  })

  // 基础网格样式
  const gridStyles = computed(() => ({
    display: 'grid',
    gap: currentGap.value,
    gridTemplateColumns: `repeat(auto-fit, minmax(${config.minItemWidth}, 1fr))`
  }))

  // 响应式CSS类名
  const responsiveClasses = computed(() => [
    'responsive-grid',
    `responsive-grid--${deviceType.value}`,
    `responsive-grid--cols-${currentColumns.value}`
  ])

  // 生成CSS媒体查询样式
  const generateMediaQueries = () => {
    const queries = []

    // 大屏幕样式
    queries.push(`
      .responsive-grid {
        grid-template-columns: repeat(${config.columns.xl}, 1fr) !important;
        gap: ${config.gap.xl} !important;
      }
    `)

    // 中等屏幕样式
    queries.push(`
      @media (max-width: ${config.breakpoints.lg - 1}px) and (min-width: ${config.breakpoints.md}px) {
        .responsive-grid {
          grid-template-columns: repeat(${config.columns.lg}, 1fr) !important;
          gap: ${config.gap.lg} !important;
        }
      }
    `)

    // 小屏幕样式
    queries.push(`
      @media (max-width: ${config.breakpoints.md - 1}px) and (min-width: ${config.breakpoints.sm}px) {
        .responsive-grid {
          grid-template-columns: repeat(${config.columns.md}, 1fr) !important;
          gap: ${config.gap.md} !important;
        }
      }
    `)

    // 超小屏幕样式
    queries.push(`
      @media (max-width: ${config.breakpoints.sm - 1}px) {
        .responsive-grid {
          grid-template-columns: repeat(${config.columns.sm}, 1fr) !important;
          gap: ${config.gap.sm} !important;
        }
      }
    `)

    return queries.join('\n')
  }

  // 监听窗口大小变化
  const handleResize = () => {
    screenWidth.value = window.innerWidth
  }

  // 初始化
  const init = () => {
    window.addEventListener('resize', handleResize)
  }

  // 清理
  const cleanup = () => {
    window.removeEventListener('resize', handleResize)
  }

  // 获取网格信息
  const getGridInfo = () => ({
    deviceType: deviceType.value,
    columns: currentColumns.value,
    gap: currentGap.value,
    screenWidth: screenWidth.value
  })

  return {
    // 响应式数据
    screenWidth,
    deviceType,
    currentColumns,
    currentGap,
    gridStyles,
    responsiveClasses,

    // 方法
    generateMediaQueries,
    getGridInfo,
    init,
    cleanup,

    // 配置
    config
  }
}

/**
 * 简化版响应式网格
 * 提供最基本的网格布局功能
 * @param {number} maxColumns - 最大列数
 * @param {string} gap - 间距
 * @param {string} minItemWidth - 最小项宽度
 */
export function useSimpleGrid(maxColumns = 3, gap = '20px', minItemWidth = '300px') {
  const gridStyles = computed(() => ({
    display: 'grid',
    gap,
    gridTemplateColumns: `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`,
    maxWidth: '100%'
  }))

  const responsiveClasses = computed(() => [
    'simple-grid',
    `simple-grid--max-${maxColumns}`
  ])

  return {
    gridStyles,
    responsiveClasses
  }
}

/**
 * 网格项工具函数
 * 提供网格项相关的辅助功能
 */
export function useGridItem() {
  // 网格项样式
  const itemStyles = computed(() => ({
    display: 'flex',
    flexDirection: 'column',
    minHeight: '0'
  }))

  // 网格项类名
  const itemClasses = computed(() => [
    'grid-item'
  ])

  return {
    itemStyles,
    itemClasses
  }
}
