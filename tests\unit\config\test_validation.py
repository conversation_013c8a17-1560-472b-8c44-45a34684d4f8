"""
规则注册配置验证功能测试

测试配置验证器和健康检查器的各种场景，
确保配置验证机制的正确性和可靠性。
"""

from unittest.mock import AsyncMock, MagicMock, patch

import httpx
import pytest

from config.validation import (
    RuleRegistrationConfigValidator,
    RuleRegistration<PERSON>eal<PERSON><PERSON><PERSON><PERSON>,
    check_rule_registration_health,
    validate_rule_registration_config,
)


class TestRuleRegistrationConfigValidator:
    """规则注册配置验证器测试"""

    def test_valid_config(self):
        """测试有效配置"""
        with patch('config.validation.get_settings') as mock_settings:
            # 模拟有效配置
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="http://localhost:6060",
                RULE_REGISTRATION_TIMEOUT=30.0,
                RULE_REGISTRATION_MAX_RETRIES=3,
                RULE_REGISTRATION_BATCH_SIZE=100,
                RULE_REGISTRATION_QUEUE_MAX_SIZE=1000,
                RULE_REGISTRATION_TASK_TIMEOUT=300.0,
                REGISTRATION_WORKER_COUNT=2
            )

            validator = RuleRegistrationConfigValidator()
            is_valid, errors, warnings = validator.validate_all()

            assert is_valid is True
            assert len(errors) == 0

    def test_invalid_host_config(self):
        """测试无效的主机配置"""
        with patch('config.validation.get_settings') as mock_settings:
            # 模拟无效的主机配置
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="",  # 空主机地址
                RULE_REGISTRATION_TIMEOUT=30.0,
                RULE_REGISTRATION_MAX_RETRIES=3,
                RULE_REGISTRATION_BATCH_SIZE=100,
                RULE_REGISTRATION_QUEUE_MAX_SIZE=1000,
                RULE_REGISTRATION_TASK_TIMEOUT=300.0,
                REGISTRATION_WORKER_COUNT=2
            )

            validator = RuleRegistrationConfigValidator()
            is_valid, errors, warnings = validator.validate_all()

            assert is_valid is False
            assert any("RULE_REGISTRATION_HOST 不能为空" in error for error in errors)

    def test_invalid_url_format(self):
        """测试无效的URL格式"""
        with patch('config.validation.get_settings') as mock_settings:
            # 模拟无效的URL格式
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="invalid-url",  # 无效URL
                RULE_REGISTRATION_TIMEOUT=30.0,
                RULE_REGISTRATION_MAX_RETRIES=3,
                RULE_REGISTRATION_BATCH_SIZE=100,
                RULE_REGISTRATION_QUEUE_MAX_SIZE=1000,
                RULE_REGISTRATION_TASK_TIMEOUT=300.0,
                REGISTRATION_WORKER_COUNT=2
            )

            validator = RuleRegistrationConfigValidator()
            is_valid, errors, warnings = validator.validate_all()

            assert is_valid is False
            assert any("URL格式无效" in error for error in errors)

    def test_negative_values(self):
        """测试负数值配置"""
        with patch('config.validation.get_settings') as mock_settings:
            # 模拟负数配置
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="http://localhost:6060",
                RULE_REGISTRATION_TIMEOUT=-1.0,  # 负数超时
                RULE_REGISTRATION_MAX_RETRIES=-1,  # 负数重试
                RULE_REGISTRATION_BATCH_SIZE=0,  # 零批次大小
                RULE_REGISTRATION_QUEUE_MAX_SIZE=-1,  # 负数队列大小
                RULE_REGISTRATION_TASK_TIMEOUT=0,  # 零任务超时
                REGISTRATION_WORKER_COUNT=0  # 零Worker数量
            )

            validator = RuleRegistrationConfigValidator()
            is_valid, errors, warnings = validator.validate_all()

            assert is_valid is False
            assert len(errors) >= 5  # 应该有多个错误

    def test_warning_conditions(self):
        """测试警告条件"""
        with patch('config.validation.get_settings') as mock_settings:
            # 模拟会产生警告的配置
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="http://localhost:6060",
                RULE_REGISTRATION_TIMEOUT=400.0,  # 过高的超时
                RULE_REGISTRATION_MAX_RETRIES=15,  # 过高的重试次数
                RULE_REGISTRATION_BATCH_SIZE=2000,  # 过大的批次
                RULE_REGISTRATION_QUEUE_MAX_SIZE=1000,
                RULE_REGISTRATION_TASK_TIMEOUT=300.0,
                REGISTRATION_WORKER_COUNT=10  # 过多的Worker
            )

            validator = RuleRegistrationConfigValidator()
            is_valid, errors, warnings = validator.validate_all()

            assert is_valid is True  # 配置有效但有警告
            assert len(warnings) >= 3  # 应该有多个警告

    def test_timeout_relationship_warning(self):
        """测试超时配置关系警告"""
        with patch('config.validation.get_settings') as mock_settings:
            # 任务超时小于网络超时
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="http://localhost:6060",
                RULE_REGISTRATION_TIMEOUT=60.0,
                RULE_REGISTRATION_MAX_RETRIES=3,
                RULE_REGISTRATION_BATCH_SIZE=100,
                RULE_REGISTRATION_QUEUE_MAX_SIZE=1000,
                RULE_REGISTRATION_TASK_TIMEOUT=30.0,  # 小于网络超时
                REGISTRATION_WORKER_COUNT=2
            )

            validator = RuleRegistrationConfigValidator()
            is_valid, errors, warnings = validator.validate_all()

            assert is_valid is True
            assert any("RULE_REGISTRATION_TASK_TIMEOUT 应该大于" in warning for warning in warnings)


class TestRuleRegistrationHealthChecker:
    """规则注册健康检查器测试"""

    @pytest.mark.asyncio
    async def test_service_disabled(self):
        """测试服务禁用时的健康检查"""
        with patch('config.validation.get_settings') as mock_settings:
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=False
            )

            checker = RuleRegistrationHealthChecker()
            result = await checker.check_registration_service_health()

            assert result["healthy"] is True
            assert "禁用" in result["message"]

    @pytest.mark.asyncio
    async def test_service_healthy(self):
        """测试服务健康的情况"""
        with patch('config.validation.get_settings') as mock_settings:
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="http://localhost:6060",
                RULE_REGISTRATION_TIMEOUT=30.0
            )

            # 模拟成功的HTTP响应
            mock_response = MagicMock()
            mock_response.status_code = 200

            with patch('httpx.AsyncClient') as mock_client:
                mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)

                checker = RuleRegistrationHealthChecker()
                result = await checker.check_registration_service_health()

                assert result["healthy"] is True
                assert result["response_time"] is not None

    @pytest.mark.asyncio
    async def test_service_connection_error(self):
        """测试服务连接错误"""
        with patch('config.validation.get_settings') as mock_settings:
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="http://localhost:6060",
                RULE_REGISTRATION_TIMEOUT=30.0
            )

            # 模拟连接错误
            with patch('httpx.AsyncClient') as mock_client:
                mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                    side_effect=httpx.ConnectError("Connection failed")
                )

                checker = RuleRegistrationHealthChecker()
                result = await checker.check_registration_service_health()

                assert result["healthy"] is False
                assert "无法连接" in result["error"]

    @pytest.mark.asyncio
    async def test_service_timeout(self):
        """测试服务超时"""
        with patch('config.validation.get_settings') as mock_settings:
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="http://localhost:6060",
                RULE_REGISTRATION_TIMEOUT=30.0
            )

            # 模拟超时错误
            with patch('httpx.AsyncClient') as mock_client:
                mock_client.return_value.__aenter__.return_value.get = AsyncMock(
                    side_effect=httpx.TimeoutException("Request timeout")
                )

                checker = RuleRegistrationHealthChecker()
                result = await checker.check_registration_service_health()

                assert result["healthy"] is False
                assert "超时" in result["error"]

    @pytest.mark.asyncio
    async def test_service_error_status(self):
        """测试服务返回错误状态码"""
        with patch('config.validation.get_settings') as mock_settings:
            mock_settings.return_value = MagicMock(
                RULE_REGISTRATION_ENABLED=True,
                RULE_REGISTRATION_HOST="http://localhost:6060",
                RULE_REGISTRATION_TIMEOUT=30.0
            )

            # 模拟错误状态码
            mock_response = MagicMock()
            mock_response.status_code = 500
            mock_response.text = "Internal Server Error"

            with patch('httpx.AsyncClient') as mock_client:
                mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)

                checker = RuleRegistrationHealthChecker()
                result = await checker.check_registration_service_health()

                assert result["healthy"] is False
                assert "状态码" in result["error"]


class TestConvenienceFunctions:
    """便捷函数测试"""

    def test_validate_rule_registration_config(self):
        """测试配置验证便捷函数"""
        with patch('config.validation.RuleRegistrationConfigValidator') as mock_validator_class:
            mock_validator = MagicMock()
            mock_validator.validate_all.return_value = (True, [], [])
            mock_validator_class.return_value = mock_validator

            is_valid, errors, warnings = validate_rule_registration_config()

            assert is_valid is True
            assert errors == []
            assert warnings == []
            mock_validator.validate_all.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_rule_registration_health(self):
        """测试健康检查便捷函数"""
        with patch('config.validation.RuleRegistrationHealthChecker') as mock_checker_class:
            mock_checker = MagicMock()
            mock_checker.check_configuration_health = AsyncMock(return_value={"healthy": True})
            mock_checker.check_registration_service_health = AsyncMock(return_value={"healthy": True})
            mock_checker_class.return_value = mock_checker

            result = await check_rule_registration_health()

            assert "overall_healthy" in result
            assert "configuration" in result
            assert "service" in result
            assert "timestamp" in result


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
