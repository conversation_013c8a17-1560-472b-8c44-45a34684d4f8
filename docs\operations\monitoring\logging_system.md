# 日志系统文档

## 概述

本项目使用 `loguru` 库实现了一个强大的日志系统。该系统的主要目的是提供一种灵活、可配置且易于使用的方式来记录应用程序的运行情况，帮助开发和运维人员追踪问题、监控系统状态。`loguru` 以其简洁的 API 和丰富的功能（如自动文件轮转、压缩、多进程安全等）而被选中。

核心日志逻辑封装在 [`logging_system.py`](../logging_system.py:1) 中的 `LoggingSystem` 类。

## 配置

日志系统的行为通过位于 [`core/logging/logging_config.py`](../core/logging/logging_config.py:1) 的配置文件进行管理。以下是一些关键的配置选项及其说明：

*   **日志级别 (Log Level):** 控制记录哪些级别的日志消息（例如 `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`）。
*   **日志文件路径 (Log File Path):** 指定日志文件的存储目录。
*   **日志文件名格式 (Log File Name Format):** 定义日志文件的命名规则，通常包含日期和时间戳，便于识别。
*   **日志轮转 (Log Rotation):**
    *   **基于大小 (Size-based):** 当日志文件达到指定大小时，会自动创建新的日志文件。
    *   **基于时间 (Time-based):** 定期（例如每天、每周）创建新的日志文件。
*   **日志保留策略 (Log Retention):** 配置旧日志文件的保留时长或数量，超出的会自动删除，以节省磁盘空间。
*   **日志压缩 (Log Compression):** 历史日志文件可以自动压缩（例如使用 `gzip` 格式）以减少存储占用。
*   **多进程/多线程安全 (Multiprocess/Multithread Safety):** 通过在 `loguru` 中设置 `enqueue=True` 来确保在并发环境下的日志记录安全。

具体的配置示例可以在 [`core/logging/logging_config.py`](../core/logging/logging_config.py:1) 文件中找到。

## 使用方法

在项目的其他模块中，可以通过 `LoggingSystem` 获取预配置的 `logger` 实例来进行日志记录。

1.  **初始化:**
    日志系统通常在应用程序的入口点（例如 [`main.py`](../main.py:1)）进行初始化。

    ```python
    # main.py
    from logging_system import LoggingSystem
    from core.logging.logging_config import LOGGING_SETTINGS # 假设配置在这里

    # 初始化日志系统
    logger = LoggingSystem.get_logger(**LOGGING_SETTINGS)

    logger.info("日志系统已成功初始化。")
    ```

2.  **在其他模块中使用:**
    可以直接导入并使用已经初始化和配置好的 `logger` 实例。如果 `LoggingSystem` 将 `logger` 设置为全局可访问或通过某种方式注入，则可以直接使用。

    或者，如果 `LoggingSystem` 提供了一个获取 `logger` 的方法，可以这样使用：

    ```python
    # services/some_service.py
    from logging_system import LoggingSystem # 或者从主模块获取 logger

    logger = LoggingSystem.get_logger() # 获取已配置的 logger

    class SomeService:
        def do_something(self):
            logger.info("SomeService 正在执行 do_something 操作。")
            try:
                # ... 一些操作 ...
                result = 10 / 0
            except Exception as e:
                logger.error(f"执行 do_something 时发生错误: {e}", exc_info=True)
                # exc_info=True 会记录异常堆栈信息
            logger.debug("do_something 操作完成。")
    ```

**日志级别示例:**

*   `logger.debug("这是一条调试信息，用于详细追踪。")`
*   `logger.info("这是一条普通信息，例如操作成功。")`
*   `logger.warning("这是一条警告信息，表示潜在问题。")`
*   `logger.error("这是一条错误信息，表示发生了错误。")`
*   `logger.critical("这是一条严重错误信息，可能导致系统崩溃。")`

## 主要特性

*   **文件输出:** 日志可以记录到指定路径的文件中。
*   **自动轮转:** 支持基于文件大小或时间的日志文件自动轮转，防止单个日志文件过大。
*   **历史压缩:** 旧的日志文件可以自动压缩（例如 `gzip`），节省存储空间。
*   **保留策略:** 可以配置日志文件的保留时间和数量，自动删除过期的日志。
*   **多进程/多线程安全:** `loguru` 通过 `enqueue=True` 选项确保在多进程和多线程环境下的日志记录是安全的，避免日志冲突或丢失。
*   **结构化日志:** `loguru` 支持轻松创建结构化日志，便于后续的分析和处理。
*   **简洁易用:** 相比 Python 内置的 `logging` 模块，`loguru` 的 API 更为简洁直观。

## 查看日志

*   **存储位置:** 日志文件的具体存储位置由 [`core/logging/logging_config.py`](../core/logging/logging_config.py:1) 中的 `LOG_PATH` (或类似名称的配置项) 指定。
*   **命名约定:** 日志文件名通常会包含日期和时间戳，例如 `app_2025-05-30_18-00-00.log`。压缩后的文件可能带有 `.gz` 后缀，例如 `app_2025-05-29_18-00-00.log.gz`。

请查阅 [`core/logging/logging_config.py`](../core/logging/logging_config.py:1) 以获取确切的路径和命名格式。