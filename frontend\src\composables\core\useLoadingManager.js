/**
 * 统一加载状态管理器
 * 解决加载状态泄漏和配对问题
 */

import { ref, computed, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'

/**
 * 加载任务管理器
 * 确保 addLoadingTask 和 removeLoadingTask 的配对调用
 */
export function useLoadingManager() {
  const appStore = useAppStore()
  const activeTasks = ref(new Set())
  const localLoading = ref(false)

  /**
   * 创建加载任务
   * @param {string} taskId - 任务ID
   * @param {string} message - 加载消息
   * @returns {Object} 任务控制器
   */
  const createLoadingTask = (taskId, message = '加载中...') => {
    // 防止重复创建
    if (activeTasks.value.has(taskId)) {
      console.warn(`Loading task ${taskId} already exists`)
      return getTaskController(taskId)
    }

    // 添加到活跃任务集合
    activeTasks.value.add(taskId)

    // 设置局部和全局加载状态
    localLoading.value = true
    appStore.addLoadingTask(taskId, message)

    console.debug(`[LoadingManager] Task created: ${taskId}`)

    return getTaskController(taskId)
  }

  /**
   * 获取任务控制器
   * @param {string} taskId - 任务ID
   * @returns {Object} 任务控制器
   */
  const getTaskController = (taskId) => {
    return {
      id: taskId,
      finish: () => finishTask(taskId),
      isActive: () => activeTasks.value.has(taskId)
    }
  }

  /**
   * 完成加载任务
   * @param {string} taskId - 任务ID
   */
  const finishTask = (taskId) => {
    if (!activeTasks.value.has(taskId)) {
      console.warn(`Loading task ${taskId} not found`)
      return
    }

    // 从活跃任务中移除
    activeTasks.value.delete(taskId)

    // 移除全局加载任务
    appStore.removeLoadingTask(taskId)

    // 如果没有活跃任务了，关闭局部加载
    if (activeTasks.value.size === 0) {
      localLoading.value = false
    }

    console.debug(`[LoadingManager] Task finished: ${taskId}`)
  }

  /**
   * 完成所有任务
   */
  const finishAllTasks = () => {
    const tasks = Array.from(activeTasks.value)
    tasks.forEach(taskId => finishTask(taskId))
  }

  /**
   * 检查是否有泄漏的任务
   */
  const checkForLeaks = () => {
    if (activeTasks.value.size > 0) {
      console.warn('[LoadingManager] Potential task leaks detected:', Array.from(activeTasks.value))
      return Array.from(activeTasks.value)
    }
    return []
  }

  // 计算属性
  const isLoading = computed(() => localLoading.value)
  const activeTaskCount = computed(() => activeTasks.value.size)
  const hasActiveTasks = computed(() => activeTasks.value.size > 0)

  // 组件卸载时清理所有任务
  onUnmounted(() => {
    finishAllTasks()
  })

  return {
    // 状态
    isLoading,
    activeTaskCount,
    hasActiveTasks,

    // 方法
    createLoadingTask,
    finishTask,
    finishAllTasks,
    checkForLeaks,

    // 调试
    getActiveTasks: () => Array.from(activeTasks.value)
  }
}

/**
 * 自动管理的加载任务装饰器
 * @param {Function} asyncFunction - 异步函数
 * @param {string} taskId - 任务ID
 * @param {string} message - 加载消息
 * @returns {Function} 装饰后的函数
 */
export function withLoadingTask(asyncFunction, taskId, message = '加载中...') {
  return async (...args) => {
    const loadingManager = useLoadingManager()
    const task = loadingManager.createLoadingTask(taskId, message)

    try {
      const result = await asyncFunction(...args)
      return result
    } finally {
      task.finish()
    }
  }
}
