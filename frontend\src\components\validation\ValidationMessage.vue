<template>
  <div
    v-if="visible"
    :class="messageClasses"
    class="validation-message"
  >
    <!-- 错误图标 -->
    <div class="validation-message__icon">
      <el-icon v-if="type === 'error'" class="error-icon">
        <WarningFilled />
      </el-icon>
      <el-icon v-else-if="type === 'warning'" class="warning-icon">
        <Warning />
      </el-icon>
      <el-icon v-else-if="type === 'success'" class="success-icon">
        <CircleCheckFilled />
      </el-icon>
      <el-icon v-else class="info-icon">
        <InfoFilled />
      </el-icon>
    </div>

    <!-- 消息内容 -->
    <div class="validation-message__content">
      <div class="validation-message__text">
        {{ message }}
      </div>

      <!-- 建议列表 -->
      <div
        v-if="suggestions && suggestions.length > 0"
        class="validation-message__suggestions"
      >
        <div class="suggestions-title">建议：</div>
        <ul class="suggestions-list">
          <li
            v-for="(suggestion, index) in suggestions"
            :key="index"
            class="suggestion-item"
          >
            {{ suggestion }}
          </li>
        </ul>
      </div>

      <!-- 错误详情（开发模式） -->
      <div
        v-if="showDetails && errorDetails"
        class="validation-message__details"
      >
        <el-collapse>
          <el-collapse-item title="错误详情" name="details">
            <div class="error-details">
              <div v-if="errorDetails.field_name" class="detail-item">
                <strong>字段：</strong>{{ errorDetails.field_name }}
              </div>
              <div v-if="errorDetails.error_code" class="detail-item">
                <strong>错误码：</strong>{{ errorDetails.error_code }}
              </div>
              <div v-if="errorDetails.rule_type" class="detail-item">
                <strong>规则类型：</strong>{{ errorDetails.rule_type }}
              </div>
              <div v-if="errorDetails.error_value !== undefined" class="detail-item">
                <strong>错误值：</strong>{{ JSON.stringify(errorDetails.error_value) }}
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 关闭按钮 -->
    <div
      v-if="closable"
      class="validation-message__close"
      @click="handleClose"
    >
      <el-icon><Close /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import {
  WarningFilled,
  Warning,
  CircleCheckFilled,
  InfoFilled,
  Close
} from '@element-plus/icons-vue'
import type { ValidationError } from '../../utils/validation/validationTypes'

export interface ValidationMessageProps {
  type?: 'error' | 'warning' | 'success' | 'info'
  message: string
  suggestions?: string[]
  errorDetails?: ValidationError
  closable?: boolean
  showDetails?: boolean
  visible?: boolean
  duration?: number
}

const props = withDefaults(defineProps<ValidationMessageProps>(), {
  type: 'error',
  closable: false,
  showDetails: false,
  visible: true,
  duration: 0
})

const emit = defineEmits<{
  close: []
}>()

// 内部可见状态
const internalVisible = ref(props.visible)

// 计算样式类
const messageClasses = computed(() => [
  'validation-message',
  `validation-message--${props.type}`,
  {
    'validation-message--closable': props.closable,
    'validation-message--with-details': props.showDetails && props.errorDetails
  }
])

// 实际可见状态
const visible = computed(() => props.visible && internalVisible.value)

// 处理关闭
const handleClose = () => {
  internalVisible.value = false
  emit('close')
}

// 自动关闭
if (props.duration > 0) {
  setTimeout(() => {
    handleClose()
  }, props.duration)
}
</script>

<style scoped>
.validation-message {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.3s ease;
}

.validation-message--error {
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  color: #f56c6c;
}

.validation-message--warning {
  background-color: #fdf6ec;
  border: 1px solid #f5dab1;
  color: #e6a23c;
}

.validation-message--success {
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  color: #67c23a;
}

.validation-message--info {
  background-color: #f4f4f5;
  border: 1px solid #d3d4d6;
  color: #909399;
}

.validation-message__icon {
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.error-icon {
  color: #f56c6c;
}

.warning-icon {
  color: #e6a23c;
}

.success-icon {
  color: #67c23a;
}

.info-icon {
  color: #909399;
}

.validation-message__content {
  flex: 1;
  min-width: 0;
}

.validation-message__text {
  font-weight: 500;
  margin-bottom: 4px;
}

.validation-message__suggestions {
  margin-top: 8px;
}

.suggestions-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.suggestions-list {
  margin: 0;
  padding-left: 16px;
  font-size: 12px;
}

.suggestion-item {
  margin-bottom: 2px;
  color: #606266;
}

.validation-message__details {
  margin-top: 8px;
}

.error-details {
  font-size: 12px;
  color: #909399;
}

.detail-item {
  margin-bottom: 4px;
}

.detail-item strong {
  color: #606266;
}

.validation-message__close {
  margin-left: 8px;
  margin-top: 2px;
  cursor: pointer;
  color: #c0c4cc;
  transition: color 0.3s ease;
  flex-shrink: 0;
}

.validation-message__close:hover {
  color: #909399;
}

.validation-message--closable {
  padding-right: 40px;
  position: relative;
}

.validation-message--with-details .validation-message__content {
  max-width: calc(100% - 60px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .validation-message {
    padding: 10px 12px;
    font-size: 13px;
  }

  .validation-message__icon {
    margin-right: 6px;
  }

  .suggestions-list {
    padding-left: 12px;
  }
}

/* 动画效果 */
.validation-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .validation-message--error {
    background-color: #2d1b1b;
    border-color: #5c3333;
    color: #f89898;
  }

  .validation-message--warning {
    background-color: #2d2419;
    border-color: #5c4a26;
    color: #f0c674;
  }

  .validation-message--success {
    background-color: #1b2d1b;
    border-color: #335c33;
    color: #98f898;
  }

  .validation-message--info {
    background-color: #1e1e1e;
    border-color: #404040;
    color: #b0b0b0;
  }
}
</style>
