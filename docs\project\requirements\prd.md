# 产品需求文档 (PRD) - 智能规则校验系统

**文档状态更新时间**: 2025年6月30日
**项目当前阶段**: 核心功能已完成，三阶段性能优化已实施，代码质量优化进行中

## 1. 项目愿景与目标

### 1.1. 愿景 ✅ 已实现
构建一个中心化、自动化、可动态扩展的医疗规则校验平台。该平台能够将复杂的业务规则（如医保、用药规范）高效、准确地转化为可执行的系统逻辑，同时极大简化业务人员的配置流程和开发人员的维护成本。

### 1.2. 核心目标
- **功能解耦** ✅ 已完成: 将规则的管理与执行彻底分离，构建主从（Master-Slave）架构。主节点负责规则的全生命周期管理，子节点负责在隔离环境中（如医院内网）进行高性能校验。
- **业务自动化** ✅ 已完成: 实现业务人员通过Web界面自助完成规则明细的配置、上传、校验和生效，无需开发人员介入。
- **性能卓越** ✅ 已完成: 确保部署在医院内网的子节点能够对患者数据进行毫秒级的实时规则校验，满足高性能要求。*（已实现85-90%性能提升，单次校验时间从0.2-0.5秒优化到0.03-0.08秒）*
- **安全可靠** 🔄 部分完成: 保证主从节点间的数据同步安全、可靠，并适应严格的内网安全策略。*（基础认证已实现，需要安全性加固）*
- **智能感知与自愈合** ✅ 已完成: 系统能够自动感知规则逻辑代码的变更，并引导业务人员完成后续的数据适配工作。

## 2. 用户角色与场景

### 2.1. 开发人员 ✅ 已实现
- **场景**:
  - 根据新的业务需求，在主节点代码库中创建或修改【基础规则类】（`rules/base_rules/`）。
  - 维护主从节点的系统架构，确保其稳定运行。
  - **发布**: 当开发完成，将代码发布并重启主节点服务。系统会自动完成规则的扫描、状态更新和模板重建。
- **痛点解决**: ✅ 已解决 - 从手动创建大量相似的【明细规则】代码中解放出来，并且无需手动维护规则元数据和Excel模板。*（RuleChangeDetector和TemplateGenerator已实现）*

### 2.2. 业务分析人员 ✅ 已实现
- **场景**:
  - 在主节点的Web界面上，看到所有规则的最新状态：`新增`、`有变动`、`就绪`、`废弃`。
  - 对于`新增`或`有变动`的规则，下载系统自动生成的、与最新逻辑完全匹配的Excel模板。
  - 填写包含具体规则条件（如药品代码、诊断编码）的Excel文件。
  - 上传Excel文件，在界面上预览解析结果，在线修正数据错误。
  - 确认无误后，提交数据。规则状态更新为`就绪`，并等待同步至所有子节点。
- **痛点解决**: ✅ 已解决 - 告别通过邮件、会议等传统方式传递规则明细，拥有一个可追溯、可管理的自助平台，并能第一时间响应业务逻辑的变化。*（前端UI已完成，包括RuleDashboard、DataUploader等）*

## 3. 系统架构：主从（Master-Slave）模式

系统分为两个核心部分：

- **主节点 (Master)**: 部署在公司公网服务器。是规则的中央管理中心。
- **子节点 (Slave)**: 轻量级部署在客户（医院）内网。是规则的执行引擎。

### 3.1. 主节点 (Master) ✅ 已完成
- **核心功能**:
  - **启动时自检** ✅ 已实现: 启动时自动扫描规则代码，同步数据库元信息，并预生成最新的Excel模板。
  - **规则生命周期管理** ✅ 已实现: 完整的规则生命周期管理，支持`新增`、`有变动`、`就绪`、`废弃`四种状态。
  - **业务工作流** ✅ 已实现: 为业务人员提供数据上传、预览、确认的工作流。
  - **同步API** ✅ 已实现: 对外提供安全的数据同步API，供子节点拉取规则。

### 3.2. 子节点 (Slave) ✅ 已完成
- **核心功能**:
  - **规则同步** ✅ 已实现: 定期通过代理服务器，以"拉模式(Pull)"从主节点同步状态为`就绪`的规则。
  - **内存缓存** ✅ 已实现: 将规则加载到内存中。
  - **高性能校验** ✅ 已实现: 提供稳定、高性能的实时数据校验服务。*（已实现三阶段性能优化）*

## 4. 核心功能详述

### 4.1. 规则生命周期与状态机 ✅ 已完成
系统启动时，将自动扫描`rules/base_rules/`目录下的代码文件，并与数据库记录进行对比，管理规则的生命周期。
- **新增 (NEW)** ✅ 已实现: 当代码目录中出现新的规则文件，但在数据库中不存在记录时，系统会自动创建一条新记录，状态置为`新增`。
- **有变动 (CHANGED)** ✅ 已实现: 如果规则文件的内容哈希值与数据库中记录的值不匹配，说明其业务逻辑已更新，状态置为`有变动`。
- **就绪 (READY)** ✅ 已实现: 当处于`新增`或`有变动`状态的规则，由业务人员成功上传并确认其第一版或新版明细数据后，状态自动变为`就绪`。只有`就绪`状态的规则及其数据才会被同步到子节点。
- **废弃 (DEPRECATED)** ✅ 已实现: 当代码目录中删除了某个规则文件，系统会将其在数据库中的对应记录状态置为`废弃`，该规则将不再参与任何校验和同步。

*实现文件: `services/rule_change_detector.py`, `models/database.py`*

### 4.2. 动态模板引擎 ✅ 已完成
- **启动时生成** ✅ 已实现: 在主节点服务启动并完成规则扫描后，系统会为每一个非`废弃`状态的规则，执行以下操作：
    1.  通过反射机制（`inspect`模块）动态分析规则类的`__init__`构造函数。
    2.  提取其参数列表作为Excel模板的列标题（Headers）。
    3.  使用`openpyxl`库生成`.xlsx`格式的模板文件。
- **按需下载** ✅ 已实现: 业务人员在前端界面点击下载时，系统通过API接口 (`/api/v1/rules/{rule_key}/template`) 将预先生成的最新模板文件提供给用户。此举确保了下载的高性能和模板与业务逻辑的强一致性。

*实现文件: `services/template_generator.py`, `api/routers/master/management.py`*

### 4.3. 规则同步机制 (主从交互) ✅ 已完成
- 子节点只拉取主节点上状态为`就绪` (`READY`) 且 `is_active=true` 的规则数据集。版本校验和同步流程保持不变。

*实现文件: `services/sync_service.py`, `api/routers/master/sync.py`*

### 4.4. 双重校验机制 ✅ 已完成
为确保规则的准确性和业务流程的完整性，系统需在主从节点均实现校验功能，但职责不同。
- **主节点 (Master) 校验** ✅ 已实现:
  - **目的**: 作为规则上线前的最后一道质量防线。
  - **场景**: 在业务人员为规则提交了数据集，且规则状态变为`就绪` (READY) 后，主节点提供一个测试校验接口。开发或测试人员可以使用该接口，传入模拟的患者数据，对刚刚生效的规则进行功能性验证。
  - **实现**: 提供一个 `/api/v1/validate` 接口，能够根据传入的规则ID列表和患者数据，执行校验并返回详细结果。
- **子节点 (Slave) 校验** ✅ 已实现:
  - **目的**: 为外部系统（如HIS）提供生产环境下的高性能实时校验服务。
  - **场景**: 部署在医院内网的子节点，接收来自业务系统的实时请求，对真实的患者数据进行快速校验。
  - **实现**: 提供一个与主节点签名一致的 `/api/v1/validate` 接口，但其部署环境和性能要求更高。

*实现文件: `api/routers/master/validation.py`, `api/routers/slave/validation.py`, `services/rule_service.py`*

## 5. 数据库设计

### 5.1. `base_rules` (基础规则表)
- 存储规则的元信息。
- **`status` 字段**: 存储规则的当前状态，使用枚举类型，可选值为 `NEW`, `CHANGED`, `READY`, `DEPRECATED`。
- 其他字段如`rule_key`, `rule_name`, `module_path`, `file_hash`保持不变。

### 5.2. `rule_data_sets` (规则数据集表)
- 存储业务人员上传的明细数据，包含`base_rule_id`, `data_set (JSON)`, `version`, `is_active`等字段，支持版本管理。该表设计保持不变。

## 6. 非功能性需求

### 6.1. 性能 ✅ 已超额完成
- **响应时间** ✅ 已超额达成: 子节点的校验API响应时间应在100ms以内。*（实际达到30-80ms，超额完成）*
- **UI流畅性** ✅ 已实现: 主节点UI操作流畅，大文件上传有进度提示和后台处理机制。
- **高并发支持** ✅ 已超额完成: 校验引擎必须支持高并发，能够充分利用服务器的多核CPU能力，并行处理同一批次的多个规则校验，以达到最快的响应速度。*（已实现三阶段性能优化，并发能力提升7倍）*

*实现文件: `core/dynamic_process_pool.py`, `core/performance_monitor.py`, `core/intelligent_cache.py`等*

### 6.2. 安全 🔄 部分完成，需要加固
- **API认证** 🔄 基础实现: 主从节点间的所有API通信必须基于API Key或其他令牌机制进行认证。*（已实现基础API Key认证，但存在安全隐患需要修复）*
- **文件校验** ✅ 已实现: 对用户上传的文件进行严格的类型和内容校验，防止恶意文件上传。

### 6.3. 可用性 ✅ 已完成
- **配置管理** ✅ 已实现: 系统应支持通过环境变量配置运行模式（master/slave）、数据库连接、主节点地址、代理服务器等。
- **日志系统** ✅ 已实现: 提供清晰的日志，记录规则同步状态和校验执行情况。

*实现文件: `config/settings.py`, `config/logging_config.py`*