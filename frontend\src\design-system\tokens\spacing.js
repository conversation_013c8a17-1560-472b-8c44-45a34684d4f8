/**
 * 间距设计Token
 * 定义系统中使用的所有间距变量，确保布局一致性
 */

// 基础间距单位 (基于8px网格系统)
export const baseUnit = 8 // 8px

// 间距比例
export const spacing = {
  // 极小间距
  0: '0',
  px: '1px',
  0.5: '0.125rem', // 2px
  1: '0.25rem',    // 4px
  1.5: '0.375rem', // 6px
  2: '0.5rem',     // 8px
  2.5: '0.625rem', // 10px
  3: '0.75rem',    // 12px
  3.5: '0.875rem', // 14px
  4: '1rem',       // 16px
  5: '1.25rem',    // 20px
  6: '1.5rem',     // 24px
  7: '1.75rem',    // 28px
  8: '2rem',       // 32px
  9: '2.25rem',    // 36px
  10: '2.5rem',    // 40px
  11: '2.75rem',   // 44px
  12: '3rem',      // 48px
  14: '3.5rem',    // 56px
  16: '4rem',      // 64px
  20: '5rem',      // 80px
  24: '6rem',      // 96px
  28: '7rem',      // 112px
  32: '8rem',      // 128px
  36: '9rem',      // 144px
  40: '10rem',     // 160px
  44: '11rem',     // 176px
  48: '12rem',     // 192px
  52: '13rem',     // 208px
  56: '14rem',     // 224px
  60: '15rem',     // 240px
  64: '16rem',     // 256px
  72: '18rem',     // 288px
  80: '20rem',     // 320px
  96: '24rem'      // 384px
}

// 语义化间距
export const semantic = {
  // 组件内部间距
  component: {
    xs: spacing[1],    // 4px
    sm: spacing[2],    // 8px
    md: spacing[3],    // 12px
    lg: spacing[4],    // 16px
    xl: spacing[6]     // 24px
  },

  // 组件之间间距
  layout: {
    xs: spacing[2],    // 8px
    sm: spacing[4],    // 16px
    md: spacing[6],    // 24px
    lg: spacing[8],    // 32px
    xl: spacing[12],   // 48px
    xxl: spacing[16]   // 64px
  },

  // 页面级间距
  page: {
    xs: spacing[4],    // 16px
    sm: spacing[6],    // 24px
    md: spacing[8],    // 32px
    lg: spacing[12],   // 48px
    xl: spacing[16],   // 64px
    xxl: spacing[24]   // 96px
  },

  // 容器间距
  container: {
    xs: spacing[4],    // 16px
    sm: spacing[6],    // 24px
    md: spacing[8],    // 32px
    lg: spacing[12],   // 48px
    xl: spacing[16],   // 64px
    xxl: spacing[20]   // 80px
  }
}

// 内边距预设
export const padding = {
  // 按钮内边距
  button: {
    xs: `${spacing[1]} ${spacing[2]}`,      // 4px 8px
    sm: `${spacing[1.5]} ${spacing[3]}`,    // 6px 12px
    md: `${spacing[2]} ${spacing[4]}`,      // 8px 16px
    lg: `${spacing[2.5]} ${spacing[5]}`,    // 10px 20px
    xl: `${spacing[3]} ${spacing[6]}`       // 12px 24px
  },

  // 输入框内边距
  input: {
    xs: `${spacing[1]} ${spacing[2]}`,      // 4px 8px
    sm: `${spacing[1.5]} ${spacing[3]}`,    // 6px 12px
    md: `${spacing[2]} ${spacing[3]}`,      // 8px 12px
    lg: `${spacing[2.5]} ${spacing[4]}`,    // 10px 16px
    xl: `${spacing[3]} ${spacing[4]}`       // 12px 16px
  },

  // 卡片内边距
  card: {
    xs: spacing[3],    // 12px
    sm: spacing[4],    // 16px
    md: spacing[5],    // 20px
    lg: spacing[6],    // 24px
    xl: spacing[8]     // 32px
  },

  // 模态框内边距
  modal: {
    xs: spacing[4],    // 16px
    sm: spacing[5],    // 20px
    md: spacing[6],    // 24px
    lg: spacing[8],    // 32px
    xl: spacing[10]    // 40px
  },

  // 表格内边距
  table: {
    xs: `${spacing[1]} ${spacing[2]}`,      // 4px 8px
    sm: `${spacing[1.5]} ${spacing[3]}`,    // 6px 12px
    md: `${spacing[2]} ${spacing[3]}`,      // 8px 12px
    lg: `${spacing[2.5]} ${spacing[4]}`,    // 10px 16px
    xl: `${spacing[3]} ${spacing[5]}`       // 12px 20px
  }
}

// 外边距预设
export const margin = {
  // 组件外边距
  component: {
    xs: spacing[1],    // 4px
    sm: spacing[2],    // 8px
    md: spacing[3],    // 12px
    lg: spacing[4],    // 16px
    xl: spacing[6]     // 24px
  },

  // 段落外边距
  paragraph: {
    xs: spacing[2],    // 8px
    sm: spacing[3],    // 12px
    md: spacing[4],    // 16px
    lg: spacing[5],    // 20px
    xl: spacing[6]     // 24px
  },

  // 标题外边距
  heading: {
    xs: spacing[2],    // 8px
    sm: spacing[3],    // 12px
    md: spacing[4],    // 16px
    lg: spacing[6],    // 24px
    xl: spacing[8]     // 32px
  },

  // 列表外边距
  list: {
    xs: spacing[1],    // 4px
    sm: spacing[2],    // 8px
    md: spacing[3],    // 12px
    lg: spacing[4],    // 16px
    xl: spacing[5]     // 20px
  }
}

// 间隙 (Gap) 预设
export const gap = {
  // 网格间隙
  grid: {
    xs: spacing[1],    // 4px
    sm: spacing[2],    // 8px
    md: spacing[3],    // 12px
    lg: spacing[4],    // 16px
    xl: spacing[6],    // 24px
    xxl: spacing[8]    // 32px
  },

  // Flex间隙
  flex: {
    xs: spacing[1],    // 4px
    sm: spacing[2],    // 8px
    md: spacing[3],    // 12px
    lg: spacing[4],    // 16px
    xl: spacing[5],    // 20px
    xxl: spacing[6]    // 24px
  },

  // 表单间隙
  form: {
    xs: spacing[2],    // 8px
    sm: spacing[3],    // 12px
    md: spacing[4],    // 16px
    lg: spacing[5],    // 20px
    xl: spacing[6]     // 24px
  }
}

// 尺寸预设
export const size = {
  // 图标尺寸
  icon: {
    xs: spacing[3],    // 12px
    sm: spacing[4],    // 16px
    md: spacing[5],    // 20px
    lg: spacing[6],    // 24px
    xl: spacing[8],    // 32px
    xxl: spacing[12]   // 48px
  },

  // 头像尺寸
  avatar: {
    xs: spacing[6],    // 24px
    sm: spacing[8],    // 32px
    md: spacing[10],   // 40px
    lg: spacing[12],   // 48px
    xl: spacing[16],   // 64px
    xxl: spacing[20]   // 80px
  },

  // 按钮高度
  button: {
    xs: spacing[6],    // 24px
    sm: spacing[7],    // 28px
    md: spacing[8],    // 32px
    lg: spacing[10],   // 40px
    xl: spacing[12]    // 48px
  },

  // 输入框高度
  input: {
    xs: spacing[6],    // 24px
    sm: spacing[7],    // 28px
    md: spacing[8],    // 32px
    lg: spacing[10],   // 40px
    xl: spacing[12]    // 48px
  }
}

// 响应式间距
export const responsive = {
  // 容器最大宽度
  container: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
  },

  // 响应式内边距
  padding: {
    sm: spacing[4],    // 16px
    md: spacing[6],    // 24px
    lg: spacing[8],    // 32px
    xl: spacing[12],   // 48px
    '2xl': spacing[16] // 64px
  },

  // 响应式外边距
  margin: {
    sm: spacing[2],    // 8px
    md: spacing[4],    // 16px
    lg: spacing[6],    // 24px
    xl: spacing[8],    // 32px
    '2xl': spacing[12] // 48px
  }
}

// 工具函数
export const spacingUtils = {
  // 获取间距值
  getSpacing: (key) => {
    return spacing[key] || spacing[4] // 默认16px
  },

  // 生成间距CSS
  generateSpacingCSS: (type, size, direction = 'all') => {
    const value = spacing[size] || spacing[4]
    const property = type === 'padding' ? 'padding' : 'margin'

    switch (direction) {
      case 'top':
        return { [`${property}Top`]: value }
      case 'right':
        return { [`${property}Right`]: value }
      case 'bottom':
        return { [`${property}Bottom`]: value }
      case 'left':
        return { [`${property}Left`]: value }
      case 'horizontal':
        return {
          [`${property}Left`]: value,
          [`${property}Right`]: value
        }
      case 'vertical':
        return {
          [`${property}Top`]: value,
          [`${property}Bottom`]: value
        }
      default:
        return { [property]: value }
    }
  },

  // 计算响应式间距
  getResponsiveSpacing: (baseSize, breakpoint = 'md') => {
    const multipliers = {
      sm: 0.75,
      md: 1,
      lg: 1.25,
      xl: 1.5,
      '2xl': 2
    }

    const baseValue = parseFloat(spacing[baseSize] || spacing[4])
    const multiplier = multipliers[breakpoint] || 1

    return `${baseValue * multiplier}rem`
  }
}

// 导出所有间距token
export const spacingTokens = {
  spacing,
  semantic,
  padding,
  margin,
  gap,
  size,
  responsive,
  baseUnit,
  ...spacingUtils
}
