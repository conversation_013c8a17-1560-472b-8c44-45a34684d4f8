import asyncio
from collections.abc import Coroutine
from typing import Any

from models import FeeItem, PatientData, RuleOutput, RuleResult
from rules.base_rules.base import BaseRule


class MockRule(BaseRule):
    """用于测试的模拟规则类"""
    def __init__(
        self,
        rule_id: str,
        will_violate: bool,
        rule_name: str = "Mock Rule",
        should_raise_exception: bool = False
    ):
        # 为基类提供必需的参数，并设置默认值
        super().__init__(
            rule_id=rule_id,
            rule_name=rule_name,
            level1="模拟一级",
            level2="模拟二级",
            level3="模拟三级"
        )
        self.will_violate = will_violate
        self.should_raise_exception = should_raise_exception

    async def _validate_logic(self, patient_data: PatientData) -> RuleResult | None:
        """
        模拟验证逻辑。
        如果 should_raise_exception 为 True，则引发异常。
        如果 will_violate 为 True，则返回一个违规结果。
        否则，返回 None。
        """
        await asyncio.sleep(0.01) # 模拟IO延迟
        if self.should_raise_exception:
            raise ValueError("故意的测试异常")

        if self.will_violate:
            # 模拟一个涉及具体费用项目的违规结果
            violating_item = FeeItem(
                uniq_field="1",
                fymc="模拟违规项目",
                fydm="FEE001",
                dj=100.0,
                sl=1.0,
                je=100.0,
            )
            output = RuleOutput(
                message=f"规则 '{self.rule_name}' ({self.rule_id}) 被触发。",
                error_fee=violating_item.je,
                illegal_item=violating_item.fymc
            )
            return RuleResult(
                id=self.rule_id,
                output=output
            )
        return None

    def __call__(self, *args: Any, **kwds: Any) -> Coroutine[Any, Any, RuleResult | None]:
        return super().__call__(*args, **kwds)
