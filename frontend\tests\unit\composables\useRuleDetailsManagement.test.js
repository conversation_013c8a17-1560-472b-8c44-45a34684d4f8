import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActiveP<PERSON>, createPinia } from 'pinia'
import { nextTick } from 'vue'
import { useRuleDetailsManagement } from '@/composables/business/useRuleDetailsManagement'
import { useRuleDetailsStore } from '@/stores/ruleDetails'
import { useRulesStore } from '@/stores/rules'

// Mock stores
vi.mock('@/stores/ruleDetails')
vi.mock('@/stores/rules')

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn()
  }
}))

// Mock lodash-es
vi.mock('lodash-es', () => ({
  debounce: vi.fn((fn) => fn)
}))

describe('useRuleDetailsManagement', () => {
  let mockRuleDetailsStore
  let mockRulesStore
  let management

  beforeEach(() => {
    setActivePinia(createPinia())

    // Mock store 返回值
    mockRuleDetailsStore = {
      detailsList: [],
      currentDetail: null,
      selectedDetails: [],
      pagination: { page: 1, pageSize: 20, total: 0 },
      filters: { status: null, search: '', sortBy: 'created_at', sortOrder: 'desc' },
      operationStatus: { lastOperation: null, operationTime: null, affectedCount: 0, errors: [] },
      loading: false,
      detailLoading: false,
      batchLoading: false,
      searchLoading: false,
      isLoading: false,
      paginationInfo: { current: 1, total: 0, pageSize: 20 },
      activeFilters: [],
      detailsCount: 0,
      hasDetails: false,
      selectedCount: 0,
      hasSelected: false,
      fetchDetailsList: vi.fn(),
      fetchDetailById: vi.fn(),
      createDetail: vi.fn(),
      updateDetail: vi.fn(),
      deleteDetail: vi.fn(),
      searchDetails: vi.fn(),
      updateFilters: vi.fn(),
      resetFilters: vi.fn(),
      updatePagination: vi.fn(),
      toggleDetailSelection: vi.fn(),
      toggleSelectAll: vi.fn(),
      selectedDetails: []
    }

    mockRulesStore = {
      getRuleByKey: vi.fn(),
      refreshRule: vi.fn()
    }

    useRuleDetailsStore.mockReturnValue(mockRuleDetailsStore)
    useRulesStore.mockReturnValue(mockRulesStore)

    vi.clearAllMocks()
  })

  describe('初始化', () => {
    it('应该正确初始化管理器', async () => {
      const ruleKey = 'test-rule'
      management = useRuleDetailsManagement(ruleKey)

      expect(management.currentRuleKey.value).toBe(ruleKey)
      expect(management.isInitialized.value).toBe(false)
    })

    it('应该在初始化时加载明细列表', async () => {
      const ruleKey = 'test-rule'
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })

      management = useRuleDetailsManagement()
      await management.initialize(ruleKey)

      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalledWith(
        ruleKey,
        expect.objectContaining({
          page: 1,
          page_size: 20
        })
      )
      expect(management.isInitialized.value).toBe(true)
    })

    it('应该处理缺少 ruleKey 的情况', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { })

      management = useRuleDetailsManagement()
      await management.initialize()

      expect(consoleSpy).toHaveBeenCalledWith('useRuleDetailsManagement: 缺少 ruleKey')
      expect(management.isInitialized.value).toBe(false)

      consoleSpy.mockRestore()
    })
  })

  describe('数据加载', () => {
    beforeEach(() => {
      management = useRuleDetailsManagement('test-rule')
    })

    it('应该加载明细列表', async () => {
      const mockParams = { page: 2, status: 'ACTIVE' }
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })

      await management.loadDetailsList(mockParams)

      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalledWith(
        'test-rule',
        expect.objectContaining({
          page: 1, // 来自 pagination
          page_size: 20,
          status: null, // 来自 filters
          ...mockParams
        })
      )
    })

    it('应该刷新列表', async () => {
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })

      await management.refreshList()

      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalledWith(
        'test-rule',
        expect.objectContaining({
          _refresh: expect.any(Number)
        })
      )
    })
  })

  describe('搜索和过滤', () => {
    beforeEach(() => {
      management = useRuleDetailsManagement('test-rule')
    })

    it('应该执行搜索', async () => {
      const keyword = '测试关键词'
      mockRuleDetailsStore.searchDetails.mockResolvedValue({ items: [], pagination: {} })

      await management.performSearch(keyword)

      expect(management.searchKeyword.value).toBe(keyword)
      // 由于 debounce 被 mock 为立即执行，searchDetails 应该被调用
      expect(mockRuleDetailsStore.searchDetails).toHaveBeenCalledWith(
        'test-rule',
        expect.objectContaining({
          search: keyword,
          page: 1,
          page_size: 20
        })
      )
    })

    it('应该清除搜索', async () => {
      management.searchKeyword.value = '测试'
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })

      await management.clearSearch()

      expect(management.searchKeyword.value).toBe('')
      expect(mockRuleDetailsStore.updateFilters).toHaveBeenCalledWith({ search: '' })
      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalledWith(
        'test-rule',
        expect.objectContaining({ page: 1 })
      )
    })

    it('应该应用过滤条件', async () => {
      const newFilters = { status: 'ACTIVE', errorLevel1: '错误类型' }
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })

      await management.applyFilters(newFilters)

      expect(mockRuleDetailsStore.updateFilters).toHaveBeenCalledWith(newFilters)
      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalledWith(
        'test-rule',
        expect.objectContaining({ page: 1 })
      )
    })

    it('应该重置过滤条件', async () => {
      management.searchKeyword.value = '测试'
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })

      await management.resetFilters()

      expect(mockRuleDetailsStore.resetFilters).toHaveBeenCalled()
      expect(management.searchKeyword.value).toBe('')
      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalledWith(
        'test-rule',
        expect.objectContaining({ page: 1 })
      )
    })
  })

  describe('分页管理', () => {
    beforeEach(() => {
      management = useRuleDetailsManagement('test-rule')
    })

    it('应该处理分页变化', async () => {
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })

      await management.handlePageChange(2, 20)

      expect(mockRuleDetailsStore.updatePagination).toHaveBeenCalledWith({ page: 2, pageSize: 20 })
      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalled()
    })

    it('应该处理页面大小变化', async () => {
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })

      await management.handlePageSizeChange(50)

      expect(mockRuleDetailsStore.updatePagination).toHaveBeenCalledWith({ page: 1, pageSize: 50 })
      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalled()
    })
  })

  describe('CRUD 操作', () => {
    beforeEach(() => {
      management = useRuleDetailsManagement('test-rule')
    })

    it('应该查看明细详情', async () => {
      const detail = { id: 1, rule_detail_id: 'R001' }
      mockRuleDetailsStore.fetchDetailById.mockResolvedValue(detail)

      await management.viewDetail(detail)

      expect(mockRuleDetailsStore.fetchDetailById).toHaveBeenCalledWith('test-rule', 1)
    })

    it('应该创建新明细', async () => {
      const detailData = { rule_detail_id: 'R001', rule_name: '新规则' }
      const mockResult = { id: 1, ...detailData }

      mockRuleDetailsStore.createDetail.mockResolvedValue(mockResult)
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })
      mockRulesStore.refreshRule.mockResolvedValue()

      const result = await management.createDetail(detailData)

      expect(mockRuleDetailsStore.createDetail).toHaveBeenCalledWith('test-rule', detailData)
      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalled() // refreshList
      expect(mockRulesStore.refreshRule).toHaveBeenCalledWith('test-rule')
      expect(result).toEqual(mockResult)
    })

    it('应该更新明细', async () => {
      const detail = { id: 1, rule_detail_id: 'R001' }
      const updateData = { rule_name: '更新的规则' }
      const mockResult = { id: 1, rule_name: '更新的规则' }

      mockRuleDetailsStore.updateDetail.mockResolvedValue(mockResult)
      mockRulesStore.refreshRule.mockResolvedValue()

      const result = await management.updateDetail(detail, updateData)

      expect(mockRuleDetailsStore.updateDetail).toHaveBeenCalledWith('test-rule', 1, updateData)
      expect(mockRulesStore.refreshRule).toHaveBeenCalledWith('test-rule')
      expect(result).toEqual(mockResult)
    })

    it('应该删除明细', async () => {
      const detail = { id: 1, rule_detail_id: 'R001', rule_name: '规则1' }

      // Mock 确认对话框
      const { ElMessageBox } = await import('element-plus')
      ElMessageBox.confirm.mockResolvedValue('confirm')

      mockRuleDetailsStore.deleteDetail.mockResolvedValue(true)
      mockRuleDetailsStore.fetchDetailsList.mockResolvedValue({ items: [], pagination: {} })
      mockRulesStore.refreshRule.mockResolvedValue()

      const result = await management.deleteDetail(detail)

      expect(ElMessageBox.confirm).toHaveBeenCalledWith(
        '确定要删除明细 "规则1" 吗？',
        '确认删除',
        expect.objectContaining({
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      )
      expect(mockRuleDetailsStore.deleteDetail).toHaveBeenCalledWith('test-rule', 1)
      expect(mockRuleDetailsStore.fetchDetailsList).toHaveBeenCalled() // refreshList
      expect(mockRulesStore.refreshRule).toHaveBeenCalledWith('test-rule')
      expect(result).toBe(true)
    })

    it('应该处理删除取消', async () => {
      const detail = { id: 1, rule_detail_id: 'R001', rule_name: '规则1' }

      // Mock 取消确认对话框
      const { ElMessageBox } = await import('element-plus')
      ElMessageBox.confirm.mockRejectedValue('cancel')

      const result = await management.deleteDetail(detail)

      expect(mockRuleDetailsStore.deleteDetail).not.toHaveBeenCalled()
      expect(result).toBeUndefined()
    })
  })

  describe('选择管理', () => {
    beforeEach(() => {
      management = useRuleDetailsManagement('test-rule')
    })

    it('应该处理行选择变化', () => {
      const selection = [{ id: 1 }, { id: 2 }]

      management.handleSelectionChange(selection)

      expect(mockRuleDetailsStore.selectedDetails).toEqual(selection)
    })

    it('应该切换行选择', () => {
      const detail = { id: 1, rule_detail_id: 'R001' }

      management.toggleRowSelection(detail)

      expect(mockRuleDetailsStore.toggleDetailSelection).toHaveBeenCalledWith(detail)
    })

    it('应该处理全选/取消全选', () => {
      management.handleSelectAll(true)
      expect(mockRuleDetailsStore.toggleSelectAll).toHaveBeenCalledWith(true)

      management.handleSelectAll(false)
      expect(mockRuleDetailsStore.toggleSelectAll).toHaveBeenCalledWith(false)
    })
  })

  describe('计算属性', () => {
    beforeEach(() => {
      management = useRuleDetailsManagement('test-rule')
    })

    it('应该正确计算当前规则信息', () => {
      const mockRule = { rule_key: 'test-rule', rule_name: '测试规则' }
      mockRulesStore.getRuleByKey.mockReturnValue(mockRule)

      const ruleInfo = management.currentRuleInfo.value

      expect(mockRulesStore.getRuleByKey).toHaveBeenCalledWith('test-rule')
      expect(ruleInfo).toEqual(mockRule)
    })

    it('应该正确计算是否有搜索条件', () => {
      // 重置所有条件
      management.searchKeyword.value = ''

      // 重置 filters 的各个属性而不是替换整个对象
      mockRuleDetailsStore.filters.status = null
      mockRuleDetailsStore.filters.search = ''
      mockRuleDetailsStore.filters.errorLevel1 = null
      mockRuleDetailsStore.filters.errorLevel2 = null
      mockRuleDetailsStore.filters.ruleCategory = null

      // 无搜索条件
      expect(management.hasSearchConditions.value).toBe(false)

      // 有搜索关键词
      management.searchKeyword.value = '测试'
      expect(management.hasSearchConditions.value).toBe(true)

      // 清除搜索关键词，设置过滤条件
      management.searchKeyword.value = ''
      mockRuleDetailsStore.filters.status = 'ACTIVE'
      expect(management.hasSearchConditions.value).toBe(true)
    })

    it('应该提供正确的表格列配置', () => {
      const columns = management.tableColumns.value

      expect(columns).toBeInstanceOf(Array)
      expect(columns.length).toBeGreaterThan(0)
      expect(columns[0]).toHaveProperty('prop')
      expect(columns[0]).toHaveProperty('label')
    })
  })
})
