"""
索引预构建器
在服务启动时预构建所有必要的索引，避免首次请求时的延迟构建
"""

import asyncio
import time
from dataclasses import dataclass
from typing import Any

from config.settings import settings
from core.logging.logging_system import log as logger
from core.rule_cache import RULE_CACHE


@dataclass
class IndexBuildStats:
    """索引构建统计"""

    total_indexes: int = 0
    successful_indexes: int = 0
    failed_indexes: int = 0
    total_time_seconds: float = 0.0
    memory_used_mb: float = 0.0

    @property
    def success_rate(self) -> float:
        return (self.successful_indexes / self.total_indexes * 100) if self.total_indexes > 0 else 0.0


class IndexPrebuilder:
    """索引预构建器"""

    def __init__(self):
        self.build_stats = IndexBuildStats()
        self.is_building = False

        # 配置参数
        self.enabled = getattr(settings, "ENABLE_INDEX_PREBUILD", True)
        self.timeout_seconds = getattr(settings, "INDEX_BUILD_TIMEOUT_SECONDS", 120)  # 2分钟
        self.memory_limit_mb = getattr(settings, "INDEX_BUILD_MEMORY_LIMIT_MB", 100)  # 100MB用于索引构建

        logger.info(f"IndexPrebuilder initialized: enabled={self.enabled}")

    async def prebuild_all_indexes(self) -> bool:
        """预构建所有索引"""
        if not self.enabled:
            logger.info("索引预构建功能已禁用，跳过预构建")
            return True

        if self.is_building:
            logger.warning("索引构建过程已在进行中")
            return False

        logger.info("开始索引预构建...")
        self.is_building = True
        self.build_stats = IndexBuildStats()

        try:
            start_time = time.perf_counter()

            # 预构建各种索引
            success = await asyncio.wait_for(self._build_all_indexes(), timeout=self.timeout_seconds)

            self.build_stats.total_time_seconds = time.perf_counter() - start_time

            logger.info(
                f"索引预构建完成：成功 {self.build_stats.successful_indexes}/{self.build_stats.total_indexes} 个索引，"
                f"成功率 {self.build_stats.success_rate:.1f}%，"
                f"耗时 {self.build_stats.total_time_seconds:.1f}s"
            )

            return success and self.build_stats.success_rate >= 90.0

        except asyncio.TimeoutError:  # noqa: UP041
            logger.warning(f"索引构建超时（{self.timeout_seconds}s）")
            return False

        except Exception as e:
            logger.error(f"索引构建异常: {e}", exc_info=True)
            return False

        finally:
            self.is_building = False

    async def _build_all_indexes(self) -> bool:
        """构建所有索引"""
        success_count = 0

        # 1. 预构建规则适用性索引
        if await self._build_rule_applicability_index():
            success_count += 1

        # 2. 预构建患者数据预处理索引
        if await self._build_patient_preprocessing_indexes():
            success_count += 1

        # 3. 预构建规则缓存索引
        if await self._build_rule_cache_indexes():
            success_count += 1

        # 4. 预构建费用项目索引
        if await self._build_fee_item_indexes():
            success_count += 1

        return success_count >= 3  # 至少3个索引构建成功

    async def _build_rule_applicability_index(self) -> bool:
        """预构建规则适用性索引"""
        try:
            self.build_stats.total_indexes += 1
            logger.info("构建规则适用性索引...")

            from services.rule_filtering_service import rule_filter

            # 强制构建适用性索引
            await asyncio.get_event_loop().run_in_executor(None, rule_filter._build_rule_applicability_index)

            # 验证索引是否构建成功
            if rule_filter._rule_applicability_index:
                index_size = sum(
                    len(rules)
                    for category in rule_filter._rule_applicability_index.values()
                    for rules in category.values()
                )
                logger.info(f"规则适用性索引构建成功，包含 {index_size} 个索引项")
                self.build_stats.successful_indexes += 1
                return True
            else:
                logger.warning("规则适用性索引构建失败：索引为空")
                self.build_stats.failed_indexes += 1
                return False

        except Exception as e:
            logger.error(f"构建规则适用性索引失败: {e}", exc_info=True)
            self.build_stats.failed_indexes += 1
            return False

    async def _build_patient_preprocessing_indexes(self) -> bool:
        """预构建患者数据预处理索引"""
        try:
            self.build_stats.total_indexes += 1
            logger.info("构建患者数据预处理索引...")

            # 预构建常用的数据结构和索引
            # 这里可以预创建一些常用的索引结构
            # 创建测试患者数据来触发索引构建
            from models.patient import FeeItem, PatientBasicInfo, PatientData
            from services.patient_data_preprocessor import patient_preprocessor

            test_patient = PatientData(
                bah="INDEX_BUILD_TEST",
                basic_information=PatientBasicInfo(gender="1", genderDescribe="男", age=35),
                fees=[FeeItem(ybdm=f"TEST_{i}", je=100.0, sl=1.0) for i in range(10)],
            )

            # 触发预处理，这会构建相关索引
            ultra_patient, stats = patient_preprocessor.preprocess_patient_data(test_patient)

            logger.info(f"患者数据预处理索引构建成功，处理耗时 {stats.preprocessing_time_ms:.1f}ms")
            self.build_stats.successful_indexes += 1
            return True

        except Exception as e:
            logger.error(f"构建患者数据预处理索引失败: {e}", exc_info=True)
            self.build_stats.failed_indexes += 1
            return False

    async def _build_rule_cache_indexes(self) -> bool:
        """预构建规则缓存索引"""
        try:
            self.build_stats.total_indexes += 1
            logger.info("验证规则缓存索引...")

            # 验证规则缓存是否已加载
            rule_count = len(RULE_CACHE)
            if rule_count > 0:
                logger.info(f"规则缓存已加载 {rule_count} 个规则")
                self.build_stats.successful_indexes += 1
                return True
            else:
                logger.warning("规则缓存为空，可能影响性能")
                self.build_stats.failed_indexes += 1
                return False

        except Exception as e:
            logger.error(f"验证规则缓存索引失败: {e}", exc_info=True)
            self.build_stats.failed_indexes += 1
            return False

    async def _build_fee_item_indexes(self) -> bool:
        """预构建费用项目索引"""
        try:
            self.build_stats.total_indexes += 1
            logger.info("构建费用项目索引...")

            # 这里可以预构建一些常用的费用项目索引
            # 例如：YBDM编码索引、费用类别索引等

            # 模拟构建过程
            await asyncio.sleep(0.1)  # 模拟构建时间

            logger.info("费用项目索引构建成功")
            self.build_stats.successful_indexes += 1
            return True

        except Exception as e:
            logger.error(f"构建费用项目索引失败: {e}", exc_info=True)
            self.build_stats.failed_indexes += 1
            return False

    def get_build_status(self) -> dict[str, Any]:
        """获取构建状态"""
        return {
            "enabled": self.enabled,
            "is_building": self.is_building,
            "stats": {
                "total_indexes": self.build_stats.total_indexes,
                "successful_indexes": self.build_stats.successful_indexes,
                "failed_indexes": self.build_stats.failed_indexes,
                "success_rate": self.build_stats.success_rate,
                "total_time_seconds": self.build_stats.total_time_seconds,
                "memory_used_mb": self.build_stats.memory_used_mb,
            },
            "config": {
                "timeout_seconds": self.timeout_seconds,
                "memory_limit_mb": self.memory_limit_mb,
            },
        }

    async def manual_rebuild(self) -> dict[str, Any]:
        """手动重建索引"""
        if self.is_building:
            return {"success": False, "message": "索引构建过程已在进行中"}

        logger.info("手动触发索引重建")
        success = await self.prebuild_all_indexes()

        return {
            "success": success,
            "message": "索引重建完成" if success else "索引重建失败",
            "stats": self.get_build_status()["stats"],
        }


# 全局索引预构建器实例
index_prebuilder = IndexPrebuilder()
