<template>
  <div :class="cardClasses" :style="cardStyles">
    <!-- 卡片头部 -->
    <div v-if="$slots.header || title" class="r-card__header">
      <slot name="header">
        <div class="r-card__title">{{ title }}</div>
      </slot>

      <!-- 头部操作区 -->
      <div v-if="$slots.actions" class="r-card__actions">
        <slot name="actions" />
      </div>
    </div>

    <!-- 卡片主体 -->
    <div v-if="$slots.default" class="r-card__body">
      <slot />
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="r-card__footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { designTokens } from '../../tokens'

const props = defineProps({
  // 卡片标题
  title: {
    type: String,
    default: ''
  },

  // 卡片变体
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'outlined', 'elevated', 'filled'].includes(value)
  },

  // 卡片尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },

  // 是否可悬浮
  hoverable: {
    type: Boolean,
    default: false
  },

  // 是否可点击
  clickable: {
    type: Boolean,
    default: false
  },

  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },

  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  },

  // 自定义边框颜色
  borderColor: {
    type: String,
    default: null
  },

  // 自定义背景颜色
  backgroundColor: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['click'])

// 计算卡片类名
const cardClasses = computed(() => {
  return [
    'r-card',
    `r-card--${props.variant}`,
    `r-card--${props.size}`,
    {
      'r-card--hoverable': props.hoverable,
      'r-card--clickable': props.clickable,
      'r-card--disabled': props.disabled,
      'r-card--loading': props.loading
    }
  ]
})

// 计算卡片样式
const cardStyles = computed(() => {
  const styles = {}

  if (props.borderColor) {
    styles['--r-card-border-color'] = props.borderColor
  }

  if (props.backgroundColor) {
    styles['--r-card-background-color'] = props.backgroundColor
  }

  return styles
})

// 点击处理
const handleClick = (event) => {
  if (props.disabled || props.loading) return
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style scoped>
.r-card {
  /* 基础样式 */
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  word-wrap: break-word;
  background-color: var(--r-card-background-color, v-bind('designTokens.colors.background.paper'));
  border: 1px solid var(--r-card-border-color, v-bind('designTokens.colors.border.default'));
  border-radius: v-bind('designTokens.borderRadius.lg');
  transition: v-bind('designTokens.transitions.presets.normal');

  /* 默认尺寸 */
  padding: v-bind('designTokens.spacing.semantic.component.lg');
}

/* 尺寸变体 */
.r-card--xs {
  padding: v-bind('designTokens.spacing.semantic.component.xs');
  border-radius: v-bind('designTokens.borderRadius.base');
}

.r-card--sm {
  padding: v-bind('designTokens.spacing.semantic.component.sm');
  border-radius: v-bind('designTokens.borderRadius.md');
}

.r-card--md {
  padding: v-bind('designTokens.spacing.semantic.component.lg');
  border-radius: v-bind('designTokens.borderRadius.lg');
}

.r-card--lg {
  padding: v-bind('designTokens.spacing.semantic.component.xl');
  border-radius: v-bind('designTokens.borderRadius.xl');
}

.r-card--xl {
  padding: v-bind('designTokens.spacing.semantic.layout.sm');
  border-radius: v-bind('designTokens.borderRadius["2xl"]');
}

/* 变体样式 */
.r-card--default {
  box-shadow: v-bind('designTokens.shadows.sm');
}

.r-card--outlined {
  box-shadow: none;
  border-width: v-bind('designTokens.borderWidth[2]');
  /* 添加毛玻璃效果 */
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.r-card--elevated {
  box-shadow: v-bind('designTokens.shadows.lg');
  border: none;
  /* 添加毛玻璃效果 */
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.r-card--filled {
  background-color: v-bind('designTokens.colors.background.neutral');
  border: none;
  box-shadow: none;
}

/* 交互状态 */
.r-card--hoverable {
  /* 添加渐变边框效果 */
  position: relative;
  z-index: 0;
}

.r-card--hoverable::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0;
  background: linear-gradient(90deg, #409eff, #53a8ff);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: -1;
}

.r-card--hoverable:hover:not(.r-card--disabled)::before {
  opacity: 1;
  height: 4px;
}

.r-card--hoverable:hover:not(.r-card--disabled) {
  box-shadow: v-bind('designTokens.shadows.hover.md');
  transform: translateY(-2px);
}

.r-card--clickable {
  cursor: pointer;
}

.r-card--clickable:hover:not(.r-card--disabled) {
  box-shadow: v-bind('designTokens.shadows.hover.md');
}

.r-card--clickable:active:not(.r-card--disabled) {
  transform: translateY(1px);
}

.r-card--disabled {
  opacity: v-bind('designTokens.opacity[60]');
  cursor: not-allowed;
}

.r-card--loading {
  position: relative;
  pointer-events: none;
}

.r-card--loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: v-bind('designTokens.colors.background.overlay');
  z-index: 1;
}

/* 添加加载动画 */
.r-card--loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  margin: -15px 0 0 -15px;
  border: 2px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  z-index: 2;
  animation: r-card-loading-rotate 0.8s linear infinite;
}

@keyframes r-card-loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 卡片头部 */
.r-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: v-bind('designTokens.spacing.semantic.component.md');
  padding-bottom: v-bind('designTokens.spacing.semantic.component.sm');
  border-bottom: 1px solid v-bind('designTokens.colors.border.light');
  position: relative;
}

.r-card__title {
  font-size: v-bind('designTokens.typography.fontSize.lg');
  font-weight: v-bind('designTokens.typography.fontWeight.semibold');
  color: v-bind('designTokens.colors.text.primary');
  margin: 0;
  /* 添加文本渐变效果 */
  background: linear-gradient(90deg, v-bind('designTokens.colors.text.primary'), v-bind('designTokens.colors.semantic.primary.main'));
  background-clip: text;
  -webkit-background-clip: text;
  transition: all 0.3s ease;
}

/* 悬停时增强标题效果 */
.r-card--hoverable:hover .r-card__title,
.r-card--clickable:hover .r-card__title {
  color: transparent;
  -webkit-text-fill-color: transparent;
}

.r-card__actions {
  display: flex;
  align-items: center;
  gap: v-bind('designTokens.spacing.semantic.component.sm');
}

/* 卡片主体 */
.r-card__body {
  flex: 1;
  color: v-bind('designTokens.colors.text.secondary');
  line-height: v-bind('designTokens.typography.lineHeight.relaxed');
}

/* 卡片底部 */
.r-card__footer {
  margin-top: 4px !important; /* 使用固定的小间距值 */
  padding-top: 4px !important; /* 使用固定的小内边距值 */
  border-top: 1px solid v-bind('designTokens.colors.border.light');
}

/* 响应式调整 */
@media (max-width: 640px) {
  .r-card {
    border-radius: v-bind('designTokens.borderRadius.base');
  }

  .r-card--xs,
  .r-card--sm {
    padding: v-bind('designTokens.spacing.semantic.component.sm');
  }

  .r-card--md,
  .r-card--lg,
  .r-card--xl {
    padding: v-bind('designTokens.spacing.semantic.component.md');
  }

  .r-card__header {
    flex-direction: column;
    align-items: flex-start;
    gap: v-bind('designTokens.spacing.semantic.component.sm');
  }

  .r-card__actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
