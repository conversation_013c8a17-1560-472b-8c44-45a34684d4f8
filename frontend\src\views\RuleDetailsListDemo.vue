<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>规则明细功能已整合</h1>
      <p>规则明细功能已整合到规则详情界面中，请通过规则管理页面查看具体规则的详情</p>
    </div>

    <div class="demo-content">
      <el-card>
        <h3>功能说明</h3>
        <p>原规则明细列表界面已被移除，相关功能已整合到以下位置：</p>
        <ul>
          <li><strong>规则详情查看</strong>：通过仪表盘或规则管理页面点击"查看详情"</li>
          <li><strong>数据上传</strong>：通过仪表盘点击"上传数据"</li>
          <li><strong>规则配置</strong>：通过导航菜单进入"规则配置"页面</li>
        </ul>

        <div style="margin-top: 20px;">
          <el-button type="primary" @click="goToDashboard">返回仪表盘</el-button>
          <el-button @click="goToRuleManagement">规则配置</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToDashboard = () => {
  router.push('/')
}

const goToRuleManagement = () => {
  router.push('/rules/management')
}
</script>

<style scoped>
.demo-page {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.demo-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
