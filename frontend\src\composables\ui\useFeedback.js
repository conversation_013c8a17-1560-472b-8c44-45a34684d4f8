/**
 * 统一用户反馈系统
 * 提供Toast、Notification、Modal、Progress等多样化反馈机制
 */

import { ref, computed } from 'vue'
import { ElMessage, ElNotification, ElMessageBox, ElLoading } from 'element-plus'
import { useAppStore } from '@/stores/app'
import { FeedbackType, FeedbackLevel, FeedbackPosition } from '@/types/feedback'

/**
 * 默认配置
 */
const DEFAULT_CONFIG = {
  toast: {
    duration: 3000,
    position: FeedbackPosition.TOP_CENTER,
    showClose: true,
    maxCount: 3
  },
  notification: {
    duration: 4500,
    position: FeedbackPosition.TOP_RIGHT,
    showClose: true,
    maxCount: 5
  },
  modal: {
    width: '500px',
    closable: true,
    maskClosable: false,
    keyboard: true,
    centered: true
  },
  loading: {
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
    lock: true
  }
}

/**
 * 统一反馈系统
 * @param {Object} config - 配置选项
 * @returns {Object} 反馈系统实例
 */
export function useFeedback(config = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  const appStore = useAppStore()

  // 反馈实例管理
  const activeToasts = ref(new Map())
  const activeNotifications = ref(new Map())
  const activeModals = ref(new Map())
  const activeLoadings = ref(new Map())
  const activeProgresses = ref(new Map())

  // 反馈统计
  const feedbackStats = ref({
    totalToasts: 0,
    totalNotifications: 0,
    totalModals: 0,
    totalLoadings: 0
  })

  // ==================== Toast 系统 ====================

  /**
   * 显示 Toast
   * @param {Object} options - Toast 选项
   * @returns {string} Toast ID
   */
  const toast = (options) => {
    const toastOptions = {
      ...finalConfig.toast,
      ...options,
      onClose: () => {
        activeToasts.value.delete(toastId)
        options.onClose?.()
      }
    }

    const toastId = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 检查最大数量限制
    if (activeToasts.value.size >= finalConfig.toast.maxCount) {
      // 关闭最早的 Toast
      const oldestToast = Array.from(activeToasts.value.values())[0]
      if (oldestToast) {
        oldestToast.close()
      }
    }

    const toastInstance = ElMessage(toastOptions)

    activeToasts.value.set(toastId, {
      id: toastId,
      instance: toastInstance,
      options: toastOptions,
      timestamp: Date.now(),
      close: () => toastInstance.close()
    })

    feedbackStats.value.totalToasts++

    // 同步到全局状态
    appStore.addToast({
      type: options.type || FeedbackLevel.INFO,
      message: options.message,
      duration: toastOptions.duration
    })

    return toastId
  }

  /**
   * 成功 Toast
   */
  const toastSuccess = (message, options = {}) =>
    toast({ ...options, message, type: FeedbackLevel.SUCCESS })

  /**
   * 错误 Toast
   */
  const toastError = (message, options = {}) =>
    toast({ ...options, message, type: FeedbackLevel.ERROR })

  /**
   * 警告 Toast
   */
  const toastWarning = (message, options = {}) =>
    toast({ ...options, message, type: FeedbackLevel.WARNING })

  /**
   * 信息 Toast
   */
  const toastInfo = (message, options = {}) =>
    toast({ ...options, message, type: FeedbackLevel.INFO })

  // ==================== Notification 系统 ====================

  /**
   * 显示通知
   * @param {Object} options - 通知选项
   * @returns {string} 通知 ID
   */
  const notify = (options) => {
    const notificationOptions = {
      ...finalConfig.notification,
      ...options,
      onClose: () => {
        activeNotifications.value.delete(notificationId)
        options.onClose?.()
      }
    }

    const notificationId = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 检查最大数量限制
    if (activeNotifications.value.size >= finalConfig.notification.maxCount) {
      // 关闭最早的通知
      const oldestNotification = Array.from(activeNotifications.value.values())[0]
      if (oldestNotification) {
        oldestNotification.close()
      }
    }

    const notificationInstance = ElNotification(notificationOptions)

    activeNotifications.value.set(notificationId, {
      id: notificationId,
      instance: notificationInstance,
      options: notificationOptions,
      timestamp: Date.now(),
      close: () => notificationInstance.close(),
      update: (newOptions) => {
        // Element Plus 不支持直接更新，需要关闭后重新创建
        notificationInstance.close()
        return notify({ ...notificationOptions, ...newOptions })
      }
    })

    feedbackStats.value.totalNotifications++

    // 同步到全局状态
    appStore.addNotification({
      type: options.type || FeedbackLevel.INFO,
      title: options.title,
      message: options.message,
      duration: notificationOptions.duration,
      persistent: notificationOptions.duration === 0
    })

    return notificationId
  }

  /**
   * 成功通知
   */
  const notifySuccess = (title, message = '', options = {}) =>
    notify({ ...options, title, message, type: FeedbackLevel.SUCCESS })

  /**
   * 错误通知
   */
  const notifyError = (title, message = '', options = {}) =>
    notify({ ...options, title, message, type: FeedbackLevel.ERROR })

  /**
   * 警告通知
   */
  const notifyWarning = (title, message = '', options = {}) =>
    notify({ ...options, title, message, type: FeedbackLevel.WARNING })

  /**
   * 信息通知
   */
  const notifyInfo = (title, message = '', options = {}) =>
    notify({ ...options, title, message, type: FeedbackLevel.INFO })

  // ==================== Modal 系统 ====================

  /**
   * 显示模态框
   * @param {Object} options - 模态框选项
   * @returns {Promise<string>} 模态框 ID
   */
  const modal = async (options) => {
    const modalOptions = {
      ...finalConfig.modal,
      ...options
    }

    const modalId = `modal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    try {
      const result = await ElMessageBox({
        title: modalOptions.title,
        message: modalOptions.content || modalOptions.message,
        type: modalOptions.type,
        showCancelButton: modalOptions.showCancel !== false,
        confirmButtonText: modalOptions.confirmText || '确定',
        cancelButtonText: modalOptions.cancelText || '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm' && modalOptions.onConfirm) {
            const confirmResult = modalOptions.onConfirm()
            if (confirmResult instanceof Promise) {
              instance.confirmButtonLoading = true
              confirmResult
                .then(() => {
                  done()
                })
                .catch(() => {
                  instance.confirmButtonLoading = false
                })
            } else {
              done()
            }
          } else {
            if (modalOptions.onCancel && action === 'cancel') {
              modalOptions.onCancel()
            }
            done()
          }
        }
      })

      activeModals.value.set(modalId, {
        id: modalId,
        options: modalOptions,
        timestamp: Date.now(),
        result
      })

      feedbackStats.value.totalModals++
      return modalId

    } catch (error) {
      // 用户取消或其他错误
      return null
    }
  }

  /**
   * 确认对话框
   * @param {string} message - 确认消息
   * @param {string} title - 标题
   * @param {Object} options - 选项
   * @returns {Promise<boolean>} 是否确认
   */
  const confirm = async (message, title = '确认', options = {}) => {
    try {
      await modal({
        title,
        content: message,
        type: FeedbackLevel.WARNING,
        ...options
      })
      return true
    } catch {
      return false
    }
  }

  /**
   * 警告对话框
   * @param {string} message - 警告消息
   * @param {string} title - 标题
   * @param {Object} options - 选项
   * @returns {Promise<void>}
   */
  const alert = async (message, title = '提示', options = {}) => {
    return modal({
      title,
      content: message,
      showCancel: false,
      type: FeedbackLevel.INFO,
      ...options
    })
  }

  // ==================== Loading 系统 ====================

  /**
   * 显示加载
   * @param {Object} options - 加载选项
   * @returns {Object} 加载实例
   */
  const loading = (options = {}) => {
    const loadingOptions = {
      ...finalConfig.loading,
      ...options
    }

    const loadingId = `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const loadingInstance = ElLoading.service(loadingOptions)

    const loadingWrapper = {
      id: loadingId,
      instance: loadingInstance,
      options: loadingOptions,
      timestamp: Date.now(),
      visible: true,
      show: () => {
        if (!loadingWrapper.visible) {
          loadingWrapper.instance = ElLoading.service(loadingOptions)
          loadingWrapper.visible = true
        }
      },
      hide: () => {
        if (loadingWrapper.visible) {
          loadingWrapper.instance.close()
          loadingWrapper.visible = false
          activeLoadings.value.delete(loadingId)
        }
      },
      setText: (text) => {
        loadingWrapper.options.text = text
        // Element Plus 不支持动态更新文本，需要重新创建
        if (loadingWrapper.visible) {
          loadingWrapper.instance.close()
          loadingWrapper.instance = ElLoading.service({
            ...loadingWrapper.options,
            text
          })
        }
      }
    }

    activeLoadings.value.set(loadingId, loadingWrapper)
    feedbackStats.value.totalLoadings++

    // 同步到全局状态
    appStore.addLoadingTask(loadingId, loadingOptions.text)

    return loadingWrapper
  }

  /**
   * 显示全屏加载
   */
  const showLoading = (text = '加载中...', options = {}) => {
    return loading({
      text,
      fullscreen: true,
      ...options
    })
  }

  /**
   * 隐藏加载
   * @param {Object} instance - 加载实例
   */
  const hideLoading = (instance) => {
    if (instance) {
      instance.hide()
    } else {
      // 隐藏所有加载
      activeLoadings.value.forEach(loading => loading.hide())
    }
  }

  // ==================== Progress 系统 ====================

  /**
   * 创建进度条
   * @param {Object} options - 进度条选项
   * @returns {Object} 进度条实例
   */
  const progress = (options = {}) => {
    const progressId = `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const progressInstance = {
      id: progressId,
      options: {
        type: 'line',
        percentage: 0,
        status: '',
        showText: true,
        ...options
      },
      timestamp: Date.now(),
      update: (percentage, status) => {
        progressInstance.options.percentage = Math.max(0, Math.min(100, percentage))
        if (status) {
          progressInstance.options.status = status
        }

        // 更新全局加载进度
        appStore.updateLoadingProgress(progressInstance.options.percentage)
      },
      finish: () => {
        progressInstance.update(100, 'success')
        setTimeout(() => {
          activeProgresses.value.delete(progressId)
        }, 1000)
      },
      error: () => {
        progressInstance.update(progressInstance.options.percentage, 'exception')
      }
    }

    activeProgresses.value.set(progressId, progressInstance)
    return progressInstance
  }

  // ==================== 管理方法 ====================

  /**
   * 清除指定类型的反馈
   * @param {string} type - 反馈类型
   */
  const clear = (type) => {
    switch (type) {
      case FeedbackType.TOAST:
        activeToasts.value.forEach(toast => toast.close())
        activeToasts.value.clear()
        break

      case FeedbackType.NOTIFICATION:
        activeNotifications.value.forEach(notification => notification.close())
        activeNotifications.value.clear()
        break

      case FeedbackType.LOADING:
        activeLoadings.value.forEach(loading => loading.hide())
        activeLoadings.value.clear()
        break

      case FeedbackType.PROGRESS:
        activeProgresses.value.clear()
        break
    }
  }

  /**
   * 清除所有反馈
   */
  const clearAll = () => {
    Object.values(FeedbackType).forEach(type => clear(type))
  }

  /**
   * 获取反馈状态
   */
  const getState = () => ({
    toasts: Array.from(activeToasts.value.values()),
    notifications: Array.from(activeNotifications.value.values()),
    modals: Array.from(activeModals.value.values()),
    loadings: Array.from(activeLoadings.value.values()),
    progresses: Array.from(activeProgresses.value.values()),
    stats: feedbackStats.value
  })

  // ==================== 计算属性 ====================

  /**
   * 是否有活跃的反馈
   */
  const hasActiveFeedback = computed(() => {
    return activeToasts.value.size > 0 ||
      activeNotifications.value.size > 0 ||
      activeModals.value.size > 0 ||
      activeLoadings.value.size > 0
  })

  /**
   * 反馈统计
   */
  const stats = computed(() => ({
    ...feedbackStats.value,
    active: {
      toasts: activeToasts.value.size,
      notifications: activeNotifications.value.size,
      modals: activeModals.value.size,
      loadings: activeLoadings.value.size,
      progresses: activeProgresses.value.size
    }
  }))

  // ==================== 返回接口 ====================

  return {
    // Toast 方法
    toast,
    toastSuccess,
    toastError,
    toastWarning,
    toastInfo,

    // Notification 方法
    notify,
    notifySuccess,
    notifyError,
    notifyWarning,
    notifyInfo,

    // Modal 方法
    modal,
    confirm,
    alert,

    // Loading 方法
    loading,
    showLoading,
    hideLoading,

    // Progress 方法
    progress,

    // 管理方法
    clear,
    clearAll,
    getState,

    // 状态
    activeToasts,
    activeNotifications,
    activeModals,
    activeLoadings,
    activeProgresses,
    hasActiveFeedback,
    stats,

    // 配置
    config: finalConfig,

    // 常量
    FeedbackType,
    FeedbackLevel,
    FeedbackPosition
  }
}
