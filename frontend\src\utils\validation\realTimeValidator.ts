/**
 * 实时校验组件
 * 提供字段级实时校验和用户体验优化
 */

import {
  ValidationResult,
  RealTimeValidationOptions,
  ValidationState,
  ValidationEvent,
  ValidationEventListener
} from './validationTypes'
import { DynamicValidationEngine } from './dynamicValidationEngine'

export class RealTimeValidator {
  private validationEngine: DynamicValidationEngine
  private options: RealTimeValidationOptions
  private validationStates: Map<string, ValidationState> = new Map()
  private debounceTimers: Map<string, number> = new Map()
  private eventListeners: Map<string, ValidationEventListener[]> = new Map()

  constructor(options: Partial<RealTimeValidationOptions> = {}) {
    this.validationEngine = new DynamicValidationEngine()
    this.options = {
      debounce_delay: 300,
      validate_on_blur: true,
      validate_on_change: true,
      show_success_feedback: false,
      auto_focus_error: true,
      ...options
    }
  }

  /**
   * 注册字段实时校验
   */
  registerField(
    fieldName: string,
    ruleKey: string,
    element?: HTMLElement
  ): void {
    // 初始化字段状态
    this.validationStates.set(fieldName, {
      is_validating: false,
      has_errors: false,
      error_count: 0,
      warning_count: 0
    })

    // 绑定事件监听器
    if (element) {
      this.bindFieldEvents(element, fieldName, ruleKey)
    }

    console.log(`字段 ${fieldName} 实时校验已注册`)
  }

  /**
   * 绑定字段事件
   */
  private bindFieldEvents(
    element: HTMLElement,
    fieldName: string,
    ruleKey: string
  ): void {
    // 输入事件（实时校验）
    if (this.options.validate_on_change) {
      element.addEventListener('input', (event) => {
        const target = event.target as HTMLInputElement
        this.validateFieldWithDebounce(fieldName, target.value, ruleKey)
      })
    }

    // 失焦事件（立即校验）
    if (this.options.validate_on_blur) {
      element.addEventListener('blur', (event) => {
        const target = event.target as HTMLInputElement
        this.validateFieldImmediate(fieldName, target.value, ruleKey)
      })
    }

    // 聚焦事件（清除之前的错误状态）
    element.addEventListener('focus', () => {
      this.clearFieldValidationState(fieldName)
    })
  }

  /**
   * 防抖校验
   */
  private validateFieldWithDebounce(
    fieldName: string,
    value: any,
    ruleKey: string
  ): void {
    // 清除之前的定时器
    const existingTimer = this.debounceTimers.get(fieldName)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // 设置新的定时器
    const timer = window.setTimeout(() => {
      this.validateFieldImmediate(fieldName, value, ruleKey)
      this.debounceTimers.delete(fieldName)
    }, this.options.debounce_delay)

    this.debounceTimers.set(fieldName, timer)
  }

  /**
   * 立即校验字段
   */
  private async validateFieldImmediate(
    fieldName: string,
    value: any,
    ruleKey: string
  ): Promise<ValidationResult> {
    // 更新校验状态
    const state = this.validationStates.get(fieldName)
    if (state) {
      state.is_validating = true
      state.last_validation_time = new Date()
    }

    try {
      const result = await this.validationEngine.validateField(fieldName, value, ruleKey)

      // 更新校验状态
      if (state) {
        state.is_validating = false
        state.has_errors = !result.valid
        state.error_count = result.errors.length
        state.warning_count = result.warnings.length
        state.validation_duration = result.duration
      }

      // 发送校验事件
      this.emitEvent('field_validated', {
        field_name: fieldName,
        result,
        timestamp: new Date()
      })

      // 处理校验结果
      this.handleValidationResult(fieldName, result)

      return result

    } catch (error) {
      console.error(`字段 ${fieldName} 校验失败:`, error)

      // 更新错误状态
      if (state) {
        state.is_validating = false
        state.has_errors = true
        state.error_count = 1
      }

      throw error
    }
  }

  /**
   * 处理校验结果
   */
  private handleValidationResult(fieldName: string, result: ValidationResult): void {
    const fieldElement = this.findFieldElement(fieldName)
    if (!fieldElement) return

    // 清除之前的样式
    this.clearFieldStyles(fieldElement)

    if (!result.valid) {
      // 显示错误状态
      this.showFieldError(fieldElement, result.errors[0])

      // 自动聚焦到错误字段
      if (this.options.auto_focus_error) {
        fieldElement.focus()
      }
    } else if (this.options.show_success_feedback) {
      // 显示成功状态
      this.showFieldSuccess(fieldElement)
    }
  }

  /**
   * 显示字段错误
   */
  private showFieldError(element: HTMLElement, error: any): void {
    // 添加错误样式
    element.classList.add('validation-error')

    // 创建或更新错误提示
    let errorElement = element.parentElement?.querySelector('.validation-error-message')
    if (!errorElement) {
      errorElement = document.createElement('div')
      errorElement.className = 'validation-error-message'
      element.parentElement?.appendChild(errorElement)
    }

    errorElement.textContent = error.error_message
    errorElement.setAttribute('title', error.suggestions.join('; '))
  }

  /**
   * 显示字段成功
   */
  private showFieldSuccess(element: HTMLElement): void {
    element.classList.add('validation-success')

    // 移除错误提示
    const errorElement = element.parentElement?.querySelector('.validation-error-message')
    if (errorElement) {
      errorElement.remove()
    }
  }

  /**
   * 清除字段样式
   */
  private clearFieldStyles(element: HTMLElement): void {
    element.classList.remove('validation-error', 'validation-success')

    // 移除错误提示
    const errorElement = element.parentElement?.querySelector('.validation-error-message')
    if (errorElement) {
      errorElement.remove()
    }
  }

  /**
   * 清除字段校验状态
   */
  private clearFieldValidationState(fieldName: string): void {
    const state = this.validationStates.get(fieldName)
    if (state) {
      state.has_errors = false
      state.error_count = 0
      state.warning_count = 0
    }

    // 清除视觉反馈
    const fieldElement = this.findFieldElement(fieldName)
    if (fieldElement) {
      this.clearFieldStyles(fieldElement)
    }
  }

  /**
   * 查找字段元素
   */
  private findFieldElement(fieldName: string): HTMLElement | null {
    // 尝试多种选择器
    const selectors = [
      `[name="${fieldName}"]`,
      `[data-field="${fieldName}"]`,
      `#${fieldName}`,
      `.field-${fieldName} input`,
      `.field-${fieldName} select`,
      `.field-${fieldName} textarea`
    ]

    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLElement
      if (element) return element
    }

    return null
  }

  /**
   * 批量校验表单
   */
  async validateForm(
    formData: Record<string, any>,
    ruleKey: string
  ): Promise<ValidationResult> {
    try {
      const result = await this.validationEngine.validateForm(formData, ruleKey)

      // 更新所有字段的校验状态
      for (const fieldName of Object.keys(formData)) {
        const fieldErrors = result.errors.filter(e => e.field_name === fieldName)
        const fieldResult: ValidationResult = {
          valid: fieldErrors.length === 0,
          errors: fieldErrors,
          warnings: [],
          field_count: 1,
          validated_fields: [fieldName],
          duration: result.duration
        }

        this.handleValidationResult(fieldName, fieldResult)
      }

      // 发送表单校验事件
      this.emitEvent('form_validated', {
        result,
        timestamp: new Date()
      })

      return result

    } catch (error) {
      console.error('表单校验失败:', error)
      throw error
    }
  }

  /**
   * 获取字段校验状态
   */
  getFieldState(fieldName: string): ValidationState | null {
    return this.validationStates.get(fieldName) || null
  }

  /**
   * 获取所有字段状态
   */
  getAllFieldStates(): Map<string, ValidationState> {
    return new Map(this.validationStates)
  }

  /**
   * 重置字段校验
   */
  resetField(fieldName: string): void {
    // 清除定时器
    const timer = this.debounceTimers.get(fieldName)
    if (timer) {
      clearTimeout(timer)
      this.debounceTimers.delete(fieldName)
    }

    // 重置状态
    this.clearFieldValidationState(fieldName)
  }

  /**
   * 重置所有字段
   */
  resetAllFields(): void {
    // 清除所有定时器
    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()

    // 重置所有状态
    this.validationStates.forEach((_, fieldName) => {
      this.clearFieldValidationState(fieldName)
    })
  }

  /**
   * 发送事件
   */
  private emitEvent(eventType: string, data: any): void {
    const listeners = this.eventListeners.get(eventType) || []
    listeners.forEach(listener => {
      try {
        listener({ type: eventType, ...data } as ValidationEvent)
      } catch (error) {
        console.error('事件监听器执行失败:', error)
      }
    })
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventType: string, listener: ValidationEventListener): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, [])
    }
    this.eventListeners.get(eventType)!.push(listener)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventType: string, listener: ValidationEventListener): void {
    const listeners = this.eventListeners.get(eventType) || []
    const index = listeners.indexOf(listener)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    // 清除所有定时器
    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()

    // 清除状态
    this.validationStates.clear()
    this.eventListeners.clear()

    console.log('实时校验器已销毁')
  }
}

// 创建全局实例
export const realTimeValidator = new RealTimeValidator()
