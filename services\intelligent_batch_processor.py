"""
智能分批处理器
集成自适应批次大小管理和性能监控，提供智能分批处理功能
支持流式处理大数据量，避免内存压力，并实时调整批处理策略
"""

import asyncio
import gc
import time
import uuid
from collections import deque
from collections.abc import AsyncGenerator, Callable
from dataclasses import dataclass
from typing import Any

from config.settings import settings
from core.http_retry.retry_client import RetryClient
from core.logging.logging_system import log as logger
from services.adaptive_batch_manager import adaptive_batch_manager


@dataclass
class BatchProcessingResult:
    """批处理结果"""

    batch_id: str  # 批次ID
    batch_size: int  # 批次大小
    processing_time: float  # 处理时间
    success: bool  # 是否成功
    processed_items: int  # 已处理项目数
    failed_items: int  # 失败项目数
    error_message: str = ""  # 错误信息
    memory_usage_mb: float = 0.0  # 内存使用量
    retry_count: int = 0  # 重试次数


@dataclass
class ProcessingProgress:
    """处理进度信息"""

    total_items: int  # 总项目数
    processed_items: int  # 已处理项目数
    current_batch: int  # 当前批次号
    total_batches: int  # 总批次数
    success_rate: float  # 成功率
    avg_processing_time: float  # 平均处理时间
    estimated_remaining_time: float  # 预估剩余时间
    current_batch_size: int  # 当前批次大小
    memory_usage_mb: float  # 当前内存使用量


@dataclass
class ProcessingStats:
    """处理统计信息"""

    total_batches: int  # 总批次数
    successful_batches: int  # 成功批次数
    failed_batches: int  # 失败批次数
    total_items: int  # 总项目数
    successful_items: int  # 成功项目数
    failed_items: int  # 失败项目数
    total_processing_time: float  # 总处理时间
    avg_batch_processing_time: float  # 平均批次处理时间
    throughput: float  # 吞吐量（项目/秒）
    memory_peak_usage: float  # 内存峰值使用量
    batch_size_adjustments: int  # 批次大小调整次数


class IntelligentBatchProcessor:
    """
    智能分批处理器

    集成自适应批次大小管理和性能监控，提供智能分批处理功能。
    支持流式处理大数据量，避免内存压力，并实时调整批处理策略。
    """

    def __init__(
        self,
        adaptive_manager: Any | None = None,
        performance_monitor: Any | None = None,
        retry_client: RetryClient | None = None,
        enable_memory_monitoring: bool = True,
        enable_progress_tracking: bool = True,
    ):
        """
        初始化智能分批处理器

        Args:
            adaptive_manager: 自适应批次大小管理器，None表示使用全局实例
            performance_monitor: 性能监控器，None表示使用全局实例
            retry_client: 重试客户端，None表示创建新实例
            enable_memory_monitoring: 是否启用内存监控
            enable_progress_tracking: 是否启用进度跟踪
        """
        # 核心组件
        self.adaptive_manager = adaptive_manager or adaptive_batch_manager
        self.performance_monitor = performance_monitor or performance_monitor

        # 重试客户端（用于HTTP请求重试）
        self.retry_client = retry_client or RetryClient(
            circuit_breaker_name="intelligent_batch_processor", enable_circuit_breaker=True
        )

        # 功能开关
        self.enable_memory_monitoring = enable_memory_monitoring
        self.enable_progress_tracking = enable_progress_tracking

        # 处理状态
        self.is_processing = False
        self.current_session_id = None

        # 统计信息
        self.processing_stats = ProcessingStats(
            total_batches=0,
            successful_batches=0,
            failed_batches=0,
            total_items=0,
            successful_items=0,
            failed_items=0,
            total_processing_time=0.0,
            avg_batch_processing_time=0.0,
            throughput=0.0,
            memory_peak_usage=0.0,
            batch_size_adjustments=0,
        )

        # 进度跟踪
        self.progress_history = deque(maxlen=100)
        self.batch_results_history = deque(maxlen=50)

        # 内存监控配置
        self.memory_threshold_mb = getattr(settings, "INTELLIGENT_BATCH_MEMORY_THRESHOLD_MB", 1024)  # 1GB
        self.memory_cleanup_interval = getattr(settings, "INTELLIGENT_BATCH_MEMORY_CLEANUP_INTERVAL", 10)  # 每10个批次

        logger.info(
            f"IntelligentBatchProcessor initialized: "
            f"memory_monitoring={self.enable_memory_monitoring}, "
            f"progress_tracking={self.enable_progress_tracking}, "
            f"memory_threshold={self.memory_threshold_mb}MB"
        )

    async def process_registration_data_adaptively(
        self,
        data_list: list[Any],
        processor_func: Callable,
        session_id: str | None = None,
        max_retries: int = 3,
        enable_streaming: bool = True,
    ) -> AsyncGenerator[BatchProcessingResult, None]:
        """
        自适应分批处理注册数据

        Args:
            data_list: 待处理的数据列表
            processor_func: 处理函数，接收批次数据并返回处理结果
            session_id: 会话ID，None表示自动生成
            max_retries: 最大重试次数
            enable_streaming: 是否启用流式处理

        Yields:
            BatchProcessingResult: 批处理结果
        """
        if self.is_processing:
            raise RuntimeError("Another processing session is already running")

        self.is_processing = True
        self.current_session_id = session_id or str(uuid.uuid4())

        try:
            logger.info(
                f"Starting adaptive batch processing: session_id={self.current_session_id}, "
                f"total_items={len(data_list)}, streaming={enable_streaming}"
            )

            # 重置统计信息
            self._reset_session_stats()

            # 流式处理或批量处理
            if enable_streaming:
                async for result in self._process_streaming(data_list, processor_func, max_retries):
                    yield result
            else:
                async for result in self._process_batch_mode(data_list, processor_func, max_retries):
                    yield result

        except Exception as e:
            logger.error(f"Batch processing failed: {e}", exc_info=True)
            raise
        finally:
            self.is_processing = False
            self.current_session_id = None

            # 最终统计
            self._finalize_session_stats()
            logger.info(f"Batch processing session completed: {self._get_session_summary()}")

    async def _process_streaming(
        self, data_list: list[Any], processor_func: Callable, max_retries: int
    ) -> AsyncGenerator[BatchProcessingResult, None]:
        """
        流式处理模式：逐批处理，实时调整批次大小
        """
        total_items = len(data_list)
        processed_items = 0
        batch_number = 0

        while processed_items < total_items:
            # 获取当前最优批次大小
            current_batch_size = self.adaptive_manager.get_current_batch_size()

            # 创建当前批次
            batch_start = processed_items
            batch_end = min(processed_items + current_batch_size, total_items)
            batch_data = data_list[batch_start:batch_end]

            batch_number += 1
            batch_id = f"{self.current_session_id}_batch_{batch_number}"

            # 处理批次
            result = await self._process_single_batch(batch_id, batch_data, processor_func, max_retries, batch_number)

            # 更新进度
            processed_items = batch_end
            self._update_progress(processed_items, total_items, batch_number, result)

            # 根据处理结果调整批次大小
            self._adjust_batch_size_based_on_result(result)

            # 内存清理
            if batch_number % self.memory_cleanup_interval == 0:
                await self._perform_memory_cleanup()

            yield result

    async def _process_batch_mode(
        self, data_list: list[Any], processor_func: Callable, max_retries: int
    ) -> AsyncGenerator[BatchProcessingResult, None]:
        """
        批量处理模式：预先分批，固定批次大小
        """
        # 获取初始批次大小
        batch_size = self.adaptive_manager.get_current_batch_size()

        # 预先创建所有批次
        batches = self._create_adaptive_batches(data_list, batch_size)
        total_batches = len(batches)

        logger.info(f"Created {total_batches} batches with initial size {batch_size}")

        for batch_number, batch_data in enumerate(batches, 1):
            batch_id = f"{self.current_session_id}_batch_{batch_number}"

            # 处理批次
            result = await self._process_single_batch(batch_id, batch_data, processor_func, max_retries, batch_number)

            # 更新进度
            processed_items = sum(len(batch) for batch in batches[:batch_number])
            self._update_progress(processed_items, len(data_list), batch_number, result)

            yield result

    def _create_adaptive_batches(self, data_list: list[Any], initial_batch_size: int) -> list[list[Any]]:
        """
        创建自适应批次

        Args:
            data_list: 数据列表
            initial_batch_size: 初始批次大小

        Returns:
            批次列表
        """
        batches = []
        total_items = len(data_list)
        current_pos = 0

        while current_pos < total_items:
            # 动态调整批次大小（可以根据数据特征调整）
            current_batch_size = self._calculate_dynamic_batch_size(current_pos, total_items, initial_batch_size)

            # 创建批次
            batch_end = min(current_pos + current_batch_size, total_items)
            batch = data_list[current_pos:batch_end]
            batches.append(batch)

            current_pos = batch_end

        return batches

    def _calculate_dynamic_batch_size(self, current_pos: int, total_items: int, base_batch_size: int) -> int:
        """
        计算动态批次大小

        Args:
            current_pos: 当前处理位置
            total_items: 总项目数
            base_batch_size: 基础批次大小

        Returns:
            动态调整后的批次大小
        """
        # 基于剩余数据量调整
        remaining_items = total_items - current_pos

        if remaining_items <= base_batch_size * 1.2:
            # 如果剩余数据不多，使用较小的批次避免最后一个批次过小
            return min(remaining_items, max(base_batch_size // 2, 10))
        else:
            # 正常情况使用基础批次大小
            return base_batch_size

    async def _process_single_batch(
        self, batch_id: str, batch_data: list[Any], processor_func: Callable, max_retries: int, batch_number: int
    ) -> BatchProcessingResult:
        """
        处理单个批次

        Args:
            batch_id: 批次ID
            batch_data: 批次数据
            processor_func: 处理函数
            max_retries: 最大重试次数
            batch_number: 批次号

        Returns:
            批处理结果
        """
        start_time = time.perf_counter()
        retry_count = 0
        last_error = None

        # 获取初始内存使用量
        initial_memory = self._get_current_memory_usage()

        while retry_count <= max_retries:
            try:
                logger.debug(
                    f"Processing batch {batch_id}: size={len(batch_data)}, "
                    f"attempt={retry_count + 1}/{max_retries + 1}"
                )

                # 执行处理函数
                processing_result = await self._execute_with_monitoring(processor_func, batch_data, batch_id)

                # 计算处理时间和内存使用
                processing_time = time.perf_counter() - start_time
                current_memory = self._get_current_memory_usage()
                memory_usage = max(0, current_memory - initial_memory)

                # 创建成功结果
                result = BatchProcessingResult(
                    batch_id=batch_id,
                    batch_size=len(batch_data),
                    processing_time=processing_time,
                    success=True,
                    processed_items=len(batch_data),
                    failed_items=0,
                    memory_usage_mb=memory_usage,
                    retry_count=retry_count,
                )

                # 记录性能指标
                self._record_batch_performance(result)

                # 更新统计信息
                self._update_batch_stats(result, True)

                logger.debug(
                    f"Batch {batch_id} completed successfully: "
                    f"time={processing_time:.3f}s, memory={memory_usage:.1f}MB"
                )

                return result

            except Exception as e:
                retry_count += 1
                last_error = e
                processing_time = time.perf_counter() - start_time

                logger.warning(f"Batch {batch_id} failed (attempt {retry_count}/{max_retries + 1}): {e}")

                # 如果还有重试机会，等待一段时间
                if retry_count <= max_retries:
                    await self._calculate_retry_delay(retry_count, e)

        # 所有重试都失败了
        processing_time = time.perf_counter() - start_time
        current_memory = self._get_current_memory_usage()
        memory_usage = max(0, current_memory - initial_memory)

        result = BatchProcessingResult(
            batch_id=batch_id,
            batch_size=len(batch_data),
            processing_time=processing_time,
            success=False,
            processed_items=0,
            failed_items=len(batch_data),
            error_message=str(last_error),
            memory_usage_mb=memory_usage,
            retry_count=retry_count - 1,
        )

        # 记录失败的性能指标
        self._record_batch_performance(result)

        # 更新统计信息
        self._update_batch_stats(result, False)

        logger.error(f"Batch {batch_id} failed after {retry_count - 1} retries: {last_error}")

        return result

    async def _execute_with_monitoring(self, processor_func: Callable, batch_data: list[Any], batch_id: str) -> Any:
        """
        在监控下执行处理函数

        Args:
            processor_func: 处理函数
            batch_data: 批次数据
            batch_id: 批次ID

        Returns:
            处理结果
        """
        # 检查内存使用情况
        if self.enable_memory_monitoring:
            current_memory = self._get_current_memory_usage()
            if current_memory > self.memory_threshold_mb:
                logger.warning(
                    f"Memory usage high ({current_memory:.1f}MB > {self.memory_threshold_mb}MB), "
                    f"performing cleanup before processing batch {batch_id}"
                )
                await self._perform_memory_cleanup()

        # 执行处理函数
        if asyncio.iscoroutinefunction(processor_func):
            result = await processor_func(batch_data)
        else:
            result = processor_func(batch_data)

        return result

    async def _calculate_retry_delay(self, retry_count: int, error: Exception) -> None:
        """
        计算重试延迟时间

        Args:
            retry_count: 重试次数
            error: 错误信息
        """
        # 基础延迟时间（指数退避）
        base_delay = 1.0
        max_delay = 30.0
        backoff_factor = 2.0

        # 计算延迟时间
        delay = min(base_delay * (backoff_factor ** (retry_count - 1)), max_delay)

        # 根据错误类型调整延迟
        if "timeout" in str(error).lower():
            delay *= 1.5  # 超时错误延迟更长
        elif "connection" in str(error).lower():
            delay *= 2.0  # 连接错误延迟更长

        logger.debug(f"Retry delay: {delay:.2f}s for retry {retry_count}")
        await asyncio.sleep(delay)

    def _get_current_memory_usage(self) -> float:
        """
        获取当前内存使用量（MB）

        Returns:
            内存使用量（MB）
        """
        try:
            import psutil

            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except ImportError:
            # 如果psutil不可用，返回0
            return 0.0
        except Exception as e:
            logger.debug(f"Failed to get memory usage: {e}")
            return 0.0

    async def _perform_memory_cleanup(self) -> None:
        """执行内存清理"""
        try:
            # 强制垃圾回收
            gc.collect()

            # 记录清理后的内存使用量
            memory_after = self._get_current_memory_usage()
            logger.debug(f"Memory cleanup completed, current usage: {memory_after:.1f}MB")

        except Exception as e:
            logger.warning(f"Memory cleanup failed: {e}")

    def _record_batch_performance(self, result: BatchProcessingResult) -> None:
        """
        记录批次性能指标到性能监控器

        Args:
            result: 批处理结果
        """
        try:
            # 记录到性能监控器
            self.performance_monitor.record_registration_task_performance(
                task_id=result.batch_id,
                batch_size=result.batch_size,
                processing_time=result.processing_time,
                success=result.success,
                error_message=result.error_message,
            )

            # 记录到历史
            self.batch_results_history.append(result)

        except Exception as e:
            logger.warning(f"Failed to record batch performance: {e}")

    def _adjust_batch_size_based_on_result(self, result: BatchProcessingResult) -> None:
        """
        根据批处理结果调整批次大小

        Args:
            result: 批处理结果
        """
        try:
            # 调用自适应管理器调整批次大小
            adjusted = self.adaptive_manager.adjust_based_on_performance(
                batch_size=result.batch_size, processing_time=result.processing_time, success=result.success
            )

            if adjusted:
                self.processing_stats.batch_size_adjustments += 1
                new_size = self.adaptive_manager.get_current_batch_size()
                logger.debug(f"Batch size adjusted to {new_size} based on performance")

        except Exception as e:
            logger.warning(f"Failed to adjust batch size: {e}")

    def _update_progress(
        self, processed_items: int, total_items: int, current_batch: int, result: BatchProcessingResult
    ) -> None:
        """
        更新处理进度

        Args:
            processed_items: 已处理项目数
            total_items: 总项目数
            current_batch: 当前批次号
            result: 批处理结果
        """
        if not self.enable_progress_tracking:
            return

        try:
            # 计算进度指标
            progress_rate = processed_items / total_items if total_items > 0 else 0.0

            # 计算成功率
            successful_batches = sum(1 for r in self.batch_results_history if r.success)
            total_batches = len(self.batch_results_history)
            success_rate = successful_batches / total_batches if total_batches > 0 else 1.0

            # 计算平均处理时间
            processing_times = [r.processing_time for r in self.batch_results_history if r.success]
            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0.0

            # 估算剩余时间
            remaining_items = total_items - processed_items
            if avg_processing_time > 0 and result.batch_size > 0:
                estimated_remaining_batches = remaining_items / result.batch_size
                estimated_remaining_time = estimated_remaining_batches * avg_processing_time
            else:
                estimated_remaining_time = 0.0

            # 创建进度信息
            progress = ProcessingProgress(
                total_items=total_items,
                processed_items=processed_items,
                current_batch=current_batch,
                total_batches=0,  # 流式处理时无法预知总批次数
                success_rate=success_rate,
                avg_processing_time=avg_processing_time,
                estimated_remaining_time=estimated_remaining_time,
                current_batch_size=result.batch_size,
                memory_usage_mb=result.memory_usage_mb,
            )

            # 记录进度历史
            self.progress_history.append(progress)

            # 记录进度日志
            if current_batch % 10 == 0 or progress_rate >= 1.0:
                logger.info(
                    f"Processing progress: {progress_rate:.1%} "
                    f"({processed_items}/{total_items}), "
                    f"batch {current_batch}, "
                    f"success_rate: {success_rate:.1%}, "
                    f"avg_time: {avg_processing_time:.2f}s"
                )

        except Exception as e:
            logger.warning(f"Failed to update progress: {e}")

    def _update_batch_stats(self, result: BatchProcessingResult, success: bool) -> None:
        """
        更新批次统计信息

        Args:
            result: 批处理结果
            success: 是否成功
        """
        self.processing_stats.total_batches += 1
        self.processing_stats.total_items += result.batch_size
        self.processing_stats.total_processing_time += result.processing_time

        if success:
            self.processing_stats.successful_batches += 1
            self.processing_stats.successful_items += result.processed_items
        else:
            self.processing_stats.failed_batches += 1
            self.processing_stats.failed_items += result.failed_items

        # 更新内存峰值使用量
        if result.memory_usage_mb > self.processing_stats.memory_peak_usage:
            self.processing_stats.memory_peak_usage = result.memory_usage_mb

        # 更新平均处理时间
        if self.processing_stats.total_batches > 0:
            self.processing_stats.avg_batch_processing_time = (
                self.processing_stats.total_processing_time / self.processing_stats.total_batches
            )

    def _reset_session_stats(self) -> None:
        """重置会话统计信息"""
        self.processing_stats = ProcessingStats(
            total_batches=0,
            successful_batches=0,
            failed_batches=0,
            total_items=0,
            successful_items=0,
            failed_items=0,
            total_processing_time=0.0,
            avg_batch_processing_time=0.0,
            throughput=0.0,
            memory_peak_usage=0.0,
            batch_size_adjustments=0,
        )
        self.progress_history.clear()
        self.batch_results_history.clear()

    def _finalize_session_stats(self) -> None:
        """完成会话统计"""
        if self.processing_stats.total_processing_time > 0:
            self.processing_stats.throughput = (
                self.processing_stats.successful_items / self.processing_stats.total_processing_time
            )

    def _get_session_summary(self) -> str:
        """获取会话摘要"""
        stats = self.processing_stats
        success_rate = stats.successful_batches / stats.total_batches if stats.total_batches > 0 else 0.0

        return (
            f"total_batches={stats.total_batches}, "
            f"success_rate={success_rate:.1%}, "
            f"total_items={stats.total_items}, "
            f"throughput={stats.throughput:.2f} items/s, "
            f"avg_batch_time={stats.avg_batch_processing_time:.3f}s, "
            f"memory_peak={stats.memory_peak_usage:.1f}MB, "
            f"adjustments={stats.batch_size_adjustments}"
        )

    # 公共接口方法

    def get_processing_stats(self) -> ProcessingStats:
        """
        获取处理统计信息

        Returns:
            处理统计信息
        """
        return ProcessingStats(
            total_batches=self.processing_stats.total_batches,
            successful_batches=self.processing_stats.successful_batches,
            failed_batches=self.processing_stats.failed_batches,
            total_items=self.processing_stats.total_items,
            successful_items=self.processing_stats.successful_items,
            failed_items=self.processing_stats.failed_items,
            total_processing_time=self.processing_stats.total_processing_time,
            avg_batch_processing_time=self.processing_stats.avg_batch_processing_time,
            throughput=self.processing_stats.throughput,
            memory_peak_usage=self.processing_stats.memory_peak_usage,
            batch_size_adjustments=self.processing_stats.batch_size_adjustments,
        )

    def get_current_progress(self) -> ProcessingProgress | None:
        """
        获取当前处理进度

        Returns:
            当前处理进度，如果没有进度信息则返回None
        """
        if self.progress_history:
            return self.progress_history[-1]
        return None

    def get_batch_results_history(self, limit: int | None = None) -> list[BatchProcessingResult]:
        """
        获取批处理结果历史

        Args:
            limit: 返回结果数量限制，None表示返回所有结果

        Returns:
            批处理结果历史列表
        """
        history = list(self.batch_results_history)
        if limit is not None and limit > 0:
            history = history[-limit:]
        return history

    def is_processing_active(self) -> bool:
        """
        检查是否正在处理

        Returns:
            是否正在处理
        """
        return self.is_processing

    def get_current_session_id(self) -> str | None:
        """
        获取当前会话ID

        Returns:
            当前会话ID，如果没有活动会话则返回None
        """
        return self.current_session_id


# 全局智能分批处理器实例
intelligent_batch_processor = IntelligentBatchProcessor()
