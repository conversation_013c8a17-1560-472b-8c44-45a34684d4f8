#!/usr/bin/env python3
"""
数据库字段标准化集成测试
验证整个标准化实施的完整性和正确性
"""

from sqlalchemy.orm import Session

from models.database import (
    FieldTypeEnum,
    RuleDetail,
    RuleDetailStatusEnum,
    RuleFieldMetadata,
    RuleTemplate,
    RuleTemplateStatusEnum,
)


class TestDatabaseFieldStandardization:
    """数据库字段标准化集成测试类"""

    def test_rule_template_model(self, integration_db_session: Session):
        """测试 RuleTemplate 模型功能"""
        template = RuleTemplate(
            rule_key="test_integration",
            rule_type="integration_test",
            name="集成测试规则模板",
            description="用于集成测试的规则模板",
            status=RuleTemplateStatusEnum.READY,
        )

        integration_db_session.add(template)
        integration_db_session.flush()

        assert template.id is not None
        assert template.rule_key == "test_integration"
        assert template.status == RuleTemplateStatusEnum.READY

        # 测试 to_dict 方法
        template_dict = template.to_dict()
        assert "rule_key" in template_dict
        assert "status" in template_dict
        assert template_dict["rule_key"] == "test_integration"

    def test_rule_detail_model(self, integration_db_session: Session):
        """测试 RuleDetail 模型功能"""
        detail = RuleDetail(
            rule_id="test_integration_001",
            rule_key="test_integration",
            rule_name="集成测试规则明细",
            level1="测试一级错误",
            level2="测试二级错误",
            level3="测试三级错误",
            error_reason="集成测试错误原因",
            degree="轻微",
            reference="集成测试参考资料",
            detail_position="测试位置",
            prompted_fields1="test_field",
            type="集成测试类型",
            pos="集成测试业务",
            applicableArea="全国",
            default_use="是",
            start_date="2025-01-01",
            end_date="2025-12-31",
            status=RuleDetailStatusEnum.ACTIVE,
        )

        integration_db_session.add(detail)
        integration_db_session.flush()

        assert detail.id is not None
        assert detail.level1 == "测试一级错误"
        assert detail.degree == "轻微"
        assert detail.reference == "集成测试参考资料"

        # 测试 to_dict 方法
        detail_dict = detail.to_dict()
        required_fields = ["level1", "level2", "level3", "degree", "reference"]
        for field in required_fields:
            assert field in detail_dict, f"字段 {field} 不在 to_dict 结果中"

    def test_rule_field_metadata_model(self, integration_db_session: Session):
        """测试 RuleFieldMetadata 模型功能"""
        metadata = RuleFieldMetadata(
            rule_key="test_integration",
            field_name="level1",
            field_type=FieldTypeEnum.string,
            is_required=True,
            is_fixed_field=True,
            display_name="一级错误类型",
            description="规则的一级错误类型分类",
        )

        integration_db_session.add(metadata)
        integration_db_session.flush()

        assert metadata.id is not None
        assert metadata.field_name == "level1"
        assert metadata.field_type == FieldTypeEnum.string

        # 测试 to_dict 方法
        metadata_dict = metadata.to_dict()
        assert "field_name" in metadata_dict
        assert "field_type" in metadata_dict
        assert metadata_dict["field_name"] == "level1"

    def test_data_relationships(self, integration_db_session: Session):
        """测试数据关联性"""
        # 创建测试数据
        template = RuleTemplate(
            rule_key="test_relationship",
            rule_type="relationship_test",
            name="关联测试规则模板",
            status=RuleTemplateStatusEnum.READY,
        )
        integration_db_session.add(template)
        integration_db_session.flush()

        detail = RuleDetail(
            rule_id="test_relationship_001",
            rule_key="test_relationship",
            rule_name="关联测试规则明细",
            level1="关联测试一级",
            level2="关联测试二级",
            level3="关联测试三级",
            error_reason="关联测试原因",
            degree="严重",
            reference="关联测试参考",
            detail_position="关联测试位置",
            prompted_fields1="test_field",
            type="关联测试类型",
            pos="关联测试业务",
            applicableArea="全国",
            default_use="是",
            start_date="2025-01-01",
            end_date="2025-12-31",
        )
        integration_db_session.add(detail)
        integration_db_session.flush()

        metadata = RuleFieldMetadata(
            rule_key="test_relationship",
            field_name="level1",
            field_type=FieldTypeEnum.string,
            is_required=True,
            display_name="一级错误类型",
        )
        integration_db_session.add(metadata)
        integration_db_session.flush()

        # 验证关联查询
        template_count = (
            integration_db_session.query(RuleTemplate).filter(RuleTemplate.rule_key == "test_relationship").count()
        )

        detail_count = (
            integration_db_session.query(RuleDetail).filter(RuleDetail.rule_key == "test_relationship").count()
        )

        metadata_count = (
            integration_db_session.query(RuleFieldMetadata)
            .filter(RuleFieldMetadata.rule_key == "test_relationship")
            .count()
        )

        assert template_count == 1
        assert detail_count == 1
        assert metadata_count == 1

    def test_standard_field_names_consistency(self, integration_db_session: Session):
        """测试标准字段名的一致性"""
        # 创建一个包含所有标准字段的 RuleDetail 实例
        detail = RuleDetail(
            rule_id="consistency_test",
            rule_key="consistency_test",
            rule_name="一致性测试",
            level1="一级",  # 标准字段名
            level2="二级",  # 标准字段名
            level3="三级",  # 标准字段名
            error_reason="原因",
            degree="程度",  # 标准字段名
            reference="参考",  # 标准字段名
            detail_position="位置",  # 标准字段名
            prompted_fields1="字段1",  # 标准字段名
            type="类型",  # 标准字段名
            pos="业务",  # 标准字段名
            applicableArea="地区",  # 标准字段名
            default_use="默认",  # 标准字段名
            start_date="2025-01-01",
            end_date="2025-12-31",
        )

        integration_db_session.add(detail)
        integration_db_session.flush()

        # 验证所有标准字段都可以正常访问
        assert hasattr(detail, "level1")
        assert hasattr(detail, "level2")
        assert hasattr(detail, "level3")
        assert hasattr(detail, "degree")
        assert hasattr(detail, "reference")
        assert hasattr(detail, "detail_position")
        assert hasattr(detail, "prompted_fields1")
        assert hasattr(detail, "type")
        assert hasattr(detail, "pos")
        assert hasattr(detail, "applicableArea")
        assert hasattr(detail, "default_use")

        # 验证旧字段名不存在
        assert not hasattr(detail, "error_level_1")
        assert not hasattr(detail, "error_level_2")
        assert not hasattr(detail, "error_level_3")
        assert not hasattr(detail, "error_severity")
        assert not hasattr(detail, "quality_basis")
        assert not hasattr(detail, "location_desc")
