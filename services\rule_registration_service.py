"""
规则注册服务核心模块
负责与外部规则注册服务进行通信，实现规则的注册、更新和删除操作
"""

import json
import time
from pathlib import Path
from typing import Any
from urllib.parse import urljoin

import httpx

from config.settings import get_settings
from core.http_retry.retry_client import RetryClient
from core.logging.logging_system import log as logger
from services.service_degradation_manager import (
    DegradationConfig,
    ServiceDegradationManager,
)


class RuleRegistrationError(Exception):
    """规则注册相关异常"""

    def __init__(self, message: str, error_code: str | None = None, details: dict | None = None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}


class RuleRegistrationService:
    """
    规则注册服务核心类

    负责与外部规则注册服务进行HTTP通信，提供规则注册、健康检查等功能。
    集成现有的RetryClient，支持重试和断路器机制。
    """

    def __init__(self):
        """初始化规则注册服务"""
        self.settings = get_settings()

        # 初始化HTTP重试客户端
        self.retry_client = RetryClient(
            base_client=httpx.AsyncClient(
                timeout=httpx.Timeout(self.settings.RULE_REGISTRATION_TIMEOUT),
                headers={"Content-Type": "application/json", "User-Agent": "RuleRegistrationService/1.0"},
                # 强制使用HTTP/1.1，避免与简单HTTP服务器的兼容性问题
                http2=False,
                # 设置连接限制，避免连接池问题
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
                # 使用异步传输，禁用连接复用
                transport=httpx.AsyncHTTPTransport(retries=0),
            ),
            retry_config={
                "enabled": True,
                "max_attempts": self.settings.RULE_REGISTRATION_MAX_RETRIES,
                "base_delay": 1.0,
                "max_delay": 30.0,
                "backoff_factor": 2.0,
                "jitter": True,
                "retryable_status_codes": [408, 429, 500, 502, 503, 504],
                "retry_on_connection_error": True,
                "retry_on_timeout_error": True,
            },
            circuit_breaker_name="rule_registration",
            enable_circuit_breaker=True,
        )

        # 初始化服务降级管理器
        degradation_config = DegradationConfig(
            health_check_interval=getattr(self.settings, "RULE_REGISTRATION_HEALTH_CHECK_INTERVAL", 30.0),
            health_check_timeout=getattr(self.settings, "RULE_REGISTRATION_HEALTH_CHECK_TIMEOUT", 5.0),
            failure_threshold=getattr(self.settings, "RULE_REGISTRATION_CIRCUIT_BREAKER_FAILURE_THRESHOLD", 0.5),
            min_requests=getattr(self.settings, "RULE_REGISTRATION_CIRCUIT_BREAKER_MIN_REQUESTS", 10),
            timeout_duration=getattr(self.settings, "RULE_REGISTRATION_CIRCUIT_BREAKER_TIMEOUT", 60.0),
            half_open_max_calls=getattr(self.settings, "RULE_REGISTRATION_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS", 3),
            cache_max_size=getattr(self.settings, "RULE_REGISTRATION_CACHE_MAX_SIZE", 10000),
            cache_ttl=getattr(self.settings, "RULE_REGISTRATION_CACHE_TTL", 3600.0),
            sync_batch_size=getattr(self.settings, "RULE_REGISTRATION_SYNC_BATCH_SIZE", 100),
            sync_interval=getattr(self.settings, "RULE_REGISTRATION_SYNC_INTERVAL", 300.0),
        )

        self.degradation_manager = ServiceDegradationManager(degradation_config)

        # 检查是否启用降级模式
        self.degraded_mode_enabled = getattr(self.settings, "RULE_REGISTRATION_DEGRADED_MODE_ENABLED", True)

        # 服务状态（保留兼容性）
        self._is_healthy = True
        self._last_health_check = None
        self._degraded_mode = False
        self._degraded_mode_start_time = None
        self._consecutive_failures = 0
        self._max_consecutive_failures = 3  # 连续失败3次后进入降级模式

        # 离线队列配置
        self._offline_queue_path = Path("data/offline_registration_queue.json")
        self._offline_queue_path.parent.mkdir(parents=True, exist_ok=True)
        self._offline_queue: list[dict] = []
        self._load_offline_queue()

        self._registration_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_rules_registered": 0,
            "last_request_time": None,
            "average_response_time": 0.0,
            "degraded_mode_activations": 0,
            "offline_queue_size": len(self._offline_queue),
        }

        logger.info(
            f"RuleRegistrationService初始化完成 - "
            f"服务地址: {self.settings.RULE_REGISTRATION_HOST}, "
            f"超时: {self.settings.RULE_REGISTRATION_TIMEOUT}s, "
            f"重试次数: {self.settings.RULE_REGISTRATION_MAX_RETRIES}, "
            f"离线队列大小: {len(self._offline_queue)}"
        )

    def _load_offline_queue(self):
        """加载离线注册队列"""
        try:
            if self._offline_queue_path.exists():
                with open(self._offline_queue_path, "r", encoding="utf-8") as f:
                    self._offline_queue = json.load(f)
                logger.info(f"加载离线注册队列: {len(self._offline_queue)} 个待处理项")
            else:
                self._offline_queue = []
        except Exception as e:
            logger.error(f"加载离线注册队列失败: {e}")
            self._offline_queue = []

    def _save_offline_queue(self):
        """保存离线注册队列"""
        try:
            with open(self._offline_queue_path, "w", encoding="utf-8") as f:
                json.dump(self._offline_queue, f, ensure_ascii=False, indent=2)
            logger.debug(f"保存离线注册队列: {len(self._offline_queue)} 个待处理项")
        except Exception as e:
            logger.error(f"保存离线注册队列失败: {e}")

    def _add_to_offline_queue(self, registration_data: list[dict[str, Any]], reason: str):
        """添加注册数据到离线队列"""
        offline_item = {
            "timestamp": time.perf_counter(),
            "reason": reason,
            "data": registration_data,
            "retry_count": 0,
            "max_retries": 5,
        }
        self._offline_queue.append(offline_item)
        self._save_offline_queue()
        self._registration_stats["offline_queue_size"] = len(self._offline_queue)

        logger.warning(
            f"注册数据已添加到离线队列 - "
            f"原因: {reason}, "
            f"数据量: {len(registration_data)}, "
            f"队列大小: {len(self._offline_queue)}"
        )

    def _enter_degraded_mode(self, reason: str):
        """进入降级模式"""
        if not self._degraded_mode:
            self._degraded_mode = True
            self._degraded_mode_start_time = time.perf_counter()
            self._registration_stats["degraded_mode_activations"] += 1

            logger.warning(f"规则注册服务进入降级模式 - 原因: {reason}, 连续失败次数: {self._consecutive_failures}")

    def _exit_degraded_mode(self):
        """退出降级模式"""
        if self._degraded_mode:
            duration = time.perf_counter() - self._degraded_mode_start_time if self._degraded_mode_start_time else 0
            self._degraded_mode = False
            self._degraded_mode_start_time = None
            self._consecutive_failures = 0

            logger.info(f"规则注册服务退出降级模式 - 持续时间: {duration:.2f}秒")

    async def register_rules(self, registration_data: list[dict[str, Any]]) -> dict[str, Any]:
        """
        注册规则到外部服务

        Args:
            registration_data: 规则注册数据列表，每个元素包含规则信息

        Returns:
            Dict: 注册结果，包含成功状态和相关信息

        Raises:
            RuleRegistrationError: 注册失败时抛出
        """
        if not self.settings.RULE_REGISTRATION_ENABLED:
            logger.warning("规则注册功能已禁用，跳过注册操作")
            return {
                "success": True,
                "message": "规则注册功能已禁用",
                "skipped": True,
                "rules_count": len(registration_data),
            }

        if not registration_data:
            logger.warning("注册数据为空，跳过注册操作")
            return {"success": True, "message": "注册数据为空", "rules_count": 0}

        # 检查是否处于降级模式（新的降级管理器）
        if self.degraded_mode_enabled and self.degradation_manager.should_degrade():
            logger.warning("当前处于降级模式，将注册数据缓存")

            # 使用新的降级管理器缓存数据
            cache_key = f"registration_batch_{int(time.perf_counter() * 1000)}"
            cache_success = await self.degradation_manager.cache_data(cache_key, registration_data)

            if cache_success:
                return {
                    "success": True,
                    "message": "服务处于降级模式，数据已缓存等待同步",
                    "degraded": True,
                    "rules_count": len(registration_data),
                    "cache_key": cache_key,
                    "degradation_status": self.degradation_manager.get_status_info(),
                }
            else:
                # 缓存失败，回退到原有的离线队列
                logger.warning("降级缓存失败，回退到离线队列")
                self._add_to_offline_queue(registration_data, "降级缓存失败")
                return {
                    "success": True,
                    "message": "降级缓存失败，数据已保存到离线队列",
                    "degraded": True,
                    "rules_count": len(registration_data),
                    "offline_queue_size": len(self._offline_queue),
                }

        # 检查是否处于降级模式（原有逻辑，保持兼容性）
        elif self._degraded_mode:
            logger.warning("当前处于降级模式，将注册数据添加到离线队列")
            self._add_to_offline_queue(registration_data, "服务处于降级模式")
            return {
                "success": True,
                "message": "服务处于降级模式，数据已保存到离线队列",
                "degraded": True,
                "rules_count": len(registration_data),
                "offline_queue_size": len(self._offline_queue),
            }

        start_time = time.perf_counter()
        self._registration_stats["total_requests"] += 1
        self._registration_stats["last_request_time"] = start_time

        try:
            logger.info(f"开始注册规则，数量: {len(registration_data)}")

            # 构建注册请求
            request_payload = {"rules": registration_data}

            # 构建请求URL
            registration_url = urljoin(
                self.settings.RULE_REGISTRATION_HOST.rstrip("/") + "/", "api/compliance/register"
            )

            # 发送注册请求
            response = await self.retry_client.post(registration_url, json=request_payload)

            # 处理响应
            response_time = time.perf_counter() - start_time
            self._update_response_time_stats(response_time)

            if response.status_code == 200:
                response_data = response.json()

                # 检查注册服务返回的成功状态
                if response_data.get("success", False):
                    self._registration_stats["successful_requests"] += 1
                    self._registration_stats["total_rules_registered"] += len(registration_data)

                    # 记录成功请求到降级管理器
                    if self.degraded_mode_enabled:
                        await self.degradation_manager.record_request_result(True, response_time)

                    # 注册成功，重置失败计数并退出降级模式
                    self._consecutive_failures = 0
                    if self._degraded_mode:
                        self._exit_degraded_mode()

                    logger.info(f"规则注册成功 - 规则数量: {len(registration_data)}, 响应时间: {response_time:.3f}s")

                    return {
                        "success": True,
                        "message": "规则注册成功",
                        "rules_count": len(registration_data),
                        "response_time": response_time,
                        "response_data": response_data,
                    }
                else:
                    # 注册服务返回失败状态
                    self._registration_stats["failed_requests"] += 1

                    # 记录失败请求到降级管理器
                    if self.degraded_mode_enabled:
                        await self.degradation_manager.record_request_result(False, response_time)

                    error_message = f"注册服务返回失败状态: {response_data}"

                    logger.error(error_message)
                    raise RuleRegistrationError(
                        error_message,
                        error_code="REGISTRATION_FAILED",
                        details={"response_data": response_data, "response_time": response_time},
                    )
            else:
                # HTTP状态码错误
                self._registration_stats["failed_requests"] += 1

                # 记录失败请求到降级管理器
                if self.degraded_mode_enabled:
                    await self.degradation_manager.record_request_result(False, response_time)

                error_message = f"注册请求失败，HTTP状态码: {response.status_code}"

                logger.error(f"{error_message}, 响应内容: {response.text}")
                raise RuleRegistrationError(
                    error_message,
                    error_code="HTTP_ERROR",
                    details={
                        "status_code": response.status_code,
                        "response_text": response.text,
                        "response_time": response_time,
                    },
                )

        except RuleRegistrationError as e:
            # 处理已知的注册错误，考虑降级策略
            await self._handle_registration_failure(registration_data, str(e))
            raise
        except Exception as e:
            # 处理其他异常
            self._registration_stats["failed_requests"] += 1
            response_time = time.perf_counter() - start_time

            # 记录失败请求到降级管理器
            if self.degraded_mode_enabled:
                await self.degradation_manager.record_request_result(False, response_time)

            error_message = f"规则注册过程中发生异常: {str(e)}"
            logger.error(error_message, exc_info=True)

            # 处理失败并考虑降级
            await self._handle_registration_failure(registration_data, error_message)

            raise RuleRegistrationError(
                error_message,
                error_code="UNEXPECTED_ERROR",
                details={"exception_type": type(e).__name__, "response_time": response_time},
            ) from e

    async def _handle_registration_failure(self, registration_data: list[dict[str, Any]], error_reason: str):
        """处理注册失败，实施降级策略"""
        self._consecutive_failures += 1

        # 检查是否需要进入降级模式
        if self._consecutive_failures >= self._max_consecutive_failures:
            self._enter_degraded_mode(f"连续失败{self._consecutive_failures}次")

        # 如果启用了新的降级管理器，优先使用缓存
        if self.degraded_mode_enabled:
            cache_key = f"failed_registration_{int(time.perf_counter() * 1000)}"
            cache_success = await self.degradation_manager.cache_data(cache_key, registration_data)

            if cache_success:
                logger.info(f"失败的注册数据已缓存: {cache_key}")
            else:
                # 缓存失败，回退到离线队列
                self._add_to_offline_queue(registration_data, f"注册失败: {error_reason}")
        else:
            # 将失败的注册数据添加到离线队列
            self._add_to_offline_queue(registration_data, f"注册失败: {error_reason}")

        logger.warning(
            f"注册失败处理完成 - 连续失败次数: {self._consecutive_failures}, 降级模式: {self._degraded_mode}"
        )

    async def health_check(self) -> dict[str, Any]:
        """
        检查注册服务健康状态

        Returns:
            Dict: 健康检查结果
        """
        try:
            start_time = time.perf_counter()

            # 构建健康检查URL（假设有健康检查端点）
            health_url = urljoin(self.settings.RULE_REGISTRATION_HOST.rstrip("/") + "/", "health")

            # 发送健康检查请求（使用较短的超时时间）
            response = await self.retry_client.get(
                health_url,
                timeout=10.0,  # 健康检查使用较短超时
            )

            response_time = time.perf_counter() - start_time

            if response.status_code == 200:
                self._is_healthy = True
                self._last_health_check = time.perf_counter()

                logger.debug(f"注册服务健康检查成功，响应时间: {response_time:.3f}s")

                return {
                    "healthy": True,
                    "response_time": response_time,
                    "status_code": response.status_code,
                    "message": "服务健康",
                }
            else:
                self._is_healthy = False
                logger.warning(f"注册服务健康检查失败，状态码: {response.status_code}")

                return {
                    "healthy": False,
                    "response_time": response_time,
                    "status_code": response.status_code,
                    "message": f"服务不健康，状态码: {response.status_code}",
                }

        except Exception as e:
            self._is_healthy = False
            response_time = time.perf_counter() - start_time

            logger.warning(f"注册服务健康检查异常: {str(e)}")

            return {"healthy": False, "response_time": response_time, "error": str(e), "message": "健康检查异常"}

    def _update_response_time_stats(self, response_time: float):
        """更新响应时间统计"""
        current_avg = self._registration_stats["average_response_time"]
        total_requests = self._registration_stats["total_requests"]

        # 计算新的平均响应时间
        if total_requests == 1:
            self._registration_stats["average_response_time"] = response_time
        else:
            self._registration_stats["average_response_time"] = (
                current_avg * (total_requests - 1) + response_time
            ) / total_requests

    def get_stats(self) -> dict[str, Any]:
        """
        获取注册服务统计信息

        Returns:
            Dict: 统计信息
        """
        stats = self._registration_stats.copy()

        # 添加健康状态
        stats["is_healthy"] = self._is_healthy
        stats["last_health_check"] = self._last_health_check

        # 添加成功率
        if stats["total_requests"] > 0:
            stats["success_rate"] = stats["successful_requests"] / stats["total_requests"]
        else:
            stats["success_rate"] = 0.0

        # 添加HTTP客户端统计
        stats["http_client_stats"] = self.retry_client.get_stats()

        # 添加降级模式信息
        stats["degraded_mode"] = self._degraded_mode
        stats["consecutive_failures"] = self._consecutive_failures
        if self._degraded_mode_start_time:
            stats["degraded_mode_duration"] = time.perf_counter() - self._degraded_mode_start_time

        return stats

    async def process_offline_queue(self) -> dict[str, Any]:
        """
        处理离线注册队列

        Returns:
            Dict: 处理结果统计
        """
        if not self._offline_queue:
            return {"processed": 0, "successful": 0, "failed": 0, "remaining": 0, "message": "离线队列为空"}

        processed = 0
        successful = 0
        failed = 0
        items_to_remove = []

        logger.info(f"开始处理离线注册队列，共 {len(self._offline_queue)} 个待处理项")

        for i, item in enumerate(self._offline_queue):
            try:
                # 检查重试次数
                if item["retry_count"] >= item["max_retries"]:
                    logger.warning(f"离线队列项已达到最大重试次数，跳过: {item['reason']}")
                    items_to_remove.append(i)
                    failed += 1
                    continue

                # 增加重试次数
                item["retry_count"] += 1

                # 尝试注册
                logger.debug(f"处理离线队列项 {i + 1}/{len(self._offline_queue)}: {item['reason']}")

                # 临时禁用降级模式进行重试
                original_degraded = self._degraded_mode
                self._degraded_mode = False

                try:
                    result = await self.register_rules(item["data"])
                    if result.get("success", False) and not result.get("degraded", False):
                        successful += 1
                        items_to_remove.append(i)
                        logger.info(f"离线队列项处理成功: {item['reason']}")
                    else:
                        failed += 1
                        logger.warning(f"离线队列项处理失败: {item['reason']}")
                finally:
                    # 恢复降级模式状态
                    self._degraded_mode = original_degraded

                processed += 1

            except Exception as e:
                failed += 1
                logger.error(f"处理离线队列项时发生异常: {e}")

                # 如果是网络相关错误，停止处理剩余项目
                if "connection" in str(e).lower() or "timeout" in str(e).lower():
                    logger.warning("检测到网络问题，停止处理离线队列")
                    break

        # 移除已成功处理的项目（从后往前删除以避免索引问题）
        for i in sorted(items_to_remove, reverse=True):
            del self._offline_queue[i]

        # 保存更新后的队列
        self._save_offline_queue()
        self._registration_stats["offline_queue_size"] = len(self._offline_queue)

        result = {
            "processed": processed,
            "successful": successful,
            "failed": failed,
            "remaining": len(self._offline_queue),
            "message": f"处理完成: 成功 {successful}, 失败 {failed}, 剩余 {len(self._offline_queue)}",
        }

        logger.info(f"离线队列处理完成: {result['message']}")
        return result

    async def close(self):
        """关闭服务，清理资源"""
        try:
            # 关闭降级管理器
            if self.degraded_mode_enabled and hasattr(self, "degradation_manager"):
                await self.degradation_manager.stop()
                logger.info("ServiceDegradationManager已关闭")

            await self.retry_client.close()
            logger.info("RuleRegistrationService已关闭")
        except Exception as e:
            logger.error(f"关闭RuleRegistrationService时发生错误: {e}")

    async def start(self):
        """启动服务，初始化降级管理器"""
        try:
            # 启动降级管理器
            if self.degraded_mode_enabled and hasattr(self, "degradation_manager"):
                await self.degradation_manager.start()
                logger.info("ServiceDegradationManager已启动")

            logger.info("RuleRegistrationService已启动")
        except Exception as e:
            logger.error(f"启动RuleRegistrationService时发生错误: {e}")
            raise

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
        return False  # 不抑制异常
