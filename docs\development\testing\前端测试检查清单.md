# 前端测试检查清单

## 开发前检查

### 1. 环境检查
- [ ] Node.js 版本 >= 16
- [ ] npm 或 yarn 已安装
- [ ] 项目依赖已安装 (`npm install`)

### 2. 配置文件检查
- [ ] `package.json` 存在且格式正确
- [ ] `vite.config.js` 存在且配置正确
- [ ] `tsconfig.json` 和 `tsconfig.node.json` 存在
- [ ] `.env` 文件配置正确

## 开发过程中检查

### 1. 代码修改后
- [ ] 运行 `npm run dev` 确保应用能启动
- [ ] 检查浏览器控制台无错误
- [ ] 检查网络请求正常
- [ ] 测试主要功能路径

### 2. 添加新依赖后
- [ ] 确认依赖已添加到 `package.json`
- [ ] 运行 `npm install` 安装依赖
- [ ] 测试应用启动和构建

### 3. 修改配置文件后
- [ ] 重启开发服务器
- [ ] 检查配置是否生效
- [ ] 测试构建过程

## 提交前检查

### 1. 基础功能测试
- [ ] 应用能正常启动 (`npm run dev`)
- [ ] 应用能正常构建 (`npm run build`)
- [ ] 构建产物能正常预览 (`npm run preview`)

### 2. 页面功能测试
- [ ] 主页面加载正常
- [ ] 路由跳转正常
- [ ] API 请求正常
- [ ] 组件交互正常

### 3. 兼容性测试
- [ ] Chrome 浏览器测试
- [ ] Firefox 浏览器测试
- [ ] 移动端响应式测试

## 部署前检查

### 1. 构建测试
- [ ] 生产环境构建成功
- [ ] 构建产物大小合理
- [ ] 静态资源路径正确

### 2. Docker 测试
- [ ] Docker 镜像构建成功
- [ ] 容器能正常启动
- [ ] 容器内应用访问正常

### 3. 环境变量测试
- [ ] 开发环境变量正确
- [ ] 生产环境变量正确
- [ ] API 地址配置正确

## 自动化测试脚本

### 快速启动测试
```bash
npm run test:startup
```

### 构建测试
```bash
npm run test:build
```

### 类型检查
```bash
npm run type-check
```

### 代码检查
```bash
npm run lint
```

## 常见问题排查

### 1. 应用无法启动
- 检查 Node.js 版本
- 检查依赖是否安装完整
- 检查配置文件语法
- 查看错误日志

### 2. 构建失败
- 检查 TypeScript 类型错误
- 检查导入路径是否正确
- 检查环境变量配置
- 查看构建日志

### 3. 运行时错误
- 检查浏览器控制台
- 检查网络请求
- 检查 API 接口状态
- 检查路由配置

## 性能检查

### 1. 开发环境
- [ ] 热重载响应时间 < 1秒
- [ ] 页面首次加载时间 < 3秒
- [ ] 内存使用合理

### 2. 生产环境
- [ ] 首屏加载时间 < 2秒
- [ ] 静态资源缓存正常
- [ ] 代码分割生效

## 安全检查

### 1. 依赖安全
- [ ] 运行 `npm audit` 检查漏洞
- [ ] 及时更新有安全问题的依赖

### 2. 代码安全
- [ ] 不包含敏感信息
- [ ] API 密钥正确配置
- [ ] 跨域配置安全

## 文档检查

### 1. 代码文档
- [ ] 重要函数有注释
- [ ] 组件有使用说明
- [ ] API 接口有文档

### 2. 项目文档
- [ ] README.md 更新
- [ ] 部署文档更新
- [ ] 变更日志更新
