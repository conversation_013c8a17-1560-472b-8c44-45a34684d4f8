<template>
  <div class="action-buttons">
    <div class="button-group">
      <!-- 刷新按钮 -->
      <el-button
        :icon="Refresh"
        :loading="loading"
        @click="$emit('refresh')"
        title="刷新数据"
        class="action-btn"
      >
        <span class="btn-text">刷新</span>
      </el-button>

      <!-- 导出按钮 -->
      <el-button
        :icon="Download"
        @click="$emit('export')"
        title="导出数据"
        class="action-btn"
      >
        <span class="btn-text">导出</span>
      </el-button>

      <!-- 批量操作下拉菜单 -->
      <el-dropdown
        @command="handleBatchAction"
        trigger="click"
        class="batch-dropdown"
      >
        <el-button :icon="MoreFilled" class="action-btn">
          <span class="btn-text">批量操作</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="activate" :icon="Check">
              批量激活
            </el-dropdown-item>
            <el-dropdown-item command="deactivate" :icon="Close">
              批量停用
            </el-dropdown-item>
            <el-dropdown-item command="delete" :icon="Delete" divided>
              批量删除
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 新增按钮 -->
      <el-button
        type="primary"
        :icon="Plus"
        @click="$emit('create')"
        title="新增规则明细"
        class="action-btn primary-btn"
      >
        <span class="btn-text">新增</span>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import {
  Refresh,
  Download,
  MoreFilled,
  ArrowDown,
  Plus,
  Check,
  Close,
  Delete
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  showCreate: {
    type: Boolean,
    default: true
  },
  showExport: {
    type: Boolean,
    default: true
  },
  showBatch: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['refresh', 'export', 'batch-action', 'create'])

// 处理批量操作
const handleBatchAction = (command) => {
  emit('batch-action', command)
}
</script>

<style scoped>
.action-buttons {
  display: flex;
  align-items: center;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.primary-btn {
  font-weight: 500;
}

.dropdown-icon {
  margin-left: 4px;
  font-size: 12px;
}

.btn-text {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-group {
    flex-wrap: wrap;
    gap: 6px;
  }

  .action-btn {
    padding: 8px 12px;
  }

  .btn-text {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .btn-text {
    display: none;
  }

  .action-btn {
    padding: 8px;
    min-width: 36px;
    justify-content: center;
  }

  .dropdown-icon {
    margin-left: 0;
  }
}
</style>
