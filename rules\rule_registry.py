import importlib
import inspect
import pkgutil

from core.logging.logging_system import log as logger
from rules.base_rules.base import BaseRule


class RuleRegistry:
    """
    规则注册管理器

    负责动态发现、存储和检索规则的 *类*。
    它不再存储规则的实例，而是作为规则类的中央仓库。
    """

    def __init__(self):
        self._rules: dict[str, type[BaseRule]] = {}

    def register_rule_class(self, rule_key: str, rule_class: type[BaseRule]) -> None:
        """
        注册一个规则类

        Args:
            rule_key: 规则的唯一标识符 (通常是文件名).
            rule_class: 规则的类定义.
        """
        if rule_key in self._rules:
            logger.warning(f"规则键 '{rule_key}' 已存在，类将被覆盖。")

        self._rules[rule_key] = rule_class
        logger.debug(f"规则类 '{rule_class.__name__}' 已使用键 '{rule_key}' 注册。")

    def get_rule_class(self, rule_key: str) -> type[BaseRule] | None:
        """
        根据规则键获取规则类

        Args:
            rule_key: 规则的唯一标识符.

        Returns:
            规则的类定义，如果未找到则返回 None.
        """
        return self._rules.get(rule_key)

    def get_all_rules(self) -> dict[str, type[BaseRule]]:
        """
        获取所有已注册的规则类

        Returns:
            一个从规则键到规则类的映射字典。
        """
        return self._rules.copy()

    def auto_discover_rules(self, package_name: str = "rules.base_rules") -> int:
        """
        自动发现并注册规则类。

        在指定的包（默认为 `rules.base_rules`）中查找所有继承了 `BaseRule` 的类，
        并将其注册到注册表中。

        Args:
            package_name: 规则所在的Python包名。

        Returns:
            int: 新注册的规则类的数量。
        """
        initial_count = len(self._rules)
        try:
            package = importlib.import_module(package_name)
            logger.info(f"开始从包 '{package_name}' 中自动发现规则类...")

            for _, module_name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + "."):
                if is_pkg:
                    continue  # 不处理子包

                try:
                    module = importlib.import_module(module_name)
                    rule_key = module_name.split(".")[-1]  # 使用模块名作为 key

                    # 查找模块中所有继承自 BaseRule 的类
                    for _, obj in inspect.getmembers(module, inspect.isclass):
                        if issubclass(obj, BaseRule) and obj is not BaseRule and obj.__module__ == module_name:
                            # 注册找到的第一个符合条件的类
                            self.register_rule_class(rule_key, obj)
                            logger.info(f"在模块 '{module_name}' 中发现并注册了规则类: '{obj.__name__}'")
                            # 假设一个文件只定义一个核心规则类，找到后就跳出
                            break

                except Exception as e:
                    logger.error(f"加载模块 {module_name} 或注册规则失败: {e}", exc_info=True)

            final_count = len(self._rules)
            newly_registered_count = final_count - initial_count
            logger.info(f"规则类自动发现完成。共注册了 {newly_registered_count} 个新规则类，总数为 {final_count}。")
            return newly_registered_count

        except ImportError:
            logger.error(f"无法导入规则包 '{package_name}'。请检查路径是否正确。", exc_info=True)
            return 0
        except Exception as e:
            logger.error(f"自动发现规则时发生未知错误: {e}", exc_info=True)
            return 0


# 全局规则注册表实例
rule_registry = RuleRegistry()
