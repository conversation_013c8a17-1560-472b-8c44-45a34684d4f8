"""
任务状态管理器
负责管理异步注册任务的状态、进度跟踪和生命周期管理
"""

import asyncio
import uuid
from datetime import datetime
from enum import Enum
from typing import Any

from core.logging.logging_system import log as logger


class TaskStatus(Enum):
    """任务状态枚举"""

    PENDING = "pending"  # 等待处理
    RUNNING = "running"  # 正在执行
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 执行失败
    CANCELLED = "cancelled"  # 已取消


class TaskInfo:
    """任务信息类"""

    def __init__(
        self, task_id: str, task_type: str, rule_key: str, total_operations: int = 0, user_id: str | None = None
    ):
        self.task_id = task_id
        self.task_type = task_type
        self.rule_key = rule_key
        self.user_id = user_id
        self.status = TaskStatus.PENDING
        self.total_operations = total_operations
        self.completed_operations = 0
        self.progress_percentage = 0.0
        self.current_message = ""
        self.error_message = ""
        self.result_data = None
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.execution_time = 0.0

        # 详细统计信息
        self.stats = {
            "delete_operations": 0,
            "upsert_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "retry_count": 0,
        }

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "rule_key": self.rule_key,
            "user_id": self.user_id,
            "status": self.status.value,
            "total_operations": self.total_operations,
            "completed_operations": self.completed_operations,
            "progress_percentage": self.progress_percentage,
            "current_message": self.current_message,
            "error_message": self.error_message,
            "result_data": self.result_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "execution_time": self.execution_time,
            "stats": self.stats,
        }


class TaskStatusManager:
    """
    任务状态管理器

    负责管理异步注册任务的状态、进度跟踪和生命周期管理。
    使用内存字典存储，支持后续扩展为Redis。
    """

    def __init__(self):
        """初始化任务状态管理器"""
        self._tasks: dict[str, TaskInfo] = {}
        self._lock = asyncio.Lock()
        self._cleanup_interval = 3600  # 1小时清理一次
        self._task_retention_time = 86400  # 24小时保留时间
        self._cleanup_task = None

        # 统计信息
        self._manager_stats = {
            "total_tasks_created": 0,
            "total_tasks_completed": 0,
            "total_tasks_failed": 0,
            "total_tasks_cancelled": 0,
            "active_tasks_count": 0,
        }

        logger.info("TaskStatusManager初始化完成")

    async def create_task(
        self, task_type: str, rule_key: str, total_operations: int = 0, user_id: str | None = None
    ) -> str:
        """
        创建新任务

        Args:
            task_type: 任务类型
            rule_key: 规则键值
            total_operations: 总操作数
            user_id: 用户ID

        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())

        async with self._lock:
            task_info = TaskInfo(
                task_id=task_id,
                task_type=task_type,
                rule_key=rule_key,
                total_operations=total_operations,
                user_id=user_id,
            )

            self._tasks[task_id] = task_info
            self._manager_stats["total_tasks_created"] += 1
            self._manager_stats["active_tasks_count"] += 1

        logger.info(f"创建任务: {task_id}, 类型: {task_type}, 规则: {rule_key}")
        return task_id

    async def update_task_status(
        self, task_id: str, status: TaskStatus, message: str | None = None, error_message: str | None = None
    ) -> bool:
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            message: 状态消息
            error_message: 错误消息

        Returns:
            bool: 更新是否成功
        """
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"任务 {task_id} 不存在")
                return False

            task_info = self._tasks[task_id]
            old_status = task_info.status
            task_info.status = status

            if message:
                task_info.current_message = message

            if error_message:
                task_info.error_message = error_message

            # 更新时间戳
            if status == TaskStatus.RUNNING and old_status == TaskStatus.PENDING:
                task_info.started_at = datetime.now()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                task_info.completed_at = datetime.now()
                if task_info.started_at:
                    task_info.execution_time = (task_info.completed_at - task_info.started_at).total_seconds()

                # 更新统计信息
                self._manager_stats["active_tasks_count"] -= 1
                if status == TaskStatus.COMPLETED:
                    self._manager_stats["total_tasks_completed"] += 1
                elif status == TaskStatus.FAILED:
                    self._manager_stats["total_tasks_failed"] += 1
                elif status == TaskStatus.CANCELLED:
                    self._manager_stats["total_tasks_cancelled"] += 1

        logger.debug(f"任务 {task_id} 状态更新: {old_status.value} -> {status.value}")
        return True

    async def update_task_progress(
        self, task_id: str, completed_operations: int, message: str | None = None
    ) -> bool:
        """
        更新任务进度

        Args:
            task_id: 任务ID
            completed_operations: 已完成操作数
            message: 进度消息

        Returns:
            bool: 更新是否成功
        """
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"任务 {task_id} 不存在")
                return False

            task_info = self._tasks[task_id]
            task_info.completed_operations = completed_operations

            # 计算进度百分比
            if task_info.total_operations > 0:
                task_info.progress_percentage = min(100.0, (completed_operations / task_info.total_operations) * 100.0)
            else:
                task_info.progress_percentage = 0.0

            if message:
                task_info.current_message = message

        logger.debug(
            f"任务 {task_id} 进度更新: {completed_operations}/{task_info.total_operations} ({task_info.progress_percentage:.1f}%)"
        )
        return True

    async def update_task_stats(self, task_id: str, stats: dict[str, Any]) -> bool:
        """
        更新任务统计信息

        Args:
            task_id: 任务ID
            stats: 统计信息字典

        Returns:
            bool: 更新是否成功
        """
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"任务 {task_id} 不存在")
                return False

            task_info = self._tasks[task_id]
            task_info.stats.update(stats)

        logger.debug(f"任务 {task_id} 统计信息已更新")
        return True

    async def set_task_result(self, task_id: str, result_data: Any) -> bool:
        """
        设置任务结果数据

        Args:
            task_id: 任务ID
            result_data: 结果数据

        Returns:
            bool: 设置是否成功
        """
        async with self._lock:
            if task_id not in self._tasks:
                logger.warning(f"任务 {task_id} 不存在")
                return False

            task_info = self._tasks[task_id]
            task_info.result_data = result_data

        logger.debug(f"任务 {task_id} 结果数据已设置")
        return True

    async def get_task_info(self, task_id: str) -> dict[str, Any] | None:
        """
        获取任务信息

        Args:
            task_id: 任务ID

        Returns:
            Optional[Dict]: 任务信息字典，不存在时返回None
        """
        async with self._lock:
            if task_id not in self._tasks:
                return None

            return self._tasks[task_id].to_dict()

    async def list_tasks(
        self,
        status_filter: TaskStatus | None = None,
        rule_key_filter: str | None = None,
        user_id_filter: str | None = None,
        limit: int = 100,
    ) -> list[dict[str, Any]]:
        """
        列出任务

        Args:
            status_filter: 状态过滤器
            rule_key_filter: 规则键值过滤器
            user_id_filter: 用户ID过滤器
            limit: 返回数量限制

        Returns:
            List[Dict]: 任务信息列表
        """
        async with self._lock:
            tasks = []

            for task_info in self._tasks.values():
                # 应用过滤器
                if status_filter and task_info.status != status_filter:
                    continue
                if rule_key_filter and task_info.rule_key != rule_key_filter:
                    continue
                if user_id_filter and task_info.user_id != user_id_filter:
                    continue

                tasks.append(task_info.to_dict())

                if len(tasks) >= limit:
                    break

            # 按创建时间倒序排列
            tasks.sort(key=lambda x: x["created_at"], reverse=True)

            return tasks

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 取消是否成功
        """
        return await self.update_task_status(task_id, TaskStatus.CANCELLED, message="任务已被取消")

    async def cleanup_old_tasks(self):
        """清理过期任务"""
        current_time = datetime.now()
        cleanup_count = 0

        async with self._lock:
            tasks_to_remove = []

            for task_id, task_info in self._tasks.items():
                # 计算任务存在时间
                task_age = (current_time - task_info.created_at).total_seconds()

                # 清理超过保留时间的已完成任务
                if (
                    task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
                    and task_age > self._task_retention_time
                ):
                    tasks_to_remove.append(task_id)

            # 删除过期任务
            for task_id in tasks_to_remove:
                del self._tasks[task_id]
                cleanup_count += 1

        if cleanup_count > 0:
            logger.info(f"清理了 {cleanup_count} 个过期任务")

    async def get_manager_stats(self) -> dict[str, Any]:
        """
        获取管理器统计信息

        Returns:
            Dict: 统计信息
        """
        async with self._lock:
            stats = self._manager_stats.copy()
            stats["total_tasks_in_memory"] = len(self._tasks)

            # 按状态统计当前任务
            status_counts = {}
            for task_info in self._tasks.values():
                status = task_info.status.value
                status_counts[status] = status_counts.get(status, 0) + 1

            stats["current_tasks_by_status"] = status_counts

            return stats

    async def start_cleanup_task(self):
        """启动清理任务"""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("任务清理循环已启动")

    async def stop_cleanup_task(self):
        """停止清理任务"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
            logger.info("任务清理循环已停止")

    async def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(self._cleanup_interval)
                await self.cleanup_old_tasks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"任务清理过程中发生错误: {e}")


# 全局任务状态管理器实例
_task_status_manager: TaskStatusManager | None = None


def get_task_status_manager():
    """
    获取全局任务状态管理器实例

    根据配置返回内存版本或持久化版本的任务状态管理器
    """
    global _task_status_manager
    if _task_status_manager is None:
        from config.settings import get_settings

        settings = get_settings()

        if settings.TASK_STATUS_STORAGE_TYPE == "database":
            # 使用持久化版本
            from services.persistent_task_status_manager import get_persistent_task_status_manager

            logger.info("使用数据库持久化任务状态管理器")
            return get_persistent_task_status_manager()
        else:
            # 使用内存版本（默认）
            _task_status_manager = TaskStatusManager()
            logger.info("使用内存任务状态管理器")

    return _task_status_manager
