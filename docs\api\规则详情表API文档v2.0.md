# 规则详情表API文档 v2.0

## 📋 概述

本文档描述了规则详情表重构后的API接口，基于新的三表结构（RuleTemplate, RuleDetail, RuleFieldMetadata）设计。

**版本**: v2.0  
**更新时间**: 2025-01-25  
**基础路径**: `/api/v1/rules/details`  
**认证方式**: X-API-KEY头部认证  

## 🔧 字段映射表

| 前端字段 | 后端字段 | 中文名称 | 数据类型 | 必填 |
|---------|---------|---------|---------|------|
| level1 | level1 | 一级错误类型 | string | ✓ |
| level2 | level2 | 二级错误类型 | string | ✓ |
| level3 | level3 | 三级错误类型 | string | ✓ |
| degree | degree | 错误程度 | string | ✓ |
| reference | reference | 质控依据 | string | ✓ |
| detail_position | detail_position | 具体位置描述 | string | ✓ |
| prompted_fields1 | prompted_fields1 | 提示字段编码 | string | ✓ |
| prompted_fields3 | prompted_fields3 | 提示字段类型 | string | - |
| type | type | 规则类别 | string | ✓ |
| pos | pos | 适用业务 | string | ✓ |
| applicableArea | applicableArea | 适用地区 | string | ✓ |
| default_use | default_use | 默认选用 | string | ✓ |

## 📡 API接口列表

### 1. 创建规则明细

**接口**: `POST /api/v1/rules/details`

**请求体**:
```json
{
  "rule_id": "RULE_001",
  "rule_key": "drug_limit_adult_and_diag_exact",
  "rule_name": "成人药品限制规则",
  "level1": "用药安全",
  "level2": "适应症限制",
  "level3": "年龄限制",
  "error_reason": "该药品仅适用于成人患者",
  "degree": "严重",
  "reference": "药品说明书第3条",
  "detail_position": "处方明细",
  "prompted_fields1": "drug_code",
  "type": "药品规则",
  "pos": "门诊",
  "applicableArea": "全国",
  "default_use": "是",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "extended_fields": {
    "age_threshold": 18,
    "drug_codes": ["A01AA01", "A01AA02"]
  }
}
```

**响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "rule_id": "RULE_001",
    "rule_key": "drug_limit_adult_and_diag_exact",
    "rule_name": "成人药品限制规则",
    "level1": "用药安全",
    "level2": "适应症限制",
    "level3": "年龄限制",
    "status": "ACTIVE",
    "created_at": "2024-01-25T10:00:00Z",
    "updated_at": "2024-01-25T10:00:00Z"
  }
}
```

### 2. 查询规则明细列表

**接口**: `GET /api/v1/rules/details/{rule_key}`

**查询参数**:
- `page`: 页码（默认1）
- `page_size`: 每页大小（默认20，最大100）
- `status`: 状态过滤（ACTIVE/INACTIVE/DEPRECATED）
- `search`: 搜索关键词
- `level1`: 一级错误类型过滤
- `level2`: 二级错误类型过滤
- `type`: 规则类别过滤
- `sort_by`: 排序字段（默认created_at）
- `sort_order`: 排序方向（asc/desc，默认desc）

**响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": {
    "items": [
      {
        "id": 1,
        "rule_id": "RULE_001",
        "rule_key": "drug_limit_adult_and_diag_exact",
        "rule_name": "成人药品限制规则",
        "level1": "用药安全",
        "level2": "适应症限制",
        "level3": "年龄限制",
        "status": "ACTIVE",
        "created_at": "2024-01-25T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20,
    "total_pages": 1
  }
}
```

### 3. 查询单条规则明细

**接口**: `GET /api/v1/rules/details/{rule_key}/{detail_id}`

**响应**: 同创建接口的响应格式

### 4. 更新规则明细

**接口**: `PUT /api/v1/rules/details/{rule_key}/{detail_id}`

**请求体**: 同创建接口的请求体格式

**响应**: 同创建接口的响应格式

### 5. 删除规则明细

**接口**: `DELETE /api/v1/rules/details/{rule_key}/{detail_id}`

**响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "删除成功",
  "data": {}
}
```

### 6. 批量操作

**接口**: `POST /api/v1/rules/details/{rule_key}/batch`

**请求体**:
```json
{
  "operation": "CREATE",
  "data": [
    {
      "rule_id": "RULE_001",
      "rule_name": "规则1",
      "level1": "用药安全",
      "level2": "适应症限制",
      "level3": "年龄限制"
    },
    {
      "rule_id": "RULE_002", 
      "rule_name": "规则2",
      "level1": "用药安全",
      "level2": "剂量限制",
      "level3": "超量使用"
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "批量操作完成",
  "data": {
    "success_count": 2,
    "failed_count": 0,
    "total_count": 2,
    "errors": []
  }
}
```

## 🔒 认证示例

```bash
curl -X GET "http://localhost:18001/api/v1/rules/details/drug_limit_adult_and_diag_exact" \
  -H "X-API-KEY: a_very_secret_key_for_development" \
  -H "Content-Type: application/json"
```

## ⚠️ 错误处理

所有API接口使用统一的错误响应格式：

```json
{
  "success": false,
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

**常见错误码**:
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 📈 性能考虑

1. **分页查询**: 建议使用分页参数，避免一次性查询大量数据
2. **索引优化**: 系统已对常用查询字段建立索引
3. **缓存机制**: 支持查询结果缓存，提升响应速度
4. **批量操作**: 大量数据操作建议使用批量接口

## 🔄 版本变更

### v2.0 (2025-01-25)
- 重构为三表结构
- 统一字段命名规范
- 增强批量操作功能
- 优化错误处理机制

### v1.0 (2024-12-01)
- 初始版本
- 基础CRUD操作
