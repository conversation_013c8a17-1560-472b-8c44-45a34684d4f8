/**
 * API配置管理
 * 统一管理API相关的配置和常量
 */

import type { CacheConfig, EnhancedRequestConfig } from '../types/apiEnhanced'

/**
 * API基础配置
 */
export const API_CONFIG = {
  // 基础URL配置
  BASE_URL: '/api',
  VERSION: 'v1',

  // 超时配置
  DEFAULT_TIMEOUT: 30000,
  UPLOAD_TIMEOUT: 120000,

  // 重试配置
  DEFAULT_RETRY_COUNT: 2,
  DEFAULT_RETRY_DELAY: 1000,

  // 分页配置
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,

  // 缓存配置
  CACHE: {
    ttl: 5 * 60 * 1000,           // 默认5分钟
    maxSize: 100,                 // 最大100个条目
    enablePersistence: true,      // 启用持久化
    versionBased: true,           // 基于版本缓存
    keyPrefix: 'api_cache_'       // 缓存键前缀
  } as CacheConfig,

  // 字段映射配置
  FIELD_MAPPING: {
    CONFIG_URL: '/data/field_mapping.json',
    ENABLE_AUTO_TRANSFORM: true,
    ENABLE_VALIDATION: true,
    CACHE_FIELD_DEFINITIONS: true
  },

  // 错误处理配置
  ERROR_HANDLING: {
    SHOW_USER_MESSAGE: true,
    ENABLE_ERROR_REPORTING: true,
    MAX_ERROR_LOG_SIZE: 1000,
    AUTO_RETRY_NETWORK_ERRORS: true
  }
}

/**
 * 规则明细API特定配置
 */
export const RULE_DETAILS_API_CONFIG = {
  // 缓存配置
  CACHE_TTL: {
    LIST: 2 * 60 * 1000,          // 列表缓存2分钟
    DETAIL: 5 * 60 * 1000,        // 详情缓存5分钟
    STATISTICS: 10 * 60 * 1000    // 统计缓存10分钟
  },

  // 批量操作配置
  BATCH_OPERATION: {
    MAX_OPERATIONS: 100,          // 最大批量操作数
    CHUNK_SIZE: 20,               // 分块大小
    ENABLE_TRANSACTION: true      // 启用事务
  },

  // 搜索配置
  SEARCH: {
    MIN_KEYWORD_LENGTH: 2,        // 最小搜索关键词长度
    MAX_RESULTS: 1000,            // 最大搜索结果数
    DEBOUNCE_DELAY: 300           // 搜索防抖延迟
  }
}

/**
 * 默认请求配置生成器
 */
export class ApiConfigBuilder {
  private config: Partial<EnhancedRequestConfig> = {}

  /**
   * 设置URL
   */
  url(url: string): ApiConfigBuilder {
    this.config.url = url
    return this
  }

  /**
   * 设置HTTP方法
   */
  method(method: 'GET' | 'POST' | 'PUT' | 'DELETE'): ApiConfigBuilder {
    this.config.method = method
    return this
  }

  /**
   * 设置请求数据
   */
  data(data: any): ApiConfigBuilder {
    this.config.data = data
    return this
  }

  /**
   * 设置查询参数
   */
  params(params: Record<string, any>): ApiConfigBuilder {
    this.config.params = params
    return this
  }

  /**
   * 启用缓存
   */
  enableCache(cacheKey?: string, ttl?: number): ApiConfigBuilder {
    this.config.enableCache = true
    if (cacheKey) this.config.cacheKey = cacheKey
    if (ttl) this.config.cacheTtl = ttl
    return this
  }

  /**
   * 启用字段映射
   */
  enableFieldMapping(ruleKey?: string): ApiConfigBuilder {
    this.config.enableFieldMapping = true
    if (ruleKey) this.config.ruleKey = ruleKey
    return this
  }

  /**
   * 启用请求验证
   */
  enableValidation(): ApiConfigBuilder {
    this.config.validateRequest = true
    this.config.validateResponse = true
    return this
  }

  /**
   * 启用重试
   */
  enableRetry(maxRetries?: number, delay?: number): ApiConfigBuilder {
    this.config.enableRetry = true
    if (maxRetries) this.config.maxRetries = maxRetries
    if (delay) this.config.retryDelay = delay
    return this
  }

  /**
   * 设置超时
   */
  timeout(timeout: number): ApiConfigBuilder {
    this.config.timeout = timeout
    return this
  }

  /**
   * 构建配置
   */
  build(): EnhancedRequestConfig {
    if (!this.config.url || !this.config.method) {
      throw new Error('URL and method are required')
    }
    return this.config as EnhancedRequestConfig
  }
}

/**
 * 创建API配置构建器
 */
export function createApiConfig(): ApiConfigBuilder {
  return new ApiConfigBuilder()
}

/**
 * 预定义的配置模板
 */
export const API_CONFIG_TEMPLATES = {
  /**
   * 获取列表的配置模板
   */
  LIST: (url: string, ruleKey?: string) => createApiConfig()
    .url(url)
    .method('GET')
    .enableCache(undefined, RULE_DETAILS_API_CONFIG.CACHE_TTL.LIST)
    .enableFieldMapping(ruleKey)
    .build(),

  /**
   * 获取详情的配置模板
   */
  DETAIL: (url: string, ruleKey?: string) => createApiConfig()
    .url(url)
    .method('GET')
    .enableCache(undefined, RULE_DETAILS_API_CONFIG.CACHE_TTL.DETAIL)
    .enableFieldMapping(ruleKey)
    .build(),

  /**
   * 创建资源的配置模板
   */
  CREATE: (url: string, data: any, ruleKey?: string) => createApiConfig()
    .url(url)
    .method('POST')
    .data(data)
    .enableFieldMapping(ruleKey)
    .enableValidation()
    .build(),

  /**
   * 更新资源的配置模板
   */
  UPDATE: (url: string, data: any, ruleKey?: string) => createApiConfig()
    .url(url)
    .method('PUT')
    .data(data)
    .enableFieldMapping(ruleKey)
    .enableValidation()
    .build(),

  /**
   * 删除资源的配置模板
   */
  DELETE: (url: string) => createApiConfig()
    .url(url)
    .method('DELETE')
    .build(),

  /**
   * 批量操作的配置模板
   */
  BATCH: (url: string, data: any, ruleKey?: string) => createApiConfig()
    .url(url)
    .method('POST')
    .data(data)
    .enableFieldMapping(ruleKey)
    .enableValidation()
    .timeout(API_CONFIG.UPLOAD_TIMEOUT)
    .build(),

  /**
   * 统计数据的配置模板
   */
  STATISTICS: (url: string) => createApiConfig()
    .url(url)
    .method('GET')
    .enableCache(undefined, RULE_DETAILS_API_CONFIG.CACHE_TTL.STATISTICS)
    .build()
}

/**
 * 环境相关配置
 */
export const ENVIRONMENT_CONFIG = {
  // 开发环境
  development: {
    enableDebug: true,
    enableMockData: false,
    logLevel: 'debug',
    cacheEnabled: true
  },

  // 测试环境
  test: {
    enableDebug: true,
    enableMockData: true,
    logLevel: 'info',
    cacheEnabled: false
  },

  // 生产环境
  production: {
    enableDebug: false,
    enableMockData: false,
    logLevel: 'error',
    cacheEnabled: true
  }
}

/**
 * 获取当前环境配置
 */
export function getCurrentEnvironmentConfig() {
  const env = (import.meta as any).env?.MODE || 'development'
  return ENVIRONMENT_CONFIG[env as keyof typeof ENVIRONMENT_CONFIG] || ENVIRONMENT_CONFIG.development
}

/**
 * API端点常量
 */
export const API_ENDPOINTS = {
  // 规则管理
  RULES: {
    STATUS: '/v1/rules/status',
    DETAIL: (ruleKey: string) => `/v1/rules/${ruleKey}/detail`,
    SCHEMA: (ruleKey: string) => `/v1/rules/${ruleKey}/schema`,
    TEMPLATE: (ruleKey: string) => `/v1/rules/${ruleKey}/template`
  },

  // 规则明细
  RULE_DETAILS: {
    LIST: (ruleKey: string) => `/v1/rules/details/${ruleKey}`,
    DETAIL: (ruleKey: string, detailId: string | number) => `/v1/rules/details/${ruleKey}/${detailId}`,
    CREATE: (ruleKey: string) => `/v1/rules/details/${ruleKey}`,
    UPDATE: (ruleKey: string, detailId: string | number) => `/v1/rules/details/${ruleKey}/${detailId}`,
    DELETE: (ruleKey: string, detailId: string | number) => `/v1/rules/details/${ruleKey}/${detailId}`,
    BATCH: (ruleKey: string) => `/v1/rules/details/${ruleKey}/batch`,
    STATISTICS: (ruleKey: string) => `/v1/rules/details/${ruleKey}/statistics`,
    SEARCH: (ruleKey: string) => `/v1/rules/details/${ruleKey}/search`
  },

  // 系统
  SYSTEM: {
    HEALTH: '/health',
    VERSION: '/version',
    CONFIG: '/config'
  }
}

/**
 * HTTP状态码常量
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
} as const

/**
 * 错误码常量
 */
export const ERROR_CODES = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',

  // 验证错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  FIELD_REQUIRED: 'FIELD_REQUIRED',
  FIELD_INVALID: 'FIELD_INVALID',

  // 业务错误
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  OPERATION_FAILED: 'OPERATION_FAILED',

  // 权限错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',

  // 系统错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE'
} as const
