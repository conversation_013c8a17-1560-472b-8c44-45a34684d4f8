"""
批量校验处理器
支持大数据量的批量校验，提供并行处理和详细报告
"""

import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import Any

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from services.rule_detail_service import ServiceError
from services.validation_rule_engine import ValidationResult, ValidationRuleEngine


@dataclass
class BatchValidationItem:
    """批量校验项"""

    index: int
    data: dict[str, Any]
    row_id: str | None = None  # 可选的行标识符


@dataclass
class BatchValidationError:
    """批量校验错误"""

    index: int
    row_id: str | None
    errors: list[dict[str, Any]]
    warnings: list[str]
    data: dict[str, Any]


@dataclass
class BatchValidationReport:
    """批量校验报告"""

    total_items: int
    valid_items: int
    invalid_items: int
    success_rate: float
    total_duration: float
    average_duration: float
    errors: list[BatchValidationError]
    warnings: list[str]
    metadata: dict[str, Any]

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_items": self.total_items,
            "valid_items": self.valid_items,
            "invalid_items": self.invalid_items,
            "success_rate": self.success_rate,
            "total_duration": self.total_duration,
            "average_duration": self.average_duration,
            "errors": [
                {
                    "index": error.index,
                    "row_id": error.row_id,
                    "errors": error.errors,
                    "warnings": error.warnings,
                    "data": error.data,
                }
                for error in self.errors
            ],
            "warnings": self.warnings,
            "metadata": self.metadata,
        }


class BatchValidationProcessor:
    """批量校验处理器"""

    def __init__(self, session: Session, max_workers: int = 4, batch_size: int = 100):
        """
        初始化批量校验处理器

        Args:
            session: 数据库会话
            max_workers: 最大工作线程数
            batch_size: 批处理大小
        """
        self.session = session
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.validation_engine = ValidationRuleEngine(session)

    def validate_batch(
        self, rule_key: str, data_list: list[dict[str, Any]], parallel: bool = True, include_valid_data: bool = False
    ) -> BatchValidationReport:
        """
        批量验证数据

        Args:
            rule_key: 规则模板键
            data_list: 待验证的数据列表
            parallel: 是否并行处理
            include_valid_data: 是否在报告中包含有效数据

        Returns:
            BatchValidationReport: 批量验证报告

        Raises:
            ServiceError: 当批量验证失败时
        """
        start_time = time.perf_counter()

        try:
            logger.info(f"开始批量校验: rule_key={rule_key}, items={len(data_list)}, parallel={parallel}")

            # 创建批量校验项
            batch_items = [
                BatchValidationItem(index=i, data=data, row_id=data.get("_row_id") or data.get("id") or str(i))
                for i, data in enumerate(data_list)
            ]

            # 执行批量校验
            if parallel and len(batch_items) > 1:
                validation_results = self._validate_parallel(rule_key, batch_items)
            else:
                validation_results = self._validate_sequential(rule_key, batch_items)

            # 生成报告
            report = self._generate_report(rule_key, batch_items, validation_results, start_time, include_valid_data)

            logger.info(
                f"批量校验完成: rule_key={rule_key}, total={report.total_items}, "
                f"valid={report.valid_items}, invalid={report.invalid_items}, "
                f"success_rate={report.success_rate:.2%}, duration={report.total_duration:.3f}s"
            )

            return report

        except Exception as e:
            error_msg = f"批量校验失败: rule_key={rule_key}, items={len(data_list)}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg,
                error_code="BATCH_VALIDATION_FAILED",
                details={"rule_key": rule_key, "items_count": len(data_list), "error": str(e)},
            ) from None

    def _validate_parallel(
        self, rule_key: str, batch_items: list[BatchValidationItem]
    ) -> list[tuple[int, ValidationResult]]:
        """并行校验"""
        results = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交校验任务
            future_to_item = {executor.submit(self._validate_single_item, rule_key, item): item for item in batch_items}

            # 收集结果
            for future in as_completed(future_to_item):
                item = future_to_item[future]
                try:
                    result = future.result()
                    results.append((item.index, result))
                except ServiceError:
                    # ServiceError需要重新抛出，不应该被捕获
                    raise
                except Exception as e:
                    logger.error(f"并行校验项 {item.index} 失败: {str(e)}")
                    # 创建错误结果
                    error_result = ValidationResult(
                        valid=False,
                        errors=[
                            {
                                "field_name": "system",
                                "chinese_name": "系统",
                                "error_code": "VALIDATION_EXCEPTION",
                                "error_message": f"校验异常: {str(e)}",
                                "error_value": None,
                                "rule_type": "system",
                                "suggestions": ["请检查数据格式或联系系统管理员"],
                            }
                        ],
                        warnings=[],
                        field_count=len(item.data),
                        validated_fields=list(item.data.keys()),
                        duration=0.0,
                    )
                    results.append((item.index, error_result))

        # 按索引排序
        results.sort(key=lambda x: x[0])
        return results

    def _validate_sequential(
        self, rule_key: str, batch_items: list[BatchValidationItem]
    ) -> list[tuple[int, ValidationResult]]:
        """顺序校验"""
        results = []

        for item in batch_items:
            try:
                result = self._validate_single_item(rule_key, item)
                results.append((item.index, result))
            except ServiceError:
                # ServiceError需要重新抛出，不应该被捕获
                raise
            except Exception as e:
                logger.error(f"顺序校验项 {item.index} 失败: {str(e)}")
                # 创建错误结果
                error_result = ValidationResult(
                    valid=False,
                    errors=[
                        {
                            "field_name": "system",
                            "chinese_name": "系统",
                            "error_code": "VALIDATION_EXCEPTION",
                            "error_message": f"校验异常: {str(e)}",
                            "error_value": None,
                            "rule_type": "system",
                            "suggestions": ["请检查数据格式或联系系统管理员"],
                        }
                    ],
                    warnings=[],
                    field_count=len(item.data),
                    validated_fields=list(item.data.keys()),
                    duration=0.0,
                )
                results.append((item.index, error_result))

        return results

    def _validate_single_item(self, rule_key: str, item: BatchValidationItem) -> ValidationResult:
        """校验单个项目"""
        return self.validation_engine.validate_data(rule_key, item.data)

    def _generate_report(
        self,
        rule_key: str,
        batch_items: list[BatchValidationItem],
        validation_results: list[tuple[int, ValidationResult]],
        start_time: float,
        include_valid_data: bool,
    ) -> BatchValidationReport:
        """生成批量校验报告"""
        total_items = len(batch_items)
        valid_items = 0
        invalid_items = 0
        errors = []
        warnings = []
        total_duration = time.perf_counter() - start_time

        # 处理校验结果
        for index, result in validation_results:
            if result.valid:
                valid_items += 1
            else:
                invalid_items += 1
                # 转换错误格式
                error_dicts = []
                if hasattr(result, "errors") and result.errors:
                    for error in result.errors:
                        if hasattr(error, "to_dict"):
                            error_dicts.append(error.to_dict())
                        elif isinstance(error, dict):
                            error_dicts.append(error)
                        else:
                            error_dicts.append(
                                {
                                    "field_name": "unknown",
                                    "chinese_name": "未知字段",
                                    "error_code": "UNKNOWN_ERROR",
                                    "error_message": str(error),
                                    "error_value": None,
                                    "rule_type": "unknown",
                                    "suggestions": [],
                                }
                            )

                batch_error = BatchValidationError(
                    index=index,
                    row_id=batch_items[index].row_id,
                    errors=error_dicts,
                    warnings=result.warnings if hasattr(result, "warnings") else [],
                    data=batch_items[index].data if include_valid_data else {},
                )
                errors.append(batch_error)

            # 收集警告
            if hasattr(result, "warnings") and result.warnings:
                warnings.extend([f"行{index}: {warning}" for warning in result.warnings])

        # 计算成功率
        success_rate = valid_items / total_items if total_items > 0 else 0.0

        # 计算平均耗时
        total_validation_duration = sum(
            result.duration for _, result in validation_results if hasattr(result, "duration")
        )
        average_duration = total_validation_duration / total_items if total_items > 0 else 0.0

        return BatchValidationReport(
            total_items=total_items,
            valid_items=valid_items,
            invalid_items=invalid_items,
            success_rate=success_rate,
            total_duration=total_duration,
            average_duration=average_duration,
            errors=errors,
            warnings=warnings,
            metadata={
                "rule_key": rule_key,
                "max_workers": self.max_workers,
                "batch_size": self.batch_size,
                "validation_duration": total_validation_duration,
                "processing_overhead": max(0, total_duration - total_validation_duration),
                "validated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            },
        )

    def get_validation_summary(self, rule_key: str) -> dict[str, Any]:
        """获取校验规则摘要"""
        try:
            rules = self.validation_engine.get_validation_rules(rule_key)

            rule_types = {}
            for rule in rules:
                rule_type = rule.rule_type.value
                if rule_type not in rule_types:
                    rule_types[rule_type] = 0
                rule_types[rule_type] += 1

            return {
                "rule_key": rule_key,
                "total_rules": len(rules),
                "rule_types": rule_types,
                "required_fields": [rule.field_name for rule in rules if rule.is_required],
                "cache_stats": self.validation_engine.get_cache_stats(),
            }

        except Exception as e:
            logger.error(f"获取校验规则摘要失败: rule_key={rule_key}, error={str(e)}")
            return {"rule_key": rule_key, "error": str(e)}
