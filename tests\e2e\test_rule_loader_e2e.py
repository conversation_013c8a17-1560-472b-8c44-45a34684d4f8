"""
端到端测试场景
模拟真实的规则加载使用场景，包括主从节点数据同步、规则验证等完整工作流
"""

import asyncio
import gzip
import json
import os
import tempfile
import time
import unittest
from unittest.mock import Mock, patch

from core.rule_cache import RULE_CACHE
from services.rule_loader import load_rules_from_file


class TestE2ERuleLoadingWorkflow(unittest.TestCase):
    """端到端规则加载工作流测试"""

    def setUp(self):
        """E2E测试准备"""
        # 清空规则缓存
        RULE_CACHE.clear()

        # 创建临时目录模拟真实环境
        self.temp_dir = tempfile.mkdtemp()
        self.master_cache_path = os.path.join(self.temp_dir, "master_rules_cache.json.gz")
        self.slave_cache_path = os.path.join(self.temp_dir, "slave_rules_cache.json.gz")
        self.version_path = os.path.join(self.temp_dir, "rules_version.txt")

        # 创建真实的医疗规则测试数据
        self.create_realistic_medical_rules_data()

    def tearDown(self):
        """E2E测试清理"""
        import shutil

        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        RULE_CACHE.clear()

    def create_realistic_medical_rules_data(self):
        """创建真实的医疗规则数据"""
        # 模拟医疗规则数据结构
        medical_rules_data = {
            "version": "v2.0",
            "metadata": {
                "export_timestamp": "2025-07-27T10:00:00Z",
                "total_templates": 5,
                "total_details": 15,
                "export_source": "master_node",
                "data_integrity_hash": "abc123def456",
            },
            "templates": [
                {
                    "rule_template_id": "template_med_001",
                    "rule_key": "yb_drug_dose_check",
                    "rule_name": "医保药品剂量检查",
                    "rule_category": "医保合规",
                    "status": "active",
                    "created_at": "2025-01-01T00:00:00Z",
                },
                {
                    "rule_template_id": "template_med_002",
                    "rule_key": "diagnosis_drug_match",
                    "rule_name": "诊断与用药匹配检查",
                    "rule_category": "临床合理性",
                    "status": "active",
                    "created_at": "2025-01-02T00:00:00Z",
                },
                {
                    "rule_template_id": "template_med_003",
                    "rule_key": "patient_age_drug_limit",
                    "rule_name": "患者年龄用药限制",
                    "rule_category": "用药安全",
                    "status": "active",
                    "created_at": "2025-01-03T00:00:00Z",
                },
                {
                    "rule_template_id": "template_med_004",
                    "rule_key": "drug_interaction_check",
                    "rule_name": "药物相互作用检查",
                    "rule_category": "用药安全",
                    "status": "active",
                    "created_at": "2025-01-04T00:00:00Z",
                },
                {
                    "rule_template_id": "template_med_005",
                    "rule_key": "medical_expense_limit",
                    "rule_name": "医疗费用限制检查",
                    "rule_category": "费用控制",
                    "status": "active",
                    "created_at": "2025-01-05T00:00:00Z",
                },
            ],
            "details": [
                # 医保药品剂量检查规则明细
                {
                    "rule_detail_id": "detail_med_001_001",
                    "rule_key": "yb_drug_dose_check",
                    "rule_name": "胰岛素日剂量检查",
                    "level1": "错误",
                    "level2": "用药剂量",
                    "level3": "超量使用",
                    "error_reason": "胰岛素日用量超过规定限制",
                    "error_severity": "高",
                    "yb_code": "A10A",
                    "max_daily_dose": 100,
                    "unit": "IU",
                },
                {
                    "rule_detail_id": "detail_med_001_002",
                    "rule_key": "yb_drug_dose_check",
                    "rule_name": "抗生素剂量检查",
                    "level1": "警告",
                    "level2": "用药剂量",
                    "level3": "可能超量",
                    "error_reason": "抗生素用量接近上限",
                    "error_severity": "中",
                    "yb_code": "J01",
                    "max_daily_dose": 4000,
                    "unit": "mg",
                },
                {
                    "rule_detail_id": "detail_med_001_003",
                    "rule_key": "yb_drug_dose_check",
                    "rule_name": "镇痛药剂量检查",
                    "level1": "错误",
                    "level2": "用药剂量",
                    "level3": "超量使用",
                    "error_reason": "镇痛药日用量超过安全限制",
                    "error_severity": "高",
                    "yb_code": "N02A",
                    "max_daily_dose": 4000,
                    "unit": "mg",
                },
                # 诊断与用药匹配检查规则明细
                {
                    "rule_detail_id": "detail_med_002_001",
                    "rule_key": "diagnosis_drug_match",
                    "rule_name": "糖尿病用药匹配",
                    "level1": "错误",
                    "level2": "诊疗匹配",
                    "level3": "用药不符",
                    "error_reason": "糖尿病诊断与用药不匹配",
                    "error_severity": "高",
                    "diagnosis_code": "E11",
                    "required_drug_category": "A10",
                },
                {
                    "rule_detail_id": "detail_med_002_002",
                    "rule_key": "diagnosis_drug_match",
                    "rule_name": "高血压用药匹配",
                    "level1": "警告",
                    "level2": "诊疗匹配",
                    "level3": "用药建议",
                    "error_reason": "高血压诊断建议使用指定类别药物",
                    "error_severity": "中",
                    "diagnosis_code": "I10",
                    "recommended_drug_category": "C09",
                },
                {
                    "rule_detail_id": "detail_med_002_003",
                    "rule_key": "diagnosis_drug_match",
                    "rule_name": "感染用药匹配",
                    "level1": "错误",
                    "level2": "诊疗匹配",
                    "level3": "用药不当",
                    "error_reason": "感染诊断与抗生素选择不匹配",
                    "error_severity": "高",
                    "diagnosis_code": "A49",
                    "required_drug_category": "J01",
                },
                # 患者年龄用药限制规则明细
                {
                    "rule_detail_id": "detail_med_003_001",
                    "rule_key": "patient_age_drug_limit",
                    "rule_name": "儿童用药限制",
                    "level1": "错误",
                    "level2": "年龄限制",
                    "level3": "禁用药物",
                    "error_reason": "儿童禁用该类药物",
                    "error_severity": "高",
                    "min_age": 0,
                    "max_age": 12,
                    "prohibited_drug_codes": ["N02BA01", "M01AE01"],
                },
                {
                    "rule_detail_id": "detail_med_003_002",
                    "rule_key": "patient_age_drug_limit",
                    "rule_name": "老年人用药限制",
                    "level1": "警告",
                    "level2": "年龄限制",
                    "level3": "慎用药物",
                    "error_reason": "老年人慎用该类药物",
                    "error_severity": "中",
                    "min_age": 65,
                    "max_age": 120,
                    "caution_drug_codes": ["N05A", "N05B"],
                },
                {
                    "rule_detail_id": "detail_med_003_003",
                    "rule_key": "patient_age_drug_limit",
                    "rule_name": "孕妇用药限制",
                    "level1": "错误",
                    "level2": "特殊人群",
                    "level3": "禁用药物",
                    "error_reason": "孕妇禁用该类药物",
                    "error_severity": "高",
                    "patient_condition": "pregnancy",
                    "prohibited_drug_codes": ["M01AE", "N02BA"],
                },
                # 药物相互作用检查规则明细
                {
                    "rule_detail_id": "detail_med_004_001",
                    "rule_key": "drug_interaction_check",
                    "rule_name": "华法林相互作用",
                    "level1": "错误",
                    "level2": "药物相互作用",
                    "level3": "严重相互作用",
                    "error_reason": "华法林与该药物存在严重相互作用",
                    "error_severity": "高",
                    "primary_drug": "B01AA03",
                    "interaction_drugs": ["J01FA", "N02BE01"],
                },
                {
                    "rule_detail_id": "detail_med_004_002",
                    "rule_key": "drug_interaction_check",
                    "rule_name": "地高辛相互作用",
                    "level1": "警告",
                    "level2": "药物相互作用",
                    "level3": "中度相互作用",
                    "error_reason": "地高辛与该药物可能存在相互作用",
                    "error_severity": "中",
                    "primary_drug": "C01AA05",
                    "interaction_drugs": ["A02BC", "C07AB"],
                },
                {
                    "rule_detail_id": "detail_med_004_003",
                    "rule_key": "drug_interaction_check",
                    "rule_name": "胰岛素相互作用",
                    "level1": "警告",
                    "level2": "药物相互作用",
                    "level3": "影响血糖",
                    "error_reason": "该药物可能影响胰岛素效果",
                    "error_severity": "中",
                    "primary_drug": "A10A",
                    "interaction_drugs": ["H02AB", "C07A"],
                },
                # 医疗费用限制检查规则明细
                {
                    "rule_detail_id": "detail_med_005_001",
                    "rule_key": "medical_expense_limit",
                    "rule_name": "门诊费用限制",
                    "level1": "警告",
                    "level2": "费用控制",
                    "level3": "超限提醒",
                    "error_reason": "门诊费用超过限制",
                    "error_severity": "中",
                    "expense_type": "outpatient",
                    "max_amount": 5000,
                    "period": "monthly",
                },
                {
                    "rule_detail_id": "detail_med_005_002",
                    "rule_key": "medical_expense_limit",
                    "rule_name": "住院费用限制",
                    "level1": "错误",
                    "level2": "费用控制",
                    "level3": "超限拒绝",
                    "error_reason": "住院费用超过年度限制",
                    "error_severity": "高",
                    "expense_type": "inpatient",
                    "max_amount": 50000,
                    "period": "yearly",
                },
                {
                    "rule_detail_id": "detail_med_005_003",
                    "rule_key": "medical_expense_limit",
                    "rule_name": "药品费用限制",
                    "level1": "警告",
                    "level2": "费用控制",
                    "level3": "药品超限",
                    "error_reason": "药品费用超过比例限制",
                    "error_severity": "中",
                    "expense_type": "medication",
                    "max_percentage": 0.6,
                    "period": "per_visit",
                },
            ],
            "field_metadata": [
                {"field_id": "field_001", "field_name": "yb_code", "field_type": "string", "description": "医保编码"},
                {
                    "field_id": "field_002",
                    "field_name": "diagnosis_code",
                    "field_type": "string",
                    "description": "诊断编码",
                },
                {
                    "field_id": "field_003",
                    "field_name": "max_daily_dose",
                    "field_type": "number",
                    "description": "最大日剂量",
                },
                {
                    "field_id": "field_004",
                    "field_name": "patient_condition",
                    "field_type": "string",
                    "description": "患者状况",
                },
            ],
        }

        # 保存主节点缓存文件
        with gzip.open(self.master_cache_path, "wt", encoding="utf-8") as f:
            json.dump(medical_rules_data, f, ensure_ascii=False, indent=2)

        # 保存版本信息
        with open(self.version_path, "w") as f:
            f.write("v2.0_medical_rules_20250727_100000")

    def test_complete_master_to_slave_sync_workflow(self):
        """测试完整的主从节点同步工作流"""
        print("\n=== 开始端到端主从同步测试 ===")

        # 1. 模拟主节点生成缓存文件
        print("1. 主节点生成规则缓存文件")
        self.assertTrue(os.path.exists(self.master_cache_path))
        self.assertTrue(os.path.exists(self.version_path))

        # 验证文件大小合理
        file_size = os.path.getsize(self.master_cache_path)
        self.assertGreater(file_size, 1000)  # 应该大于1KB
        print(f"   缓存文件大小: {file_size} bytes")

        # 2. 模拟文件传输（复制到从节点位置）
        print("2. 模拟文件传输到从节点")
        import shutil

        shutil.copy2(self.master_cache_path, self.slave_cache_path)

        # 3. 从节点加载规则缓存
        print("3. 从节点加载规则缓存")

        with patch("services.rule_loader.LOCAL_RULES_PATH", self.slave_cache_path):
            with patch("services.rule_loader.LOCAL_VERSION_PATH", self.version_path):
                # 模拟RuleDataSyncService成功加载
                with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                    mock_stats = Mock()
                    mock_stats.total_templates = 5
                    mock_stats.total_details = 15
                    mock_stats.sync_duration = 0.8
                    mock_stats.cache_size_mb = 2.5

                    mock_instance = Mock()
                    mock_instance.load_from_cache.return_value = mock_stats
                    mock_sync_service.return_value = mock_instance

                    # 执行加载
                    start_time = time.time()
                    result = asyncio.run(load_rules_from_file())
                    load_time = time.time() - start_time

                    self.assertTrue(result)
                    print(f"   加载成功，耗时: {load_time:.3f}s")
                    print(f"   模板数量: {mock_stats.total_templates}")
                    print(f"   规则详情数量: {mock_stats.total_details}")

        # 4. 验证缓存内容正确性
        print("4. 验证缓存内容正确性")
        # 注意：由于使用了mock，实际的RULE_CACHE不会被填充
        # 在真实环境中，这里会验证RULE_CACHE的内容
        print("   缓存验证完成（模拟环境）")

        print("=== 主从同步测试完成 ===\n")

    def test_rule_validation_workflow(self):
        """测试规则验证工作流"""
        print("\n=== 开始规则验证工作流测试 ===")

        # 1. 模拟加载规则到缓存
        print("1. 加载规则到缓存")

        with patch("services.rule_loader.LOCAL_RULES_PATH", self.master_cache_path):
            with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                mock_stats = Mock()
                mock_stats.total_templates = 5
                mock_stats.total_details = 15
                mock_stats.sync_duration = 0.5
                mock_stats.cache_size_mb = 2.0

                mock_instance = Mock()
                mock_instance.load_from_cache.return_value = mock_stats
                mock_sync_service.return_value = mock_instance

                result = asyncio.run(load_rules_from_file())
                self.assertTrue(result)
                print("   规则加载成功")

        # 2. 模拟患者数据验证
        print("2. 模拟患者数据验证")

        # 创建模拟的患者数据
        patient_data = {
            "patient_id": "P001",
            "age": 75,
            "diagnosis_codes": ["E11.9", "I10"],  # 2型糖尿病，高血压
            "medications": [
                {
                    "drug_code": "A10BA02",  # 二甲双胍
                    "daily_dose": 1000,
                    "unit": "mg",
                },
                {
                    "drug_code": "C09AA02",  # 依那普利
                    "daily_dose": 10,
                    "unit": "mg",
                },
            ],
            "expenses": {"outpatient_monthly": 3000, "medication_percentage": 0.4},
        }

        # 3. 模拟规则验证过程
        print("3. 执行规则验证")

        # 模拟验证结果
        validation_results = [
            {
                "rule_id": "detail_med_002_001",
                "rule_name": "糖尿病用药匹配",
                "result": "通过",
                "message": "糖尿病诊断与用药匹配正确",
            },
            {
                "rule_id": "detail_med_003_002",
                "rule_name": "老年人用药限制",
                "result": "警告",
                "message": "患者年龄75岁，建议关注用药安全",
            },
            {
                "rule_id": "detail_med_005_001",
                "rule_name": "门诊费用限制",
                "result": "通过",
                "message": "门诊费用在限制范围内",
            },
        ]

        print(f"   验证了 {len(validation_results)} 条规则")
        for result in validation_results:
            print(f"   - {result['rule_name']}: {result['result']} - {result['message']}")

        # 4. 验证性能指标
        print("4. 验证性能指标")
        validation_time = 0.05  # 模拟验证时间
        self.assertLess(validation_time, 0.1)  # 验证时间应小于100ms
        print(f"   验证耗时: {validation_time*1000:.1f}ms (目标: <100ms)")

        print("=== 规则验证工作流测试完成 ===\n")

    def test_error_recovery_workflow(self):
        """测试错误恢复工作流"""
        print("\n=== 开始错误恢复工作流测试 ===")

        # 1. 模拟缓存文件损坏
        print("1. 模拟缓存文件损坏")
        corrupted_file = os.path.join(self.temp_dir, "corrupted_cache.json.gz")
        with open(corrupted_file, "wb") as f:
            f.write(b"corrupted data")

        # 2. 尝试加载损坏的文件
        print("2. 尝试加载损坏的缓存文件")

        with patch("services.rule_loader.LOCAL_RULES_PATH", corrupted_file):
            with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                # 模拟同步服务失败
                mock_sync_service.side_effect = Exception("Failed to load corrupted cache")

                result = asyncio.run(load_rules_from_file())
                self.assertFalse(result)
                print("   检测到文件损坏，加载失败")

        # 3. 模拟从备份恢复
        print("3. 模拟从备份文件恢复")

        backup_file = os.path.join(self.temp_dir, "backup_cache.json.gz")
        import shutil

        shutil.copy2(self.master_cache_path, backup_file)

        with patch("services.rule_loader.LOCAL_RULES_PATH", backup_file):
            with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                mock_stats = Mock()
                mock_stats.total_templates = 5
                mock_stats.total_details = 15
                mock_stats.sync_duration = 1.0
                mock_stats.cache_size_mb = 2.5

                mock_instance = Mock()
                mock_instance.load_from_cache.return_value = mock_stats
                mock_sync_service.return_value = mock_instance

                result = asyncio.run(load_rules_from_file())
                self.assertTrue(result)
                print("   从备份文件恢复成功")

        # 4. 验证系统稳定性
        print("4. 验证系统稳定性")
        print("   系统能够检测文件损坏并进行恢复")
        print("   错误处理机制正常工作")

        print("=== 错误恢复工作流测试完成 ===\n")

    def test_performance_benchmark_workflow(self):
        """测试性能基准工作流"""
        print("\n=== 开始性能基准测试 ===")

        # 1. 创建大型规则数据集
        print("1. 创建大型规则数据集")
        large_cache_file = os.path.join(self.temp_dir, "large_cache.json.gz")
        self.create_large_rule_dataset(large_cache_file, 100, 500)  # 100个模板，500个详情

        file_size = os.path.getsize(large_cache_file)
        print(f"   大型数据集文件大小: {file_size/1024/1024:.2f} MB")

        # 2. 测试加载性能
        print("2. 测试加载性能")

        load_times = []
        for i in range(3):  # 执行3次测试
            with patch("services.rule_loader.LOCAL_RULES_PATH", large_cache_file):
                with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                    mock_stats = Mock()
                    mock_stats.total_templates = 100
                    mock_stats.total_details = 500
                    mock_stats.sync_duration = 0.5
                    mock_stats.cache_size_mb = file_size / 1024 / 1024

                    mock_instance = Mock()
                    mock_instance.load_from_cache.return_value = mock_stats
                    mock_sync_service.return_value = mock_instance

                    start_time = time.time()
                    result = asyncio.run(load_rules_from_file())
                    load_time = time.time() - start_time

                    self.assertTrue(result)
                    load_times.append(load_time)
                    print(f"   第{i+1}次加载: {load_time:.3f}s")

        # 3. 分析性能指标
        print("3. 分析性能指标")
        avg_load_time = sum(load_times) / len(load_times)
        max_load_time = max(load_times)
        min_load_time = min(load_times)

        print(f"   平均加载时间: {avg_load_time:.3f}s")
        print(f"   最大加载时间: {max_load_time:.3f}s")
        print(f"   最小加载时间: {min_load_time:.3f}s")

        # 验证性能目标
        self.assertLess(avg_load_time, 2.0)  # 平均加载时间应小于2秒
        self.assertLess(max_load_time, 3.0)  # 最大加载时间应小于3秒

        # 4. 测试内存使用
        print("4. 测试内存使用")
        from services.rule_loader import _optimize_memory_usage_enhanced

        start_time = time.time()
        _optimize_memory_usage_enhanced()
        optimization_time = time.time() - start_time

        self.assertLess(optimization_time, 1.0)  # 内存优化应小于1秒
        print(f"   内存优化时间: {optimization_time:.3f}s")

        print("=== 性能基准测试完成 ===\n")

    def create_large_rule_dataset(self, file_path, template_count, detail_count):
        """创建大型规则数据集"""
        large_data = {
            "version": "v2.0",
            "metadata": {
                "export_timestamp": "2025-07-27T10:00:00Z",
                "total_templates": template_count,
                "total_details": detail_count,
            },
            "templates": [],
            "details": [],
        }

        # 生成模板数据
        for i in range(template_count):
            template = {
                "rule_template_id": f"template_perf_{i:03d}",
                "rule_key": f"perf_rule_{i:03d}",
                "rule_name": f"性能测试规则{i}",
                "rule_category": f"测试类别{i % 10}",
                "status": "active",
            }
            large_data["templates"].append(template)

        # 生成详情数据（每个模板多个详情）
        details_per_template = detail_count // template_count
        for i in range(template_count):
            for j in range(details_per_template):
                detail = {
                    "rule_detail_id": f"detail_perf_{i:03d}_{j:03d}",
                    "rule_key": f"perf_rule_{i:03d}",
                    "rule_name": f"性能测试规则{i}详情{j}",
                    "level1": "错误" if j % 2 == 0 else "警告",
                    "level2": f"类别{j % 5}",
                    "level3": f"子类别{j % 3}",
                    "error_reason": f"性能测试错误原因{i}_{j}",
                    "error_severity": "高" if j % 3 == 0 else "中",
                }
                large_data["details"].append(detail)

        # 保存为压缩文件
        with gzip.open(file_path, "wt", encoding="utf-8") as f:
            json.dump(large_data, f, ensure_ascii=False)

    def test_backward_compatibility_workflow(self):
        """测试向后兼容性工作流"""
        print("\n=== 开始向后兼容性测试 ===")

        # 1. 创建不同版本的缓存文件
        print("1. 创建不同版本的缓存文件")

        # v1.5格式文件
        v1_5_file = os.path.join(self.temp_dir, "v1_5_cache.json.gz")
        v1_5_data = {
            "version": "v1.5",
            "rule_datasets": [
                {
                    "id": "rule_compat_001",
                    "rule_key": "compatibility_test",
                    "error_level_1": "错误",
                    "error_level_2": "兼容性",
                    "error_level_3": "测试",
                    "error_reason": "向后兼容性测试规则",
                }
            ],
            "total_count": 1,
        }

        with gzip.open(v1_5_file, "wt", encoding="utf-8") as f:
            json.dump(v1_5_data, f, ensure_ascii=False)

        # 2. 测试v1.5格式加载
        print("2. 测试v1.5格式加载")

        from services.rule_loader import _detect_cache_format

        format_info = _detect_cache_format(v1_5_file)

        self.assertEqual(format_info["format_version"], "v1.5")
        self.assertEqual(format_info["format_type"], "enhanced_dataset")
        print(f"   检测到v1.5格式，置信度: {format_info['detection_confidence']:.2f}")

        # 3. 测试降级处理
        print("3. 测试降级处理")

        with patch("services.rule_loader.LOCAL_RULES_PATH", v1_5_file):
            with patch("services.rule_loader.RuleDataSyncService") as mock_sync_service:
                # 模拟新版本加载失败，触发降级
                mock_sync_service.side_effect = Exception("Unsupported format")

                result = asyncio.run(load_rules_from_file())
                self.assertFalse(result)  # 不再支持降级，应该失败
                print("   不再支持旧版本降级，加载失败")

        # 4. 验证v2.0格式优势
        print("4. 验证v2.0格式优势")

        v2_format_info = _detect_cache_format(self.master_cache_path)
        v1_5_format_info = _detect_cache_format(v1_5_file)

        self.assertGreater(v2_format_info["detection_confidence"], v1_5_format_info["detection_confidence"])
        print(f"   v2.0格式置信度: {v2_format_info['detection_confidence']:.2f}")
        print(f"   v1.5格式置信度: {v1_5_format_info['detection_confidence']:.2f}")

        print("=== 向后兼容性测试完成 ===\n")


if __name__ == "__main__":
    # 运行端到端测试
    print("开始执行子节点数据加载重构端到端测试套件")
    print("=" * 60)

    unittest.main(verbosity=2)

    print("=" * 60)
    print("端到端测试套件执行完成")
