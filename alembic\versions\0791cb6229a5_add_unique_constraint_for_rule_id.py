"""add_unique_constraint_for_rule_id

为rule_detail表添加rule_id唯一约束，防止重复数据插入

Revision ID: 0791cb6229a5
Revises: 7df46d6ce308
Create Date: 2025-07-30 22:36:20.922454

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0791cb6229a5"
down_revision: str | None = "7df46d6ce308"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """添加rule_id唯一约束"""
    # 1. 首先清理可能存在的重复数据（当前数据库为空，但为了安全起见保留此逻辑）
    connection = op.get_bind()

    # 查找重复的rule_id记录
    duplicate_check = connection.execute(
        sa.text("""
        SELECT rule_id, rule_key, COUNT(*) as count
        FROM rule_detail
        GROUP BY rule_id, rule_key
        HAVING COUNT(*) > 1
    """)
    ).fetchall()

    if duplicate_check:
        print(f"发现 {len(duplicate_check)} 组重复数据，开始清理...")

        # 对于每组重复数据，保留最新的记录（基于created_at），删除其他记录
        for rule_id, rule_key, count in duplicate_check:
            print(f"清理重复数据: rule_id={rule_id}, rule_key={rule_key}, 重复数量={count}")

            # 删除除了最新记录之外的所有重复记录
            connection.execute(
                sa.text("""
                DELETE FROM rule_detail
                WHERE rule_id = :rule_id AND rule_key = :rule_key
                AND id NOT IN (
                    SELECT * FROM (
                        SELECT id FROM rule_detail
                        WHERE rule_id = :rule_id AND rule_key = :rule_key
                        ORDER BY created_at DESC, id DESC
                        LIMIT 1
                    ) as latest
                )
            """),
                {"rule_id": rule_id, "rule_key": rule_key},
            )

        print("重复数据清理完成")
    else:
        print("未发现重复数据，跳过清理步骤")

    # 2. 添加唯一约束
    # 使用组合唯一约束 (rule_id, rule_key) 确保在不同规则类型中rule_id可以重复
    # 但在同一规则类型中rule_id必须唯一
    try:
        op.create_unique_constraint(
            "uk_rule_detail_rule_id_key",  # 约束名称
            "rule_detail",  # 表名
            ["rule_id", "rule_key"],  # 字段组合
        )
        print("✅ 成功添加唯一约束: uk_rule_detail_rule_id_key (rule_id, rule_key)")
    except Exception as e:
        print(f"❌ 添加唯一约束失败: {e}")
        raise


def downgrade() -> None:
    """移除rule_id唯一约束"""
    try:
        op.drop_constraint("uk_rule_detail_rule_id_key", "rule_detail", type_="unique")
        print("✅ 成功移除唯一约束: uk_rule_detail_rule_id_key")
    except Exception as e:
        print(f"❌ 移除唯一约束失败: {e}")
        raise
