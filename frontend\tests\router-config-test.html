<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-link {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 16px;
            background: #409EFF;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #337ecc;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .error {
            background: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <h1>路由配置测试页面</h1>
    <p>点击下面的链接测试各个路由是否正常工作：</p>

    <div class="test-section">
        <h2>主要功能路由</h2>
        <a href="http://localhost:3001/" class="test-link" target="_blank">首页 - 模板状态仪表盘</a>
        <a href="http://localhost:3001/rules/management" class="test-link" target="_blank">规则配置管理</a>
        <div class="status success">
            ✓ 这些是核心功能路由，应该正常工作
        </div>
    </div>

    <div class="test-section">
        <h2>规则明细管理路由（动态路由）</h2>
        <a href="http://localhost:3001/rules/ch_drug_deny_use/details" class="test-link" target="_blank">中药饮片规则明细</a>
        <a href="http://localhost:3001/rules/drug_limit_children/details" class="test-link" target="_blank">儿童用药规则明细</a>
        <div class="status success">
            ✓ 测试动态路由参数传递和规则明细CRUD界面
        </div>
    </div>

    <div class="test-section">
        <h2>数据上传路由（重构后）</h2>
        <a href="http://localhost:3001/rules/ch_drug_deny_use/upload" class="test-link" target="_blank">中药饮片数据上传</a>
        <a href="http://localhost:3001/rules/drug_limit_children/upload" class="test-link" target="_blank">儿童用药数据上传</a>
        <div class="status success">
            ✓ 测试与任务5.3.2数据上传界面重构的集成
        </div>
    </div>

    <div class="test-section">
        <h2>兼容性路由（重定向）</h2>
        <a href="http://localhost:3001/upload/ch_drug_deny_use" class="test-link" target="_blank">旧版上传路由（应重定向）</a>
        <a href="http://localhost:3001/degradation" class="test-link" target="_blank">旧版降级监控（应重定向）</a>
        <div class="status success">
            ✓ 测试向后兼容性，旧路由应自动重定向到新路由
        </div>
    </div>

    <div class="test-section">
        <h2>管理员专用路由</h2>
        <a href="http://localhost:3001/test" class="test-link" target="_blank">API连接测试</a>
        <a href="http://localhost:3001/monitoring/degradation" class="test-link" target="_blank">降级状态监控</a>
        <a href="http://localhost:3001/demo/rule-details" class="test-link" target="_blank">规则明细演示</a>
        <div class="status success">
            ✓ 这些路由需要管理员权限，测试权限控制
        </div>
    </div>

    <div class="test-section">
        <h2>错误页面测试</h2>
        <a href="http://localhost:3001/nonexistent-page" class="test-link" target="_blank">404页面测试</a>
        <a href="http://localhost:3001/unauthorized" class="test-link" target="_blank">403权限拒绝页面</a>
        <div class="status success">
            ✓ 测试错误页面的用户体验和导航功能
        </div>
    </div>

    <div class="test-section">
        <h2>面包屑导航测试</h2>
        <p>访问以下页面时，应该看到完整的面包屑导航：</p>
        <a href="http://localhost:3001/rules/ch_drug_deny_use/details" class="test-link" target="_blank">规则明细页面</a>
        <a href="http://localhost:3001/rules/ch_drug_deny_use/upload" class="test-link" target="_blank">数据上传页面</a>
        <div class="status success">
            ✓ 面包屑应显示：首页 > 规则管理 > 规则配置 > [当前页面]
        </div>
    </div>

    <div class="test-section">
        <h2>路由懒加载测试</h2>
        <p>打开浏览器开发者工具的Network面板，观察页面加载：</p>
        <ul>
            <li>首次访问首页时，只应加载必要的资源</li>
            <li>点击导航到其他页面时，应动态加载对应的组件</li>
            <li>页面切换应该流畅，没有明显的加载延迟</li>
        </ul>
        <div class="status success">
            ✓ 验证路由懒加载优化效果
        </div>
    </div>

    <script>
        // 简单的测试脚本
        document.addEventListener('DOMContentLoaded', function() {
            console.log('路由配置测试页面已加载');
            console.log('请点击上面的链接测试各个路由功能');
            
            // 检查当前是否在开发环境
            if (window.location.hostname === 'localhost') {
                console.log('✓ 检测到开发环境，可以进行完整测试');
            } else {
                console.warn('⚠ 非开发环境，请确保服务正在运行');
            }
        });
    </script>
</body>
</html>
