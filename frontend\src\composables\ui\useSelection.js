import { ref, computed } from 'vue'

/**
 * 选择功能 Composable
 * 提供多选、单选、全选等功能
 */
export function useSelection(options = {}) {
  const {
    multiple = true,
    keyField = 'id'
  } = options

  // 响应式状态
  const selectedItems = ref([])

  // 计算属性
  const selectedCount = computed(() => selectedItems.value.length)

  const hasSelection = computed(() => selectedCount.value > 0)

  const selectedIds = computed(() =>
    selectedItems.value.map(item => item[keyField])
  )

  // 检查项目是否被选中
  const isSelected = (item) => {
    return selectedItems.value.some(selected =>
      selected[keyField] === item[keyField]
    )
  }

  // 选择单个项目
  const selectItem = (item) => {
    if (!multiple) {
      selectedItems.value = [item]
      return
    }

    if (!isSelected(item)) {
      selectedItems.value.push(item)
    }
  }

  // 取消选择单个项目
  const unselectItem = (item) => {
    const index = selectedItems.value.findIndex(selected =>
      selected[keyField] === item[keyField]
    )

    if (index > -1) {
      selectedItems.value.splice(index, 1)
    }
  }

  // 切换选择状态
  const toggleItem = (item) => {
    if (isSelected(item)) {
      unselectItem(item)
    } else {
      selectItem(item)
    }
  }

  // 处理项目选择（支持复选框事件）
  const handleItemSelect = ({ item, checked }) => {
    if (checked) {
      selectItem(item)
    } else {
      unselectItem(item)
    }
  }

  // 全选
  const selectAll = (items) => {
    if (!multiple) return

    selectedItems.value = [...items]
  }

  // 取消全选
  const unselectAll = () => {
    selectedItems.value = []
  }

  // 反选
  const invertSelection = (items) => {
    if (!multiple) return

    const newSelection = items.filter(item => !isSelected(item))
    selectedItems.value = newSelection
  }

  // 检查是否全选
  const isAllSelected = (items) => {
    if (!items || items.length === 0) return false
    return items.every(item => isSelected(item))
  }

  // 检查是否部分选择
  const isIndeterminate = (items) => {
    if (!items || items.length === 0) return false
    const selectedInItems = items.filter(item => isSelected(item))
    return selectedInItems.length > 0 && selectedInItems.length < items.length
  }

  // 处理全选复选框
  const handleSelectAll = (items, checked) => {
    if (checked) {
      selectAll(items)
    } else {
      unselectAll()
    }
  }

  // 根据条件选择
  const selectByCondition = (items, condition) => {
    if (!multiple) return

    const matchedItems = items.filter(condition)
    matchedItems.forEach(item => {
      if (!isSelected(item)) {
        selectedItems.value.push(item)
      }
    })
  }

  // 根据ID数组选择
  const selectByIds = (items, ids) => {
    if (!multiple && ids.length > 1) return

    const itemsToSelect = items.filter(item =>
      ids.includes(item[keyField])
    )

    if (!multiple) {
      selectedItems.value = itemsToSelect.slice(0, 1)
    } else {
      itemsToSelect.forEach(item => {
        if (!isSelected(item)) {
          selectedItems.value.push(item)
        }
      })
    }
  }

  // 清空选择
  const clearSelection = () => {
    selectedItems.value = []
  }

  // 设置选择项
  const setSelection = (items) => {
    if (!multiple && items.length > 1) {
      selectedItems.value = items.slice(0, 1)
    } else {
      selectedItems.value = [...items]
    }
  }

  // 获取选择的第一个项目
  const getFirstSelected = () => {
    return selectedItems.value[0] || null
  }

  // 获取选择项的指定字段值
  const getSelectedValues = (field = keyField) => {
    return selectedItems.value.map(item => item[field])
  }

  // 批量操作辅助函数
  const batchOperation = (operation, ...args) => {
    if (selectedCount.value === 0) {
      console.warn('没有选中的项目')
      return Promise.resolve([])
    }

    return Promise.all(
      selectedItems.value.map(item => operation(item, ...args))
    )
  }

  return {
    // 响应式数据
    selectedItems,
    selectedCount,
    hasSelection,
    selectedIds,

    // 基础方法
    isSelected,
    selectItem,
    unselectItem,
    toggleItem,
    handleItemSelect,

    // 批量操作
    selectAll,
    unselectAll,
    invertSelection,
    isAllSelected,
    isIndeterminate,
    handleSelectAll,

    // 条件选择
    selectByCondition,
    selectByIds,

    // 工具方法
    clearSelection,
    setSelection,
    getFirstSelected,
    getSelectedValues,
    batchOperation
  }
}
