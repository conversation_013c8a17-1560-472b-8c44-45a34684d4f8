<template>
  <div class="rule-status-card-new">
    <!-- 卡片头部 -->
    <div class="rule-header">
      <h3 class="rule-name" :title="rule.rule_name">{{ rule.rule_name }}</h3>
      <StatusTag :status="rule.status" />
    </div>

    <!-- 规则描述区域 -->
    <div class="rule-description" v-if="rule.description && rule.description.trim()">
      <el-tooltip
        :content="formatDescription(rule.description, false)"
        placement="top"
        effect="dark"
        :show-after="500"
        :hide-after="3000"
        :enterable="true"
        popper-class="rule-description-tooltip"
      >
        <div class="description-text" ref="descriptionRef">
          <p v-html="formatDescription(rule.description, true)"></p>
        </div>
      </el-tooltip>
    </div>
    <div class="rule-description-placeholder" v-else>
      <p class="placeholder-text">暂无规则描述</p>
    </div>

    <!-- 卡片操作区 -->
    <div class="rule-actions">
      <el-button
        size="small"
        :icon="View"
        @click="handleViewDetail"
        class="action-btn"
      >
        详情
      </el-button>
      <el-button
        size="small"
        :icon="Download"
        :loading="downloadLoading"
        @click="handleDownloadTemplate"
        class="action-btn"
      >
        模板
      </el-button>
      <el-button
        type="primary"
        size="small"
        :icon="Upload"
        @click="handleUploadData"
        class="action-btn action-btn-primary"
      >
        上传
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { View, Download, Upload } from '@element-plus/icons-vue'
// import RButton from '../../design-system/components/base/RButton.vue' // 改用Element Plus按钮
import StatusTag from '../common/StatusTag.vue'
import { formatDate } from '../../utils/dateUtils'

const props = defineProps({
  rule: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'view-detail',
  'download-template',
  'upload-data'
])

const downloadLoading = ref(false)
const descriptionRef = ref(null)

// 描述文本截断配置
const MAX_DESCRIPTION_LENGTH = 100

// 格式化描述文本
const formatDescription = (text, truncate = false) => {
  if (!text) return ''

  let formattedText = text.trim()

  // 如果需要截断且文本过长
  if (truncate && formattedText.length > MAX_DESCRIPTION_LENGTH) {
    formattedText = formattedText.substring(0, MAX_DESCRIPTION_LENGTH) + '...'
  }

  // 将换行符转换为HTML换行标签，并处理序号和缩进
  return formattedText
    .split('\n')
    .map(line => {
      // 识别并增强序号格式（如：1. 2. 等）
      return line.replace(/^(\d+\.)\s+(.*)/, '<span class="list-number">$1</span> $2')
    })
    .join('<br>')
}

// 计算截断后的描述文本
const truncatedDescription = computed(() => {
  if (!props.rule.description) return ''
  return formatDescription(props.rule.description, true)
})

// 判断描述是否被截断
const isDescriptionTruncated = computed(() => {
  if (!props.rule.description) return false
  return props.rule.description.trim().length > MAX_DESCRIPTION_LENGTH
})

const handleViewDetail = () => {
  emit('view-detail', props.rule)
}

const handleDownloadTemplate = async () => {
  downloadLoading.value = true
  try {
    emit('download-template', props.rule)
  } finally {
    // 延迟重置loading状态，提供更好的用户体验
    setTimeout(() => {
      downloadLoading.value = false
    }, 1000)
  }
}

const handleUploadData = () => {
  emit('upload-data', props.rule)
}

// 组件挂载后的处理
onMounted(() => {
  nextTick(() => {
    // 可以在这里添加一些初始化逻辑
  })
})
</script>

<style>
/* 全局样式，不使用scoped */
.rule-description-tooltip .el-tooltip__popper-content {
  white-space: pre-line !important;
}

.rule-description-tooltip .list-number {
  font-weight: 600;
  color: #409eff;
  margin-right: 2px;
}
</style>

<style scoped>
/* 新卡片样式 - 基于测试页面效果 */
.rule-status-card-new {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: auto;
  display: flex;
  flex-direction: column;
}

.rule-status-card-new:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 规则头部 */
.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.rule-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #303133;
  line-height: 1.4;

  /* 文本截断 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 规则描述区域 */
.rule-description {
  min-height: 60px;
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  flex: 1;
}

.description-text {
  width: 100%;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
  word-break: break-word;
  cursor: help;
}

.description-text p {
  margin: 0;
}

/* 增强列表项样式 */
.description-text .list-number {
  font-weight: 600;
  color: #409eff;
  display: inline-block;
  min-width: 20px;
}

.rule-description-placeholder {
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  flex: 1;
}

.placeholder-text {
  margin: 0;
  font-size: 14px;
  color: #C0C4CC;
  font-style: italic;
}

/* 规则操作区 */
.rule-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: auto;
}

/* 覆盖Element Plus按钮样式，使其与测试页面一致 */
.rule-actions :deep(.el-button) {
  padding: 8px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  height: auto;
  min-height: auto;
}

.rule-actions :deep(.el-button:hover) {
  border-color: #409eff;
  color: #409eff;
  background: white;
}

.rule-actions :deep(.el-button--primary) {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

.rule-actions :deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rule-status-card-new {
    padding: 16px;
    margin-bottom: 16px;
  }

  .rule-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .rule-name {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .rule-actions {
    width: 100%;
    justify-content: space-between;
  }

  .description-text {
    font-size: 13px;
  }

  .rule-description {
    min-height: 50px;
  }

  .rule-description-placeholder {
    min-height: 50px;
  }
}

@media (max-width: 575px) {
  .rule-status-card-new {
    padding: 12px;
    margin-bottom: 12px;
  }

  .rule-description,
  .rule-description-placeholder {
    min-height: 40px;
  }
}
</style>
