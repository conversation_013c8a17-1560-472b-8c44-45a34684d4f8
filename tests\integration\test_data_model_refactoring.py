"""
数据模型重构集成测试
验证新的数据模型功能是否正常工作
"""


from models.database import (
    FieldTypeEnum,
    RuleDetail,
    RuleDetailStatusEnum,
    RuleFieldMetadata,
    RuleTemplate,
    RuleTemplateStatusEnum,
)


class TestDataModelRefactoring:
    """测试数据模型重构的集成功能"""

    def test_rule_template_creation_and_validation(self):
        """测试规则模板创建和验证"""
        # 创建规则模板
        template_data = {
            "rule_key": "test_rule_001",
            "rule_type": "医保规则",
            "name": "测试规则模板",
            "description": "这是一个测试规则模板",
            "module_path": "rules.test_rule_001",
            "status": "NEW",
        }

        template = RuleTemplate.from_dict(template_data)

        # 验证基本属性
        assert template.rule_key == "test_rule_001"
        assert template.rule_type == "医保规则"
        assert template.name == "测试规则模板"
        assert template.status == RuleTemplateStatusEnum.NEW

        # 验证模板
        validation = template.validate_template()
        assert validation["valid"] is True
        assert len(validation["errors"]) == 0

        # 测试to_dict
        template_dict = template.to_dict()
        assert template_dict["rule_key"] == "test_rule_001"
        assert template_dict["status"] == "NEW"

    def test_rule_field_metadata_creation_and_validation(self):
        """测试规则字段元数据创建和验证"""
        # 创建字段元数据
        metadata_data = {
            "rule_key": "test_rule_001",
            "field_name": "age_threshold",
            "field_type": "integer",
            "is_required": True,
            "is_fixed_field": False,
            "display_name": "年龄阈值",
            "description": "患者年龄阈值限制",
            "validation_rule": '["required", "min:0", "max:150"]',
            "default_value": "18",
            "excel_column_order": 1,
        }

        metadata = RuleFieldMetadata.from_dict(metadata_data)

        # 验证基本属性
        assert metadata.rule_key == "test_rule_001"
        assert metadata.field_name == "age_threshold"
        assert metadata.field_type == FieldTypeEnum.integer
        assert metadata.is_required is True
        assert metadata.display_name == "年龄阈值"

        # 测试验证规则解析
        rules = metadata.get_validation_rules()
        assert "required" in rules
        assert "min:0" in rules
        assert "max:150" in rules

        # 测试字段值验证
        # 有效值
        result = metadata.validate_field_value(25)
        assert result["valid"] is True

        # 无效值（超出范围）
        result = metadata.validate_field_value(200)
        assert result["valid"] is False
        assert any("不能大于150" in error for error in result["errors"])

        # 空值（必填字段）
        result = metadata.validate_field_value(None)
        assert result["valid"] is False
        assert any("不能为空" in error for error in result["errors"])

        # 测试默认值解析
        default_value = metadata.get_default_value_parsed()
        assert default_value == 18
        assert isinstance(default_value, int)

    def test_rule_detail_creation_and_operations(self):
        """测试规则明细创建和操作"""
        # 创建规则明细
        detail_data = {
            "rule_id": "test_001",
            "rule_key": "test_rule_001",
            "rule_name": "测试规则明细",
            "level1": "一级错误",
            "level2": "二级错误",
            "level3": "三级错误",
            "error_reason": "年龄不符合要求",
            "degree": "轻微",
            "reference": "医保政策第123条",
            "detail_position": "门诊",
            "prompted_fields3": "age,gender",
            "prompted_fields1": "age",
            "type": "年龄限制",
            "pos": "门诊业务",
            "applicableArea": "全国",
            "default_use": "是",
            "remarks": "测试备注",
            "in_illustration": "否",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": "YB001",
            "status": "ACTIVE",
        }

        rule_detail = RuleDetail.from_dict(detail_data)

        # 验证基本属性
        assert rule_detail.rule_id == "test_001"
        assert rule_detail.rule_key == "test_rule_001"
        assert rule_detail.rule_name == "测试规则明细"
        assert rule_detail.status == RuleDetailStatusEnum.ACTIVE

        # 测试扩展字段操作
        rule_detail.set_extended_field("age_threshold", 18)
        rule_detail.set_extended_field("gender_limit", "male")

        assert rule_detail.get_extended_field("age_threshold") == 18
        assert rule_detail.get_extended_field("gender_limit") == "male"

        # 测试批量更新扩展字段
        rule_detail.update_extended_fields(
            {"age_threshold": 21, "new_field": "new_value", "complex_field": {"nested": "data"}}
        )

        assert rule_detail.get_extended_field("age_threshold") == 21
        assert rule_detail.get_extended_field("new_field") == "new_value"
        assert rule_detail.get_extended_field("complex_field") == {"nested": "data"}

        # 测试获取所有扩展字段
        all_extended = rule_detail.get_all_extended_fields()
        assert "age_threshold" in all_extended
        assert "gender_limit" in all_extended
        assert "new_field" in all_extended
        assert "complex_field" in all_extended

        # 测试API响应格式
        api_response = rule_detail.to_api_response()
        assert api_response["rule_id"] == "test_001"
        assert api_response["age_threshold"] == 21
        assert api_response["new_field"] == "new_value"

        # 测试数据合并
        update_data = {"rule_name": "更新后的规则名称", "degree": "严重", "custom_field": "自定义值"}

        rule_detail.merge_from_dict(update_data)

        assert rule_detail.rule_name == "更新后的规则名称"
        assert rule_detail.degree == "严重"
        assert rule_detail.get_extended_field("custom_field") == "自定义值"

    def test_rule_detail_validation(self):
        """测试规则明细数据验证"""
        # 创建一个不完整的规则明细
        incomplete_detail = RuleDetail(
            rule_id="test_002",
            rule_key="test_rule_001",
            # 缺少必填字段
        )

        # 验证应该失败
        validation = incomplete_detail.validate_data()
        assert validation["valid"] is False
        assert len(validation["errors"]) > 0

        # 验证错误信息包含缺失字段
        errors = validation["errors"]
        assert any("rule_name不能为空" in error for error in errors)
        assert any("level1不能为空" in error for error in errors)

    def test_field_metadata_with_different_types(self):
        """测试不同类型的字段元数据"""
        # 字符串类型
        string_metadata = RuleFieldMetadata(
            rule_key="test_rule",
            field_name="description",
            field_type=FieldTypeEnum.string,
            display_name="描述",
            validation_rule='["max_length:100"]',
        )

        result = string_metadata.validate_field_value("短描述")
        assert result["valid"] is True

        long_text = "这是一个非常长的描述" * 15  # 超过100个字符
        result = string_metadata.validate_field_value(long_text)
        assert result["valid"] is False

        # 布尔类型
        bool_metadata = RuleFieldMetadata(
            rule_key="test_rule",
            field_name="is_active",
            field_type=FieldTypeEnum.boolean,
            display_name="是否激活",
            default_value="true",
        )

        result = bool_metadata.validate_field_value(True)
        assert result["valid"] is True

        result = bool_metadata.validate_field_value("invalid_bool")
        assert result["valid"] is False

        default_value = bool_metadata.get_default_value_parsed()
        assert default_value is True

        # 数组类型
        array_metadata = RuleFieldMetadata(
            rule_key="test_rule",
            field_name="tags",
            field_type=FieldTypeEnum.array,
            display_name="标签",
            default_value='["tag1", "tag2"]',
        )

        result = array_metadata.validate_field_value(["tag1", "tag2"])
        assert result["valid"] is True

        default_value = array_metadata.get_default_value_parsed()
        assert default_value == ["tag1", "tag2"]

    def test_complete_workflow(self):
        """测试完整的工作流程"""
        # 1. 创建规则模板
        template = RuleTemplate.from_dict(  # noqa: F841
            {
                "rule_key": "workflow_test",
                "rule_type": "综合测试",
                "name": "工作流程测试模板",
                "description": "测试完整工作流程",
                "status": "NEW",
            }
        )

        # 2. 创建字段元数据
        field_metadata = RuleFieldMetadata.from_dict(
            {
                "rule_key": "workflow_test",
                "field_name": "patient_age",
                "field_type": "integer",
                "is_required": True,
                "display_name": "患者年龄",
                "validation_rule": '["required", "min:0", "max:120"]',
                "default_value": "0",
            }
        )

        # 3. 创建规则明细
        rule_detail = RuleDetail.from_dict(
            {
                "rule_id": "workflow_001",
                "rule_key": "workflow_test",
                "rule_name": "工作流程测试规则",
                "level1": "一级错误",
                "level2": "二级错误",
                "level3": "三级错误",
                "error_reason": "年龄验证失败",
                "degree": "严重",
                "reference": "测试参考",
                "detail_position": "测试位置",
                "prompted_fields1": "patient_age",
                "type": "年龄验证",
                "pos": "测试业务",
                "applicableArea": "全国",
                "default_use": "是",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "status": "ACTIVE",
            }
        )

        # 4. 添加扩展字段
        rule_detail.set_extended_field("patient_age", 25)

        # 5. 使用字段元数据验证规则明细
        validation = rule_detail.validate_data([field_metadata])
        assert validation["valid"] is True

        # 6. 测试无效数据
        rule_detail.set_extended_field("patient_age", 150)  # 超出范围
        validation = rule_detail.validate_data([field_metadata])
        assert validation["valid"] is False

        # 7. 修正数据
        rule_detail.set_extended_field("patient_age", 30)
        validation = rule_detail.validate_data([field_metadata])
        assert validation["valid"] is True

        # 8. 生成API响应
        api_response = rule_detail.to_api_response()
        assert api_response["rule_id"] == "workflow_001"
        assert api_response["patient_age"] == 30

        print("✅ 数据模型重构集成测试全部通过！")


if __name__ == "__main__":
    test = TestDataModelRefactoring()
    test.test_rule_template_creation_and_validation()
    test.test_rule_field_metadata_creation_and_validation()
    test.test_rule_detail_creation_and_operations()
    test.test_rule_detail_validation()
    test.test_field_metadata_with_different_types()
    test.test_complete_workflow()
    print("🎉 所有测试通过，数据模型重构成功！")
