<template>
  <div class="rule-details-crud">

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Document /></el-icon>
            规则明细管理
          </h1>
          <p class="page-subtitle">
            规则：{{ ruleName }} | 总计：<el-tag type="info" size="small">{{ pagination.total }}</el-tag> 条明细
          </p>
        </div>
        <div class="header-right">
          <el-button @click="goBackToManagement" :icon="ArrowLeft">
            返回规则配置
          </el-button>
          <el-button @click="goBackToDetail" :icon="View">
            查看规则详情
          </el-button>
          <el-button type="primary" @click="handleRefresh" :loading="loading" :icon="Refresh">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <RuleDetailsToolbar
      :rule-key="ruleKey"
      :selected-count="selectedCount"
      :has-selected="hasSelected"
      :selected-items="selectedDetails"
      @create="handleCreate"
      @batch-operation="handleBatchOperation"
      @export="handleExport"
      @import="handleImport"
      @clear-selection="clearSelection"
    />

    <!-- 搜索和过滤区域 -->
    <div class="search-filter-section">
      <el-card shadow="never" class="filter-card">
        <div class="filter-row">
          <div class="filter-item">
            <el-input
              v-model="searchKeyword"
              :placeholder="`搜索${getFieldChineseName('rule_name')}、${getFieldChineseName('level1')}...`"
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              style="width: 300px"
            />
          </div>
          <div class="filter-item">
            <el-select
              v-model="statusFilter"
              placeholder="状态筛选"
              clearable
              style="width: 150px"
              @change="handleFilter"
            >
              <el-option label="活跃" value="ACTIVE" />
              <el-option label="非活跃" value="INACTIVE" />
              <el-option label="已删除" value="DELETED" />
            </el-select>
          </div>
          <div class="filter-item">
            <el-select
              v-model="errorLevelFilter"
              placeholder="错误级别"
              clearable
              style="width: 150px"
              @change="handleFilter"
            >
              <el-option :label="getFieldChineseName('level1')" value="level1" />
              <el-option :label="getFieldChineseName('level2')" value="level2" />
              <el-option :label="getFieldChineseName('level3')" value="level3" />
            </el-select>
          </div>
          <div class="filter-item">
            <el-button @click="handleResetFilter" :icon="RefreshLeft">重置</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <RuleDetailsTable
      :rule-key="ruleKey"
      :loading="loading"
      :max-selection="maxSelection"
      :show-smart-selection="showSmartSelection"
      @detail:view="handleView"
      @detail:edit="handleEdit"
      @detail:delete="handleDelete"
      @selection:change="handleSelectionChange"
      @pagination:change="handlePaginationChange"
      @table:sort="handleSortChange"
    />

    <!-- 新增/编辑表单对话框 -->
    <RuleDetailForm
      v-model="formDialogVisible"
      :rule-key="ruleKey"
      :mode="formMode"
      :detail-data="currentDetailData"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
    />

    <!-- 详情查看对话框 -->
    <RuleDetailViewDialog
      v-model="viewDialogVisible"
      :detail-data="currentDetailData"
    />

    <!-- 批量操作对话框 -->
    <BatchOperationDialog
      v-model="batchDialogVisible"
      :rule-key="ruleKey"
      :selected-details="selectedDetails"
      :operation-type="batchOperationType"
      @success="handleBatchSuccess"
    />


  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  ArrowLeft,
  Refresh,
  Search,
  RefreshLeft,
  View
} from '@element-plus/icons-vue'

// 组件导入
import RuleDetailsToolbar from '../components/business/RuleDetailsToolbar.vue'
import RuleDetailsTable from '../components/business/RuleDetailsTable.vue'
import RuleDetailForm from '../components/business/RuleDetailForm.vue'
import RuleDetailViewDialog from '../components/business/RuleDetailViewDialog.vue'
import BatchOperationDialog from '../components/business/BatchOperationDialog.vue'


// Composables
import { useRuleDetailsManagement } from '../composables/business/useRuleDetailsManagement'
import { useAppStore } from '../stores/app'
import { storeToRefs } from 'pinia'

// 类型定义和字段映射
import { getFieldChineseName, getRuleTypeChineseName } from '../types/generated-fields'

// 路由和全局状态
const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const { globalLoading, hasError } = storeToRefs(appStore)

// 规则store
import { useRulesStore } from '../stores/rules'
const rulesStore = useRulesStore()

// 获取路由参数
const ruleKey = computed(() => route.params.ruleKey)

// 批量选择配置
const maxSelection = ref(100) // 最大选择数量限制
const showSmartSelection = ref(true) // 是否显示智能选择面板



// 使用规则明细管理功能
const {
  detailsList,
  selectedDetails,
  detailsCount,
  selectedCount,
  hasSelected,
  isLoading,
  pagination,
  loadDetailsList,
  refreshList,
  deleteDetail,
  performSearch,
  clearSearch,
  applyFilters,
  resetFilters,
  handleSelectionChange: clearSelection,
  handlePageChange,
  handlePageSizeChange,
  handleSort
} = useRuleDetailsManagement(ruleKey.value)

// 本地状态
const searchKeyword = ref('')
const statusFilter = ref('')
const errorLevelFilter = ref('')

// 对话框状态
const formDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const batchDialogVisible = ref(false)

// 表单相关状态
const formMode = ref('create') // 'create' | 'edit'
const currentDetailData = ref(null)
const batchOperationType = ref('')

// 计算属性
const loading = computed(() => isLoading.value || globalLoading.value)

// 获取当前规则信息
const currentRuleInfo = computed(() => {
  if (!ruleKey.value) return null
  return rulesStore.getRuleByKey(ruleKey.value) || rulesStore.currentRule
})

// 获取规则中文名称
const ruleName = computed(() => {
  return currentRuleInfo.value?.rule_name || currentRuleInfo.value?.name || ruleKey.value
})

// 方法定义
const goBack = () => {
  router.back()
}

const goBackToManagement = () => {
  router.push('/rules/management')
}

const goBackToDetail = () => {
  router.push(`/rule-template-detail/${ruleKey.value}`)
}

const handleRefresh = async () => {
  try {
    await refreshList()
    ElMessage.success('刷新成功')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败')
  }
}

const handleSearch = async () => {
  try {
    if (searchKeyword.value.trim()) {
      await performSearch(searchKeyword.value.trim())
    } else {
      await clearSearch()
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败')
  }
}

const handleFilter = async () => {
  try {
    const filterParams = {}
    if (statusFilter.value) {
      filterParams.status = statusFilter.value
    }
    if (errorLevelFilter.value) {
      filterParams.error_level = errorLevelFilter.value
    }

    await applyFilters(filterParams)
  } catch (error) {
    console.error('过滤失败:', error)
    ElMessage.error('过滤失败')
  }
}

const handleResetFilter = async () => {
  try {
    searchKeyword.value = ''
    statusFilter.value = ''
    errorLevelFilter.value = ''
    await resetFilters()
    await refreshList()
  } catch (error) {
    console.error('重置过滤失败:', error)
    ElMessage.error('重置过滤失败')
  }
}

// CRUD操作处理
const handleCreate = () => {
  formMode.value = 'create'
  currentDetailData.value = null
  formDialogVisible.value = true
}

const handleView = (detail) => {
  currentDetailData.value = detail
  viewDialogVisible.value = true
}

const handleEdit = (detail) => {
  formMode.value = 'edit'
  currentDetailData.value = detail
  formDialogVisible.value = true
}

const handleDelete = async (detail) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则明细 "${detail.rule_name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteDetail(detail.id)
    ElMessage.success('删除成功')
    await refreshList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 表单操作处理
const handleFormSuccess = async () => {
  formDialogVisible.value = false
  await refreshList()
  ElMessage.success(formMode.value === 'create' ? '创建成功' : '更新成功')
}

const handleFormCancel = () => {
  formDialogVisible.value = false
  currentDetailData.value = null
}

// 选择处理
const handleSelectionChange = (eventData) => {
  // 选择变更逻辑将通过store处理
  console.log('选择变更:', eventData)
}

// 分页处理
const handlePaginationChange = (eventData) => {
  if (eventData.type === 'page') {
    handlePageChange(eventData.page, eventData.pageSize)
  } else if (eventData.type === 'size') {
    handlePageSizeChange(eventData.pageSize)
  }
}

// 排序处理
const handleSortChange = (eventData) => {
  if (handleSort) {
    handleSort(eventData)
  } else {
    console.log('handleSort函数不存在')
  }
}

// 批量操作处理
const handleBatchOperation = (operationType) => {
  batchOperationType.value = operationType
  batchDialogVisible.value = true
}

const handleBatchSuccess = async (operationData) => {
  batchDialogVisible.value = false
  await refreshList()
  clearSelection()

  ElMessage.success('批量操作成功')
}

const getOperationName = (type) => {
  const names = {
    activate: '激活',
    deactivate: '停用',
    delete: '删除',
    copy: '复制',
    move: '移动'
  }
  return names[type] || type
}

// 导入导出处理
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

// 生命周期
onMounted(async () => {
  if (ruleKey.value) {
    // 确保获取规则信息
    if (!currentRuleInfo.value) {
      try {
        await rulesStore.fetchRuleDetail(ruleKey.value)
      } catch (error) {
        console.warn('获取规则详情失败:', error)
      }
    }
    await loadDetailsList()
  }
})

// 监听路由变化
watch(ruleKey, async (newRuleKey) => {
  if (newRuleKey) {
    await loadDetailsList()
  }
})
</script>

<style scoped>
.rule-details-crud {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
  width: 100% !important;
  max-width: 1352px !important;
  min-width: 1352px !important;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.search-filter-section {
  margin-bottom: 20px;
}

.filter-card {
  border: none;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
}
</style>
