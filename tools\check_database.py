#!/usr/bin/env python3
"""
数据库连接检查工具
用于验证数据库配置和连接状态
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from core.database_initializer import get_database_info, initialize_database


def main():
    """主函数"""
    print("=" * 60)
    print("数据库连接检查工具")
    print("=" * 60)

    # 1. 显示数据库配置信息
    print("\n1. 数据库配置信息:")
    print("-" * 40)
    db_info = get_database_info()
    for key, value in db_info.items():
        if key == "password":
            value = "***" if value else None
        print(f"  {key}: {value}")

    # 2. 验证配置
    print("\n2. 配置验证:")
    print("-" * 40)
    is_valid, error_msg = settings.validate_database_config()
    if is_valid:
        print("  ✅ 配置验证通过")
    else:
        print(f"  ❌ 配置验证失败: {error_msg}")
        return 1

    # 3. 测试数据库连接
    print("\n3. 数据库连接测试:")
    print("-" * 40)
    success, message = initialize_database()
    if success:
        print(f"  ✅ 数据库连接成功: {message}")
    else:
        print(f"  ❌ 数据库连接失败: {message}")
        return 1

    # 4. 显示建议
    print("\n4. 部署建议:")
    print("-" * 40)
    if settings.AUTO_CREATE_DATABASE:
        if settings.RUN_MODE == "PROD":
            print("  ⚠️  生产环境建议禁用AUTO_CREATE_DATABASE")
        else:
            print("  ✅ 开发/测试环境已启用自动建库")
    else:
        print("  ✅ 自动建库已禁用（推荐用于生产环境）")

    if db_info["config_method"] == "independent_params":
        print("  ✅ 使用独立参数配置方式（推荐）")
    else:
        print("  ⚠️  使用DATABASE_URL配置方式，如密码包含特殊字符建议改用独立参数")

    print("\n" + "=" * 60)
    print("数据库检查完成")
    print("=" * 60)

    return 0


if __name__ == "__main__":
    sys.exit(main())
