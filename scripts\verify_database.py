#!/usr/bin/env python3
"""
验证数据库表结构和外键约束
"""

import sys

import pymysql


def verify_database():
    """验证数据库表结构"""

    config = {
        'host': '***************',
        'port': 3306,
        'user': 'rule_user',
        'password': 'mysql_password',
        'database': 'rule_service',
        'charset': 'utf8mb4'
    }

    try:
        connection = pymysql.connect(**config)

        with connection.cursor() as cursor:
            print("🔍 验证数据库表结构...")

            # 1. 检查表是否存在
            cursor.execute("SHOW TABLES")
            tables = [row[0] for row in cursor.fetchall()]

            expected_tables = ['rule_template', 'rule_detail', 'rule_field_metadata']
            print(f"\n📋 数据库中的表: {tables}")

            for table in expected_tables:
                if table in tables:
                    print(f"✅ {table} 表存在")
                else:
                    print(f"❌ {table} 表不存在")

            # 2. 检查外键约束
            print("\n🔗 检查外键约束...")
            cursor.execute("""
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_SCHEMA = 'rule_service'
                    AND TABLE_NAME IN ('rule_detail', 'rule_field_metadata')
                ORDER BY TABLE_NAME, COLUMN_NAME
            """)

            foreign_keys = cursor.fetchall()
            if foreign_keys:
                print("外键约束:")
                for fk in foreign_keys:
                    table, column, constraint, ref_table, ref_column = fk
                    print(f"  ✅ {table}.{column} -> {ref_table}.{ref_column} ({constraint})")
            else:
                print("❌ 未找到外键约束")

            # 3. 检查索引
            print("\n📊 检查索引...")
            for table in expected_tables:
                cursor.execute(f"SHOW INDEX FROM {table}")
                indexes = cursor.fetchall()
                print(f"\n{table} 表的索引:")
                for idx in indexes:
                    index_name = idx[2]
                    column_name = idx[4]
                    print(f"  📌 {index_name}: {column_name}")

            # 4. 测试表关联查询
            print("\n🧪 测试表关联查询...")
            cursor.execute("""
                SELECT 
                    t.rule_key,
                    t.name,
                    COUNT(d.id) as detail_count,
                    COUNT(m.id) as metadata_count
                FROM rule_template t
                LEFT JOIN rule_detail d ON t.rule_key = d.rule_key
                LEFT JOIN rule_field_metadata m ON t.rule_key = m.rule_key
                GROUP BY t.rule_key, t.name
                LIMIT 5
            """)

            results = cursor.fetchall()
            if results:
                print("关联查询测试成功:")
                for row in results:
                    print(f"  📝 {row[0]}: {row[1]} (明细:{row[2]}, 元数据:{row[3]})")
            else:
                print("✅ 关联查询语法正确（无数据）")

            print("\n🎉 数据库表结构验证完成！")

    except Exception as e:
        print(f"❌ 验证失败: {e}")
        sys.exit(1)
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    verify_database()
