# 规则开发最佳实践指南

## 📋 概述

本指南为医保规则开发提供最佳实践，帮助开发者充分利用超快速校验引擎的性能优势。

## 🚀 现有规则兼容性

### ✅ 无需修改现有规则

所有 `rules/base_rules/` 目录下的现有规则**无需任何修改**即可获得性能提升：

- **自动规则筛选**: 基于患者特征智能过滤（如性别、年龄、诊断、药品）
- **数据预处理**: 自动使用优化的患者数据结构
- **智能缓存**: 自动缓存校验结果，避免重复计算

### 🔧 自动适配机制

现有规则通过以下机制自动适配：

```python
# BaseRule 中的默认适配器
def validate_ultra_fast(self, ultra_patient_data):
    # 自动将超优化数据转换为标准格式
    patient_data = self._create_patient_data_adapter(ultra_patient_data)
    return self.validate(patient_data)  # 调用现有的 validate 方法
```

## 🏆 新规则开发最佳实践

### 1. 性能优先的规则开发

为了获得最佳性能，新规则建议重写 `validate_ultra_fast()` 方法：

```python
class HighPerformanceDrugRule(BaseRule):
    """高性能药品规则示例"""
    
    def __init__(self, rule_id: str, yb_codes: list[str], **kwargs):
        super().__init__(rule_id, **kwargs)
        self.yb_codes = set(yb_codes)  # 转为set提高查找性能
    
    def validate_ultra_fast(self, ultra_patient_data: "UltraOptimizedPatientData") -> RuleResult | None:
        """
        超快速校验实现 - 推荐新规则使用此方式
        """
        # 1. 快速特征检查
        if not ultra_patient_data.get_feature_fast('has_fees'):
            return None
        
        # 2. 使用预计算的患者特征
        gender = ultra_patient_data.get_feature_fast('gender')
        if gender != 'M':  # 假设这是男性专用药品
            return None
        
        # 3. 超快速费用查找（O(1)复杂度）
        relevant_fees = ultra_patient_data.get_fees_by_codes_ultra_fast(list(self.yb_codes))
        if not relevant_fees:
            return None
        
        # 4. 使用预计算的统计数据
        total_amount, total_quantity, days = ultra_patient_data.calculate_totals_ultra_fast(list(self.yb_codes))
        
        # 5. 业务逻辑检查
        if total_amount > self.limit_amount:
            return self._create_violation_result(
                total_amount=total_amount,
                total_quantity=total_quantity,
                days=days,
                fee_ids=[f.id for f in relevant_fees]
            )
        
        return None
    
    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        传统校验实现 - 保持向后兼容
        """
        # 原有逻辑保持不变...
        pass
```

### 2. 关键性能优化技巧

#### 🎯 使用快速特征检查
```python
# ✅ 推荐：使用预计算的特征
gender = ultra_patient_data.get_feature_fast('gender')
age = ultra_patient_data.get_feature_fast('age')
has_fees = ultra_patient_data.get_feature_fast('has_fees')

# ❌ 避免：访问复杂对象属性
# gender = ultra_patient_data.basic_information.gender  # 较慢
```

#### ⚡ 使用超快速数据访问
```python
# ✅ 推荐：O(1)复杂度的费用查找
fees = ultra_patient_data.get_fees_by_codes_ultra_fast(['A01', 'A02'])

# ✅ 推荐：预计算的统计数据
total_amount, total_quantity, days = ultra_patient_data.calculate_totals_ultra_fast(codes)

# ❌ 避免：遍历所有费用
# for fee in all_fees:  # O(n)复杂度，较慢
#     if fee.ybdm in target_codes:
```

#### 🔍 使用超快速诊断检查
```python
# ✅ 推荐：集合查找
if ultra_patient_data.has_diagnosis_ultra_fast('I10'):
    return None

# ✅ 推荐：批量诊断检查
if ultra_patient_data.has_any_diagnosis_ultra_fast(['I10', 'I11', 'I12']):
    return None

# ❌ 避免：遍历诊断列表
# for diag in patient_data.Diagnosis.diagnosis:  # 较慢
```

### 3. 规则筛选优化配合

#### 🏷️ 规则属性标记

确保规则属性被正确设置，以支持智能筛选：

```python
class OptimizedRule(BaseRule):
    def __init__(self, rule_id: str, **kwargs):
        super().__init__(rule_id, **kwargs)
        
        # 这些属性会被筛选器自动识别
        self.yb_codes = kwargs.get('yb_code', [])           # 药品代码
        self.diagnosis_codes = kwargs.get('diag_codes', [])  # 诊断代码
        
        # 性别和年龄限制通过规则类名识别
        # 如: MaleOnlyDrugRule, PediatricDrugRule
```

#### 📊 规则复杂度分类

根据规则复杂度选择合适的实现策略：

```python
# 轻量级规则（<1ms）- 简单条件检查
class LightweightRule(BaseRule):
    def validate_ultra_fast(self, ultra_patient_data):
        # 简单的性别、年龄检查
        return self._simple_check(ultra_patient_data)

# 中等复杂度规则（1-10ms）- 涉及费用计算
class MediumComplexityRule(BaseRule):
    def validate_ultra_fast(self, ultra_patient_data):
        # 费用统计和基本业务逻辑
        return self._fee_calculation(ultra_patient_data)

# 重量级规则（>10ms）- 复杂算法和大量数据处理
class HeavyweightRule(BaseRule):
    def validate_ultra_fast(self, ultra_patient_data):
        # 复杂的多条件判断和计算
        return self._complex_analysis(ultra_patient_data)
```

### 4. 内存优化建议

#### 💾 避免大对象创建
```python
# ✅ 推荐：复用对象和预计算
class MemoryOptimizedRule(BaseRule):
    def __init__(self, rule_id: str, **kwargs):
        super().__init__(rule_id, **kwargs)
        # 预计算常用数据
        self.yb_codes_set = set(kwargs.get('yb_code', []))
        self.limit_cache = {}
    
    def validate_ultra_fast(self, ultra_patient_data):
        # 使用预创建的对象
        relevant_codes = self.yb_codes_set.intersection(
            ultra_patient_data.fee_index.get_unique_ybdm()
        )
        # ...

# ❌ 避免：在校验中创建大量临时对象
# def validate(self, patient_data):
#     temp_list = []  # 避免创建大量临时列表
#     for fee in patient_data.fees:  # 避免大量循环
```

#### 🗂️ 智能数据结构选择
```python
# ✅ 推荐：使用高效的数据结构
self.target_codes = set(codes)      # 查找: O(1)
self.code_mapping = dict(mapping)   # 映射: O(1)
self.sorted_limits = sorted(limits) # 二分查找: O(log n)

# ❌ 避免：使用低效的数据结构
# self.target_codes = list(codes)   # 查找: O(n)
```

## 🧪 测试最佳实践

### 1. 性能测试
```python
import time
from services.patient_data_preprocessor import patient_preprocessor

def test_rule_performance():
    """测试规则性能"""
    # 创建测试数据
    patient_data = create_test_patient_data()
    ultra_patient, _ = patient_preprocessor.preprocess_patient_data(patient_data)
    
    rule = YourOptimizedRule(rule_id="test", ...)
    
    # 测试传统方式
    start = time.time()
    result1 = rule.validate(patient_data)
    traditional_time = (time.time() - start) * 1000
    
    # 测试超快速方式
    start = time.time()
    result2 = rule.validate_ultra_fast(ultra_patient)
    ultra_fast_time = (time.time() - start) * 1000
    
    print(f"传统方式: {traditional_time:.2f}ms")
    print(f"超快速方式: {ultra_fast_time:.2f}ms")
    print(f"性能提升: {traditional_time / ultra_fast_time:.1f}x")
    
    # 验证结果一致性
    assert result1 == result2 or (result1 is None and result2 is None)
```

### 2. 筛选效果测试
```python
def test_rule_filtering():
    """测试规则筛选效果"""
    from services.rule_filtering_service import rule_filter
    
    # 创建不同特征的患者数据
    male_patient = create_male_patient()
    female_patient = create_female_patient()
    
    # 测试性别特定规则筛选
    male_rules, stats = rule_filter.filter_applicable_rules(male_patient, all_rule_ids)
    print(f"男性患者适用规则: {len(male_rules)}/{len(all_rule_ids)}")
    
    female_rules, stats = rule_filter.filter_applicable_rules(female_patient, all_rule_ids)
    print(f"女性患者适用规则: {len(female_rules)}/{len(all_rule_ids)}")
```

## 📊 规则开发检查清单

### ✅ 性能优化检查
- [ ] 实现了 `validate_ultra_fast()` 方法
- [ ] 使用了超快速数据访问接口
- [ ] 避免了不必要的数据转换
- [ ] 使用了高效的数据结构（set, dict）
- [ ] 预计算了常用数据

### ✅ 筛选兼容性检查
- [ ] 正确设置了 `yb_codes` 属性
- [ ] 正确设置了 `diagnosis_codes` 属性  
- [ ] 规则类名包含了特征关键词（如 Male, Female, Pediatric）
- [ ] 属性使用了标准字段名

### ✅ 内存优化检查
- [ ] 避免创建大量临时对象
- [ ] 复用预计算的数据结构
- [ ] 使用 `__slots__` 减少内存占用（如适用）
- [ ] 及时释放不需要的引用

### ✅ 兼容性检查
- [ ] 保留了原有的 `validate()` 方法
- [ ] 两种方法返回结果一致
- [ ] 错误处理兼容现有系统
- [ ] 支持降级到传统模式

## 🔧 调试和故障排除

### 1. 性能问题诊断
```python
# 使用性能监控API
import requests

# 获取规则性能统计
response = requests.get("http://localhost:18001/api/v1/performance/validation-stats")
stats = response.json()

print(f"平均校验时间: {stats['data']['avg_time_per_validation_ms']:.2f}ms")
print(f"规则筛选效果: {stats['data']['avg_rules_filtered_per_validation']:.0f}条")
```

### 2. 内存问题诊断
```python
# 检查内存使用
response = requests.get("http://localhost:18001/api/v1/performance/memory-stats")
memory_stats = response.json()

if memory_stats['data']['has_memory_pressure']:
    print(f"内存压力: {memory_stats['data']['pressure_level']}")
    print(f"当前使用: {memory_stats['data']['current_memory_mb']:.1f}MB")
    
    # 手动触发内存优化
    requests.post("http://localhost:18001/api/v1/performance/optimize-memory")
```

### 3. 规则筛选问题诊断
```python
# 检查规则筛选统计
response = requests.get("http://localhost:18001/api/v1/performance/rule-filter-stats")
filter_stats = response.json()

print("筛选索引大小:")
for index_type, size in filter_stats['data']['index_sizes'].items():
    print(f"  {index_type}: {size}条规则")
```

## 📚 示例：完整的高性能规则

```python
from typing import TYPE_CHECKING
from models import PatientData, RuleOutput, RuleResult
from rules.base_rules.base import BaseRule

if TYPE_CHECKING:
    from services.patient_data_preprocessor import UltraOptimizedPatientData

class HighPerformanceDrugLimitRule(BaseRule):
    """
    高性能药品限制规则示例
    展示所有最佳实践的完整实现
    """
    
    def __init__(self, rule_id: str, yb_codes: list[str], limit_amount: float, **kwargs):
        super().__init__(rule_id, **kwargs)
        
        # 性能优化：预处理数据
        self.yb_codes = set(yb_codes)  # set查找O(1)
        self.limit_amount = limit_amount
        
        # 支持规则筛选
        self.yb_code = yb_codes  # 标准属性名
        
    def validate_ultra_fast(self, ultra_patient_data: "UltraOptimizedPatientData") -> RuleResult | None:
        """超快速校验实现"""
        # 1. 快速预检查
        if not ultra_patient_data.get_feature_fast('has_fees'):
            return None
        
        # 2. 使用超快速费用查找
        relevant_fees = ultra_patient_data.get_fees_by_codes_ultra_fast(list(self.yb_codes))
        if not relevant_fees:
            return None
        
        # 3. 使用预计算统计
        total_amount, total_quantity, days = ultra_patient_data.calculate_totals_ultra_fast(list(self.yb_codes))
        
        # 4. 业务逻辑
        if total_amount <= self.limit_amount:
            return None
        
        # 5. 构建违规结果
        return RuleResult(
            id=self.rule_id,
            output=RuleOutput(
                error_fee=total_amount,
                used_count=total_quantity,
                used_day=days,
                illegal_count=total_quantity,
                illegal_day=days,
                illegal_item=",".join(f.id for f in relevant_fees if f.id),
                # ... 其他字段
            )
        )
    
    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """传统校验实现 - 向后兼容"""
        # 保持原有逻辑不变
        if not patient_data.fees:
            return None
        
        total_amount = 0
        relevant_fees = []
        
        for fee in patient_data.fees:
            if fee.ybdm in self.yb_codes:
                total_amount += fee.je
                relevant_fees.append(fee)
        
        if total_amount <= self.limit_amount:
            return None
        
        # 构建相同的结果...
        return RuleResult(...)
```

## 🎯 总结

1. **现有规则**: 无需修改，自动获得性能提升
2. **新规则**: 建议实现 `validate_ultra_fast()` 获得最佳性能  
3. **关键优化**: 使用超快速数据接口，避免遍历和临时对象
4. **兼容性**: 始终保持向后兼容，支持降级
5. **监控**: 使用性能API持续监控和优化

通过遵循这些最佳实践，您的规则可以充分利用超快速校验引擎的性能优势，实现极致的校验速度！