"""
原子性热重载机制测试
验证IndexHotReloader的原子性操作、超时控制、备份恢复等功能
"""

import asyncio
import os
import tempfile
import time
import unittest
from unittest.mock import Mock, patch, MagicMock

from core.slave_node_index_builder import SlaveNode<PERSON>ndex<PERSON><PERSON>er, IndexHotReloader, IndexBackup


class TestAtomicHotReload(unittest.TestCase):
    """原子性热重载测试类"""

    def setUp(self):
        """设置测试环境"""
        # 不再需要模拟环境变量，使用settings配置

        # 创建临时缓存文件
        self.temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".json.gz", delete=False)
        self.temp_file.close()

        # 模拟rule_index_manager
        self.mock_rule_index_manager = Mock()
        self.mock_rule_index_manager.yb_code_index = {"Y001": ["rule1"], "Y002": ["rule2"]}
        self.mock_rule_index_manager.diag_code_index = {"D001": ["rule1"]}
        self.mock_rule_index_manager.fee_code_index = {"F001": ["rule1"]}

        # 确保没有get_index_state和restore_index_state方法，使用默认逻辑
        if hasattr(self.mock_rule_index_manager, "get_index_state"):
            del self.mock_rule_index_manager.get_index_state
        if hasattr(self.mock_rule_index_manager, "restore_index_state"):
            del self.mock_rule_index_manager.restore_index_state

    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_index_backup_creation(self, mock_memory_optimizer_class):
        """测试索引备份创建"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 测试备份创建
        backup = reloader._create_index_backup()

        self.assertIsInstance(backup, IndexBackup)
        self.assertTrue(backup.is_valid())
        self.assertGreater(backup.backup_time, 0)
        self.assertIsInstance(backup.index_state, dict)
        self.assertGreater(len(backup.backup_id), 0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_index_backup_restore(self, mock_memory_optimizer_class):
        """测试索引备份恢复"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 创建备份
        backup = reloader._create_index_backup()

        # 修改索引状态
        self.mock_rule_index_manager.yb_code_index = {"Y999": ["new_rule"]}

        # 恢复备份
        success = reloader._restore_from_backup(backup)

        self.assertTrue(success)
        # 验证索引状态已恢复
        self.assertIn("Y001", self.mock_rule_index_manager.yb_code_index)
        self.assertIn("Y002", self.mock_rule_index_manager.yb_code_index)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_invalid_backup_handling(self, mock_memory_optimizer_class):
        """测试无效备份处理"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 测试无效备份
        invalid_backup = IndexBackup(
            backup_time=0,  # 无效时间
            index_state={},
            rule_count=-1,  # 无效数量
            backup_id="",  # 无效ID
        )

        self.assertFalse(invalid_backup.is_valid())

        # 尝试恢复无效备份
        success = reloader._restore_from_backup(invalid_backup)
        self.assertFalse(success)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_reload_statistics(self, mock_memory_optimizer_class):
        """测试重载统计功能"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 获取初始统计
        stats = reloader.get_reload_stats()
        self.assertEqual(stats["total_reloads"], 0)
        self.assertEqual(stats["successful_reloads"], 0)
        self.assertEqual(stats["failed_reloads"], 0)
        self.assertEqual(stats["timeout_reloads"], 0)
        self.assertEqual(stats["success_rate"], 0.0)

        # 重置统计
        reloader.reset_stats()
        stats = reloader.get_reload_stats()
        self.assertEqual(stats["total_reloads"], 0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    @patch("core.slave_node_index_builder.gzip")
    @patch("core.slave_node_index_builder.json")
    def test_successful_atomic_reload(self, mock_json, mock_gzip, mock_memory_optimizer_class):
        """测试成功的原子性重载"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        # 模拟内存状态
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        # 模拟文件内容
        mock_cache_data = {
            "rule_details": [
                {"rule_id": "test_1", "yb_code": "Y001"},
                {"rule_id": "test_2", "yb_code": "Y002"},
            ]
        }
        mock_json.load.return_value = mock_cache_data

        # 模拟文件操作
        mock_file = Mock()
        mock_gzip.open.return_value.__enter__.return_value = mock_file

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 执行原子性重载
        with patch("os.path.exists", return_value=True):
            success = reloader.reload_index()

        # 验证重载成功
        self.assertTrue(success)

        # 验证统计信息
        stats = reloader.get_reload_stats()
        self.assertEqual(stats["total_reloads"], 1)
        self.assertEqual(stats["successful_reloads"], 1)
        self.assertEqual(stats["failed_reloads"], 0)
        self.assertEqual(stats["success_rate"], 100.0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_reload_timeout_handling(self, mock_memory_optimizer_class):
        """测试重载超时处理"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 设置很短的超时时间
        reloader._reload_timeout = 0.1

        # 模拟慢速重载
        def slow_build():
            time.sleep(0.2)  # 超过超时时间
            return True

        builder.build_index_from_cache_file = slow_build

        # 执行重载，应该超时
        success = reloader.reload_index()

        # 验证超时处理
        self.assertFalse(success)

        # 验证统计信息
        stats = reloader.get_reload_stats()
        self.assertEqual(stats["timeout_reloads"], 1)
        self.assertEqual(stats["failed_reloads"], 1)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_concurrent_reload_protection(self, mock_memory_optimizer_class):
        """测试并发重载保护"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 模拟正在进行的重载
        reloader._reload_in_progress = True

        # 尝试并发重载
        success = reloader.reload_index()

        # 验证并发保护
        self.assertFalse(success)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_reload_failure_recovery(self, mock_memory_optimizer_class):
        """测试重载失败后的恢复"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 记录原始索引状态
        original_yb_index = self.mock_rule_index_manager.yb_code_index.copy()

        # 模拟重载失败
        builder.build_index_from_cache_file = Mock(return_value=False)

        # 执行重载
        success = reloader.reload_index()

        # 验证重载失败
        self.assertFalse(success)

        # 验证索引状态已恢复（通过备份恢复机制）
        # 注意：这里的验证取决于具体的恢复实现
        stats = reloader.get_reload_stats()
        self.assertEqual(stats["failed_reloads"], 1)


if __name__ == "__main__":
    unittest.main()
