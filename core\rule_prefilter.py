"""
规则预过滤器模块
实现智能规则过滤，显著提升校验性能

主要功能：
1. 集成规则索引管理器和患者数据分析器
2. 提供完整的规则预过滤流程
3. 支持降级机制和异常处理
4. 提供详细的过滤效果统计

技术特点：
- 过滤时间小于5毫秒
- 过滤率达到60-80%
- 支持配置化的过滤策略
- 完善的监控和降级机制
"""

import time
from typing import Any

from config.settings import settings
from core.logging.logging_system import log as logger
from core.patient_data_analyzer import patient_data_analyzer
from core.rule_index_manager import FilterResult, rule_index_manager
from models.patient import PatientData


class RulePreFilter:
    """
    规则预过滤器

    核心组件，负责协调索引管理器和患者数据分析器
    实现完整的规则预过滤流程
    """

    def __init__(self):
        # 性能统计
        self._filter_count = 0
        self._total_filter_time = 0.0
        self._fallback_count = 0
        self._timeout_count = 0

        # 过滤效果统计
        self._total_original_rules = 0
        self._total_filtered_rules = 0

    def filter_rules_for_patient(self, patient_data: PatientData, requested_rule_ids: list[str]) -> FilterResult:
        """
        为患者过滤相关规则

        Args:
            patient_data: 患者数据对象
            requested_rule_ids: 请求的规则ID列表

        Returns:
            FilterResult: 过滤结果
        """
        start_time = time.perf_counter()

        try:
            # 检查是否启用过滤
            if not self._is_filter_enabled():
                logger.debug("规则预过滤未启用，返回原始规则列表")
                result = self._create_no_filter_result(requested_rule_ids, start_time, "预过滤功能未启用")
                self._update_statistics(result)
                return result

            # 检查索引是否就绪
            if not rule_index_manager.is_ready():
                logger.warning(
                    "规则索引未就绪，触发降级机制",
                    extra={"fallback_reason": "index_not_ready", "requested_rules_count": len(requested_rule_ids)},
                )
                self._fallback_count += 1
                result = self._create_no_filter_result(requested_rule_ids, start_time, "规则索引未就绪")
                self._update_statistics(result)
                return result

            # 提取患者代码
            patient_codes = patient_data_analyzer.extract_codes(patient_data)

            # 检查代码提取是否成功
            if not self._has_valid_codes(patient_codes):
                logger.debug(
                    "患者无有效代码，保留所有规则",
                    extra={
                        "yb_codes_count": len(patient_codes.yb_codes),
                        "diag_codes_count": len(patient_codes.diag_codes),
                        "surgery_codes_count": len(patient_codes.surgery_codes),
                        "extraction_time": patient_codes.extraction_time,
                    },
                )
                result = self._create_no_filter_result(requested_rule_ids, start_time, "患者无有效代码")
                self._update_statistics(result)
                return result

            # 执行规则过滤
            logger.debug(
                "开始执行规则过滤",
                extra={
                    "patient_codes_summary": {
                        "yb_codes": len(patient_codes.yb_codes),
                        "diag_codes": len(patient_codes.diag_codes),
                        "surgery_codes": len(patient_codes.surgery_codes),
                    },
                    "requested_rules_count": len(requested_rule_ids),
                },
            )

            filter_result = rule_index_manager.filter_rules(patient_codes, requested_rule_ids)

            # 检查过滤超时
            if filter_result.filter_time > self._get_filter_timeout():
                logger.warning(
                    f"过滤超时 ({filter_result.filter_time:.2f}ms > {self._get_filter_timeout():.1f}ms)，启用降级",
                    extra={
                        "filter_time": filter_result.filter_time,
                        "timeout_threshold": self._get_filter_timeout(),
                        "fallback_reason": "filter_timeout",
                    },
                )
                self._timeout_count += 1
                result = self._create_no_filter_result(requested_rule_ids, start_time, "过滤操作超时")
                self._update_statistics(result)
                return result

            # 更新统计信息
            self._update_statistics(filter_result)

            logger.info(
                f"规则过滤完成 - 原始: {filter_result.original_rule_count}个, "
                f"过滤后: {filter_result.filtered_rule_count}个, "
                f"过滤率: {filter_result.filter_rate * 100:.1f}%, "
                f"耗时: {filter_result.filter_time:.2f}ms",
                extra={
                    "filter_stats": {
                        "original_count": filter_result.original_rule_count,
                        "filtered_count": filter_result.filtered_rule_count,
                        "filter_rate": filter_result.filter_rate,
                        "filter_time_ms": filter_result.filter_time,
                    }
                },
            )

            return filter_result

        except Exception as e:
            logger.error(
                f"规则过滤发生未预期异常: {e}",
                exc_info=True,
                extra={
                    "fallback_reason": "unexpected_exception",
                    "requested_rules_count": len(requested_rule_ids),
                    "exception_type": type(e).__name__,
                },
            )
            self._fallback_count += 1
            result = self._create_no_filter_result(requested_rule_ids, start_time, f"异常: {str(e)}")
            self._update_statistics(result)
            return result

    def _is_filter_enabled(self) -> bool:
        """检查是否启用规则过滤"""
        return getattr(settings, "ENABLE_RULE_PREFILTER", False)

    def _get_filter_timeout(self) -> float:
        """获取过滤超时时间（毫秒）"""
        return getattr(settings, "PREFILTER_TIMEOUT_MS", 10.0)

    def _has_valid_codes(self, patient_codes) -> bool:
        """检查患者代码是否有效"""
        return len(patient_codes.yb_codes) > 0 or len(patient_codes.diag_codes) > 0 or len(patient_codes.surgery_codes) > 0

    def _create_no_filter_result(
        self, requested_rule_ids: list[str], start_time: float, reason: str = "未知原因"
    ) -> FilterResult:
        """创建未过滤的结果（降级情况）"""
        filter_time = (time.perf_counter() - start_time) * 1000

        logger.debug(f"创建降级结果 - 原因: {reason}, 规则数量: {len(requested_rule_ids)}, 耗时: {filter_time:.2f}ms")

        return FilterResult(
            original_rule_count=len(requested_rule_ids),
            filtered_rule_count=len(requested_rule_ids),
            filter_rate=0.0,
            filter_time=filter_time,
            filtered_rule_ids=requested_rule_ids,
        )

    def _update_statistics(self, filter_result: FilterResult) -> None:
        """更新统计信息"""
        self._filter_count += 1
        self._total_filter_time += filter_result.filter_time
        self._total_original_rules += filter_result.original_rule_count
        self._total_filtered_rules += filter_result.filtered_rule_count

    def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计信息"""
        avg_filter_time = self._total_filter_time / self._filter_count if self._filter_count > 0 else 0.0

        overall_filter_rate = (
            1 - (self._total_filtered_rules / self._total_original_rules) if self._total_original_rules > 0 else 0.0
        )

        fallback_rate = self._fallback_count / self._filter_count if self._filter_count > 0 else 0.0

        timeout_rate = self._timeout_count / self._filter_count if self._filter_count > 0 else 0.0

        return {
            # 基本统计
            "filter_count": self._filter_count,
            "total_filter_time_ms": round(self._total_filter_time, 2),
            "avg_filter_time_ms": round(avg_filter_time, 2),
            # 过滤效果
            "total_original_rules": self._total_original_rules,
            "total_filtered_rules": self._total_filtered_rules,
            "overall_filter_rate": round(overall_filter_rate, 3),
            # 异常统计
            "fallback_count": self._fallback_count,
            "timeout_count": self._timeout_count,
            "fallback_rate": round(fallback_rate, 3),
            "timeout_rate": round(timeout_rate, 3),
            # 配置信息
            "filter_enabled": self._is_filter_enabled(),
            "filter_timeout_ms": self._get_filter_timeout(),
            # 子组件统计
            "index_manager_stats": rule_index_manager.get_performance_stats(),
            "patient_analyzer_stats": patient_data_analyzer.get_performance_stats(),
        }

    def reset_stats(self) -> None:
        """重置统计信息"""
        self._filter_count = 0
        self._total_filter_time = 0.0
        self._fallback_count = 0
        self._timeout_count = 0
        self._total_original_rules = 0
        self._total_filtered_rules = 0

        # 重置子组件统计
        patient_data_analyzer.reset_stats()

    def is_healthy(self) -> bool:
        """检查预过滤器健康状态"""
        if not self._is_filter_enabled():
            return True  # 未启用时认为是健康的

        # 检查降级率是否过高
        fallback_threshold = getattr(settings, "PREFILTER_FALLBACK_THRESHOLD", 0.1)
        if self._filter_count > 10:  # 至少有10次过滤记录
            fallback_rate = self._fallback_count / self._filter_count
            if fallback_rate > fallback_threshold:
                return False

        # 检查索引管理器状态
        if not rule_index_manager.is_ready():
            return False

        return True

    def get_health_report(self) -> dict[str, Any]:
        """获取健康状态报告"""
        stats = self.get_performance_stats()

        return {
            "is_healthy": self.is_healthy(),
            "filter_enabled": self._is_filter_enabled(),
            "index_ready": rule_index_manager.is_ready(),
            "fallback_rate": stats["fallback_rate"],
            "timeout_rate": stats["timeout_rate"],
            "avg_filter_time_ms": stats["avg_filter_time_ms"],
            "overall_filter_rate": stats["overall_filter_rate"],
            "recommendations": self._get_health_recommendations(),
        }

    def _get_health_recommendations(self) -> list[str]:
        """获取健康状态建议"""
        recommendations = []

        if not self._is_filter_enabled():
            recommendations.append("规则预过滤未启用，建议启用以提升30-50%性能")

        if not rule_index_manager.is_ready():
            recommendations.append("规则索引未就绪，请检查索引构建状态或重启索引服务")

        if self._filter_count > 10:
            fallback_rate = self._fallback_count / self._filter_count
            if fallback_rate > 0.1:
                recommendations.append(
                    f"降级率过高({fallback_rate * 100:.1f}%)，建议检查索引状态、内存使用情况或患者数据质量"
                )

            timeout_rate = self._timeout_count / self._filter_count
            if timeout_rate > 0.05:
                timeout_threshold = self._get_filter_timeout()
                recommendations.append(
                    f"超时率过高({timeout_rate * 100:.1f}%)，建议增加超时阈值(当前{timeout_threshold:.1f}ms)或优化索引性能"
                )

        if self._filter_count > 5:
            avg_filter_time = self._total_filter_time / self._filter_count
            if avg_filter_time > 5.0:
                recommendations.append(f"平均过滤时间较长({avg_filter_time:.1f}ms)，建议检查索引效率或系统负载")

        if self._total_original_rules > 0:
            filter_rate = 1 - (self._total_filtered_rules / self._total_original_rules)
            if filter_rate < 0.3:
                recommendations.append(f"过滤率较低({filter_rate * 100:.1f}%)，建议检查索引完整性或患者数据匹配度")
            elif filter_rate > 0.9:
                recommendations.append(f"过滤率很高({filter_rate * 100:.1f}%)，可能存在过度过滤，建议检查过滤逻辑")

        if len(recommendations) == 0:
            recommendations.append("系统运行状态良好，过滤效果符合预期")

        return recommendations


# 全局实例
rule_prefilter = RulePreFilter()
