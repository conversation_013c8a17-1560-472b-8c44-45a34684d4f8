"""
服务降级管理器
实现规则注册服务的降级策略和容错机制，包括服务健康检查、熔断器机制、备用处理方案和监控告警
"""

import asyncio
import json
import time
from collections import deque
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any

from core.logging.logging_system import log as logger


class ServiceStatus(Enum):
    """服务状态枚举"""

    HEALTHY = "healthy"  # 健康
    DEGRADED = "degraded"  # 降级
    FAILED = "failed"  # 失败


class CircuitBreakerState(Enum):
    """熔断器状态枚举"""

    CLOSED = "closed"  # 关闭（正常）
    OPEN = "open"  # 开启（熔断）
    HALF_OPEN = "half_open"  # 半开（试探）


@dataclass
class HealthCheckResult:
    """健康检查结果"""

    is_healthy: bool
    response_time: float
    error_message: str = ""
    timestamp: float = field(default_factory=time.time)


@dataclass
class CircuitBreakerMetrics:
    """熔断器指标"""

    total_requests: int = 0
    failed_requests: int = 0
    success_requests: int = 0
    last_failure_time: float = 0
    last_success_time: float = 0

    @property
    def failure_rate(self) -> float:
        """计算失败率"""
        if self.total_requests == 0:
            return 0.0
        return self.failed_requests / self.total_requests

    @property
    def success_rate(self) -> float:
        """计算成功率"""
        return 1.0 - self.failure_rate


@dataclass
class DegradationConfig:
    """降级配置"""

    # 健康检查配置
    health_check_interval: float = 30.0  # 健康检查间隔（秒）
    health_check_timeout: float = 5.0  # 健康检查超时（秒）

    # 熔断器配置
    failure_threshold: float = 0.5  # 失败率阈值
    min_requests: int = 10  # 最小请求数
    timeout_duration: float = 60.0  # 熔断超时时间（秒）
    half_open_max_calls: int = 3  # 半开状态最大调用次数

    # 缓存配置
    cache_max_size: int = 10000  # 缓存最大条目数
    cache_ttl: float = 3600.0  # 缓存TTL（秒）

    # 同步配置
    sync_batch_size: int = 100  # 同步批次大小
    sync_interval: float = 300.0  # 同步间隔（秒）


class ServiceDegradationManager:
    """
    服务降级管理器

    实现服务健康检查、熔断器机制、数据缓存和自动恢复功能。
    当外部服务不可用时，自动切换到降级模式，确保业务连续性。
    """

    def __init__(self, config: DegradationConfig | None = None):
        """
        初始化服务降级管理器

        Args:
            config: 降级配置，None表示使用默认配置
        """
        self.config = config or DegradationConfig()

        # 服务状态
        self.service_status = ServiceStatus.HEALTHY
        self.last_status_change = time.perf_counter()

        # 熔断器状态
        self.circuit_breaker_state = CircuitBreakerState.CLOSED
        self.circuit_breaker_metrics = CircuitBreakerMetrics()
        self.circuit_breaker_open_time = 0

        # 健康检查历史
        self.health_check_history = deque(maxlen=100)

        # 数据缓存
        self.cached_data = {}
        self.cache_timestamps = {}

        # 同步队列
        self.sync_queue = deque()

        # 运行状态
        self.is_running = False
        self.background_tasks = set()

        # 缓存文件路径
        self.cache_file_path = Path("data/degradation_cache.json")
        self.cache_file_path.parent.mkdir(parents=True, exist_ok=True)

        logger.info(
            f"ServiceDegradationManager initialized: "
            f"failure_threshold={self.config.failure_threshold}, "
            f"health_check_interval={self.config.health_check_interval}s"
        )

    async def start(self):
        """启动服务降级管理器"""
        if self.is_running:
            logger.warning("ServiceDegradationManager is already running")
            return

        self.is_running = True

        # 加载缓存数据
        await self._load_cache_from_file()

        # 启动后台任务
        health_check_task = asyncio.create_task(self._health_check_loop())
        sync_task = asyncio.create_task(self._sync_loop())

        self.background_tasks.add(health_check_task)
        self.background_tasks.add(sync_task)

        # 设置任务完成回调
        health_check_task.add_done_callback(self.background_tasks.discard)
        sync_task.add_done_callback(self.background_tasks.discard)

        logger.info("ServiceDegradationManager started")

    async def stop(self):
        """停止服务降级管理器"""
        if not self.is_running:
            return

        self.is_running = False

        # 取消所有后台任务
        for task in self.background_tasks:
            task.cancel()

        # 等待任务完成
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)

        # 保存缓存数据
        await self._save_cache_to_file()

        logger.info("ServiceDegradationManager stopped")

    def is_service_available(self) -> bool:
        """
        检查服务是否可用

        Returns:
            服务是否可用
        """
        return self.service_status == ServiceStatus.HEALTHY and self.circuit_breaker_state != CircuitBreakerState.OPEN

    def should_degrade(self) -> bool:
        """
        检查是否应该降级

        Returns:
            是否应该降级
        """
        return not self.is_service_available()

    async def record_request_result(self, success: bool, response_time: float = 0.0):
        """
        记录请求结果

        Args:
            success: 请求是否成功
            response_time: 响应时间
        """
        current_time = time.perf_counter()

        # 更新熔断器指标
        self.circuit_breaker_metrics.total_requests += 1

        if success:
            self.circuit_breaker_metrics.success_requests += 1
            self.circuit_breaker_metrics.last_success_time = current_time
        else:
            self.circuit_breaker_metrics.failed_requests += 1
            self.circuit_breaker_metrics.last_failure_time = current_time

        # 更新熔断器状态
        await self._update_circuit_breaker_state()

        # 更新服务状态
        await self._update_service_status()

        logger.debug(
            f"Request result recorded: success={success}, "
            f"response_time={response_time:.3f}s, "
            f"failure_rate={self.circuit_breaker_metrics.failure_rate:.3f}"
        )

    async def cache_data(self, key: str, data: Any) -> bool:
        """
        缓存数据

        Args:
            key: 缓存键
            data: 缓存数据

        Returns:
            是否缓存成功
        """
        try:
            # 检查缓存大小限制
            if len(self.cached_data) >= self.config.cache_max_size:
                # 清理过期缓存
                await self._cleanup_expired_cache()

                # 如果仍然超过限制，删除最旧的缓存
                if len(self.cached_data) >= self.config.cache_max_size:
                    oldest_key = min(self.cache_timestamps.keys(), key=lambda k: self.cache_timestamps[k])
                    del self.cached_data[oldest_key]
                    del self.cache_timestamps[oldest_key]

            # 缓存数据
            self.cached_data[key] = data
            self.cache_timestamps[key] = time.perf_counter()

            # 添加到同步队列
            self.sync_queue.append({"key": key, "data": data, "timestamp": time.perf_counter(), "operation": "cache"})

            logger.debug(f"Data cached: key={key}, cache_size={len(self.cached_data)}")
            return True

        except Exception as e:
            logger.error(f"Failed to cache data: key={key}, error={e}")
            return False

    async def get_cached_data(self, key: str) -> Any | None:
        """
        获取缓存数据

        Args:
            key: 缓存键

        Returns:
            缓存数据，如果不存在或过期则返回None
        """
        try:
            if key not in self.cached_data:
                return None

            # 检查是否过期
            cache_time = self.cache_timestamps.get(key, 0)
            if time.perf_counter() - cache_time > self.config.cache_ttl:
                # 删除过期缓存
                del self.cached_data[key]
                del self.cache_timestamps[key]
                return None

            return self.cached_data[key]

        except Exception as e:
            logger.error(f"Failed to get cached data: key={key}, error={e}")
            return None

    def get_status_info(self) -> dict[str, Any]:
        """
        获取状态信息

        Returns:
            状态信息字典
        """
        return {
            "service_status": self.service_status.value,
            "circuit_breaker_state": self.circuit_breaker_state.value,
            "circuit_breaker_metrics": {
                "total_requests": self.circuit_breaker_metrics.total_requests,
                "failed_requests": self.circuit_breaker_metrics.failed_requests,
                "success_requests": self.circuit_breaker_metrics.success_requests,
                "failure_rate": self.circuit_breaker_metrics.failure_rate,
                "success_rate": self.circuit_breaker_metrics.success_rate,
            },
            "cache_info": {
                "cached_items": len(self.cached_data),
                "sync_queue_size": len(self.sync_queue),
            },
            "health_check_history_size": len(self.health_check_history),
            "last_status_change": self.last_status_change,
            "is_running": self.is_running,
        }

    async def _health_check_loop(self):
        """健康检查循环"""
        while self.is_running:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.config.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health check loop error: {e}")
                await asyncio.sleep(self.config.health_check_interval)

    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            start_time = time.perf_counter()

            # 这里应该调用实际的健康检查逻辑
            # 暂时使用模拟的健康检查
            is_healthy = await self._simulate_health_check()

            response_time = time.perf_counter() - start_time

            result = HealthCheckResult(
                is_healthy=is_healthy,
                response_time=response_time,
                error_message="" if is_healthy else "Health check failed",
            )

            self.health_check_history.append(result)

            logger.debug(f"Health check completed: healthy={is_healthy}, response_time={response_time:.3f}s")

        except Exception as e:
            result = HealthCheckResult(
                is_healthy=False, response_time=self.config.health_check_timeout, error_message=str(e)
            )
            self.health_check_history.append(result)
            logger.error(f"Health check failed: {e}")

    async def _simulate_health_check(self) -> bool:
        """
        模拟健康检查（实际实现中应该调用外部服务的健康检查接口）

        Returns:
            健康检查结果
        """
        # 模拟网络延迟
        await asyncio.sleep(0.1)

        # 基于最近的请求成功率判断健康状态
        if self.circuit_breaker_metrics.total_requests > 0:
            return self.circuit_breaker_metrics.success_rate > 0.8

        # 如果没有请求历史，默认认为健康
        return True

    async def _update_circuit_breaker_state(self):
        """更新熔断器状态"""
        current_time = time.perf_counter()

        if self.circuit_breaker_state == CircuitBreakerState.CLOSED:
            # 检查是否需要开启熔断器
            if (
                self.circuit_breaker_metrics.total_requests >= self.config.min_requests
                and self.circuit_breaker_metrics.failure_rate >= self.config.failure_threshold
            ):
                self.circuit_breaker_state = CircuitBreakerState.OPEN
                self.circuit_breaker_open_time = current_time

                logger.warning(
                    f"Circuit breaker opened: failure_rate={self.circuit_breaker_metrics.failure_rate:.3f}, "
                    f"total_requests={self.circuit_breaker_metrics.total_requests}"
                )

        elif self.circuit_breaker_state == CircuitBreakerState.OPEN:
            # 检查是否可以进入半开状态
            if current_time - self.circuit_breaker_open_time >= self.config.timeout_duration:
                self.circuit_breaker_state = CircuitBreakerState.HALF_OPEN
                # 重置指标以便重新评估
                self.circuit_breaker_metrics = CircuitBreakerMetrics()

                logger.info("Circuit breaker entered half-open state")

        elif self.circuit_breaker_state == CircuitBreakerState.HALF_OPEN:
            # 检查是否可以关闭熔断器或重新开启
            if self.circuit_breaker_metrics.total_requests >= self.config.half_open_max_calls:
                if self.circuit_breaker_metrics.failure_rate < self.config.failure_threshold:
                    self.circuit_breaker_state = CircuitBreakerState.CLOSED
                    logger.info("Circuit breaker closed - service recovered")
                else:
                    self.circuit_breaker_state = CircuitBreakerState.OPEN
                    self.circuit_breaker_open_time = current_time
                    logger.warning("Circuit breaker reopened - service still failing")

    async def _update_service_status(self):
        """更新服务状态"""
        old_status = self.service_status

        if self.circuit_breaker_state == CircuitBreakerState.OPEN:
            new_status = ServiceStatus.FAILED
        elif self.circuit_breaker_state == CircuitBreakerState.HALF_OPEN:
            new_status = ServiceStatus.DEGRADED
        else:
            # 基于最近的健康检查结果判断
            recent_checks = list(self.health_check_history)[-5:]  # 最近5次检查
            if recent_checks:
                healthy_count = sum(1 for check in recent_checks if check.is_healthy)
                if healthy_count >= len(recent_checks) * 0.8:  # 80%以上健康
                    new_status = ServiceStatus.HEALTHY
                else:
                    new_status = ServiceStatus.DEGRADED
            else:
                new_status = ServiceStatus.HEALTHY

        if new_status != old_status:
            self.service_status = new_status
            self.last_status_change = time.perf_counter()

            logger.info(f"Service status changed: {old_status.value} -> {new_status.value}")

    async def _sync_loop(self):
        """同步循环"""
        while self.is_running:
            try:
                if self.sync_queue and self.is_service_available():
                    await self._sync_cached_data()
                await asyncio.sleep(self.config.sync_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Sync loop error: {e}")
                await asyncio.sleep(self.config.sync_interval)

    async def _sync_cached_data(self):
        """同步缓存数据"""
        if not self.sync_queue:
            return

        # 获取一批待同步的数据
        batch = []
        for _ in range(min(self.config.sync_batch_size, len(self.sync_queue))):
            if self.sync_queue:
                batch.append(self.sync_queue.popleft())

        if not batch:
            return

        try:
            # 这里应该调用实际的同步逻辑
            # 暂时使用模拟的同步
            success = await self._simulate_sync(batch)

            if success:
                logger.info(f"Successfully synced {len(batch)} cached items")
            else:
                # 同步失败，重新加入队列
                for item in reversed(batch):
                    self.sync_queue.appendleft(item)
                logger.warning(f"Failed to sync {len(batch)} cached items, re-queued")

        except Exception as e:
            # 同步异常，重新加入队列
            for item in reversed(batch):
                self.sync_queue.appendleft(item)
            logger.error(f"Sync error: {e}, re-queued {len(batch)} items")

    async def _simulate_sync(self, batch: list[dict[str, Any]]) -> bool:
        """
        模拟同步操作（实际实现中应该调用外部服务的同步接口）

        Args:
            batch: 待同步的数据批次

        Returns:
            同步是否成功
        """
        # 模拟网络延迟
        await asyncio.sleep(0.5)

        # 模拟同步成功率（基于服务状态）
        if self.service_status == ServiceStatus.HEALTHY:
            return True
        else:
            return False

    async def _cleanup_expired_cache(self):
        """清理过期缓存"""
        current_time = time.perf_counter()
        expired_keys = []

        for key, timestamp in self.cache_timestamps.items():
            if current_time - timestamp > self.config.cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self.cached_data[key]
            del self.cache_timestamps[key]

        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

    async def _save_cache_to_file(self):
        """保存缓存到文件"""
        try:
            cache_data = {
                "cached_data": self.cached_data,
                "cache_timestamps": self.cache_timestamps,
                "sync_queue": list(self.sync_queue),
                "saved_at": time.perf_counter(),
            }

            with open(self.cache_file_path, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

            logger.debug(f"Cache saved to file: {self.cache_file_path}")

        except Exception as e:
            logger.error(f"Failed to save cache to file: {e}")

    async def _load_cache_from_file(self):
        """从文件加载缓存"""
        try:
            if not self.cache_file_path.exists():
                return

            with open(self.cache_file_path, "r", encoding="utf-8") as f:
                cache_data = json.load(f)

            # 检查缓存是否过期
            saved_at = cache_data.get("saved_at", 0)
            if time.perf_counter() - saved_at > self.config.cache_ttl:
                logger.info("Cache file expired, starting with empty cache")
                return

            # 恢复缓存数据
            self.cached_data = cache_data.get("cached_data", {})
            self.cache_timestamps = cache_data.get("cache_timestamps", {})

            # 恢复同步队列
            sync_queue_data = cache_data.get("sync_queue", [])
            self.sync_queue = deque(sync_queue_data)

            logger.info(f"Cache loaded from file: {len(self.cached_data)} items, {len(self.sync_queue)} pending sync")

        except Exception as e:
            logger.error(f"Failed to load cache from file: {e}")


# 全局服务降级管理器实例
service_degradation_manager = ServiceDegradationManager()
