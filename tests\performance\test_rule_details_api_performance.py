#!/usr/bin/env python3
"""
规则明细API性能测试
测试统一后的API接口性能表现
"""

import json
import time

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from models.database import RuleTemplate, RuleTemplateStatusEnum


class TestRuleDetailsAPIPerformance:
    """规则明细API性能测试类"""

    @pytest.fixture
    def test_template(self, db_session: Session):
        """创建测试规则模板"""
        template = RuleTemplate(
            rule_key="test_performance",
            rule_type="performance_test",
            name="性能测试规则模板",
            description="用于性能测试的规则模板",
            status=RuleTemplateStatusEnum.READY
        )
        db_session.add(template)
        db_session.commit()
        db_session.refresh(template)
        return template

    @pytest.fixture
    def sample_rule_detail_data(self):
        """示例规则明细数据"""
        return {
            "rule_name": "性能测试规则明细",
            "level1": "药品适应症",
            "level2": "限制使用",
            "level3": "年龄限制",
            "error_reason": "该药品仅限成人使用",
            "degree": "严重",
            "reference": "药品说明书第3条",
            "detail_position": "处方明细",
            "prompted_fields1": "drug_code",
            "prompted_fields3": "药品编码",
            "type": "药品规则",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "remarks": "性能测试备注信息",
            "in_illustration": "输入药品编码和患者年龄",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": "XB05BA01,XB05BA02",
            "diag_whole_code": "I10.x00",
            "diag_code_prefix": "I10",
            "diag_name_keyword": "高血压",
            "fee_whole_code": "310101001",
            "fee_code_prefix": "3101",
            "extended_fields": json.dumps({"age_threshold": 18, "limit_days": 30})
        }

    def test_single_create_performance(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试单条创建的性能"""
        start_time = time.time()

        response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200
        assert response.json().get("success") is True

        # 性能要求：单条创建应在200ms内完成
        assert response_time < 0.2, f"单条创建耗时 {response_time:.3f}s，超过200ms阈值"

    def test_batch_create_performance(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试批量创建的性能"""
        # 准备100条数据
        batch_data = {
            "operations": []
        }

        for i in range(100):
            data = sample_rule_detail_data.copy()
            data["rule_name"] = f"批量性能测试规则{i+1}"
            batch_data["operations"].append({
                "operation": "CREATE",
                "data": data
            })

        start_time = time.time()

        response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}/batch",
            json=batch_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

        # 性能要求：100条批量创建应在5秒内完成
        assert response_time < 5.0, f"100条批量创建耗时 {response_time:.3f}s，超过5秒阈值"

        # 验证处理速度
        operations_per_second = 100 / response_time
        assert operations_per_second > 20, f"处理速度 {operations_per_second:.1f} ops/s，低于20 ops/s要求"

    def test_list_query_performance(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试列表查询的性能"""
        # 先创建1000条测试数据
        batch_data = {
            "operations": []
        }

        for i in range(1000):
            data = sample_rule_detail_data.copy()
            data["rule_name"] = f"查询性能测试规则{i+1}"
            batch_data["operations"].append({
                "operation": "CREATE",
                "data": data
            })

        # 分批创建数据（避免单次请求过大）
        for batch_start in range(0, 1000, 100):
            batch_end = min(batch_start + 100, 1000)
            current_batch = {
                "operations": batch_data["operations"][batch_start:batch_end]
            }

            response = client.post(
                f"/api/v1/rules/details/{test_template.rule_key}/batch",
                json=current_batch,
                headers={"X-API-KEY": "a_very_secret_key_for_development"}
            )
            assert response.status_code == 200

        # 测试查询性能
        start_time = time.time()

        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={"page": 1, "page_size": 50},
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

        # 性能要求：查询1000条数据中的50条应在500ms内完成
        assert response_time < 0.5, f"列表查询耗时 {response_time:.3f}s，超过500ms阈值"

    def test_filtered_query_performance(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试带过滤条件查询的性能"""
        # 先创建500条测试数据
        batch_data = {
            "operations": []
        }

        for i in range(500):
            data = sample_rule_detail_data.copy()
            data["rule_name"] = f"过滤性能测试规则{i+1}"
            data["level1"] = "药品适应症" if i % 2 == 0 else "诊疗项目"
            data["type"] = "药品规则" if i % 3 == 0 else "诊疗规则"
            batch_data["operations"].append({
                "operation": "CREATE",
                "data": data
            })

        # 分批创建数据
        for batch_start in range(0, 500, 100):
            batch_end = min(batch_start + 100, 500)
            current_batch = {
                "operations": batch_data["operations"][batch_start:batch_end]
            }

            response = client.post(
                f"/api/v1/rules/details/{test_template.rule_key}/batch",
                json=current_batch,
                headers={"X-API-KEY": "a_very_secret_key_for_development"}
            )
            assert response.status_code == 200

        # 测试过滤查询性能
        start_time = time.time()

        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={
                "page": 1,
                "page_size": 20,
                "level1": "药品适应症",
                "type": "药品规则"
            },
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

        # 性能要求：过滤查询应在300ms内完成
        assert response_time < 0.3, f"过滤查询耗时 {response_time:.3f}s，超过300ms阈值"

    def test_search_query_performance(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试搜索查询的性能"""
        # 先创建200条测试数据
        batch_data = {
            "operations": []
        }

        for i in range(200):
            data = sample_rule_detail_data.copy()
            data["rule_name"] = f"搜索性能测试规则{i+1}"
            data["error_reason"] = f"搜索测试错误原因{i+1}"
            batch_data["operations"].append({
                "operation": "CREATE",
                "data": data
            })

        # 分批创建数据
        for batch_start in range(0, 200, 50):
            batch_end = min(batch_start + 50, 200)
            current_batch = {
                "operations": batch_data["operations"][batch_start:batch_end]
            }

            response = client.post(
                f"/api/v1/rules/details/{test_template.rule_key}/batch",
                json=current_batch,
                headers={"X-API-KEY": "a_very_secret_key_for_development"}
            )
            assert response.status_code == 200

        # 测试搜索性能
        start_time = time.time()

        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={
                "page": 1,
                "page_size": 20,
                "search": "搜索性能测试"
            },
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

        # 性能要求：搜索查询应在400ms内完成
        assert response_time < 0.4, f"搜索查询耗时 {response_time:.3f}s，超过400ms阈值"

    def test_single_update_performance(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试单条更新的性能"""
        # 先创建一条规则明细
        create_response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )
        assert create_response.status_code == 200
        created_data = create_response.json()["data"]
        detail_id = created_data["id"]

        # 测试更新性能
        update_data = {
            "rule_name": "更新后的性能测试规则",
            "error_reason": "更新后的错误原因",
            "remarks": "更新后的备注信息"
        }

        start_time = time.time()

        response = client.put(
            f"/api/v1/rules/details/{test_template.rule_key}/{detail_id}",
            json=update_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200
        assert response.json().get("success") is True

        # 性能要求：单条更新应在150ms内完成
        assert response_time < 0.15, f"单条更新耗时 {response_time:.3f}s，超过150ms阈值"

    def test_single_delete_performance(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试单条删除的性能"""
        # 先创建一条规则明细
        create_response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )
        assert create_response.status_code == 200
        created_data = create_response.json()["data"]
        detail_id = created_data["id"]

        # 测试删除性能
        start_time = time.time()

        response = client.delete(
            f"/api/v1/rules/details/{test_template.rule_key}/{detail_id}",
            headers={"X-API-KEY": "a_very_secret_key_for_development"}
        )

        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200
        assert response.json().get("success") is True

        # 性能要求：单条删除应在100ms内完成
        assert response_time < 0.1, f"单条删除耗时 {response_time:.3f}s，超过100ms阈值"

    def test_concurrent_requests_performance(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试并发请求的性能（模拟）"""
        import queue
        import threading

        results = queue.Queue()

        def make_request():
            """发起单个请求"""
            start_time = time.time()

            response = client.get(
                f"/api/v1/rules/details/{test_template.rule_key}",
                params={"page": 1, "page_size": 10},
                headers={"X-API-KEY": "a_very_secret_key_for_development"}
            )

            end_time = time.time()
            response_time = end_time - start_time

            results.put({
                "status_code": response.status_code,
                "success": response.json().get("success"),
                "response_time": response_time
            })

        # 创建10个并发线程
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)

        # 启动所有线程
        start_time = time.time()
        for thread in threads:
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        end_time = time.time()
        total_time = end_time - start_time

        # 收集结果
        response_times = []
        success_count = 0

        while not results.empty():
            result = results.get()
            assert result["status_code"] == 200
            if result["success"]:
                success_count += 1
            response_times.append(result["response_time"])

        # 验证并发性能
        assert success_count == 10, f"只有 {success_count}/10 个请求成功"
        assert total_time < 2.0, f"10个并发请求总耗时 {total_time:.3f}s，超过2秒阈值"

        avg_response_time = sum(response_times) / len(response_times)
        assert avg_response_time < 0.5, f"平均响应时间 {avg_response_time:.3f}s，超过500ms阈值"
