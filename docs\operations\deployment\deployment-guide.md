# 规则验证系统部署指南

## 1. 概述

本文档详细说明了如何在不同环境中部署和配置规则验证系统，包括主子节点架构、降级机制、规则同步等功能的部署方案。

**最新更新**：2025-07-02 - 添加主子节点规则同步启动修复相关部署说明

## 2. 系统要求

### 2.1 硬件要求

#### 2.1.1 最小配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB可用空间
- **网络**: 100Mbps

#### 2.1.2 推荐配置
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 50GB SSD
- **网络**: 1Gbps

#### 2.1.3 生产环境配置
- **CPU**: 8核心或更多
- **内存**: 16GB RAM或更多
- **存储**: 100GB SSD + 备份存储
- **网络**: 冗余网络连接

### 2.2 软件要求

#### 2.2.1 操作系统
- **Linux**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Windows**: Windows Server 2019+
- **macOS**: macOS 11+ (仅开发环境)

#### 2.2.2 Python环境
- **Python版本**: 3.9+
- **包管理器**: pip 21.0+
- **虚拟环境**: venv或conda

#### 2.2.3 数据库
- **开发/测试**: SQLite 3.35+
- **生产环境**: PostgreSQL 13+ 或 MySQL 8.0+

#### 2.2.4 Web服务器
- **开发环境**: Uvicorn (内置)
- **生产环境**: Nginx + Uvicorn/Gunicorn

## 3. 安装步骤

### 3.1 环境准备

#### 3.1.1 创建用户和目录
```bash
# 创建应用用户
sudo useradd -m -s /bin/bash ruleengine

# 创建应用目录
sudo mkdir -p /opt/ruleengine
sudo chown ruleengine:ruleengine /opt/ruleengine

# 创建日志目录
sudo mkdir -p /var/log/ruleengine
sudo chown ruleengine:ruleengine /var/log/ruleengine
```

#### 3.1.2 安装系统依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3 python3-pip python3-venv git nginx postgresql-client

# CentOS/RHEL
sudo yum update
sudo yum install -y python3 python3-pip git nginx postgresql
```

### 3.2 代码部署

#### 3.2.1 获取代码
```bash
# 切换到应用用户
sudo su - ruleengine

# 克隆代码仓库
cd /opt/ruleengine
git clone https://github.com/your-org/rule-engine.git .

# 或者从发布包部署
wget https://releases.example.com/rule-engine-v1.0.0.tar.gz
tar -xzf rule-engine-v1.0.0.tar.gz
```

#### 3.2.2 创建虚拟环境
```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

#### 3.2.3 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 验证安装
python -c "import fastapi, uvicorn; print('依赖安装成功')"
```

### 3.3 数据库配置

#### 3.3.1 SQLite配置（开发/测试）
```bash
# 创建数据库目录
mkdir -p data

# 初始化数据库
python -c "
from models.database import init_db
init_db()
print('数据库初始化完成')
"
```

#### 3.3.2 PostgreSQL配置（生产环境）
```bash
# 创建数据库和用户
sudo -u postgres psql << EOF
CREATE DATABASE ruleengine;
CREATE USER ruleengine WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE ruleengine TO ruleengine;
\q
EOF

# 配置数据库连接
export DATABASE_URL="postgresql://ruleengine:your_secure_password@localhost/ruleengine"

# 运行数据库迁移
alembic upgrade head
```

### 3.4 配置文件设置

#### 3.4.1 创建配置文件
```bash
# 复制配置模板
cp config/settings.py.example config/settings.py
cp config/degradation_config.py.example config/degradation_config.py

# 编辑配置文件
nano config/settings.py
```

#### 3.4.2 环境变量配置
```bash
# 创建环境变量文件
cat > .env << EOF
# 基础配置
DEBUG=false
SECRET_KEY=your_very_secure_secret_key
DATABASE_URL=postgresql://ruleengine:password@localhost/ruleengine

# 降级机制配置
DEGRADATION_ENABLED=true
DEGRADATION_API_KEY=your_secure_api_key
DEGRADATION_LOG_LEVEL=INFO

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
EOF

# 设置文件权限
chmod 600 .env
```

## 4. 主子节点架构部署

### 4.1 架构概述

规则验证系统采用主子节点（Master-Slave）架构：
- **主节点**：负责规则管理、Web界面、规则同步服务
- **子节点**：负责高性能规则验证，从主节点同步规则数据

### 4.2 主节点部署

#### 4.2.1 主节点配置
```bash
# 主节点环境变量配置
cat > .env.master << EOF
# 节点类型
NODE_TYPE=master

# 数据库配置（主节点需要完整数据库）
DATABASE_URL=postgresql://ruleengine:password@localhost/ruleengine

# 规则同步配置
ENABLE_RULE_SYNC=true
RULE_SYNC_INTERVAL=60
RULE_SYNC_TIMEOUT=120.0

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# 规则文件生成配置（新增 - 2025-07-02）
AUTO_GENERATE_RULE_CACHE=true
RULE_CACHE_PATH=rules_cache.json.gz
RULE_VERSION_PATH=rules_version.txt
EOF
```

#### 4.2.2 主节点启动流程
**更新说明（2025-07-02）**：主节点启动时现在会自动生成规则缓存文件

```bash
# 启动主节点
python master.py

# 启动日志关键信息：
# 1. Loading rules into memory cache...
# 2. Generating rules cache file for slave nodes...  ← 新增步骤
# 3. Rules cache file generated successfully.        ← 新增步骤
# 4. Starting X queue workers...
# 5. Startup process complete.
```

**生成的文件**：
- `rules_cache.json.gz` - gzip压缩的规则数据文件
- `rules_version.txt` - 规则版本文件

#### 4.2.3 主节点验证
```bash
# 检查主节点状态
curl http://localhost:8000/health

# 检查规则文件生成
ls -la rules_cache.json.gz rules_version.txt

# 验证规则文件格式
python -c "
import gzip, json
with gzip.open('rules_cache.json.gz', 'rt') as f:
    data = json.load(f)
print('✓ 规则文件格式正确')
print(f'规则数量: {data.get(\"total_count\", 0)}')
print(f'版本: {data.get(\"version\", \"unknown\")[:16]}...')
"
```

### 4.3 子节点部署

#### 4.3.1 子节点配置
```bash
# 子节点环境变量配置
cat > .env.slave << EOF
# 节点类型
NODE_TYPE=slave

# 主节点连接配置
MASTER_NODE_URL=http://master-node:8000
MASTER_API_KEY=your_secure_api_key

# 规则同步配置
ENABLE_RULE_SYNC=true
RULE_SYNC_INTERVAL=60
RULE_SYNC_TIMEOUT=120.0
RULE_SYNC_MAX_RETRIES=3
RULE_SYNC_RETRY_INTERVAL=30.0

# 本地规则文件配置（新增 - 2025-07-02）
LOCAL_RULES_PATH=rules_cache.json.gz
LOCAL_VERSION_PATH=rules_version.txt

# API配置
API_HOST=0.0.0.0
API_PORT=8001
API_WORKERS=2
EOF
```

#### 4.3.2 子节点启动流程
**更新说明（2025-07-02）**：子节点启动时现在支持智能文件格式检测

```bash
# 启动子节点
python slave.py

# 启动日志关键信息：
# 1. Slave node starting up...
# 2. Loading rules from local file: rules_cache.json.gz (size: X bytes)  ← 增强
# 3. Successfully loaded gzip-compressed rule data                       ← 新增
# 4. Rule package info - Version: XXXXXXXX..., Export time: ...         ← 新增
# 5. Successfully loaded X rule datasets from local file cache.
# 6. Rule sync service started (if enabled)
```

#### 4.3.3 子节点验证
```bash
# 检查子节点状态
curl http://localhost:8001/health

# 检查规则加载状态
curl http://localhost:8001/api/v1/rules/status

# 验证规则验证功能
curl -X POST http://localhost:8001/api/v1/validate \
  -H "Content-Type: application/json" \
  -d '{"patient_data": {...}}'
```

### 4.4 离线部署模式

#### 4.4.1 离线子节点配置
```bash
# 离线模式配置
cat > .env.offline << EOF
# 禁用规则同步
ENABLE_RULE_SYNC=false

# 其他配置保持不变
NODE_TYPE=slave
API_HOST=0.0.0.0
API_PORT=8001
EOF
```

#### 4.4.2 规则文件准备
```bash
# 从主节点复制规则文件到离线环境
scp master-node:/opt/ruleengine/rules_cache.json.gz ./
scp master-node:/opt/ruleengine/rules_version.txt ./

# 或使用规则文件管理工具
python tools/rule_file_manager.py export --output rules_cache.json.gz
```

### 4.5 故障排查

#### 4.5.1 主节点问题
**规则文件生成失败**：
```bash
# 检查日志
tail -f logs/app.log | grep "Generating rules cache file"

# 常见原因和解决方法：
# 1. 数据库连接问题 - 检查数据库配置
# 2. 磁盘空间不足 - 清理磁盘空间
# 3. 文件权限问题 - 检查目录权限
```

#### 4.5.2 子节点问题
**规则文件加载失败**：
```bash
# 检查文件存在性
ls -la rules_cache.json.gz

# 检查文件格式
python -c "
import os
if os.path.exists('rules_cache.json.gz'):
    with open('rules_cache.json.gz', 'rb') as f:
        header = f.read(2)
    print(f'文件头: {header.hex()}')
    print(f'是否gzip: {header == b\"\\x1f\\x8b\"}')
else:
    print('文件不存在')
"

# 强制重新同步
rm -f rules_cache.json.gz rules_version.txt
# 重启子节点，会自动从主节点同步
```

## 5. 服务配置

### 5.1 Systemd服务配置

#### 5.1.1 主节点服务配置
```bash
sudo tee /etc/systemd/system/ruleengine-master.service << EOF
[Unit]
Description=Rule Engine Master Node
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=exec
User=ruleengine
Group=ruleengine
WorkingDirectory=/opt/ruleengine
Environment=PATH=/opt/ruleengine/venv/bin
EnvironmentFile=/opt/ruleengine/.env.master
ExecStart=/opt/ruleengine/venv/bin/python master.py
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 健康检查
ExecStartPost=/bin/sleep 10
ExecStartPost=/bin/bash -c 'curl -f http://localhost:8000/health || exit 1'

[Install]
WantedBy=multi-user.target
EOF
```

#### 5.1.2 子节点服务配置
```bash
sudo tee /etc/systemd/system/ruleengine-slave.service << EOF
[Unit]
Description=Rule Engine Slave Node
After=network.target
# 如果需要连接主节点，添加依赖
# After=network-online.target
# Wants=network-online.target

[Service]
Type=exec
User=ruleengine
Group=ruleengine
WorkingDirectory=/opt/ruleengine
Environment=PATH=/opt/ruleengine/venv/bin
EnvironmentFile=/opt/ruleengine/.env.slave
ExecStart=/opt/ruleengine/venv/bin/python slave.py
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 健康检查
ExecStartPost=/bin/sleep 10
ExecStartPost=/bin/bash -c 'curl -f http://localhost:8001/health || exit 1'

[Install]
WantedBy=multi-user.target
EOF
```

#### 5.1.3 离线子节点服务配置
```bash
sudo tee /etc/systemd/system/ruleengine-offline.service << EOF
[Unit]
Description=Rule Engine Offline Slave Node
After=network.target

[Service]
Type=exec
User=ruleengine
Group=ruleengine
WorkingDirectory=/opt/ruleengine
Environment=PATH=/opt/ruleengine/venv/bin
EnvironmentFile=/opt/ruleengine/.env.offline
ExecStart=/opt/ruleengine/venv/bin/python slave.py
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
```

#### 4.1.2 启用和启动服务
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable ruleengine

# 启动服务
sudo systemctl start ruleengine

# 检查服务状态
sudo systemctl status ruleengine
```

### 4.2 Nginx配置

#### 4.2.1 创建Nginx配置
```bash
sudo tee /etc/nginx/sites-available/ruleengine << EOF
upstream ruleengine_backend {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态文件
    location /static/ {
        alias /opt/ruleengine/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://ruleengine_backend;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 前端应用
    location / {
        root /opt/ruleengine/frontend/dist;
        try_files \$uri \$uri/ /index.html;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://ruleengine_backend/health;
        access_log off;
    }
}
EOF
```

#### 4.2.2 启用Nginx配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/ruleengine /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载Nginx
sudo systemctl reload nginx
```

## 5. 前端部署

### 5.1 构建前端应用

#### 5.1.1 安装Node.js依赖
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 配置环境变量
cat > .env.production << EOF
VITE_API_URL=https://your-domain.com/api
VITE_API_KEY=your_api_key
VITE_DEBUG=false
EOF
```

#### 5.1.2 构建生产版本
```bash
# 构建应用
npm run build

# 验证构建结果
ls -la dist/
```

### 5.2 部署静态文件

#### 5.2.1 复制文件到Web目录
```bash
# 创建前端目录
sudo mkdir -p /opt/ruleengine/frontend

# 复制构建文件
sudo cp -r dist/* /opt/ruleengine/frontend/

# 设置权限
sudo chown -R ruleengine:ruleengine /opt/ruleengine/frontend
```

## 6. Docker部署

### 6.1 Docker Compose配置

#### 6.1.1 创建docker-compose.yml
```yaml
version: '3.8'

services:
  database:
    image: postgres:13
    environment:
      POSTGRES_DB: ruleengine
      POSTGRES_USER: ruleengine
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ruleengine_network
    restart: unless-stopped

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      DATABASE_URL: postgresql://ruleengine:${DB_PASSWORD}@database:5432/ruleengine
      DEGRADATION_ENABLED: true
      DEGRADATION_API_KEY: ${API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - database
    networks:
      - ruleengine_network
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    environment:
      VITE_API_URL: http://backend:8000/api
    depends_on:
      - backend
    networks:
      - ruleengine_network
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - ruleengine_network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  ruleengine_network:
    driver: bridge
```

#### 6.1.2 创建环境变量文件
```bash
cat > .env << EOF
DB_PASSWORD=your_secure_db_password
API_KEY=your_secure_api_key
EOF
```

#### 6.1.3 启动服务
```bash
# 构建和启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
```

## 7. 监控和日志

### 7.1 日志配置

#### 7.1.1 日志轮转配置
```bash
sudo tee /etc/logrotate.d/ruleengine << EOF
/var/log/ruleengine/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ruleengine ruleengine
    postrotate
        systemctl reload ruleengine
    endscript
}
EOF
```

### 7.2 监控配置

#### 7.2.1 健康检查脚本
```bash
cat > /opt/ruleengine/health_check.sh << EOF
#!/bin/bash

# 检查服务状态
if ! systemctl is-active --quiet ruleengine; then
    echo "ERROR: Rule Engine service is not running"
    exit 1
fi

# 检查API响应
if ! curl -f -s http://localhost:8000/health > /dev/null; then
    echo "ERROR: API health check failed"
    exit 1
fi

# 检查降级机制状态
if ! curl -f -s http://localhost:8000/api/v1/degradation/status > /dev/null; then
    echo "ERROR: Degradation mechanism not responding"
    exit 1
fi

echo "OK: All checks passed"
exit 0
EOF

chmod +x /opt/ruleengine/health_check.sh
```

#### 7.2.2 Cron监控任务
```bash
# 添加到crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/ruleengine/health_check.sh >> /var/log/ruleengine/health.log 2>&1") | crontab -
```

## 8. 安全配置

### 8.1 防火墙配置

#### 8.1.1 UFW配置（Ubuntu）
```bash
# 启用防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 拒绝直接访问应用端口
sudo ufw deny 8000/tcp
```

### 8.2 SSL证书配置

#### 8.2.1 Let's Encrypt证书
```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

## 9. 备份和恢复

### 9.1 数据备份

#### 9.1.1 数据库备份脚本
```bash
cat > /opt/ruleengine/backup.sh << EOF
#!/bin/bash

BACKUP_DIR="/opt/ruleengine/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p \$BACKUP_DIR

# 备份数据库
pg_dump -h localhost -U ruleengine ruleengine > \$BACKUP_DIR/db_backup_\$DATE.sql

# 备份配置文件
tar -czf \$BACKUP_DIR/config_backup_\$DATE.tar.gz config/

# 清理旧备份（保留30天）
find \$BACKUP_DIR -name "*.sql" -mtime +30 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: \$DATE"
EOF

chmod +x /opt/ruleengine/backup.sh
```

#### 9.1.2 定期备份任务
```bash
# 添加到crontab（每天凌晨2点备份）
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/ruleengine/backup.sh >> /var/log/ruleengine/backup.log 2>&1") | crontab -
```

## 10. 故障排查

### 10.1 常见问题

#### 10.1.1 服务启动失败
```bash
# 检查服务状态
sudo systemctl status ruleengine

# 查看详细日志
sudo journalctl -u ruleengine -f

# 检查配置文件
python -c "from config.settings import *; print('配置正确')"
```

#### 10.1.2 数据库连接问题
```bash
# 测试数据库连接
python -c "
from sqlalchemy import create_engine
from config.settings import DATABASE_URL
engine = create_engine(DATABASE_URL)
conn = engine.connect()
print('数据库连接成功')
conn.close()
"
```

### 10.2 性能调优

#### 10.2.1 Uvicorn工作进程调优
```bash
# 根据CPU核心数调整工作进程数
WORKERS=$(nproc)
echo "推荐工作进程数: $WORKERS"

# 更新服务配置
sudo sed -i "s/--workers 4/--workers $WORKERS/" /etc/systemd/system/ruleengine.service
sudo systemctl daemon-reload
sudo systemctl restart ruleengine
```

## 11. 升级指南

### 11.1 版本升级步骤

#### 11.1.1 准备升级
```bash
# 备份当前版本
/opt/ruleengine/backup.sh

# 停止服务
sudo systemctl stop ruleengine
```

#### 11.1.2 执行升级
```bash
# 获取新版本代码
cd /opt/ruleengine
git fetch origin
git checkout v2.0.0

# 更新依赖
source venv/bin/activate
pip install -r requirements.txt

# 运行数据库迁移
alembic upgrade head
```

#### 11.1.3 验证升级
```bash
# 启动服务
sudo systemctl start ruleengine

# 检查服务状态
sudo systemctl status ruleengine

# 验证功能
curl http://localhost:8000/health
```
