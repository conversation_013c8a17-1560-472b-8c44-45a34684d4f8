{"metadata": {"version": "3.1.0", "last_updated": "2025-07-24", "description": "规则详情表三表结构字段映射权威配置（已移除别名支持，简化架构）", "author": "规则验证系统开发团队", "tables": ["rule_template", "rule_detail", "rule_field_metadata"], "changelog": "v3.1.0: 移除所有字段别名配置，简化字段映射架构"}, "field_definitions": {"common_fields": {"id": {"chinese_name": "主键ID", "data_type": "integer", "required": false, "description": "数据库主键ID", "database_column": "id", "api_field": "id", "excel_column": "主键ID", "system_field": true}, "rule_key": {"chinese_name": "规则键", "data_type": "string", "required": true, "max_length": 100, "description": "规则模板键，关联rule_template表", "database_column": "rule_key", "api_field": "rule_key", "excel_column": "规则键", "system_field": true}, "status": {"chinese_name": "状态", "data_type": "string", "required": false, "description": "记录状态", "database_column": "status", "api_field": "status", "excel_column": "状态", "system_field": true}, "created_at": {"chinese_name": "创建时间", "data_type": "string", "required": false, "description": "记录创建时间", "database_column": "created_at", "api_field": "created_at", "excel_column": "创建时间", "system_field": true}, "updated_at": {"chinese_name": "更新时间", "data_type": "string", "required": false, "description": "记录更新时间", "database_column": "updated_at", "api_field": "updated_at", "excel_column": "更新时间", "system_field": true}, "extended_fields": {"chinese_name": "扩展字段", "data_type": "string", "required": false, "description": "JSON格式的扩展字段", "database_column": "extended_fields", "api_field": "extended_fields", "excel_column": "扩展字段", "system_field": true}, "rule_id": {"chinese_name": "规则ID", "data_type": "string", "required": false, "max_length": 100, "description": "规则的唯一标识符", "database_column": "rule_id", "api_field": "rule_id", "excel_column": "规则ID", "system_field": true}, "rule_name": {"chinese_name": "规则名称", "data_type": "string", "required": true, "max_length": 500, "description": "规则的显示名称", "database_column": "rule_name", "api_field": "rule_name", "excel_column": "规则名称", "validation_rules": ["required", "max_length:500"]}, "level1": {"chinese_name": "一级错误类型", "data_type": "string", "required": true, "max_length": 100, "description": "一级错误类型", "database_column": "level1", "api_field": "level1", "excel_column": "一级错误类型", "validation_rules": ["required", "max_length:100"]}, "level2": {"chinese_name": "二级错误类型", "data_type": "string", "required": true, "max_length": 100, "description": "二级错误类型", "database_column": "level2", "api_field": "level2", "excel_column": "二级错误类型", "validation_rules": ["required", "max_length:100"]}, "level3": {"chinese_name": "三级错误类型", "data_type": "string", "required": true, "max_length": 100, "description": "三级错误类型", "database_column": "level3", "api_field": "level3", "excel_column": "三级错误类型", "validation_rules": ["required", "max_length:100"]}, "error_reason": {"chinese_name": "错误原因", "data_type": "text", "required": true, "description": "错误原因", "database_column": "error_reason", "api_field": "error_reason", "excel_column": "错误原因", "validation_rules": ["required"]}, "degree": {"chinese_name": "错误程度", "data_type": "string", "required": true, "max_length": 50, "description": "错误程度", "database_column": "degree", "api_field": "degree", "excel_column": "错误程度", "validation_rules": ["required", "max_length:50"]}, "reference": {"chinese_name": "质控依据或参考资料", "data_type": "text", "required": true, "description": "质控依据或参考资料", "database_column": "reference", "api_field": "reference", "excel_column": "质控依据或参考资料", "validation_rules": ["required"]}, "detail_position": {"chinese_name": "具体位置描述", "data_type": "text", "required": true, "description": "具体位置描述", "database_column": "detail_position", "api_field": "detail_position", "excel_column": "具体位置描述", "validation_rules": ["required"]}, "prompted_fields3": {"chinese_name": "提示字段类型", "data_type": "string", "required": false, "max_length": 100, "description": "提示字段的类型", "database_column": "prompted_fields3", "api_field": "prompted_fields3", "excel_column": "提示字段类型", "validation_rules": ["max_length:100"]}, "prompted_fields1": {"chinese_name": "提示字段编码", "data_type": "string", "required": true, "max_length": 100, "description": "提示字段编码", "database_column": "prompted_fields1", "api_field": "prompted_fields1", "excel_column": "提示字段编码", "validation_rules": ["required", "max_length:100"]}, "type": {"chinese_name": "规则类别", "data_type": "string", "required": true, "max_length": 100, "description": "规则类别", "database_column": "type", "api_field": "type", "excel_column": "规则类别", "validation_rules": ["required", "max_length:100"]}, "pos": {"chinese_name": "适用业务", "data_type": "string", "required": true, "max_length": 100, "description": "适用业务", "database_column": "pos", "api_field": "pos", "excel_column": "适用业务", "validation_rules": ["required", "max_length:100"]}, "applicableArea": {"chinese_name": "适用地区", "data_type": "string", "required": true, "max_length": 100, "description": "适用地区", "database_column": "applicableArea", "api_field": "applicableArea", "excel_column": "适用地区", "validation_rules": ["required", "max_length:100"]}, "default_use": {"chinese_name": "默认选用", "data_type": "string", "required": true, "max_length": 50, "description": "默认选用", "database_column": "default_use", "api_field": "default_use", "excel_column": "默认选用", "validation_rules": ["required", "max_length:50"]}, "remarks": {"chinese_name": "备注信息", "data_type": "text", "required": false, "description": "备注信息", "database_column": "remarks", "api_field": "remarks", "excel_column": "备注信息", "validation_rules": []}, "in_illustration": {"chinese_name": "入参说明", "data_type": "text", "required": false, "description": "入参说明", "database_column": "in_illustration", "api_field": "in_illustration", "excel_column": "入参说明", "validation_rules": []}, "start_date": {"chinese_name": "开始日期", "data_type": "string", "required": true, "max_length": 20, "description": "开始日期", "database_column": "start_date", "api_field": "start_date", "excel_column": "开始日期", "validation_rules": ["required", "date_format"]}, "end_date": {"chinese_name": "结束日期", "data_type": "string", "required": true, "max_length": 20, "description": "结束日期", "database_column": "end_date", "api_field": "end_date", "excel_column": "结束日期", "validation_rules": ["required", "date_format"]}, "yb_code": {"chinese_name": "药品编码", "data_type": "array", "required": true, "description": "药品编码", "database_column": "yb_code", "api_field": "yb_code", "excel_column": "药品编码", "validation_rules": ["required", "array"]}, "diag_whole_code": {"chinese_name": "完整诊断编码", "data_type": "array", "required": false, "description": "完整诊断编码", "database_column": "diag_whole_code", "api_field": "diag_whole_code", "excel_column": "完整诊断编码", "validation_rules": ["array"]}, "diag_code_prefix": {"chinese_name": "诊断编码前缀", "data_type": "array", "required": false, "description": "诊断编码前缀", "database_column": "diag_code_prefix", "api_field": "diag_code_prefix", "excel_column": "诊断编码前缀", "validation_rules": ["array"]}, "diag_name_keyword": {"chinese_name": "诊断名称关键字", "data_type": "string", "required": false, "max_length": 200, "description": "诊断名称关键字", "database_column": "diag_name_keyword", "api_field": "diag_name_keyword", "excel_column": "诊断名称关键字", "validation_rules": ["max_length:200"]}, "fee_whole_code": {"chinese_name": "完整费用编码", "data_type": "array", "required": false, "description": "完整费用编码", "database_column": "fee_whole_code", "api_field": "fee_whole_code", "excel_column": "完整费用编码", "validation_rules": ["array"]}, "fee_code_prefix": {"chinese_name": "费用编码前缀", "data_type": "array", "required": false, "description": "费用编码前缀", "database_column": "fee_code_prefix", "api_field": "fee_code_prefix", "excel_column": "费用编码前缀", "validation_rules": ["array"]}}, "specific_fields": {"age_threshold": {"chinese_name": "年龄阈值", "data_type": "integer", "required": false, "min_value": 0, "max_value": 150, "description": "年龄阈值", "rule_types": ["drug_limit_adult_and_diag_exact", "drug_limit_adult_and_diag_prefix"], "database_column": "age_threshold", "api_field": "age_threshold", "excel_column": "年龄阈值", "validation_rules": ["integer", "min:0", "max:150"]}, "limit_days": {"chinese_name": "限制天数", "data_type": "integer", "required": false, "min_value": 1, "max_value": 365, "description": "限制天数", "rule_types": ["drug_limit_max_pay_days"], "database_column": "limit_days", "api_field": "limit_days", "excel_column": "限制天数", "validation_rules": ["integer", "min:1", "max:365"]}, "mono_use_yb_code": {"chinese_name": "中药饮片医保编码", "data_type": "string", "required": false, "max_length": 50, "description": "中药饮片医保编码", "rule_types": ["ch_drug_mono_use"], "database_column": "mono_use_yb_code", "api_field": "mono_use_yb_code", "excel_column": "中药饮片医保编码", "validation_rules": ["max_length:10", "min-length:10"]}, "drug_start_with": {"chinese_name": "搭配使用的药品编码前缀", "data_type": "string", "required": false, "max_length": 50, "description": "搭配使用的药品编码前缀", "rule_types": ["drug_deny_mono_use"], "database_column": "drug_start_with", "api_field": "drug_start_with", "excel_column": "搭配使用的药品编码前缀", "validation_rules": ["max_length:50"]}, "drug_code_prefix": {"chinese_name": "药敏试验医保代码前缀", "data_type": "array", "required": false, "description": "药敏试验医保代码前缀", "rule_types": ["drug_limit_drug_sensitivity"], "database_column": "drug_code_prefix", "api_field": "drug_code_prefix", "excel_column": "药敏试验医保代码前缀", "validation_rules": ["array"]}}}, "rule_type_mappings": {"ch_drug_deny_use": {"name": "中药饮片单复方均不予支付", "required_fields": ["yb_code", "rule_name"], "optional_fields": ["remarks"]}, "ch_drug_mono_use": {"name": "中药饮片单方使用不予支付", "required_fields": ["mono_use_yb_code", "yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_deny_mono_use": {"name": "药品单独使用不予支付", "required_fields": ["yb_code", "drug_start_with", "rule_name"], "optional_fields": ["remarks"]}, "drug_exclude_diag": {"name": "药品禁忌症", "required_fields": ["yb_code", "diag_whole_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_adult_and_diag_exact": {"name": "药品限适应症+年龄（精确匹配诊断代码）", "required_fields": ["age_threshold", "diag_whole_code", "yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_adult_and_diag_prefix": {"name": "药品限适应症+年龄（模糊匹配诊断代码）", "required_fields": ["age_threshold", "diag_code_prefix", "yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_children": {"name": "药品儿童专用", "required_fields": ["yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_diag_and_fee_prefix": {"name": "药品限适应症（诊断+费用开头）", "required_fields": ["diag_code_prefix", "yb_code", "fee_code_prefix", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_diag_and_multi_fee": {"name": "药品限适应症（诊断+多个费用）", "required_fields": ["diag_code_prefix", "yb_code", "fee_whole_code", "fee_code_prefix", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_diag_exact": {"name": "药品限适应症（精确匹配诊断）", "required_fields": ["diag_whole_code", "yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_diag_prefix": {"name": "药品限适应症（模糊匹配诊断）", "required_fields": ["diag_code_prefix", "yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_drug_sensitivity": {"name": "药品限适应症（药敏试验+重症感染）", "required_fields": ["diag_code_prefix", "diag_name_keyword", "yb_code", "drug_code_prefix", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_fee_prefix": {"name": "药品限适应症（费用开头）", "required_fields": ["yb_code", "fee_code_prefix", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_female": {"name": "药品区分性别使用（女）", "required_fields": ["yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_institution_level_and_diag_exact": {"name": "药品限适应症+机构级别（精确匹配诊断代码）", "required_fields": ["diag_whole_code", "yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_institution_level_and_diag_prefix": {"name": "药品限适应症+机构级别（模糊匹配诊断代码）", "required_fields": ["diag_code_prefix", "yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_institution_level": {"name": "药品限医疗机构级别", "required_fields": ["yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_male": {"name": "药品区分性别使用（男）", "required_fields": ["yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_maternity_insurance": {"name": "药品限生育保险", "required_fields": ["yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_max_pay_days": {"name": "药品限制最大支付天数", "required_fields": ["yb_code", "limit_days", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_nutritional_risk": {"name": "药品限适应症（营养风险筛查）", "required_fields": ["yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_two_diag": {"name": "药品限适应症（两个诊断）", "required_fields": ["diag_code_prefix", "diag_whole_code", "yb_code", "rule_name"], "optional_fields": ["remarks"]}, "drug_limit_work_injury_insurance": {"name": "药品限工伤保险", "required_fields": ["yb_code", "rule_name"], "optional_fields": ["remarks"]}}}