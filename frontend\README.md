# 智能规则校验系统 - 前端

本项目是智能规则校验系统的前端UI部分，基于 Vue 3 + Vite + Element Plus 构建。

## 🚀 新特性

### 增强API系统 (v3.2.0)
- **字段映射引擎**: 自动处理前后端字段名转换
- **智能缓存**: 基于版本的缓存策略，提升60-80%响应速度
- **增强错误处理**: 分层错误处理和用户友好提示
- **性能监控**: 实时API性能统计和健康检查
- **向后兼容**: 现有代码无需修改即可享受增强功能

详细使用指南请参考：[增强API使用指南](../docs/development/frontend/增强API使用指南.md)

## 项目设置

1.  **安装 Node.js**
    请确保你已安装 Node.js (推荐版本 18.x 或更高)。

2.  **安装依赖**
    在 `frontend` 目录下打开终端，运行以下命令：
    ```sh
    npm install
    ```

## 开发

要启动本地开发服务器，请运行：

```sh
npm run dev
```
开发服务器启动后，通常可以在 `http://localhost:5173` 访问。

项目已配置API代理，所有对 `/api` 的请求都会被转发到 `http://127.0.0.1:18001`（后端服务地址）。

## 构建

要为生产环境构建项目，请运行：

```sh
npm run build
```

构建产物将生成在 `dist` 目录中。 