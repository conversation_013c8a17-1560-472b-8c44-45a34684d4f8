"""
健康检查路由
提供应用启动状态、就绪状态和详细健康检查信息
"""

from typing import Any

from fastapi import APIRouter

from core.logging.logging_system import log as logger
from models.api import ApiResponse

health_router = APIRouter(prefix="/health", tags=["健康检查"])


@health_router.get("/", response_model=ApiResponse[dict[str, Any]])
async def health_check():
    """
    基础健康检查
    """
    try:
        return ApiResponse.success_response(
            data={"status": "healthy", "message": "服务运行正常"},
            message="健康检查通过"
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"健康检查失败: {str(e)}",
            error_code="HEALTH_CHECK_ERROR"
        )


@health_router.get("/ready", response_model=ApiResponse[dict[str, Any]])
async def readiness_check():
    """
    就绪状态检查 - 检查应用是否已完成启动优化
    """
    try:
        from services.application_startup import app_startup

        is_ready = app_startup.is_ready()
        startup_status = app_startup.get_startup_status()

        if is_ready:
            return ApiResponse.success_response(
                data={
                    "ready": True,
                    "startup_completed": startup_status["startup_completed"],
                    "startup_success": startup_status["startup_success"],
                    "startup_time": startup_status["startup_time"]
                },
                message="应用已就绪"
            )
        else:
            return ApiResponse.error_response(
                data={
                    "ready": False,
                    "startup_completed": startup_status["startup_completed"],
                    "startup_success": startup_status["startup_success"],
                    "startup_error": startup_status["startup_error"]
                },
                message="应用未就绪",
                error_code="APP_NOT_READY"
            )

    except Exception as e:
        logger.error(f"就绪状态检查失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"就绪状态检查失败: {str(e)}",
            error_code="READINESS_CHECK_ERROR"
        )


@health_router.get("/detailed", response_model=ApiResponse[dict[str, Any]])
async def detailed_health_check():
    """
    详细健康检查 - 检查所有组件状态
    """
    try:
        health_info = {
            "overall_status": "healthy",
            "components": {},
            "startup_info": {},
            "performance_info": {}
        }

        # 1. 检查应用启动状态
        try:
            from services.application_startup import app_startup
            startup_status = app_startup.get_startup_status()
            health_info["startup_info"] = startup_status
            health_info["components"]["application_startup"] = {
                "status": "healthy" if startup_status["startup_success"] else "unhealthy",
                "details": startup_status
            }
        except Exception as e:
            health_info["components"]["application_startup"] = {
                "status": "error",
                "error": str(e)
            }

        # 2. 检查规则缓存
        try:
            from core.rule_cache import RULE_CACHE
            rule_count = len(RULE_CACHE)
            health_info["components"]["rule_cache"] = {
                "status": "healthy" if rule_count > 0 else "warning",
                "rule_count": rule_count
            }
        except Exception as e:
            health_info["components"]["rule_cache"] = {
                "status": "error",
                "error": str(e)
            }

        # 3. 检查进程池状态
        try:
            from core.dynamic_process_pool import dynamic_process_pool
            pool_stats = dynamic_process_pool.get_stats()
            health_info["components"]["process_pool"] = {
                "status": "healthy" if pool_stats.current_workers > 0 else "warning",
                "details": {
                    "current_workers": pool_stats.current_workers,
                    "max_workers": pool_stats.max_workers,
                    "is_running": dynamic_process_pool._is_running
                }
            }
        except Exception as e:
            health_info["components"]["process_pool"] = {
                "status": "error",
                "error": str(e)
            }

        # 4. 检查内存使用
        try:
            from core.memory_optimizer import memory_optimizer
            memory_stats = memory_optimizer.get_memory_stats()
            memory_status = "healthy"
            if memory_stats.process_memory_mb > 900:  # 90% of 1GB limit
                memory_status = "warning"
            elif memory_stats.process_memory_mb > 1024:  # Over 1GB limit
                memory_status = "critical"

            health_info["components"]["memory"] = {
                "status": memory_status,
                "details": {
                    "process_memory_mb": memory_stats.process_memory_mb,
                    "system_memory_mb": memory_stats.system_memory_mb,
                    "memory_usage_percent": memory_stats.memory_usage_percent
                }
            }
        except Exception as e:
            health_info["components"]["memory"] = {
                "status": "error",
                "error": str(e)
            }

        # 5. 检查缓存状态
        try:
            from core.intelligent_cache import intelligent_cache
            cache_stats = intelligent_cache.get_overall_stats()
            health_info["components"]["intelligent_cache"] = {
                "status": "healthy",
                "details": cache_stats
            }
        except Exception as e:
            health_info["components"]["intelligent_cache"] = {
                "status": "error",
                "error": str(e)
            }

        # 6. 检查超快速校验服务
        try:
            from services.ultra_fast_rule_service import ultra_fast_rule_service
            ultra_stats = ultra_fast_rule_service.get_service_stats()
            health_info["components"]["ultra_fast_service"] = {
                "status": "healthy",
                "details": ultra_stats
            }
        except Exception as e:
            health_info["components"]["ultra_fast_service"] = {
                "status": "error",
                "error": str(e)
            }

        # 计算总体状态
        component_statuses = [comp["status"] for comp in health_info["components"].values()]
        if "error" in component_statuses or "critical" in component_statuses:
            health_info["overall_status"] = "unhealthy"
        elif "warning" in component_statuses:
            health_info["overall_status"] = "degraded"
        else:
            health_info["overall_status"] = "healthy"

        return ApiResponse.success_response(
            data=health_info,
            message=f"详细健康检查完成，总体状态：{health_info['overall_status']}"
        )

    except Exception as e:
        logger.error(f"详细健康检查失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"详细健康检查失败: {str(e)}",
            error_code="DETAILED_HEALTH_CHECK_ERROR"
        )


@health_router.get("/startup-progress", response_model=ApiResponse[dict[str, Any]])
async def get_startup_progress():
    """
    获取启动进度信息
    """
    try:
        from services.index_prebuilder import index_prebuilder
        from services.startup_optimizer import startup_optimizer
        from services.warmup_manager import warmup_manager

        progress_info = {
            "startup_optimizer": startup_optimizer.get_startup_status(),
            "warmup_manager": warmup_manager.get_warmup_status(),
            "index_prebuilder": index_prebuilder.get_build_status()
        }

        return ApiResponse.success_response(
            data=progress_info,
            message="启动进度信息获取成功"
        )

    except Exception as e:
        logger.error(f"获取启动进度失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"获取启动进度失败: {str(e)}",
            error_code="STARTUP_PROGRESS_ERROR"
        )
