#!/usr/bin/env python3
"""
数据库表结构重建执行脚本
执行rebuild_database_tables.sql中的SQL语句
"""

import sys
from pathlib import Path

import pymysql


def execute_sql_file(sql_file_path: str):
    """执行SQL文件"""

    # 数据库连接配置
    config = {
        'host': '***************',
        'port': 3306,
        'user': 'rule_user',
        'password': 'mysql_password',
        'database': 'rule_service',
        'charset': 'utf8mb4'
    }

    try:
        # 读取SQL文件
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # 连接数据库
        print(f"正在连接数据库 {config['host']}:{config['port']}...")
        connection = pymysql.connect(**config)

        try:
            with connection.cursor() as cursor:
                # 分割SQL语句（按分号分割）
                sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]

                print(f"准备执行 {len(sql_statements)} 条SQL语句...")

                for i, sql in enumerate(sql_statements, 1):
                    if sql.strip():
                        try:
                            print(f"执行第 {i} 条SQL: {sql[:50]}...")
                            cursor.execute(sql)

                            # 如果是查询语句，显示结果
                            if sql.strip().upper().startswith(('SELECT', 'SHOW')):
                                results = cursor.fetchall()
                                if results:
                                    print(f"查询结果: {results}")

                        except Exception as e:
                            print(f"执行第 {i} 条SQL时出错: {e}")
                            print(f"SQL语句: {sql}")
                            # 继续执行其他语句
                            continue

                # 提交事务
                connection.commit()
                print("✅ 数据库表结构重建完成！")

        finally:
            connection.close()

    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # SQL文件路径
    sql_file = Path(__file__).parent / "rebuild_database_tables.sql"

    if not sql_file.exists():
        print(f"❌ SQL文件不存在: {sql_file}")
        sys.exit(1)

    print("🚀 开始执行数据库表结构重建...")
    execute_sql_file(str(sql_file))
