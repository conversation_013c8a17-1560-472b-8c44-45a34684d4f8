# 患者数据规则校验服务

基于FastAPI的高性能患者数据规则校验服务，支持并行执行多个验证规则。

## 功能特点

- 基于FastAPI构建的高性能RESTful API
- 支持并行执行多个规则验证
- 可扩展的规则引擎架构
- 完善的错误处理和日志机制
- 提供健康检查和规则查询接口

## 性能优化

### 问题

原始的规则校验服务在处理大规模数据和大量规则时遇到了性能瓶颈。主要原因是：

1.  **GIL 限制**: Python的全局解释器锁（GIL）使得多线程无法真正利用多核CPU并行执行计算密集型任务。
2.  **CPU密集型任务**: 单个规则的校验过程是CPU密集型的，当规则数量增加时，串行执行会导致处理时间线性增长。

### 解决方案

为了解决上述问题，我们对 `RuleService` 进行了架构重构，采用了基于 `multiprocessing.Pool` 的并行处理模型。

核心设计如下：

-   **多进程并行**: 利用 `multiprocessing.Pool` 将不同的规则校验任务分发到多个独立的子进程中，从而绕过GIL的限制，充分利用服务器的多核CPU资源。
-   **高效数据共享**: 通过 `Pool` 的 `initializer` 和 `initargs` 参数，在每个工作进程启动时，将大型的、只读的患者数据加载一次。数据成为子进程的全局变量，避免了在每次任务分发时重复序列化和传输大量数据，极大地降低了进程间通信（IPC）的开销。
-   **异步集成**: 使用 `asyncio.run_in_executor` 将同步的 `pool.map` 操作无缝集成到FastAPI的异步事件循环中，防止阻塞主线程，保证了API的响应性。

### 成果

本次优化带来了显著的性能提升：

-   **充分利用硬件**: 能够有效利用多核CPU，并行处理能力随CPU核心数扩展。
-   **提升处理效率**: 在处理大量规则时，总耗时远低于串行执行，显著提升了服务的吞吐量和响应速度。
-   **可扩展性**: 架构具备良好的可扩展性，能够应对未来更复杂的规则和更大的数据量。

更多关于本次优化的详细架构设计，请参考文档：[规则服务性能优化架构](documentation/rule_service_optimization_arch.md)。

## 高并发处理与请求排队

为了应对高并发场景下可能出现的瞬时请求洪峰，我们引入了基于 `asyncio.Queue` 的请求排队系统。该系统旨在解耦请求的接收与处理，防止服务因过载而崩溃。

### 解决的问题

在高并发下，大量请求同时到达可能耗尽服务的工作线程或进程，导致新请求被拒绝，甚至整个服务崩溃。此排队系统通过缓冲传入的请求，解决了服务过载的问题。

### 工作原理

1.  **请求缓冲**: API 端点 (`/api/v1/validate`) 接收到请求后，不再直接进行处理，而是将其放入一个有界大小的 `asyncio.Queue` 中，并立即返回一个等待凭证。
2.  **后台工作者池**: 一组后台异步任务（`worker`）作为消费者，持续从队列中获取请求进行处理。这确保了实际的规则校验工作是在可控的并发水平下进行的。
3.  **非阻塞API**: 由于请求被快速放入队列，API 端点可以迅速响应客户端，避免了因等待耗时任务完成而导致的长时间阻塞。

### 带来的好处

-   **提高稳定性**: 通过平滑请求峰值，有效防止了服务因瞬时高并发而崩溃。
-   **增强响应能力**: 即使后台正在处理计算密集型任务，API 依然能够快速响应并接受新的请求，提升了用户体验。

## 项目结构

```
rule_validation_system/
│
├── master.py                         # Master节点主应用
├── slave.py                          # Slave节点主应用
├── requirements.txt                  # 项目依赖
│
├── api/                              # API层
│   ├── routers/
│   │   └── master/                   # Master节点路由
│   │       ├── management.py         # 规则管理API
│   │       ├── rule_details.py       # 规则明细API (新增)
│   │       ├── sync.py               # 同步API
│   │       ├── validation.py         # 验证API
│   │       └── degradation.py        # 降级API
│   ├── dependencies/                 # 依赖注入
│   └── middleware/                   # 中间件
│
├── models/                           # 数据模型
│   ├── database.py                   # 数据库模型
│   ├── api.py                        # API模型
│   └── patient.py                    # 患者数据模型
│
├── services/                         # 服务层 (重新组织)
│   ├── rule_details/                 # 规则明细服务 (新增)
│   │   ├── migration_services/       # 数据迁移服务
│   │   └── validation_services/      # 数据验证服务
│   ├── data_management/              # 数据管理服务 (新增)
│   │   ├── data_mapping_engine.py    # 数据映射引擎
│   │   └── data_consistency_checker.py # 一致性检查
│   ├── task_management/              # 任务管理服务 (新增)
│   │   ├── task_status_manager.py    # 任务状态管理
│   │   └── batch_processor.py        # 批处理管理
│   ├── rule_service.py               # 核心规则服务
│   ├── rule_loader.py                # 规则加载服务
│   └── sync_service.py               # 同步服务
│
├── core/                             # 核心组件
│   ├── db_session.py                 # 数据库会话
│   ├── logging/                      # 日志系统
│   ├── degradation_manager.py        # 降级管理
│   └── performance_monitor.py        # 性能监控
│
├── rules/                            # 规则定义
│   ├── base_rules/                   # 基础规则
│   ├── specific_rules/               # 专项规则
│   └── rule_registry.py              # 规则注册
│
├── tests/                            # 测试文件 (重新组织)
│   ├── api/
│   │   └── rule_details/             # 规则明细API测试 (新增)
│   ├── services/
│   │   ├── rule_details/             # 规则明细服务测试 (新增)
│   │   ├── data_management/          # 数据管理测试 (新增)
│   │   └── task_management/          # 任务管理测试 (新增)
│   ├── integration/                  # 集成测试
│   └── performance/                  # 性能测试
│
├── documentation/                    # 项目文档
│   ├── rule_details/                 # 规则明细功能文档 (新增)
│   ├── rule_data_sets_restructure_*.md # 重构相关文档
│   └── api_testing_report.md         # API测试报告
│
├── config/                           # 配置文件
├── tools/                            # 工具脚本
└── frontend/                         # 前端应用
```

## 主从架构设计

本项目采用主从架构（Master-Slave Architecture）设计，支持分布式部署和高性能规则验证。

### 架构概述

```
┌─────────────────┐  规则同步    ┌─────────────────┐
│ 主节点 (Master) │ ──────────→  │ 子节点 (Slave)  │
│                 │              │                 │
│ • 规则管理      │              │ • 规则验证      │
│ • 数据库连接    │              │ • 本地缓存      │
│ • Web界面       │              │ • 高性能处理    │
│ • API服务       │              │ • 离线运行      │
└─────────────────┘              └─────────────────┘
```

### 节点职责

#### 主节点 (Master)
- **规则管理**：负责规则的创建、修改、删除和版本管理
- **数据库连接**：直接连接数据库，管理规则数据和配置
- **Web界面**：提供规则管理的Web界面
- **规则同步**：生成规则缓存文件，供子节点使用
- **API服务**：提供完整的API接口

#### 子节点 (Slave)
- **规则验证**：专注于高性能的规则验证处理
- **本地缓存**：使用本地缓存文件，无需数据库连接
- **离线运行**：支持完全离线模式，适合封闭网络环境
- **性能优化**：针对验证性能进行优化，支持高并发

### 数据库连接要求

**重要说明**：在主从架构中，只有主节点需要连接数据库，子节点完全脱离数据库运行。

| 节点类型 | 数据库连接 | 配置要求 | 运行模式 |
|---------|-----------|----------|----------|
| **主节点 (Master)** | ✅ 必需 | 完整数据库配置 | 在线模式 |
| **子节点 (Slave)** | ❌ 不需要 | 无数据库配置 | 离线/在线模式 |

### 部署模式

#### 1. 离线模式（推荐）
- 子节点设置 `ENABLE_RULE_SYNC=false`
- 使用预生成的 `rules_cache.json.gz` 文件
- 完全脱离网络和数据库运行
- 适合医院内网等封闭环境

#### 2. 在线同步模式
- 子节点设置 `ENABLE_RULE_SYNC=true`
- 定期从主节点同步规则更新
- 需要网络连接到主节点
- 适合云部署或联网环境

## 配置管理

本项目采用集中化的配置管理策略，通过环境变量和 `.env` 文件来管理所有可配置项，以增强安全性和灵活性。

### 核心组件

-   **`.env` 文件**: 用于存储本地开发环境的配置变量。此文件不应提交到版本控制中。
-   **[`config/settings.py`](config/settings.py)**: 使用 `pydantic-settings` 库定义和加载配置。它会从环境变量和 `.env` 文件中读取配置，并提供类型安全的 `settings` 对象供整个应用程序使用。

### 设置步骤

1.  **创建配置文件**:
    复制项目根目录下的 [`.env.example`](.env.example) 文件，并将其重命名为 `.env`。

    ```bash
    cp .env.example .env
    ```

2.  **编辑配置**:
    在 `.env` 文件中添加或修改您需要的环境变量。例如，要更改服务端口，可以添加以下行：

    ```dotenv
    # 服务运行端口
    SERVER_PORT=18001
    ```

### 工作原理

-   **类型安全**: [`config/settings.py`](config/settings.py) 中的 `Settings` 类为每个配置项定义了预期的 Python 类型（如 `int`, `str`)。`pydantic-settings` 会在应用启动时自动验证环境变量，确保其类型正确，否则将引发错误，从而避免了因配置错误导致的运行时问题。
-   **集中访问**: 应用中的任何模块都可以通过 `from config.settings import settings` 导入全局配置实例，实现了配置的统一管理和轻松访问。

## 日志系统

本项目实现了先进的环境特定日志配置系统，为不同运行环境（开发、测试、生产）提供差异化的日志策略，显著提升运维效率和系统安全性。

### 核心特性

-   **🌍 环境自适应**: 自动检测运行环境，应用相应的日志配置策略
-   **🔒 敏感信息保护**: 智能过滤敏感数据，确保生产环境安全
-   **📊 结构化日志**: 支持JSON格式输出，便于日志分析和监控
-   **⚡ 性能优化**: 异步日志处理，最小化对应用性能的影响
-   **🔄 向后兼容**: 完全兼容现有日志配置，平滑升级

### 环境配置策略

| 环境 | 日志级别 | 控制台输出 | 日志格式 | 敏感信息过滤 | 文件轮转 |
|------|----------|------------|----------|--------------|----------|
| **开发环境 (DEV)** | DEBUG | ✅ 启用 | 彩色人类可读 | ❌ 不过滤 | 5MB/3天 |
| **测试环境 (TEST)** | INFO | ❌ 禁用 | 结构化格式 | ⚠️ 基础过滤 | 10MB/7天 |
| **生产环境 (PROD)** | WARNING | ❌ 禁用 | JSON格式 | ✅ 完全过滤 | 50MB/30天 |

### 配置方法

#### 1. 环境变量配置

```bash
# 启用环境特定日志配置
LOG_USE_ENVIRONMENT_CONFIG=true

# 开发环境配置
LOG_DEV_LEVEL=DEBUG
LOG_DEV_STDOUT_ENABLED=true
LOG_DEV_ROTATION=5 MB
LOG_DEV_RETENTION=3 days

# 测试环境配置
LOG_TEST_LEVEL=INFO
LOG_TEST_STDOUT_ENABLED=false
LOG_TEST_ROTATION=10 MB
LOG_TEST_RETENTION=7 days

# 生产环境配置
LOG_PROD_LEVEL=WARNING
LOG_PROD_STDOUT_ENABLED=false
LOG_PROD_ROTATION=50 MB
LOG_PROD_RETENTION=30 days
```

#### 2. 代码中使用

```python
# 使用全局日志器（推荐）
from core.logging.logging_system import log

log.info("这是一条信息日志")
log.warning("这是一条警告日志")
log.error("这是一条错误日志")

# 获取环境特定配置
from config.environment_log_config import get_current_log_config

config = get_current_log_config()
print(f"当前环境: {config.environment}")
```

### 敏感信息保护

系统自动过滤以下敏感信息：

-   **基础过滤** (TEST/PROD): 密码、密钥、令牌等
-   **完全过滤** (PROD): 用户信息、邮箱、IP地址等

```python
# 原始日志: "用户登录: user=<EMAIL> password=secret123"
# 生产环境输出: "用户登录: ***EMAIL*** ***FILTERED***"
```

### 日志文件结构

```
logs/
├── dev/          # 开发环境日志
├── test/         # 测试环境日志
└── prod/         # 生产环境日志
    ├── app_2025-07-01_10-30-00_123456.log
    └── app_2025-07-01_10-30-00_123456.log.gz
```
## 安装与运行

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行服务

```bash
uvicorn main:app --host 0.0.0.0 --port 18001 --reload
```

服务将在 http://localhost:18001 启动

## API接口

### 验证患者数据

**POST /api/v1/validate**

请求体示例:

```json
{
    "ids": [
        "a021f78c4edae55921f64c9e0fc62300",
        "665bd39691da00c273a3737a598f2028",
        "5d7598d054c988cfcb41a639db5f7abe",
        "f486da6d9dbd62630f4adadf9fcf02d3",
        "fa807c37bc2251c6b3056c44368c0267",
        "288481dc85bf8964a37523d82a11a57f",
        "e4aec8ec17ab4f925b0ef1bbdb6cc7b7",
        "02c30323deb92a03919d0c2e0b14bd0e",
        "3dd5e8f662bc33ab078f1bf97236e25f",
        "3068e6a48bc05b8277560169970d6c4b",
        "16488ad6767174294e0a634ee4fe54fc",
        "2b330d5766c27eb058c19d9f560b486f",
        "737878a53055e44abec4127d94da6e26",
        "4ff163a16ea327e9343707cbf44b749e",
        "fe0f78896722dc9f1b635e048507e221",
        "efdc48bda7a58f6348075748be69647a",
        "1df2e491c182e2e5bbee3a23e44597d0",
        "3c526b024bbedb25d4020a9c0445cdd8",
        "b07cdcabbe93a18eadfeb1598b213904",
        "f8adc4c2cd8202c1239d0329cbb29ccb",
        "f910141ae741270f2358cbe08259f833",
        "70966fd92db139a78075bdc50ee04f2a",
        "daa52aa2f305b6befe663043646c1ae0",
        "ff9dfe0d596b90d33e01bcb2ab627085",
        "de2614d91dd167e4b280e55bd80b8179",
        "32d20b69b07f23dd38dfcb7066b17c39",
        "5f6e05ada12ba95e4d9a07c2af491308",
        "b3b2dd4ff9dfdf9bddfdfebe1a236bf9",
        "c863f74d2b8c06f820ea66207c506e5a"
    ],
    "patientInfo": {
        "bah": "0******0",
        "dataSource": "",
        "patientMedicalInsuranceType": "城乡",
        "surgicalDepartmentCode": "",
        "settlementOrNot": "",
        "insuredPlace": "",
        "medicalCategory": "",
        "medicalInsuranceType": "",
        "basic_information": {
            "grade": "2",
            "level": "-",
            "hospitalNature": "",
            "gender": "",
            "genderDescribe": "男",
            "birthDate": 1719763200000,
            "age": 15,
            "country": "CHN",
            "newbornAge": 29
        },
        "process": {
            "admissionRoute": "2",
            "admissionDate": 1722476736000,
            "dischargeDate": 1722831041000,
            "dischargeDeptCode": "A07",
            "dischargeDeptCodeDescribe": "儿科",
            "transferDeptCode": null,
            "transferDeptCodeDescribe": null,
            "admissionDeptCode": "A07",
            "admissionDeptCodeDescribe": "儿科"
        },
        "Diagnosis": {
            "outPatientDiagnosisICDCode": "",
            "outPatientDiagnosisChICDCode": null,
            "diagnoseNameOnAdmission": null,
            "diagnoseICDCodeOnAdmission": null,
            "diagnosis": [
                {
                    "diagnosisType": "0",
                    "isPrincipalDiagnosis": "1",
                    "diagnosisName": "恶性肿瘤维持性化学治疗",
                    "diagnosisICDCode": "O26.900",
                    "conditionOnAdmission": "1",
                    "situationAtDischarge": null,
                    "displayOrder": "0"
                },
                {
                    "diagnosisType": "0",
                    "isPrincipalDiagnosis": "0",
                    "diagnosisName": "泌尿道感染",
                    "diagnosisICDCode": "D51.00",
                    "conditionOnAdmission": "1",
                    "situationAtDischarge": null,
                    "displayOrder": "1"
                },
                {
                    "diagnosisType": "0",
                    "isPrincipalDiagnosis": "0",
                    "diagnosisName": "急性髓细胞白血病",
                    "diagnosisICDCode": "",
                    "conditionOnAdmission": "1",
                    "situationAtDischarge": null,
                    "displayOrder": "2"
                },
                {
                    "diagnosisType": "0",
                    "isPrincipalDiagnosis": "0",
                    "diagnosisName": "结核性胸膜",
                    "diagnosisICDCode": "L20.000",
                    "conditionOnAdmission": "1",
                    "situationAtDischarge": null,
                    "displayOrder": "3"
                },
                {
                    "diagnosisType": "0",
                    "isPrincipalDiagnosis": "0",
                    "diagnosisName": "脾大",
                    "diagnosisICDCode": "R16.100x001",
                    "conditionOnAdmission": "1",
                    "situationAtDischarge": null,
                    "displayOrder": "4"
                },
                {
                    "diagnosisType": "0",
                    "isPrincipalDiagnosis": "0",
                    "diagnosisName": "血小板减少",
                    "diagnosisICDCode": "",
                    "conditionOnAdmission": "2",
                    "situationAtDischarge": null,
                    "displayOrder": "5"
                },
                {
                    "diagnosisType": "0",
                    "isPrincipalDiagnosis": "0",
                    "diagnosisName": "肿瘤性贫血",
                    "diagnosisICDCode": "H34.500",
                    "conditionOnAdmission": "2",
                    "situationAtDischarge": null,
                    "displayOrder": "6"
                },
                {
                    "diagnosisType": "0",
                    "isPrincipalDiagnosis": "0",
                    "diagnosisName": "低蛋白血症",
                    "diagnosisICDCode": "",
                    "conditionOnAdmission": "2",
                    "situationAtDischarge": null,
                    "displayOrder": "7"
                },
                {
                    "diagnosisType": "0",
                    "isPrincipalDiagnosis": "0",
                    "diagnosisName": "肺",
                    "diagnosisICDCode": "J18.900",
                    "conditionOnAdmission": "2",
                    "situationAtDischarge": null,
                    "displayOrder": "8"
                }
            ],
            "operation": [
                {
                    "operationType": "1",
                    "operationName": "静脉注射化疗药物",
                    "operationICDCode": "99.2503",
                    "operationDate": 1711382400000,
                    "operationStartTime": 1711382400000,
                    "operationEndTime": 1711382400000,
                    "operationDuration": null,
                    "anesthesiaStartTime": null,
                    "anesthesiaEndTime": null,
                    "operationLevel": "1",
                    "operationDrName": "丛*",
                    "operationDrCode": "D220171014482",
                    "operationDrHealthCareCode": "D220171014482",
                    "firstAssistantName": null,
                    "secondAssistantName": null,
                    "anesthesiaDrName": "张*",
                    "anesthesiaDrCode": "D220171014274",
                    "anesthesiaDrHealthCareCode": "D220171014274",
                    "anesthesiaType": null,
                    "assistantLevel": null,
                    "incisionClass": null,
                    "healing": null,
                    "combinedIncisionAndHealingCode": null,
                    "isMajorOperation": "1",
                    "operationIndex": "0",
                    "firstAssistantCode": null,
                    "secondAssistantCode": null,
                    "anesthesiaTypeDescribe": null
                },
                {
                    "operationType": "1",
                    "operationName": "分子靶向治疗",
                    "operationICDCode": "99.2800x006",
                    "operationDate": 1711382400000,
                    "operationStartTime": 1711382400000,
                    "operationEndTime": 1711382400000,
                    "operationDuration": null,
                    "anesthesiaStartTime": null,
                    "anesthesiaEndTime": null,
                    "operationLevel": "1",
                    "operationDrName": "丛*",
                    "operationDrCode": "D220171014482",
                    "operationDrHealthCareCode": "D220171014482",
                    "firstAssistantName": null,
                    "secondAssistantName": null,
                    "anesthesiaDrName": "张*",
                    "anesthesiaDrCode": "D220171014274",
                    "anesthesiaDrHealthCareCode": "D220171014274",
                    "anesthesiaType": null,
                    "assistantLevel": null,
                    "incisionClass": null,
                    "healing": null,
                    "combinedIncisionAndHealingCode": null,
                    "isMajorOperation": "0",
                    "operationIndex": "1",
                    "firstAssistantCode": null,
                    "secondAssistantCode": null,
                    "anesthesiaTypeDescribe": null
                },
                {
                    "operationType": "1",
                    "operationName": "骨髓穿刺术",
                    "operationICDCode": "41.3800x001",
                    "operationDate": 1710864000000,
                    "operationStartTime": 1710864000000,
                    "operationEndTime": 1710864000000,
                    "operationDuration": null,
                    "anesthesiaStartTime": null,
                    "anesthesiaEndTime": null,
                    "operationLevel": "2",
                    "operationDrName": "丛*",
                    "operationDrCode": "D220171014482",
                    "operationDrHealthCareCode": "D220171014482",
                    "firstAssistantName": null,
                    "secondAssistantName": null,
                    "anesthesiaDrName": "张*",
                    "anesthesiaDrCode": "D220171014274",
                    "anesthesiaDrHealthCareCode": "D220171014274",
                    "anesthesiaType": null,
                    "assistantLevel": null,
                    "incisionClass": null,
                    "healing": null,
                    "combinedIncisionAndHealingCode": null,
                    "isMajorOperation": "0",
                    "operationIndex": "2",
                    "firstAssistantCode": null,
                    "secondAssistantCode": null,
                    "anesthesiaTypeDescribe": null
                },
                {
                    "operationType": "1",
                    "operationName": "骨髓穿刺术",
                    "operationICDCode": "41.3800x001",
                    "operationDate": 1712592000000,
                    "operationStartTime": 1712592000000,
                    "operationEndTime": 1712592000000,
                    "operationDuration": null,
                    "anesthesiaStartTime": null,
                    "anesthesiaEndTime": null,
                    "operationLevel": "2",
                    "operationDrName": "丛*",
                    "operationDrCode": "D220171014482",
                    "operationDrHealthCareCode": "D220171014482",
                    "firstAssistantName": null,
                    "secondAssistantName": null,
                    "anesthesiaDrName": "张*",
                    "anesthesiaDrCode": "D220171014274",
                    "anesthesiaDrHealthCareCode": "D220171014274",
                    "anesthesiaType": null,
                    "assistantLevel": null,
                    "incisionClass": null,
                    "healing": null,
                    "combinedIncisionAndHealingCode": null,
                    "isMajorOperation": "0",
                    "operationIndex": "3",
                    "firstAssistantCode": null,
                    "secondAssistantCode": null,
                    "anesthesiaTypeDescribe": null
                }
            ]
        },
        "fees": [
            {
                "uniq_field": "1|1",
                "bzjs":null,
                "id": "1",
                "create_date": 1731050374000,
                "jzsj": 1717088000000,
                "xh": "1",
                "tfxh": null,
                "fydm": "F00000063643",
                "fymc": "手术",
                "je": 2,
                "ksdm": "28",
                "ksmc": "检验科",
                "xmdm": "36",
                "xmmc": "临床诊断项目费",
                "ybdm": "XN07XXY184B001010100344",
                "ybdmRaw": "002101020170000-320000000-02",
                "wjdm": "33.06",
                "dj": 2.0,
                "sl":2,
                "dw": "例",
                "ynxmmc": "临床诊断项目费",
                "kdksmc": "重症医学科",
                "kdksdm": "0103030217"
            },
            {
                "uniq_field": "1|2",
                "bzjs":null,
                "id": "2",
                "create_date": 1731050374000,
                "jzsj": 1716965080000,
                "xh": "2",
                "tfxh": null,
                "fydm": "F00000063643",
                "fymc": "血清碳酸氢盐(HCO3)测定(酶促动力学法)",
                "je": 4,
                "ksdm": "28",
                "ksmc": "检验科",
                "xmdm": "36",
                "xmmc": "临床诊断项目费",
                "ybdm": "XN07XXY184B001010100344",
                "ybdmRaw": "001201000140000-120100014-5",
                "wjdm": "331700001",
                "dj": 2,
                "sl":1,
                "dw": "小时",
                "ynxmmc": "临床诊断项目费",
                "kdksmc": "重症医学科",
                "kdksdm": "0103030217"
            },
            {
                "uniq_field": "1|3",
                "bzjs":null,
                "id": "3",
                "create_date": 1731050374000,
                "jzsj": 1716965080000,
                "xh": "3",
                "tfxh": null,
                "fydm": "25030100101",
                "fymc": "门急诊留观诊查费腔镜",
                "je": 2,
                "ksdm": "28",
                "ksmc": "检验科",
                "xmdm": "24",
                "xmmc": "实验室诊断费",
                "ybdm": "XN07XXY184B001010100344",
                "ybdmRaw": "",
                "wjdm": "",
                "dj": 2,
                "sl": 1,
                "dw": "日",
                "ynxmmc": "实验室诊断费",
                "kdksmc": "重症医学科",
                "kdksdm": "0103030217"
            },
            {
                "uniq_field": "2|1",
                "bzjs":null,
                "id": "4",
                "create_date": 1713543005000,
                "jzsj": 1706933957000,
                "xh": "4",
                "tfxh": null,
                "fydm": "12010001800",
                "fymc": "磁共振增强扫描/每部",
                "je": 396,
                "ksdm": "0502",
                "ksmc": "康复理疗科",
                "xmdm": "36",
                "xmmc": "临床诊断项目费",
                "ybdm": "XN07XXY184B001010100344",
                "ybdmRaw": "",
                "wjdm": "14912084||3",
                "dj": 396.0,
                "sl": 5,
                "dw": "次",
                "ynxmmc": "临床诊断项目费",
                "kdksmc": "重症医学科",
                "kdksdm": "0103030217"
            },
            {
                "uniq_field": "2|2",
                "bzjs":null,
                "id": "5",
                "create_date": 1713543005000,
                "jzsj": 1733544641000,
                "xh": "5",
                "tfxh": null,
                "fydm": "25030500180",
                "fymc": "磁共振扫描-超过1.5T加收",
                "je": 5,
                "ksdm": "0103030217",
                "ksmc": "康复理疗科",
                "xmdm": "36",
                "xmmc": "临床诊断项目费",
                "ybdm": "XN07XXY184B001020100344",
                "ybdmRaw": "",
                "wjdm": "14912084||4",
                "dj": 20.0,
                "sl": 5.0,
                "dw": "日",
                "ynxmmc": "临床诊断项目费",
                "kdksmc": "肿瘤血液内科一病区病房",
                "kdksdm": "0103030217"
            },
            {
                "uniq_field": "2|3",
                "bzjs":null,
                "id": "6",
                "create_date": 1713543005000,
                "jzsj": 1733470611000,
                "xh": "6",
                "tfxh": null,
                "fydm": "25030500180",
                "fymc": "磁共振增强扫描（使用高压加收）",
                "je": 6,
                "ksdm": "0130000001",
                "ksmc": "康复理疗科",
                "xmdm": "24",
                "xmmc": "实验室诊断费",
                "ybdm": "XN07XXY184B001020100344",
                "ybdmRaw": "XN07XXD130E002010102774",
                "wjdm": "14912084||10",
                "dj": 3.8,
                "sl": 1.0,
                "dw": "日",
                "ynxmmc": "实验室诊断费",
                "kdksmc": "肿瘤血液内科一病区病房",
                "kdksdm": "0103030217"
            },
            {
                "uniq_field": "31171596||5||5",
                "bzjs":null,
                "id": "7",
                "create_date": 1672798200000,
                "jzsj": 1733470611000,
                "xh": "7",
                "tfxh": null,
                "fydm": "25030500280",
                "fymc": "特级护理",
                "je": 7,
                "ksdm": "0130000001",
                "ksmc": "康复理疗科",
                "xmdm": "24",
                "xmmc": "实验室诊断费",
                "ybdm": "XN07XXY184B001020100344",
                "ybdmRaw": "",
                "wjdm": "14912084||10",
                "dj": 3.8,
                "sl": 1.0,
                "dw": "日",
                "ynxmmc": "实验室诊断费",
                "kdksmc": "肿瘤血液内科一病区病房",
                "kdksdm": "0103030217"
            },
            {
                "uniq_field": "31171596||5||6",
                "bzjs":null,
                "id": "8",
                "create_date": 1672823580000,
                "jzsj": 1725242746000,
                "xh": "8",
                "tfxh": null,
                "fydm": "F00000038007",
                "fymc": "普通病房床位费（单人间）",
                "je": 8,
                "ksdm": "0130000001",
                "ksmc": "检验科",
                "xmdm": "24",
                "xmmc": "实验室诊断费",
                "ybdm": "001201000130000",
                "ybdmRaw": "003112020010000-A311202001",
                "wjdm": "14912084||10",
                "dj": 1.0,
                "sl": 1.0,
                "dw": "项",
                "ynxmmc": "实验室诊断费",
                "kdksmc": "肿瘤血液内科一病区病房",
                "kdksdm": "0103030217"
            },
            {
                "uniq_field": "31171596||5||6",
                "bzjs":null,
                "id": "9",
                "create_date": 1672823580000,
                "jzsj": 1729799200000,
                "xh": "9",
                "tfxh": null,
                "fydm": "F00000038007",
                "fymc": "皮肤牵引术",
                "je": 9,
                "ksdm": "0130000001",
                "ksmc": "检验科",
                "xmdm": "24",
                "xmmc": "实验室诊断费",
                "ybdm": "XN07XXY184B001010100344",
                "ybdmRaw": "",
                "wjdm": "14912084||10",
                "dj": 1.0,
                "sl": 25.0,
                "dw": "小时",
                "ynxmmc": "实验室诊断费",
                "kdksmc": "肿瘤血液内科一病区病房",
                "kdksdm": "0103030217"
            }
        ]
    }
}
```

响应示例:

```json
{
  "results": [
    {
      "rule_id": "RULE001",
      "rule_name": "同一项目同一天重复收费检测",
      "main_category": "重复收费规则",
      "sub_category": "同项目重复收费",
      "passed": true,
      "message": "未发现同一项目同一天重复收费",
      "details": null,
      "execution_time": 0.1023
    },
    ...
  ],
  "execution_time": 0.2541,
  "total_rules": 5
}
```

### 获取可用规则列表

**GET /rules**

响应示例:

```json
{
  "code": 200,
  "success": true,
  "message": "规则验证完成",
  "data": [
    {
      "id": "c863f74d2b8c06f820ea66207c506e5a",
      "output": {
        "type_": "非编码",
        "message": "\"注射用尤瑞克林\"支付不超过21天",
        "level1": "政策类",
        "level2": "药品政策限定类",
        "level3": "药品限最大支付天数",
        "error_reason": "\"注射用尤瑞克林\"支付不超过21天",
        "degree": "强制",
        "reference": "\"1、国家基本医疗保险、工伤保险和生育保险药品目录（2023年）\n2、国家基本医疗保险、工伤保险和生育保险药品目录（2024年）\n3、医保药品分类与代码数据库更新（截至2024年3月22日维护）\"\n",
        "prompted_fields3": "",
        "prompted_fields1": "ybdm",
        "prompted_fields2": "[序号]",
        "detail_position": "费用信息",
        "type": "",
        "pos": "通用",
        "applicableArea": "全国",
        "default_use": "是",
        "error_fee": 27,
        "remarks": "排除自费",
        "in_illustration": "",
        "used_count": 41,
        "illegal_count": 32,
        "used_days": 6,
        "illegal_days": 3,
        "illegal_item": "9,6,7,5"
      }
    }
  ]
}
```

### 健康检查

**GET /health**

响应示例:

```json
{
  "status": "healthy",
  "service": "patient-rule-validator"
}
```

## 开发新规则

要添加新的验证规则，需要完成两步：
1. 第一步在`rules/base_rules`目录下创建新的规则类，并确保该类继承自`BaseRule`基类，实现`_validate_logic`方法，实现规则逻辑
2. 第二步在`rules/specific_rules`目录下创建新的规则类，并确保该类继承自第一步实现的规则类，并在实例化传入具体参数

PS: 这么做的目的是，为了加快校验，所以把每一个明细规则都单独实现，然后通过`rule_registry.py`注册到服务中
    第一步实现的父类，其校验逻辑只针对某一类型的规则，
    第二步实现的孙类，只是把父类校验逻辑中需要传入的参数，传入到孙类中，并进行实例化



新规则会在服务启动时自动注册。

## 性能优化

本服务使用`asyncio`进行规则的并行验证，对于大量规则的场景具有良好的性能表现。如果规则数量过多，可以考虑以下优化:

1. 增加工作线程池以处理CPU密集型的规则计算
2. 实现规则预筛选，只执行与特定患者数据相关的规则
3. 缓存常用规则的验证结果

## 扩展建议

1. 完善日志，添加日志文件
2. 添加数据库支持，持久化规则配置和验证结果
3. 实现规则版本管理，支持规则的热更新
4. 集成监控和警报系统

## 内存优化与管理

为了解决服务在长时间运行后出现的内存持续增长问题，我们实施了一套全面的内存优化方案。该方案结合了短期缓解措施和长期根治策略，确保了系统的稳定性和健壮性。

### 问题背景

我们观察到，随着处理请求数量的增加，服务占用的内存会随时间线性增长，最终可能导致服务因内存耗尽而崩溃。经过分析，根本原因在于 `multiprocessing.Pool` 的工作进程被长期复用，而部分规则在执行过程中存在未能完全释放的资源。

### 解决方案

我们采纳了分阶段的解决方案来应对此问题：

1.  **短期缓解策略：定期重启工作进程**
    *   通过为 `multiprocessing.Pool` 设置 `maxtasksperchild` 参数，我们强制每个工作进程在处理一定数量的任务后自动终止并被一个新的、干净的进程替换。这有效地回收了累积的内存，作为一种快速止血手段，迅速稳定了系统。

2.  **长期根治策略：引入规则生命周期**
    *   为了从根本上解决资源泄漏，我们引入了 `setup()` 和 `teardown()` 的规则生命周期管理。所有规则被要求遵循这一模式：在 `setup()` 方法中初始化所需资源，并在 `teardown()` 方法中确保其被完全释放。这一机制（尤其是在 `finally` 块中调用 `teardown`）保证了即使在发生异常时，资源也能被可靠地清理，从而根除了内存泄漏的源头。

通过以上措施，我们不仅解决了当前的内存泄漏问题，还为未来规则的开发建立了一套健壮的资源管理规范。

更多关于此方案的详细信息，请参阅架构设计文档：[内存泄漏缓解架构方案](documentation/memory_leak_mitigation_arch.md)
