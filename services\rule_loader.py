import gc
import gzip
import json
import os
import time
from typing import Any, TypeVar

import psutil
from sqlalchemy.orm import Session

from core.db_session import get_session_factory
from core.logging.logging_system import log as logger
from core.performance_monitor import performance_monitor
from core.rule_cache import RULE_CACHE
from models.database import RuleDetail, RuleDetailStatusEnum, RuleTemplate, RuleTemplateStatusEnum
from services.rule_data_sync_service import RuleDataSyncService
from services.rule_detail_service import ServiceError
from services.unified_data_mapping_engine import UnifiedDataMappingEngine

# --- Constants ---
LOCAL_RULES_PATH = "rules_cache.json.gz"
LOCAL_VERSION_PATH = "rules_version.txt"
CACHE_METADATA_PATH = "cache_metadata.json"
T = TypeVar("T")

# --- 缓存性能统计 ---
_cache_stats = {
    "cache_hits": 0,
    "cache_misses": 0,
    "load_times": [],
    "memory_usage": [],
    "last_cache_check": 0,
    "cache_version": None,
    "cache_size_mb": 0,
}


def _convert_array_fields_for_rules(data: dict[str, Any]) -> dict[str, Any]:
    """
    转换数组字段为列表格式，用于规则实例化

    Args:
        data: 规则数据字典

    Returns:
        转换后的数据字典
    """
    array_fields = [
        "yb_code",
        "diag_whole_code",
        "diag_code_prefix",
        "fee_whole_code",
        "fee_code_prefix",
        "drug_code_prefix",
    ]

    for field in array_fields:
        if field in data and isinstance(data[field], str):
            # 字符串转换为列表
            if data[field].strip():
                data[field] = [item.strip() for item in data[field].split(",") if item.strip()]
            else:
                data[field] = []

    return data


def _convert_rule_detail_to_cache_format(rule_detail: RuleDetail) -> dict:
    """
    将 RuleDetail 对象转换为规则实例化所需的格式

    重构版本：集成UnifiedDataMappingEngine进行字段映射标准化，
    支持扩展字段的JSON格式处理和field_mapping.json配置驱动。

    Args:
        rule_detail: RuleDetail 对象

    Returns:
        dict: 规则实例化格式的数据
    """
    try:
        # 使用UnifiedDataMappingEngine进行字段映射
        unified_mapping_engine = UnifiedDataMappingEngine()

        # 将 RuleDetail 转换为字典
        detail_dict = rule_detail.to_dict()

        # 字段名称标准化（error_level_1 → level1等）
        normalized_data = unified_mapping_engine.normalize_field_names(detail_dict)

        # 验证必填字段完整性
        validation_result = unified_mapping_engine.validate_data(normalized_data)
        if not validation_result.valid:
            logger.warning(f"Rule detail {rule_detail.id} validation failed: {validation_result.errors}")

        # 处理扩展字段的JSON序列化
        structured_data = unified_mapping_engine.convert_to_structured_format(
            normalized_data, rule_key=rule_detail.rule_key
        )

        # 转换数组字段：字符串 → 列表（用于规则实例化）
        structured_data = _convert_array_fields_for_rules(structured_data)

        # 确保 rule_id 字段存在
        if "rule_id" not in structured_data and rule_detail.id:
            structured_data["rule_id"] = str(rule_detail.id)

        logger.debug(
            f"Successfully converted rule detail {rule_detail.id} "
            f"using UnifiedDataMappingEngine with {len(structured_data)} fields"
        )

        return structured_data

    except Exception as e:
        logger.error(f"Failed to convert rule detail {rule_detail.id}: {e}")
        # 直接抛出异常，不再降级处理
        raise ServiceError(
            message=f"Rule detail conversion failed: {str(e)}",
            error_code="RULE_CONVERSION_FAILED",
            details={"rule_detail_id": rule_detail.id, "error": str(e)},
        ) from e


def _batch_convert_rule_details_to_cache_format(rule_details: list[RuleDetail]) -> list[dict]:
    """
    批量转换RuleDetail对象为缓存格式
    使用UnifiedDataMappingEngine的批量处理功能提升性能

    Args:
        rule_details: RuleDetail对象列表

    Returns:
        list[dict]: 转换后的规则数据列表
    """
    if not rule_details:
        return []

    try:
        # 使用UnifiedDataMappingEngine进行批量处理
        unified_mapping_engine = UnifiedDataMappingEngine()

        # 将RuleDetail对象转换为字典列表
        detail_dicts = [detail.to_dict() for detail in rule_details]

        # 批量字段名称标准化
        normalized_data_list = unified_mapping_engine.batch_normalize_field_names(detail_dicts)

        # 批量验证数据
        validation_results = unified_mapping_engine.batch_validate_data(normalized_data_list)

        # 处理验证结果
        valid_data = []
        for i, (data, validation) in enumerate(zip(normalized_data_list, validation_results, strict=False)):
            if not validation.valid:
                logger.warning(f"Rule detail {rule_details[i].id} validation failed: {validation.errors}")

            # 处理扩展字段的JSON序列化
            try:
                structured_data = unified_mapping_engine.convert_to_structured_format(
                    data, rule_key=rule_details[i].rule_key
                )

                # 确保 rule_id 字段存在
                if "rule_id" not in structured_data and rule_details[i].id:
                    structured_data["rule_id"] = str(rule_details[i].id)

                valid_data.append(structured_data)

            except Exception as e:
                logger.error(f"Failed to convert rule detail {rule_details[i].id}: {e}")
                continue

        logger.debug(
            f"Batch converted {len(valid_data)}/{len(rule_details)} rule details using UnifiedDataMappingEngine"
        )

        return valid_data

    except Exception as e:
        logger.error(f"Batch conversion failed: {e}")
        # 降级处理：逐个转换
        logger.warning("Falling back to individual conversion in batch function")
        converted_data = []
        for detail in rule_details:
            try:
                converted_item = _convert_rule_detail_to_cache_format(detail)
                if converted_item:
                    converted_data.append(converted_item)
            except Exception as e:
                logger.warning(f"Failed to convert rule detail {detail.id}: {e}")
                continue

        return converted_data


def _validate_field_mapping_configuration() -> bool:
    """
    验证field_mapping.json配置的完整性和正确性

    Returns:
        bool: 配置是否有效
    """
    try:
        unified_mapping_engine = UnifiedDataMappingEngine()

        # 获取引擎状态信息
        engine_info = unified_mapping_engine.get_engine_info()

        # 检查配置文件是否加载成功
        if not engine_info.get("field_manager_info", {}).get("config_loaded", False):
            logger.error("Field mapping configuration not loaded")
            return False

        # 检查必要的字段定义
        required_fields = ["rule_id", "rule_name", "level1", "level2", "level3", "error_reason"]
        field_definitions = engine_info.get("field_manager_info", {}).get("field_definitions", {})

        missing_fields = []
        for field in required_fields:
            if field not in field_definitions.get("common_fields", {}):
                missing_fields.append(field)

        if missing_fields:
            logger.error(f"Missing required field definitions in field_mapping.json: {missing_fields}")
            return False

        # 检查配置版本
        config_version = engine_info.get("field_manager_info", {}).get("config_version", "unknown")
        logger.info(f"Field mapping configuration validated successfully, version: {config_version}")

        return True

    except Exception as e:
        logger.error(f"Failed to validate field mapping configuration: {e}")
        return False


def _check_cache_freshness() -> dict:
    """
    检查缓存文件的新鲜度和版本信息
    实现智能缓存机制，避免重复加载相同数据

    Returns:
        dict: 缓存状态信息
    """
    cache_info = {
        "cache_exists": False,
        "version_exists": False,
        "cache_fresh": False,
        "cache_version": None,
        "cache_size_mb": 0,
        "cache_modified_time": 0,
        "should_reload": True,
        "reason": "unknown",
    }

    try:
        # 检查缓存文件是否存在
        if os.path.exists(LOCAL_RULES_PATH):
            cache_info["cache_exists"] = True

            # 获取缓存文件信息
            cache_stat = os.stat(LOCAL_RULES_PATH)
            cache_info["cache_size_mb"] = cache_stat.st_size / (1024 * 1024)
            cache_info["cache_modified_time"] = cache_stat.st_mtime

            # 检查版本文件
            if os.path.exists(LOCAL_VERSION_PATH):
                cache_info["version_exists"] = True
                with open(LOCAL_VERSION_PATH, "r") as f:
                    cache_info["cache_version"] = f.read().strip()

            # 检查是否需要重新加载
            current_time = time.perf_counter()
            time_since_check = current_time - _cache_stats["last_cache_check"]

            # 如果版本相同且最近检查过，则认为缓存新鲜
            if (
                cache_info["cache_version"] == _cache_stats["cache_version"] and time_since_check < 300
            ):  # 5分钟内不重复检查
                cache_info["cache_fresh"] = True
                cache_info["should_reload"] = False
                cache_info["reason"] = "cache_fresh"
                _cache_stats["cache_hits"] += 1
            else:
                cache_info["should_reload"] = True
                cache_info["reason"] = "version_changed_or_expired"
                _cache_stats["cache_misses"] += 1

        else:
            cache_info["reason"] = "cache_not_exists"

        logger.debug(
            f"Cache freshness check: exists={cache_info['cache_exists']}, "
            f"fresh={cache_info['cache_fresh']}, size={cache_info['cache_size_mb']:.2f}MB, "
            f"version={cache_info['cache_version']}, should_reload={cache_info['should_reload']}"
        )

        return cache_info

    except Exception as e:
        logger.error(f"Failed to check cache freshness: {e}")
        cache_info["reason"] = f"check_failed: {e}"
        return cache_info


def _detect_cache_format(cache_file_path: str) -> dict:
    """
    自动检测缓存文件格式（v1.0或v2.0）
    实现向后兼容的格式识别机制

    Args:
        cache_file_path: 缓存文件路径

    Returns:
        dict: 格式检测结果
    """
    format_info = {
        "format_version": "unknown",
        "format_type": "unknown",
        "is_gzip": False,
        "is_valid": False,
        "error_message": None,
        "file_size_mb": 0,
        "detection_confidence": 0.0,
    }

    try:
        if not os.path.exists(cache_file_path):
            format_info["error_message"] = f"Cache file not found: {cache_file_path}"
            return format_info

        # 获取文件大小
        file_size = os.path.getsize(cache_file_path)
        format_info["file_size_mb"] = file_size / (1024 * 1024)

        if file_size == 0:
            format_info["error_message"] = "Cache file is empty"
            return format_info

        # 检查文件头部判断是否为gzip格式
        with open(cache_file_path, "rb") as f:
            header = f.read(2)
            format_info["is_gzip"] = header == b"\x1f\x8b"

        # 尝试读取和解析文件内容
        try:
            if format_info["is_gzip"]:
                with gzip.open(cache_file_path, "rt", encoding="utf-8") as f:
                    data = json.load(f)
            else:
                with open(cache_file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)

            # 检测格式版本
            if isinstance(data, dict):
                if "version" in data and "templates" in data and "details" in data:
                    # v2.0格式：RuleDataSyncService新格式
                    format_info["format_version"] = "v2.0"
                    format_info["format_type"] = "sync_service"
                    format_info["detection_confidence"] = 0.95
                    format_info["is_valid"] = True

                elif "version" in data and "rule_datasets" in data:
                    # v1.5格式：增强的RuleDataSet格式
                    format_info["format_version"] = "v1.5"
                    format_info["format_type"] = "enhanced_dataset"
                    format_info["detection_confidence"] = 0.90
                    format_info["is_valid"] = True

                elif "rules" in data:
                    # v1.0格式：旧的规则格式
                    format_info["format_version"] = "v1.0"
                    format_info["format_type"] = "legacy_rules"
                    format_info["detection_confidence"] = 0.85
                    format_info["is_valid"] = True

                else:
                    # 未知字典格式
                    format_info["format_version"] = "unknown_dict"
                    format_info["format_type"] = "unknown"
                    format_info["detection_confidence"] = 0.30

            elif isinstance(data, list):
                # 直接的RuleDataSet列表格式
                format_info["format_version"] = "v1.0"
                format_info["format_type"] = "dataset_list"
                format_info["detection_confidence"] = 0.80
                format_info["is_valid"] = True

            else:
                format_info["error_message"] = f"Unexpected data type: {type(data)}"

        except json.JSONDecodeError as e:
            format_info["error_message"] = f"JSON decode error: {e}"
        except (OSError, gzip.BadGzipFile) as e:
            format_info["error_message"] = f"File read error: {e}"

        logger.debug(
            f"Cache format detection: version={format_info['format_version']}, "
            f"type={format_info['format_type']}, confidence={format_info['detection_confidence']:.2f}, "
            f"gzip={format_info['is_gzip']}, size={format_info['file_size_mb']:.2f}MB"
        )

        return format_info

    except Exception as e:
        format_info["error_message"] = f"Format detection failed: {e}"
        logger.error(f"Failed to detect cache format for {cache_file_path}: {e}")
        return format_info


def _handle_rule_loading_error(error: Exception, context: dict, operation: str) -> ServiceError:
    """
    统一的规则加载错误处理机制
    提供详细的错误上下文信息和恢复策略建议

    Args:
        error: 原始异常
        context: 错误上下文信息
        operation: 操作类型

    Returns:
        ServiceError: 统一的服务错误对象
    """
    error_details = {
        "original_error": str(error),
        "error_type": type(error).__name__,
        "operation": operation,
        "context": context,
        "timestamp": time.perf_counter(),
        "recovery_suggestions": [],
    }

    # 根据错误类型提供恢复建议
    if isinstance(error, FileNotFoundError):
        error_details["recovery_suggestions"] = [
            "检查缓存文件路径是否正确",
            "确认主节点已生成缓存文件",
            "检查文件权限设置",
            "尝试重新同步缓存文件",
        ]
        error_code = "CACHE_FILE_NOT_FOUND"

    elif isinstance(error, json.JSONDecodeError):
        error_details["recovery_suggestions"] = [
            "检查缓存文件是否损坏",
            "尝试删除缓存文件并重新同步",
            "检查文件编码格式",
            "验证JSON格式完整性",
        ]
        error_code = "CACHE_FILE_CORRUPTED"

    elif isinstance(error, gzip.BadGzipFile):
        error_details["recovery_suggestions"] = [
            "检查gzip文件是否完整",
            "尝试使用非压缩格式",
            "重新生成缓存文件",
            "检查文件传输完整性",
        ]
        error_code = "CACHE_FILE_COMPRESSION_ERROR"

    elif isinstance(error, MemoryError):
        error_details["recovery_suggestions"] = ["增加系统内存", "优化缓存文件大小", "使用流式处理", "分批加载规则数据"]
        error_code = "INSUFFICIENT_MEMORY"

    elif "database" in str(error).lower() or "sql" in str(error).lower():
        error_details["recovery_suggestions"] = [
            "检查数据库连接状态",
            "验证数据库权限",
            "检查数据库服务是否正常",
            "尝试重新连接数据库",
        ]
        error_code = "DATABASE_ERROR"

    else:
        error_details["recovery_suggestions"] = [
            "检查系统日志获取更多信息",
            "尝试重启服务",
            "联系技术支持",
            "使用降级处理机制",
        ]
        error_code = "UNKNOWN_ERROR"

    # 记录详细的错误信息
    logger.error(
        f"Rule loading error in {operation}: {error_code} - {str(error)}",
        extra={"error_details": error_details, "context": context},
    )

    # 创建ServiceError对象
    service_error = ServiceError(
        message=f"Failed to {operation}: {str(error)}", error_code=error_code, details=error_details
    )

    return service_error


def _create_error_recovery_plan(error: ServiceError, format_info: dict = None) -> dict:
    """
    创建错误恢复计划
    基于错误类型和上下文信息提供具体的恢复步骤

    Args:
        error: ServiceError对象
        format_info: 缓存格式信息

    Returns:
        dict: 恢复计划
    """
    recovery_plan = {
        "can_recover": False,
        "recovery_steps": [],
        "fallback_options": [],
        "estimated_success_rate": 0.0,
        "requires_manual_intervention": False,
    }

    error_code = error.error_code

    if error_code == "CACHE_FILE_NOT_FOUND":
        recovery_plan.update(
            {
                "can_recover": True,
                "recovery_steps": [
                    "尝试从备用缓存路径加载",
                    "使用数据库直接加载（如果是主节点）",
                    "请求主节点重新生成缓存文件",
                ],
                "fallback_options": ["使用空缓存启动（仅验证模式）", "使用最后已知的良好缓存"],
                "estimated_success_rate": 0.7,
            }
        )

    elif error_code == "CACHE_FILE_CORRUPTED":
        recovery_plan.update(
            {
                "can_recover": True,
                "recovery_steps": ["尝试使用不同的解析器", "尝试修复JSON格式", "使用降级格式处理"],
                "fallback_options": ["删除损坏的缓存文件", "使用备份缓存文件", "重新从数据库生成"],
                "estimated_success_rate": 0.6,
            }
        )

    elif error_code == "CACHE_FILE_COMPRESSION_ERROR":
        recovery_plan.update(
            {
                "can_recover": True,
                "recovery_steps": ["尝试非压缩格式读取", "使用不同的解压缩方法", "检查文件完整性"],
                "fallback_options": ["使用原始JSON格式", "重新生成压缩文件"],
                "estimated_success_rate": 0.8,
            }
        )

    elif error_code == "INSUFFICIENT_MEMORY":
        recovery_plan.update(
            {
                "can_recover": True,
                "recovery_steps": ["执行内存清理", "使用流式处理", "分批加载数据"],
                "fallback_options": ["减少缓存大小", "使用磁盘缓存"],
                "estimated_success_rate": 0.5,
                "requires_manual_intervention": True,
            }
        )

    else:
        recovery_plan.update(
            {
                "can_recover": False,
                "recovery_steps": ["联系技术支持"],
                "fallback_options": ["使用降级模式"],
                "estimated_success_rate": 0.2,
                "requires_manual_intervention": True,
            }
        )

    return recovery_plan


def _log_operation_details(operation: str, details: dict, level: str = "info"):
    """
    统一的操作日志记录
    提供详细的调试信息和性能指标

    Args:
        operation: 操作名称
        details: 操作详情
        level: 日志级别
    """
    # 构建统一的日志消息格式
    log_message = f"[RULE_LOADER] {operation}"

    # 添加关键性能指标
    if "duration" in details:
        log_message += f" | Duration: {details['duration']:.3f}s"
    if "memory_mb" in details:
        log_message += f" | Memory: {details['memory_mb']:.2f}MB"
    if "cache_size_mb" in details:
        log_message += f" | Cache: {details['cache_size_mb']:.2f}MB"
    if "success_rate" in details:
        log_message += f" | Success: {details['success_rate']:.1%}"

    # 添加操作路径信息
    execution_path = []
    if "format_version" in details:
        execution_path.append(f"format={details['format_version']}")
    if "service_type" in details:
        execution_path.append(f"service={details['service_type']}")
    if "fallback_used" in details and details["fallback_used"]:
        execution_path.append("fallback=true")

    if execution_path:
        log_message += f" | Path: {' -> '.join(execution_path)}"

    # 记录日志
    log_extra = {
        "operation": operation,
        "details": details,
        "execution_path": execution_path,
        "timestamp": time.perf_counter(),
    }

    if level == "debug":
        logger.debug(log_message, extra=log_extra)
    elif level == "info":
        logger.info(log_message, extra=log_extra)
    elif level == "warning":
        logger.warning(log_message, extra=log_extra)
    elif level == "error":
        logger.error(log_message, extra=log_extra)
    else:
        logger.info(log_message, extra=log_extra)


def _create_operation_context(operation_type: str, **kwargs) -> dict:
    """
    创建操作上下文信息
    用于错误处理和日志记录

    Args:
        operation_type: 操作类型
        **kwargs: 额外的上下文信息

    Returns:
        dict: 操作上下文
    """
    context = {
        "operation_type": operation_type,
        "timestamp": time.perf_counter(),
        "process_id": os.getpid(),
        "thread_id": None,  # 可以添加线程ID如果需要
    }

    # 添加系统信息
    try:
        process = psutil.Process()
        context.update(
            {
                "memory_usage_mb": process.memory_info().rss / 1024 / 1024,
                "cpu_percent": process.cpu_percent(),
                "open_files": len(process.open_files()) if hasattr(process, "open_files") else 0,
            }
        )
    except ImportError:
        pass

    # 添加缓存统计信息
    context.update(
        {
            "cache_hits": _cache_stats["cache_hits"],
            "cache_misses": _cache_stats["cache_misses"],
            "cache_size": len(RULE_CACHE),
            "last_cache_check": _cache_stats["last_cache_check"],
        }
    )

    # 添加自定义上下文
    context.update(kwargs)

    return context


def _update_cache_stats(load_time: float, memory_usage_mb: float, cache_version: str = None):
    """
    更新缓存性能统计信息

    Args:
        load_time: 加载时间（秒）
        memory_usage_mb: 内存使用量（MB）
        cache_version: 缓存版本
    """
    global _cache_stats

    _cache_stats["load_times"].append(load_time)
    _cache_stats["memory_usage"].append(memory_usage_mb)
    _cache_stats["last_cache_check"] = time.perf_counter()

    if cache_version:
        _cache_stats["cache_version"] = cache_version

    # 保持统计历史在合理范围内
    if len(_cache_stats["load_times"]) > 100:
        _cache_stats["load_times"] = _cache_stats["load_times"][-50:]
    if len(_cache_stats["memory_usage"]) > 100:
        _cache_stats["memory_usage"] = _cache_stats["memory_usage"][-50:]


def _get_cache_performance_report() -> dict:
    """
    生成缓存性能报告

    Returns:
        dict: 性能报告
    """
    if not _cache_stats["load_times"]:
        return {"status": "no_data", "message": "No cache performance data available"}

    load_times = _cache_stats["load_times"]
    memory_usage = _cache_stats["memory_usage"]

    report = {
        "cache_hits": _cache_stats["cache_hits"],
        "cache_misses": _cache_stats["cache_misses"],
        "hit_rate": _cache_stats["cache_hits"] / max(1, _cache_stats["cache_hits"] + _cache_stats["cache_misses"]),
        "avg_load_time": sum(load_times) / len(load_times),
        "min_load_time": min(load_times),
        "max_load_time": max(load_times),
        "avg_memory_usage": sum(memory_usage) / len(memory_usage) if memory_usage else 0,
        "current_cache_version": _cache_stats["cache_version"],
        "last_check_time": _cache_stats["last_cache_check"],
        "total_loads": len(load_times),
    }

    return report


def _clear_cache_stats():
    """
    清理缓存性能统计信息，主要用于测试环境
    """
    global _cache_stats

    _cache_stats.update(
        {
            "cache_hits": 0,
            "cache_misses": 0,
            "load_times": [],
            "memory_usage": [],
            "last_cache_check": 0,
            "cache_version": None,
            "cache_size_mb": 0,
        }
    )


def _optimize_memory_usage_enhanced():
    """
    增强的内存使用优化策略
    集成性能监控和详细统计
    """
    start_time = time.perf_counter()

    # 记录优化前的内存使用
    try:
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
    except ImportError:
        memory_before = 0

    # 强制垃圾回收
    collected = gc.collect()

    # 记录优化后的内存使用
    try:
        process = psutil.Process()
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_saved = memory_before - memory_after
        memory_percent = process.memory_percent()

        optimization_time = time.perf_counter() - start_time

        logger.info(
            f"Enhanced memory optimization completed in {optimization_time:.3f}s: "
            f"Before: {memory_before:.2f}MB, After: {memory_after:.2f}MB, "
            f"Saved: {memory_saved:.2f}MB, Usage: {memory_percent:.1f}%, "
            f"Collected: {collected} objects"
        )

        # 更新性能统计
        _cache_stats["memory_usage"].append(memory_after)

        # 内存使用警告
        if memory_after > 500:  # 超过500MB发出警告
            logger.warning(f"High memory usage detected after optimization: {memory_after:.2f}MB")

        # 集成性能监控
        if hasattr(performance_monitor, "record_memory_optimization"):
            performance_monitor.record_memory_optimization(optimization_time, memory_before, memory_after, collected)

    except ImportError:
        optimization_time = time.perf_counter() - start_time
        logger.debug(
            f"Enhanced memory optimization completed in {optimization_time:.3f}s, collected {collected} objects"
        )


def _load_rule_details_as_dict(session: Session, rule_key: str) -> list[dict]:
    """
    从 RuleDetail 表按 rule_key 加载数据并转换为规则实例化格式（适配新模型）

    Args:
        session: 数据库会话
        rule_key: 规则模板键

    Returns:
        list[dict]: 转换后的规则数据
    """
    start_time = time.perf_counter()

    try:
        # 查询指定 rule_key 的所有活跃 RuleDetail 数据
        rule_details = (
            session.query(RuleDetail)
            .filter(RuleDetail.rule_key == rule_key, RuleDetail.status == RuleDetailStatusEnum.ACTIVE)
            .all()
        )

        if not rule_details:
            logger.warning(f"No active rule details found for rule_key {rule_key}")
            return []

        # 性能优化：使用批量字段映射处理
        try:
            converted_data = _batch_convert_rule_details_to_cache_format(rule_details)
            conversion_errors = len(rule_details) - len(converted_data)
        except Exception as e:
            logger.error(f"Batch conversion failed for rule_key {rule_key}: {e}")
            # 降级处理：逐个转换
            logger.warning("Falling back to individual conversion")
            converted_data = []
            conversion_errors = 0

            for detail in rule_details:
                try:
                    converted_item = _convert_rule_detail_to_cache_format(detail)
                    if converted_item:  # 只添加成功转换的数据
                        converted_data.append(converted_item)
                except Exception as e:
                    conversion_errors += 1
                    logger.warning(f"Failed to convert rule detail {detail.id}: {e}")
                    continue

        # 性能监控
        load_time = time.perf_counter() - start_time
        logger.info(
            f"Loaded {len(converted_data)} rule details from database for rule_key {rule_key} "
            f"in {load_time:.3f}s (conversion errors: {conversion_errors})"
        )

        # 性能统计
        if load_time > 1.0:  # 如果加载时间超过1秒，记录警告
            logger.warning(f"Slow rule detail loading detected: {load_time:.3f}s for rule_key {rule_key}")

        return converted_data

    except Exception as e:
        load_time = time.perf_counter() - start_time
        logger.error(f"Failed to load rule details for rule_key {rule_key} after {load_time:.3f}s: {e}")
        return []


# 新模型不再需要这些基于 RuleDataSet 的函数，直接使用 RuleDetail 和 RuleTemplate


def load_rules_from_db(session: Session) -> dict[str, list[dict]]:
    """
    从数据库加载活跃的规则模板和明细数据

    Returns:
        dict: rule_key 到规则数据列表的映射
    """
    logger.info("Loading active rule templates and details from database...")

    # 查询所有活跃的规则模板
    active_templates = session.query(RuleTemplate).filter(RuleTemplate.status == RuleTemplateStatusEnum.READY).all()

    logger.info(f"Found {len(active_templates)} active rule templates")

    # 按规则键组织数据
    rules_by_key = {}

    for template in active_templates:
        rule_key = template.rule_key
        logger.debug(f"Loading rule details for template: {template.name} (key: {rule_key})")

        # 加载该模板下的所有规则明细
        rule_data = _load_rule_details_as_dict(session, rule_key)

        if rule_data:
            rules_by_key[rule_key] = rule_data
            logger.debug(f"Loaded {len(rule_data)} rule details for key {rule_key}")
        else:
            logger.warning(f"No rule details found for template {rule_key}")

    logger.info(f"Successfully loaded rules for {len(rules_by_key)} rule keys")
    return rules_by_key


def load_rules_into_cache(session: Session | None = None):
    """
    Main entry point for loading rules into the cache from the database.
    Can use an existing session or create a new one.

    重构版本：使用RuleDataSyncService进行数据同步，避免重复实现缓存管理功能。
    集成性能监控和内存优化。
    """
    load_start_time = time.perf_counter()
    logger.info("Starting to load rules into memory cache...")

    # 验证字段映射配置
    if not _validate_field_mapping_configuration():
        logger.warning("Field mapping configuration validation failed, but continuing with loading")
    else:
        logger.info("Field mapping configuration validated successfully")

    def _load_with_sync_service(sess: Session):
        try:
            # 使用RuleDataSyncService进行同步
            sync_service = RuleDataSyncService(session=sess, cache_file_path=LOCAL_RULES_PATH)
            stats = sync_service.sync_from_database(force_full_sync=True)

            logger.info(
                f"Finished loading rules from DB using RuleDataSyncService. "
                f"Templates: {stats.total_templates}, Details: {stats.total_details}, "
                f"Duration: {stats.sync_duration:.2f}s, Cache size: {stats.cache_size_mb:.2f}MB"
            )

        except Exception as e:
            # 使用增强的错误处理
            error_context = {
                "operation": "database_sync",
                "service_type": "RuleDataSyncService",
                "session_type": "provided" if sess else "new",
                "cache_file_path": LOCAL_RULES_PATH,
            }
            service_error = _handle_rule_loading_error(e, error_context, "sync rules from database")

            logger.error(f"Failed to load rules using RuleDataSyncService: {service_error.error_code}")
            logger.error("No fallback available. RuleDataSyncService is required for database loading.")
            raise service_error from e

    if session:
        _load_with_sync_service(session)
    else:
        session_factory = get_session_factory()
        with session_factory() as new_session:
            _load_with_sync_service(new_session)

    total_rules_in_cache = len(RULE_CACHE)

    # 计算总加载时间和性能统计
    total_load_time = time.perf_counter() - load_start_time

    # 执行内存优化
    _optimize_memory_usage_enhanced()

    # 估算内存使用
    try:
        process = psutil.Process()
        memory_usage_mb = process.memory_info().rss / 1024 / 1024
    except ImportError:
        memory_usage_mb = 0

    # 更新缓存统计信息
    _update_cache_stats(total_load_time, memory_usage_mb)

    # 生成性能报告
    perf_report = _get_cache_performance_report()

    logger.info(
        f"Finished loading rules from DB. Total rule instances in cache: {total_rules_in_cache}. "
        f"Load time: {total_load_time:.2f}s, Memory: {memory_usage_mb:.2f}MB, "
        f"Hit rate: {perf_report['hit_rate']:.2%}. "
        f"Cache keys (sample): {list(RULE_CACHE.keys())[:5] if total_rules_in_cache > 0 else '[]'}",
    )

    # 性能监控集成
    if hasattr(performance_monitor, "record_rule_loading"):
        performance_monitor.record_rule_loading(total_load_time, total_rules_in_cache, memory_usage_mb, True)


async def load_rules_from_file() -> bool:
    """
    Loads rules from the local gzipped JSON file into the cache.
    Used by the slave node.

    重构版本：使用RuleDataSyncService进行缓存加载，支持新的v2.0格式和向后兼容性。
    集成智能缓存机制和性能监控。
    """
    load_start_time = time.perf_counter()

    # 验证字段映射配置
    if not _validate_field_mapping_configuration():
        logger.warning("Field mapping configuration validation failed, but continuing with loading")
    else:
        logger.info("Field mapping configuration validated successfully")

    # 智能缓存检查
    cache_info = _check_cache_freshness()

    if cache_info["cache_fresh"] and len(RULE_CACHE) > 0:
        logger.info(
            f"Cache is fresh (version: {cache_info['cache_version']}, "
            f"size: {cache_info['cache_size_mb']:.2f}MB), skipping reload"
        )

        # 更新统计信息
        load_time = time.perf_counter() - load_start_time
        _update_cache_stats(load_time, cache_info["cache_size_mb"], cache_info["cache_version"])

        # 生成性能报告
        perf_report = _get_cache_performance_report()
        logger.info(
            f"Cache performance: hit_rate={perf_report['hit_rate']:.2%}, "
            f"avg_load_time={perf_report['avg_load_time']:.3f}s"
        )

        return True

    if not os.path.exists(LOCAL_RULES_PATH):
        error_context = {"file_path": LOCAL_RULES_PATH, "operation": "file_existence_check"}
        error = _handle_rule_loading_error(
            FileNotFoundError(f"Cache file not found: {LOCAL_RULES_PATH}"), error_context, "check cache file existence"
        )
        logger.warning(f"Local rule cache file not found at '{LOCAL_RULES_PATH}'. Error: {error.error_code}")
        return False

    # 自动检测缓存格式
    format_info = _detect_cache_format(LOCAL_RULES_PATH)

    if not format_info["is_valid"]:
        error_context = {"file_path": LOCAL_RULES_PATH, "format_info": format_info, "operation": "format_detection"}
        error = _handle_rule_loading_error(
            ValueError(f"Invalid cache format: {format_info['error_message']}"), error_context, "detect cache format"
        )
        logger.error(f"Cache format detection failed: {error.error_code}")
        return False

    logger.info(
        f"Cache format detected: version={format_info['format_version']}, "
        f"type={format_info['format_type']}, confidence={format_info['detection_confidence']:.2f}, "
        f"size={format_info['file_size_mb']:.2f}MB"
    )

    # 检查文件基本信息
    try:
        file_size = os.path.getsize(LOCAL_RULES_PATH)
        logger.info(f"Loading rules from local file: {LOCAL_RULES_PATH} (size: {file_size} bytes)")

        if file_size == 0:
            error_context = {"file_path": LOCAL_RULES_PATH, "file_size": file_size}
            error = _handle_rule_loading_error(ValueError("Cache file is empty"), error_context, "check file size")
            logger.error(f"Rule cache file is empty: {error.error_code}")
            return False

    except Exception as e:
        error_context = {"file_path": LOCAL_RULES_PATH, "operation": "file_size_check"}
        error = _handle_rule_loading_error(e, error_context, "check cache file")
        logger.error(f"Failed to check rule cache file: {error.error_code}")
        return False

    # 使用RuleDataSyncService加载缓存
    try:
        sync_service = RuleDataSyncService(cache_file_path=LOCAL_RULES_PATH)
        stats = sync_service.load_from_cache()

        # 计算总加载时间
        total_load_time = time.perf_counter() - load_start_time

        # 更新缓存统计信息
        _update_cache_stats(total_load_time, stats.cache_size_mb, cache_info.get("cache_version"))

        # 执行内存优化
        _optimize_memory_usage_enhanced()

        # 生成性能报告
        perf_report = _get_cache_performance_report()

        logger.info(
            f"Successfully loaded rules using RuleDataSyncService. "
            f"Templates: {stats.total_templates}, Details: {stats.total_details}, "
            f"Duration: {stats.sync_duration:.2f}s, Total time: {total_load_time:.2f}s, "
            f"Cache size: {stats.cache_size_mb:.2f}MB, Hit rate: {perf_report['hit_rate']:.2%}"
        )

        # 性能监控集成
        if hasattr(performance_monitor, "record_rule_loading"):
            performance_monitor.record_rule_loading(total_load_time, stats.total_details, stats.cache_size_mb, True)

        return True

    except Exception as e:
        # 使用增强的错误处理
        error_context = {
            "file_path": LOCAL_RULES_PATH,
            "format_info": format_info,
            "operation": "sync_service_loading",
            "service_type": "RuleDataSyncService",
        }
        service_error = _handle_rule_loading_error(e, error_context, "load rules using RuleDataSyncService")

        # 创建错误恢复计划
        recovery_plan = _create_error_recovery_plan(service_error, format_info)

        logger.error(
            f"Failed to load rules using RuleDataSyncService: {service_error.error_code}. "
            f"Recovery possible: {recovery_plan['can_recover']}, "
            f"Success rate: {recovery_plan['estimated_success_rate']:.1%}"
        )

        # 不再支持降级处理，直接返回失败
        logger.error("RuleDataSyncService failed and no fallback available. Only v2.0 format is supported.")
        return False
