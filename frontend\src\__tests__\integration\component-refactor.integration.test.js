/**
 * 组件重构综合集成测试
 * 验证Store层、Composables层、组件层的完整功能和性能
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { mount } from '@vue/test-utils'
import { ElMessage } from 'element-plus'

// 测试组件导入
import RuleDetailDrawer from '@/components/business/RuleDetailDrawer.vue'
import RuleDetailsCRUD from '@/views/RuleDetailsCRUD.vue'
import DataUploader from '@/views/DataUploader.vue'

// Store导入
import { useRulesStore } from '@/stores/rules'
import { useRuleDetailsStore } from '@/stores/ruleDetails'

// Composables导入
import { useRuleDetail } from '@/composables/business/useRuleDetail'
import { useRuleDetailsManagement } from '@/composables/business/useRuleDetailsManagement'

// Mock API
vi.mock('@/api/enhancedRuleDetailsApi', () => ({
  enhancedRuleDetailsApi: {
    getDetailsList: vi.fn().mockResolvedValue({
      items: [
        { id: 1, rule_name: '测试规则1', status: 'ACTIVE' },
        { id: 2, rule_name: '测试规则2', status: 'INACTIVE' }
      ],
      total: 2,
      page: 1,
      pageSize: 20
    }),
    getDetailById: vi.fn().mockResolvedValue({
      id: 1,
      rule_name: '测试规则1',
      status: 'ACTIVE'
    }),
    createDetail: vi.fn().mockResolvedValue({ id: 3, rule_name: '新规则' }),
    updateDetail: vi.fn().mockResolvedValue({ id: 1, rule_name: '更新规则' }),
    deleteDetail: vi.fn().mockResolvedValue(true),
    searchDetails: vi.fn().mockResolvedValue({
      items: [{ id: 1, rule_name: '搜索结果' }],
      total: 1
    }),
    clearCache: vi.fn().mockResolvedValue(true)
  }
}))

vi.mock('@/api/rules', () => ({
  getRuleDetail: vi.fn().mockResolvedValue({
    rule_key: 'test-rule',
    rule_name: '测试规则',
    status: 'ACTIVE'
  }),
  getRuleSchema: vi.fn().mockResolvedValue([
    { name_en: 'rule_name', name_cn: '规则名称', type: 'string', required: true }
  ])
}))

// Mock 字段映射
vi.mock('@/types/generated-fields', () => ({
  getFieldChineseName: vi.fn((field) => {
    const fieldMap = {
      'rule_name': '规则名称',
      'status': '状态',
      'level1': '一级错误'
    }
    return fieldMap[field] || field
  }),
  validateFieldValue: vi.fn(() => ({ valid: true }))
}))

// Mock 增强错误处理
vi.mock('@/utils/enhancedErrorHandler', () => ({
  enhancedErrorHandler: {
    handle: vi.fn()
  }
}))

describe('组件重构综合集成测试', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    vi.clearAllMocks()

    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => { })
    vi.spyOn(console, 'warn').mockImplementation(() => { })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Store层功能测试', () => {
    it('应该正确初始化ruleDetailsStore', () => {
      const store = useRuleDetailsStore()

      expect(store.detailsList).toEqual([])
      expect(store.selectedDetails).toEqual([])
      expect(store.pagination.page).toBe(1)
      expect(store.pagination.pageSize).toBe(20)
      expect(store.loading).toBe(false)
    })

    it('应该正确处理详情列表获取', async () => {
      const store = useRuleDetailsStore()

      await store.fetchDetailsList('test-rule')

      expect(store.detailsList).toHaveLength(2)
      expect(store.detailsList[0].rule_name).toBe('测试规则1')
      expect(store.pagination.total).toBe(2)
    })

    it('应该正确处理CRUD操作', async () => {
      const store = useRuleDetailsStore()

      // 测试创建
      const createResult = await store.createDetail('test-rule', {
        rule_name: '新规则'
      })
      expect(createResult.rule_name).toBe('新规则')

      // 测试更新
      const updateResult = await store.updateDetail('test-rule', 1, {
        rule_name: '更新规则'
      })
      expect(updateResult.rule_name).toBe('更新规则')

      // 测试删除
      const deleteResult = await store.deleteDetail('test-rule', 1)
      expect(deleteResult).toBe(true)
    })

    it('应该正确处理搜索功能', async () => {
      const store = useRuleDetailsStore()

      await store.searchDetails('test-rule', { search: '测试' })

      expect(store.detailsList).toHaveLength(1)
      expect(store.detailsList[0].rule_name).toBe('搜索结果')
    })

    it('应该正确处理缓存清理', async () => {
      const store = useRuleDetailsStore()
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')

      await store.clearEnhancedCache()

      expect(enhancedRuleDetailsApi.clearCache).toHaveBeenCalled()
    })
  })

  describe('Composables层功能测试', () => {
    it('useRuleDetail应该正确工作', async () => {
      const ruleDetail = useRuleDetail()

      // 测试获取规则详情
      await ruleDetail.fetchRuleDetail('test-rule')

      expect(ruleDetail.currentRule.value).toBeDefined()
      expect(ruleDetail.currentRule.value.rule_name).toBe('测试规则')
    })

    it('useRuleDetailsManagement应该正确工作', async () => {
      const management = useRuleDetailsManagement('test-rule')

      // 测试初始化
      await management.initialize('test-rule')
      expect(management.currentRuleKey.value).toBe('test-rule')

      // 测试加载列表
      await management.loadDetailsList()
      expect(management.detailsList.value).toHaveLength(2)

      // 测试搜索
      await management.performSearch('测试')
      expect(management.detailsList.value).toHaveLength(1)
    })

    it('应该正确处理性能监控', async () => {
      const ruleDetail = useRuleDetail()

      await ruleDetail.fetchRuleDetail('test-rule')

      const stats = ruleDetail.getPerformanceStats()
      expect(stats.fetchRuleDetailTime).toBeGreaterThanOrEqual(0)
      expect(stats.lastUpdateTime).toBeTruthy()
    })
  })

  describe('组件层功能测试', () => {
    it('RuleDetailDrawer应该正确渲染和工作', async () => {
      const wrapper = mount(RuleDetailDrawer, {
        props: {
          modelValue: true,
          ruleKey: 'test-rule'
        },
        global: {
          plugins: [pinia],
          stubs: {
            'el-drawer': true,
            'el-card': true,
            'el-descriptions': true,
            'el-table': true
          }
        }
      })

      expect(wrapper.exists()).toBe(true)

      // 测试props传递
      expect(wrapper.props('ruleKey')).toBe('test-rule')
      expect(wrapper.props('modelValue')).toBe(true)
    })

    it('应该正确处理事件传递', async () => {
      const wrapper = mount(RuleDetailDrawer, {
        props: {
          modelValue: true,
          ruleKey: 'test-rule'
        },
        global: {
          plugins: [pinia],
          stubs: {
            'el-drawer': true,
            'el-card': true,
            'el-descriptions': true,
            'el-table': true
          }
        }
      })

      // 模拟事件触发
      await wrapper.vm.$emit('update:modelValue', false)

      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')[0]).toEqual([false])
    })
  })

  describe('性能基准测试', () => {
    it('应该在合理时间内完成API调用', async () => {
      const store = useRuleDetailsStore()

      const startTime = performance.now()
      await store.fetchDetailsList('test-rule')
      const endTime = performance.now()

      const duration = endTime - startTime
      expect(duration).toBeLessThan(100) // 应该在100ms内完成
    })

    it('应该正确缓存数据', async () => {
      const store = useRuleDetailsStore()
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')

      // 第一次调用
      await store.fetchDetailsList('test-rule')
      expect(enhancedRuleDetailsApi.getDetailsList).toHaveBeenCalledTimes(1)

      // 第二次调用应该使用缓存
      await store.fetchDetailsList('test-rule')
      expect(enhancedRuleDetailsApi.getDetailsList).toHaveBeenCalledTimes(2) // Mock不会真正缓存
    })

    it('应该正确处理大量数据', async () => {
      const store = useRuleDetailsStore()

      // Mock大量数据
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')
      enhancedRuleDetailsApi.getDetailsList.mockResolvedValueOnce({
        items: Array.from({ length: 1000 }, (_, i) => ({
          id: i + 1,
          rule_name: `规则${i + 1}`,
          status: 'ACTIVE'
        })),
        total: 1000
      })

      const startTime = performance.now()
      await store.fetchDetailsList('test-rule')
      const endTime = performance.now()

      expect(store.detailsList).toHaveLength(1000)
      expect(endTime - startTime).toBeLessThan(200) // 大量数据处理应在200ms内
    })
  })

  describe('错误处理测试', () => {
    it('应该正确处理API错误', async () => {
      const store = useRuleDetailsStore()
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')

      // Mock API错误
      enhancedRuleDetailsApi.getDetailsList.mockRejectedValueOnce(
        new Error('网络错误')
      )

      await expect(store.fetchDetailsList('test-rule')).rejects.toThrow('网络错误')
    })

    it('应该正确处理组件错误', async () => {
      const { enhancedErrorHandler } = await import('@/utils/enhancedErrorHandler')

      const ruleDetail = useRuleDetail()

      // 模拟错误情况
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')
      enhancedRuleDetailsApi.getDetailsList.mockRejectedValueOnce(
        new Error('测试错误')
      )

      try {
        await ruleDetail.fetchRuleDetailsList('test-rule')
      } catch (error) {
        // 错误应该被处理
      }

      expect(enhancedErrorHandler.handleError).toHaveBeenCalled()
    })
  })
})
