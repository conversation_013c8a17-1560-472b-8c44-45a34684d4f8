"""
错误消息模板定义
提供用户友好的错误消息，支持国际化和参数化
"""

from .error_codes import ErrorCodes


class ErrorMessages:
    """
    错误消息模板类
    提供标准化的用户友好错误消息
    """

    # 成功消息
    SUCCESS_MESSAGES = {
        ErrorCodes.SUCCESS: "操作成功",
    }

    # 客户端错误消息
    CLIENT_ERROR_MESSAGES = {
        ErrorCodes.BAD_REQUEST: "请求参数错误，请检查输入数据",
        ErrorCodes.UNAUTHORIZED: "未授权访问，请检查API密钥",
        ErrorCodes.FORBIDDEN: "禁止访问，权限不足",
        ErrorCodes.NOT_FOUND: "请求的资源不存在",
        ErrorCodes.METHOD_NOT_ALLOWED: "请求方法不被允许",
        ErrorCodes.REQUEST_TIMEOUT: "请求超时，请稍后重试",
        ErrorCodes.CONFLICT: "资源冲突，请检查数据状态",
        ErrorCodes.VALIDATION_ERROR: "数据验证失败，请检查输入格式",
        ErrorCodes.TOO_MANY_REQUESTS: "请求过于频繁，请稍后重试",
    }

    # 服务器错误消息
    SERVER_ERROR_MESSAGES = {
        ErrorCodes.INTERNAL_ERROR: "服务器内部错误，请联系管理员",
        ErrorCodes.NOT_IMPLEMENTED: "功能暂未实现",
        ErrorCodes.BAD_GATEWAY: "网关错误，请稍后重试",
        ErrorCodes.SERVICE_UNAVAILABLE: "服务暂时不可用，请稍后重试",
        ErrorCodes.GATEWAY_TIMEOUT: "网关超时，请稍后重试",
    }

    # 业务错误消息
    BUSINESS_ERROR_MESSAGES = {
        ErrorCodes.RULE_VALIDATION_FAILED: "规则验证失败",
        ErrorCodes.RULE_SYNC_FAILED: "规则同步失败，请检查网络连接",
        ErrorCodes.RULE_NOT_FOUND: "指定的规则不存在",
        ErrorCodes.RULE_EXECUTION_ERROR: "规则执行出错，请联系管理员",
        ErrorCodes.RULE_CACHE_ERROR: "规则缓存错误，请稍后重试",
    }

    # 数据错误消息
    DATA_ERROR_MESSAGES = {
        ErrorCodes.DATA_VALIDATION_ERROR: "数据验证失败，请检查数据格式",
        ErrorCodes.DATA_FORMAT_ERROR: "数据格式错误，请使用正确的格式",
        ErrorCodes.DATA_MISSING_ERROR: "缺少必要的数据字段",
        ErrorCodes.DATA_DUPLICATE_ERROR: "数据重复，请检查输入",
        ErrorCodes.DATA_PROCESSING_ERROR: "数据处理失败，请联系管理员",
    }

    # 系统错误消息
    SYSTEM_ERROR_MESSAGES = {
        ErrorCodes.DATABASE_ERROR: "数据库错误，请联系管理员",
        ErrorCodes.CACHE_ERROR: "缓存系统错误，请稍后重试",
        ErrorCodes.NETWORK_ERROR: "网络连接错误，请检查网络设置",
        ErrorCodes.FILE_SYSTEM_ERROR: "文件系统错误，请联系管理员",
        ErrorCodes.MEMORY_ERROR: "内存不足，请联系管理员",
        ErrorCodes.CPU_ERROR: "CPU资源不足，请稍后重试",
    }

    # 认证授权错误消息
    AUTH_ERROR_MESSAGES = {
        ErrorCodes.API_KEY_MISSING: "缺少API密钥，请在请求头中提供X-API-KEY",
        ErrorCodes.API_KEY_INVALID: "API密钥无效，请检查密钥是否正确",
        ErrorCodes.API_KEY_EXPIRED: "API密钥已过期，请更新密钥",
        ErrorCodes.PERMISSION_DENIED: "权限不足，无法执行此操作",
        ErrorCodes.SESSION_EXPIRED: "会话已过期，请重新登录",
    }

    # 默认错误消息
    DEFAULT_ERROR_MESSAGE = "系统错误，请联系管理员"

    @classmethod
    def get_message(cls, error_code: int, **kwargs) -> str:
        """
        根据错误码获取用户友好的错误消息

        Args:
            error_code: 错误码
            **kwargs: 消息参数（用于参数化消息）

        Returns:
            str: 用户友好的错误消息
        """
        # 合并所有消息字典
        all_messages = {
            **cls.SUCCESS_MESSAGES,
            **cls.CLIENT_ERROR_MESSAGES,
            **cls.SERVER_ERROR_MESSAGES,
            **cls.BUSINESS_ERROR_MESSAGES,
            **cls.DATA_ERROR_MESSAGES,
            **cls.SYSTEM_ERROR_MESSAGES,
            **cls.AUTH_ERROR_MESSAGES,
        }

        # 获取消息模板
        message_template = all_messages.get(error_code, cls.DEFAULT_ERROR_MESSAGE)

        # 如果有参数，进行格式化
        if kwargs:
            try:
                return message_template.format(**kwargs)
            except (KeyError, ValueError):
                # 如果格式化失败，返回原始消息
                return message_template

        return message_template

    @classmethod
    def get_detailed_message(cls, error_code: int, detail: str | None = None, **kwargs) -> str:
        """
        获取详细的错误消息

        Args:
            error_code: 错误码
            detail: 详细信息
            **kwargs: 消息参数

        Returns:
            str: 详细的错误消息
        """
        base_message = cls.get_message(error_code, **kwargs)

        if detail:
            return f"{base_message}: {detail}"

        return base_message


class MessageFormatter:
    """消息格式化工具"""

    @staticmethod
    def format_validation_error(field_errors: dict[str, str]) -> str:
        """
        格式化验证错误消息

        Args:
            field_errors: 字段错误字典 {字段名: 错误信息}

        Returns:
            str: 格式化后的错误消息
        """
        if not field_errors:
            return "数据验证失败"

        error_parts = []
        for field, error in field_errors.items():
            error_parts.append(f"字段 '{field}': {error}")

        return "数据验证失败: " + "; ".join(error_parts)

    @staticmethod
    def format_rule_error(rule_id: str, error_detail: str) -> str:
        """
        格式化规则错误消息

        Args:
            rule_id: 规则ID
            error_detail: 错误详情

        Returns:
            str: 格式化后的错误消息
        """
        return f"规则 '{rule_id}' 执行失败: {error_detail}"

    @staticmethod
    def format_sync_error(sync_type: str, error_detail: str) -> str:
        """
        格式化同步错误消息

        Args:
            sync_type: 同步类型（如：规则同步、数据同步）
            error_detail: 错误详情

        Returns:
            str: 格式化后的错误消息
        """
        return f"{sync_type}失败: {error_detail}"


# 国际化支持（预留）
class I18nErrorMessages:
    """
    国际化错误消息支持
    目前仅支持中文，未来可扩展支持多语言
    """

    SUPPORTED_LANGUAGES = ["zh-CN"]
    DEFAULT_LANGUAGE = "zh-CN"

    @classmethod
    def get_message(cls, error_code: int, language: str = "zh-CN", **kwargs) -> str:
        """
        获取指定语言的错误消息

        Args:
            error_code: 错误码
            language: 语言代码
            **kwargs: 消息参数

        Returns:
            str: 指定语言的错误消息
        """
        # 目前仅支持中文，直接使用ErrorMessages
        return ErrorMessages.get_message(error_code, **kwargs)
