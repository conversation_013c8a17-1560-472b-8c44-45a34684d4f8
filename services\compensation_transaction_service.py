"""
补偿事务处理服务
负责处理规则注册失败后的补偿操作，包括数据回滚和重新同步
"""

from datetime import datetime
from enum import Enum
from typing import Any

from sqlalchemy.exc import SQLAlchemyError

from core.db_session import get_session_factory
from core.logging.logging_system import log as logger
from models.database import RuleDetail, RuleTemplate
from services.data_mapping_engine import DataMappingEngine
from services.difference_analyzer import DifferenceAnalyzer
from services.rule_registration_service import RuleRegistrationService


class CompensationStrategy(Enum):
    """补偿策略枚举"""

    ROLLBACK = "rollback"  # 回滚本地数据
    RETRY = "retry"  # 重新尝试外部注册
    MANUAL = "manual"  # 手动处理


class CompensationResult:
    """补偿操作结果"""

    def __init__(self, success: bool, strategy: CompensationStrategy, message: str, details: dict | None = None):
        self.success = success
        self.strategy = strategy
        self.message = message
        self.details = details or {}
        self.timestamp = datetime.now()


class CompensationTransactionService:
    """
    补偿事务处理服务

    负责处理规则注册失败后的补偿操作，支持数据回滚和重新同步两种策略。
    """

    def __init__(self):
        """初始化补偿事务处理服务"""
        self.session_factory = get_session_factory()
        self.registration_service = None
        self.mapping_engine = None
        self.difference_analyzer = None

        # 统计信息
        self.stats = {
            "total_compensations": 0,
            "successful_compensations": 0,
            "failed_compensations": 0,
            "rollback_count": 0,
            "retry_count": 0,
            "manual_count": 0,
        }

        logger.info("CompensationTransactionService初始化完成")

    async def initialize_dependencies(self):
        """初始化依赖服务"""
        try:
            self.registration_service = RuleRegistrationService()
            self.mapping_engine = DataMappingEngine()
            self.difference_analyzer = DifferenceAnalyzer()

            logger.info("补偿事务服务依赖初始化完成")
        except Exception as e:
            logger.error(f"补偿事务服务依赖初始化失败: {e}")
            raise

    async def process_compensation(
        self, task_id: str, task_info: dict[str, Any], strategy: CompensationStrategy = CompensationStrategy.RETRY
    ) -> CompensationResult:
        """
        处理补偿操作

        Args:
            task_id: 任务ID
            task_info: 任务信息
            strategy: 补偿策略

        Returns:
            CompensationResult: 补偿操作结果
        """
        try:
            self.stats["total_compensations"] += 1

            logger.info(f"开始处理任务 {task_id} 的补偿操作，策略: {strategy.value}")

            if strategy == CompensationStrategy.ROLLBACK:
                result = await self._rollback_local_data(task_id, task_info)
            elif strategy == CompensationStrategy.RETRY:
                result = await self._retry_external_registration(task_id, task_info)
            elif strategy == CompensationStrategy.MANUAL:
                result = await self._mark_manual_processing(task_id, task_info)
            else:
                raise ValueError(f"不支持的补偿策略: {strategy}")

            # 更新统计信息
            if result.success:
                self.stats["successful_compensations"] += 1
                if strategy == CompensationStrategy.ROLLBACK:
                    self.stats["rollback_count"] += 1
                elif strategy == CompensationStrategy.RETRY:
                    self.stats["retry_count"] += 1
                elif strategy == CompensationStrategy.MANUAL:
                    self.stats["manual_count"] += 1
            else:
                self.stats["failed_compensations"] += 1

            logger.info(f"任务 {task_id} 补偿操作完成，结果: {result.success}, 消息: {result.message}")
            return result

        except Exception as e:
            self.stats["failed_compensations"] += 1
            error_msg = f"处理补偿操作失败: {e}"
            logger.error(f"任务 {task_id} {error_msg}", exc_info=True)
            return CompensationResult(False, strategy, error_msg, {"error": str(e)})

    async def _rollback_local_data(self, task_id: str, task_info: dict[str, Any]) -> CompensationResult:
        """
        回滚本地数据

        Args:
            task_id: 任务ID
            task_info: 任务信息

        Returns:
            CompensationResult: 回滚操作结果
        """
        try:
            rule_key = task_info.get("rule_key")
            if not rule_key:
                return CompensationResult(False, CompensationStrategy.ROLLBACK, "缺少rule_key信息，无法执行回滚")

            # 获取事务步骤信息，确定回滚范围
            transaction_steps = task_info.get("transaction_steps", {})
            database_save_completed = transaction_steps.get("database_save", {}).get("status") == "completed"

            if not database_save_completed:
                return CompensationResult(True, CompensationStrategy.ROLLBACK, "数据库保存未完成，无需回滚")

            # 执行数据回滚
            with self.session_factory() as session:
                # 先查找RuleTemplate
                rule_template = session.query(RuleTemplate).filter_by(rule_key=rule_key).first()
                if not rule_template:
                    return CompensationResult(False, CompensationStrategy.ROLLBACK, f"未找到规则 {rule_key} 的模板信息")

                # 查找相关的数据集记录
                rule_datasets = session.query(RuleDetail).filter_by(rule_key=rule_key).all()

                if not rule_datasets:
                    return CompensationResult(
                        True, CompensationStrategy.ROLLBACK, f"未找到规则 {rule_key} 的数据集，可能已被清理"
                    )

                # 删除相关数据集（回滚到注册前状态）
                deleted_count = 0
                for dataset in rule_datasets:
                    session.delete(dataset)
                    deleted_count += 1

                session.commit()

                logger.info(f"任务 {task_id} 回滚完成，删除了 {deleted_count} 个数据集记录")

                return CompensationResult(
                    True,
                    CompensationStrategy.ROLLBACK,
                    f"成功回滚本地数据，删除了 {deleted_count} 个数据集记录",
                    {"deleted_count": deleted_count, "rule_key": rule_key},
                )

        except SQLAlchemyError as e:
            error_msg = f"数据库回滚操作失败: {e}"
            logger.error(f"任务 {task_id} {error_msg}")
            return CompensationResult(False, CompensationStrategy.ROLLBACK, error_msg)
        except Exception as e:
            error_msg = f"回滚操作异常: {e}"
            logger.error(f"任务 {task_id} {error_msg}", exc_info=True)
            return CompensationResult(False, CompensationStrategy.ROLLBACK, error_msg)

    async def _retry_external_registration(self, task_id: str, task_info: dict[str, Any]) -> CompensationResult:
        """
        重新尝试外部注册

        Args:
            task_id: 任务ID
            task_info: 任务信息

        Returns:
            CompensationResult: 重试操作结果
        """
        try:
            if not self.registration_service:
                await self.initialize_dependencies()

            rule_key = task_info.get("rule_key")
            if not rule_key:
                return CompensationResult(False, CompensationStrategy.RETRY, "缺少rule_key信息，无法执行重试")

            # 从数据库重新获取数据并进行注册
            with self.session_factory() as session:
                # 先查找BaseRule
                from models.database import BaseRule

                base_rule = session.query(BaseRule).filter_by(rule_key=rule_key).first()

                if not base_rule:
                    return CompensationResult(False, CompensationStrategy.RETRY, f"未找到规则 {rule_key} 的模板信息")

                rule_datasets = session.query(RuleDetail).filter_by(rule_key=rule_key).all()

                if not rule_datasets:
                    return CompensationResult(False, CompensationStrategy.RETRY, f"未找到规则 {rule_key} 的活跃数据集")

                # 准备注册数据
                excel_data = []
                for dataset in rule_datasets:
                    if dataset.data_set:
                        excel_data.extend(dataset.data_set)

                if not excel_data:
                    return CompensationResult(True, CompensationStrategy.RETRY, "数据集为空，无需重新注册")

                # BaseRule信息已经在上面获取了，直接使用

                # 进行数据映射和差异分析
                mapped_data = self.mapping_engine.map_to_registration_format(excel_data, base_rule, "UPSERT")
                diff_result = self.difference_analyzer.analyze_data_diff(rule_key, mapped_data, session)

                delete_operations = diff_result["delete_operations"]
                upsert_operations = diff_result["upsert_operations"]

                # 执行注册操作
                successful_ops = 0
                failed_ops = 0

                if delete_operations:
                    try:
                        delete_result = await self.registration_service.register_rules(delete_operations)
                        if delete_result.get("success"):
                            successful_ops += len(delete_operations)
                        else:
                            failed_ops += len(delete_operations)
                    except Exception as e:
                        failed_ops += len(delete_operations)
                        logger.error(f"任务 {task_id} 重试DELETE操作失败: {e}")

                if upsert_operations:
                    try:
                        upsert_result = await self.registration_service.register_rules(upsert_operations)
                        if upsert_result.get("success"):
                            successful_ops += len(upsert_operations)
                        else:
                            failed_ops += len(upsert_operations)
                    except Exception as e:
                        failed_ops += len(upsert_operations)
                        logger.error(f"任务 {task_id} 重试UPSERT操作失败: {e}")

                # 判断重试结果
                if failed_ops == 0:
                    return CompensationResult(
                        True,
                        CompensationStrategy.RETRY,
                        f"重新注册成功，处理了 {successful_ops} 个操作",
                        {
                            "successful_operations": successful_ops,
                            "delete_count": len(delete_operations),
                            "upsert_count": len(upsert_operations),
                        },
                    )
                else:
                    return CompensationResult(
                        False,
                        CompensationStrategy.RETRY,
                        f"重新注册部分失败，成功: {successful_ops}, 失败: {failed_ops}",
                        {"successful_operations": successful_ops, "failed_operations": failed_ops},
                    )

        except Exception as e:
            error_msg = f"重试外部注册异常: {e}"
            logger.error(f"任务 {task_id} {error_msg}", exc_info=True)
            return CompensationResult(False, CompensationStrategy.RETRY, error_msg)

    async def _mark_manual_processing(self, task_id: str, task_info: dict[str, Any]) -> CompensationResult:
        """
        标记为手动处理

        Args:
            task_id: 任务ID
            task_info: 任务信息

        Returns:
            CompensationResult: 标记操作结果
        """
        try:
            rule_key = task_info.get("rule_key", "unknown")

            # 记录手动处理信息
            manual_info = {
                "task_id": task_id,
                "rule_key": rule_key,
                "marked_at": datetime.now().isoformat(),
                "reason": "自动补偿失败，需要手动处理",
                "transaction_steps": task_info.get("transaction_steps", {}),
            }

            logger.warning(f"任务 {task_id} 已标记为手动处理: {manual_info}")

            return CompensationResult(
                True, CompensationStrategy.MANUAL, "已标记为手动处理，请运维人员介入", manual_info
            )

        except Exception as e:
            error_msg = f"标记手动处理异常: {e}"
            logger.error(f"任务 {task_id} {error_msg}", exc_info=True)
            return CompensationResult(False, CompensationStrategy.MANUAL, error_msg)

    def get_stats(self) -> dict[str, Any]:
        """
        获取补偿服务统计信息

        Returns:
            Dict: 统计信息
        """
        return self.stats.copy()

    async def health_check(self) -> dict[str, Any]:
        """
        健康检查

        Returns:
            Dict: 健康状态信息
        """
        try:
            # 检查依赖服务
            if not self.registration_service:
                await self.initialize_dependencies()

            # 检查注册服务健康状态
            registration_health = await self.registration_service.health_check()

            return {
                "healthy": True,
                "service": "CompensationTransactionService",
                "dependencies": {
                    "registration_service": registration_health.get("healthy", False),
                    "mapping_engine": self.mapping_engine is not None,
                    "difference_analyzer": self.difference_analyzer is not None,
                },
                "stats": self.get_stats(),
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"补偿服务健康检查失败: {e}")
            return {
                "healthy": False,
                "service": "CompensationTransactionService",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }
