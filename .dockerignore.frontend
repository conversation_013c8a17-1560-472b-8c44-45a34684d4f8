# ===== 前端Docker构建专用 .dockerignore =====
# 优化构建上下文，只包含前端构建所需的文件

# ===== 保留前端相关文件 =====
# frontend/ 目录会被保留
# Dockerfile.frontend 会被保留

# ===== 排除后端和其他服务文件 =====
# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 后端服务文件
master.py
slave.py
*.py
requirements.txt
requirements.in
pytest.ini
ruff.toml
alembic.ini

# 后端目录
api/
core/
models/
services/
rules/
utils/
tests/
config/
alembic/

# 数据和缓存文件
data/
logs/
*.db
*.json.gz
rules_cache.json*
rules_version.txt

# 生成的模板文件
generated_templates/

# 内存银行和文档（构建时不需要）
memory-bank/
docs/

# 演示和工具文件
demo/
tools/

# ===== 排除开发和构建工具 =====
# 版本控制
.git/
.gitignore
.gitattributes

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 环境文件
.env*
!.env.example

# 日志文件
*.log
logs/

# 临时文件
tmp/
temp/
.tmp/

# ===== 排除其他Docker文件 =====
# 保留 Dockerfile.frontend，排除其他
Dockerfile.base
Dockerfile.master
Dockerfile.slave
docker-compose*.yml
!docker-compose.frontend-test.yml

# ===== 排除前端构建产物和缓存 =====
frontend/node_modules/
frontend/dist/
frontend/.cache/
frontend/.parcel-cache/
frontend/.vite/

# 前端日志和缓存
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/pnpm-debug.log*
frontend/.npm/
frontend/.eslintcache

# 前端测试和覆盖率
frontend/coverage/
frontend/.nyc_output/

# ===== 排除脚本和测试文件 =====
scripts/
!scripts/test-frontend-docker.*

# ===== 大文件和二进制文件 =====
*.tar
*.tar.gz
*.zip
*.rar
*.7z
*.dmg
*.iso

# 媒体文件（如果不需要）
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm

# ===== 保留必要的配置文件 =====
# 这些文件会被保留用于前端构建
# frontend/package.json
# frontend/package-lock.json
# frontend/vite.config.js
# frontend/tsconfig.json
# frontend/nginx.conf
# frontend/src/
# frontend/public/
# frontend/index.html
