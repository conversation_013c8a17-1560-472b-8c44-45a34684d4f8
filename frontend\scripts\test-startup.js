#!/usr/bin/env node

/**
 * 前端应用启动测试脚本
 * 用于验证应用能否正常启动和基本功能是否正常
 */

const { spawn } = require('child_process')
const http = require('http')
const path = require('path')
const fs = require('fs')

const TEST_PORT = 3000
const TEST_TIMEOUT = 30000 // 30秒超时

console.log('🚀 开始前端应用启动测试...\n')

// 测试步骤
const tests = [
  {
    name: '检查package.json文件',
    test: checkPackageJson
  },
  {
    name: '检查TypeScript配置',
    test: checkTypeScriptConfig
  },
  {
    name: '检查Vite配置',
    test: checkViteConfig
  },
  {
    name: '启动开发服务器',
    test: startDevServer
  },
  {
    name: '检查服务器响应',
    test: checkServerResponse
  },
  {
    name: '检查关键文件',
    test: checkKeyFiles
  }
]

async function runTests() {
  let passedTests = 0
  let failedTests = 0

  for (const test of tests) {
    try {
      console.log(`📋 ${test.name}...`)
      await test.test()
      console.log(`✅ ${test.name} - 通过\n`)
      passedTests++
    } catch (error) {
      console.log(`❌ ${test.name} - 失败`)
      console.log(`   错误: ${error.message}\n`)
      failedTests++
    }
  }

  console.log('📊 测试结果:')
  console.log(`   通过: ${passedTests}`)
  console.log(`   失败: ${failedTests}`)
  console.log(`   总计: ${tests.length}`)

  if (failedTests > 0) {
    console.log('\n❌ 测试失败，请检查上述错误')
    process.exit(1)
  } else {
    console.log('\n✅ 所有测试通过！应用可以正常启动')
    process.exit(0)
  }
}

// 测试函数
function checkPackageJson() {
  return new Promise((resolve, reject) => {
    const packagePath = path.join(process.cwd(), 'package.json')
    
    if (!fs.existsSync(packagePath)) {
      reject(new Error('package.json 文件不存在'))
      return
    }

    try {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
      
      if (!packageJson.scripts || !packageJson.scripts.dev) {
        reject(new Error('package.json 中缺少 dev 脚本'))
        return
      }

      if (!packageJson.dependencies || !packageJson.dependencies.vue) {
        reject(new Error('package.json 中缺少 Vue 依赖'))
        return
      }

      resolve()
    } catch (error) {
      reject(new Error(`package.json 解析失败: ${error.message}`))
    }
  })
}

function checkTypeScriptConfig() {
  return new Promise((resolve, reject) => {
    const tsconfigPath = path.join(process.cwd(), 'tsconfig.json')
    const tsconfigNodePath = path.join(process.cwd(), 'tsconfig.node.json')
    
    if (!fs.existsSync(tsconfigPath)) {
      reject(new Error('tsconfig.json 文件不存在'))
      return
    }

    if (!fs.existsSync(tsconfigNodePath)) {
      reject(new Error('tsconfig.node.json 文件不存在'))
      return
    }

    try {
      JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'))
      JSON.parse(fs.readFileSync(tsconfigNodePath, 'utf8'))
      resolve()
    } catch (error) {
      reject(new Error(`TypeScript 配置文件解析失败: ${error.message}`))
    }
  })
}

function checkViteConfig() {
  return new Promise((resolve, reject) => {
    const viteConfigPath = path.join(process.cwd(), 'vite.config.js')
    
    if (!fs.existsSync(viteConfigPath)) {
      reject(new Error('vite.config.js 文件不存在'))
      return
    }

    // 简单检查配置文件语法
    try {
      const configContent = fs.readFileSync(viteConfigPath, 'utf8')
      
      if (!configContent.includes('defineConfig')) {
        reject(new Error('vite.config.js 缺少 defineConfig'))
        return
      }

      if (!configContent.includes('@vitejs/plugin-vue')) {
        reject(new Error('vite.config.js 缺少 Vue 插件'))
        return
      }

      resolve()
    } catch (error) {
      reject(new Error(`Vite 配置检查失败: ${error.message}`))
    }
  })
}

function startDevServer() {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      if (devProcess) {
        devProcess.kill()
      }
      reject(new Error(`开发服务器启动超时 (${TEST_TIMEOUT}ms)`))
    }, TEST_TIMEOUT)

    const devProcess = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      shell: true
    })

    let output = ''

    devProcess.stdout.on('data', (data) => {
      output += data.toString()
      
      // 检查是否启动成功
      if (output.includes('ready in') && output.includes('localhost:3000')) {
        clearTimeout(timeout)
        
        // 等待一秒确保服务器完全启动
        setTimeout(() => {
          resolve(devProcess)
        }, 1000)
      }
    })

    devProcess.stderr.on('data', (data) => {
      const errorOutput = data.toString()
      
      // 如果是严重错误，立即失败
      if (errorOutput.includes('Error:') && !errorOutput.includes('TSConfckParseError')) {
        clearTimeout(timeout)
        devProcess.kill()
        reject(new Error(`开发服务器启动失败: ${errorOutput}`))
      }
    })

    devProcess.on('error', (error) => {
      clearTimeout(timeout)
      reject(new Error(`无法启动开发服务器: ${error.message}`))
    })

    // 保存进程引用以便后续清理
    global.devProcess = devProcess
  })
}

function checkServerResponse() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: TEST_PORT,
      path: '/',
      method: 'GET',
      timeout: 5000
    }

    const req = http.request(options, (res) => {
      if (res.statusCode === 200) {
        resolve()
      } else {
        reject(new Error(`服务器响应状态码: ${res.statusCode}`))
      }
    })

    req.on('error', (error) => {
      reject(new Error(`服务器请求失败: ${error.message}`))
    })

    req.on('timeout', () => {
      req.destroy()
      reject(new Error('服务器响应超时'))
    })

    req.end()
  })
}

function checkKeyFiles() {
  return new Promise((resolve, reject) => {
    const keyFiles = [
      'src/main.js',
      'src/App.vue',
      'index.html',
      'public/favicon.ico'
    ]

    for (const file of keyFiles) {
      const filePath = path.join(process.cwd(), file)
      if (!fs.existsSync(filePath)) {
        reject(new Error(`关键文件不存在: ${file}`))
        return
      }
    }

    resolve()
  })
}

// 清理函数
function cleanup() {
  if (global.devProcess) {
    console.log('\n🧹 清理开发服务器进程...')
    global.devProcess.kill()
  }
}

// 处理进程退出
process.on('SIGINT', cleanup)
process.on('SIGTERM', cleanup)
process.on('exit', cleanup)

// 运行测试
runTests().catch((error) => {
  console.error('测试运行失败:', error)
  cleanup()
  process.exit(1)
})
