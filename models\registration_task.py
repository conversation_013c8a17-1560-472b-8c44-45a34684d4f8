"""
注册任务状态数据模型
"""

from typing import Any

from sqlalchemy import JSON, Boolean, Column, DateTime, Index, Integer, Numeric, String, Text, func

from models.database import Base


class RegistrationTaskStatus(Base):
    """注册任务状态表模型"""

    __tablename__ = "registration_task_status"
    __table_args__ = (
        Index("idx_registration_task_status_rule_key", "rule_key"),
        Index("idx_registration_task_status_status", "status"),
        Index("idx_registration_task_status_created_at", "created_at"),
        Index("idx_registration_task_status_user_id", "user_id"),
        Index("idx_registration_task_status_updated_at", "updated_at"),
        {"comment": "注册任务状态表"},
    )

    # 基本信息
    task_id = Column(String(36), primary_key=True, comment="任务ID")
    task_type = Column(String(50), nullable=False, comment="任务类型")
    rule_key = Column(String(255), nullable=False, comment="规则键值")
    user_id = Column(String(255), nullable=True, comment="用户ID")

    # 状态信息
    status = Column(String(20), nullable=False, comment="任务状态")
    total_operations = Column(Integer, default=0, comment="总操作数")
    completed_operations = Column(Integer, default=0, comment="已完成操作数")
    progress_percentage = Column(Numeric(5, 2), default=0.0, comment="进度百分比")

    # 消息信息
    current_message = Column(Text, nullable=True, comment="当前消息")
    error_message = Column(Text, nullable=True, comment="错误消息")

    # 数据信息
    result_data = Column(JSON, nullable=True, comment="结果数据")
    stats = Column(JSON, nullable=True, comment="统计信息")

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    execution_time = Column(Numeric(10, 3), default=0.0, comment="执行时间(秒)")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 事务步骤跟踪字段（用于数据一致性保障）
    transaction_steps = Column(JSON, nullable=True, comment="事务步骤状态")
    compensation_needed = Column(Boolean, default=False, comment="是否需要补偿操作")
    last_step_completed = Column(String(50), nullable=True, comment="最后完成的步骤")

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "rule_key": self.rule_key,
            "user_id": self.user_id,
            "status": self.status,
            "total_operations": self.total_operations,
            "completed_operations": self.completed_operations,
            "progress_percentage": float(self.progress_percentage) if self.progress_percentage else 0.0,
            "current_message": self.current_message,
            "error_message": self.error_message,
            "result_data": self.result_data,
            "stats": self.stats or {},
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "execution_time": float(self.execution_time) if self.execution_time else 0.0,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "transaction_steps": self.transaction_steps or {},
            "compensation_needed": self.compensation_needed,
            "last_step_completed": self.last_step_completed,
        }

    def update_from_dict(self, data: dict[str, Any]):
        """从字典更新字段"""
        for key, value in data.items():
            if hasattr(self, key) and key not in ["task_id", "created_at"]:  # 不允许更新主键和创建时间
                setattr(self, key, value)

    def __repr__(self):
        return f"<RegistrationTaskStatus(task_id='{self.task_id}', status='{self.status}', rule_key='{self.rule_key}')>"
