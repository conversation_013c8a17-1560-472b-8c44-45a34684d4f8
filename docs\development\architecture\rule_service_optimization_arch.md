# RuleService 架构优化文档

## 1. 背景与原始问题

`RuleService` 的核心职责是高效地并行执行大量规则校验任务。为了利用多核 CPU 的处理能力，我们采用了基于 `multiprocessing` 的多进程架构。

然而，最初的实现面临一个典型问题：**子进程无法访问主进程的内存数据**。

具体来说：

*   规则注册表（一个包含所有已加载规则实例的字典）在主进程中被创建和填充。
*   当任务被分发到由 `multiprocessing.Pool` 创建的子进程时，这些子进程拥有独立的内存空间。它们无法直接访问主进程中的规则注册表。
*   如果尝试在任务函数中传递规则对象，会导致每次调用都需要对规则对象进行序列化（pickle）和反序列化，这会带来巨大的性能开销，尤其是在规则对象复杂或数量庞大时。

因此，我们需要一个高效的机制，在不牺牲性能的前提下，让所有子进程都能访问到完整的规则信息。

## 2. 解决方案选型

经过分析，我们选定了利用 `multiprocessing.Pool` 的 `initializer` 功能作为最终解决方案。

**为什么选择 `initializer`？**

`initializer` 是 `multiprocessing.Pool` 构造函数的一个参数。它允许我们指定一个函数，该函数会在每个工作进程（子进程）启动时、开始接收任务之前被执行一次。

这种模式的优势在于：

*   **一次性数据传递**: 我们可以将主进程加载的完整规则注册表，通过 `initargs` 参数一次性地传递给每个子进程的初始化函数。
*   **进程内缓存**: 在初始化函数中，子进程可以将接收到的数据缓存在其全局变量中。由于每个子进程有自己独立的内存空间，这个全局变量对该进程内的所有后续任务都是可见的。
*   **高效的任务执行**: 当子进程执行实际任务时，它可以直接从自己的内存缓存中读取规则数据，完全避免了跨进程通信或重复数据传输的开销。
*   **优雅且简洁**: 该方案利用了 `multiprocessing` 模块的原生特性，代码实现清晰，易于理解和维护。

相比于其他方案（如使用 `Manager` 或在每个任务中传递数据），`initializer` 模式在我们的“读多写少”场景下（规则加载一次，被频繁读取）是性能最高、侵入性最小的选择。

## 3. 实现细节

为了实现上述方案，我们对代码结构进行了如下调整：

### 3.1. 新增 `services/worker_init.py`

我们创建了一个新模块 [`services/worker_init.py`](../services/worker_init.py:1) 来专门处理子进程的初始化和任务执行逻辑。

*   **`init_worker(registry)` 函数**:
    *   此函数被指定为进程池的 `initializer`。
    *   它接收主进程传来的规则字典 `registry`。
    *   它将这个字典赋值给一个模块级的全局变量 `worker_rule_registry`，从而在子进程的内存中缓存了所有规则。

*   **`execute_rule_in_worker(rule_id, data)` 函数**:
    *   这是在子进程中被 `starmap` 调用的实际工作函数。
    *   它直接从 `worker_rule_registry` 缓存中通过 `rule_id` 获取规则实例。
    *   获取到规则实例后，调用其 `validate` 方法执行校验。

### 3.2. 重构 `services/rule_service.py`

[`RuleService`](../services/rule_service.py:1) 的初始化逻辑被重构以支持新架构。关键代码如下:

```python
# services/rule_service.py
import asyncio
from concurrent.futures import ThreadPoolExecutor
from functools import partial
from itertools import repeat
from multiprocessing import Pool, cpu_count

from rules.rule_registry import rule_registry
from services.worker_init import execute_rule_in_worker, init_worker
from models import PatientData

class RuleService:
    def __init__(self):
        """
        初始化规则服务。
        - 自动发现并加载所有规则。
        - 创建一个多进程池，并使用规则注册表对其进行初始化，
          以便所有工作进程都可以访问所有规则。
        """
        rule_count = rule_registry.auto_discover_rules()
        
        # 将主进程的规则注册表传递给所有工作进程
        # 这是通过 initializer 和 initargs 实现的
        initargs = (rule_registry._rules,)
        self.pool = Pool(initializer=init_worker, initargs=initargs)
        
        # 用于在异步上下文中运行阻塞的 pool.starmap
        self.executor = ThreadPoolExecutor(max_workers=cpu_count())

    async def validate_rules(self, patient_data: PatientData, rule_ids: list[str]):
        """
        使用预先初始化的多进程池并行验证多个规则。
        """
        if not rule_ids:
            return []
        
        loop = asyncio.get_running_loop()
        tasks = zip(rule_ids, repeat(patient_data))

        blocking_task = partial(
            self.pool.starmap,
            execute_rule_in_worker,
            tasks,
        )
        results = await loop.run_in_executor(self.executor, blocking_task)
        return results

    def close(self):
        self.pool.close()
        self.pool.join()
        self.executor.shutdown(wait=True)
```

在 `__init__` 方法中，创建 `Pool` 时，我们传入了 `init_worker` 作为 `initializer`，并把 `rule_registry._rules` 作为 `initargs`。在 `validate_rules` 方法中，`pool.starmap` 的目标函数指向了 `execute_rule_in_worker`。

## 4. 最终架构与数据流

优化后的架构和数据流如下：

1.  **启动阶段 (Initialization)**:
    *   `RuleService` 在主进程中启动。
    *   `rule_registry` 自动发现并加载所有规则，在主进程内存中形成一个完整的规则字典。
    *   `RuleService` 创建一个 `multiprocessing.Pool`。
    *   在创建进程池的过程中，`multiprocessing` 模块为每个工作进程调用 `init_worker` 函数。
    *   主进程将完整的规则字典序列化一次，并传递给每个 `init_worker` 函数。
    *   每个子进程收到数据后，将其存储在各自的全局变量 `worker_rule_registry` 中，完成初始化。

2.  **运行阶段 (Execution)**:
    *   外部请求调用 `RuleService.validate_rules()`，并传入患者数据和规则 ID 列表。
    *   主进程通过 `pool.starmap` 将任务（包含 `rule_id` 和 `patient_data`）分发到进程池的空闲子进程中。**注意：此时只传递轻量的 `rule_id`，而不是整个规则对象。**
    *   子进程接收到任务，调用 `execute_rule_in_worker` 函数。
    *   该函数使用 `rule_id` 在其本地缓存 `worker_rule_registry` 中进行快速查找，获得规则实例。
    *   执行规则校验，并将结果（`RuleResult` 或 `None`）返回给主进程。
    *   主进程收集所有子进程的结果，并返回给调用方。

3.  **关闭阶段 (Shutdown)**:
    *   应用程序关闭时，调用 `RuleService.close()`，优雅地关闭并清理进程池资源。

这个架构确保了数据在初始化时被高效地分发和缓存，并在运行时实现了高性能的并行处理，完美地解决了多进程架构下的数据共享问题。