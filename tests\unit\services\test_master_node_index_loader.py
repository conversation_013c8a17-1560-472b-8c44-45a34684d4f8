"""
Task 2.1: 主节点索引加载器单元测试
采用TDD方法，覆盖主节点从数据库实时构建索引的核心功能

测试覆盖：
1. 数据库查询性能优化
2. 增量更新的变更检测
3. 大数据量的内存管理
4. 并发读写场景
5. 异常处理和错误恢复

验收标准：
- 能够在30秒内完成全量索引构建
- 增量更新响应时间小于5秒
- 支持并发读写，不阻塞校验服务
"""

import asyncio
import pytest
import time
from unittest.mock import AsyncMock, MagicMock, patch

from sqlalchemy.orm import Session

from models.database import RuleDetail, RuleTemplate
from services.master_node_index_loader import MasterNodeIndexLoader
from core.rule_index_manager import IndexMetadata, rule_index_manager


class TestMasterNodeIndexLoader:
    """主节点索引加载器测试类"""

    def _setup_db_mock(self, mock_db_session, rule_details):
        """设置数据库会话模拟（增强版）"""
        mock_query = mock_db_session.query.return_value
        mock_filter = mock_query.filter.return_value
        mock_order = mock_filter.order_by.return_value
        
        # 修复数据模拟：确保 limit(1).first()返回第一条记录
        if rule_details:
            mock_order.limit.return_value.first.return_value = rule_details[0]
            mock_order.all.return_value = rule_details
            
            # 修复分页查询模拟，避免无限循环
            def mock_offset_limit(offset, limit):
                mock_paginated = MagicMock()
                start_idx = offset
                end_idx = min(offset + limit, len(rule_details))
                
                if start_idx >= len(rule_details):
                    mock_paginated.all.return_value = []
                else:
                    mock_paginated.all.return_value = rule_details[start_idx:end_idx]
                
                return mock_paginated
            
            # 修复链式调用
            mock_order.offset.side_effect = lambda offset: MagicMock(
                **{"limit.side_effect": lambda limit: mock_offset_limit(offset, limit)}
            )
        else:
            mock_order.limit.return_value.first.return_value = None
            mock_order.all.return_value = []
            mock_order.offset.return_value.limit.return_value.all.return_value = []
            
        return mock_db_session

    @pytest.fixture
    def loader(self):
        """创建索引加载器实例"""
        return MasterNodeIndexLoader()

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = MagicMock(spec=Session)
        return session

    @pytest.fixture
    def sample_rule_details(self):
        """示例规则明细数据"""
        rule_details = []
        
        # 创建包含各种代码类型的测试规则
        rule1 = MagicMock(spec=RuleDetail)
        rule1.rule_id = "RULE_001"
        rule1.rule_key = "drug_validation"
        rule1.yb_code = "A0101,A0102"  # 字符串格式，逗号分隔
        rule1.diag_whole_code = "I10,I11"  # 字符串格式，逗号分隔
        rule1.diag_code_prefix = "I1"  # 字符串格式
        rule1.fee_whole_code = "310101001"  # 字符串格式
        rule1.fee_code_prefix = "3101"  # 字符串格式
        rule1.extended_fields = '{"surgery_code": "01.01,01.02"}'
        # Fix the status attribute to avoid comparison issues
        rule1.status = "ACTIVE"
        rule_details.append(rule1)

        # 通用规则（无特定代码限制）
        rule2 = MagicMock(spec=RuleDetail)
        rule2.rule_id = "RULE_002"
        rule2.rule_key = "general_validation"
        rule2.yb_code = ""  # 空字符串表示无特定代码
        rule2.diag_whole_code = ""
        rule2.diag_code_prefix = ""
        rule2.fee_whole_code = ""
        rule2.fee_code_prefix = ""
        rule2.extended_fields = None
        rule_details.append(rule2)

        # 大量数据测试规则
        for i in range(3, 103):  # 创建100个规则用于性能测试
            rule = MagicMock(spec=RuleDetail)
            rule.rule_id = f"RULE_{i:03d}"
            rule.rule_key = "batch_validation"
            rule.yb_code = f"B{i:04d}"  # 字符串格式
            rule.diag_whole_code = f"J{i:02d}"  # 字符串格式
            rule.diag_code_prefix = f"J{i}"  # 字符串格式
            rule.fee_whole_code = f"320{i:03d}001"  # 字符串格式
            rule.fee_code_prefix = f"320{i}"  # 字符串格式
            rule.extended_fields = None
            rule_details.append(rule)

        return rule_details

    def test_init_loader_with_default_config(self, loader):
        """测试默认配置下的加载器初始化"""
        assert loader is not None
        assert loader._db_session is None
        assert loader._last_build_time == 0
        assert loader._build_in_progress is False
        assert loader._background_sync_enabled is False

    @pytest.mark.asyncio
    async def test_build_full_index_success(self, loader, mock_db_session, sample_rule_details):
        """测试全量索引构建成功场景"""
        # 设置数据库会话
        loader._db_session = self._setup_db_mock(mock_db_session, sample_rule_details)
        
        # 执行全量索引构建
        start_time = time.perf_counter()
        result = await loader.build_full_index()
        build_duration = time.perf_counter() - start_time
        
        # 验证结果
        assert result.success is True
        assert result.rule_count == len(sample_rule_details)
        assert result.build_duration_ms > 0
        assert build_duration < 30.0  # 验收标准：30秒内完成
        
        # 验证索引管理器已更新
        assert rule_index_manager.is_ready()
        assert rule_index_manager.index_metadata is not None

    @pytest.mark.asyncio
    async def test_build_full_index_performance_with_large_dataset(self, loader, mock_db_session):
        """测试大数据量索引构建性能 - 验收标准验证"""
        # 创建大量测试数据（1000条规则用于测试，避免过长运行时间）
        large_dataset = []
        for i in range(1000):  # 减少测试数据量
            rule = MagicMock(spec=RuleDetail)
            rule.rule_id = f"PERF_RULE_{i:05d}"
            rule.rule_key = "performance_test"
            rule.yb_code = f"P{i:05d}"  # 字符串格式
            rule.diag_whole_code = f"Z{i:03d}"  # 字符串格式
            rule.diag_code_prefix = f"Z{i//100}"  # 字符串格式
            rule.fee_whole_code = f"999{i:05d}"  # 字符串格式
            rule.fee_code_prefix = f"999{i//1000}"  # 字符串格式
            rule.extended_fields = None
            large_dataset.append(rule)
        
        loader._db_session = self._setup_db_mock(mock_db_session, large_dataset)
        
        # 执行性能测试
        start_time = time.perf_counter()
        result = await loader.build_full_index()
        build_duration = time.perf_counter() - start_time
        
        # 验证性能要求
        assert result.success is True
        assert build_duration < 30.0  # 验收标准：30秒内完成
        assert result.rule_count == 1000
        
        # 验证内存使用合理性
        memory_usage = result.memory_usage_mb
        assert memory_usage > 0
        # 假设1000条规则约5MB，索引不应超过5MB
        assert memory_usage < 5.0

    @pytest.mark.asyncio
    async def test_incremental_update_success(self, loader, mock_db_session):
        """测试增量更新成功场景"""
        # 先构建基础索引
        base_rules = []
        for i in range(5):
            rule = MagicMock(spec=RuleDetail)
            rule.rule_id = f"BASE_RULE_{i}"
            rule.rule_key = "base_validation"
            rule.yb_code = f"BASE{i:03d}"  # 字符串格式，不是列表
            rule.diag_whole_code = ""
            rule.diag_code_prefix = ""
            rule.fee_whole_code = ""
            rule.fee_code_prefix = ""
            rule.extended_fields = None
            base_rules.append(rule)
        
        loader._db_session = self._setup_db_mock(mock_db_session, base_rules)
        
        # 构建基础索引
        await loader.build_full_index()
        
        # 准备增量更新数据
        updated_rules = []
        new_rule = MagicMock(spec=RuleDetail)
        new_rule.rule_id = "NEW_RULE_001"
        new_rule.rule_key = "new_validation"
        new_rule.yb_code = "NEW001"  # 字符串格式，不是列表
        new_rule.diag_whole_code = "N01"  # 字符串格式，不是列表
        new_rule.diag_code_prefix = ""
        new_rule.fee_whole_code = ""
        new_rule.fee_code_prefix = ""
        new_rule.extended_fields = None
        updated_rules.append(new_rule)
        
        # 执行增量更新
        start_time = time.perf_counter()
        result = await loader.incremental_update(updated_rules)
        update_duration = time.perf_counter() - start_time
        
        # 验证结果
        assert result.success is True
        assert result.updated_count == 1
        assert update_duration < 5.0  # 验收标准：5秒内完成增量更新
        assert result.update_duration_ms > 0

    @pytest.mark.asyncio
    async def test_concurrent_read_write_safety(self, loader, mock_db_session, sample_rule_details):
        """测试并发读写安全性 - 不阻塞校验服务"""
        loader._db_session = self._setup_db_mock(mock_db_session, sample_rule_details)
        
        # 模拟并发场景：同时进行索引构建和规则查询
        async def build_index():
            return await loader.build_full_index()
        
        async def query_rules():
            # 模拟校验服务查询规则
            await asyncio.sleep(0.1)  # 模拟查询时间
            return rule_index_manager.is_ready()
        
        # 并发执行
        tasks = [
            build_index(),
            query_rules(),
            query_rules(),
            query_rules()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证结果
        build_result = results[0]
        query_results = results[1:4]
        
        assert build_result.success is True
        # 查询操作不应该被阻塞或出现异常
        for query_result in query_results:
            assert isinstance(query_result, bool)

    @pytest.mark.asyncio
    async def test_database_error_handling(self, loader, mock_db_session):
        """测试数据库错误处理"""
        loader._db_session = mock_db_session
        
        # 模拟数据库连接错误
        mock_db_session.query.side_effect = Exception("Database connection failed")
        
        # 执行索引构建
        result = await loader.build_full_index()
        
        # 验证错误处理
        assert result.success is False
        assert result.error_message is not None  # 有错误信息
        assert result.rule_count == 0

    @pytest.mark.asyncio
    async def test_partial_data_corruption_handling(self, loader, mock_db_session):
        """测试部分数据损坏的处理"""
        # 创建包含损坏数据的规则列表
        corrupted_rules = []
        
        # 正常规则
        normal_rule = MagicMock(spec=RuleDetail)
        normal_rule.rule_id = "NORMAL_001"
        normal_rule.rule_key = "normal_validation"
        normal_rule.yb_code = "N001"  # 字符串格式
        normal_rule.diag_whole_code = ""
        normal_rule.diag_code_prefix = ""
        normal_rule.fee_whole_code = ""
        normal_rule.fee_code_prefix = ""
        normal_rule.extended_fields = None
        corrupted_rules.append(normal_rule)
        
        # 损坏的规则（缺少rule_id）
        corrupted_rule = MagicMock(spec=RuleDetail)
        corrupted_rule.rule_id = None
        corrupted_rule.rule_key = "corrupted_validation"
        corrupted_rules.append(corrupted_rule)
        
        # 损坏的扩展字段JSON
        json_corrupted_rule = MagicMock(spec=RuleDetail)
        json_corrupted_rule.rule_id = "JSON_CORRUPTED_001"
        json_corrupted_rule.rule_key = "json_validation"
        json_corrupted_rule.yb_code = ""  # 字符串格式
        json_corrupted_rule.diag_whole_code = ""
        json_corrupted_rule.diag_code_prefix = ""
        json_corrupted_rule.fee_whole_code = ""
        json_corrupted_rule.fee_code_prefix = ""
        json_corrupted_rule.extended_fields = "invalid json{"
        corrupted_rules.append(json_corrupted_rule)
        
        loader._db_session = self._setup_db_mock(mock_db_session, corrupted_rules)
        
        # 执行索引构建
        result = await loader.build_full_index()
        
        # 验证部分成功处理 - 索引管理器对数据错误具有一定容错性
        assert result.success is True  # 整体成功
        assert result.rule_count == 3  # 所有规则都被处理（索引管理器容错处理）
        assert result.error_count == 0  # 索引管理器级别没有严重错误

    @pytest.mark.asyncio
    async def test_memory_usage_monitoring(self, loader, mock_db_session, sample_rule_details):
        """测试内存使用监控"""
        loader._db_session = self._setup_db_mock(mock_db_session, sample_rule_details)
        
        # 执行索引构建
        result = await loader.build_full_index()
        
        # 验证内存监控
        assert result.success is True
        assert result.memory_usage_mb > 0
        assert result.memory_efficiency_ratio > 0
        
        # 验证内存使用在合理范围内（不超过规则数据的50%）
        estimated_rule_data_size = len(sample_rule_details) * 0.5  # 估算每个规则0.5MB
        assert result.memory_usage_mb < estimated_rule_data_size * 0.5

    def test_change_detection_mechanism(self, loader, mock_db_session):
        """测试变更检测机制"""
        # 模拟规则版本信息
        with patch('services.master_node_index_loader.get_rules_version') as mock_version:
            mock_version.return_value = "v1.0.1"
            
            # 设置初始版本
            loader._last_rules_version = "v1.0.0"
            
            # 检测变更
            has_changes = loader._detect_rule_changes()
            assert has_changes is True
            
            # 更新版本后再次检测
            loader._last_rules_version = "v1.0.1"
            has_changes = loader._detect_rule_changes()
            assert has_changes is False

    @pytest.mark.asyncio
    async def test_background_sync_functionality(self, loader, mock_db_session, sample_rule_details):
        """测试后台同步功能"""
        loader._db_session = self._setup_db_mock(mock_db_session, sample_rule_details)
        
        # 启用后台同步
        loader._background_sync_enabled = True
        loader._sync_interval = 1  # 1秒间隔用于测试
        
        # 启动后台同步
        sync_task = asyncio.create_task(loader._background_sync_loop())
        
        # 等待至少一次同步
        await asyncio.sleep(1.5)
        
        # 停止后台同步
        loader._background_sync_enabled = False
        sync_task.cancel()
        
        try:
            await sync_task
        except asyncio.CancelledError:
            pass
        
        # 验证索引已构建
        assert rule_index_manager.is_ready()

    def test_rebuild_index_interface(self, loader, mock_db_session):
        """测试索引重建接口"""
        loader._db_session = mock_db_session
        
        # 模拟已存在的索引
        loader._last_build_time = time.time() - 3600  # 1小时前构建
        
        # 调用重建接口
        with patch.object(loader, 'build_full_index') as mock_build:
            mock_build.return_value = MagicMock(success=True)
            
            result = loader.rebuild_index()
            
            # 验证重建被调用
            mock_build.assert_called_once()
            assert result.success is True

    @pytest.mark.asyncio
    async def test_index_validation_after_build(self, loader, mock_db_session, sample_rule_details):
        """测试索引构建后的验证"""
        loader._db_session = self._setup_db_mock(mock_db_session, sample_rule_details)
        
        # 执行索引构建
        result = await loader.build_full_index()
        
        # 验证索引完整性
        assert result.success is True
        
        # 验证索引管理器状态
        assert rule_index_manager.is_ready()
        
        # 验证索引内容
        stats = rule_index_manager.get_performance_stats()
        assert stats["index_metadata"]["rule_count"] == len(sample_rule_details)
        assert stats["universal_rules_count"] >= 1  # 至少有一个通用规则

    def test_concurrent_update_protection(self, loader):
        """测试并发更新保护机制"""
        # 模拟正在进行的构建
        loader._build_in_progress = True
        
        # 尝试启动另一个构建操作
        with pytest.raises(RuntimeError, match="Index build already in progress"):
            asyncio.run(loader.build_full_index())
        
        # 重置状态
        loader._build_in_progress = False

    @pytest.mark.parametrize("rule_count,expected_max_time", [
        (100, 5.0),
        (1000, 15.0),
        (5000, 25.0)
    ])
    @pytest.mark.asyncio
    async def test_scalability_performance(self, loader, mock_db_session, rule_count, expected_max_time):
        """测试不同规模数据的性能表现"""
        # 生成指定数量的测试规则
        test_rules = []
        for i in range(rule_count):
            rule = MagicMock(spec=RuleDetail)
            rule.rule_id = f"SCALE_RULE_{i:05d}"
            rule.rule_key = "scale_validation"
            rule.yb_code = f"S{i:05d}"  # 字符串格式
            rule.diag_whole_code = ""
            rule.diag_code_prefix = ""
            rule.fee_whole_code = ""
            rule.fee_code_prefix = ""
            rule.extended_fields = None
            test_rules.append(rule)
        
        loader._db_session = self._setup_db_mock(mock_db_session, test_rules)
        
        # 执行性能测试
        start_time = time.perf_counter()
        result = await loader.build_full_index()
        actual_time = time.perf_counter() - start_time
        
        # 验证性能要求
        assert result.success is True
        assert actual_time < expected_max_time
        assert result.rule_count == rule_count
        
    @pytest.mark.asyncio
    async def test_concurrent_read_write_operations(self, loader, mock_db_session, sample_rule_details):
        """测试并发读写操作的线程安全性"""
        # 设置模拟数据
        self._setup_db_mock(mock_db_session, sample_rule_details)
        loader.set_database_session(mock_db_session)
        
        # 创建并发任务
        tasks = []
        
        # 10个读操作（获取状态）
        for i in range(10):
            tasks.append(asyncio.create_task(
                self._safe_get_status(loader),
                name=f"read_task_{i}"
            ))
        
        # 2个写操作
        tasks.append(asyncio.create_task(
            loader.build_full_index(),
            name="write_task_1"
        ))
        tasks.append(asyncio.create_task(
            loader.incremental_update(sample_rule_details[:5]),
            name="write_task_2"
        ))
        
        # 并发执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证结果
        read_results = results[:10]
        write_results = results[10:]
        
        # 所有读操作都应该成功
        for i, result in enumerate(read_results):
            assert not isinstance(result, Exception), f"读操作{i}失败: {result}"
            assert isinstance(result, dict), f"读操作{i}返回类型错误"
        
        # 至少一个写操作应该成功（因为只能有一个写者）
        successful_writes = 0
        for i, result in enumerate(write_results):
            if not isinstance(result, Exception):
                successful_writes += 1
        
        assert successful_writes >= 1, "至少应该有一个写操作成功"
        
    async def _safe_get_status(self, loader):
        """安全地获取状态，避免异常"""
        try:
            return loader.get_status()
        except Exception as e:
            return {"error": str(e)}
            
    @pytest.mark.asyncio
    async def test_high_concurrency_stress(self, loader, mock_db_session, sample_rule_details):
        """高并发压力测试"""
        # 设置模拟数据
        self._setup_db_mock(mock_db_session, sample_rule_details)
        loader.set_database_session(mock_db_session)
        
        # 创建大量并发任务
        tasks = []
        
        # 20个读操作
        for i in range(20):
            tasks.append(asyncio.create_task(
                self._safe_get_status(loader),
                name=f"stress_read_{i}"
            ))
        
        # 3个写操作
        for i in range(3):
            tasks.append(asyncio.create_task(
                loader.incremental_update(sample_rule_details[:5]),
                name=f"stress_write_{i}"
            ))
        
        # 记录开始时间
        start_time = time.perf_counter()
        
        # 并发执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 记录结束时间
        end_time = time.perf_counter()
        total_duration = (end_time - start_time) * 1000
        
        # 统计结果
        successful_operations = 0
        failed_operations = 0
        
        for result in results:
            if isinstance(result, Exception):
                failed_operations += 1
            else:
                successful_operations += 1
        
        # 验证性能要求
        assert total_duration < 10000, f"高并发操作耗时过长: {total_duration:.2f}ms"
        assert successful_operations > 0, "应该有成功的操作"