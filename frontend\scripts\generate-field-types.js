import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * TypeScript类型生成器
 * 基于field_mapping.json配置自动生成前端类型定义
 */
class TypeScriptGenerator {
  constructor(configPath = '../../data/field_mapping.json') {
    this.configPath = path.resolve(__dirname, configPath)
    this.config = this.loadConfig()
  }

  /**
   * 加载字段映射配置
   */
  loadConfig() {
    try {
      if (!fs.existsSync(this.configPath)) {
        throw new Error(`配置文件不存在: ${this.configPath}`)
      }

      const configContent = fs.readFileSync(this.configPath, 'utf8')
      const config = JSON.parse(configContent)

      if (!config.field_definitions) {
        throw new Error('配置文件格式错误：缺少field_definitions')
      }

      console.log(`✅ 成功加载配置文件: ${this.configPath}`)
      console.log(`📋 配置版本: ${config.metadata?.version || 'unknown'}`)

      return config
    } catch (error) {
      console.error('❌ 加载配置文件失败:', error.message)
      process.exit(1)
    }
  }

  /**
   * 生成TypeScript类型定义
   */
  generateTypes() {
    const commonFields = this.config.field_definitions?.common_fields || {}
    const specificFields = this.config.field_definitions?.specific_fields || {}

    console.log(`📊 通用字段数量: ${Object.keys(commonFields).length}`)
    console.log(`📊 特定字段数量: ${Object.keys(specificFields).length}`)

    let content = this.generateHeader()
    content += this.generateCommonFieldsInterface(commonFields)
    content += this.generateSpecificFieldsInterface(specificFields)
    content += this.generateRuleDetailInterface()
    content += this.generateFieldMappingConstants(commonFields, specificFields)
    content += this.generateUtilityFunctions()
    content += this.generateRuleTypeEnums()

    return content
  }

  /**
   * 生成文件头部注释
   */
  generateHeader() {
    const timestamp = new Date().toISOString()
    const version = this.config.metadata?.version || 'unknown'

    return `// 自动生成的字段类型定义
// 🚨 请勿手动修改此文件，运行 npm run generate:types 重新生成
// 
// 生成时间: ${timestamp}
// 配置版本: ${version}
// 数据源: data/field_mapping.json

/**
 * 规则详情表字段类型定义
 * 基于统一字段映射配置自动生成
 */

`
  }

  /**
   * 生成通用字段接口
   */
  generateCommonFieldsInterface(commonFields) {
    let content = `/**
 * 通用字段接口
 * 包含所有规则类型共有的字段
 */
export interface CommonFields {
`

    for (const [fieldName, fieldDef] of Object.entries(commonFields)) {
      const tsType = this.getTypeScriptType(fieldDef.data_type)
      const required = fieldDef.required ? '' : '?'
      const comment = `  /** ${fieldDef.chinese_name} - ${fieldDef.description || ''} */`

      content += `${comment}\n`
      content += `  ${fieldName}${required}: ${tsType}\n`
    }

    content += `}

`
    return content
  }

  /**
   * 生成特定字段接口
   */
  generateSpecificFieldsInterface(specificFields) {
    let content = `/**
 * 特定字段接口
 * 包含特定规则类型的扩展字段
 */
export interface SpecificFields {
`

    for (const [fieldName, fieldDef] of Object.entries(specificFields)) {
      const tsType = this.getTypeScriptType(fieldDef.data_type)
      const comment = `  /** ${fieldDef.chinese_name} - ${fieldDef.description || ''} */`

      content += `${comment}\n`
      content += `  ${fieldName}?: ${tsType}\n`
    }

    content += `}

`
    return content
  }

  /**
   * 生成完整规则详情接口
   */
  generateRuleDetailInterface() {
    return `/**
 * 完整规则详情接口
 * 继承通用字段，包含扩展字段
 */
export interface RuleDetail extends Omit<CommonFields, 'extended_fields'> {
  /** 扩展字段 - JSON格式存储的特定字段 */
  extended_fields?: SpecificFields

  /** 元数据字段 */
  id?: number
  created_at?: string
  updated_at?: string
}

/**
 * 规则详情创建数据接口
 */
export interface CreateRuleDetailData extends Omit<Partial<CommonFields>, 'extended_fields'> {
  extended_fields?: SpecificFields
}

/**
 * 规则详情更新数据接口
 */
export interface UpdateRuleDetailData extends Omit<Partial<CommonFields>, 'extended_fields'> {
  extended_fields?: SpecificFields
}

`
  }

  /**
   * 转换数据类型为TypeScript类型
   */
  getTypeScriptType(dataType) {
    const typeMapping = {
      'string': 'string',
      'text': 'string',
      'integer': 'number',
      'array': 'string[]',
      'boolean': 'boolean'
    }
    return typeMapping[dataType] || 'any'
  }

  /**
   * 生成字段映射常量
   */
  generateFieldMappingConstants(commonFields, specificFields) {
    let content = `/**
 * 字段中文名称映射
 * 用于显示和国际化
 */
export const FIELD_CHINESE_NAMES = {
`

    // 生成字段映射
    for (const [fieldName, fieldDef] of Object.entries({ ...commonFields, ...specificFields })) {
      content += `  ${fieldName}: '${fieldDef.chinese_name}',\n`
    }

    content += `} as const

/**
 * 字段数据类型映射
 */
export const FIELD_DATA_TYPES = {
`

    for (const [fieldName, fieldDef] of Object.entries({ ...commonFields, ...specificFields })) {
      content += `  ${fieldName}: '${fieldDef.data_type}',\n`
    }

    content += `} as const

/**
 * 字段验证规则映射
 */
export const FIELD_VALIDATION_RULES = {
`

    for (const [fieldName, fieldDef] of Object.entries({ ...commonFields, ...specificFields })) {
      const rules = fieldDef.validation_rules || []
      content += `  ${fieldName}: ${JSON.stringify(rules)},\n`
    }

    content += `} as const

`
    return content
  }

  /**
   * 生成工具函数
   */
  generateUtilityFunctions() {
    return `/**
 * 获取字段的中文名称
 * @param fieldName 字段名
 * @returns 中文名称
 */
export function getFieldChineseName(fieldName: string): string {
  return FIELD_CHINESE_NAMES[fieldName as keyof typeof FIELD_CHINESE_NAMES] || fieldName
}

/**
 * 获取字段的数据类型
 * @param fieldName 字段名
 * @returns 数据类型
 */
export function getFieldDataType(fieldName: string): string {
  return FIELD_DATA_TYPES[fieldName as keyof typeof FIELD_DATA_TYPES] || 'string'
}

/**
 * 获取字段的验证规则
 * @param fieldName 字段名
 * @returns 验证规则数组
 */
export function getFieldValidationRules(fieldName: string): string[] {
  const rules = FIELD_VALIDATION_RULES[fieldName as keyof typeof FIELD_VALIDATION_RULES] || []
  return [...rules] // 转换为可变数组
}

/**
 * 检查字段是否为必填
 * @param fieldName 字段名
 * @returns 是否必填
 */
export function isFieldRequired(fieldName: string): boolean {
  const rules = getFieldValidationRules(fieldName)
  return rules.includes('required')
}

`
  }

  /**
   * 生成规则类型枚举
   */
  generateRuleTypeEnums() {
    const ruleTypeMappings = this.config.rule_type_mappings || {}

    let content = `/**
 * 规则类型枚举
 */
export enum RuleType {
`

    for (const [ruleType] of Object.entries(ruleTypeMappings)) {
      const enumName = ruleType.toUpperCase().replace(/[^A-Z0-9]/g, '_')
      content += `  ${enumName} = '${ruleType}',\n`
    }

    content += `}

/**
 * 规则类型中文名称映射
 */
export const RULE_TYPE_CHINESE_NAMES = {
`

    for (const [ruleType, ruleInfo] of Object.entries(ruleTypeMappings)) {
      const name = ruleInfo.name || ruleType
      content += `  ${ruleType}: '${name}',\n`
    }

    content += `} as const

/**
 * 获取规则类型的中文名称
 * @param ruleType 规则类型
 * @returns 中文名称
 */
export function getRuleTypeChineseName(ruleType: string): string {
  return RULE_TYPE_CHINESE_NAMES[ruleType as keyof typeof RULE_TYPE_CHINESE_NAMES] || ruleType
}

`
    return content
  }

  /**
   * 保存生成的类型定义到文件
   */
  saveToFile(outputPath = 'src/types/generated-fields.ts') {
    try {
      const fullOutputPath = path.resolve(__dirname, '..', outputPath)
      const outputDir = path.dirname(fullOutputPath)

      // 确保输出目录存在
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
        console.log(`📁 创建输出目录: ${outputDir}`)
      }

      const content = this.generateTypes()
      fs.writeFileSync(fullOutputPath, content, 'utf8')

      console.log(`✅ 类型定义已生成: ${fullOutputPath}`)
      console.log(`📏 文件大小: ${(content.length / 1024).toFixed(2)} KB`)

      return fullOutputPath
    } catch (error) {
      console.error('❌ 保存文件失败:', error.message)
      process.exit(1)
    }
  }
}

// 执行生成
console.log('🚀 开始生成TypeScript类型定义...')

const generator = new TypeScriptGenerator()
const outputPath = generator.saveToFile()

console.log('🎉 类型生成完成!')
console.log(`📄 输出文件: ${outputPath}`)

export default TypeScriptGenerator
