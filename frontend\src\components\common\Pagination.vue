<template>
  <div class="pagination-wrapper">
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :total="total"
      :layout="layout"
      :background="background"
      :small="small"
      :disabled="disabled"
      :hide-on-single-page="hideOnSinglePage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="pagination"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 20
  },
  total: {
    type: Number,
    default: 0
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  background: {
    type: Boolean,
    default: true
  },
  small: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },
  hideOnSinglePage: {
    type: <PERSON>olean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:current-page', 'update:page-size', 'change'])

// 处理页码变化
const handleCurrentChange = (page) => {
  emit('update:current-page', page)
  emit('change', { page, pageSize: props.pageSize })
}

// 处理页面大小变化
const handleSizeChange = (size) => {
  emit('update:page-size', size)
  emit('change', { page: props.currentPage, pageSize: size })
}
</script>

<style scoped>
.pagination-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
}

.pagination {
  flex-wrap: wrap;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-wrapper {
    padding: 12px 0;
  }
}

@media (max-width: 576px) {
  .pagination {
    font-size: 12px;
  }

  .pagination :deep(.el-pagination__sizes) {
    margin-right: 8px;
  }

  .pagination :deep(.el-pagination__jump) {
    margin-left: 8px;
  }
}
</style>
