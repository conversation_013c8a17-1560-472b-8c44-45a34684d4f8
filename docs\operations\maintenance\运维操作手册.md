# 规则验证系统运维操作手册

**文档版本**: v2.0  
**适用版本**: rule_data_sets 表结构重构后的生产环境  
**创建时间**: 2025-07-14  
**更新时间**: 2025-07-14  

## 📋 概述

本手册为规则验证系统的日常运维提供详细的操作指南，包括监控、维护、故障处理等操作流程。

## 🔍 日常监控

### 1. 系统状态检查

#### 快速状态检查
```bash
# 查看监控仪表板
/data/rule-service/monitoring/scripts/dashboard.sh

# 检查服务状态
docker-compose -f docker-compose.master.yml ps
docker-compose -f docker-compose.slave.yml ps

# 检查系统资源
htop
df -h
free -h
```

#### 详细健康检查
```bash
# 主节点健康检查
curl -f http://localhost:18001/health
curl -f http://localhost:18001/api/v1/health

# 从节点健康检查
curl -f http://localhost:18001/health
curl -f http://localhost:18001/performance

# 前端服务检查
curl -f http://localhost:18099/health
```

### 2. 性能监控

#### 关键指标监控
```bash
# CPU使用率
top -bn1 | grep "Cpu(s)" | awk '{print $2}'

# 内存使用率
free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}'

# 磁盘使用率
df -h / | awk 'NR==2 {print $5}'

# 网络连接数
netstat -an | grep :18001 | wc -l
```

#### 应用性能指标
```bash
# 查看应用指标
curl -s http://localhost:18001/metrics | jq '.'

# 数据库连接池状态
docker exec sub-rule-master python -c "
from core.database import get_db_engine
engine = get_db_engine()
print('连接池状态:', engine.pool.status())
"

# 规则验证性能
curl -H "X-API-KEY: your-api-key" \
     -X POST http://localhost:18001/api/v1/validate \
     -d '{"test": "data"}' \
     -w "响应时间: %{time_total}s\n"
```

### 3. 日志监控

#### 实时日志查看
```bash
# 应用日志
docker-compose -f docker-compose.master.yml logs -f --tail=100
docker-compose -f docker-compose.slave.yml logs -f --tail=100

# 系统日志
tail -f /data/rule-service/logs/master/application.log
tail -f /data/rule-service/logs/slave/application.log

# 错误日志
tail -f /data/rule-service/logs/error.log | grep ERROR
```

#### 日志分析
```bash
# 错误统计
grep ERROR /data/rule-service/logs/master/application.log | wc -l

# 响应时间分析
grep "response_time" /data/rule-service/logs/access.log | \
  awk '{sum+=$NF; count++} END {print "平均响应时间:", sum/count "ms"}'

# API调用统计
grep "API_CALL" /data/rule-service/logs/access.log | \
  awk '{print $4}' | sort | uniq -c | sort -nr
```

## 🔧 维护操作

### 1. 服务管理

#### 服务启停
```bash
# 启动服务
docker-compose -f docker-compose.master.yml up -d
docker-compose -f docker-compose.slave.yml up -d

# 停止服务
docker-compose -f docker-compose.master.yml down
docker-compose -f docker-compose.slave.yml down

# 重启服务
docker-compose -f docker-compose.master.yml restart
docker-compose -f docker-compose.slave.yml restart

# 优雅重启（等待请求完成）
docker-compose -f docker-compose.master.yml kill -s SIGTERM
sleep 30
docker-compose -f docker-compose.master.yml up -d
```

#### 配置更新
```bash
# 更新配置文件
vim .env.master
vim .env.slave

# 验证配置
docker-compose -f docker-compose.master.yml config
docker-compose -f docker-compose.slave.yml config

# 应用配置（重启服务）
docker-compose -f docker-compose.master.yml up -d --force-recreate
```

### 2. 数据库维护

#### 数据库状态检查
```bash
# 连接数据库
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME

# 检查数据库状态
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Queries';
SHOW PROCESSLIST;

# 检查表状态
SELECT table_name, table_rows, data_length, index_length 
FROM information_schema.tables 
WHERE table_schema = 'rule_service';
```

#### 数据库优化
```sql
-- 分析表
ANALYZE TABLE rule_data_sets;
ANALYZE TABLE rule_details;

-- 优化表
OPTIMIZE TABLE rule_data_sets;
OPTIMIZE TABLE rule_details;

-- 检查索引使用情况
SHOW INDEX FROM rule_details;
EXPLAIN SELECT * FROM rule_details WHERE dataset_id = 1;
```

#### 数据迁移状态检查
```bash
# 检查迁移状态
docker exec sub-rule-master python -c "
from services.data_management.rule_data_migration import EnhancedRuleDataMigration
migration = EnhancedRuleDataMigration()
status = migration.get_migration_status()
print('迁移状态:', status)
"

# 查看迁移进度
docker exec sub-rule-master python -c "
from models.database import RuleDataSet, MigrationStatusEnum
from core.database import get_db_session
with get_db_session() as session:
    total = session.query(RuleDataSet).count()
    completed = session.query(RuleDataSet).filter(
        RuleDataSet.migration_status == MigrationStatusEnum.COMPLETED
    ).count()
    print(f'迁移进度: {completed}/{total} ({completed/total*100:.1f}%)')
"
```

### 3. 缓存管理

#### 规则缓存更新
```bash
# 主节点：强制刷新缓存
curl -X POST -H "X-API-KEY: your-api-key" \
     http://localhost:18001/api/v1/cache/refresh

# 从节点：手动同步规则
curl -X POST -H "X-API-KEY: your-api-key" \
     http://localhost:18001/api/v1/sync/force

# 查看缓存状态
curl -H "X-API-KEY: your-api-key" \
     http://localhost:18001/api/v1/cache/status
```

#### 缓存文件管理
```bash
# 查看缓存文件
ls -la /data/rule-service/slave/data/rules_cache.json.gz

# 备份缓存文件
cp /data/rule-service/slave/data/rules_cache.json.gz \
   /data/rule-service/backup/rules_cache_$(date +%Y%m%d_%H%M%S).json.gz

# 验证缓存文件完整性
gzip -t /data/rule-service/slave/data/rules_cache.json.gz
```

## 💾 备份与恢复

### 1. 数据备份

#### 数据库备份
```bash
# 全量备份
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD \
  --single-transaction --routines --triggers $DB_NAME > \
  /data/rule-service/backup/db_full_$(date +%Y%m%d_%H%M%S).sql

# 增量备份（基于binlog）
mysqlbinlog --start-datetime="2025-07-14 00:00:00" \
  --stop-datetime="2025-07-14 23:59:59" \
  /var/log/mysql/mysql-bin.000001 > \
  /data/rule-service/backup/db_incr_$(date +%Y%m%d).sql

# 压缩备份
gzip /data/rule-service/backup/db_full_*.sql
```

#### 应用数据备份
```bash
# 配置文件备份
tar -czf /data/rule-service/backup/config_$(date +%Y%m%d).tar.gz \
  .env.master .env.slave docker-compose.*.yml monitoring-config.yml

# 应用数据备份
tar -czf /data/rule-service/backup/appdata_$(date +%Y%m%d).tar.gz \
  /data/rule-service/master/data \
  /data/rule-service/slave/data \
  /data/rule-service/monitoring

# 日志备份
tar -czf /data/rule-service/backup/logs_$(date +%Y%m%d).tar.gz \
  /data/rule-service/logs
```

### 2. 数据恢复

#### 数据库恢复
```bash
# 停止应用服务
docker-compose -f docker-compose.master.yml down

# 恢复数据库
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME < \
  /data/rule-service/backup/db_full_20250714_120000.sql

# 应用增量恢复（如果需要）
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME < \
  /data/rule-service/backup/db_incr_20250714.sql

# 启动应用服务
docker-compose -f docker-compose.master.yml up -d
```

#### 应用数据恢复
```bash
# 恢复配置文件
tar -xzf /data/rule-service/backup/config_20250714.tar.gz

# 恢复应用数据
tar -xzf /data/rule-service/backup/appdata_20250714.tar.gz -C /

# 设置权限
chown -R $USER:$USER /data/rule-service
chmod -R 755 /data/rule-service
```

## 🚨 故障处理

### 1. 服务故障

#### 服务无法启动
```bash
# 检查配置文件
./pre-deployment-check.sh master

# 检查端口占用
netstat -tuln | grep 18001
lsof -i :18001

# 检查Docker状态
docker info
docker system df

# 查看详细错误
docker-compose -f docker-compose.master.yml logs
```

#### 服务响应缓慢
```bash
# 检查系统资源
top
iotop
netstat -i

# 检查应用性能
curl -w "@curl-format.txt" http://localhost:18001/health

# 检查数据库性能
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD -e "SHOW PROCESSLIST;"
```

### 2. 数据库故障

#### 连接失败
```bash
# 测试网络连接
telnet $DB_HOST $DB_PORT

# 检查数据库状态
systemctl status mysql

# 检查连接数
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD -e "SHOW STATUS LIKE 'Threads_connected';"

# 检查错误日志
tail -f /var/log/mysql/error.log
```

#### 性能问题
```sql
-- 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 检查锁等待
SHOW ENGINE INNODB STATUS;
SELECT * FROM information_schema.INNODB_LOCKS;

-- 检查表大小
SELECT table_name, 
       ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'rule_service'
ORDER BY (data_length + index_length) DESC;
```

### 3. 网络故障

#### 主从同步失败
```bash
# 检查主节点状态
curl -f http://$MASTER_API_ENDPOINT/health

# 检查网络连接
ping $MASTER_HOST
traceroute $MASTER_HOST

# 检查API密钥
curl -H "X-API-KEY: $SLAVE_API_KEY" \
     $MASTER_API_ENDPOINT/api/v1/rules/version

# 强制同步
curl -X POST -H "X-API-KEY: $SLAVE_API_KEY" \
     http://localhost:18001/api/v1/sync/force
```

## 📊 性能调优

### 1. 应用层优化

#### 工作进程调优
```bash
# 根据CPU核心数调整
CPU_CORES=$(nproc)
OPTIMAL_WORKERS=$((CPU_CORES * 2))

# 更新配置
sed -i "s/WORKERS=.*/WORKERS=$OPTIMAL_WORKERS/" .env.master
sed -i "s/WORKERS=.*/WORKERS=$OPTIMAL_WORKERS/" .env.slave
```

#### 连接池优化
```bash
# 调整数据库连接池
sed -i "s/DB_POOL_SIZE=.*/DB_POOL_SIZE=50/" .env.master
sed -i "s/DB_MAX_OVERFLOW=.*/DB_MAX_OVERFLOW=100/" .env.master

# 调整应用连接数
sed -i "s/MAX_CONNECTIONS=.*/MAX_CONNECTIONS=500/" .env.slave
```

### 2. 数据库优化

#### 索引优化
```sql
-- 检查缺失的索引
SELECT * FROM sys.schema_unused_indexes;

-- 添加复合索引
CREATE INDEX idx_dataset_status ON rule_details(dataset_id, status);
CREATE INDEX idx_created_at ON rule_details(created_at);

-- 删除未使用的索引
DROP INDEX unused_index_name ON table_name;
```

#### 查询优化
```sql
-- 优化慢查询
EXPLAIN SELECT * FROM rule_details 
WHERE dataset_id = 1 AND status = 'active';

-- 使用覆盖索引
CREATE INDEX idx_covering ON rule_details(dataset_id, status, rule_name);
```

## 📋 运维检查清单

### 日常检查（每日）
- [ ] 服务状态正常
- [ ] 系统资源使用率正常
- [ ] 错误日志无异常
- [ ] 数据库连接正常
- [ ] 主从同步正常
- [ ] 备份任务执行成功

### 周期检查（每周）
- [ ] 磁盘空间清理
- [ ] 日志文件轮转
- [ ] 性能指标分析
- [ ] 安全更新检查
- [ ] 备份文件验证

### 月度检查（每月）
- [ ] 系统性能评估
- [ ] 容量规划评估
- [ ] 安全审计
- [ ] 灾难恢复演练
- [ ] 文档更新

## 📞 应急联系

### 联系方式
- **运维值班**: <EMAIL>
- **技术支持**: <EMAIL>
- **数据库DBA**: <EMAIL>
- **安全团队**: <EMAIL>

### 升级流程
1. **L1**: 运维工程师（基础故障处理）
2. **L2**: 高级运维工程师（复杂故障分析）
3. **L3**: 开发团队（代码级问题解决）
4. **L4**: 架构师（架构级问题决策）

---

**注意**: 本手册应定期更新，确保操作流程与系统实际状态保持一致。
