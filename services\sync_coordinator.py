"""
统一同步协调器

基于现有RuleDataSyncService和ErrorRecoveryManager，实现统一的同步协调器。
负责协调在线同步和离线包生成，集成现有的错误处理和重试机制。
"""

import asyncio
import base64

# 导入安全相关模块
import hashlib
import hmac
import time
from datetime import datetime
from typing import Any

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from sqlalchemy.orm import Session

from config.settings import Settings
from core.constants.error_codes import ErrorCodes
from core.logging.logging_system import log as logger
from core.utils.error_recovery import ErrorRecoveryManager, RecoveryConfig, RecoveryStrategy
from core.version_management import RuleSyncVersionManager
from models.sync import (
    ChangesRequest,
    ChangesResponse,
    SyncAcknowledgment,
    SyncConfiguration,
    SyncMetrics,
    SyncMode,
    SyncRequest,
    SyncResponse,
    SyncStatus,
    SyncStatusResponse,
    create_sync_error,
    create_sync_id,
)
from services.rule_data_sync_service import RuleDataSyncService

# 导入系统监控模块
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil不可用，系统资源监控功能将被禁用")



class SyncSecurityValidator:
    """同步安全验证器"""

    def __init__(self, encryption_key: str | None = None):
        """
        初始化安全验证器

        Args:
            encryption_key: 加密密钥，用于数据加密传输
        """
        self.encryption_key = encryption_key
        self.cipher_suite = None
        self.trusted_nodes = set()  # 受信任的节点列表
        self.blacklisted_nodes = set()  # 黑名单节点

        # 初始化加密套件
        if encryption_key:
            self._init_encryption()

    def _init_encryption(self):
        """初始化加密套件"""
        try:
            # 如果是密码，通过PBKDF2生成密钥
            if isinstance(self.encryption_key, str):
                password = self.encryption_key.encode()
                salt = b'sync_coordinator_salt'  # 在生产环境中应该使用随机盐
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(password))
                self.cipher_suite = Fernet(key)
            else:
                # 直接使用提供的密钥
                self.cipher_suite = Fernet(self.encryption_key)

            logger.info("数据加密功能已启用")
        except Exception as e:
            logger.error(f"初始化加密套件失败: {e}")
            self.cipher_suite = None

    def validate_sync_request(self, request: SyncRequest, source_ip: str | None = None) -> bool:
        """
        验证同步请求的安全性

        Args:
            request: 同步请求
            source_ip: 请求来源IP地址

        Returns:
            bool: 是否通过验证
        """
        try:
            # 1. 节点ID验证
            if not self._validate_node_id(request.node_id):
                logger.warning(f"无效的节点ID: {request.node_id}")
                return False

            # 2. 检查黑名单
            if request.node_id in self.blacklisted_nodes:
                logger.warning(f"黑名单节点尝试同步: {request.node_id}")
                return False

            # 3. IP地址验证（如果提供）
            if source_ip and not self._validate_source_ip(source_ip):
                logger.warning(f"可疑的IP地址: {source_ip}")
                return False

            # 4. 请求参数验证
            if not self._validate_request_parameters(request):
                logger.warning(f"无效的请求参数: {request.node_id}")
                return False

            # 5. 频率限制检查
            if not self._check_rate_limit(request.node_id):
                logger.warning(f"节点请求频率过高: {request.node_id}")
                return False

            logger.debug(f"同步请求安全验证通过: {request.node_id}")
            return True

        except Exception as e:
            logger.error(f"同步请求安全验证失败: {e}")
            return False

    def _validate_node_id(self, node_id: str) -> bool:
        """验证节点ID格式和合法性"""
        if not node_id or len(node_id) < 3:
            return False

        # 节点ID应该只包含字母、数字、下划线和连字符
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', node_id):
            return False

        # 长度限制
        if len(node_id) > 64:
            return False

        return True

    def _validate_source_ip(self, ip: str) -> bool:
        """验证源IP地址"""
        try:
            import ipaddress
            addr = ipaddress.ip_address(ip)

            # 拒绝私有地址之外的某些地址范围
            if addr.is_loopback and ip != "127.0.0.1":
                return False

            # 可以添加更多IP验证逻辑
            return True

        except ValueError:
            return False

    def _validate_request_parameters(self, request: SyncRequest) -> bool:
        """验证请求参数的合理性"""
        try:
            # 批大小验证
            if request.batch_size <= 0 or request.batch_size > 10000:
                return False

            # 超时时间验证
            if request.timeout_seconds <= 0 or request.timeout_seconds > 3600:
                return False

            # 版本格式验证（如果提供）
            if request.current_version:
                if len(request.current_version) > 100:  # 版本号不应该太长
                    return False

            return True

        except Exception:
            return False

    def _check_rate_limit(self, node_id: str) -> bool:
        """检查请求频率限制"""
        # 这里应该实现基于时间窗口的频率限制
        # 简化实现，实际应该使用Redis或内存缓存
        current_time = time.time()
        rate_limit_key = f"rate_limit_{node_id}"

        # 简单的内存频率限制（仅作演示）
        if not hasattr(self, '_rate_limits'):
            self._rate_limits = {}

        if rate_limit_key not in self._rate_limits:
            self._rate_limits[rate_limit_key] = []

        # 清理过期记录（1分钟窗口）
        window_start = current_time - 60
        self._rate_limits[rate_limit_key] = [
            timestamp for timestamp in self._rate_limits[rate_limit_key] 
            if timestamp > window_start
        ]

        # 检查频率（每分钟最多10次请求）
        if len(self._rate_limits[rate_limit_key]) >= 10:
            return False

        # 记录当前请求
        self._rate_limits[rate_limit_key].append(current_time)
        return True

    def encrypt_data(self, data: bytes) -> bytes | None:
        """加密数据"""
        if not self.cipher_suite:
            return data

        try:
            return self.cipher_suite.encrypt(data)
        except Exception as e:
            logger.error(f"数据加密失败: {e}")
            return None

    def decrypt_data(self, encrypted_data: bytes) -> bytes | None:
        """解密数据"""
        if not self.cipher_suite:
            return encrypted_data

        try:
            return self.cipher_suite.decrypt(encrypted_data)
        except Exception as e:
            logger.error(f"数据解密失败: {e}")
            return None

    def generate_signature(self, data: str, secret_key: str) -> str:
        """生成数据签名"""
        try:
            signature = hmac.new(
                secret_key.encode(),
                data.encode(),
                hashlib.sha256
            ).hexdigest()
            return signature
        except Exception as e:
            logger.error(f"生成签名失败: {e}")
            return ""

    def verify_signature(self, data: str, signature: str, secret_key: str) -> bool:
        """验证数据签名"""
        try:
            expected_signature = self.generate_signature(data, secret_key)
            return hmac.compare_digest(signature, expected_signature)
        except Exception as e:
            logger.error(f"验证签名失败: {e}")
            return False

    def add_trusted_node(self, node_id: str):
        """添加受信任节点"""
        self.trusted_nodes.add(node_id)
        logger.info(f"添加受信任节点: {node_id}")

    def remove_trusted_node(self, node_id: str):
        """移除受信任节点"""
        self.trusted_nodes.discard(node_id)
        logger.info(f"移除受信任节点: {node_id}")

    def add_blacklisted_node(self, node_id: str):
        """添加黑名单节点"""
        self.blacklisted_nodes.add(node_id)
        logger.warning(f"添加黑名单节点: {node_id}")

    def remove_blacklisted_node(self, node_id: str):
        """移除黑名单节点"""
        self.blacklisted_nodes.discard(node_id)
        logger.info(f"移除黑名单节点: {node_id}")

    def is_trusted_node(self, node_id: str) -> bool:
        """检查是否为受信任节点"""
        return node_id in self.trusted_nodes

    def audit_log_sync_attempt(self, request: SyncRequest, result: str, source_ip: str | None = None):
        """记录同步尝试的审计日志"""
        audit_info = {
            "timestamp": datetime.now().isoformat(),
            "node_id": request.node_id,
            "sync_mode": request.sync_mode.value,
            "source_ip": source_ip,
            "result": result,
            "request_size": len(str(request)),
        }

        logger.info(f"同步审计日志: {audit_info}", extra={"audit": True})


class SyncPerformanceMonitor:
    """同步性能监控器"""

    def __init__(self):
        """初始化性能监控器"""
        self.start_time = 0.0
        self.start_memory = 0
        self.start_cpu_times = None
        self.db_query_times = []
        self.cache_stats = {'hits': 0, 'misses': 0}

    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.perf_counter()
        if PSUTIL_AVAILABLE:
            try:
                process = psutil.Process()
                self.start_memory = process.memory_info().rss
                self.start_cpu_times = process.cpu_times()
            except Exception as e:
                logger.warning(f"启动性能监控失败: {e}")

    def get_system_metrics(self) -> dict[str, Any]:
        """获取系统性能指标"""
        metrics = {
            'cpu_usage_percent': 0.0,
            'memory_usage_mb': 0,
            'memory_peak_mb': 0,
            'disk_io_read_mb': 0.0,
            'disk_io_write_mb': 0.0,
            'network_io_in_mb': 0.0,
            'network_io_out_mb': 0.0,
        }

        if not PSUTIL_AVAILABLE:
            return metrics

        try:
            process = psutil.Process()

            # CPU使用率
            metrics['cpu_usage_percent'] = process.cpu_percent()

            # 内存使用
            memory_info = process.memory_info()
            metrics['memory_usage_mb'] = memory_info.rss // (1024 * 1024)
            if hasattr(memory_info, 'peak_wset'):
                metrics['memory_peak_mb'] = memory_info.peak_wset // (1024 * 1024)
            else:
                metrics['memory_peak_mb'] = metrics['memory_usage_mb']

            # IO统计
            try:
                io_counters = process.io_counters()
                metrics['disk_io_read_mb'] = io_counters.read_bytes / (1024 * 1024)
                metrics['disk_io_write_mb'] = io_counters.write_bytes / (1024 * 1024)
            except Exception:
                pass  # 某些系统不支持IO统计

            # 网络IO（系统级）
            try:
                net_io = psutil.net_io_counters()
                if net_io:
                    metrics['network_io_in_mb'] = net_io.bytes_recv / (1024 * 1024)
                    metrics['network_io_out_mb'] = net_io.bytes_sent / (1024 * 1024)
            except Exception:
                pass

        except Exception as e:
            logger.warning(f"获取系统指标失败: {e}")

        return metrics

    def record_db_query(self, duration_ms: float):
        """记录数据库查询时间"""
        self.db_query_times.append(duration_ms)

    def record_cache_hit(self):
        """记录缓存命中"""
        self.cache_stats['hits'] += 1

    def record_cache_miss(self):
        """记录缓存未命中"""
        self.cache_stats['misses'] += 1

    def get_cache_metrics(self) -> dict[str, Any]:
        """获取缓存性能指标"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = self.cache_stats['hits'] / total_requests if total_requests > 0 else 0.0

        return {
            'cache_hit_rate': hit_rate,
            'cache_miss_count': self.cache_stats['misses'],
            'cache_size_mb': 0.0  # 需要从具体缓存实现获取
        }

    def get_db_metrics(self) -> dict[str, Any]:
        """获取数据库性能指标"""
        if not self.db_query_times:
            return {
                'db_query_count': 0,
                'db_query_avg_time_ms': 0.0,
                'db_connection_pool_usage': 0.0
            }

        avg_time = sum(self.db_query_times) / len(self.db_query_times)

        return {
            'db_query_count': len(self.db_query_times),
            'db_query_avg_time_ms': avg_time,
            'db_connection_pool_usage': 0.0  # 需要从数据库连接池获取
        }


class SyncStateManager:
    """同步状态管理器"""

    def __init__(self):
        self.active_syncs: dict[str, SyncResponse] = {}
        self.sync_history: list[SyncResponse] = []
        self.node_states: dict[str, dict[str, Any]] = {}
        self.max_history_size = 1000

    def add_sync(self, sync_response: SyncResponse):
        """添加同步任务"""
        self.active_syncs[sync_response.sync_id] = sync_response
        logger.debug(f"添加同步任务: {sync_response.sync_id}")

    def update_sync(self, sync_id: str, **updates):
        """更新同步任务"""
        if sync_id in self.active_syncs:
            sync_response = self.active_syncs[sync_id]
            for key, value in updates.items():
                if hasattr(sync_response, key):
                    setattr(sync_response, key, value)
            logger.debug(f"更新同步任务: {sync_id}, updates: {updates}")

    def complete_sync(self, sync_id: str, status: SyncStatus):
        """完成同步任务"""
        if sync_id in self.active_syncs:
            sync_response = self.active_syncs[sync_id]
            sync_response.status = status
            sync_response.end_time = datetime.now()

            # 移动到历史记录
            self.sync_history.append(sync_response)
            del self.active_syncs[sync_id]

            # 限制历史记录大小
            if len(self.sync_history) > self.max_history_size:
                self.sync_history = self.sync_history[-self.max_history_size :]

            logger.info(f"完成同步任务: {sync_id}, status: {status}")

    def get_sync(self, sync_id: str) -> SyncResponse | None:
        """获取同步任务"""
        return self.active_syncs.get(sync_id)

    def get_node_state(self, node_id: str) -> dict[str, Any]:
        """获取节点状态"""
        return self.node_states.get(node_id, {})

    def update_node_state(self, node_id: str, **updates):
        """更新节点状态"""
        if node_id not in self.node_states:
            self.node_states[node_id] = {}
        self.node_states[node_id].update(updates)
        logger.debug(f"更新节点状态: {node_id}, updates: {updates}")


class SyncCoordinator:
    """
    统一同步协调器

    负责协调在线同步和离线包生成，集成现有的错误处理和重试机制，
    提供统一的同步状态管理。
    """

    def __init__(
        self,
        session: Session | None = None,
        settings: Settings | None = None,
        config: SyncConfiguration | None = None,
    ):
        """
        初始化同步协调器

        Args:
            session: 数据库会话
            settings: 系统设置
            config: 同步配置
        """
        self.session = session
        self.settings = settings or Settings()
        self.config = config or SyncConfiguration()

        # 初始化核心组件
        self.version_manager = RuleSyncVersionManager()
        self.state_manager = SyncStateManager()

        # 初始化错误恢复管理器，使用专门的同步恢复配置
        self.error_recovery = ErrorRecoveryManager()
        self._configure_sync_error_recovery()

        # 初始化数据同步服务
        self.data_sync_service = RuleDataSyncService(session=session, cache_file_path="rules_cache.json.gz")

        # 初始化性能监控器
        self.performance_monitor = SyncPerformanceMonitor()

        # 初始化安全验证器
        encryption_key = getattr(settings, 'SYNC_ENCRYPTION_KEY', None) if settings else None
        self.security_validator = SyncSecurityValidator(encryption_key=encryption_key)

        # 同步指标
        self.metrics: dict[str, SyncMetrics] = {}

        logger.info("SyncCoordinator initialized")

    def _configure_sync_error_recovery(self):
        """
        配置同步相关的错误恢复策略
        """
        from core.constants.error_codes import ErrorCodes
        from core.utils.error_recovery import RecoveryConfig, RecoveryStrategy

        try:
            # 同步超时错误 - 重试策略
            timeout_config = RecoveryConfig(
                strategy=RecoveryStrategy.RETRY,
                max_retries=self.config.max_retries,
                retry_delay=self.config.retry_delay,
                timeout=self.config.timeout_seconds,
                fallback_enabled=True
            )
            self.error_recovery.recovery_configs[ErrorCodes.REQUEST_TIMEOUT] = timeout_config

            # 网络连接错误 - 指数退避重试
            network_config = RecoveryConfig(
                strategy=RecoveryStrategy.RETRY,
                max_retries=5,
                retry_delay=2.0,
                timeout=120.0,
                circuit_breaker_threshold=3,
                circuit_breaker_timeout=300.0
            )
            self.error_recovery.recovery_configs[ErrorCodes.NETWORK_ERROR] = network_config

            # 数据库连接错误 - 熔断器策略
            db_config = RecoveryConfig(
                strategy=RecoveryStrategy.CIRCUIT_BREAKER,
                max_retries=3,
                retry_delay=5.0,
                timeout=60.0,
                circuit_breaker_threshold=2,
                circuit_breaker_timeout=600.0,
                fallback_enabled=True
            )
            self.error_recovery.recovery_configs[ErrorCodes.DATABASE_ERROR] = db_config

            # 同步冲突错误 - 优雅降级
            conflict_config = RecoveryConfig(
                strategy=RecoveryStrategy.GRACEFUL_DEGRADATION,
                max_retries=1,
                retry_delay=10.0,
                timeout=30.0,
                fallback_enabled=True
            )
            self.error_recovery.recovery_configs[ErrorCodes.VALIDATION_ERROR] = conflict_config

            # 资源不足错误 - 资源限流策略
            resource_config = RecoveryConfig(
                strategy=RecoveryStrategy.RESOURCE_THROTTLING,
                max_retries=2,
                retry_delay=30.0,
                timeout=180.0,
                fallback_enabled=True
            )
            self.error_recovery.recovery_configs[ErrorCodes.INTERNAL_SERVER_ERROR] = resource_config

            logger.info("同步错误恢复策略配置完成")

        except Exception as e:
            logger.error(f"配置同步错误恢复策略失败: {e}")

    async def _handle_sync_error(self, error: Exception, context: dict[str, Any]) -> bool:
        """
        处理同步错误并尝试恢复

        Args:
            error: 错误对象
            context: 错误上下文

        Returns:
            bool: 是否成功恢复
        """
        try:
            error_code = getattr(error, 'error_code', ErrorCodes.INTERNAL_ERROR)
            sync_id = context.get('sync_id', 'unknown')
            # node_id = context.get('node_id', 'unknown')

            logger.info(f"处理同步错误: sync_id={sync_id}, error_code={error_code}, error={str(error)}")

            # 获取恢复配置
            config = self.error_recovery.recovery_configs.get(error_code)
            if not config:
                logger.warning(f"未找到错误码 {error_code} 的恢复配置，使用默认策略")
                return False

            # 检查重试次数
            retry_key = f"{sync_id}_{error_code}"
            current_retries = self.error_recovery.retry_counts.get(retry_key, 0)

            if current_retries >= config.max_retries:
                logger.error(f"超过最大重试次数: {current_retries}/{config.max_retries}")
                return False

            # 记录重试次数
            self.error_recovery.retry_counts[retry_key] = current_retries + 1

            # 根据策略执行恢复
            if config.strategy == RecoveryStrategy.RETRY:
                await self._execute_retry_recovery(config, context)
                return True
            elif config.strategy == RecoveryStrategy.CIRCUIT_BREAKER:
                return await self._execute_circuit_breaker_recovery(config, context)
            elif config.strategy == RecoveryStrategy.GRACEFUL_DEGRADATION:
                return await self._execute_graceful_degradation(config, context)
            elif config.strategy == RecoveryStrategy.RESOURCE_THROTTLING:
                return await self._execute_resource_throttling(config, context)
            else:
                logger.warning(f"未知的恢复策略: {config.strategy}")
                return False

        except Exception as e:
            logger.error(f"错误恢复处理失败: {e}")
            return False

    async def _execute_retry_recovery(self, config: RecoveryConfig, context: dict[str, Any]):
        """执行重试恢复策略"""
        retry_delay = config.retry_delay * (2 ** (context.get('retry_count', 0)))  # 指数退避
        logger.info(f"执行重试恢复，延迟 {retry_delay} 秒")
        await asyncio.sleep(retry_delay)

    async def _execute_circuit_breaker_recovery(self, config: RecoveryConfig, context: dict[str, Any]) -> bool:
        """执行熔断器恢复策略"""
        circuit_key = f"sync_{context.get('node_id', 'default')}"

        # 检查熔断器状态
        if circuit_key in self.error_recovery.circuit_breakers:
            circuit_time = self.error_recovery.circuit_breakers[circuit_key]
            if time.time() - circuit_time < config.circuit_breaker_timeout:
                logger.warning(f"熔断器激活，拒绝请求: {circuit_key}")
                return False

        # 激活熔断器
        self.error_recovery.circuit_breakers[circuit_key] = time.time()
        logger.info(f"激活熔断器: {circuit_key}")
        return False

    async def _execute_graceful_degradation(self, config: RecoveryConfig, context: dict[str, Any]) -> bool:
        """执行优雅降级恢复策略"""
        logger.info("执行优雅降级策略，降低同步质量要求")
        # 这里可以实现降级逻辑，比如跳过某些非关键数据的同步
        context['degraded_mode'] = True
        return True

    async def _execute_resource_throttling(self, config: RecoveryConfig, context: dict[str, Any]) -> bool:
        """执行资源限流恢复策略"""
        logger.info("执行资源限流策略，减少并发度")
        # 这里可以实现限流逻辑，比如减少并发同步任务数量
        await asyncio.sleep(config.retry_delay)
        context['throttled_mode'] = True
        return True

    async def handle_sync_request(self, request: SyncRequest, source_ip: str | None = None) -> SyncResponse:
        """
        处理同步请求

        Args:
            request: 同步请求
            source_ip: 请求来源IP地址（可选）

        Returns:
            SyncResponse: 同步响应
        """
        sync_id = create_sync_id(request.node_id)
        start_time = time.perf_counter()

        # 安全验证
        if not self.security_validator.validate_sync_request(request, source_ip):
            logger.warning(f"同步请求安全验证失败: {request.node_id}")
            self.security_validator.audit_log_sync_attempt(request, "SECURITY_FAILED", source_ip)

            # 创建失败响应
            sync_response = SyncResponse(
                sync_id=sync_id,
                status=SyncStatus.FAILED,
                node_id=request.node_id,
                sync_mode=request.sync_mode,
                from_version=request.current_version,
                start_time=datetime.now(),
                end_time=datetime.now(),
            )

            # 添加安全验证失败错误
            security_error = create_sync_error(
                error_code="SECURITY_VALIDATION_FAILED",
                error_message="同步请求安全验证失败，可能的原因：无效节点ID、IP地址受限、请求频率过高或参数不合法",
                error_type="SecurityError",
                is_retryable=False
            )
            sync_response.errors.append(security_error.model_dump())

            return sync_response

        # 创建同步响应
        sync_response = SyncResponse(
            sync_id=sync_id,
            status=SyncStatus.STARTED,
            node_id=request.node_id,
            sync_mode=request.sync_mode,
            from_version=request.current_version,
            start_time=datetime.now(),
        )

        # 添加到状态管理器
        self.state_manager.add_sync(sync_response)

        try:
            logger.info(f"开始处理同步请求: {sync_id}, node: {request.node_id}, mode: {request.sync_mode}")

            # 记录审计日志
            self.security_validator.audit_log_sync_attempt(request, "STARTED", source_ip)

            # 更新状态为进行中
            self.state_manager.update_sync(sync_id, status=SyncStatus.IN_PROGRESS)

            if request.sync_mode == SyncMode.FULL:
                # 全量同步
                result = await self._handle_full_sync(request, sync_response)
            else:
                # 增量同步
                result = await self._handle_incremental_sync(request, sync_response)

            # 更新成功状态
            self.state_manager.update_sync(
                sync_id,
                status=SyncStatus.COMPLETED,
                to_version=result.get("version"),
                processed_items=result.get("processed_items", 0),
                end_time=datetime.now(),
            )

            # 更新节点状态
            self.state_manager.update_node_state(
                request.node_id,
                last_sync_time=datetime.now(),
                last_successful_sync=datetime.now(),
                current_version=result.get("version"),
                sync_failures=0,
            )

            logger.info(f"同步请求处理完成: {sync_id}")

            # 记录成功审计日志
            self.security_validator.audit_log_sync_attempt(request, "COMPLETED", source_ip)

        except Exception as e:
            logger.error(f"同步请求处理失败: {sync_id}, error: {e}", exc_info=True)

            # 尝试错误恢复
            recovery_context = {
                'sync_id': sync_id,
                'node_id': request.node_id,
                'sync_mode': request.sync_mode,
                'retry_count': self.error_recovery.retry_counts.get(f"{sync_id}_sync", 0)
            }

            recovery_success = await self._handle_sync_error(e, recovery_context)

            if recovery_success:
                logger.info(f"同步错误恢复成功: {sync_id}")
                # 如果恢复成功，可能需要重新尝试同步（根据策略）
                # 这里先标记为部分成功状态
                self.state_manager.update_sync(
                    sync_id, 
                    status=SyncStatus.COMPLETED, 
                    metadata={"recovered": True, "degraded_mode": recovery_context.get('degraded_mode', False)}
                )
                # 记录恢复成功审计日志
                self.security_validator.audit_log_sync_attempt(request, "RECOVERED", source_ip)
            else:
                # 创建错误信息
                sync_error = create_sync_error(
                    error_code="SYNC_FAILED", error_message=str(e), error_type=type(e).__name__, is_retryable=True
                )

                # 更新失败状态
                self.state_manager.update_sync(
                    sync_id, status=SyncStatus.FAILED, errors=[sync_error.model_dump()], end_time=datetime.now()
                )

                # 记录失败审计日志
                self.security_validator.audit_log_sync_attempt(request, "FAILED", source_ip)

            # 更新节点状态
            node_state = self.state_manager.get_node_state(request.node_id)
            sync_failures = node_state.get("sync_failures", 0) + (0 if recovery_success else 1)
            self.state_manager.update_node_state(
                request.node_id,
                last_sync_time=datetime.now(),
                sync_failures=sync_failures,
                network_partition=sync_failures >= 3,
                last_recovery_attempt=datetime.now() if not recovery_success else None,
                recovery_success_rate=self._calculate_recovery_success_rate(request.node_id)
            )

        finally:
            # 记录同步指标
            duration = time.perf_counter() - start_time
            self._record_sync_metrics(sync_id, request.node_id, duration)

            # 完成同步任务
            current_sync = self.state_manager.get_sync(sync_id)
            if current_sync:
                self.state_manager.complete_sync(sync_id, current_sync.status)

        return self.state_manager.get_sync(sync_id) or sync_response

    def _calculate_recovery_success_rate(self, node_id: str) -> float:
        """
        计算节点的错误恢复成功率

        Args:
            node_id: 节点ID

        Returns:
            float: 恢复成功率 (0.0-1.0)
        """
        try:
            # 这是一个简化的实现，实际中可能需要更复杂的统计逻辑
            node_state = self.state_manager.get_node_state(node_id)
            total_failures = node_state.get("sync_failures", 0)
            if total_failures == 0:
                return 1.0

            # 假设恢复成功的比例，实际应该基于历史数据统计
            recovery_attempts = node_state.get("recovery_attempts", 0)
            if recovery_attempts == 0:
                return 0.0

            recovery_successes = node_state.get("recovery_successes", 0)
            return recovery_successes / recovery_attempts

        except Exception as e:
            logger.error(f"计算恢复成功率失败: {e}")
            return 0.0

    async def _handle_full_sync(self, request: SyncRequest, sync_response: SyncResponse) -> dict[str, Any]:
        """处理全量同步"""
        logger.info(f"执行全量同步: {sync_response.sync_id}")

        try:
            # 使用现有的数据同步服务
            if self.session:
                # 主节点：从数据库同步
                stats = self.data_sync_service.sync_from_database(force_full_sync=True)

                return {
                    "version": self.version_manager.generate_version({"sync_type": "full", "timestamp": time.time()}),
                    "processed_items": stats.total_templates + stats.total_details,
                    "sync_duration": stats.sync_duration,
                    "cache_size_mb": stats.cache_size_mb,
                }
            else:
                # 子节点：从缓存文件同步
                stats = self.data_sync_service.sync_from_cache()

                return {
                    "version": self.version_manager.generate_version({"sync_type": "full", "timestamp": time.time()}),
                    "processed_items": stats.total_templates + stats.total_details,
                    "sync_duration": stats.sync_duration,
                    "cache_size_mb": stats.cache_size_mb,
                }

        except Exception as e:
            logger.error(f"全量同步失败: {e}")
            raise

    async def _handle_incremental_sync(self, request: SyncRequest, sync_response: SyncResponse) -> dict[str, Any]:
        """处理增量同步"""
        logger.info(f"执行增量同步: {sync_response.sync_id}")

        try:
            # 确保有有效的起始版本
            from_version = request.current_version or "initial"

            # 如果是初始同步，回退到全量同步
            if from_version == "initial":
                logger.info("检测到初始同步，回退到全量同步模式")
                return await self._handle_full_sync(request, sync_response)

            # 获取当前数据快照并缓存
            current_data = await self._get_current_rule_data()
            current_version = self.version_manager.generate_version(
                {"sync_type": "incremental", "timestamp": time.time(), "data": current_data}
            )

            # 缓存当前版本数据，用于变更计算
            self.version_manager.cache_version_data(current_version, current_data)

            # 获取增量变更
            changes_request = ChangesRequest(
                from_version=from_version, 
                to_version=current_version,
                rule_keys=request.rule_keys
            )

            changes_response = await self.get_incremental_changes(changes_request)

            # 应用变更
            applied_changes = await self._apply_changes(changes_response.changes)

            # 更新版本记录
            self.version_manager.update_version_record("current", current_version)

            return {
                "version": changes_response.to_version,
                "processed_items": len(applied_changes),
                "changes_count": changes_response.total_changes,
                "sync_mode": "incremental",
            }

        except Exception as e:
            logger.error(f"增量同步失败: {e}")
            raise

    async def _get_current_rule_data(self) -> dict[str, Any]:
        """
        获取当前规则数据

        Returns:
            Dict[str, Any]: 当前规则数据
        """
        try:
            if self.session:
                # 主节点：从数据库获取
                stats = self.data_sync_service.sync_from_database(force_full_sync=False)
                return {
                    "templates": stats.templates_data or {},
                    "details": stats.details_data or {},
                    "metadata": {
                        "sync_time": time.time(),
                        "total_templates": stats.total_templates,
                        "total_details": stats.total_details,
                    }
                }
            else:
                # 从节点：从缓存获取
                stats = self.data_sync_service.sync_from_cache()
                return {
                    "cache_data": stats.cache_data or {},
                    "metadata": {
                        "sync_time": time.time(),
                        "cache_size": stats.cache_size_mb,
                    }
                }
        except Exception as e:
            logger.error(f"获取当前规则数据失败: {e}")
            return {}

    async def get_incremental_changes(self, request: ChangesRequest) -> ChangesResponse:
        """
        获取增量变更

        Args:
            request: 变更请求

        Returns:
            ChangesResponse: 变更响应
        """
        logger.info(f"获取增量变更: {request.from_version} -> {request.to_version}")

        try:
            # 生成目标版本（如果未指定）
            to_version = request.to_version
            if not to_version:
                to_version = self.version_manager.generate_version(
                    {"sync_type": "incremental", "timestamp": time.time()}
                )

            # 计算变更（目前返回空列表，后续完善）
            changes = self.version_manager.calculate_changes(
                request.from_version, to_version, rule_keys=request.rule_keys
            )

            # 转换为字典格式
            changes_data = [change.to_dict() for change in changes]

            return ChangesResponse(
                from_version=request.from_version,
                to_version=to_version,
                total_changes=len(changes_data),
                changes=changes_data[: request.max_changes],
                has_more=len(changes_data) > request.max_changes,
            )

        except Exception as e:
            logger.error(f"获取增量变更失败: {e}")
            raise

    async def _apply_changes(self, changes: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """应用变更"""
        applied_changes = []

        for change in changes:
            try:
                # 这里实现具体的变更应用逻辑
                # 目前先记录日志，后续完善
                logger.debug(f"应用变更: {change}")
                applied_changes.append(change)

            except Exception as e:
                logger.error(f"应用变更失败: {change}, error: {e}")

        return applied_changes

    async def acknowledge_sync(self, acknowledgment: SyncAcknowledgment) -> bool:
        """
        确认同步完成

        Args:
            acknowledgment: 同步确认

        Returns:
            bool: 确认是否成功
        """
        logger.info(f"确认同步完成: {acknowledgment.sync_id}")

        try:
            # 更新节点状态
            self.state_manager.update_node_state(
                acknowledgment.node_id,
                applied_version=acknowledgment.applied_version,
                applied_changes=acknowledgment.applied_changes,
                last_successful_sync=datetime.now(),
            )

            return True

        except Exception as e:
            logger.error(f"确认同步失败: {e}")
            return False

    def get_sync_status(self, node_id: str) -> SyncStatusResponse:
        """
        获取同步状态

        Args:
            node_id: 节点ID

        Returns:
            SyncStatusResponse: 同步状态响应
        """
        node_state = self.state_manager.get_node_state(node_id)

        # 获取活跃同步任务
        active_syncs = [
            {
                "sync_id": sync.sync_id,
                "status": sync.status,
                "start_time": sync.start_time,
                "progress": sync.processed_items / max(sync.total_items, 1) * 100,
            }
            for sync in self.state_manager.active_syncs.values()
            if sync.node_id == node_id
        ]

        return SyncStatusResponse(
            node_id=node_id,
            current_version=node_state.get("current_version"),
            last_sync_time=node_state.get("last_sync_time"),
            last_successful_sync=node_state.get("last_successful_sync"),
            sync_failures=node_state.get("sync_failures", 0),
            is_syncing=len(active_syncs) > 0,
            network_partition=node_state.get("network_partition", False),
            active_syncs=active_syncs,
            total_syncs=node_state.get("total_syncs", 0),
            successful_syncs=node_state.get("successful_syncs", 0),
            failed_syncs=node_state.get("failed_syncs", 0),
        )

    def _record_sync_metrics(self, sync_id: str, node_id: str, duration: float):
        """记录同步指标"""
        if not self.config.enable_metrics:
            return

        try:
            # 获取系统性能指标
            system_metrics = self.performance_monitor.get_system_metrics()
            cache_metrics = self.performance_monitor.get_cache_metrics()
            db_metrics = self.performance_monitor.get_db_metrics()

            # 获取同步状态信息
            sync_state = self.state_manager.get_sync(sync_id)
            error_count = len(sync_state.errors) if sync_state else 0

            # 计算数据一致性分数（简化实现）
            consistency_score = 1.0 - (error_count * 0.1)  # 每个错误减少0.1分
            consistency_score = max(0.0, min(1.0, consistency_score))

            # 创建详细的同步指标
            metrics = SyncMetrics(
                sync_id=sync_id,
                node_id=node_id,
                start_timestamp=time.perf_counter() - duration,
                end_timestamp=time.perf_counter(),
                duration=duration,

                # 系统资源指标
                **system_metrics,

                # 缓存性能指标
                **cache_metrics,

                # 数据库性能指标
                **db_metrics,

                # 同步质量指标
                sync_accuracy_percent=100.0 - (error_count * 5),  # 每个错误减少5%准确性
                data_consistency_score=consistency_score,
                rule_validation_pass_rate=1.0,  # 暂时设为100%，实际应从验证结果获取

                # 错误指标
                error_count=error_count,
                retry_count=self.error_recovery.retry_counts.get(f"{sync_id}_sync", 0),
                recovery_attempts=0,  # 需要从错误恢复管理器获取
                recovery_success_count=0,  # 需要从错误恢复管理器获取

                # 数据大小指标（需要从同步服务获取实际值）
                data_size_bytes=0,
                compressed_size_bytes=0,
                compression_ratio=1.0,

                # 网络指标（需要实际测量）
                network_latency_ms=None,
                throughput_mbps=None,

                metadata={
                    "sync_mode": sync_state.sync_mode if sync_state else "unknown",
                    "monitoring_enabled": PSUTIL_AVAILABLE,
                    "collection_time": time.time()
                }
            )

            self.metrics[sync_id] = metrics
            logger.info(
                f"记录详细同步指标: {sync_id} | "
                f"耗时: {duration:.2f}s | "
                f"CPU: {system_metrics['cpu_usage_percent']:.1f}% | "
                f"内存: {system_metrics['memory_usage_mb']}MB | "
                f"缓存命中率: {cache_metrics['cache_hit_rate']:.2f}"
            )

        except Exception as e:
            logger.error(f"记录同步指标失败: {sync_id}, error: {e}")
            # 回退到基本指标记录
            basic_metrics = SyncMetrics(
                sync_id=sync_id,
                node_id=node_id,
                start_timestamp=time.perf_counter() - duration,
                end_timestamp=time.perf_counter(),
                duration=duration,
            )
            self.metrics[sync_id] = basic_metrics

    def add_trusted_node(self, node_id: str):
        """添加受信任节点"""
        self.security_validator.add_trusted_node(node_id)
        logger.info(f"SyncCoordinator: 添加受信任节点 {node_id}")

    def remove_trusted_node(self, node_id: str):
        """移除受信任节点"""
        self.security_validator.remove_trusted_node(node_id)
        logger.info(f"SyncCoordinator: 移除受信任节点 {node_id}")

    def add_blacklisted_node(self, node_id: str):
        """添加黑名单节点"""
        self.security_validator.add_blacklisted_node(node_id)
        logger.warning(f"SyncCoordinator: 添加黑名单节点 {node_id}")

    def remove_blacklisted_node(self, node_id: str):
        """移除黑名单节点"""
        self.security_validator.remove_blacklisted_node(node_id)
        logger.info(f"SyncCoordinator: 移除黑名单节点 {node_id}")

    def get_security_status(self, node_id: str | None = None) -> dict[str, Any]:
        """
        获取安全状态信息

        Args:
            node_id: 可选的节点ID，获取特定节点信息

        Returns:
            Dict[str, Any]: 安全状态信息
        """
        status = {
            "encryption_enabled": self.security_validator.cipher_suite is not None,
            "trusted_nodes_count": len(self.security_validator.trusted_nodes),
            "blacklisted_nodes_count": len(self.security_validator.blacklisted_nodes),
            "rate_limiting_enabled": True,
        }

        if node_id:
            status.update({
                "node_id": node_id,
                "is_trusted": self.security_validator.is_trusted_node(node_id),
                "is_blacklisted": node_id in self.security_validator.blacklisted_nodes,
            })

        return status


# 全局同步协调器实例
_sync_coordinator: SyncCoordinator | None = None


def get_sync_coordinator() -> SyncCoordinator:
    """
    获取全局同步协调器实例

    Returns:
        SyncCoordinator: 同步协调器实例
    """
    global _sync_coordinator
    if _sync_coordinator is None:
        _sync_coordinator = SyncCoordinator()
    return _sync_coordinator


def reset_sync_coordinator():
    """重置全局同步协调器实例（主要用于测试）"""
    global _sync_coordinator
    _sync_coordinator = None
