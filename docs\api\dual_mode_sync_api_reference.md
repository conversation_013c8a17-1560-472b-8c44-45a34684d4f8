# 双模式同步API参考文档

## 概述

双模式同步API提供在线同步和离线包管理的完整接口，支持规则数据的实时同步和离线部署。所有API都遵循RESTful设计原则，使用统一的认证和响应格式。

## 🔐 认证

所有API请求都需要在请求头中包含API密钥：

```http
X-API-KEY: your_api_key_here
```

## 📡 在线同步API

### 1. 获取同步状态

获取指定节点的同步状态信息。

**请求**
```http
GET /api/v1/rules/sync/status?node_id={node_id}
```

**参数**
- `node_id` (string, required): 节点ID

**响应**
```json
{
  "success": true,
  "data": {
    "node_id": "slave_001",
    "current_version": "v1.2.3",
    "sync_failures": 0,
    "is_syncing": false,
    "network_partition": false,
    "active_syncs": [],
    "total_syncs": 150,
    "successful_syncs": 148,
    "failed_syncs": 2,
    "last_sync_time": "2025-08-02T10:30:00Z",
    "last_successful_sync": "2025-08-02T10:30:00Z"
  },
  "message": "同步状态获取成功",
  "request_id": "req_123456789"
}
```

### 2. 请求同步

发起同步请求，支持全量和增量同步。

**请求**
```http
POST /api/v1/rules/sync/request
Content-Type: application/json

{
  "node_id": "slave_001",
  "sync_mode": "incremental",
  "current_version": "v1.2.2",
  "force_full_sync": false
}
```

**参数**
- `node_id` (string, required): 节点ID
- `sync_mode` (string, required): 同步模式 ("full" | "incremental")
- `current_version` (string, optional): 当前版本
- `force_full_sync` (boolean, optional): 强制全量同步

**响应**
```json
{
  "success": true,
  "data": {
    "sync_id": "sync_789012345",
    "status": "completed",
    "node_id": "slave_001",
    "sync_mode": "incremental",
    "from_version": "v1.2.2",
    "to_version": "v1.2.3",
    "processed_items": 25,
    "start_time": "2025-08-02T10:30:00Z",
    "end_time": "2025-08-02T10:30:15Z",
    "duration_seconds": 15.2
  },
  "message": "同步请求处理成功",
  "request_id": "req_123456790"
}
```

### 3. 获取增量变更

获取两个版本之间的增量变更信息。

**请求**
```http
GET /api/v1/rules/sync/changes?from_version={from_version}&to_version={to_version}&limit={limit}&offset={offset}
```

**参数**
- `from_version` (string, required): 起始版本
- `to_version` (string, required): 目标版本
- `limit` (integer, optional): 返回数量限制，默认100
- `offset` (integer, optional): 偏移量，默认0

**响应**
```json
{
  "success": true,
  "data": {
    "from_version": "v1.2.2",
    "to_version": "v1.2.3",
    "changes": [
      {
        "operation": "update",
        "rule_key": "drug_limit_diag_exact",
        "old_data": [{"id": 1, "name": "旧规则"}],
        "new_data": [{"id": 1, "name": "新规则"}],
        "change_time": "2025-08-02T10:25:00Z"
      }
    ],
    "total_changes": 25,
    "has_more": false
  },
  "message": "增量变更获取成功",
  "request_id": "req_123456791"
}
```

### 4. 获取缓存统计

获取同步缓存的统计信息。

**请求**
```http
GET /api/v1/rules/sync/cache/stats
```

**响应**
```json
{
  "success": true,
  "data": {
    "version_cache": {
      "size": 1500,
      "max_size": 10000,
      "hit_rate": 0.85,
      "memory_usage_mb": 15.2
    },
    "changes_cache": {
      "size": 250,
      "max_size": 1000,
      "hit_rate": 0.72,
      "memory_usage_mb": 45.8
    },
    "package_cache": {
      "size": 50,
      "max_size": 500,
      "hit_rate": 0.90,
      "memory_usage_mb": 25.3
    },
    "total_memory_usage_mb": 86.3
  },
  "message": "缓存统计获取成功",
  "request_id": "req_123456792"
}
```

## 📦 离线包管理API

### 1. 生成离线包

生成包含规则数据的离线部署包。

**请求**
```http
POST /api/v1/rules/offline/generate
Content-Type: application/json

{
  "package_name": "rules_package_v1.0",
  "compression_level": 6,
  "expiry_days": 30,
  "description": "生产环境规则包",
  "tags": ["production", "v1.0"],
  "rule_filters": {
    "rule_types": ["drug_limit", "drug_deny"],
    "updated_after": "2025-08-01T00:00:00Z"
  }
}
```

**参数**
- `package_name` (string, required): 包名称
- `compression_level` (integer, optional): 压缩级别1-9，默认6
- `expiry_days` (integer, optional): 过期天数，默认30
- `description` (string, optional): 包描述
- `tags` (array, optional): 标签列表
- `rule_filters` (object, optional): 规则过滤条件

**响应**
```json
{
  "success": true,
  "data": {
    "package_id": "pkg_1754069006_9c5d8fa5",
    "package_name": "rules_package_v1.0",
    "version": "v1.2.3",
    "size_bytes": 1048576,
    "created_at": "2025-08-02T10:30:00Z",
    "expires_at": "2025-09-01T10:30:00Z",
    "rule_count": 150,
    "checksum": "sha256:abc123...",
    "description": "生产环境规则包",
    "tags": ["production", "v1.0"],
    "metadata": {
      "compression_level": 6,
      "original_size": 2097152,
      "compression_ratio": 2.0
    }
  },
  "message": "离线包生成成功",
  "request_id": "req_123456793"
}
```

### 2. 下载离线包

下载指定的离线包文件。

**请求**
```http
GET /api/v1/rules/offline/download/{package_id}
```

**参数**
- `package_id` (string, required): 包ID

**响应**
```http
HTTP/1.1 200 OK
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="rules_package_v1.0.gz"
Content-Length: 1048576
X-Package-ID: pkg_1754069006_9c5d8fa5
X-Package-Version: v1.2.3
X-Package-Checksum: sha256:abc123...

[二进制包数据]
```

### 3. 列出离线包

获取所有可用的离线包列表。

**请求**
```http
GET /api/v1/rules/offline/packages?limit={limit}&offset={offset}&status={status}
```

**参数**
- `limit` (integer, optional): 返回数量限制，默认20
- `offset` (integer, optional): 偏移量，默认0
- `status` (string, optional): 包状态过滤 ("active" | "expired" | "all")

**响应**
```json
{
  "success": true,
  "data": [
    {
      "package_id": "pkg_1754069006_9c5d8fa5",
      "package_name": "rules_package_v1.0",
      "version": "v1.2.3",
      "size_bytes": 1048576,
      "created_at": "2025-08-02T10:30:00Z",
      "expires_at": "2025-09-01T10:30:00Z",
      "rule_count": 150,
      "is_expired": false,
      "tags": ["production", "v1.0"]
    }
  ],
  "pagination": {
    "total": 25,
    "limit": 20,
    "offset": 0,
    "has_more": true
  },
  "message": "离线包列表获取成功",
  "request_id": "req_123456794"
}
```

### 4. 获取包信息

获取指定离线包的详细信息。

**请求**
```http
GET /api/v1/rules/offline/packages/{package_id}
```

**响应**
```json
{
  "success": true,
  "data": {
    "package_id": "pkg_1754069006_9c5d8fa5",
    "package_name": "rules_package_v1.0",
    "version": "v1.2.3",
    "size_bytes": 1048576,
    "created_at": "2025-08-02T10:30:00Z",
    "expires_at": "2025-09-01T10:30:00Z",
    "rule_count": 150,
    "checksum": "sha256:abc123...",
    "description": "生产环境规则包",
    "tags": ["production", "v1.0"],
    "is_expired": false,
    "metadata": {
      "compression_level": 6,
      "original_size": 2097152,
      "compression_ratio": 2.0,
      "rule_types": ["drug_limit", "drug_deny"],
      "generation_time_seconds": 15.2
    }
  },
  "message": "包信息获取成功",
  "request_id": "req_123456795"
}
```

### 5. 删除离线包

删除指定的离线包。

**请求**
```http
DELETE /api/v1/rules/offline/packages/{package_id}
```

**响应**
```json
{
  "success": true,
  "data": {
    "package_id": "pkg_1754069006_9c5d8fa5",
    "deleted_at": "2025-08-02T10:35:00Z"
  },
  "message": "离线包删除成功",
  "request_id": "req_123456796"
}
```

### 6. 清理过期包

清理所有过期的离线包。

**请求**
```http
POST /api/v1/rules/offline/cleanup
```

**响应**
```json
{
  "success": true,
  "data": {
    "cleaned_packages": 5,
    "freed_space_bytes": 5242880,
    "cleanup_time": "2025-08-02T10:35:00Z"
  },
  "message": "过期包清理成功",
  "request_id": "req_123456797"
}
```

### 7. 获取存储统计

获取离线包存储的统计信息。

**请求**
```http
GET /api/v1/rules/offline/storage/stats
```

**响应**
```json
{
  "success": true,
  "data": {
    "total_packages": 25,
    "total_size_bytes": 26214400,
    "total_size_mb": 25.0,
    "expired_packages": 3,
    "active_packages": 22,
    "storage_path": "/data/offline_packages",
    "available_space_bytes": 1073741824,
    "available_space_mb": 1024.0,
    "usage_percentage": 2.4
  },
  "message": "存储统计获取成功",
  "request_id": "req_123456798"
}
```

## 🔧 管理API

### 1. 获取同步指标

获取同步性能指标。

**请求**
```http
GET /api/v1/rules/sync/metrics?node_id={node_id}&time_range={time_range}
```

**响应**
```json
{
  "success": true,
  "data": {
    "node_id": "slave_001",
    "time_range": "1h",
    "metrics": {
      "sync_count": 12,
      "success_rate": 0.92,
      "average_duration_seconds": 8.5,
      "total_processed_items": 300,
      "cache_hit_rate": 0.85,
      "network_errors": 1,
      "data_errors": 0
    },
    "collected_at": "2025-08-02T10:35:00Z"
  },
  "message": "同步指标获取成功",
  "request_id": "req_123456799"
}
```

### 2. 清理同步缓存

清理指定类型的同步缓存。

**请求**
```http
POST /api/v1/rules/sync/cache/clear
Content-Type: application/json

{
  "cache_types": ["version", "changes"],
  "node_id": "slave_001"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "cleared_caches": ["version", "changes"],
    "cleared_items": 1750,
    "freed_memory_mb": 60.5
  },
  "message": "缓存清理成功",
  "request_id": "req_123456800"
}
```

## 📊 错误码

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 600 | 200 | 同步请求处理失败 |
| 601 | 200 | 版本不匹配 |
| 602 | 200 | 包不存在 |
| 603 | 200 | 包已过期 |
| 604 | 200 | 存储空间不足 |
| 605 | 200 | 包生成失败 |
| 606 | 200 | 包校验失败 |
| 607 | 200 | 缓存操作失败 |

## 📝 使用示例

### Python示例

```python
import requests

# 配置
API_BASE = "http://localhost:18001"
API_KEY = "your_api_key_here"
HEADERS = {"X-API-KEY": API_KEY, "Content-Type": "application/json"}

# 获取同步状态
response = requests.get(
    f"{API_BASE}/api/v1/rules/sync/status",
    params={"node_id": "slave_001"},
    headers=HEADERS
)
print(response.json())

# 生成离线包
package_data = {
    "package_name": "test_package",
    "compression_level": 6,
    "expiry_days": 30
}
response = requests.post(
    f"{API_BASE}/api/v1/rules/offline/generate",
    json=package_data,
    headers=HEADERS
)
package_info = response.json()["data"]

# 下载离线包
response = requests.get(
    f"{API_BASE}/api/v1/rules/offline/download/{package_info['package_id']}",
    headers={"X-API-KEY": API_KEY}
)
with open("package.gz", "wb") as f:
    f.write(response.content)
```

### cURL示例

```bash
# 获取同步状态
curl -H "X-API-KEY: your_key" \
  "http://localhost:18001/api/v1/rules/sync/status?node_id=slave_001"

# 请求同步
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"node_id":"slave_001","sync_mode":"incremental"}' \
  http://localhost:18001/api/v1/rules/sync/request

# 生成离线包
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"package_name":"test_package","compression_level":6}' \
  http://localhost:18001/api/v1/rules/offline/generate
```

---

**文档版本**: v1.0  
**更新时间**: 2025-08-02  
**适用版本**: 双模式同步机制 v1.0+
