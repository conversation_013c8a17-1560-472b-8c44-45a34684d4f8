"""
从节点索引构建器内存管理集成测试
验证MemoryOptimizer集成是否正常工作
"""

import os
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock

import pytest

from core.slave_node_index_builder import SlaveNodeIndexBuilder
from core.memory_optimizer import MemoryOptimizer, MemoryStats


class TestSlaveNodeIndexBuilderMemory(unittest.TestCase):
    """从节点索引构建器内存管理测试类"""

    def setUp(self):
        """设置测试环境"""
        # 模拟环境变量
        self.env_patcher = patch.dict(
            "os.environ",
            {
                "SLAVE_INDEX_MEMORY_LIMIT_MB": "300",
                "SLAVE_INDEX_GC_THRESHOLD": "500",
            },
        )
        self.env_patcher.start()

        # 创建临时缓存文件
        self.temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".json.gz", delete=False)
        self.temp_file.close()

        # 模拟rule_index_manager
        self.mock_rule_index_manager = Mock()

    def tearDown(self):
        """清理测试环境"""
        self.env_patcher.stop()
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_memory_optimizer_initialization(self, mock_memory_optimizer_class):
        """测试内存优化器初始化"""
        # 模拟MemoryOptimizer实例
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        # 创建索引构建器
        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 验证内存优化器已初始化
        mock_memory_optimizer_class.assert_called_once_with(memory_limit_mb=300)
        mock_memory_optimizer.start_monitoring.assert_called_once()
        self.assertEqual(builder._memory_limit_mb, 300)
        self.assertEqual(builder._gc_threshold, 1000)  # 更新为settings中的默认值

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_configuration_validation(self, mock_memory_optimizer_class):
        """测试配置验证"""
        # 模拟MemoryOptimizer
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        # 测试正常配置
        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        errors = builder._validate_configuration()
        self.assertEqual(len(errors), 0)

        # 现在配置从settings读取，使用默认值，不会抛出异常
        # 测试配置验证功能本身
        builder2 = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        errors2 = builder2._validate_configuration()
        self.assertEqual(len(errors2), 0)  # 使用默认配置应该没有错误

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_should_optimize_memory(self, mock_memory_optimizer_class):
        """测试内存优化判断逻辑"""
        # 模拟MemoryOptimizer
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 测试内存压力超过阈值
        mock_memory_stats = MemoryStats(
            total_mb=8192,
            used_mb=4096,
            available_mb=4096,
            usage_percentage=50.0,
            process_memory_mb=350,  # 超过300MB限制
            cache_memory_mb=50,
            threshold_mb=300,
        )
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        self.assertTrue(builder._should_optimize_memory())

        # 测试内存压力未超过阈值
        mock_memory_stats.process_memory_mb = 250  # 未超过300MB限制
        self.assertFalse(builder._should_optimize_memory())

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_shutdown_method(self, mock_memory_optimizer_class):
        """测试优雅关闭方法"""
        # 模拟MemoryOptimizer
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 测试正常关闭
        builder.shutdown()
        mock_memory_optimizer.stop_monitoring.assert_called_once()

        # 测试异常情况
        mock_memory_optimizer.stop_monitoring.side_effect = Exception("Test error")
        builder.shutdown()  # 应该不抛出异常

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    @patch("core.slave_node_index_builder.gzip")
    @patch("core.slave_node_index_builder.json")
    def test_memory_optimization_during_build(self, mock_json, mock_gzip, mock_memory_optimizer_class):
        """测试构建过程中的内存优化"""
        # 模拟MemoryOptimizer
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        # 模拟内存状态 - 超过阈值
        mock_memory_stats = MemoryStats(
            total_mb=8192,
            used_mb=4096,
            available_mb=4096,
            usage_percentage=50.0,
            process_memory_mb=350,  # 超过300MB限制
            cache_memory_mb=50,
            threshold_mb=300,
        )
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        # 模拟文件内容
        mock_cache_data = {
            "rule_details": [
                {"rule_id": "test_1", "yb_code": "Y001"},
                {"rule_id": "test_2", "yb_code": "Y002"},
            ]
        }
        mock_json.load.return_value = mock_cache_data

        # 模拟文件操作
        mock_file = Mock()
        mock_gzip.open.return_value.__enter__.return_value = mock_file

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 执行构建
        with patch("os.path.exists", return_value=True):
            result = builder.build_index_from_cache_file()

        # 验证内存优化被调用
        self.assertTrue(result)
        self.assertTrue(mock_memory_optimizer.optimize_memory.called)
        self.assertTrue(mock_memory_optimizer.get_memory_stats.called)


if __name__ == "__main__":
    unittest.main()
