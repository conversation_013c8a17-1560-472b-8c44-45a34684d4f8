# 主子节点一致性修复部署检查清单

## 📋 部署前检查

### 环境准备
- [ ] 确认主节点运行正常
- [ ] 确认子节点当前版本可正常启动
- [ ] 备份当前子节点代码
- [ ] 准备测试数据和验证脚本

### 代码检查
- [ ] 确认修复代码已合并到目标分支
- [ ] 检查`api/routers/slave/validation.py`文件修改正确
- [ ] 确认导入了`time`模块
- [ ] 确认时间测量逻辑已添加
- [ ] 确认响应消息格式已修复
- [ ] 确认事件记录已统一

---

## 🚀 部署步骤

### 步骤1: 备份当前版本
```bash
# 创建备份目录
mkdir -p backup/$(date +%Y%m%d_%H%M%S)

# 备份关键文件
cp api/routers/slave/validation.py backup/$(date +%Y%m%d_%H%M%S)/
```
- [ ] 备份完成
- [ ] 备份文件路径记录: `_________________`

### 步骤2: 停止子节点服务
```bash
# 如果使用systemd
sudo systemctl stop rule-slave

# 或者直接终止进程
pkill -f "python slave.py"
```
- [ ] 子节点服务已停止
- [ ] 确认进程已完全终止

### 步骤3: 应用修复代码
```bash
# 拉取最新代码（如果使用Git）
git pull origin main

# 或者直接替换文件
cp /path/to/fixed/validation.py api/routers/slave/validation.py
```
- [ ] 修复代码已应用
- [ ] 文件权限检查正常

### 步骤4: 启动子节点服务
```bash
# 启动子节点
python slave.py

# 或者使用systemd
sudo systemctl start rule-slave
```
- [ ] 子节点启动成功
- [ ] 启动日志无错误信息
- [ ] 服务状态检查正常

---

## 🧪 功能验证

### 基础功能验证
```bash
# 运行验证脚本
python verify_consistency_fix.py
```

#### 检查项目
- [ ] 子节点健康检查通过
- [ ] 子节点响应包含耗时信息
- [ ] 主子节点响应格式一致
- [ ] 主子节点业务状态码一致
- [ ] 主子节点成功标识一致
- [ ] 主子节点违规项数量一致
- [ ] 消息格式模板一致

### 详细测试验证
```bash
# 运行测试套件
pytest tests/integration/test_master_slave_validation_consistency.py -v
pytest tests/integration/test_comprehensive_consistency.py -v
```

#### 测试结果检查
- [ ] 响应格式一致性测试通过
- [ ] 执行时间报告一致性测试通过
- [ ] 请求追踪一致性测试通过
- [ ] 错误处理一致性测试通过
- [ ] 性能监控数据一致性测试通过
- [ ] 所有测试用例通过率: `_____%`

### 性能验证
```bash
# 发送测试请求，检查响应时间
curl -X POST http://localhost:8001/api/v1/validate \
  -H "Content-Type: application/json" \
  -d '{
    "patientInfo": {
      "bah": "TEST001",
      "name": "测试患者",
      "age": 45,
      "gender": "男"
    },
    "ids": ["rule_001", "rule_002"]
  }'
```

#### 性能检查
- [ ] 响应时间正常（< 2秒）
- [ ] 响应消息包含耗时信息
- [ ] 耗时信息格式正确（4位小数）
- [ ] 内存使用正常
- [ ] CPU使用正常

---

## 📊 监控验证

### 日志检查
```bash
# 检查子节点日志
tail -f logs/app_*.log | grep -E "(validation|耗时)"
```

#### 日志验证项目
- [ ] 校验开始日志正常
- [ ] 校验完成日志包含耗时信息
- [ ] 日志格式与主节点一致
- [ ] 错误日志（如有）包含执行时间
- [ ] 无异常错误日志

### 请求追踪验证
- [ ] `validation_request`事件正常记录
- [ ] `validation_completed`事件包含`execution_time`
- [ ] 事件字段结构与主节点一致
- [ ] 请求ID正确传递和记录

### 性能监控验证
- [ ] 执行时间统计正常
- [ ] 违规项统计正确
- [ ] 成功/失败统计正确
- [ ] 监控数据与主节点格式一致

---

## 🔄 回滚准备

### 回滚条件
如果出现以下情况，应立即回滚：
- [ ] 子节点无法正常启动
- [ ] 校验功能异常
- [ ] 响应格式错误
- [ ] 性能显著下降
- [ ] 出现未预期的错误

### 回滚步骤
```bash
# 停止服务
sudo systemctl stop rule-slave

# 恢复备份
cp backup/YYYYMMDD_HHMMSS/validation.py api/routers/slave/validation.py

# 重启服务
sudo systemctl start rule-slave

# 验证回滚成功
python verify_consistency_fix.py
```

#### 回滚验证
- [ ] 服务正常启动
- [ ] 基础功能正常
- [ ] 无新增错误日志

---

## 📝 部署记录

### 部署信息
- **部署日期**: `_________________`
- **部署人员**: `_________________`
- **部署环境**: `_________________`
- **代码版本**: `_________________`
- **备份路径**: `_________________`

### 验证结果
- **基础功能验证**: ✅ 通过 / ❌ 失败
- **详细测试验证**: ✅ 通过 / ❌ 失败
- **性能验证**: ✅ 通过 / ❌ 失败
- **监控验证**: ✅ 通过 / ❌ 失败

### 问题记录
如有问题，请详细记录：
```
问题描述：
_________________________________

解决方案：
_________________________________

最终状态：
_________________________________
```

### 签字确认
- **部署人员签字**: `_________________`
- **测试人员签字**: `_________________`
- **运维人员签字**: `_________________`
- **项目负责人签字**: `_________________`

---

## 📞 联系信息

### 技术支持
- **开发团队**: `_________________`
- **运维团队**: `_________________`
- **项目经理**: `_________________`

### 紧急联系
- **24小时值班**: `_________________`
- **技术负责人**: `_________________`

---

## 📚 相关文档

- [主子节点一致性修复报告](MASTER_SLAVE_CONSISTENCY_FIX_REPORT.md)
- [验证脚本使用说明](verify_consistency_fix.py)
- [测试套件文档](tests/integration/)
- [系统架构文档](README.md)

---

**注意**: 请严格按照检查清单执行，确保每个步骤都得到验证和确认。如有疑问，请及时联系技术团队。
