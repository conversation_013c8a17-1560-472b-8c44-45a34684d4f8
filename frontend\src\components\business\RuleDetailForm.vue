<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
      class="rule-detail-form"
    >
      <!-- 校验状态显示 -->
      <div v-if="validation.hasErrors || validation.hasWarnings" class="validation-status-section">
        <ValidationStatus
          :is-valid="validation.isValid"
          :is-validating="validation.isValidating"
          :error-count="validation.errorCount"
          :warning-count="validation.warningCount"
          :field-states="validation.fieldStates"
          :field-errors="validation.getErrorSummary()"
          :field-name-map="fieldNameMap"
          :show-details="true"
        />
      </div>
      <!-- 基础信息 -->
      <div class="form-section">
        <h4 class="section-title">基础信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="getFieldChineseName('rule_detail_id')" prop="rule_detail_id">
              <el-input
                v-model="formData.rule_detail_id"
                placeholder="将根据规则名称自动生成"
                readonly
                :disabled="true"
              />
              <div class="field-hint">
                <el-icon><InfoFilled /></el-icon>
                明细ID将根据规则名称自动生成（MD5值）
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="getFieldChineseName('rule_name')" prop="rule_name">
              <el-input
                v-model="formData.rule_name"
                :placeholder="`请输入${getFieldChineseName('rule_name')}`"
                @input="handleRuleNameChange"
                @blur="registerFieldValidation('rule_name', $event.target)"
              />
              <!-- 字段级校验消息 -->
              <div v-if="validation.getFieldErrors('rule_name').length > 0" class="field-validation-messages">
                <ValidationMessage
                  v-for="(error, index) in validation.getFieldErrors('rule_name')"
                  :key="index"
                  type="error"
                  :message="error.error_message"
                  :suggestions="error.suggestions"
                  :error-details="error"
                  class="field-validation-message"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="getFieldChineseName('type')" prop="type">
              <el-select
                v-model="formData.type"
                :placeholder="`请选择${getFieldChineseName('type')}`"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="option in ruleTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="getFieldChineseName('status')" prop="status">
              <el-select
                v-model="formData.status"
                :placeholder="`请选择${getFieldChineseName('status')}`"
                style="width: 100%"
              >
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="默认选用">
              <el-switch
                v-model="formData.default_use"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="涉及金额" prop="involved_amount">
              <el-input-number
                v-model="formData.involved_amount"
                :precision="2"
                :min="0"
                :max="999999999.99"
                style="width: 100%"
                placeholder="请输入涉及金额"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 错误分类 -->
      <div class="form-section">
        <h4 class="section-title">错误分类</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="一级错误" prop="level1">
              <el-select
                v-model="formData.level1"
                placeholder="请选择一级错误"
                style="width: 100%"
              >
                <el-option label="数据错误" value="data_error" />
                <el-option label="逻辑错误" value="logic_error" />
                <el-option label="格式错误" value="format_error" />
                <el-option label="业务错误" value="business_error" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="二级错误" prop="level2">
              <el-input
                v-model="formData.level2"
                placeholder="请输入二级错误类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="三级错误" prop="level3">
              <el-input
                v-model="formData.level3"
                placeholder="请输入三级错误类型"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="错误原因" prop="error_reason">
          <el-input
            v-model="formData.error_reason"
            type="textarea"
            :rows="3"
            placeholder="请描述错误原因"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="错误程度" prop="degree">
              <el-select
                v-model="formData.degree"
                placeholder="请选择错误程度"
                style="width: 100%"
              >
                <el-option label="轻微" value="minor" />
                <el-option label="一般" value="normal" />
                <el-option label="严重" value="serious" />
                <el-option label="致命" value="critical" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适用业务" prop="pos">
              <el-input
                v-model="formData.pos"
                placeholder="请输入适用业务"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 质控信息 -->
      <div class="form-section">
        <h4 class="section-title">质控信息</h4>
        <el-form-item label="质控依据" prop="reference">
          <el-input
            v-model="formData.reference"
            type="textarea"
            :rows="2"
            placeholder="请输入质控依据或参考资料"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="位置描述" prop="detail_position">
              <el-input
                v-model="formData.detail_position"
                placeholder="请输入具体位置描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适用地区" prop="applicableArea">
              <el-input
                v-model="formData.applicableArea"
                placeholder="请输入适用地区"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="提示字段类型" prop="prompted_fields3">
              <el-input
                v-model="formData.prompted_fields3"
                placeholder="请输入提示字段类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="提示字段编码" prop="prompted_fields1">
              <el-input
                v-model="formData.prompted_fields1"
                placeholder="请输入提示字段编码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="提示字段序号" prop="prompted_fields2">
              <el-input-number
                v-model="formData.prompted_fields2"
                :min="1"
                style="width: 100%"
                placeholder="请输入序号"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 统计信息 -->
      <div class="form-section">
        <h4 class="section-title">统计信息</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="使用数量" prop="usage_quantity">
              <el-input-number
                v-model="formData.usage_quantity"
                :min="0"
                style="width: 100%"
                placeholder="使用数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="违规数量" prop="violation_quantity">
              <el-input-number
                v-model="formData.violation_quantity"
                :min="0"
                style="width: 100%"
                placeholder="违规数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="使用天数" prop="usage_days">
              <el-input-number
                v-model="formData.usage_days"
                :min="0"
                style="width: 100%"
                placeholder="使用天数"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="违规天数" prop="violation_days">
              <el-input-number
                v-model="formData.violation_days"
                :min="0"
                style="width: 100%"
                placeholder="违规天数"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="违规项目" prop="violation_items">
          <el-input
            v-model="formData.violation_items"
            type="textarea"
            :rows="2"
            placeholder="请输入违规项目描述"
          />
        </el-form-item>
      </div>

      <!-- 备注信息 -->
      <div class="form-section">
        <h4 class="section-title">备注信息</h4>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ mode === 'create' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import CryptoJS from 'crypto-js'
import { createRuleDetail, updateRuleDetail } from '../../api/ruleDetails'
import { getFieldChineseName } from '../../types/generated-fields'
import { fieldMappingEngine } from '../../utils/fieldMappingEngine'
import { useFormValidation } from '../../composables/validation/useFormValidation'
import ValidationMessage from '../validation/ValidationMessage.vue'
import ValidationStatus from '../validation/ValidationStatus.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  ruleKey: {
    type: String,
    required: true
  },
  mode: {
    type: String,
    default: 'create', // 'create' | 'edit'
    validator: (value) => ['create', 'edit'].includes(value)
  },
  detailData: {
    type: Object,
    default: null
  }
})

// Emits - 统一事件命名规范
const emit = defineEmits([
  'update:modelValue',
  'form:success',      // 表单提交成功
  'form:cancel',       // 表单取消
  'form:error',        // 表单错误
  'field:change'       // 字段变更
])

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  return props.mode === 'create' ? '新增规则明细' : '编辑规则明细'
})

// 选项数据
const ruleTypeOptions = computed(() => [
  { label: '数据质量', value: 'data_quality' },
  { label: '业务规则', value: 'business_rule' },
  { label: '合规检查', value: 'compliance_check' },
  { label: '安全验证', value: 'security_validation' },
  { label: '药品限制', value: 'drug_limit' },
  { label: '诊断限制', value: 'diag_limit' }
])

const statusOptions = computed(() => [
  { label: '活跃', value: 'ACTIVE' },
  { label: '非活跃', value: 'INACTIVE' },
  { label: '已删除', value: 'DELETED' }
])

// 表单数据
const formData = ref({
  rule_detail_id: '',
  rule_name: '',
  type: '',
  status: 'ACTIVE',
  default_use: false,
  involved_amount: null,
  level1: '',
  level2: '',
  level3: '',
  error_reason: '',
  degree: '',
  pos: '',
  reference: '',
  detail_position: '',
  applicableArea: '',
  prompted_fields3: '',
  prompted_fields1: '',
  prompted_fields2: null,
  usage_quantity: null,
  violation_quantity: null,
  usage_days: null,
  violation_days: null,
  violation_items: '',
  remarks: ''
})

// 使用新的校验系统
const validation = useFormValidation({
  ruleKey: props.ruleKey,
  autoSync: true,
  showMessages: true,
  submitOnValid: true
})

// 设置表单引用
watch(formRef, (newRef) => {
  if (newRef) {
    validation.setFormRef(newRef)
  }
})

// 字段名称映射（用于校验状态显示）
const fieldNameMap = computed(() => {
  const map = {}
  // 基础字段映射
  map.rule_detail_id = getFieldChineseName('rule_detail_id')
  map.rule_name = getFieldChineseName('rule_name')
  map.status = '状态'

  // 动态添加其他字段映射
  const ruleConfig = validation.formRules.value
  for (const fieldName of Object.keys(ruleConfig)) {
    if (!map[fieldName]) {
      map[fieldName] = getFieldChineseName(fieldName)
    }
  }

  return map
})

// 表单验证规则（保持向后兼容）
const formRules = computed(() => ({
  rule_detail_id: [
    { required: true, message: '明细ID不能为空', trigger: 'blur' }
  ],
  rule_name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  // 集成动态校验规则
  ...validation.elementFormRules.value
}))

// MD5生成函数
const generateRuleDetailId = (ruleName) => {
  if (!ruleName || ruleName.trim() === '') {
    return ''
  }
  return CryptoJS.MD5(ruleName.trim()).toString()
}

// 方法定义
const initFormData = () => {
  if (props.mode === 'edit' && props.detailData) {
    // 编辑模式：填充现有数据
    Object.keys(formData.value).forEach(key => {
      if (props.detailData[key] !== undefined) {
        formData.value[key] = props.detailData[key]
      }
    })
  } else {
    // 创建模式：重置表单
    Object.keys(formData.value).forEach(key => {
      if (key === 'status') {
        formData.value[key] = 'ACTIVE'
      } else if (key === 'default_use') {
        formData.value[key] = false
      } else if (['involved_amount', 'prompted_fields2', 'usage_quantity', 'violation_quantity', 'usage_days', 'violation_days'].includes(key)) {
        formData.value[key] = null
      } else {
        formData.value[key] = ''
      }
    })
  }
}

const handleSubmit = async () => {
  try {
    submitting.value = true

    // 使用增强的校验系统
    const submitResult = await validation.submitForm(formData.value, async (validatedData) => {
      // 使用字段映射引擎转换数据
      const transformedData = fieldMappingEngine.transformToApi(validatedData)

      // 调用API
      if (props.mode === 'create') {
        return await createRuleDetail(props.ruleKey, transformedData)
      } else {
        return await updateRuleDetail(props.ruleKey, props.detailData.id, transformedData)
      }
    })

    if (submitResult.success) {
      ElMessage.success(props.mode === 'create' ? '创建成功' : '更新成功')
      emit('form:success', {
        mode: props.mode,
        data: submitResult.data,
        timestamp: Date.now()
      })
    } else {
      // 校验失败，错误信息已由校验系统处理
      emit('form:error', {
        type: 'VALIDATION_ERROR',
        mode: props.mode,
        errors: submitResult.errors,
        timestamp: Date.now()
      })
    }

  } catch (error) {
    console.error('提交失败:', error)
    const errorMessage = error.message || '未知错误'
    ElMessage.error(`提交失败：${errorMessage}`)

    // 发送错误事件
    emit('form:error', {
      type: 'SUBMIT_ERROR',
      mode: props.mode,
      error: errorMessage,
      timestamp: Date.now()
    })
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  try {
    // 使用增强的重置功能
    validation.resetForm()
    emit('form:cancel')
  } catch (error) {
    console.error('取消操作失败:', error)
    emit('form:cancel') // 确保对话框能关闭
  }
}

// 注册字段实时校验
const registerFieldValidation = (fieldName, element) => {
  validation.registerFieldValidation(fieldName, element)
}

// 规则名称变更处理
const handleRuleNameChange = (value) => {
  try {
    if (props.mode === 'create' && value) {
      // 自动生成明细ID
      formData.value.rule_detail_id = generateRuleDetailId(value)

      // 基本验证规则名称
      if (value && value.length > 255) {
        ElMessage.warning('规则名称长度不能超过255个字符')
      }
    }
  } catch (error) {
    console.error('规则名称变更处理失败:', error)
  }
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    nextTick(() => {
      initFormData()
      // 清除验证状态
      formRef.value?.clearValidate()
    })
  }
})

watch(() => props.detailData, () => {
  if (props.modelValue) {
    initFormData()
  }
})

// 监听规则名称变化，自动生成明细ID
watch(() => formData.value.rule_name, (newName) => {
  if (props.mode === 'create') {
    formData.value.rule_detail_id = generateRuleDetailId(newName)
  }
}, { immediate: false })

// 导出所有需要的响应式数据和方法
defineExpose({
  formData,
  formRules,
  submitting,
  visible,
  dialogTitle,
  ruleTypeOptions,
  statusOptions,
  handleSubmit,
  handleCancel,
  handleRuleNameChange,
  generateRuleDetailId,
  getFieldChineseName,
  // 新增校验相关导出
  validation,
  fieldNameMap,
  registerFieldValidation
})
</script>

<style scoped>
.rule-detail-form {
  max-height: 600px;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.field-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.field-hint .el-icon {
  font-size: 14px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.dialog-footer {
  text-align: right;
}

/* 表单项样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* 校验状态样式 */
.validation-status-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.field-validation-messages {
  margin-top: 8px;
}

.field-validation-message {
  margin-bottom: 4px;
}

/* 字段校验状态样式 */
:deep(.validation-error) {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;
}

:deep(.validation-success) {
  border-color: #67c23a !important;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2) !important;
}

.validation-error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  padding: 4px 8px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
}
</style>
