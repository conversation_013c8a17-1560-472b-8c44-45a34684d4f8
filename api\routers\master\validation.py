"""
Master node validation router - 使用统一校验逻辑
"""

import asyncio
import json

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from api.dependencies.database import get_db_session
from api.routers.common.validation_logic import unified_validate_patient_data
from models.api import ApiResponse
from models.database import RuleFieldMetadata
from models.rule import RuleRequest, RuleResult

# Validation router (no authentication required for public validation)
validation_router = APIRouter(prefix="/api/v1", tags=["Validation"])

# Global queue reference (will be set during application startup)
_request_queue: asyncio.Queue | None = None


def set_request_queue(queue: asyncio.Queue):
    """
    Set the global request queue instance.

    Args:
        queue: The asyncio Queue instance for validation requests
    """
    global _request_queue
    _request_queue = queue


@validation_router.post("/validate", response_model=ApiResponse[list[RuleResult]])
async def validate_patient_data(http_request: Request, request: RuleRequest):
    """
    主节点校验接口 - 使用统一的校验逻辑
    支持压力测试和环境一致性

    Args:
        http_request: HTTP request object
        request: The rule validation request containing patient data and rule IDs

    Returns:
        ApiResponse[list[RuleResult]]: 统一的校验响应格式
    """
    return await unified_validate_patient_data(
        http_request=http_request, request=request, request_queue=_request_queue, node_type="master"
    )


@validation_router.get("/validation/frontend-rules/{rule_key}", response_model=ApiResponse[dict])
async def get_frontend_validation_rules(rule_key: str, session: Session = Depends(get_db_session)):
    """
    获取前端校验规则配置

    Args:
        rule_key: 规则键
        session: 数据库会话

    Returns:
        ApiResponse[dict]: 前端校验规则配置
    """
    try:
        # 直接从数据库获取字段元数据
        field_metadata_list = (
            session.query(RuleFieldMetadata)
            .filter(RuleFieldMetadata.rule_key == rule_key)
            .order_by(RuleFieldMetadata.excel_column_order.asc())
            .all()
        )

        if not field_metadata_list:
            raise HTTPException(status_code=404, detail=f"规则 '{rule_key}' 的字段元数据未找到")

        # 构建前端校验配置
        frontend_config = {"rule_key": rule_key, "fields": {}, "version": "1.0.0", "last_updated": None}

        for metadata in field_metadata_list:
            field_name = metadata.field_name
            frontend_config["fields"][field_name] = {
                "chinese_name": metadata.display_name,
                "is_required": metadata.is_required,
                "data_type": metadata.field_type.value
                if hasattr(metadata.field_type, "value")
                else str(metadata.field_type),
                "default_value": metadata.default_value,
                "rules": [],  # 基础校验规则
                "element_rules": [],  # Element Plus 表单规则
            }

            # 构建基础校验规则
            rules = []
            element_rules = []

            # 必填校验
            if metadata.is_required:
                rules.append({"type": "required", "message": f"{metadata.display_name}不能为空"})
                element_rules.append(
                    {"required": True, "message": f"{metadata.display_name}不能为空", "trigger": "blur"}
                )

            # 解析validation_rule字段中的校验规则
            if metadata.validation_rule:
                try:
                    validation_rules = json.loads(metadata.validation_rule)
                    for rule in validation_rules:
                        if isinstance(rule, dict):
                            rule_type = rule.get("type")
                            rule_value = rule.get("value")
                            rule_message = rule.get("message", f"{metadata.display_name}校验失败")

                            # 添加到基础规则
                            rules.append({"type": rule_type, "value": rule_value, "message": rule_message})

                            # 转换为Element Plus规则
                            if rule_type == "max_length":
                                element_rules.append({"max": rule_value, "message": rule_message, "trigger": "blur"})
                            elif rule_type == "min_length":
                                element_rules.append({"min": rule_value, "message": rule_message, "trigger": "blur"})
                            elif rule_type == "pattern":
                                element_rules.append(
                                    {"pattern": rule_value, "message": rule_message, "trigger": "blur"}
                                )
                            elif rule_type == "enum":
                                element_rules.append(
                                    {
                                        "type": "enum",
                                        "enum": rule_value if isinstance(rule_value, list) else [rule_value],
                                        "message": rule_message,
                                        "trigger": "change",
                                    }
                                )
                except (json.JSONDecodeError, Exception) as e:
                    # 如果解析失败，记录日志但不影响其他字段
                    print(f"解析字段 {field_name} 的校验规则失败: {e}")

            frontend_config["fields"][field_name]["rules"] = rules
            frontend_config["fields"][field_name]["element_rules"] = element_rules

        return ApiResponse.success_response(data=frontend_config, message=f"成功获取规则 '{rule_key}' 的前端校验配置")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取前端校验规则失败: {str(e)}")
