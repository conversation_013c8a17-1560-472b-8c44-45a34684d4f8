"""
Slave node validation router - 使用统一校验逻辑
"""

import asyncio

from fastapi import APIRouter, Request

from api.routers.common.validation_logic import unified_validate_patient_data
from models.api import ApiResponse
from models.rule import RuleRequest, RuleResult

# Validation router (no authentication required for internal validation)
validation_router = APIRouter(prefix="/api/v1", tags=["Validation"])

# Global queue reference (will be set during application startup)
_request_queue: asyncio.Queue | None = None


def set_request_queue(queue: asyncio.Queue):
    """
    Set the global request queue instance.

    Args:
        queue: The asyncio Queue instance for validation requests
    """
    global _request_queue
    _request_queue = queue


@validation_router.post("/validate", response_model=ApiResponse[list[RuleResult]])
async def validate_patient_data(http_request: Request, request: RuleRequest):
    """
    从节点校验接口 - 使用统一的校验逻辑
    处理高并发生产环境请求

    Args:
        http_request: HTTP request object
        request: The rule validation request containing patient data and rule IDs

    Returns:
        ApiResponse[list[RuleResult]]: 统一的校验响应格式
    """
    return await unified_validate_patient_data(
        http_request=http_request, request=request, request_queue=_request_queue, node_type="slave"
    )
