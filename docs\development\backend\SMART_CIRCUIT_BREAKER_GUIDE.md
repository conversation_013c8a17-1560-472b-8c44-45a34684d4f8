# 智能断路器使用指南

## 1. 概述

智能断路器是一种高级的故障保护机制，通过滑动窗口统计、智能半开状态恢复和成功率监控，提供比传统断路器更精确的故障检测和恢复能力。

### 1.1 核心特性

- **滑动窗口统计**：基于最近N次请求的统计信息进行决策
- **智能半开状态**：根据成功率动态调整恢复策略
- **双重阈值检测**：支持失败次数和失败率双重阈值
- **详细指标收集**：提供丰富的监控数据
- **线程安全**：支持多线程并发使用
- **状态可视化**：提供详细的状态报告

## 2. 快速开始

### 2.1 基础使用

```python
from core.http_retry.circuit_breaker import (
    CircuitBreakerConfig, 
    SmartCircuitBreaker,
    get_circuit_breaker_manager
)

# 创建断路器配置
config = CircuitBreakerConfig(
    failure_threshold=5,        # 失败次数阈值
    failure_rate_threshold=0.5, # 失败率阈值(50%)
    recovery_timeout=60.0,      # 恢复超时(秒)
    window_size=100,           # 滑动窗口大小
    half_open_max_calls=3      # 半开状态最大调用数
)

# 创建智能断路器
breaker = SmartCircuitBreaker("api-service", config)

# 检查是否可以执行请求
if breaker.can_execute():
    try:
        # 执行业务操作
        result = await api_call()
        
        # 记录成功
        breaker.record_success(response_time=0.2)
        
    except Exception as e:
        # 记录失败
        breaker.record_failure(error_type=type(e).__name__)
        raise
else:
    # 断路器打开，拒绝请求
    raise CircuitBreakerOpenError("Service is currently unavailable")
```

### 2.2 使用管理器

```python
# 获取全局断路器管理器
manager = get_circuit_breaker_manager()

# 获取或创建断路器
breaker = manager.get_circuit_breaker("user-service", config)

# 使用断路器保护服务调用
async def protected_service_call():
    if not breaker.can_execute():
        raise ServiceUnavailableError("Circuit breaker is open")
    
    try:
        result = await user_service.get_user(user_id)
        breaker.record_success(0.15)
        return result
    except Exception as e:
        breaker.record_failure(type(e).__name__)
        raise
```

## 3. 配置详解

### 3.1 基础配置参数

```python
config = CircuitBreakerConfig(
    # 失败检测配置
    failure_threshold=5,              # 失败次数阈值
    failure_rate_threshold=0.5,       # 失败率阈值(0.0-1.0)
    
    # 恢复配置
    recovery_timeout=60.0,            # 恢复超时时间(秒)
    
    # 滑动窗口配置
    window_size=100,                  # 滑动窗口大小
    min_requests_threshold=10,        # 最小请求数阈值
    
    # 半开状态配置
    half_open_max_calls=3,            # 半开状态最大调用数
    half_open_success_threshold=0.6,  # 半开状态成功率阈值
    
    # 监控配置
    enable_metrics=True,              # 启用指标收集
    metrics_window_size=1000          # 指标窗口大小
)
```

### 3.2 配置参数说明

**失败检测配置：**
- `failure_threshold`：连续失败次数达到此值时打开断路器
- `failure_rate_threshold`：失败率超过此值时打开断路器

**恢复配置：**
- `recovery_timeout`：断路器打开后，等待此时间后尝试半开状态

**滑动窗口配置：**
- `window_size`：统计窗口大小，只考虑最近N次请求
- `min_requests_threshold`：最小请求数，请求数不足时不触发断路器

**半开状态配置：**
- `half_open_max_calls`：半开状态下允许的最大调用数
- `half_open_success_threshold`：半开状态下的成功率阈值

## 4. 状态机详解

### 4.1 三种状态

```python
from core.http_retry.circuit_breaker import CircuitBreakerState

# CLOSED（关闭）：正常处理所有请求
# OPEN（打开）：拒绝所有请求
# HALF_OPEN（半开）：允许少量请求测试服务恢复
```

### 4.2 状态转换逻辑

```python
# CLOSED -> OPEN
# 条件：失败次数 >= failure_threshold 或 失败率 >= failure_rate_threshold
if failures >= config.failure_threshold or failure_rate >= config.failure_rate_threshold:
    breaker.state = CircuitBreakerState.OPEN

# OPEN -> HALF_OPEN  
# 条件：超过恢复超时时间
if time.time() - last_failure_time >= config.recovery_timeout:
    breaker.state = CircuitBreakerState.HALF_OPEN

# HALF_OPEN -> CLOSED
# 条件：半开状态下成功率达到阈值
if success_rate >= config.half_open_success_threshold:
    breaker.state = CircuitBreakerState.CLOSED

# HALF_OPEN -> OPEN
# 条件：半开状态下任何失败
if any_failure_in_half_open:
    breaker.state = CircuitBreakerState.OPEN
```

## 5. 高级功能

### 5.1 滑动窗口统计

```python
# 滑动窗口只保留最近的请求记录
breaker = SmartCircuitBreaker("service", config)

# 记录100次请求
for i in range(100):
    if i % 10 == 0:
        breaker.record_failure("Error")
    else:
        breaker.record_success(0.1)

# 窗口中只保留最近的window_size条记录
print(f"窗口大小: {len(breaker.request_window)}")
print(f"失败率: {breaker.metrics.get_failure_rate():.2%}")
```

### 5.2 智能半开状态

```python
# 半开状态下的智能恢复
config = CircuitBreakerConfig(
    half_open_max_calls=5,           # 允许5次测试调用
    half_open_success_threshold=0.8  # 需要80%成功率才恢复
)

breaker = SmartCircuitBreaker("service", config)

# 模拟半开状态测试
breaker._transition_to_half_open()

# 记录测试结果
for i in range(5):
    if i < 4:  # 前4次成功
        breaker.record_success(0.1)
    else:      # 最后1次失败
        breaker.record_failure("Error")

# 成功率 = 4/5 = 80%，达到阈值，断路器关闭
print(f"断路器状态: {breaker.state.value}")  # 输出: closed
```

### 5.3 详细指标收集

```python
# 获取详细指标
status = breaker.get_status()

print(f"断路器名称: {status['name']}")
print(f"当前状态: {status['state']}")
print(f"状态持续时间: {status['state_duration']:.2f}秒")
print(f"窗口请求数: {status['window_size']}")
print(f"窗口成功率: {status['window_success_rate']:.2%}")

# 获取详细指标
metrics = status['metrics']
print(f"总请求数: {metrics['total_requests']}")
print(f"成功率: {metrics['success_rate']:.2%}")
print(f"平均响应时间: {metrics['average_response_time']:.3f}秒")
print(f"状态转换: {metrics['state_transitions']}")
```

## 6. 与ErrorRecoveryManager集成

### 6.1 自动集成

```python
from core.utils.error_recovery import ErrorRecoveryManager

# ErrorRecoveryManager会自动使用智能断路器
manager = ErrorRecoveryManager()

# 如果增强模块可用，会自动使用SmartCircuitBreaker
# 否则回退到传统断路器
async def protected_operation():
    try:
        result = await risky_operation()
        return result
    except Exception as error:
        context = {"request_id": "req-123"}
        
        # 自动使用智能断路器保护
        return await manager.handle_error_with_recovery(
            error=error,
            error_code=ErrorCodes.EXTERNAL_API_ERROR,
            context=context,
            operation=risky_operation
        )
```

### 6.2 配置集成

```python
# 断路器配置会从config/settings.py中读取
# CIRCUIT_BREAKER_ENABLED = True
# CIRCUIT_BREAKER_FAILURE_THRESHOLD = 5
# CIRCUIT_BREAKER_FAILURE_RATE_THRESHOLD = 0.5
# CIRCUIT_BREAKER_RECOVERY_TIMEOUT = 60.0

# ErrorRecoveryManager会自动应用这些配置
manager = ErrorRecoveryManager()

# 检查智能断路器是否可用
if manager.smart_circuit_breaker_manager:
    print("智能断路器已启用")
else:
    print("使用传统断路器")
```

## 7. 实际应用场景

### 7.1 微服务调用保护

```python
class ServiceClient:
    def __init__(self):
        self.manager = get_circuit_breaker_manager()
        
        # 为不同服务创建不同的断路器
        self.user_breaker = self.manager.get_circuit_breaker(
            "user-service",
            CircuitBreakerConfig(failure_threshold=3, recovery_timeout=30.0)
        )
        
        self.order_breaker = self.manager.get_circuit_breaker(
            "order-service", 
            CircuitBreakerConfig(failure_threshold=5, recovery_timeout=60.0)
        )
    
    async def get_user(self, user_id):
        if not self.user_breaker.can_execute():
            raise ServiceUnavailableError("User service unavailable")
        
        try:
            start_time = time.time()
            result = await self.user_service.get(user_id)
            response_time = time.time() - start_time
            
            self.user_breaker.record_success(response_time)
            return result
            
        except Exception as e:
            self.user_breaker.record_failure(type(e).__name__)
            raise
```

### 7.2 数据库连接保护

```python
class DatabaseClient:
    def __init__(self):
        self.breaker = SmartCircuitBreaker(
            "database",
            CircuitBreakerConfig(
                failure_threshold=3,
                failure_rate_threshold=0.3,  # 数据库更敏感
                recovery_timeout=120.0,      # 更长的恢复时间
                window_size=50
            )
        )
    
    async def execute_query(self, query):
        if not self.breaker.can_execute():
            # 使用缓存或只读副本
            return await self.get_from_cache(query)
        
        try:
            start_time = time.time()
            result = await self.db.execute(query)
            response_time = time.time() - start_time
            
            self.breaker.record_success(response_time)
            return result
            
        except DatabaseError as e:
            self.breaker.record_failure("DatabaseError")
            
            # 如果断路器打开，尝试降级处理
            if self.breaker.state == CircuitBreakerState.OPEN:
                return await self.get_from_cache(query)
            
            raise
```

### 7.3 外部API调用保护

```python
class ExternalAPIClient:
    def __init__(self):
        self.breaker = SmartCircuitBreaker(
            "external-api",
            CircuitBreakerConfig(
                failure_threshold=5,
                failure_rate_threshold=0.4,
                recovery_timeout=300.0,      # 外部API恢复时间更长
                half_open_max_calls=2,       # 谨慎测试
                half_open_success_threshold=1.0  # 要求100%成功
            )
        )
    
    async def call_api(self, endpoint, data):
        if not self.breaker.can_execute():
            logger.warning(f"External API circuit breaker open for {endpoint}")
            raise ExternalAPIUnavailableError("External API temporarily unavailable")
        
        try:
            start_time = time.time()
            response = await self.http_client.post(endpoint, json=data)
            response_time = time.time() - start_time
            
            if response.status_code >= 500:
                # 5xx错误视为失败
                self.breaker.record_failure(f"HTTP_{response.status_code}")
                response.raise_for_status()
            else:
                self.breaker.record_success(response_time)
                return response.json()
                
        except (httpx.ConnectError, httpx.TimeoutException) as e:
            self.breaker.record_failure(type(e).__name__)
            raise
```

## 8. 监控和告警

### 8.1 状态监控

```python
def monitor_circuit_breakers():
    """监控所有断路器状态"""
    manager = get_circuit_breaker_manager()
    all_status = manager.get_all_status()
    
    for name, status in all_status.items():
        print(f"断路器: {name}")
        print(f"  状态: {status['state']}")
        print(f"  窗口成功率: {status['window_success_rate']:.2%}")
        print(f"  总请求数: {status['metrics']['total_requests']}")
        
        # 告警检查
        if status['state'] == 'open':
            send_alert(f"断路器 {name} 已打开")
        elif status['window_success_rate'] < 0.8:
            send_warning(f"断路器 {name} 成功率低于80%")
```

### 8.2 指标导出

```python
def export_circuit_breaker_metrics():
    """导出断路器指标到监控系统"""
    manager = get_circuit_breaker_manager()
    all_status = manager.get_all_status()
    
    metrics = []
    for name, status in all_status.items():
        metrics.extend([
            {
                "name": f"circuit_breaker_state",
                "labels": {"service": name},
                "value": 1 if status['state'] == 'closed' else 0
            },
            {
                "name": f"circuit_breaker_success_rate",
                "labels": {"service": name},
                "value": status['metrics']['success_rate']
            },
            {
                "name": f"circuit_breaker_total_requests",
                "labels": {"service": name},
                "value": status['metrics']['total_requests']
            }
        ])
    
    return metrics
```

## 9. 最佳实践

### 9.1 配置建议

**快速响应服务：**
```python
fast_service_config = CircuitBreakerConfig(
    failure_threshold=3,
    failure_rate_threshold=0.3,
    recovery_timeout=30.0,
    window_size=50
)
```

**批处理服务：**
```python
batch_service_config = CircuitBreakerConfig(
    failure_threshold=10,
    failure_rate_threshold=0.5,
    recovery_timeout=300.0,
    window_size=200
)
```

**关键服务：**
```python
critical_service_config = CircuitBreakerConfig(
    failure_threshold=2,
    failure_rate_threshold=0.2,
    recovery_timeout=60.0,
    half_open_max_calls=1,
    half_open_success_threshold=1.0
)
```

### 9.2 使用建议

1. **合理设置阈值**：根据服务特性调整失败阈值和失败率
2. **监控状态转换**：关注断路器的状态转换频率
3. **提供降级方案**：断路器打开时提供备用处理逻辑
4. **定期重置**：对于长期运行的服务，考虑定期重置断路器
5. **日志记录**：记录断路器状态变化和关键事件

---

**文档版本：** 1.0  
**更新时间：** 2025-07-01  
**适用版本：** CQ-008及以后版本
