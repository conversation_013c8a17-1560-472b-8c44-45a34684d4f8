from pydantic import BaseModel, Field


class PatientBasicInfo(BaseModel):
    """患者基本信息"""

    grade: str | None = Field(None, description="医院等级（代码，甲等乙级标志）")
    level: str | None = Field(None, description="医院级别（代码，三级二级标志）")
    hospitalNature: str | None = Field(None, description="医院性质")
    gender: str | None = Field(None, description="性别（代码）")
    genderDescribe: str | None = Field(None, description="性别（中文描述）")
    birthDate: int | None = Field(None, description="出生日期（毫秒，到天）")
    age: int | None = Field(None, description="年龄")
    country: str | None = Field(None, description="国籍（代码）")
    newbornAge: float | None = Field(None, description="年龄不足1周岁的年龄天")


class FeeItem(BaseModel):
    """费用明细项"""

    uniq_field: str | None = Field(None, description="唯一id")
    create_date: int | None = Field(None, description="创建时间（毫秒，到秒）")
    jzsj: int | None = Field(None, description="记账时间（毫秒，到秒）")
    xh: str | None = Field(None, description="费用序号")
    tfxh: str | None = Field(None, description="退费序号")
    fydm: str | None = Field(None, description="费用代码")
    wjdm: str | None = Field(None, description="物价代码")
    fymc: str | None = Field(None, description="费用名称")
    je: float | None = Field(None, description="金额")
    ksdm: str | None = Field(None, description="执行科室代码")
    ksmc: str | None = Field(None, description="执行科室名称")
    xmdm: str | None = Field(None, description="项目代码")
    xmmc: str | None = Field(None, description="项目类别")
    ybdm: str | None = Field(None, description="医保代码")
    ybdmRaw: str | None = Field(None, description="医保代码原始")
    dj: float | None = Field(None, description="单价")
    sl: float | None = Field(None, description="数量")
    dw: str | None = Field(None, description="单位")
    ynxmmc: str | None = Field(None, description="院内项目名称")
    kdksdm: str | None = Field(None, description="开单科室代码")
    kdksmc: str | None = Field(None, description="开单科室名称")
    id: str | None = Field(None, description="id")
    bzjs: str | int | None = Field(None, description="病组结算")


class Process(BaseModel):
    """住院过程信息"""

    admissionRoute: str | None = Field(
        None, description="入院途径（代码，1.急诊2.门诊3.其他医疗机构转入9.其他）"
    )
    admissionDate: int | None = Field(None, description="入院时间（毫秒，到秒）")
    admissionDeptCode: str | None = Field(None, description="入院科别国家标准代码")
    admissionDeptCodeDescribe: str | None = Field(
        None, description="入院科别国家标准名称"
    )
    transferDeptCode: str | None = Field(None, description="转科科别国家标准代码")
    transferDeptCodeDescribe: str | None = Field(
        None, description="转科科别国家标准名称"
    )
    dischargeDate: int | None = Field(None, description="出院时间（毫秒，到秒）")
    dischargeDeptCode: str | None = Field(None, description="出院科别国家标准代码")
    dischargeDeptCodeDescribe: str | None = Field(
        None, description="出院科别国家标准名称"
    )
    settlementDate: int | None = Field(None, description="结算时间(毫秒)")


class DiagnosisList(BaseModel):
    """诊断列表"""

    diagnosisType: str | None = Field(None, description="诊断类型")
    isPrincipalDiagnosis: str | None = Field(None, description="是否主诊断")
    diagnosisName: str | None = Field(None, description="诊断名称")
    diagnosisICDCode: str | None = Field(None, description="诊断编码")
    conditionOnAdmission: str | None = Field(None, description="入院病情（代码）")
    situationAtDischarge: str | None = Field(None, description="出院病情（代码）")
    displayOrder: str | None = Field(None, description="序号")


class OperationList(BaseModel):
    """手术列表"""

    operationType: str | None = Field(
        None, description="手术类型，0:门诊手术， 1:住院手术"
    )
    operationName: str | None = Field(None, description="手术名称")
    operationICDCode: str | None = Field(None, description="手术编码")
    operationDate: int | None = Field(None, description="手术时间（毫秒，到秒）")
    operationStartTime: int | None = Field(
        None, description="手术开始时间（毫秒，到秒）"
    )
    operationEndTime: int | None = Field(None, description="手术结束时间（毫秒，到秒）")
    operationDuration: float | None = Field(None, description="手术持续时间（小时）")
    anesthesiaStartTime: int | None = Field(
        None, description="麻醉开始时间（毫秒，到秒）"
    )
    anesthesiaEndTime: int | None = Field(
        None, description="麻醉结束时间（毫秒，到秒）"
    )
    operationLevel: str | None = Field(None, description="手术级别")
    operationDrName: str | None = Field(None, description="手术医师")
    operationDrHealthCareCode: str | None = Field(
        None, description="术者医师代码（医保医师代码）"
    )
    operationDrCode: str | None = Field(None, description="术者医师编码")
    firstAssistantName: str | None = Field(None, description="第一助手")
    firstAssistantCode: str | None = Field(None, description="第一助手编码")
    secondAssistantName: str | None = Field(None, description="第二助手")
    secondAssistantCode: str | None = Field(None, description="第二助手编码")
    anesthesiaDrName: str | None = Field(None, description="麻醉医师")
    anesthesiaDrHealthCareCode: str | None = Field(
        None, description="麻醉医师代码（医保医师代码）"
    )
    anesthesiaDrCode: str | None = Field(None, description="麻醉医师编码")
    anesthesiaType: str | None = Field(None, description="麻醉方式")
    anesthesiaTypeDescribe: str | None = Field(None, description="麻醉方式描述")
    assistantLevel: str | None = Field(None, description="麻醉分级（代码）")
    incisionClass: str | None = Field(None, description="切口等级")
    healingClass: str | None = Field(None, description="愈合等级")
    combinedIncisionAndHealingCode: str | None = Field(
        None, description="切口愈合等级代码"
    )
    isMajorOperation: str | None = Field(
        None, description="是否为主要手术（代码，1是，0不是）"
    )
    operationIndex: str | None = Field(None, description="手术序号")


class DiagnoseInfo(BaseModel):
    """诊疗信息"""

    outPatientDiagnosisICDCode: str | None = Field(
        None, description="门急诊西医疾病代码"
    )
    outPatientDiagnosisChICDCode: str | None = Field(
        None, description="门急诊中医疾病代码"
    )
    diagnoseNameOnAdmission: str | None = Field(None, description="入院诊断名称")
    diagnoseICDCodeOnAdmission: str | None = Field(None, description="入院诊断编码")
    diagnosis: list[DiagnosisList] | None = Field(None, description="出院/门诊诊断")
    operation: list[OperationList] | None = Field(None, description="手术及操作")


class PatientData(BaseModel):
    """患者完整数据模型"""

    bah: str = Field(..., description="病案号")
    dataSource: str | None = Field(None, description="数据来源")
    patientMedicalInsuranceType: str | None = Field(None, description="患者医保类型")
    surgicalDepartmentCode: str | None = Field(None, description="手术科室编码")
    settlementOrNot: str | None = Field(None, description="是否结算")
    insuredPlace: str | None = Field(None, description="参保地")
    medicalCategory: str | None = Field(None, description="医疗类别")
    medicalInsuranceType: str | None = Field(None, description="医保类型")

    basic_information: PatientBasicInfo | None = Field(None, description="患者基本信息")
    process: Process | None = Field(None, description="住院过程信息")
    Diagnosis: DiagnoseInfo | None = Field(None, description="诊疗信息")
    fees: list[FeeItem] | None = Field(None, description="费用明细列表")
