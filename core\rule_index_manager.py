"""
规则索引管理器模块 - 优化版本
实现高性能的规则预过滤索引系统，提升校验性能60-80%

主要功能：
1. 构建和管理多类型代码索引（医保代码、诊断代码、手术代码）
2. 支持完整匹配和前缀匹配两种查询模式
3. 提供线程安全的索引操作
4. 主从节点统一的索引构建逻辑

技术特点：
- 内存优化：使用tracemalloc精确计算内存使用，确保不超过规则数据的50%
- 查询性能：O(log n)或更优的查询时间复杂度
- 并发安全：支持多线程并发访问
- 容错性：异常情况下自动降级
- 架构统一：主从节点使用相同的索引构建逻辑

版本更新：
- v1.1: 修复内存计算问题，优化Trie树实现
- v1.1: 移除索引数据导入导出，改为统一构建逻辑
- v1.1: 增强性能监控和错误处理
"""

import json
import threading
import time
import tracemalloc
from dataclasses import dataclass
from typing import Any

from core.logging.logging_system import log as logger


@dataclass
class IndexMetadata:
    """索引元数据"""

    build_time: float
    rule_count: int
    index_size: int
    memory_usage_mb: float
    build_duration_ms: float

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "build_time": self.build_time,
            "rule_count": self.rule_count,
            "index_size": self.index_size,
            "memory_usage_mb": self.memory_usage_mb,
            "build_duration_ms": self.build_duration_ms,
        }


@dataclass
class DetailedPerformanceStats:
    """详细性能统计"""

    query_count: int
    avg_query_time_ms: float
    memory_usage_mb: float
    cache_hit_rate: float
    index_efficiency: float  # 索引效率（基于查询时间）
    error_rate: float
    total_rules_before_filter: int
    total_rules_after_filter: int
    overall_filter_rate: float

    def to_dict(self) -> dict[str, Any]:
        return {
            "query_count": self.query_count,
            "avg_query_time_ms": round(self.avg_query_time_ms, 2),
            "memory_usage_mb": round(self.memory_usage_mb, 2),
            "cache_hit_rate": round(self.cache_hit_rate, 2),
            "index_efficiency": round(self.index_efficiency, 2),
            "error_rate": round(self.error_rate, 4),
            "total_rules_before_filter": self.total_rules_before_filter,
            "total_rules_after_filter": self.total_rules_after_filter,
            "overall_filter_rate": round(self.overall_filter_rate, 2),
        }


@dataclass
class PatientCodeExtraction:
    """患者代码提取结果"""

    yb_codes: set[str]          # 医保代码集合
    diag_codes: set[str]        # 诊断代码集合
    surgery_codes: set[str]     # 手术代码集合
    extraction_time: float      # 提取耗时（毫秒）

    def get_all_codes(self) -> dict[str, set[str]]:
        """获取所有代码的字典格式"""
        return {
            "yb_codes": self.yb_codes,
            "diag_codes": self.diag_codes,
            "surgery_codes": self.surgery_codes,
        }


@dataclass
class FilterResult:
    """规则过滤结果"""

    original_rule_count: int    # 原始规则数量
    filtered_rule_count: int    # 过滤后规则数量
    filter_rate: float          # 过滤率
    filter_time: float          # 过滤耗时（毫秒）
    filtered_rule_ids: list[str] # 过滤后的规则ID列表

    @property
    def performance_gain(self) -> float:
        """性能提升比例"""
        if self.original_rule_count == 0:
            return 0.0
        return self.filter_rate


class OptimizedTrieNode:
    """优化的Trie树节点 - 减少内存开销和重复存储"""

    __slots__ = ["children", "is_end", "rule_ids"]  # 减少内存开销

    def __init__(self):
        self.children: dict[str, OptimizedTrieNode] = {}
        self.is_end: bool = False
        self.rule_ids: set[str] = set()  # 只在终端节点存储规则ID


class PrefixMatcher:
    """前缀匹配器，根据前缀规则数量选择最优算法"""

    def __init__(self, threshold: int = 500):
        self.threshold = threshold
        self.use_trie = False
        self.trie_root: OptimizedTrieNode | None = None
        self.prefix_map: dict[str, set[str]] = {}

    def build_index(self, prefixes_to_rules: dict[str, set[str]]) -> None:
        """构建前缀索引，自动选择最优算法"""
        prefix_count = len(prefixes_to_rules)

        if prefix_count > self.threshold:
            # 使用优化的Trie树实现
            self.use_trie = True
            self._build_optimized_trie(prefixes_to_rules)
            logger.info(f"使用优化Trie树构建前缀索引，前缀数量: {prefix_count}")
        else:
            # 使用预构建映射表
            self.use_trie = False
            self.prefix_map = prefixes_to_rules.copy()
            logger.info(f"使用映射表构建前缀索引，前缀数量: {prefix_count}")

    def _build_optimized_trie(self, prefixes_to_rules: dict[str, set[str]]) -> None:
        """构建优化的Trie树 - 只在终端节点存储规则ID，减少内存重复"""
        self.trie_root = OptimizedTrieNode()

        for prefix, rule_ids in prefixes_to_rules.items():
            current = self.trie_root
            for char in prefix:
                if char not in current.children:
                    current.children[char] = OptimizedTrieNode()
                current = current.children[char]
            # 只在终端节点存储规则ID，避免重复存储
            current.is_end = True
            current.rule_ids.update(rule_ids)

    def find_matching_rules(self, code: str) -> set[str]:
        """查找匹配的规则ID集合"""
        if self.use_trie:
            return self._find_by_trie(code)
        else:
            return self._find_by_mapping(code)

    def _find_by_trie(self, code: str) -> set[str]:
        """使用优化Trie树查找匹配的规则"""
        if not self.trie_root:
            return set()

        matching_rules = set()
        current = self.trie_root

        # 遍历代码的每个字符，查找所有匹配的前缀
        for char in code:
            if char in current.children:
                current = current.children[char]
                # 如果当前节点是终端节点，说明找到了一个匹配的前缀
                if current.is_end:
                    matching_rules.update(current.rule_ids)
            else:
                break

        return matching_rules

    def _find_by_mapping(self, code: str) -> set[str]:
        """使用映射表查找"""
        matching_rules = set()

        for prefix, rule_ids in self.prefix_map.items():
            if code.startswith(prefix):
                matching_rules.update(rule_ids)

        return matching_rules


class RuleIndexManager:
    """
    规则索引管理器 - Task 1.2多类型代码索引实现

    负责构建和管理所有类型的代码索引，提供高性能的规则查询服务
    支持完整匹配索引和前缀匹配索引，确保查询性能和内存使用的平衡

    Task 1.2实现特性:
    1. 医保代码索引：yb_code、fee_whole_code、fee_code_prefix
    2. 诊断代码索引：diag_whole_code、diag_code_prefix
    3. 手术代码索引：从extended_fields解析
    4. 通用规则标识：无特定代码限制的规则
    5. 增量更新机制：支持单个规则的增删改
    6. 异常数据处理：完善的错误处理和数据验证
    """

    def __init__(self):
        # 线程锁，确保并发安全
        self._lock = threading.RLock()

        # 完整匹配索引：代码 -> 规则ID集合
        self.exact_match_indexes: dict[str, dict[str, set[str]]] = {
            "yb_code": {},          # 医保代码(yb_code) -> 规则ID集合
            "diag_code": {},        # 诊断代码(diag_whole_code) -> 规则ID集合
            "surgery_code": {},     # 手术代码(extended_fields) -> 规则ID集合
            "fee_code": {},         # 收费项目代码(fee_whole_code) -> 规则ID集合
        }

        # 前缀匹配器
        self.prefix_matchers: dict[str, PrefixMatcher] = {
            "yb_code_prefix": PrefixMatcher(),      # 医保代码前缀匹配
            "diag_code_prefix": PrefixMatcher(),    # 诊断代码前缀匹配
            "fee_code_prefix": PrefixMatcher(),     # 收费代码前缀匹配
        }

        # 通用规则（无特定代码限制的规则）
        self.universal_rules: set[str] = set()

        # 索引元数据
        self.index_metadata: IndexMetadata | None = None

        # 性能统计
        self._query_count = 0
        self._total_query_time = 0.0

        # 增量更新追踪
        self._indexed_rules: dict[str, dict[str, Any]] = {}  # rule_id -> rule_data快照
        self._update_count = 0
        self._last_update_time = 0.0

        # 异常统计
        self._parse_error_count = 0
        self._validation_error_count = 0

    def build_indexes_from_rule_details(self, rule_details: list[Any]) -> None:
        """
        从规则明细列表构建索引

        Args:
            rule_details: RuleDetail对象列表
        """
        with self._lock:
            start_time = time.perf_counter()
            logger.info(f"开始构建规则索引，规则数量: {len(rule_details)}")

            # 清空现有索引
            self._clear_indexes()

            # 临时索引数据
            exact_indexes = {key: {} for key in self.exact_match_indexes.keys()}
            prefix_indexes = {key: {} for key in self.prefix_matchers.keys()}

            # 遍历规则明细构建索引
            processed_count = 0
            error_count = 0

            for rule_detail in rule_details:
                try:
                    # Task 1.2: 数据验证
                    if not self._validate_rule_detail(rule_detail):
                        error_count += 1
                        continue

                    rule_id = rule_detail.rule_id

                    # 处理医保代码
                    self._process_yb_codes(rule_detail, rule_id, exact_indexes, prefix_indexes)

                    # 处理诊断代码
                    self._process_diag_codes(rule_detail, rule_id, exact_indexes, prefix_indexes)

                    # 处理收费项目代码
                    self._process_fee_codes(rule_detail, rule_id, exact_indexes, prefix_indexes)

                    # 处理手术代码（从扩展字段提取）
                    self._process_surgery_codes(rule_detail, rule_id, exact_indexes)

                    # 检查是否为通用规则
                    if self._is_universal_rule(rule_detail):
                        self.universal_rules.add(rule_id)

                    # 保存规则快照（用于增量更新）
                    self._indexed_rules[rule_id] = self._create_rule_snapshot(rule_detail)
                    processed_count += 1

                except Exception as e:
                    error_count += 1
                    rule_id = getattr(rule_detail, "rule_id", "unknown")
                    logger.error(f"处理规则失败 rule_id={rule_id}: {e}")

            if error_count > 0:
                logger.warning(f"索引构建完成，但有{error_count}个规则处理失败，成功处理{processed_count}个规则")

            # 构建完整匹配索引
            self.exact_match_indexes = exact_indexes

            # 构建前缀匹配索引
            for key, matcher in self.prefix_matchers.items():
                matcher.build_index(prefix_indexes[key])

            # 计算索引元数据
            build_time = time.perf_counter() - start_time
            self._calculate_index_metadata(len(rule_details), build_time)

            logger.info(
                f"索引构建完成 - 耗时: {build_time * 1000:.2f}ms, "
                f"内存使用: {self.index_metadata.memory_usage_mb:.2f}MB, "
                f"通用规则: {len(self.universal_rules)}个"
            )

    def _clear_indexes(self) -> None:
        """清空所有索引"""
        for index in self.exact_match_indexes.values():
            index.clear()

        for matcher in self.prefix_matchers.values():
            matcher.prefix_map.clear()
            matcher.trie_root = None

        self.universal_rules.clear()

    def _process_yb_codes(
        self,
        rule_detail: Any,
        rule_id: str, 
        exact_indexes: dict,
        prefix_indexes: dict
    ) -> None:
        """处理医保代码索引"""
        # 处理完整医保代码
        if hasattr(rule_detail, "yb_code") and rule_detail.yb_code:
            codes = self._parse_comma_separated(rule_detail.yb_code)
            for code in codes:
                if code not in exact_indexes["yb_code"]:
                    exact_indexes["yb_code"][code] = set()
                exact_indexes["yb_code"][code].add(rule_id)

        # 处理医保代码前缀
        if hasattr(rule_detail, "fee_code_prefix") and rule_detail.fee_code_prefix:
            prefixes = self._parse_comma_separated(rule_detail.fee_code_prefix)
            for prefix in prefixes:
                if prefix not in prefix_indexes["yb_code_prefix"]:
                    prefix_indexes["yb_code_prefix"][prefix] = set()
                prefix_indexes["yb_code_prefix"][prefix].add(rule_id)

    def _process_diag_codes(
            self,
        rule_detail: Any,
        rule_id: str,
        exact_indexes: dict,
        prefix_indexes: dict
    ) -> None:
        """处理诊断代码索引"""
        # 处理完整诊断代码
        if hasattr(rule_detail, "diag_whole_code") and rule_detail.diag_whole_code:
            codes = self._parse_comma_separated(rule_detail.diag_whole_code)
            for code in codes:
                if code not in exact_indexes["diag_code"]:
                    exact_indexes["diag_code"][code] = set()
                exact_indexes["diag_code"][code].add(rule_id)

        # 处理诊断代码前缀
        if hasattr(rule_detail, "diag_code_prefix") and rule_detail.diag_code_prefix:
            prefixes = self._parse_comma_separated(rule_detail.diag_code_prefix)
            for prefix in prefixes:
                if prefix not in prefix_indexes["diag_code_prefix"]:
                    prefix_indexes["diag_code_prefix"][prefix] = set()
                prefix_indexes["diag_code_prefix"][prefix].add(rule_id)

    def _process_fee_codes(
        self,
        rule_detail: Any,
        rule_id: str,
        exact_indexes: dict,
        prefix_indexes: dict
    ) -> None:
        """处理收费项目代码索引"""
        # 处理完整收费代码
        if hasattr(rule_detail, "fee_whole_code") and rule_detail.fee_whole_code:
            codes = self._parse_comma_separated(rule_detail.fee_whole_code)
            for code in codes:
                if code not in exact_indexes["fee_code"]:
                    exact_indexes["fee_code"][code] = set()
                exact_indexes["fee_code"][code].add(rule_id)

        # 处理收费代码前缀
        if hasattr(rule_detail, "fee_code_prefix") and rule_detail.fee_code_prefix:
            prefixes = self._parse_comma_separated(rule_detail.fee_code_prefix)
            for prefix in prefixes:
                if prefix not in prefix_indexes["fee_code_prefix"]:
                    prefix_indexes["fee_code_prefix"][prefix] = set()
                prefix_indexes["fee_code_prefix"][prefix].add(rule_id)

    def _process_surgery_codes(self, rule_detail: Any, rule_id: str, exact_indexes: dict) -> None:
        """处理手术代码索引（从扩展字段提取）- Task 1.2增强版"""
        if hasattr(rule_detail, "extended_fields") and rule_detail.extended_fields:
            # 使用安全JSON解析
            extended_data = self._safe_parse_json_field(rule_detail.extended_fields, rule_id)

            if extended_data:
                # 提取手术相关字段
                surgery_fields = ["surgery_code", "operation_code", "surgical_code"]
                for field in surgery_fields:
                    if field in extended_data and extended_data[field]:
                        # 使用安全的逗号分隔解析
                        codes = self._safe_parse_comma_separated(
                            str(extended_data[field]), rule_id, f"extended_fields.{field}"
                        )
                        for code in codes:
                            if code not in exact_indexes["surgery_code"]:
                                exact_indexes["surgery_code"][code] = set()
                            exact_indexes["surgery_code"][code].add(rule_id)

    def _is_universal_rule(self, rule_detail: Any) -> bool:
        """判断是否为通用规则（无特定代码限制）"""
        # 检查所有代码字段是否为空
        code_fields = ["yb_code", "diag_whole_code", "diag_code_prefix", "fee_whole_code", "fee_code_prefix"]

        has_codes = False
        for field in code_fields:
            if hasattr(rule_detail, field):
                value = getattr(rule_detail, field)
                if value and value.strip():
                    has_codes = True
                    break

        # 检查扩展字段中的代码
        if not has_codes and hasattr(rule_detail, "extended_fields") and rule_detail.extended_fields:
            extended_data = self._safe_parse_json_field(
                rule_detail.extended_fields, getattr(rule_detail, "rule_id", "unknown")
            )
            surgery_fields = ["surgery_code", "operation_code", "surgical_code"]
            for field in surgery_fields:
                if field in extended_data and extended_data[field]:
                    has_codes = True
                    break

        return not has_codes

    def _parse_comma_separated(self, value: str, rule_id: str = None, field_name: str = None) -> list[str]:
        """解析逗号分隔的字符串（兼容性方法，调用安全解析）"""
        return self._safe_parse_comma_separated(value, rule_id, field_name)

    def find_relevant_rules(self, patient_codes: PatientCodeExtraction) -> set[str]:
        """
        根据患者代码查找相关规则

        Args:
            patient_codes: 患者代码提取结果

        Returns:
            Set[str]: 相关规则ID集合
        """
        with self._lock:
            start_time = time.perf_counter()
            relevant_rules = set()

            try:
                # 完整匹配查询
                code_mappings = {
                    "yb_codes": "yb_code",
                    "diag_codes": "diag_code",
                    "surgery_codes": "surgery_code"
                }

                for patient_field, index_key in code_mappings.items():
                    codes = getattr(patient_codes, patient_field, set())
                    index = self.exact_match_indexes.get(index_key, {})

                    for code in codes:
                        if code in index:
                            relevant_rules.update(index[code])

                # 处理收费代码（患者的yb_codes对应fee_code索引）
                fee_index = self.exact_match_indexes.get("fee_code", {})
                for code in patient_codes.yb_codes:
                    if code in fee_index:
                        relevant_rules.update(fee_index[code])

                # 前缀匹配查询
                prefix_mappings = {"yb_codes": "yb_code_prefix", "diag_codes": "diag_code_prefix"}

                for patient_field, matcher_key in prefix_mappings.items():
                    codes = getattr(patient_codes, patient_field, set())
                    matcher = self.prefix_matchers.get(matcher_key)

                    if matcher:
                        for code in codes:
                            matching_rules = matcher.find_matching_rules(code)
                            relevant_rules.update(matching_rules)

                # 收费代码前缀匹配
                fee_matcher = self.prefix_matchers.get("fee_code_prefix")
                if fee_matcher:
                    for code in patient_codes.yb_codes:
                        matching_rules = fee_matcher.find_matching_rules(code)
                        relevant_rules.update(matching_rules)

                # 添加通用规则
                relevant_rules.update(self.universal_rules)

                # 更新性能统计
                query_time = (time.perf_counter() - start_time) * 1000
                self._query_count += 1
                self._total_query_time += query_time

                logger.debug(
                    f"规则查询完成 - 耗时: {query_time:.2f}ms, "
                    f"找到相关规则: {len(relevant_rules)}个"
                )

                return relevant_rules

            except Exception as e:
                logger.error(f"规则查询失败: {e}", exc_info=True)
                # 异常情况下返回通用规则，确保基本功能
                return self.universal_rules.copy()

    def filter_rules(self, patient_codes: PatientCodeExtraction, requested_rule_ids: list[str]) -> FilterResult:
        """
        规则过滤核心算法

        Args:
            patient_codes: 患者代码提取结果
            requested_rule_ids: 请求的规则ID列表

        Returns:
            FilterResult: 过滤结果统计
        """
        start_time = time.perf_counter()

        try:
            # 查找相关规则
            relevant_rules = self.find_relevant_rules(patient_codes)

            # 与请求规则求交集
            requested_set = set(requested_rule_ids)
            filtered_rules = list(relevant_rules & requested_set)

            # 计算统计信息
            filter_time = (time.perf_counter() - start_time) * 1000
            original_count = len(requested_rule_ids)
            filtered_count = len(filtered_rules)
            filter_rate = 1 - (filtered_count / original_count) if original_count > 0 else 0.0

            return FilterResult(
                original_rule_count=original_count,
                filtered_rule_count=filtered_count,
                filter_rate=filter_rate,
                filter_time=filter_time,
                filtered_rule_ids=filtered_rules,
            )

        except Exception as e:
            logger.error(f"规则过滤失败: {e}", exc_info=True)
            # 异常情况下返回原始规则列表（不过滤）
            return FilterResult(
                original_rule_count=len(requested_rule_ids),
                filtered_rule_count=len(requested_rule_ids),
                filter_rate=0.0,
                filter_time=(time.perf_counter() - start_time) * 1000,
                filtered_rule_ids=requested_rule_ids,
            )

    def _calculate_index_metadata(self, rule_count: int, build_time: float) -> None:
        """计算索引元数据 - 使用精确的内存计算方法"""
        # 使用tracemalloc获取精确的内存使用情况
        memory_usage_mb = self._get_accurate_memory_usage()

        # 计算索引大小
        total_index_size = 0
        for index in self.exact_match_indexes.values():
            total_index_size += len(index)

        # 添加前缀索引大小
        for matcher in self.prefix_matchers.values():
            if matcher.use_trie and matcher.trie_root:
                total_index_size += self._count_trie_nodes(matcher.trie_root)
            else:
                total_index_size += len(matcher.prefix_map)

        self.index_metadata = IndexMetadata(
            build_time=time.time(),
            rule_count=rule_count,
            index_size=total_index_size,
            memory_usage_mb=memory_usage_mb,
            build_duration_ms=build_time * 1000,
        )

        # 验证内存使用是否超过50%限制
        if memory_usage_mb > 0:
            estimated_rule_data_size = rule_count * 0.5  # 估算每个规则0.5MB
            memory_ratio = memory_usage_mb / estimated_rule_data_size
            if memory_ratio > 0.5:
                logger.warning(
                    f"索引内存使用可能超过50%限制: {memory_usage_mb:.2f}MB "
                    f"(估算规则数据: {estimated_rule_data_size:.2f}MB, 比例: {memory_ratio:.1%})"
                )

    def _get_accurate_memory_usage(self) -> float:
        """获取精确的内存使用情况"""
        try:
            # 启动内存跟踪
            tracemalloc.start()

            # 触发垃圾回收以获得更准确的内存使用
            import gc

            gc.collect()

            # 获取当前内存使用
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()

            return current / (1024 * 1024)  # 转换为MB

        except Exception as e:
            logger.warning(f"无法获取精确内存使用，使用估算值: {e}")
            # 回退到简化计算
            return self._estimate_memory_usage()

    def _estimate_memory_usage(self) -> float:
        """估算内存使用（回退方法）"""
        import sys

        memory_usage = 0

        # 完整匹配索引内存
        for index in self.exact_match_indexes.values():
            for code, rule_set in index.items():
                memory_usage += sys.getsizeof(code) + sys.getsizeof(rule_set)
                memory_usage += sum(sys.getsizeof(rule_id) for rule_id in rule_set)

        # 前缀匹配器内存
        for matcher in self.prefix_matchers.values():
            if matcher.use_trie and matcher.trie_root:
                memory_usage += self._estimate_trie_memory(matcher.trie_root)
            else:
                memory_usage += sys.getsizeof(matcher.prefix_map)

        # 通用规则内存
        memory_usage += sys.getsizeof(self.universal_rules)
        memory_usage += sum(sys.getsizeof(rule_id) for rule_id in self.universal_rules)

        return memory_usage / (1024 * 1024)

    def _count_trie_nodes(self, node: OptimizedTrieNode) -> int:
        """递归计算Trie树节点数量"""
        count = 1  # 当前节点
        for child in node.children.values():
            count += self._count_trie_nodes(child)
        return count

    def _estimate_trie_memory(self, node: OptimizedTrieNode) -> int:
        """递归估算Trie树内存使用"""
        import sys

        memory = sys.getsizeof(node.children) + sys.getsizeof(node.rule_ids)
        memory += sum(sys.getsizeof(rule_id) for rule_id in node.rule_ids)

        for child in node.children.values():
            memory += self._estimate_trie_memory(child)

        return memory

    def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计信息"""
        with self._lock:
            avg_query_time = self._total_query_time / self._query_count if self._query_count > 0 else 0.0

            return {
                "query_count": self._query_count,
                "total_query_time_ms": round(self._total_query_time, 2),
                "avg_query_time_ms": round(avg_query_time, 2),
                "index_metadata": self.index_metadata.to_dict() if self.index_metadata else None,
                "universal_rules_count": len(self.universal_rules),
                "exact_index_sizes": {key: len(index) for key, index in self.exact_match_indexes.items()},
                # Task 1.2: 增量更新和错误统计
                "incremental_update_stats": {
                    "update_count": self._update_count,
                    "last_update_time": self._last_update_time,
                    "indexed_rules_count": len(self._indexed_rules),
                },
                "error_stats": self.get_error_statistics(),
            }

    def get_detailed_performance_stats(self) -> DetailedPerformanceStats:
        """获取详细性能统计"""
        with self._lock:
            return DetailedPerformanceStats(
                query_count=self._query_count,
                avg_query_time_ms=self._total_query_time / max(self._query_count, 1),
                memory_usage_mb=self.index_metadata.memory_usage_mb if self.index_metadata else 0.0,
                cache_hit_rate=0.0,  # 暂未实现缓存
                index_efficiency=self._calculate_index_efficiency(),
                error_rate=self._error_count / max(self._query_count, 1),
                total_rules_before_filter=getattr(self, "_total_rules_before_filter", 0),
                total_rules_after_filter=getattr(self, "_total_rules_after_filter", 0),
                overall_filter_rate=self._calculate_overall_filter_rate(),
            )

    def _calculate_index_efficiency(self) -> float:
        """计算索引效率（基于查询时间）"""
        if self._query_count == 0:
            return 0.0

        avg_time = self._total_query_time / self._query_count
        # 假设5ms以下为高效，10ms以上为低效
        if avg_time <= 5.0:
            return 1.0
        elif avg_time >= 10.0:
            return 0.0
        else:
            return 1.0 - (avg_time - 5.0) / 5.0

    def _calculate_overall_filter_rate(self) -> float:
        """计算总体过滤率"""
        total_before = getattr(self, "_total_rules_before_filter", 0)
        total_after = getattr(self, "_total_rules_after_filter", 0)

        if total_before == 0:
            return 0.0

        return 1.0 - (total_after / total_before)

    def is_ready(self) -> bool:
        """检查索引是否就绪"""
        with self._lock:
            return self.index_metadata is not None

    # ==================== Task 1.2: 增量更新机制 ====================

    def update_single_rule(self, rule_detail: Any) -> bool:
        """
        增量更新单个规则索引

        Args:
            rule_detail: 规则明细对象

        Returns:
            bool: 更新是否成功
        """
        with self._lock:
            try:
                rule_id = rule_detail.rule_id

                # 如果规则已存在，先删除旧索引
                if rule_id in self._indexed_rules:
                    self._remove_rule_from_indexes(rule_id)

                # 创建临时索引数据
                exact_indexes = {key: {} for key in self.exact_match_indexes.keys()}
                prefix_indexes = {key: {} for key in self.prefix_matchers.keys()}

                # 构建新索引
                self._process_single_rule(rule_detail, exact_indexes, prefix_indexes)

                # 合并到主索引
                self._merge_indexes(exact_indexes, prefix_indexes)

                # 保存规则快照用于后续删除
                self._indexed_rules[rule_id] = self._create_rule_snapshot(rule_detail)

                # 更新统计
                self._update_count += 1
                self._last_update_time = time.time()

                logger.info(f"成功更新规则索引: {rule_id}")
                return True

            except Exception as e:
                logger.error(f"更新规则索引失败 rule_id={getattr(rule_detail, 'rule_id', 'unknown')}: {e}")
                self._validation_error_count += 1
                return False

    def remove_rule(self, rule_id: str) -> bool:
        """
        从索引中删除规则

        Args:
            rule_id: 规则ID

        Returns:
            bool: 删除是否成功
        """
        with self._lock:
            try:
                if rule_id not in self._indexed_rules:
                    logger.warning(f"尝试删除不存在的规则: {rule_id}")
                    return False

                # 从索引中移除
                self._remove_rule_from_indexes(rule_id)

                # 删除规则快照
                del self._indexed_rules[rule_id]

                # 更新统计
                self._update_count += 1
                self._last_update_time = time.time()

                logger.info(f"成功删除规则索引: {rule_id}")
                return True

            except Exception as e:
                logger.error(f"删除规则索引失败 rule_id={rule_id}: {e}")
                return False

    def batch_update_rules(self, rule_details: list[Any]) -> dict[str, bool]:
        """
        批量增量更新规则

        Args:
            rule_details: 规则明细列表

        Returns:
            Dict[str, bool]: rule_id -> 更新结果映射
        """
        results = {}

        for rule_detail in rule_details:
            rule_id = getattr(rule_detail, "rule_id", "unknown")
            results[rule_id] = self.update_single_rule(rule_detail)

        logger.info(f"批量更新完成 - 成功: {sum(results.values())}, 失败: {len(results) - sum(results.values())}")
        return results

    def _process_single_rule(self, rule_detail: Any, exact_indexes: dict, prefix_indexes: dict) -> None:
        """处理单个规则的所有索引类型"""
        rule_id = rule_detail.rule_id

        # 处理医保代码
        self._process_yb_codes(rule_detail, rule_id, exact_indexes, prefix_indexes)

        # 处理诊断代码
        self._process_diag_codes(rule_detail, rule_id, exact_indexes, prefix_indexes)

        # 处理收费项目代码
        self._process_fee_codes(rule_detail, rule_id, exact_indexes, prefix_indexes)

        # 处理手术代码（从扩展字段提取）
        self._process_surgery_codes(rule_detail, rule_id, exact_indexes)

        # 检查是否为通用规则
        if self._is_universal_rule(rule_detail):
            self.universal_rules.add(rule_id)

    def _merge_indexes(self, exact_indexes: dict, prefix_indexes: dict) -> None:
        """将临时索引合并到主索引"""
        # 合并完整匹配索引
        for index_key, temp_index in exact_indexes.items():
            main_index = self.exact_match_indexes[index_key]
            for code, rule_set in temp_index.items():
                if code not in main_index:
                    main_index[code] = set()
                main_index[code].update(rule_set)

        # 重建前缀匹配索引（简化实现，实际场景可优化）
        for matcher_key, temp_prefix_index in prefix_indexes.items():
            if temp_prefix_index:  # 只有在有数据时才重建
                matcher = self.prefix_matchers[matcher_key]
                # 获取现有前缀数据
                existing_prefixes = matcher.prefix_map.copy() if not matcher.use_trie else {}
                # 合并新数据
                for prefix, rule_set in temp_prefix_index.items():
                    if prefix not in existing_prefixes:
                        existing_prefixes[prefix] = set()
                    existing_prefixes[prefix].update(rule_set)
                # 重建索引
                matcher.build_index(existing_prefixes)

    def _remove_rule_from_indexes(self, rule_id: str) -> None:
        """从所有索引中移除指定规则"""
        # 从完整匹配索引中移除
        for index in self.exact_match_indexes.values():
            codes_to_remove = []
            for code, rule_set in index.items():
                rule_set.discard(rule_id)
                if not rule_set:  # 如果集合为空，标记删除
                    codes_to_remove.append(code)
            # 删除空的代码条目
            for code in codes_to_remove:
                del index[code]

        # 从通用规则中移除
        self.universal_rules.discard(rule_id)

        # 前缀匹配索引的移除比较复杂，这里简化处理
        # 实际场景中可考虑延迟清理或定期重建
        for matcher in self.prefix_matchers.values():
            if not matcher.use_trie:
                codes_to_remove = []
                for prefix, rule_set in matcher.prefix_map.items():
                    rule_set.discard(rule_id)
                    if not rule_set:
                        codes_to_remove.append(prefix)
                for prefix in codes_to_remove:
                    del matcher.prefix_map[prefix]

    def _create_rule_snapshot(self, rule_detail: Any) -> dict[str, Any]:
        """创建规则数据快照用于增量更新追踪"""
        snapshot = {"rule_id": rule_detail.rule_id, "timestamp": time.time()}

        # 保存关键字段
        fields = ["yb_code", "diag_whole_code", "diag_code_prefix", "fee_whole_code", "fee_code_prefix", "extended_fields"]

        for field in fields:
            if hasattr(rule_detail, field):
                snapshot[field] = getattr(rule_detail, field)

        return snapshot

    # ==================== Task 1.2: 异常数据处理完善 ====================

    def _validate_rule_detail(self, rule_detail: Any) -> bool:
        """验证规则数据的完整性和格式"""
        try:
            # 检查必要字段
            if not hasattr(rule_detail, "rule_id") or not rule_detail.rule_id:
                logger.warning("规则缺少必要的rule_id字段")
                self._validation_error_count += 1
                return False

            # 验证rule_id格式（基本检查）
            if not isinstance(rule_detail.rule_id, str) or len(rule_detail.rule_id.strip()) == 0:
                logger.warning(f"规则ID格式无效: {rule_detail.rule_id}")
                self._validation_error_count += 1
                return False

            # 验证扩展字段JSON格式
            if hasattr(rule_detail, "extended_fields") and rule_detail.extended_fields:
                try:
                    if rule_detail.extended_fields.strip():
                        json.loads(rule_detail.extended_fields)
                except (json.JSONDecodeError, AttributeError) as e:
                    logger.warning(f"规则{rule_detail.rule_id}的扩展字段JSON格式无效: {e}")
                    self._parse_error_count += 1
                    # 不返回False，允许处理其他字段

            return True

        except Exception as e:
            logger.error(f"验证规则数据时出错: {e}")
            self._validation_error_count += 1
            return False

    def _safe_parse_comma_separated(self, value: str, rule_id: str = None, field_name: str = None) -> list[str]:
        """安全解析逗号分隔的字符串，包含错误处理"""
        try:
            if not value or not isinstance(value, str):
                return []

            # 清理和分割
            codes = []
            for code in value.split(","):
                cleaned_code = code.strip()
                if cleaned_code:
                    # 基本格式验证（可根据具体业务规则扩展）
                    if len(cleaned_code) > 50:  # 防止异常长度的代码
                        logger.warning(f"代码长度异常 rule_id={rule_id} field={field_name} code={cleaned_code[:20]}...")
                        continue
                    codes.append(cleaned_code)

            return codes

        except Exception as e:
            logger.error(f"解析逗号分隔字符串失败 rule_id={rule_id} field={field_name}: {e}")
            self._parse_error_count += 1
            return []

    def _safe_parse_json_field(self, json_str: str, rule_id: str = None) -> dict[str, Any]:
        """安全解析JSON字段"""
        try:
            if not json_str or not isinstance(json_str, str):
                return {}

            cleaned_json = json_str.strip()
            if not cleaned_json:
                return {}

            data = json.loads(cleaned_json)

            # 确保返回字典类型
            if not isinstance(data, dict):
                logger.warning(f"扩展字段不是字典类型 rule_id={rule_id}")
                return {}

            return data

        except (json.JSONDecodeError, TypeError) as e:
            logger.warning(f"解析JSON字段失败 rule_id={rule_id}: {e}")
            self._parse_error_count += 1
            return {}

    def get_error_statistics(self) -> dict[str, Any]:
        """获取错误统计信息"""
        return {
            "parse_error_count": self._parse_error_count,
            "validation_error_count": self._validation_error_count,
            "total_errors": self._parse_error_count + self._validation_error_count,
            "update_count": self._update_count,
            "last_update_time": self._last_update_time,
            "indexed_rules_count": len(self._indexed_rules),
        }


# 全局实例
rule_index_manager = RuleIndexManager()
