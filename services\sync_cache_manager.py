"""
同步缓存管理器

基于现有IntelligentCacheManager，实现同步特定的缓存管理功能。
复用现有的三级LRU缓存机制，添加同步相关的缓存类型。
"""

from typing import Any

from core.intelligent_cache import CacheType, IntelligentCacheManager, LRUCache
from core.logging.logging_system import log as logger
from models.sync import PackageInfo, SyncResponse


class SyncCacheManager:
    """
    同步缓存管理器

    基于现有的IntelligentCacheManager，提供同步特定的缓存功能，
    包括版本缓存、变更缓存、包缓存和同步结果缓存。
    """

    def __init__(self, intelligent_cache_manager: IntelligentCacheManager | None = None):
        """
        初始化同步缓存管理器

        Args:
            intelligent_cache_manager: 现有的智能缓存管理器实例，如果为None则创建新实例
        """
        # 复用现有的IntelligentCacheManager或创建新实例
        self.cache_manager = intelligent_cache_manager or IntelligentCacheManager()

        # 创建同步专用的缓存实例
        self._init_sync_caches()

        logger.info("SyncCacheManager initialized")

    def _init_sync_caches(self):
        """初始化同步专用缓存"""
        # 版本缓存（长期缓存，用于版本比较）
        self.version_cache = LRUCache(
            max_size=10000,
            max_memory_mb=20,
            ttl_seconds=86400,  # 24小时
            cleanup_interval=3600,
        )

        # 变更缓存（中期缓存，用于增量同步）
        self.changes_cache = LRUCache(
            max_size=1000,
            max_memory_mb=100,
            ttl_seconds=7200,  # 2小时
            cleanup_interval=600,
        )

        # 包信息缓存（中期缓存，用于离线包管理）
        self.package_cache = LRUCache(
            max_size=500,
            max_memory_mb=50,
            ttl_seconds=3600,  # 1小时
            cleanup_interval=300,
        )

        # 同步结果缓存（短期缓存，用于同步状态查询）
        self.sync_result_cache = LRUCache(
            max_size=1000,
            max_memory_mb=30,
            ttl_seconds=1800,  # 30分钟
            cleanup_interval=180,
        )

    def get_version_cache(self, rule_key: str) -> str | None:
        """
        获取规则版本缓存

        Args:
            rule_key: 规则键

        Returns:
            Optional[str]: 缓存的版本号，如果不存在则返回None
        """
        cache_key = f"version:{rule_key}"
        version = self.version_cache.get(cache_key)

        if version:
            logger.debug(f"版本缓存命中: {rule_key} -> {version}")
        else:
            logger.debug(f"版本缓存未命中: {rule_key}")

        return version

    def cache_version(self, rule_key: str, version: str, ttl_seconds: int | None = None):
        """
        缓存规则版本

        Args:
            rule_key: 规则键
            version: 版本号
            ttl_seconds: 自定义TTL，如果为None则使用默认TTL
        """
        cache_key = f"version:{rule_key}"
        success = self.version_cache.put(cache_key, version, CacheType.SYNC_VERSION)

        if success:
            logger.debug(f"版本缓存成功: {rule_key} -> {version}")
        else:
            logger.warning(f"版本缓存失败: {rule_key} -> {version}")

    def get_cached_changes(self, from_version: str, to_version: str) -> list[dict[str, Any]] | None:
        """
        获取缓存的变更数据

        Args:
            from_version: 源版本
            to_version: 目标版本

        Returns:
            Optional[List[Dict[str, Any]]]: 缓存的变更列表，如果不存在则返回None
        """
        cache_key = f"changes:{from_version}:{to_version}"
        changes = self.changes_cache.get(cache_key)

        if changes:
            logger.debug(f"变更缓存命中: {from_version} -> {to_version}, {len(changes)} 个变更")
        else:
            logger.debug(f"变更缓存未命中: {from_version} -> {to_version}")

        return changes

    def cache_changes(
        self, from_version: str, to_version: str, changes: list[dict[str, Any]], ttl_seconds: int | None = None
    ):
        """
        缓存变更数据

        Args:
            from_version: 源版本
            to_version: 目标版本
            changes: 变更列表
            ttl_seconds: 自定义TTL，如果为None则使用默认TTL
        """
        cache_key = f"changes:{from_version}:{to_version}"
        success = self.changes_cache.put(cache_key, changes, CacheType.SYNC_CHANGES)

        if success:
            logger.debug(f"变更缓存成功: {from_version} -> {to_version}, {len(changes)} 个变更")
        else:
            logger.warning(f"变更缓存失败: {from_version} -> {to_version}")

    def get_package_info(self, package_id: str) -> PackageInfo | None:
        """
        获取缓存的包信息

        Args:
            package_id: 包ID

        Returns:
            Optional[PackageInfo]: 缓存的包信息，如果不存在则返回None
        """
        cache_key = f"package:{package_id}"
        package_info = self.package_cache.get(cache_key)

        if package_info:
            logger.debug(f"包信息缓存命中: {package_id}")
        else:
            logger.debug(f"包信息缓存未命中: {package_id}")

        return package_info

    def cache_package_info(self, package_info: PackageInfo, ttl_seconds: int | None = None):
        """
        缓存包信息

        Args:
            package_info: 包信息
            ttl_seconds: 自定义TTL，如果为None则使用默认TTL
        """
        cache_key = f"package:{package_info.package_id}"
        success = self.package_cache.put(cache_key, package_info, CacheType.SYNC_PACKAGE)

        if success:
            logger.debug(f"包信息缓存成功: {package_info.package_id}")
        else:
            logger.warning(f"包信息缓存失败: {package_info.package_id}")

    def get_sync_result(self, sync_id: str) -> SyncResponse | None:
        """
        获取缓存的同步结果

        Args:
            sync_id: 同步任务ID

        Returns:
            Optional[SyncResponse]: 缓存的同步结果，如果不存在则返回None
        """
        cache_key = f"sync_result:{sync_id}"
        sync_result = self.sync_result_cache.get(cache_key)

        if sync_result:
            logger.debug(f"同步结果缓存命中: {sync_id}")
        else:
            logger.debug(f"同步结果缓存未命中: {sync_id}")

        return sync_result

    def cache_sync_result(self, sync_id: str, result: SyncResponse, ttl_seconds: int | None = None):
        """
        缓存同步结果

        Args:
            sync_id: 同步任务ID
            result: 同步结果
            ttl_seconds: 自定义TTL，如果为None则使用默认TTL
        """
        cache_key = f"sync_result:{sync_id}"
        success = self.sync_result_cache.put(cache_key, result, CacheType.SYNC_RESULT)

        if success:
            logger.debug(f"同步结果缓存成功: {sync_id}")
        else:
            logger.warning(f"同步结果缓存失败: {sync_id}")

    def invalidate_version_cache(self, rule_key: str):
        """
        使版本缓存失效

        Args:
            rule_key: 规则键
        """
        cache_key = f"version:{rule_key}"
        if self.version_cache.remove(cache_key):
            logger.debug(f"版本缓存失效: {rule_key}")

    def invalidate_changes_cache(self, from_version: str, to_version: str):
        """
        使变更缓存失效

        Args:
            from_version: 源版本
            to_version: 目标版本
        """
        cache_key = f"changes:{from_version}:{to_version}"
        if self.changes_cache.remove(cache_key):
            logger.debug(f"变更缓存失效: {from_version} -> {to_version}")

    def invalidate_package_cache(self, package_id: str):
        """
        使包信息缓存失效

        Args:
            package_id: 包ID
        """
        cache_key = f"package:{package_id}"
        if self.package_cache.remove(cache_key):
            logger.debug(f"包信息缓存失效: {package_id}")

    def clear_all_sync_caches(self):
        """清空所有同步相关缓存"""
        self.version_cache.clear()
        self.changes_cache.clear()
        self.package_cache.clear()
        self.sync_result_cache.clear()
        logger.info("所有同步缓存已清空")

    def get_cache_stats(self) -> dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        return {
            "version_cache": {
                "size": len(self.version_cache._cache),
                "max_size": self.version_cache.max_size,
                "hit_rate": self.version_cache._stats.hit_rate,
                "memory_usage_mb": self.version_cache._stats.total_size_bytes / (1024 * 1024),
            },
            "changes_cache": {
                "size": len(self.changes_cache._cache),
                "max_size": self.changes_cache.max_size,
                "hit_rate": self.changes_cache._stats.hit_rate,
                "memory_usage_mb": self.changes_cache._stats.total_size_bytes / (1024 * 1024),
            },
            "package_cache": {
                "size": len(self.package_cache._cache),
                "max_size": self.package_cache.max_size,
                "hit_rate": self.package_cache._stats.hit_rate,
                "memory_usage_mb": self.package_cache._stats.total_size_bytes / (1024 * 1024),
            },
            "sync_result_cache": {
                "size": len(self.sync_result_cache._cache),
                "max_size": self.sync_result_cache.max_size,
                "hit_rate": self.sync_result_cache._stats.hit_rate,
                "memory_usage_mb": self.sync_result_cache._stats.total_size_bytes / (1024 * 1024),
            },
        }

    def optimize_cache_performance(self):
        """优化缓存性能"""
        # 触发各缓存的清理操作
        self.version_cache._cleanup_expired()
        self.changes_cache._cleanup_expired()
        self.package_cache._cleanup_expired()
        self.sync_result_cache._cleanup_expired()

        logger.info("同步缓存性能优化完成")


# 全局同步缓存管理器实例
_sync_cache_manager: SyncCacheManager | None = None


def get_sync_cache_manager() -> SyncCacheManager:
    """
    获取全局同步缓存管理器实例

    Returns:
        SyncCacheManager: 同步缓存管理器实例
    """
    global _sync_cache_manager
    if _sync_cache_manager is None:
        _sync_cache_manager = SyncCacheManager()
    return _sync_cache_manager


def reset_sync_cache_manager():
    """重置全局同步缓存管理器实例（主要用于测试）"""
    global _sync_cache_manager
    _sync_cache_manager = None
