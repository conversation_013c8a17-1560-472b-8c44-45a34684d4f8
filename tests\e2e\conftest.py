"""
端到端测试专用配置
提供端到端测试所需的fixtures和配置
"""

import pytest

# ============================================================================
# 端到端测试专用fixtures
# ============================================================================

@pytest.fixture(scope="session")
def test_client():
    """测试客户端（端到端测试专用）"""
    # 这里需要导入实际的应用
    # from master import app
    # return TestClient(app)

    # 暂时返回Mock，等待实际应用集成
    from unittest.mock import Mock
    return Mock()


@pytest.fixture
def e2e_test_data():
    """端到端测试数据"""
    return {
        "test_user": {
            "username": "test_user",
            "password": "test_password",
        },
        "test_rule": {
            "rule_key": "e2e_test_rule",
            "rule_name": "端到端测试规则",
        }
    }


# ============================================================================
# 端到端测试配置
# ============================================================================

@pytest.fixture(autouse=True)
def e2e_test_setup():
    """端到端测试自动设置"""
    # 端到端测试需要完整的应用环境
    # 包括数据库、缓存、外部服务等
    yield
