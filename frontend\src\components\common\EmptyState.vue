<template>
  <div class="empty-state">
    <div class="empty-content">
      <!-- 图标 -->
      <div class="empty-icon">
        <el-icon class="icon" :size="iconSize">
          <component :is="currentIcon" />
        </el-icon>
      </div>

      <!-- 标题 -->
      <h3 class="empty-title">{{ title }}</h3>

      <!-- 描述 -->
      <p class="empty-description">{{ description }}</p>

      <!-- 操作按钮 -->
      <div class="empty-actions" v-if="showActions">
        <el-button
          v-if="showRetry"
          type="primary"
          :icon="Refresh"
          @click="$emit('retry')"
          class="action-btn"
        >
          重试
        </el-button>

        <el-button
          v-if="showCreate"
          type="success"
          :icon="Plus"
          @click="$emit('create')"
          class="action-btn"
        >
          {{ createText }}
        </el-button>

        <el-button
          v-if="showBack"
          :icon="ArrowLeft"
          @click="$emit('back')"
          class="action-btn"
        >
          返回
        </el-button>
      </div>

      <!-- 自定义操作插槽 -->
      <div class="custom-actions" v-if="$slots.actions">
        <slot name="actions" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  DocumentRemove,
  Search,
  Warning,
  InfoFilled,
  Refresh,
  Plus,
  ArrowLeft
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  type: {
    type: String,
    default: 'empty',
    validator: (value) => ['empty', 'search', 'error', 'loading'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  icon: {
    type: [String, Object],
    default: null
  },
  iconSize: {
    type: [String, Number],
    default: 64
  },
  showRetry: {
    type: Boolean,
    default: false
  },
  showCreate: {
    type: Boolean,
    default: false
  },
  showBack: {
    type: Boolean,
    default: false
  },
  createText: {
    type: String,
    default: '新建'
  }
})

// Emits
const emit = defineEmits(['retry', 'create', 'back'])

// 计算属性
const showActions = computed(() => {
  return props.showRetry || props.showCreate || props.showBack
})

const currentIcon = computed(() => {
  if (props.icon) {
    return props.icon
  }

  const iconMap = {
    empty: DocumentRemove,
    search: Search,
    error: Warning,
    loading: InfoFilled
  }

  return iconMap[props.type] || DocumentRemove
})

const currentTitle = computed(() => {
  if (props.title) {
    return props.title
  }

  const titleMap = {
    empty: '暂无数据',
    search: '未找到相关内容',
    error: '加载失败',
    loading: '加载中...'
  }

  return titleMap[props.type] || '暂无数据'
})

const currentDescription = computed(() => {
  if (props.description) {
    return props.description
  }

  const descriptionMap = {
    empty: '当前没有任何数据，请稍后再试或联系管理员',
    search: '请尝试调整搜索条件或过滤器',
    error: '数据加载失败，请检查网络连接或稍后重试',
    loading: '正在加载数据，请稍候...'
  }

  return descriptionMap[props.type] || '当前没有任何数据'
})
</script>

<style scoped>
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  margin-bottom: 20px;
}

.icon {
  color: #c0c4cc;
}

.empty-title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.empty-description {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: #909399;
  line-height: 1.6;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  min-width: 80px;
}

.custom-actions {
  margin-top: 16px;
}

/* 不同类型的样式 */
.empty-state[data-type="error"] .icon {
  color: #f56c6c;
}

.empty-state[data-type="search"] .icon {
  color: #409eff;
}

.empty-state[data-type="loading"] .icon {
  color: #409eff;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .empty-state {
    min-height: 200px;
    padding: 20px 16px;
  }

  .empty-content {
    max-width: 300px;
  }

  .empty-title {
    font-size: 16px;
  }

  .empty-description {
    font-size: 13px;
  }

  .empty-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .empty-state {
    min-height: 160px;
    padding: 16px 12px;
  }

  .empty-icon {
    margin-bottom: 16px;
  }

  .empty-title {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .empty-description {
    font-size: 12px;
    margin-bottom: 20px;
  }
}
</style>
