"""
数据库初始化模块
负责数据库连接检查、自动创建数据库等功能
"""

import time

from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError

from config.settings import settings
from core.logging.logging_system import log as logger


class DatabaseInitializer:
    """数据库初始化器"""

    def __init__(self):
        self.settings = settings

    def check_and_initialize_database(self) -> tuple[bool, str]:
        """
        检查并初始化数据库

        Returns:
            tuple[bool, str]: (是否成功, 错误信息或成功信息)
        """
        try:
            # 1. 验证配置
            is_valid, error_msg = self.settings.validate_database_config()
            if not is_valid:
                return False, error_msg

            # 2. 获取数据库URL
            database_url = self.settings.get_database_url()
            if not database_url:
                return False, "无法构建数据库连接URL"

            # 3. 尝试连接数据库
            success, message = self._test_database_connection(database_url)
            if success:
                logger.info(f"数据库连接成功: {message}")
                return True, message

            # 4. 如果连接失败且启用了自动建库，尝试创建数据库
            if self.settings.AUTO_CREATE_DATABASE:
                logger.info("数据库连接失败，尝试自动创建数据库...")
                create_success, create_message = self._auto_create_database()
                if create_success:
                    # 重新测试连接
                    success, message = self._test_database_connection(database_url)
                    if success:
                        return True, f"数据库自动创建成功: {create_message}"
                    else:
                        return False, f"数据库创建成功但连接失败: {message}"
                else:
                    return False, f"数据库自动创建失败: {create_message}"
            else:
                return False, f"数据库连接失败: {message}。建议检查数据库配置或启用AUTO_CREATE_DATABASE"

        except Exception as e:
            logger.error(f"数据库初始化过程中发生异常: {e}")
            return False, f"数据库初始化异常: {str(e)}"

    def _test_database_connection(self, database_url: str, max_retries: int = 3) -> tuple[bool, str]:
        """
        测试数据库连接

        Args:
            database_url: 数据库连接URL
            max_retries: 最大重试次数

        Returns:
            tuple[bool, str]: (是否成功, 消息)
        """
        for attempt in range(max_retries):
            try:
                # 创建临时引擎进行连接测试
                test_engine = create_engine(
                    database_url,
                    pool_pre_ping=True,
                    connect_args={"connect_timeout": self.settings.DB_CONNECT_TIMEOUT}
                )

                # 尝试连接并执行简单查询
                with test_engine.connect() as conn:
                    result = conn.execute(text("SELECT 1 as test"))
                    test_value = result.scalar()
                    if test_value == 1:
                        test_engine.dispose()
                        return True, f"数据库连接正常 (尝试 {attempt + 1}/{max_retries})"

                test_engine.dispose()

            except OperationalError as e:
                error_msg = str(e.orig) if hasattr(e, 'orig') else str(e)

                # 检查是否是数据库不存在的错误
                if "Unknown database" in error_msg or "database doesn't exist" in error_msg:
                    return False, f"数据库不存在: {self.settings.DB_NAME}"

                # 检查是否是连接问题
                if "Can't connect" in error_msg or "Connection refused" in error_msg:
                    if attempt < max_retries - 1:
                        logger.warning(f"数据库连接失败，{2}秒后重试 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                        time.sleep(2)
                        continue
                    return False, f"无法连接到数据库服务器: {error_msg}"

                # 其他操作错误
                return False, f"数据库操作错误: {error_msg}"

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"数据库连接测试异常，{2}秒后重试 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(2)
                    continue
                return False, f"数据库连接测试异常: {str(e)}"

        return False, f"数据库连接失败，已重试 {max_retries} 次"

    def _auto_create_database(self) -> tuple[bool, str]:
        """
        自动创建数据库

        Returns:
            tuple[bool, str]: (是否成功, 消息)
        """
        if not self.settings.AUTO_CREATE_DATABASE:
            return False, "自动建库功能未启用"

        admin_url = self.settings.get_admin_database_url()
        if not admin_url:
            return False, "无法获取管理员数据库连接URL"

        try:
            # 使用管理员账户连接到MySQL服务器
            admin_engine = create_engine(
                admin_url,
                pool_pre_ping=True,
                connect_args={"connect_timeout": self.settings.DB_CONNECT_TIMEOUT}
            )

            with admin_engine.connect() as conn:
                # 检查数据库是否已存在
                check_sql = text("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = :db_name")
                result = conn.execute(check_sql, {"db_name": self.settings.DB_NAME})

                if result.fetchone():
                    admin_engine.dispose()
                    return True, f"数据库 '{self.settings.DB_NAME}' 已存在"

                # 创建数据库
                create_sql = text(
                    f"CREATE DATABASE `{self.settings.DB_NAME}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
                )
                conn.execute(create_sql)
                conn.commit()

                logger.info(f"数据库 '{self.settings.DB_NAME}' 创建成功")
                admin_engine.dispose()
                return True, f"数据库 '{self.settings.DB_NAME}' 创建成功"

        except OperationalError as e:
            error_msg = str(e.orig) if hasattr(e, 'orig') else str(e)

            # 检查权限问题
            if "Access denied" in error_msg:
                return False, f"权限不足，无法创建数据库: {error_msg}"

            return False, f"创建数据库时发生操作错误: {error_msg}"

        except Exception as e:
            return False, f"创建数据库时发生异常: {str(e)}"

    def get_database_info(self) -> dict:
        """
        获取数据库配置信息（用于调试和监控）

        Returns:
            dict: 数据库配置信息
        """
        return {
            "config_method": "independent_params" if all([
                self.settings.DB_HOST, 
                self.settings.DB_USER, 
                self.settings.DB_PASSWORD, 
                self.settings.DB_NAME
            ]) else "database_url",
            "host": self.settings.DB_HOST,
            "port": self.settings.DB_PORT,
            "database": self.settings.DB_NAME,
            "user": self.settings.DB_USER,
            "driver": self.settings.DB_DRIVER,
            "auto_create_enabled": self.settings.AUTO_CREATE_DATABASE,
            "check_on_startup": self.settings.DB_CHECK_ON_STARTUP,
            "connect_timeout": self.settings.DB_CONNECT_TIMEOUT,
        }


# 全局数据库初始化器实例
database_initializer = DatabaseInitializer()


def initialize_database() -> tuple[bool, str]:
    """
    初始化数据库的便捷函数

    Returns:
        tuple[bool, str]: (是否成功, 消息)
    """
    return database_initializer.check_and_initialize_database()


def get_database_info() -> dict:
    """
    获取数据库信息的便捷函数

    Returns:
        dict: 数据库配置信息
    """
    return database_initializer.get_database_info()
