"""
组件降级适配器实现
为现有性能优化组件实现降级适配器，使其能够响应降级事件并执行相应的降级动作
"""

import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any

from core.degradation_core import (
    DegradationActionType,
    DegradationAware,
    DegradationEvent,
    DegradationEventType,
    DegradationLevel,
)
from core.logging.logging_system import log as logger


@dataclass
class DegradationState:
    """组件降级状态"""

    current_level: DegradationLevel = DegradationLevel.L0_NORMAL
    original_config: dict[str, Any] = field(default_factory=dict)
    degraded_config: dict[str, Any] = field(default_factory=dict)
    last_change_time: float = field(default_factory=time.time)
    is_degraded: bool = False
    degradation_reason: str = ""

    def update_level(self, level: DegradationLevel, reason: str = ""):
        """更新降级级别"""
        self.current_level = level
        self.is_degraded = level != DegradationLevel.L0_NORMAL
        self.last_change_time = time.perf_counter()
        self.degradation_reason = reason


class BaseDegradationAdapter(DegradationAware, ABC):
    """降级适配器基类

    提供通用的降级适配器功能，具体组件继承此类实现特定的降级逻辑
    """

    def __init__(self, component_name: str):
        self.component_name = component_name
        self.degradation_state = DegradationState()
        self._lock = threading.RLock()
        self._enabled = True

        logger.info(f"DegradationAdapter initialized for {component_name}")

    def get_component_name(self) -> str:
        """获取组件名称"""
        return self.component_name

    def get_current_status(self) -> dict[str, Any]:
        """获取组件当前状态"""
        with self._lock:
            return {
                "component_name": self.component_name,
                "current_level": self.degradation_state.current_level.value,
                "is_degraded": self.degradation_state.is_degraded,
                "last_change_time": self.degradation_state.last_change_time,
                "degradation_reason": self.degradation_state.degradation_reason,
                "enabled": self._enabled,
                "original_config": self.degradation_state.original_config.copy(),
                "degraded_config": self.degradation_state.degraded_config.copy(),
            }

    def on_degradation_event(self, event: DegradationEvent) -> bool:
        """处理降级事件"""
        if not self._enabled:
            logger.debug(f"Degradation adapter disabled for {self.component_name}")
            return True

        try:
            # 检查是否是针对此组件的动作
            relevant_actions = [action for action in event.actions if action.target_component == self.component_name]

            if not relevant_actions and event.event_type not in [
                DegradationEventType.DEGRADATION_TRIGGERED,
                DegradationEventType.DEGRADATION_RECOVERED,
                DegradationEventType.LEVEL_CHANGED,
            ]:
                return True

            # 处理降级级别变化
            if event.event_type in [
                DegradationEventType.DEGRADATION_TRIGGERED,
                DegradationEventType.DEGRADATION_RECOVERED,
                DegradationEventType.LEVEL_CHANGED,
            ]:
                return self._handle_level_change(event.level, event.metadata.get("reason", ""))

            # 处理具体的降级动作
            if relevant_actions:
                return self._handle_degradation_actions(relevant_actions, event.level)

            return True

        except Exception as e:
            logger.error(f"Failed to handle degradation event in {self.component_name}: {e}", exc_info=True)
            return False

    def _handle_level_change(self, target_level: DegradationLevel, reason: str) -> bool:
        """处理降级级别变化"""
        with self._lock:
            current_level = self.degradation_state.current_level

            if target_level == current_level:
                return True

            logger.info(
                f"Handling level change for {self.component_name}: {current_level.value} -> {target_level.value}",
                extra={
                    "component": self.component_name,
                    "from_level": current_level.value,
                    "to_level": target_level.value,
                    "reason": reason,
                },
            )

            # 保存原始配置（如果是第一次降级）
            if current_level == DegradationLevel.L0_NORMAL and target_level != DegradationLevel.L0_NORMAL:
                self.degradation_state.original_config = self._get_current_config()

            # 执行降级或恢复
            if target_level == DegradationLevel.L0_NORMAL:
                success = self._restore_original_config()
            else:
                success = self._apply_degradation_level(target_level)

            if success:
                self.degradation_state.update_level(target_level, reason)
                logger.info(
                    f"Successfully changed degradation level for {self.component_name}",
                    extra={
                        "component": self.component_name,
                        "new_level": target_level.value,
                        "is_degraded": self.degradation_state.is_degraded,
                    },
                )
            else:
                logger.error(
                    f"Failed to change degradation level for {self.component_name}",
                    extra={"component": self.component_name, "target_level": target_level.value},
                )

            return success

    def _handle_degradation_actions(self, actions: list, target_level: DegradationLevel) -> bool:
        """处理具体的降级动作"""
        success = True

        for action in actions:
            try:
                action_success = self._execute_action(action, target_level)
                if not action_success:
                    success = False
                    logger.error(f"Failed to execute action {action.action_type.value} for {self.component_name}")
                else:
                    logger.info(f"Successfully executed action {action.action_type.value} for {self.component_name}")
            except Exception as e:
                success = False
                logger.error(
                    f"Exception executing action {action.action_type.value} for {self.component_name}: {e}",
                    exc_info=True,
                )

        return success

    def enable(self):
        """启用降级适配器"""
        self._enabled = True
        logger.info(f"Degradation adapter enabled for {self.component_name}")

    def disable(self):
        """禁用降级适配器"""
        self._enabled = False
        logger.info(f"Degradation adapter disabled for {self.component_name}")

    def is_enabled(self) -> bool:
        """检查适配器是否启用"""
        return self._enabled

    def force_restore(self) -> bool:
        """强制恢复到原始配置"""
        with self._lock:
            if not self.degradation_state.is_degraded:
                return True

            success = self._restore_original_config()
            if success:
                self.degradation_state.update_level(DegradationLevel.L0_NORMAL, "Force restore")
                logger.info(f"Force restored {self.component_name} to original configuration")
            else:
                logger.error(f"Failed to force restore {self.component_name}")

            return success

    # 抽象方法，由具体组件适配器实现
    @abstractmethod
    def _get_current_config(self) -> dict[str, Any]:
        """获取当前组件配置"""
        pass

    @abstractmethod
    def _apply_degradation_level(self, level: DegradationLevel) -> bool:
        """应用指定的降级级别"""
        pass

    @abstractmethod
    def _restore_original_config(self) -> bool:
        """恢复原始配置"""
        pass

    @abstractmethod
    def _execute_action(self, action, level: DegradationLevel) -> bool:
        """执行具体的降级动作"""
        pass


class DegradationAdapterManager:
    """降级适配器管理器

    管理所有组件的降级适配器，提供统一的注册、查询和控制接口
    """

    def __init__(self):
        self._adapters: dict[str, BaseDegradationAdapter] = {}
        self._lock = threading.RLock()

        logger.info("DegradationAdapterManager initialized")

    def register_adapter(self, adapter: BaseDegradationAdapter):
        """注册降级适配器"""
        with self._lock:
            component_name = adapter.get_component_name()
            self._adapters[component_name] = adapter

            logger.info(f"Registered degradation adapter for {component_name}")

    def unregister_adapter(self, component_name: str):
        """取消注册降级适配器"""
        with self._lock:
            if component_name in self._adapters:
                del self._adapters[component_name]
                logger.info(f"Unregistered degradation adapter for {component_name}")

    def get_adapter(self, component_name: str) -> BaseDegradationAdapter | None:
        """获取指定组件的降级适配器"""
        with self._lock:
            return self._adapters.get(component_name)

    def get_all_adapters(self) -> dict[str, BaseDegradationAdapter]:
        """获取所有降级适配器"""
        with self._lock:
            return self._adapters.copy()

    def get_adapters_status(self) -> dict[str, dict[str, Any]]:
        """获取所有适配器的状态"""
        with self._lock:
            return {name: adapter.get_current_status() for name, adapter in self._adapters.items()}

    def enable_all_adapters(self):
        """启用所有适配器"""
        with self._lock:
            for adapter in self._adapters.values():
                adapter.enable()
            logger.info("Enabled all degradation adapters")

    def disable_all_adapters(self):
        """禁用所有适配器"""
        with self._lock:
            for adapter in self._adapters.values():
                adapter.disable()
            logger.info("Disabled all degradation adapters")

    def force_restore_all(self) -> dict[str, bool]:
        """强制恢复所有组件到原始配置"""
        results = {}
        with self._lock:
            for name, adapter in self._adapters.items():
                results[name] = adapter.force_restore()

        logger.info(f"Force restore completed for {len(results)} adapters")
        return results


# 全局适配器管理器实例
_adapter_manager: DegradationAdapterManager | None = None


def get_adapter_manager() -> DegradationAdapterManager:
    """获取全局适配器管理器实例"""
    global _adapter_manager

    if _adapter_manager is None:
        _adapter_manager = DegradationAdapterManager()

    return _adapter_manager


class DynamicProcessPoolAdapter(BaseDegradationAdapter):
    """DynamicProcessPool降级适配器"""

    def __init__(self, process_pool):
        super().__init__("DynamicProcessPool")
        self.process_pool = process_pool
        self._original_max_workers = None
        self._original_auto_adjustment = None

    def _get_current_config(self) -> dict[str, Any]:
        """获取当前进程池配置"""
        return {
            "max_workers": self.process_pool.max_workers,
            "current_workers": self.process_pool.current_workers,
            "min_workers": self.process_pool.min_workers,
            "auto_adjustment_enabled": self.process_pool._is_running,
        }

    def _apply_degradation_level(self, level: DegradationLevel) -> bool:
        """应用指定的降级级别"""
        try:
            # 保存原始配置
            if self._original_max_workers is None:
                self._original_max_workers = self.process_pool.max_workers
                self._original_auto_adjustment = self.process_pool._is_running

            # 根据降级级别调整进程池
            if level == DegradationLevel.L1_LIGHT:
                # L1: 减少最大进程数到75%
                new_max_workers = max(1, int(self._original_max_workers * 0.75))
                return self._adjust_process_pool(new_max_workers, True)

            elif level == DegradationLevel.L2_MODERATE:
                # L2: 减少最大进程数到50%
                new_max_workers = max(1, int(self._original_max_workers * 0.5))
                return self._adjust_process_pool(new_max_workers, True)

            elif level == DegradationLevel.L3_SEVERE:
                # L3: 减少最大进程数到25%，禁用自动调整
                new_max_workers = max(1, int(self._original_max_workers * 0.25))
                return self._adjust_process_pool(new_max_workers, False)

            return False

        except Exception as e:
            logger.error(f"Failed to apply degradation level {level.value} to process pool: {e}")
            return False

    def _adjust_process_pool(self, new_max_workers: int, enable_auto_adjustment: bool) -> bool:
        """调整进程池配置"""
        try:
            # 更新最大工作进程数
            old_max_workers = self.process_pool.max_workers
            self.process_pool.max_workers = new_max_workers

            # 如果当前工作进程数超过新的最大值，需要调整
            if self.process_pool.current_workers > new_max_workers:
                self.process_pool.current_workers = new_max_workers

                # 重新创建进程池
                if self.process_pool.executor:
                    self.process_pool._create_executor()

            # 控制自动调整
            if not enable_auto_adjustment and self.process_pool._is_running:
                # 禁用自动调整（通过停止调整任务）
                if self.process_pool._adjustment_task:
                    self.process_pool._adjustment_task.cancel()
                    self.process_pool._adjustment_task = None

            # 记录降级配置
            self.degradation_state.degraded_config = {
                "max_workers": new_max_workers,
                "current_workers": self.process_pool.current_workers,
                "auto_adjustment_enabled": enable_auto_adjustment,
            }

            logger.info(
                f"Process pool adjusted: max_workers {old_max_workers} -> {new_max_workers}, "
                f"auto_adjustment: {enable_auto_adjustment}",
                extra={
                    "component": self.component_name,
                    "old_max_workers": old_max_workers,
                    "new_max_workers": new_max_workers,
                    "auto_adjustment": enable_auto_adjustment,
                },
            )

            return True

        except Exception as e:
            logger.error(f"Failed to adjust process pool: {e}", exc_info=True)
            return False

    def _restore_original_config(self) -> bool:
        """恢复原始配置"""
        try:
            if self._original_max_workers is None:
                return True

            # 恢复最大工作进程数
            self.process_pool.max_workers = self._original_max_workers

            # 恢复自动调整
            if self._original_auto_adjustment and not self.process_pool._adjustment_task:
                import asyncio

                self.process_pool._adjustment_task = asyncio.create_task(self.process_pool._adjustment_loop())

            # 重新创建进程池以应用新配置
            if self.process_pool.executor:
                self.process_pool._create_executor()

            logger.info(
                f"Process pool restored to original configuration: max_workers={self._original_max_workers}",
                extra={"component": self.component_name, "max_workers": self._original_max_workers},
            )

            # 清除保存的原始配置
            self._original_max_workers = None
            self._original_auto_adjustment = None

            return True

        except Exception as e:
            logger.error(f"Failed to restore process pool configuration: {e}", exc_info=True)
            return False

    def _execute_action(self, action, level: DegradationLevel) -> bool:
        """执行具体的降级动作"""
        try:
            if action.action_type == DegradationActionType.REDUCE_PROCESS_POOL:
                reduction_factor = action.parameters.get("reduction_factor", 0.75)
                if self._original_max_workers is None:
                    self._original_max_workers = self.process_pool.max_workers

                new_max_workers = max(1, int(self._original_max_workers * reduction_factor))
                return self._adjust_process_pool(new_max_workers, True)

            elif action.action_type == DegradationActionType.DISABLE_OPTIMIZATION:
                # 禁用自动调整优化
                if self.process_pool._adjustment_task:
                    self.process_pool._adjustment_task.cancel()
                    self.process_pool._adjustment_task = None
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to execute action {action.action_type.value}: {e}", exc_info=True)
            return False


class IntelligentCacheAdapter(BaseDegradationAdapter):
    """IntelligentCache降级适配器"""

    def __init__(self, cache_manager):
        super().__init__("IntelligentCache")
        self.cache_manager = cache_manager
        self._original_configs = {}
        self._cache_enabled = True

    def _get_current_config(self) -> dict[str, Any]:
        """获取当前缓存配置"""
        return {
            "rule_result_cache": {
                "max_size": self.cache_manager.rule_result_cache.max_size,
                "max_memory_mb": self.cache_manager.rule_result_cache.max_memory_mb,
                "ttl_seconds": self.cache_manager.rule_result_cache.ttl_seconds,
            },
            "patient_hash_cache": {
                "max_size": self.cache_manager.patient_hash_cache.max_size,
                "max_memory_mb": self.cache_manager.patient_hash_cache.max_memory_mb,
                "ttl_seconds": self.cache_manager.patient_hash_cache.ttl_seconds,
            },
            "computation_cache": {
                "max_size": self.cache_manager.computation_cache.max_size,
                "max_memory_mb": self.cache_manager.computation_cache.max_memory_mb,
                "ttl_seconds": self.cache_manager.computation_cache.ttl_seconds,
            },
            "cache_enabled": self._cache_enabled,
        }

    def _apply_degradation_level(self, level: DegradationLevel) -> bool:
        """应用指定的降级级别"""
        try:
            # 保存原始配置
            if not self._original_configs:
                self._original_configs = self._get_current_config()

            if level == DegradationLevel.L1_LIGHT:
                # L1: 减少缓存大小到75%
                return self._adjust_cache_size(0.75, 1.0)

            elif level == DegradationLevel.L2_MODERATE:
                # L2: 减少缓存大小到50%，缩短TTL
                return self._adjust_cache_size(0.5, 0.5)

            elif level == DegradationLevel.L3_SEVERE:
                # L3: 禁用所有缓存
                return self._disable_all_caches()

            return False

        except Exception as e:
            logger.error(f"Failed to apply degradation level {level.value} to cache: {e}")
            return False

    def _adjust_cache_size(self, size_factor: float, ttl_factor: float) -> bool:
        """调整缓存大小和TTL"""
        try:
            # 调整规则结果缓存
            original_rule_config = self._original_configs["rule_result_cache"]
            new_rule_size = max(1, int(original_rule_config["max_size"] * size_factor))
            new_rule_memory = max(1, int(original_rule_config["max_memory_mb"] * size_factor))
            new_rule_ttl = max(60, int(original_rule_config["ttl_seconds"] * ttl_factor))

            self.cache_manager.rule_result_cache.max_size = new_rule_size
            self.cache_manager.rule_result_cache.max_memory_mb = new_rule_memory
            self.cache_manager.rule_result_cache.ttl_seconds = new_rule_ttl

            # 调整患者哈希缓存
            original_patient_config = self._original_configs["patient_hash_cache"]
            new_patient_size = max(1, int(original_patient_config["max_size"] * size_factor))
            new_patient_memory = max(1, int(original_patient_config["max_memory_mb"] * size_factor))
            new_patient_ttl = max(60, int(original_patient_config["ttl_seconds"] * ttl_factor))

            self.cache_manager.patient_hash_cache.max_size = new_patient_size
            self.cache_manager.patient_hash_cache.max_memory_mb = new_patient_memory
            self.cache_manager.patient_hash_cache.ttl_seconds = new_patient_ttl

            # 调整计算结果缓存
            original_comp_config = self._original_configs["computation_cache"]
            new_comp_size = max(1, int(original_comp_config["max_size"] * size_factor))
            new_comp_memory = max(1, int(original_comp_config["max_memory_mb"] * size_factor))
            new_comp_ttl = max(60, int(original_comp_config["ttl_seconds"] * ttl_factor))

            self.cache_manager.computation_cache.max_size = new_comp_size
            self.cache_manager.computation_cache.max_memory_mb = new_comp_memory
            self.cache_manager.computation_cache.ttl_seconds = new_comp_ttl

            # 清理超出新限制的缓存项
            self._cleanup_oversized_caches()

            # 记录降级配置
            self.degradation_state.degraded_config = {
                "size_factor": size_factor,
                "ttl_factor": ttl_factor,
                "cache_enabled": True,
            }

            logger.info(
                f"Cache adjusted: size_factor={size_factor}, ttl_factor={ttl_factor}",
                extra={"component": self.component_name, "size_factor": size_factor, "ttl_factor": ttl_factor},
            )

            return True

        except Exception as e:
            logger.error(f"Failed to adjust cache configuration: {e}", exc_info=True)
            return False

    def _disable_all_caches(self) -> bool:
        """禁用所有缓存"""
        try:
            # 清空所有缓存
            self.cache_manager.rule_result_cache.clear()
            self.cache_manager.patient_hash_cache.clear()
            self.cache_manager.computation_cache.clear()

            # 标记缓存为禁用状态
            self._cache_enabled = False

            # 记录降级配置
            self.degradation_state.degraded_config = {"cache_enabled": False}

            logger.info("All caches disabled", extra={"component": self.component_name})

            return True

        except Exception as e:
            logger.error(f"Failed to disable caches: {e}", exc_info=True)
            return False

    def _cleanup_oversized_caches(self):
        """清理超出新限制的缓存项"""
        try:
            # 这里可以实现更智能的清理逻辑
            # 目前简单地触发缓存的内部清理机制
            pass
        except Exception as e:
            logger.warning(f"Failed to cleanup oversized caches: {e}")

    def _restore_original_config(self) -> bool:
        """恢复原始配置"""
        try:
            if not self._original_configs:
                return True

            # 恢复规则结果缓存配置
            rule_config = self._original_configs["rule_result_cache"]
            self.cache_manager.rule_result_cache.max_size = rule_config["max_size"]
            self.cache_manager.rule_result_cache.max_memory_mb = rule_config["max_memory_mb"]
            self.cache_manager.rule_result_cache.ttl_seconds = rule_config["ttl_seconds"]

            # 恢复患者哈希缓存配置
            patient_config = self._original_configs["patient_hash_cache"]
            self.cache_manager.patient_hash_cache.max_size = patient_config["max_size"]
            self.cache_manager.patient_hash_cache.max_memory_mb = patient_config["max_memory_mb"]
            self.cache_manager.patient_hash_cache.ttl_seconds = patient_config["ttl_seconds"]

            # 恢复计算结果缓存配置
            comp_config = self._original_configs["computation_cache"]
            self.cache_manager.computation_cache.max_size = comp_config["max_size"]
            self.cache_manager.computation_cache.max_memory_mb = comp_config["max_memory_mb"]
            self.cache_manager.computation_cache.ttl_seconds = comp_config["ttl_seconds"]

            # 恢复缓存启用状态
            self._cache_enabled = True

            logger.info("Cache configuration restored to original settings", extra={"component": self.component_name})

            # 清除保存的原始配置
            self._original_configs = {}

            return True

        except Exception as e:
            logger.error(f"Failed to restore cache configuration: {e}", exc_info=True)
            return False

    def _execute_action(self, action, level: DegradationLevel) -> bool:
        """执行具体的降级动作"""
        try:
            if action.action_type == DegradationActionType.REDUCE_CACHE_SIZE:
                reduction_factor = action.parameters.get("reduction_factor", 0.75)
                if not self._original_configs:
                    self._original_configs = self._get_current_config()
                return self._adjust_cache_size(reduction_factor, 1.0)

            elif action.action_type == DegradationActionType.DISABLE_CACHE:
                if not self._original_configs:
                    self._original_configs = self._get_current_config()
                return self._disable_all_caches()

            return False

        except Exception as e:
            logger.error(f"Failed to execute action {action.action_type.value}: {e}", exc_info=True)
            return False

    def is_cache_enabled(self) -> bool:
        """检查缓存是否启用"""
        return self._cache_enabled


class ObjectPoolAdapter(BaseDegradationAdapter):
    """ObjectPool降级适配器"""

    def __init__(self, object_pool):
        super().__init__("ObjectPool")
        self.object_pool = object_pool
        self._original_max_size = None
        self._pool_enabled = True

    def _get_current_config(self) -> dict[str, Any]:
        """获取当前对象池配置"""
        return {
            "max_size": self.object_pool.max_size,
            "current_size": self.object_pool._stats.current_size,
            "pool_enabled": self._pool_enabled,
        }

    def _apply_degradation_level(self, level: DegradationLevel) -> bool:
        """应用指定的降级级别"""
        try:
            # 保存原始配置
            if self._original_max_size is None:
                self._original_max_size = self.object_pool.max_size

            if level == DegradationLevel.L1_LIGHT:
                # L1: 减少池大小到75%
                new_max_size = max(1, int(self._original_max_size * 0.75))
                return self._adjust_pool_size(new_max_size)

            elif level == DegradationLevel.L2_MODERATE:
                # L2: 减少池大小到50%
                new_max_size = max(1, int(self._original_max_size * 0.5))
                return self._adjust_pool_size(new_max_size)

            elif level == DegradationLevel.L3_SEVERE:
                # L3: 禁用对象池，直接创建对象
                return self._disable_object_pool()

            return False

        except Exception as e:
            logger.error(f"Failed to apply degradation level {level.value} to object pool: {e}")
            return False

    def _adjust_pool_size(self, new_max_size: int) -> bool:
        """调整对象池大小"""
        try:
            old_max_size = self.object_pool.max_size
            self.object_pool.max_size = new_max_size

            # 如果当前池中对象数量超过新的最大值，需要清理
            with self.object_pool._lock:
                current_size = self.object_pool._stats.current_size
                if current_size > new_max_size:
                    # 清理多余的对象
                    objects_to_remove = current_size - new_max_size
                    for _ in range(objects_to_remove):
                        try:
                            if not self.object_pool._pool.empty():
                                obj = self.object_pool._pool.get_nowait()
                                # 从时间戳记录中移除
                                obj_id = id(obj)
                                if obj_id in self.object_pool._object_timestamps:
                                    del self.object_pool._object_timestamps[obj_id]
                        except:
                            break

                    # 更新统计信息
                    self.object_pool._stats.current_size = min(current_size, new_max_size)

            # 记录降级配置
            self.degradation_state.degraded_config = {"max_size": new_max_size, "pool_enabled": True}

            logger.info(
                f"Object pool adjusted: max_size {old_max_size} -> {new_max_size}",
                extra={"component": self.component_name, "old_max_size": old_max_size, "new_max_size": new_max_size},
            )

            return True

        except Exception as e:
            logger.error(f"Failed to adjust object pool size: {e}", exc_info=True)
            return False

    def _disable_object_pool(self) -> bool:
        """禁用对象池"""
        try:
            # 清空对象池
            with self.object_pool._lock:
                while not self.object_pool._pool.empty():
                    try:
                        obj = self.object_pool._pool.get_nowait()
                        obj_id = id(obj)
                        if obj_id in self.object_pool._object_timestamps:
                            del self.object_pool._object_timestamps[obj_id]
                    except:
                        break

                # 更新统计信息
                self.object_pool._stats.current_size = 0

            # 标记对象池为禁用状态
            self._pool_enabled = False

            # 记录降级配置
            self.degradation_state.degraded_config = {"pool_enabled": False}

            logger.info("Object pool disabled", extra={"component": self.component_name})

            return True

        except Exception as e:
            logger.error(f"Failed to disable object pool: {e}", exc_info=True)
            return False

    def _restore_original_config(self) -> bool:
        """恢复原始配置"""
        try:
            if self._original_max_size is None:
                return True

            # 恢复最大池大小
            self.object_pool.max_size = self._original_max_size

            # 恢复对象池启用状态
            self._pool_enabled = True

            logger.info(
                f"Object pool restored to original configuration: max_size={self._original_max_size}",
                extra={"component": self.component_name, "max_size": self._original_max_size},
            )

            # 清除保存的原始配置
            self._original_max_size = None

            return True

        except Exception as e:
            logger.error(f"Failed to restore object pool configuration: {e}", exc_info=True)
            return False

    def _execute_action(self, action, level: DegradationLevel) -> bool:
        """执行具体的降级动作"""
        try:
            if action.action_type == DegradationActionType.REDUCE_OBJECT_POOL:
                reduction_factor = action.parameters.get("reduction_factor", 0.75)
                if self._original_max_size is None:
                    self._original_max_size = self.object_pool.max_size

                new_max_size = max(1, int(self._original_max_size * reduction_factor))
                return self._adjust_pool_size(new_max_size)

            elif action.action_type == DegradationActionType.DISABLE_OBJECT_POOL:
                if self._original_max_size is None:
                    self._original_max_size = self.object_pool.max_size
                return self._disable_object_pool()

            return False

        except Exception as e:
            logger.error(f"Failed to execute action {action.action_type.value}: {e}", exc_info=True)
            return False

    def is_pool_enabled(self) -> bool:
        """检查对象池是否启用"""
        return self._pool_enabled

    def get_object(self):
        """获取对象（考虑降级状态）"""
        if not self._pool_enabled:
            # 对象池被禁用，直接创建新对象
            return self.object_pool.factory()
        else:
            # 使用正常的对象池逻辑（直接调用borrow避免循环）
            return self.object_pool.borrow()

    def return_object(self, obj):
        """归还对象（考虑降级状态）"""
        if not self._pool_enabled:
            # 对象池被禁用，直接丢弃对象
            return
        else:
            # 使用正常的对象池逻辑（调用原始的return_object方法）
            return self.object_pool.return_object(obj)
