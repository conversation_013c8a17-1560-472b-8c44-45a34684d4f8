#!/usr/bin/env python3
"""
API文档验证测试
验证API文档的完整性和准确性
"""

import json
from pathlib import Path


class TestAPIDocumentationValidation:
    """API文档验证测试类"""

    def test_api_reference_document_exists(self):
        """测试API参考文档是否存在"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        assert api_ref_path.exists(), "API参考文档不存在"

        # 检查文件不为空
        content = api_ref_path.read_text(encoding='utf-8')
        assert len(content) > 1000, "API参考文档内容过少"

    def test_api_examples_document_exists(self):
        """测试API示例文档是否存在"""
        api_examples_path = Path("docs/development/api/api_examples.md")
        assert api_examples_path.exists(), "API示例文档不存在"

        # 检查文件不为空
        content = api_examples_path.read_text(encoding='utf-8')
        assert len(content) > 1000, "API示例文档内容过少"

    def test_api_reference_document_structure(self):
        """测试API参考文档结构"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        content = api_ref_path.read_text(encoding='utf-8')

        # 检查必要的章节
        required_sections = [
            "# 规则明细 API 参考文档",
            "## 概述",
            "## 基础信息",
            "## 字段标准化说明",
            "### 1. 创建规则明细",
            "### 2. 查询规则明细列表",
            "### 3. 查询单条规则明细",
            "### 4. 更新规则明细",
            "### 5. 删除规则明细",
            "### 6. 批量操作",
            "## 错误码",
            "## 字段说明",
            "## 版本历史"
        ]

        for section in required_sections:
            assert section in content, f"API参考文档缺少必要章节: {section}"

    def test_api_examples_document_structure(self):
        """测试API示例文档结构"""
        api_examples_path = Path("docs/development/api/api_examples.md")
        content = api_examples_path.read_text(encoding='utf-8')

        # 检查必要的章节
        required_sections = [
            "# 规则明细 API 使用示例",
            "## 基础配置",
            "## 1. 创建规则明细",
            "## 2. 查询规则明细列表",
            "## 3. 查询单条规则明细",
            "## 4. 更新规则明细",
            "## 5. 删除规则明细",
            "## 6. 批量操作示例",
            "## 7. 增量上传示例",
            "## 错误处理示例",
            "## 字段映射说明"
        ]

        for section in required_sections:
            assert section in content, f"API示例文档缺少必要章节: {section}"

    def test_api_documentation_uses_standard_fields(self):
        """测试API文档使用标准字段名"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        api_examples_path = Path("docs/development/api/api_examples.md")

        ref_content = api_ref_path.read_text(encoding='utf-8')
        examples_content = api_examples_path.read_text(encoding='utf-8')

        # 检查标准字段名的使用
        standard_fields = [
            "level1", "level2", "level3", "error_reason", "degree",
            "reference", "detail_position", "prompted_fields1", "prompted_fields3",
            "type", "pos", "applicableArea", "default_use", "remarks",
            "in_illustration", "yb_code", "diag_whole_code", "diag_code_prefix",
            "diag_name_keyword", "fee_whole_code", "fee_code_prefix"
        ]

        for field in standard_fields:
            assert field in ref_content, f"API参考文档未使用标准字段名: {field}"
            assert field in examples_content, f"API示例文档未使用标准字段名: {field}"

    def test_api_documentation_version_consistency(self):
        """测试API文档版本一致性"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        content = api_ref_path.read_text(encoding='utf-8')

        # 检查版本信息
        assert "v1.0.0" in content, "API参考文档缺少版本信息"
        assert "2025-07-24" in content, "API参考文档缺少最新更新日期"

    def test_api_documentation_error_codes(self):
        """测试API文档错误码完整性"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        content = api_ref_path.read_text(encoding='utf-8')

        # 检查必要的错误码
        required_error_codes = [
            "200", "400", "401", "403", "404", "500",
            "605", "606", "607", "608", "609"
        ]

        for code in required_error_codes:
            assert f"| {code} |" in content, f"API参考文档缺少错误码: {code}"

    def test_api_documentation_field_mapping_table(self):
        """测试API文档字段映射表"""
        api_examples_path = Path("docs/development/api/api_examples.md")
        content = api_examples_path.read_text(encoding='utf-8')

        # 检查字段映射表的存在
        assert "| 标准字段名 | 历史字段名 | 说明 |" in content, "API示例文档缺少字段映射表"

        # 检查关键字段映射
        key_mappings = [
            ("level1", "error_level_1"),
            ("level2", "error_level_2"),
            ("level3", "error_level_3"),
            ("degree", "error_severity"),
            ("reference", "quality_basis"),
            ("type", "rule_category")
        ]

        for standard_field, legacy_field in key_mappings:
            assert f"| `{standard_field}` | `{legacy_field}` |" in content, \
                f"字段映射表缺少映射: {standard_field} -> {legacy_field}"

    def test_api_documentation_curl_examples(self):
        """测试API文档包含curl示例"""
        api_examples_path = Path("docs/development/api/api_examples.md")
        content = api_examples_path.read_text(encoding='utf-8')

        # 检查curl示例
        curl_patterns = [
            "curl -X POST",
            "curl -X GET", 
            "curl -X PUT",
            "curl -X DELETE"
        ]

        for pattern in curl_patterns:
            assert pattern in content, f"API示例文档缺少curl示例: {pattern}"

    def test_api_documentation_authentication_examples(self):
        """测试API文档包含认证示例"""
        api_examples_path = Path("docs/development/api/api_examples.md")
        content = api_examples_path.read_text(encoding='utf-8')

        # 检查认证相关内容
        auth_patterns = [
            "X-API-KEY",
            "a_very_secret_key_for_development",
            "export API_KEY"
        ]

        for pattern in auth_patterns:
            assert pattern in content, f"API示例文档缺少认证示例: {pattern}"

    def test_api_documentation_response_format_consistency(self):
        """测试API文档响应格式一致性"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        api_examples_path = Path("docs/development/api/api_examples.md")

        ref_content = api_ref_path.read_text(encoding='utf-8')
        examples_content = api_examples_path.read_text(encoding='utf-8')

        # 检查统一响应格式字段
        response_fields = [
            '"code":', '"success":', '"message":', '"data":', '"timestamp":'
        ]

        for field in response_fields:
            assert field in ref_content, f"API参考文档响应格式缺少字段: {field}"
            assert field in examples_content, f"API示例文档响应格式缺少字段: {field}"

    def test_api_documentation_batch_operations(self):
        """测试API文档批量操作说明"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        api_examples_path = Path("docs/development/api/api_examples.md")

        ref_content = api_ref_path.read_text(encoding='utf-8')
        examples_content = api_examples_path.read_text(encoding='utf-8')

        # 检查批量操作相关内容
        batch_patterns = [
            "batch", "operations", "CREATE", "UPDATE", "DELETE"
        ]

        for pattern in batch_patterns:
            assert pattern in ref_content, f"API参考文档缺少批量操作内容: {pattern}"
            assert pattern in examples_content, f"API示例文档缺少批量操作内容: {pattern}"

    def test_api_documentation_incremental_upload(self):
        """测试API文档增量上传说明"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        api_examples_path = Path("docs/development/api/api_examples.md")

        ref_content = api_ref_path.read_text(encoding='utf-8')  # noqa: F841
        examples_content = api_examples_path.read_text(encoding='utf-8')

        # 检查增量上传相关内容
        incremental_patterns = [
            "incremental", "upsert", "delete_missing"
        ]

        for pattern in incremental_patterns:
            assert pattern in examples_content, f"API示例文档缺少增量上传内容: {pattern}"

    def test_api_documentation_performance_considerations(self):
        """测试API文档性能考虑说明"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        content = api_ref_path.read_text(encoding='utf-8')

        # 检查性能相关说明
        performance_patterns = [
            "page_size", "最大 100", "分页"
        ]

        for pattern in performance_patterns:
            assert pattern in content, f"API参考文档缺少性能考虑说明: {pattern}"

    def test_field_mapping_configuration_consistency(self):
        """测试字段映射配置一致性"""
        # 检查field_mapping.json文件
        field_mapping_path = Path("config/field_mapping.json")
        if field_mapping_path.exists():
            with open(field_mapping_path, 'r', encoding='utf-8') as f:
                field_mapping = json.load(f)

            # 检查API文档中使用的字段是否在映射配置中
            api_ref_path = Path("docs/development/api/api_reference.md")
            content = api_ref_path.read_text(encoding='utf-8')

            # 从映射配置中获取标准字段名
            if "field_mappings" in field_mapping:
                standard_fields = list(field_mapping["field_mappings"].keys())

                for field in standard_fields:
                    if field not in ["id", "created_at", "updated_at", "status"]:  # 排除系统字段
                        assert field in content, f"API文档未包含映射配置中的字段: {field}"

    def test_api_documentation_completeness_score(self):
        """测试API文档完整性评分"""
        api_ref_path = Path("docs/development/api/api_reference.md")
        api_examples_path = Path("docs/development/api/api_examples.md")

        ref_content = api_ref_path.read_text(encoding='utf-8')
        examples_content = api_examples_path.read_text(encoding='utf-8')

        # 计算完整性评分
        total_score = 0
        max_score = 0

        # 基础结构 (20分)
        max_score += 20
        if len(ref_content) > 5000:
            total_score += 10
        if len(examples_content) > 5000:
            total_score += 10

        # 字段标准化 (20分)
        max_score += 20
        standard_field_count = sum(1 for field in ["level1", "level2", "level3", "error_reason", "degree"] 
                                 if field in ref_content)
        total_score += min(20, standard_field_count * 4)

        # 示例完整性 (20分)
        max_score += 20
        example_count = sum(1 for pattern in ["curl -X POST", "curl -X GET", "curl -X PUT", "curl -X DELETE"] 
                          if pattern in examples_content)
        total_score += min(20, example_count * 5)

        # 错误处理 (20分)
        max_score += 20
        error_code_count = sum(1 for code in ["200", "400", "401", "404", "500"] 
                             if f"| {code} |" in ref_content)
        total_score += min(20, error_code_count * 4)

        # 版本信息 (20分)
        max_score += 20
        if "v1.0.0" in ref_content:
            total_score += 10
        if "2025-07-24" in ref_content:
            total_score += 10

        completeness_percentage = (total_score / max_score) * 100

        # 要求完整性达到90%以上
        assert completeness_percentage >= 90, \
            f"API文档完整性评分过低: {completeness_percentage:.1f}% (要求: 90%+)"
