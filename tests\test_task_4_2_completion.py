"""
任务4.2完成验证测试
验证性能监控和降级机制的实现
"""

import pytest
from unittest.mock import Mock, patch
from core.rule_prefilter import rule_prefilter
from models.patient import PatientData


class TestTask42Completion:
    """任务4.2完成验证测试类"""

    def test_performance_stats_includes_monitoring_overhead(self):
        """测试性能统计包含监控开销信息"""
        # 重置统计
        rule_prefilter.reset_stats()
        
        # 获取统计信息
        stats = rule_prefilter.get_performance_stats()
        
        # 验证监控开销相关字段存在
        assert "total_monitoring_time_ms" in stats
        assert "avg_monitoring_time_ms" in stats
        assert "monitoring_overhead_ratio" in stats
        assert "monitoring_overhead_percentage" in stats
        
        # 验证初始值
        assert stats["total_monitoring_time_ms"] == 0.0
        assert stats["avg_monitoring_time_ms"] == 0.0
        assert stats["monitoring_overhead_ratio"] == 0.0
        assert stats["monitoring_overhead_percentage"] == 0.0

    def test_health_status_in_stats(self):
        """测试统计信息包含健康状态"""
        stats = rule_prefilter.get_performance_stats()
        
        # 验证基本统计字段
        required_fields = [
            "filter_count", "total_filter_time_ms", "avg_filter_time_ms",
            "total_original_rules", "total_filtered_rules", "overall_filter_rate",
            "fallback_count", "timeout_count", "fallback_rate", "timeout_rate",
            "filter_enabled", "filter_timeout_ms"
        ]
        
        for field in required_fields:
            assert field in stats, f"缺少必需字段: {field}"

    def test_monitoring_overhead_calculation(self):
        """测试监控开销计算"""
        # 重置统计
        rule_prefilter.reset_stats()
        
        # 模拟一些过滤操作来生成统计数据
        with patch('core.rule_prefilter.rule_index_manager') as mock_manager:
            with patch('core.rule_prefilter.patient_data_analyzer') as mock_analyzer:
                # 设置模拟返回值
                mock_manager.is_ready.return_value = True
                mock_analyzer.extract_codes.return_value = Mock(
                    yb_codes={"code1"}, diag_codes=set(), surgery_codes=set()
                )
                
                # 模拟过滤结果
                from core.rule_index_manager import FilterResult
                mock_filter_result = FilterResult(
                    original_rule_count=10,
                    filtered_rule_count=5,
                    filter_rate=0.5,
                    filter_time=2.0,
                    filtered_rule_ids=["rule1", "rule2", "rule3", "rule4", "rule5"]
                )
                mock_manager.filter_rules.return_value = mock_filter_result
                
                # 执行过滤操作
                patient_data = Mock(spec=PatientData)
                rule_prefilter.filter_rules_for_patient(patient_data, ["rule1", "rule2"])
                
                # 获取统计信息
                stats = rule_prefilter.get_performance_stats()
                
                # 验证监控开销被记录
                assert stats["filter_count"] > 0
                assert stats["total_monitoring_time_ms"] >= 0
                
                # 如果有过滤时间，验证开销比例计算
                if stats["total_filter_time_ms"] > 0:
                    expected_ratio = stats["total_monitoring_time_ms"] / stats["total_filter_time_ms"]
                    assert abs(stats["monitoring_overhead_ratio"] - expected_ratio) < 0.001

    def test_reset_stats_includes_monitoring_time(self):
        """测试重置统计包含监控时间"""
        # 设置一些统计数据
        rule_prefilter._filter_count = 5
        rule_prefilter._total_filter_time = 100.0
        rule_prefilter._total_monitoring_time = 2.0
        
        # 重置统计
        rule_prefilter.reset_stats()
        
        # 验证所有统计都被重置
        assert rule_prefilter._filter_count == 0
        assert rule_prefilter._total_filter_time == 0.0
        assert rule_prefilter._total_monitoring_time == 0.0

    def test_health_status_check(self):
        """测试健康状态检查功能"""
        # 测试健康状态方法存在
        assert hasattr(rule_prefilter, 'is_healthy')
        assert hasattr(rule_prefilter, '_get_health_recommendations')
        
        # 测试健康状态返回布尔值
        health_status = rule_prefilter.is_healthy()
        assert isinstance(health_status, bool)
        
        # 测试健康建议返回列表
        recommendations = rule_prefilter._get_health_recommendations()
        assert isinstance(recommendations, list)

    def test_configuration_access(self):
        """测试配置访问功能"""
        # 测试配置方法存在
        assert hasattr(rule_prefilter, '_is_filter_enabled')
        assert hasattr(rule_prefilter, '_get_filter_timeout')
        
        # 测试配置方法返回正确类型
        enabled = rule_prefilter._is_filter_enabled()
        assert isinstance(enabled, bool)
        
        timeout = rule_prefilter._get_filter_timeout()
        assert isinstance(timeout, (int, float))
        assert timeout > 0

    def test_fallback_logging_format(self):
        """测试降级日志格式（模拟测试）"""
        # 这个测试主要验证降级方法的存在和基本功能
        # 实际的日志格式需要在集成测试中验证
        
        # 验证降级相关方法存在
        assert hasattr(rule_prefilter, '_create_no_filter_result')
        assert hasattr(rule_prefilter, '_update_statistics')
        
        # 测试创建降级结果
        import time
        start_time = time.perf_counter()
        result = rule_prefilter._create_no_filter_result(
            ["rule1", "rule2"], start_time, "测试原因"
        )
        
        # 验证降级结果格式
        assert result.original_rule_count == 2
        assert result.filtered_rule_count == 2
        assert result.filter_rate == 0.0
        assert result.filtered_rule_ids == ["rule1", "rule2"]
        assert result.filter_time >= 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
