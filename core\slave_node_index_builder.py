"""
从节点索引构建器
实现从规则缓存文件构建索引的逻辑，支持热重载

主要功能：
1. 从rules_cache.json.gz文件解析规则数据
2. 使用与主节点相同的索引构建逻辑
3. 支持规则文件更新后的热重载
4. 实现文件监控和自动重建机制

技术特点：
- 统一构建逻辑：与主节点使用相同的RuleIndexManager.build_indexes_from_rule_details()
- 数据一致性：直接从规则数据构建，避免索引传输风险
- 热重载支持：文件更新后自动重建索引
- 容错性：构建失败时保留旧索引，不影响服务
"""

import asyncio
import gzip
import json
import os
import threading
import time
from dataclasses import dataclass
from typing import Any
from unittest.mock import Mock

from config.settings import settings
from core.logging.logging_system import log as logger
from core.memory_optimizer import MemoryOptimizer


@dataclass
class FileParsingStats:
    """文件解析性能统计"""

    file_size_mb: float = 0.0
    parse_time_ms: float = 0.0
    total_rules: int = 0
    valid_rules: int = 0
    invalid_rules: int = 0
    memory_peak_mb: float = 0.0
    memory_optimizations: int = 0
    batch_count: int = 0

    def get_success_rate(self) -> float:
        """获取解析成功率"""
        if self.total_rules == 0:
            return 0.0
        return (self.valid_rules / self.total_rules) * 100.0

    def get_throughput_rules_per_sec(self) -> float:
        """获取解析吞吐量（规则/秒）"""
        if self.parse_time_ms == 0:
            return 0.0
        return (self.total_rules / self.parse_time_ms) * 1000.0


@dataclass
class SlaveIndexErrorInfo:
    """从节点索引错误信息"""

    error_id: str
    error_type: str
    error_message: str
    error_context: dict
    timestamp: float
    severity: str  # low, medium, high, critical
    component: str  # build, parse, reload, memory
    recovery_suggestion: str
    stack_trace: str | None = None

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "error_id": self.error_id,
            "error_type": self.error_type,
            "error_message": self.error_message,
            "error_context": self.error_context,
            "timestamp": self.timestamp,
            "severity": self.severity,
            "component": self.component,
            "recovery_suggestion": self.recovery_suggestion,
            "stack_trace": self.stack_trace,
        }


class SlaveIndexErrorHandler:
    """从节点索引错误处理器"""

    def __init__(self, slave_builder):
        self.slave_builder = slave_builder

        # 错误统计
        self.error_count = 0
        self.error_history = []
        self.max_history_size = 100

        # 降级配置（使用项目配置系统）
        self.error_threshold = settings.SLAVE_INDEX_ERROR_THRESHOLD
        self.degradation_enabled = False
        self.degradation_level = 0  # 0: 正常, 1: 轻度, 2: 中度, 3: 重度
        self.max_degradation_level = 3

        # 恢复配置
        self.recovery_threshold = settings.SLAVE_INDEX_RECOVERY_THRESHOLD
        self.success_count_for_recovery = 0
        self.auto_recovery_enabled = True

        # 时间窗口配置
        self.error_window_size = 300  # 5分钟窗口
        self.last_degradation_time = 0
        self.degradation_cooldown = 60  # 1分钟冷却时间

        logger.info(f"从节点错误处理器已初始化，错误阈值: {self.error_threshold}")

    def handle_build_error(self, error: Exception, context: dict = None) -> SlaveIndexErrorInfo:
        """处理构建错误"""
        error_info = self._create_error_info(
            error=error,
            error_type="BUILD_ERROR",
            component="build",
            context=context or {},
            severity=self._determine_error_severity(error, "build"),
        )

        return self._process_error(error_info)

    def handle_parse_error(self, error: Exception, context: dict = None) -> SlaveIndexErrorInfo:
        """处理解析错误"""
        error_info = self._create_error_info(
            error=error,
            error_type="PARSE_ERROR",
            component="parse",
            context=context or {},
            severity=self._determine_error_severity(error, "parse"),
        )

        return self._process_error(error_info)

    def handle_reload_error(self, error: Exception, context: dict = None) -> SlaveIndexErrorInfo:
        """处理热重载错误"""
        error_info = self._create_error_info(
            error=error,
            error_type="RELOAD_ERROR",
            component="reload",
            context=context or {},
            severity=self._determine_error_severity(error, "reload"),
        )

        return self._process_error(error_info)

    def handle_memory_error(self, error: Exception, context: dict = None) -> SlaveIndexErrorInfo:
        """处理内存错误"""
        error_info = self._create_error_info(
            error=error,
            error_type="MEMORY_ERROR",
            component="memory",
            context=context or {},
            severity="high",  # 内存错误通常比较严重
        )

        return self._process_error(error_info)

    def _create_error_info(
        self, error: Exception, error_type: str, component: str, context: dict, severity: str
    ) -> SlaveIndexErrorInfo:
        """创建错误信息对象"""
        import traceback
        import uuid

        error_id = str(uuid.uuid4())[:8]

        # 生成恢复建议
        recovery_suggestion = self._generate_recovery_suggestion(error_type, error, context)

        return SlaveIndexErrorInfo(
            error_id=error_id,
            error_type=error_type,
            error_message=str(error),
            error_context=context,
            timestamp=time.time(),
            severity=severity,
            component=component,
            recovery_suggestion=recovery_suggestion,
            stack_trace=traceback.format_exc(),  # 总是记录堆栈信息
        )

    def _determine_error_severity(self, error: Exception, component: str) -> str:
        """确定错误严重程度"""
        error_msg = str(error).lower()

        # 关键错误关键词
        critical_keywords = ["memory", "disk", "permission", "access denied", "connection refused"]
        high_keywords = ["timeout", "failed", "invalid", "corrupt"]
        medium_keywords = ["warning", "retry", "temporary"]

        if any(keyword in error_msg for keyword in critical_keywords):
            return "critical"
        elif any(keyword in error_msg for keyword in high_keywords):
            return "high"
        elif any(keyword in error_msg for keyword in medium_keywords):
            return "medium"
        else:
            return "low"

    def _generate_recovery_suggestion(self, error_type: str, error: Exception, context: dict) -> str:
        """生成恢复建议"""
        error_msg = str(error).lower()

        if error_type == "BUILD_ERROR":
            if "memory" in error_msg:
                return "建议：1) 检查内存使用情况；2) 调整内存限制配置；3) 重启服务释放内存"
            elif "file" in error_msg or "path" in error_msg:
                return "建议：1) 检查文件路径是否正确；2) 确认文件权限；3) 检查磁盘空间"
            else:
                return "建议：1) 检查规则数据格式；2) 查看详细错误日志；3) 尝试重新构建索引"

        elif error_type == "PARSE_ERROR":
            return "建议：1) 检查缓存文件格式；2) 验证JSON数据完整性；3) 重新生成缓存文件"

        elif error_type == "RELOAD_ERROR":
            return "建议：1) 检查文件监控服务；2) 验证文件更新权限；3) 手动触发重载"

        elif error_type == "MEMORY_ERROR":
            return "建议：1) 立即释放内存；2) 调整内存配置；3) 重启服务"

        else:
            return "建议：1) 查看详细错误日志；2) 检查系统资源；3) 联系技术支持"

    def _process_error(self, error_info: SlaveIndexErrorInfo) -> SlaveIndexErrorInfo:
        """处理错误信息"""
        # 记录错误
        self._record_error(error_info)

        # 检查是否需要降级
        if self._should_trigger_degradation():
            self._trigger_degradation()

        # 记录到性能统计
        if hasattr(self.slave_builder, "performance_stats"):
            if error_info.component == "parse":
                self.slave_builder.performance_stats.parsing_errors += 1
            elif error_info.component == "build":
                self.slave_builder.performance_stats.build_errors += 1
            elif error_info.component == "memory":
                self.slave_builder.performance_stats.memory_errors += 1

        # 记录详细日志
        logger.error(
            f"从节点索引错误 [{error_info.error_id}]: {error_info.error_message}",
            extra={
                "error_id": error_info.error_id,
                "error_type": error_info.error_type,
                "component": error_info.component,
                "severity": error_info.severity,
                "context": error_info.error_context,
                "recovery_suggestion": error_info.recovery_suggestion,
            },
        )

        return error_info

    def _record_error(self, error_info: SlaveIndexErrorInfo):
        """记录错误到历史"""
        self.error_count += 1
        self.error_history.append(error_info)

        # 保持历史记录大小
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)

        # 重置成功计数（用于恢复判断）
        self.success_count_for_recovery = 0

    def _should_trigger_degradation(self) -> bool:
        """判断是否应该触发降级"""
        if self.degradation_enabled:
            return False  # 已经在降级状态

        # 检查冷却时间
        current_time = time.time()
        if current_time - self.last_degradation_time < self.degradation_cooldown:
            return False

        # 检查时间窗口内的错误数量
        recent_errors = self._get_recent_errors()
        if len(recent_errors) >= self.error_threshold:
            logger.warning(f"检测到{len(recent_errors)}个错误在{self.error_window_size}秒内，触发降级")
            return True

        return False

    def _get_recent_errors(self) -> list[SlaveIndexErrorInfo]:
        """获取最近时间窗口内的错误"""
        current_time = time.time()
        cutoff_time = current_time - self.error_window_size

        return [error for error in self.error_history if error.timestamp >= cutoff_time]

    def _trigger_degradation(self):
        """触发降级"""
        if self.degradation_enabled:
            # 已经在降级状态，尝试升级降级级别
            if self.degradation_level < self.max_degradation_level:
                self.degradation_level += 1
                logger.warning(f"升级降级级别到 L{self.degradation_level}")
        else:
            # 首次触发降级
            self.degradation_enabled = True
            self.degradation_level = 1
            logger.warning("触发从节点索引降级，级别: L1")

        self.last_degradation_time = time.time()

        # 应用降级策略
        self._apply_degradation_strategy()

    def _apply_degradation_strategy(self):
        """应用降级策略"""
        try:
            if self.degradation_level == 1:
                # L1 轻度降级：减少批次大小，增加内存检查频率
                self._apply_light_degradation()
            elif self.degradation_level == 2:
                # L2 中度降级：进一步减少批次大小，禁用部分优化
                self._apply_moderate_degradation()
            elif self.degradation_level == 3:
                # L3 重度降级：最小化资源使用，禁用热重载
                self._apply_severe_degradation()

            logger.info(f"已应用降级策略 L{self.degradation_level}")

        except Exception as e:
            logger.error(f"应用降级策略失败: {e}", exc_info=True)

    def _apply_light_degradation(self):
        """应用轻度降级策略"""
        # 减少批次大小到75%
        if hasattr(self.slave_builder, "_gc_threshold"):
            self.slave_builder._gc_threshold = int(self.slave_builder._gc_threshold * 0.75)

        # 增加内存检查频率
        logger.info("L1降级：减少批次大小，增加内存检查频率")

    def _apply_moderate_degradation(self):
        """应用中度降级策略"""
        # 减少批次大小到50%
        if hasattr(self.slave_builder, "_gc_threshold"):
            self.slave_builder._gc_threshold = int(self.slave_builder._gc_threshold * 0.5)

        # 降低内存限制
        if hasattr(self.slave_builder, "_memory_limit_mb"):
            self.slave_builder._memory_limit_mb = int(self.slave_builder._memory_limit_mb * 0.8)

        logger.info("L2降级：进一步减少批次大小，降低内存限制")

    def _apply_severe_degradation(self):
        """应用重度降级策略"""
        # 最小化批次大小
        if hasattr(self.slave_builder, "_gc_threshold"):
            self.slave_builder._gc_threshold = 100  # 最小值

        # 大幅降低内存限制
        if hasattr(self.slave_builder, "_memory_limit_mb"):
            self.slave_builder._memory_limit_mb = int(self.slave_builder._memory_limit_mb * 0.6)

        logger.warning("L3降级：最小化资源使用，系统进入保护模式")

    def record_success(self):
        """记录成功操作（用于恢复判断）"""
        if self.degradation_enabled and self.auto_recovery_enabled:
            self.success_count_for_recovery += 1

            # 检查是否可以恢复
            if self.success_count_for_recovery >= self.recovery_threshold:
                self._attempt_recovery()

    def _attempt_recovery(self):
        """尝试恢复"""
        if not self.degradation_enabled:
            return

        try:
            if self.degradation_level > 1:
                # 降低降级级别
                self.degradation_level -= 1
                logger.info(f"恢复降级级别到 L{self.degradation_level}")
                self._apply_degradation_strategy()
            else:
                # 完全恢复
                self._restore_normal_operation()

            # 重置成功计数
            self.success_count_for_recovery = 0

        except Exception as e:
            logger.error(f"恢复操作失败: {e}", exc_info=True)

    def _restore_normal_operation(self):
        """恢复正常操作"""
        try:
            # 恢复原始配置
            self.degradation_enabled = False
            self.degradation_level = 0

            # 恢复原始参数（这里需要保存原始值）
            # 注意：实际实现中应该保存原始配置值
            logger.info("已恢复正常操作模式")

        except Exception as e:
            logger.error(f"恢复正常操作失败: {e}", exc_info=True)

    def force_recovery(self):
        """强制恢复（手动干预）"""
        logger.info("执行强制恢复...")
        self._restore_normal_operation()

    def get_error_statistics(self) -> dict:
        """获取错误统计信息"""
        recent_errors = self._get_recent_errors()

        # 按组件分类错误
        error_by_component = {}
        error_by_severity = {}

        for error in self.error_history:
            # 按组件统计
            component = error.component
            if component not in error_by_component:
                error_by_component[component] = 0
            error_by_component[component] += 1

            # 按严重程度统计
            severity = error.severity
            if severity not in error_by_severity:
                error_by_severity[severity] = 0
            error_by_severity[severity] += 1

        return {
            "total_errors": self.error_count,
            "recent_errors": len(recent_errors),
            "error_history_size": len(self.error_history),
            "degradation_enabled": self.degradation_enabled,
            "degradation_level": self.degradation_level,
            "success_count_for_recovery": self.success_count_for_recovery,
            "error_by_component": error_by_component,
            "error_by_severity": error_by_severity,
            "error_threshold": self.error_threshold,
            "recovery_threshold": self.recovery_threshold,
            "auto_recovery_enabled": self.auto_recovery_enabled,
        }

    def get_recent_error_details(self, limit: int = 10) -> list[dict]:
        """获取最近的错误详情"""
        recent_errors = sorted(self.error_history, key=lambda x: x.timestamp, reverse=True)
        return [error.to_dict() for error in recent_errors[:limit]]

    def clear_error_history(self):
        """清除错误历史（谨慎使用）"""
        self.error_history.clear()
        self.error_count = 0
        logger.info("错误历史已清除")


@dataclass
class SlaveIndexPerformanceStats:
    """从节点索引构建性能统计"""

    # 构建统计
    build_count: int = 0
    successful_builds: int = 0
    failed_builds: int = 0
    avg_build_time_ms: float = 0.0
    max_build_time_ms: float = 0.0
    min_build_time_ms: float = float("inf")
    total_build_time_ms: float = 0.0

    # 内存统计
    avg_memory_usage_mb: float = 0.0
    max_memory_usage_mb: float = 0.0
    memory_optimization_count: int = 0

    # 热重载统计
    hot_reload_count: int = 0
    successful_hot_reloads: int = 0
    failed_hot_reloads: int = 0
    avg_hot_reload_time_ms: float = 0.0
    max_hot_reload_time_ms: float = 0.0
    total_hot_reload_time_ms: float = 0.0

    # 文件解析统计
    total_files_parsed: int = 0
    total_rules_processed: int = 0
    avg_parsing_throughput: float = 0.0  # 规则/秒

    # 错误统计
    parsing_errors: int = 0
    build_errors: int = 0
    memory_errors: int = 0

    # 时间戳
    last_build_time: float = 0.0
    last_hot_reload_time: float = 0.0
    stats_start_time: float = 0.0

    def __post_init__(self):
        """初始化后设置开始时间"""
        if self.stats_start_time == 0.0:
            self.stats_start_time = time.time()

    def record_build_success(self, build_time_ms: float, memory_mb: float, rules_count: int = 0):
        """记录构建成功"""
        self.build_count += 1
        self.successful_builds += 1
        self.last_build_time = time.time()

        # 更新构建时间统计
        self.total_build_time_ms += build_time_ms
        self.avg_build_time_ms = self.total_build_time_ms / self.build_count
        self.max_build_time_ms = max(self.max_build_time_ms, build_time_ms)
        self.min_build_time_ms = min(self.min_build_time_ms, build_time_ms)

        # 更新内存统计
        self._update_memory_stats(memory_mb)

        # 更新规则处理统计
        if rules_count > 0:
            self.total_rules_processed += rules_count

    def record_build_failure(self, error_msg: str = "", memory_mb: float = 0.0):
        """记录构建失败"""
        self.build_count += 1
        self.failed_builds += 1
        self.build_errors += 1
        self.last_build_time = time.time()

        if memory_mb > 0:
            self._update_memory_stats(memory_mb)

    def record_hot_reload_success(self, reload_time_ms: float):
        """记录热重载成功"""
        self.hot_reload_count += 1
        self.successful_hot_reloads += 1
        self.last_hot_reload_time = time.time()

        # 更新热重载时间统计
        self.total_hot_reload_time_ms += reload_time_ms
        self.avg_hot_reload_time_ms = self.total_hot_reload_time_ms / self.hot_reload_count
        self.max_hot_reload_time_ms = max(self.max_hot_reload_time_ms, reload_time_ms)

    def record_hot_reload_failure(self):
        """记录热重载失败"""
        self.hot_reload_count += 1
        self.failed_hot_reloads += 1
        self.last_hot_reload_time = time.time()

    def record_memory_optimization(self):
        """记录内存优化事件"""
        self.memory_optimization_count += 1

    def record_parsing_error(self):
        """记录解析错误"""
        self.parsing_errors += 1

    def _update_memory_stats(self, memory_mb: float):
        """更新内存统计"""
        if memory_mb > 0:
            # 计算平均内存使用（简单移动平均）
            total_samples = self.build_count
            if total_samples == 1:
                self.avg_memory_usage_mb = memory_mb
            else:
                self.avg_memory_usage_mb = (self.avg_memory_usage_mb * (total_samples - 1) + memory_mb) / total_samples

            self.max_memory_usage_mb = max(self.max_memory_usage_mb, memory_mb)

    def get_success_rate(self) -> float:
        """获取构建成功率"""
        if self.build_count == 0:
            return 0.0
        return (self.successful_builds / self.build_count) * 100.0

    def get_hot_reload_success_rate(self) -> float:
        """获取热重载成功率"""
        if self.hot_reload_count == 0:
            return 0.0
        return (self.successful_hot_reloads / self.hot_reload_count) * 100.0

    def get_uptime_hours(self) -> float:
        """获取运行时间（小时）"""
        return (time.time() - self.stats_start_time) / 3600.0

    def get_avg_throughput(self) -> float:
        """获取平均处理吞吐量（规则/分钟）"""
        uptime_minutes = (time.time() - self.stats_start_time) / 60.0
        if uptime_minutes == 0:
            return 0.0
        return self.total_rules_processed / uptime_minutes

    def reset_stats(self):
        """重置统计数据"""
        self.__init__()
        self.stats_start_time = time.time()


@dataclass
class IndexBackup:
    """索引备份数据"""

    backup_time: float
    index_state: dict
    rule_count: int
    backup_id: str

    def is_valid(self) -> bool:
        """检查备份是否有效"""
        return self.backup_time > 0 and isinstance(self.index_state, dict) and self.rule_count >= 0 and bool(self.backup_id)


class SlaveNodeIndexBuilder:
    """从节点索引构建器"""

    def __init__(self, rule_index_manager, rules_cache_path: str = "rules_cache.json.gz"):
        self.rule_index_manager = rule_index_manager
        self.rules_cache_path = rules_cache_path

        # 内存管理配置（使用项目配置系统）
        self._memory_limit_mb = settings.SLAVE_INDEX_MEMORY_LIMIT_MB
        self._gc_threshold = settings.SLAVE_INDEX_GC_THRESHOLD

        # 集成智能内存管理器
        self.memory_optimizer = MemoryOptimizer(memory_limit_mb=self._memory_limit_mb)
        self.memory_optimizer.start_monitoring()
        logger.info(f"从节点智能内存管理器已启动，限制: {self._memory_limit_mb}MB")

        # 初始化解析统计
        self.parsing_stats = FileParsingStats()

        # 初始化性能统计
        self.performance_stats = SlaveIndexPerformanceStats()

        # 初始化错误处理器
        self.error_handler = SlaveIndexErrorHandler(self)

        # 配置验证
        validation_errors = self._validate_configuration()
        if validation_errors:
            error_msg = "从节点索引构建器配置验证失败: " + "; ".join(validation_errors)
            logger.error(error_msg)
            raise ValueError(error_msg)

        logger.info("从节点索引构建器初始化完成")

    def _validate_configuration(self) -> list[str]:
        """验证配置参数"""
        errors = []

        if self._memory_limit_mb <= 0:
            errors.append(f"内存限制必须大于0: {self._memory_limit_mb}")

        if self._gc_threshold <= 0:
            errors.append(f"GC阈值必须大于0: {self._gc_threshold}")

        if not os.path.exists(os.path.dirname(os.path.abspath(self.rules_cache_path))):
            errors.append(f"规则缓存文件目录不存在: {os.path.dirname(self.rules_cache_path)}")

        return errors

    def _should_optimize_memory(self) -> bool:
        """判断是否应该进行内存优化"""
        memory_stats = self.memory_optimizer.get_memory_stats()
        return memory_stats.is_over_threshold

    def _validate_cache_data_integrity(self, cache_data: dict) -> tuple[bool, str]:
        """验证缓存数据完整性"""
        try:
            # 检查基本结构
            if not isinstance(cache_data, dict):
                return False, "缓存数据不是有效的字典格式"

            # 检查是否包含规则数据
            rule_data_list = cache_data.get("rule_details", [])
            if not rule_data_list:
                # 尝试其他可能的数据结构
                if "rules" in cache_data:
                    rule_data_list = cache_data["rules"]
                elif isinstance(cache_data, list):
                    rule_data_list = cache_data
                else:
                    return False, "未找到有效的规则数据字段"

            if not isinstance(rule_data_list, list):
                return False, "规则数据不是列表格式"

            if len(rule_data_list) == 0:
                return False, "规则数据为空"

            # 抽样检查规则数据格式（检查前10条）
            sample_size = min(10, len(rule_data_list))
            for i, rule_data in enumerate(rule_data_list[:sample_size]):
                if not isinstance(rule_data, dict):
                    return False, f"第{i + 1}条规则数据不是字典格式"

                # 检查必要字段（根据实际需求调整）
                required_fields = ["rule_id"]  # 可以根据实际情况添加更多必要字段
                for field in required_fields:
                    if field not in rule_data:
                        return False, f"第{i + 1}条规则数据缺少必要字段: {field}"

            return True, f"数据完整性验证通过，包含{len(rule_data_list)}条规则"

        except Exception as e:
            return False, f"数据完整性验证异常: {str(e)}"

    def _calculate_optimal_batch_size(self, total_rules: int) -> int:
        """根据规则数量和内存状态计算最优批次大小"""
        try:
            # 基础批次大小
            base_batch_size = 1000

            # 根据总规则数量调整
            if total_rules < 1000:
                return min(total_rules, 100)  # 小数据集使用小批次
            elif total_rules < 10000:
                return min(total_rules // 5, 500)  # 中等数据集
            elif total_rules < 100000:
                return base_batch_size  # 大数据集使用标准批次
            else:
                return min(base_batch_size * 2, 2000)  # 超大数据集使用大批次

        except Exception as e:
            logger.warning(f"计算最优批次大小失败，使用默认值: {e}")
            return 1000

    def get_parsing_stats(self) -> FileParsingStats:
        """获取最近一次解析的性能统计"""
        return self.parsing_stats

    def get_performance_stats(self) -> SlaveIndexPerformanceStats:
        """获取完整的性能统计"""
        return self.performance_stats

    def get_performance_report(self) -> dict:
        """获取格式化的性能报告"""
        stats = self.performance_stats
        parsing_stats = self.parsing_stats

        return {
            "summary": {
                "uptime_hours": stats.get_uptime_hours(),
                "build_success_rate": stats.get_success_rate(),
                "hot_reload_success_rate": stats.get_hot_reload_success_rate(),
                "avg_throughput_rules_per_minute": stats.get_avg_throughput(),
            },
            "build_performance": {
                "total_builds": stats.build_count,
                "successful_builds": stats.successful_builds,
                "failed_builds": stats.failed_builds,
                "avg_build_time_ms": stats.avg_build_time_ms,
                "max_build_time_ms": stats.max_build_time_ms,
                "min_build_time_ms": stats.min_build_time_ms if stats.min_build_time_ms != float("inf") else 0.0,
                "total_rules_processed": stats.total_rules_processed,
            },
            "memory_performance": {
                "avg_memory_usage_mb": stats.avg_memory_usage_mb,
                "max_memory_usage_mb": stats.max_memory_usage_mb,
                "memory_optimization_count": stats.memory_optimization_count,
            },
            "hot_reload_performance": {
                "total_hot_reloads": stats.hot_reload_count,
                "successful_hot_reloads": stats.successful_hot_reloads,
                "failed_hot_reloads": stats.failed_hot_reloads,
                "avg_hot_reload_time_ms": stats.avg_hot_reload_time_ms,
                "max_hot_reload_time_ms": stats.max_hot_reload_time_ms,
            },
            "parsing_performance": {
                "last_parse_time_ms": parsing_stats.parse_time_ms,
                "last_file_size_mb": parsing_stats.file_size_mb,
                "last_success_rate": parsing_stats.get_success_rate(),
                "last_throughput_rules_per_sec": parsing_stats.get_throughput_rules_per_sec(),
                "last_memory_peak_mb": parsing_stats.memory_peak_mb,
            },
            "error_statistics": {
                "parsing_errors": stats.parsing_errors,
                "build_errors": stats.build_errors,
                "memory_errors": stats.memory_errors,
            },
            "timestamps": {
                "stats_start_time": stats.stats_start_time,
                "last_build_time": stats.last_build_time,
                "last_hot_reload_time": stats.last_hot_reload_time,
            },
        }

    def reset_performance_stats(self):
        """重置性能统计"""
        self.performance_stats.reset_stats()
        logger.info("从节点性能统计已重置")

    def get_error_handler_status(self) -> dict:
        """获取错误处理器状态"""
        return self.error_handler.get_error_statistics()

    def get_recent_errors(self, limit: int = 10) -> list[dict]:
        """获取最近的错误详情"""
        return self.error_handler.get_recent_error_details(limit)

    def force_error_recovery(self):
        """强制错误恢复（手动干预接口）"""
        self.error_handler.force_recovery()
        logger.info("已执行强制错误恢复")

    def clear_error_history(self):
        """清除错误历史"""
        self.error_handler.clear_error_history()
        logger.info("错误历史已清除")

    def is_in_degradation_mode(self) -> bool:
        """检查是否处于降级模式"""
        return self.error_handler.degradation_enabled

    def get_degradation_level(self) -> int:
        """获取当前降级级别"""
        return self.error_handler.degradation_level

    def check_performance_anomalies(self) -> dict:
        """检查性能异常"""
        stats = self.performance_stats
        anomalies = []

        # 检查构建成功率
        if stats.build_count > 5 and stats.get_success_rate() < 80.0:
            anomalies.append(
                {
                    "type": "low_build_success_rate",
                    "severity": "high",
                    "message": f"构建成功率过低: {stats.get_success_rate():.1f}%",
                    "threshold": "80%",
                    "current_value": f"{stats.get_success_rate():.1f}%",
                }
            )

        # 检查构建时间异常
        if stats.build_count > 0 and stats.max_build_time_ms > 30000:  # 30秒
            anomalies.append(
                {
                    "type": "high_build_time",
                    "severity": "medium",
                    "message": f"最大构建时间过长: {stats.max_build_time_ms:.0f}ms",
                    "threshold": "30000ms",
                    "current_value": f"{stats.max_build_time_ms:.0f}ms",
                }
            )

        # 检查内存使用异常
        if stats.max_memory_usage_mb > self._memory_limit_mb * 0.9:  # 超过90%限制
            anomalies.append(
                {
                    "type": "high_memory_usage",
                    "severity": "high",
                    "message": f"内存使用过高: {stats.max_memory_usage_mb:.1f}MB",
                    "threshold": f"{self._memory_limit_mb * 0.9:.1f}MB",
                    "current_value": f"{stats.max_memory_usage_mb:.1f}MB",
                }
            )

        # 检查热重载成功率
        if stats.hot_reload_count > 3 and stats.get_hot_reload_success_rate() < 70.0:
            anomalies.append(
                {
                    "type": "low_hot_reload_success_rate",
                    "severity": "medium",
                    "message": f"热重载成功率过低: {stats.get_hot_reload_success_rate():.1f}%",
                    "threshold": "70%",
                    "current_value": f"{stats.get_hot_reload_success_rate():.1f}%",
                }
            )

        # 检查内存优化频率
        if stats.build_count > 0 and (stats.memory_optimization_count / stats.build_count) > 0.5:
            anomalies.append(
                {
                    "type": "frequent_memory_optimization",
                    "severity": "medium",
                    "message": f"内存优化过于频繁: {stats.memory_optimization_count}次/{stats.build_count}次构建",
                    "threshold": "50%",
                    "current_value": f"{(stats.memory_optimization_count / stats.build_count * 100):.1f}%",
                }
            )

        return {
            "has_anomalies": len(anomalies) > 0,
            "anomaly_count": len(anomalies),
            "anomalies": anomalies,
            "check_time": time.time(),
        }

    def build_index_from_cache_file(self) -> bool:
        """从规则缓存文件构建索引"""
        try:
            start_time = time.perf_counter()

            # 1. 构建前内存状态检查
            memory_stats = self.memory_optimizer.get_memory_stats()
            if memory_stats.is_over_threshold:
                logger.info(f"构建前检测到内存压力 ({memory_stats.process_memory_mb:.1f}MB)，触发内存优化")
                self.memory_optimizer.optimize_memory(aggressive=False)

            # 2. 检查文件是否存在
            if not os.path.exists(self.rules_cache_path):
                logger.error(f"规则缓存文件不存在: {self.rules_cache_path}")
                return False

            # 3. 加载规则数据
            rule_details = self._load_rule_details_from_cache()

            if not rule_details:
                logger.warning("规则缓存文件为空，无法构建索引")
                return False

            # 4. 构建索引（使用与主节点相同的逻辑）
            total_rules = len(rule_details)
            logger.info(f"从节点开始构建索引，规则数量: {total_rules}")
            self.rule_index_manager.build_indexes_from_rule_details(rule_details)

            # 5. 构建后智能内存优化：基于实际内存使用情况决定是否需要清理
            memory_stats = self.memory_optimizer.get_memory_stats()
            if memory_stats.is_over_threshold or total_rules > self._gc_threshold:
                logger.info(f"索引构建完成后进行内存优化，当前使用: {memory_stats.process_memory_mb:.1f}MB")
                self.memory_optimizer.optimize_memory(aggressive=False)
                self.performance_stats.record_memory_optimization()

            build_time = time.perf_counter() - start_time
            build_time_ms = build_time * 1000

            # 记录构建成功的性能统计
            final_memory_stats = self.memory_optimizer.get_memory_stats()
            self.performance_stats.record_build_success(
                build_time_ms=build_time_ms, memory_mb=final_memory_stats.process_memory_mb, rules_count=total_rules
            )

            # 记录成功操作（用于错误恢复判断）
            self.error_handler.record_success()

            logger.info(f"从节点索引构建完成，耗时: {build_time_ms:.2f}ms")
            return True

        except Exception as e:
            # 使用错误处理器处理构建错误
            error_context = {
                "rules_cache_path": self.rules_cache_path,
                "memory_limit_mb": self._memory_limit_mb,
                "gc_threshold": self._gc_threshold,
            }
            self.error_handler.handle_build_error(e, error_context)

            # 记录构建失败的性能统计
            try:
                memory_stats = self.memory_optimizer.get_memory_stats()
                self.performance_stats.record_build_failure(error_msg=str(e), memory_mb=memory_stats.process_memory_mb)
            except Exception:
                # 如果获取内存统计失败，仍然记录构建失败
                self.performance_stats.record_build_failure(error_msg=str(e))

            logger.error(f"从节点索引构建失败: {e}", exc_info=True)
            return False

    def shutdown(self):
        """优雅关闭从节点索引构建器"""
        try:
            if hasattr(self, "memory_optimizer") and self.memory_optimizer:
                self.memory_optimizer.stop_monitoring()
                logger.info("从节点内存监控已停止")
        except Exception as e:
            logger.error(f"关闭内存监控时发生错误: {e}")

    def _load_rule_details_from_cache(self) -> list[Any]:
        """从压缩缓存文件加载规则明细（优化版本）"""
        parse_start_time = time.perf_counter()

        try:
            logger.info(f"开始加载规则缓存文件: {self.rules_cache_path}")

            # 重置解析统计
            self.parsing_stats = FileParsingStats()

            # 获取文件大小
            file_size_bytes = os.path.getsize(self.rules_cache_path)
            self.parsing_stats.file_size_mb = file_size_bytes / (1024 * 1024)

            # 记录初始内存状态
            initial_memory = self.memory_optimizer.get_memory_stats()
            self.parsing_stats.memory_peak_mb = initial_memory.process_memory_mb

            # 流式解析JSON文件
            with gzip.open(self.rules_cache_path, "rt", encoding="utf-8") as f:
                cache_data = json.load(f)

            # 数据完整性验证
            is_valid, validation_msg = self._validate_cache_data_integrity(cache_data)
            if not is_valid:
                logger.error(f"缓存数据完整性验证失败: {validation_msg}")
                return []

            logger.info(f"数据完整性验证: {validation_msg}")

            # 文件解析后内存检查
            memory_stats = self.memory_optimizer.get_memory_stats()
            self.parsing_stats.memory_peak_mb = max(self.parsing_stats.memory_peak_mb, memory_stats.process_memory_mb)

            if memory_stats.is_over_threshold:
                logger.info(f"文件解析后检测到内存压力 ({memory_stats.process_memory_mb:.1f}MB)，触发内存优化")
                self.memory_optimizer.optimize_memory(aggressive=False)
                self.parsing_stats.memory_optimizations += 1
                self.performance_stats.record_memory_optimization()

            # 获取规则数据列表
            rule_data_list = cache_data.get("rule_details", [])
            if not rule_data_list:
                # 尝试其他可能的数据结构
                if "rules" in cache_data:
                    rule_data_list = cache_data["rules"]
                elif isinstance(cache_data, list):
                    rule_data_list = cache_data

            self.parsing_stats.total_rules = len(rule_data_list)

            # 优化的分批处理规则数据
            rule_details = []
            batch_size = self._calculate_optimal_batch_size(len(rule_data_list))
            total_processed = 0

            logger.info(f"开始处理{len(rule_data_list)}条规则数据，批次大小: {batch_size}")

            for i in range(0, len(rule_data_list), batch_size):
                batch = rule_data_list[i : i + batch_size]
                self.parsing_stats.batch_count += 1

                # 处理当前批次
                batch_valid_count = 0
                for rule_data in batch:
                    rule_detail = self._create_rule_detail_from_dict(rule_data)
                    if rule_detail:
                        rule_details.append(rule_detail)
                        batch_valid_count += 1
                    else:
                        self.parsing_stats.invalid_rules += 1

                self.parsing_stats.valid_rules += batch_valid_count
                total_processed += len(batch)

                # 智能内存监控：根据批次大小和处理进度调整检查频率
                check_interval = max(1, self.parsing_stats.batch_count // 5)  # 每5批检查一次，最少1批
                if self.parsing_stats.batch_count % check_interval == 0:
                    memory_stats = self.memory_optimizer.get_memory_stats()
                    self.parsing_stats.memory_peak_mb = max(
                        self.parsing_stats.memory_peak_mb, memory_stats.process_memory_mb
                    )

                    if memory_stats.is_over_threshold:
                        logger.info(
                            f"处理规则数据时检测到内存压力，已处理: {total_processed}/{len(rule_data_list)} ({(total_processed / len(rule_data_list) * 100):.1f}%)"
                        )
                        self.memory_optimizer.optimize_memory(aggressive=False)
                        self.parsing_stats.memory_optimizations += 1
                        self.performance_stats.record_memory_optimization()

            # 计算解析统计
            self.parsing_stats.parse_time_ms = (time.perf_counter() - parse_start_time) * 1000

            # 记录详细的解析统计
            success_rate = self.parsing_stats.get_success_rate()
            throughput = self.parsing_stats.get_throughput_rules_per_sec()

            logger.info(
                f"规则解析完成 - 总数: {self.parsing_stats.total_rules}, "
                f"有效: {self.parsing_stats.valid_rules}, "
                f"无效: {self.parsing_stats.invalid_rules}, "
                f"成功率: {success_rate:.1f}%, "
                f"耗时: {self.parsing_stats.parse_time_ms:.2f}ms, "
                f"吞吐量: {throughput:.1f}规则/秒, "
                f"内存峰值: {self.parsing_stats.memory_peak_mb:.1f}MB, "
                f"内存优化次数: {self.parsing_stats.memory_optimizations}"
            )

            return rule_details

        except Exception as e:
            # 使用错误处理器处理解析错误
            error_context = {
                "cache_file_path": self.rules_cache_path,
                "file_size_mb": getattr(self.parsing_stats, "file_size_mb", 0),
            }
            self.error_handler.handle_parse_error(e, error_context)

            logger.error(f"加载规则缓存文件失败: {e}", exc_info=True)
            return []

    def _create_rule_detail_from_dict(self, rule_data: dict) -> Any | None:
        """从字典数据创建RuleDetail对象（优化版本）"""
        try:
            # 基础数据类型验证
            if not isinstance(rule_data, dict):
                logger.warning(f"规则数据不是字典格式: {type(rule_data)}")
                return None

            # 验证必要字段
            rule_id = rule_data.get("rule_id")
            if not rule_id or not isinstance(rule_id, (str, int)):
                logger.warning(f"规则数据缺少有效的rule_id字段: {rule_data.get('rule_id')}")
                return None

            # 创建Mock对象模拟RuleDetail（优化内存使用）
            rule_detail = Mock()

            # 安全地设置字段，确保数据类型正确
            rule_detail.rule_id = str(rule_id)  # 统一转换为字符串
            rule_detail.yb_code = self._safe_get_string(rule_data, "yb_code", "")
            rule_detail.diag_whole_code = self._safe_get_string(rule_data, "diag_whole_code", "")
            rule_detail.diag_code_prefix = self._safe_get_string(rule_data, "diag_code_prefix", "")
            rule_detail.fee_whole_code = self._safe_get_string(rule_data, "fee_whole_code", "")
            rule_detail.fee_code_prefix = self._safe_get_string(rule_data, "fee_code_prefix", "")

            # 处理扩展字段
            extended_fields = rule_data.get("extended_fields", "{}")
            if isinstance(extended_fields, dict):
                # 只在必要时进行JSON序列化，减少内存开销
                rule_detail.extended_fields = json.dumps(extended_fields, ensure_ascii=False, separators=(",", ":"))
            elif isinstance(extended_fields, str):
                rule_detail.extended_fields = extended_fields
            else:
                rule_detail.extended_fields = "{}"

            return rule_detail

        except Exception as e:
            logger.error(f"创建规则对象失败: {e}, 数据: {rule_data}")
            return None

    def _safe_get_string(self, data: dict, key: str, default: str = "") -> str:
        """安全地从字典中获取字符串值"""
        try:
            value = data.get(key, default)
            if value is None:
                return default
            return str(value)
        except Exception:
            return default


class IndexHotReloader:
    """增强的索引热重载器（原子性操作）"""

    def __init__(self, rule_index_manager, slave_builder: SlaveNodeIndexBuilder):
        self.rule_index_manager = rule_index_manager
        self.slave_builder = slave_builder
        self._file_watcher = None
        self._last_reload_time = 0
        self._reload_cooldown = 5  # 5秒冷却时间，避免频繁重载

        # 原子性操作支持
        self._reload_lock = threading.RLock()
        self._reload_timeout = settings.SLAVE_INDEX_HOT_RELOAD_TIMEOUT  # 使用配置的超时时间
        self._current_backup: IndexBackup | None = None
        self._reload_in_progress = False

        # 统计信息
        self._reload_count = 0
        self._success_count = 0
        self._failure_count = 0
        self._timeout_count = 0

    def _create_index_backup(self) -> IndexBackup:
        """创建索引状态备份"""
        try:
            import uuid

            # 获取当前索引状态
            index_state = {}
            rule_count = 0

            if hasattr(self.rule_index_manager, "get_index_state"):
                index_state = self.rule_index_manager.get_index_state()
                rule_count = index_state.get("rule_count", 0)
            else:
                # 如果没有专门的状态获取方法，尝试获取关键索引信息
                yb_code_index = getattr(self.rule_index_manager, "yb_code_index", {})
                diag_code_index = getattr(self.rule_index_manager, "diag_code_index", {})
                fee_code_index = getattr(self.rule_index_manager, "fee_code_index", {})

                index_state = {
                    "yb_code_index": yb_code_index,
                    "diag_code_index": diag_code_index,
                    "fee_code_index": fee_code_index,
                }

                # 安全地计算规则数量
                try:
                    rule_count = len(yb_code_index) if yb_code_index else 0
                except (TypeError, AttributeError):
                    rule_count = 0

            backup = IndexBackup(
                backup_time=time.time(),
                index_state=index_state.copy(),
                rule_count=rule_count,
                backup_id=str(uuid.uuid4())[:8],
            )

            logger.info(f"创建索引备份成功，备份ID: {backup.backup_id}, 规则数量: {backup.rule_count}")
            return backup

        except Exception as e:
            logger.error(f"创建索引备份失败: {e}", exc_info=True)
            # 返回空备份，确保不会因为备份失败而阻止重载
            return IndexBackup(backup_time=time.time(), index_state={}, rule_count=0, backup_id="empty")

    def _restore_from_backup(self, backup: IndexBackup) -> bool:
        """从备份恢复索引状态"""
        try:
            if not backup or not backup.is_valid():
                logger.error("备份数据无效，无法恢复")
                return False

            # 恢复索引状态
            if hasattr(self.rule_index_manager, "restore_index_state"):
                success = self.rule_index_manager.restore_index_state(backup.index_state)
            else:
                # 如果没有专门的恢复方法，尝试直接设置索引
                try:
                    if "yb_code_index" in backup.index_state:
                        self.rule_index_manager.yb_code_index = backup.index_state["yb_code_index"]
                    if "diag_code_index" in backup.index_state:
                        self.rule_index_manager.diag_code_index = backup.index_state["diag_code_index"]
                    if "fee_code_index" in backup.index_state:
                        self.rule_index_manager.fee_code_index = backup.index_state["fee_code_index"]
                    success = True
                except Exception:
                    success = False

            if success:
                logger.info(f"从备份恢复索引成功，备份ID: {backup.backup_id}, 规则数量: {backup.rule_count}")
            else:
                logger.error(f"从备份恢复索引失败，备份ID: {backup.backup_id}")

            return success

        except Exception as e:
            logger.error(f"恢复索引状态异常: {e}", exc_info=True)
            return False

    def start_watching(self):
        """开始监控规则文件变化"""
        try:
            from watchdog.events import FileSystemEventHandler
            from watchdog.observers import Observer

            class RuleCacheHandler(FileSystemEventHandler):
                def __init__(self, reloader):
                    self.reloader = reloader

                def on_modified(self, event):
                    if event.src_path.endswith("rules_cache.json.gz"):
                        current_time = time.time()
                        if current_time - self.reloader._last_reload_time > self.reloader._reload_cooldown:
                            logger.info("检测到规则缓存文件更新，开始重建索引")
                            self.reloader.reload_index()
                            self.reloader._last_reload_time = current_time

            self._file_watcher = Observer()
            self._file_watcher.schedule(
                RuleCacheHandler(self),
                path=os.path.dirname(os.path.abspath(self.slave_builder.rules_cache_path)),
                recursive=False,
            )
            self._file_watcher.start()
            logger.info("索引热重载监控已启动")

        except ImportError:
            logger.warning("watchdog库未安装，无法启用热重载功能")
        except Exception as e:
            logger.error(f"启动文件监控失败: {e}")

    def stop_watching(self):
        """停止监控"""
        if self._file_watcher:
            self._file_watcher.stop()
            self._file_watcher.join()
            logger.info("索引热重载监控已停止")

    async def _async_reload_index(self) -> bool:
        """异步执行索引重载"""
        try:
            logger.info("开始异步索引重载...")

            # 在线程池中执行同步的索引构建
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(None, self.slave_builder.build_index_from_cache_file)

            if success:
                logger.info("异步索引重载成功")
            else:
                logger.error("异步索引重载失败")

            return success

        except Exception as e:
            logger.error(f"异步索引重载异常: {e}", exc_info=True)
            return False

    def reload_index(self) -> bool:
        """原子性热重载索引"""
        with self._reload_lock:
            if self._reload_in_progress:
                logger.warning("索引重载正在进行中，跳过本次请求")
                return False

            self._reload_in_progress = True
            self._reload_count += 1
            reload_start_time = time.perf_counter()

            try:
                logger.info(f"开始原子性索引热重载 (第{self._reload_count}次)...")

                # 1. 创建当前索引备份
                self._current_backup = self._create_index_backup()

                # 2. 异步执行重载，带超时控制
                try:
                    # 创建新的事件循环（如果当前线程没有）
                    try:
                        loop = asyncio.get_running_loop()
                    except RuntimeError:
                        # 没有运行中的事件循环，创建新的
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                    # 执行异步重载，带超时控制
                    future = asyncio.ensure_future(self._async_reload_index())
                    success = loop.run_until_complete(asyncio.wait_for(future, timeout=self._reload_timeout))

                except asyncio.TimeoutError:
                    logger.error(f"索引重载超时（>{self._reload_timeout}秒），开始恢复备份")
                    self._timeout_count += 1
                    success = False

                except Exception as e:
                    logger.error(f"异步重载执行异常: {e}", exc_info=True)
                    success = False

                # 3. 处理重载结果
                if success:
                    self._success_count += 1
                    reload_time = (time.perf_counter() - reload_start_time) * 1000

                    # 记录热重载成功的性能统计
                    self.slave_builder.performance_stats.record_hot_reload_success(reload_time)

                    # 记录成功操作（用于错误恢复判断）
                    self.slave_builder.error_handler.record_success()

                    logger.info(f"原子性索引热重载成功，耗时: {reload_time:.2f}ms")
                    return True
                else:
                    # 4. 重载失败，恢复备份
                    self._failure_count += 1

                    # 记录热重载失败的性能统计
                    self.slave_builder.performance_stats.record_hot_reload_failure()

                    logger.error("索引重载失败，开始恢复原有索引状态")

                    if self._current_backup:
                        restore_success = self._restore_from_backup(self._current_backup)
                        if restore_success:
                            logger.info("索引状态恢复成功，系统保持稳定")
                        else:
                            logger.error("索引状态恢复失败，系统可能不稳定")

                    return False

            except Exception as e:
                self._failure_count += 1

                # 使用错误处理器处理热重载错误
                error_context = {
                    "reload_timeout": self._reload_timeout,
                    "failure_count": self._failure_count,
                    "success_count": self._success_count,
                }
                self.slave_builder.error_handler.handle_reload_error(e, error_context)

                # 记录热重载异常的性能统计
                self.slave_builder.performance_stats.record_hot_reload_failure()

                logger.error(f"原子性索引热重载异常: {e}", exc_info=True)

                # 尝试恢复备份
                if self._current_backup:
                    self._restore_from_backup(self._current_backup)

                return False

            finally:
                self._reload_in_progress = False
                self._current_backup = None

    def get_reload_stats(self) -> dict:
        """获取热重载统计信息"""
        return {
            "total_reloads": self._reload_count,
            "successful_reloads": self._success_count,
            "failed_reloads": self._failure_count,
            "timeout_reloads": self._timeout_count,
            "success_rate": (self._success_count / max(self._reload_count, 1)) * 100,
            "is_reload_in_progress": self._reload_in_progress,
            "reload_timeout_seconds": self._reload_timeout,
            "reload_cooldown_seconds": self._reload_cooldown,
        }

    def reset_stats(self):
        """重置统计信息"""
        self._reload_count = 0
        self._success_count = 0
        self._failure_count = 0
        self._timeout_count = 0
        logger.info("热重载统计信息已重置")


# 全局实例
slave_index_builder = None
index_hot_reloader = None


def initialize_slave_index_builder(rule_index_manager, rules_cache_path: str = "rules_cache.json.gz"):
    """初始化从节点索引构建器"""
    global slave_index_builder, index_hot_reloader

    slave_index_builder = SlaveNodeIndexBuilder(rule_index_manager, rules_cache_path)
    index_hot_reloader = IndexHotReloader(rule_index_manager, slave_index_builder)

    return slave_index_builder, index_hot_reloader


def build_slave_index(
    rule_index_manager, rules_cache_path: str = "rules_cache.json.gz", enable_hot_reload: bool = True
) -> bool:
    """构建从节点索引的便捷函数"""
    builder, reloader = initialize_slave_index_builder(rule_index_manager, rules_cache_path)

    # 构建索引
    success = builder.build_index_from_cache_file()

    # 启动热重载（如果需要）
    if success and enable_hot_reload:
        reloader.start_watching()

    return success
