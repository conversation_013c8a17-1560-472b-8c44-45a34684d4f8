# Import your settings and models
import os
import sys
from logging.config import fileConfig

from sqlalchemy import create_engine, pool

from alembic import context
from config.settings import settings
from models.database import Base

# Import all models to ensure they are registered with Base.metadata
from models.registration_task import RegistrationTaskStatus

# This is a hack to ensure the models module is in the python path
sys.path.insert(0, os.path.realpath(os.path.join(os.path.dirname(__file__), "..")))

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set the database URL from settings (支持新的配置方式)
database_url = settings.get_database_url()
if not database_url:
    is_valid, error_msg = settings.validate_database_config()
    if not is_valid:
        raise ValueError(f"数据库配置错误: {error_msg}")
    else:
        raise ValueError("无法构建数据库连接URL，请检查数据库配置")

config.set_main_option("sqlalchemy.url", database_url)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection):
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # 使用新的配置方式获取数据库URL
    database_url = settings.get_database_url()
    if not database_url:
        raise ValueError("无法获取数据库连接URL")

    connectable = create_engine(
        database_url,
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        do_run_migrations(connection)

    connectable.dispose()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
