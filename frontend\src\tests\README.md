# 前端单元测试指南

## 📋 测试现状

### ✅ 已完成
- 测试基础设施搭建（Vitest + Vue Test Utils + Element Plus）
- 测试工具函数库（testHelpers.js）
- 性能优化组件（VirtualScroller、performanceUtils）
- 基础测试用例（82个通过）

### ❌ 待修复问题
- 73个测试用例失败，主要问题如下：

## 🔧 问题修复指南

### 1. API路径不匹配问题

**问题**：测试期望的API路径与实际实现不符

**修复方案**：
```javascript
// 当前测试期望
expect(request.post).toHaveBeenCalledWith(`/v1/rules/${ruleKey}/details`, mockData)

// 实际API路径
expect(request.post).toHaveBeenCalledWith(`/v1/rules/details/${ruleKey}`, mockData)
```

**修复文件**：
- `src/tests/api/ruleDetails.test.js`

### 2. 组件DOM元素缺失问题

**问题**：组件测试找不到对应的DOM元素

**修复方案**：在组件中添加data-testid属性
```vue
<!-- DataUploader.vue -->
<template>
  <div class="data-uploader">
    <h1 data-testid="page-title">数据上传</h1>
    <div class="upload-area" data-testid="upload-area">
      <input type="file" data-testid="file-input" />
      <select data-testid="upload-mode-selector">
        <option value="full">全量上传</option>
        <option value="incremental">增量上传</option>
      </select>
      <select data-testid="rule-selector">
        <!-- 规则选项 -->
      </select>
    </div>
  </div>
</template>
```

**修复文件**：
- `src/views/DataUploader.vue`
- `src/views/RuleDashboard.vue`
- `src/components/common/BreadcrumbNavigation.vue`

### 3. Vue Router Mock配置问题

**问题**：vue-router Mock配置不完整

**修复方案**：完善testHelpers.js中的Mock配置
```javascript
// testHelpers.js
vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')
  return {
    ...actual,
    useRoute: vi.fn(() => ({
      path: '/rules/test-rule/details',
      params: { ruleKey: 'test-rule' },
      query: {},
      meta: { title: '规则明细' }
    })),
    useRouter: vi.fn(() => ({
      push: vi.fn(),
      replace: vi.fn()
    }))
  }
})
```

### 4. useStateMachine Composable实现

**问题**：测试存在但实际实现缺失

**修复方案**：创建完整的useStateMachine实现
```javascript
// src/composables/core/useStateMachine.js
import { ref, computed } from 'vue'

export function useStateMachine(config) {
  const currentState = ref(config.initialState)
  const stateHistory = ref([{
    state: config.initialState,
    event: null,
    timestamp: Date.now()
  }])
  
  // 实现状态转换、事件监听、守卫等功能
  // ...
  
  return {
    currentState,
    stateHistory,
    transition,
    on,
    off,
    // 其他方法...
  }
}
```

### 5. 组件Store依赖问题

**问题**：组件依赖的stores在测试环境中配置不正确

**修复方案**：完善Mock stores配置
```javascript
// 在组件测试中
vi.mock('@/stores/rules', () => ({
  useRulesStore: () => ({
    rules: [],
    loading: false,
    error: null,
    fetchRules: vi.fn(),
    updateRule: vi.fn(),
    clearError: vi.fn()
  })
}))
```

## 🚀 运行测试

```bash
# 运行所有测试
npm run test

# 运行特定测试文件
npm run test src/tests/components/RuleDashboard.test.js

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行性能基准测试
npm run test:bench
```

## 📊 测试覆盖率目标

- **当前覆盖率**：约60%（82/155测试通过）
- **目标覆盖率**：90%以上
- **重点覆盖**：
  - 组件渲染和交互
  - 状态管理逻辑
  - API调用和错误处理
  - 性能优化功能

## 🔍 测试最佳实践

### 1. 组件测试
- 使用data-testid而不是CSS类名选择器
- 测试用户交互而不是实现细节
- Mock外部依赖（API、stores、router）

### 2. 状态管理测试
- 测试actions、mutations、getters
- 验证状态变化和副作用
- Mock API调用

### 3. 性能测试
- 使用性能基准测试验证优化效果
- 监控内存使用和渲染时间
- 测试大数据量场景

## 📝 后续计划

1. **第一阶段**：修复API路径和组件DOM问题（预计1天）
2. **第二阶段**：实现缺失的Composable功能（预计1天）
3. **第三阶段**：完善Mock配置和提升覆盖率（预计1天）
4. **第四阶段**：性能测试优化和验证（预计1天）

## 🎯 验收标准

- [ ] 所有测试用例通过（155/155）
- [ ] 测试覆盖率达到90%以上
- [ ] 性能基准测试通过
- [ ] 组件交互测试完整
- [ ] 错误处理测试覆盖
