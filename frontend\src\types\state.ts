/**
 * 状态管理相关类型定义
 * 企业级状态管理架构的核心类型系统
 */

// ==================== 异步状态机类型 ====================

/**
 * 异步操作状态枚举
 */
export enum AsyncStates {
  IDLE = 'idle',           // 空闲状态
  LOADING = 'loading',     // 加载中
  SUCCESS = 'success',     // 成功
  ERROR = 'error',         // 错误
  RETRYING = 'retrying'    // 重试中
}

/**
 * 状态转换事件类型
 */
export enum StateEvents {
  START = 'start',         // 开始执行
  SUCCESS = 'success',     // 执行成功
  ERROR = 'error',         // 执行失败
  RETRY = 'retry',         // 重试
  RESET = 'reset'          // 重置
}

/**
 * 状态机配置接口
 */
export interface StateMachineConfig {
  initialState: AsyncStates
  states: Record<AsyncStates, StateConfig>
  transitions: Record<AsyncStates, AsyncStates[]>
}

/**
 * 单个状态配置
 */
export interface StateConfig {
  onEnter?: () => void
  onExit?: () => void
  onError?: (error: Error) => void
}

// ==================== 异步状态管理类型 ====================

/**
 * 异步状态数据接口
 */
export interface AsyncStateData<T = any> {
  data: T | null
  error: Error | null
  state: AsyncStates
  isLoading: boolean
  isSuccess: boolean
  isError: boolean
  isRetrying: boolean
}

/**
 * 异步状态选项
 */
export interface AsyncStateOptions<T = any> {
  immediate?: boolean                    // 是否立即执行
  resetOnExecute?: boolean              // 执行时是否重置状态
  retryCount?: number                   // 重试次数
  retryDelay?: number | number[]        // 重试延迟
  timeout?: number                      // 超时时间
  onSuccess?: (data: T) => void         // 成功回调
  onError?: (error: Error) => void      // 错误回调
  onFinally?: () => void                // 完成回调
  onRetry?: (attempt: number) => void   // 重试回调
}

/**
 * 异步函数类型
 */
export type AsyncFunction<T = any, P extends any[] = any[]> = (...args: P) => Promise<T>

// ==================== 全局应用状态类型 ====================

/**
 * 全局加载状态
 */
export interface GlobalLoadingState {
  isLoading: boolean
  loadingTasks: Set<string>
  loadingMessage: string
  progress?: number
}

/**
 * 全局错误状态
 */
export interface GlobalErrorState {
  currentError: Error | null
  errorHistory: ErrorRecord[]
  errorCount: number
  lastErrorTime: number | null
}

/**
 * 错误记录
 */
export interface ErrorRecord {
  id: string
  error: Error
  timestamp: number
  context?: Record<string, any>
  resolved: boolean
}

/**
 * 通知状态
 */
export interface NotificationState {
  notifications: NotificationItem[]
  toasts: ToastItem[]
  maxNotifications: number
  maxToasts: number
}

/**
 * 通知项
 */
export interface NotificationItem {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: number
  duration?: number
  persistent?: boolean
  actions?: NotificationAction[]
}

/**
 * Toast项
 */
export interface ToastItem {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
  timestamp: number
  duration: number
}

/**
 * 通知操作
 */
export interface NotificationAction {
  label: string
  action: () => void
  type?: 'primary' | 'secondary'
}

// ==================== 状态持久化类型 ====================

/**
 * 持久化配置
 */
export interface PersistenceConfig {
  key: string
  storage: 'localStorage' | 'sessionStorage'
  serialize?: (value: any) => string
  deserialize?: (value: string) => any
  expires?: number
}

/**
 * 持久化状态
 */
export interface PersistedState {
  data: any
  timestamp: number
  version: string
  expires?: number
}

// ==================== 性能监控类型 ====================

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  asyncOperations: AsyncOperationMetric[]
  stateUpdates: StateUpdateMetric[]
  errorRate: number
  averageResponseTime: number
  memoryUsage?: number
}

/**
 * 异步操作指标
 */
export interface AsyncOperationMetric {
  id: string
  name: string
  startTime: number
  endTime?: number
  duration?: number
  success: boolean
  retryCount: number
  error?: string
}

/**
 * 状态更新指标
 */
export interface StateUpdateMetric {
  timestamp: number
  stateName: string
  oldState: any
  newState: any
  updateCount: number
}

// ==================== 工具类型 ====================

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * 可选字段类型
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/**
 * 状态更新函数类型
 */
export type StateUpdater<T> = (prevState: T) => T

/**
 * 状态监听器类型
 */
export type StateListener<T> = (newState: T, oldState: T) => void
