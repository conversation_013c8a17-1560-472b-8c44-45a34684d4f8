<template>
  <div class="rule-dashboard">
    <!-- 页面头部 - 添加毛玻璃效果 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h1 class="page-title">模板状态仪表盘</h1>
        <p class="page-description">管理和监控所有规则的状态，支持模板下载和数据上传</p>
      </div>
      <div class="header-actions">
        <el-button
          class="refresh-button"
          :icon="Refresh"
          @click="handleRefresh"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 数据展示区域 - 改进卡片设计 -->
    <el-card class="data-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>模板列表</span>
          <el-switch
            v-model="cardView"
            active-text="卡片视图"
            inactive-text="列表视图"
            class="view-switch"
          />
        </div>
      </template>

      <!-- 卡片视图 - 完全重写使用内联样式 -->
      <div v-if="cardView" :style="{
        display: 'grid',
        gap: '20px',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))'
      }" class="responsive-grid">
        <div
          v-for="rule in rules"
          :key="rule.rule_key"
          :style="{
            background: 'white',
            borderRadius: '8px',
            padding: '20px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            height: 'auto',
            display: 'flex',
            flexDirection: 'column'
          }"
          @mouseenter="onCardHover($event)"
          @mouseleave="onCardLeave($event)"
        >
          <!-- 卡片头部 -->
          <div :style="{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '16px'
          }">
            <h3 :style="{
              fontSize: '18px',
              fontWeight: '600',
              margin: '0',
              color: '#303133',
              lineHeight: '1.4',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: '2',
              WebkitBoxOrient: 'vertical'
            }" :title="rule.rule_name">{{ rule.rule_name }}</h3>
            <StatusTag :status="rule.status" />
          </div>

          <!-- 规则描述区域 -->
          <div v-if="rule.description && rule.description.trim()" :style="{
            minHeight: '60px',
            display: 'flex',
            alignItems: 'flex-start',
            marginBottom: '16px',
            flex: '1'
          }">
            <el-tooltip
              :content="formatDescription(rule.description, false)"
              placement="top"
              effect="dark"
              :show-after="500"
              :enterable="true"
              :hide-after="3000"
              popper-class="rule-description-tooltip"
            >
              <div :style="{
                width: '100%',
                margin: '0',
                fontSize: '14px',
                lineHeight: '1.5',
                color: '#606266',
                wordBreak: 'break-word',
                cursor: 'help'
              }">
                <p :style="{ margin: '0' }" v-html="formatDescription(rule.description, true)"></p>
              </div>
            </el-tooltip>
          </div>
          <div v-else :style="{
            minHeight: '60px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '16px',
            flex: '1'
          }">
            <p :style="{
              margin: '0',
              fontSize: '14px',
              color: '#C0C4CC',
              fontStyle: 'italic'
            }">暂无规则描述</p>
          </div>

          <!-- 卡片操作区 -->
          <div :style="{
            display: 'flex',
            gap: '8px',
            justifyContent: 'flex-end',
            marginTop: 'auto'
          }">
            <button :style="{
              padding: '8px 16px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              background: rule.status === 'DEPRECATED' ? '#f5f7fa' : 'white',
              cursor: rule.status === 'DEPRECATED' ? 'not-allowed' : 'pointer',
              fontSize: '14px',
              transition: 'all 0.3s ease',
              color: rule.status === 'DEPRECATED' ? '#c0c4cc' : '#606266',
              opacity: rule.status === 'DEPRECATED' ? 0.6 : 1
            }"
            :disabled="rule.status === 'DEPRECATED'"
            @click="rule.status !== 'DEPRECATED' && handleViewDetail(rule)"
            @mouseenter="rule.status !== 'DEPRECATED' && onBtnHover($event)"
            @mouseleave="rule.status !== 'DEPRECATED' && onBtnLeave($event)"
            :title="rule.status === 'DEPRECATED' ? '已弃用的规则模板无法查看详情' : '查看规则模板详情'">
              详情
            </button>
            <button :style="{
              padding: '8px 16px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              background: rule.status === 'DEPRECATED' ? '#f5f7fa' : 'white',
              cursor: rule.status === 'DEPRECATED' ? 'not-allowed' : 'pointer',
              fontSize: '14px',
              transition: 'all 0.3s ease',
              color: rule.status === 'DEPRECATED' ? '#c0c4cc' : '#606266',
              opacity: rule.status === 'DEPRECATED' ? 0.6 : 1
            }"
            :disabled="rule.status === 'DEPRECATED'"
            @click="rule.status !== 'DEPRECATED' && handleDownloadTemplate(rule)"
            @mouseenter="rule.status !== 'DEPRECATED' && onBtnHover($event)"
            @mouseleave="rule.status !== 'DEPRECATED' && onBtnLeave($event)"
            :title="rule.status === 'DEPRECATED' ? '已弃用的规则模板无法下载' : '下载模板'">
              下载模板
            </button>
            <button :style="{
              padding: '8px 16px',
              borderRadius: '4px',
              fontSize: '14px',
              backgroundColor: rule.status === 'DEPRECATED' ? '#c0c4cc' : '#409eff',
              color: 'white',
              border: 'none',
              cursor: rule.status === 'DEPRECATED' ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              opacity: rule.status === 'DEPRECATED' ? 0.6 : 1
            }"
            :disabled="rule.status === 'DEPRECATED'"
            @click="rule.status !== 'DEPRECATED' && handleUploadData(rule)"
            @mouseenter="rule.status !== 'DEPRECATED' && onPrimaryBtnHover($event)"
            @mouseleave="rule.status !== 'DEPRECATED' && onPrimaryBtnLeave($event)"
            :title="rule.status === 'DEPRECATED' ? '已弃用的规则模板无法上传数据' : '上传数据'">
              上传数据
            </button>
          </div>
        </div>
        <el-empty v-if="rules.length === 0" description="暂无规则数据" />
      </div>

      <!-- 表格视图 -->
      <el-table v-else :data="rules" style="width: 100%" class="rules-table">
        <el-table-column prop="rule_name" label="规则名称" min-width="200" />
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="{ row }">
            <StatusTag :status="row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" align="center">
          <template #default="{ row }">
            <el-button
              size="small"
              :disabled="row.status === 'DEPRECATED'"
              @click="handleViewDetail(row)"
              class="table-action-btn"
              :title="row.status === 'DEPRECATED' ? '已弃用的规则模板无法查看详情' : '查看规则模板详情'">
              详情
            </el-button>
            <el-button
              size="small"
              :disabled="row.status === 'DEPRECATED'"
              @click="handleDownloadTemplate(row)"
              class="table-action-btn"
              :title="row.status === 'DEPRECATED' ? '已弃用的规则模板无法下载' : '下载模板'">
              下载模板
            </el-button>
            <el-button
              type="primary"
              size="small"
              :disabled="row.status === 'DEPRECATED'"
              @click="handleUploadData(row)"
              class="table-action-btn"
              :title="row.status === 'DEPRECATED' ? '已弃用的规则模板无法上传数据' : '上传数据'">
              上传数据
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Refresh, Download, Upload, CircleCheck, Warning, InfoFilled, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { get } from '../api/request'
import fileSaver from 'file-saver'
import StatusTag from '../components/common/StatusTag.vue'
import { useRuleManagement } from '../composables/business/useRuleManagement'

const router = useRouter()

// 响应式状态
const rules = ref([])
const loading = ref(false)
const cardView = ref(true)

// API Key现在通过request.js统一处理，不需要在这里定义

// 格式化日期
const formatDate = (date) => {
  if (!date) return '未知'
  const d = new Date(date)
  if (isNaN(d.getTime())) return '未知'

  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 悬浮效果方法
const onCardHover = (event) => {
  event.target.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)'
  event.target.style.transform = 'translateY(-2px)'
}

const onCardLeave = (event) => {
  event.target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)'
  event.target.style.transform = 'translateY(0)'
}

const onBtnHover = (event) => {
  event.target.style.borderColor = '#409eff'
  event.target.style.color = '#409eff'
}

const onBtnLeave = (event) => {
  event.target.style.borderColor = '#dcdfe6'
  event.target.style.color = '#606266'
}

const onPrimaryBtnHover = (event) => {
  event.target.style.backgroundColor = '#66b1ff'
}

const onPrimaryBtnLeave = (event) => {
  event.target.style.backgroundColor = '#409eff'
}

// 格式化规则描述文本，处理换行符
const formatDescription = (text, truncate = false) => {
  if (!text) return ''

  // 统一处理不同系统的换行符
  let formattedText = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n')

  // 处理多个连续换行为最多两个换行（避免过多空白）
  formattedText = formattedText.replace(/\n{3,}/g, '\n\n')

  // 如果需要截断
  if (truncate && formattedText.length > 100) {
    // 在截断点查找最近的换行符
    const truncatePoint = formattedText.substring(0, 100).lastIndexOf('\n')

    // 如果在合理范围内找到换行符，就在换行处截断
    if (truncatePoint > 70) {
      formattedText = formattedText.substring(0, truncatePoint) + '...'
    } else {
      // 否则在100字符处截断
      formattedText = formattedText.substring(0, 100) + '...'
    }
  }

  // 将换行符转换为HTML换行标签，并处理序号和缩进
  return formattedText
    .split('\n')
    .map(line => {
      // 识别并增强序号格式（如：1. 2. 等）
      return line.replace(/^(\d+\.)\s+(.*)/, '<span class="list-number">$1</span> $2')
    })
    .join('<br>')
}

// 方法
const fetchRules = async () => {
  loading.value = true
  try {
    const response = await get('/v1/rules/status')

    // 处理统一API响应格式
    let rulesData = []
    if (response && typeof response === 'object') {
      if (response.success && response.data) {
        // 新的统一格式：{success: true, data: [...]}
        rulesData = response.data
      } else if (Array.isArray(response)) {
        // 旧格式：直接是数组
        rulesData = response
      } else {
        console.warn('未知的API响应格式:', response)
        rulesData = []
      }
    }

    rules.value = rulesData || []
    ElMessage.success('模板列表加载成功')
  } catch (error) {
    console.error('获取模板列表失败:', error)
    ElMessage.error('获取模板列表失败，请检查后端服务是否正常')
    rules.value = []
  } finally {
    loading.value = false
  }
}

const handleRefresh = () => {
  fetchRules()
}

const handleDownloadTemplate = async (rule) => {
  // 使用统一的composable处理下载
  const { handleDownloadTemplate: downloadTemplate } = useRuleManagement()
  await downloadTemplate(rule)
}

const handleViewDetail = (rule) => {
  // 检查rule_key是否存在
  if (!rule?.rule_key) {
    ElMessage.error('规则键不存在，无法查看详情')
    return
  }
  // 跳转到规则模板详情页面
  router.push(`/rule-template-detail/${rule.rule_key}`)
}

const handleUploadData = (rule) => {
  // 检查rule_key是否存在
  if (!rule?.rule_key) {
    ElMessage.error('规则键不存在，无法上传数据')
    return
  }
  router.push({ name: 'DataUpload', params: { ruleKey: rule.rule_key } })
}

// 生命周期
onMounted(() => {
  fetchRules()
})
</script>

<style>
/* 全局样式，不受scoped限制 */
.rule-description-tooltip {
  max-width: 300px !important;
  line-height: 1.5 !important;
  white-space: normal !important;
  word-break: break-word !important;
}

.rule-description-tooltip .el-tooltip__popper-content {
  white-space: pre-line !important;
}

.rule-description-tooltip .list-number {
  font-weight: 600;
  color: #409eff;
  margin-right: 2px;
}
</style>

<style scoped>
.rule-dashboard {
  padding: 24px;
  background-image: linear-gradient(135deg, #f0f8ff, #f5f7fa, #f0f5ff);
  background-attachment: fixed;
  min-height: calc(100vh - 60px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 添加背景装饰 */
.rule-dashboard::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 80%;
  height: 80%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0) 70%);
  z-index: 0;
}

.rule-dashboard::after {
  content: '';
  position: absolute;
  bottom: -50%;
  left: -50%;
  width: 80%;
  height: 80%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(103, 194, 58, 0.05) 0%, rgba(103, 194, 58, 0) 70%);
  z-index: 0;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 24px 32px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
  z-index: 1;
}

.dashboard-header:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.header-content {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  letter-spacing: -0.5px;
  position: relative;
}

.page-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -4px;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #a0cfff);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.dashboard-header:hover .page-title::after {
  width: 60px;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 16px;
  max-width: 600px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.refresh-button {
  background: linear-gradient(135deg, #409eff, #53a8ff);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.refresh-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
}

.refresh-button:active {
  transform: translateY(0);
}

/* 添加波纹效果 */
.refresh-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: width 0.6s ease-out, height 0.6s ease-out, opacity 0.6s ease-out;
}

.refresh-button:active::before {
  width: 300%;
  height: 300%;
  opacity: 1;
  transition: width 0s, height 0s;
}

.data-card {
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  padding: 0 8px;
}

.view-switch {
  --el-switch-on-color: #409eff;
}

/* 响应式卡片布局 */
.card-view .el-row {
  margin-bottom: -36px;
}

.card-col {
  margin-bottom: 36px;
  transition: transform 0.3s ease;
}

/* 卡片样式 - 现代化设计 */
.rule-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 12px;
  padding: 24px;
  height: 100%;
  min-height: 140px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(235, 238, 245, 0.8);
  position: relative;
  overflow: hidden;
  margin-bottom: 6px; /* 添加底部边距，增强视觉分离 */
}

.rule-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #53a8ff);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rule-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
}

.rule-card:hover::before {
  opacity: 1;
}

.rule-card--deprecated {
  background: rgba(249, 249, 249, 0.9);
  border: 1px solid rgba(220, 223, 230, 0.8);
}

.rule-card--deprecated::before {
  background: linear-gradient(90deg, #909399, #c0c4cc);
}

/* 规则头部样式 */
.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(240, 242, 245, 0.8);
}

.rule-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #303133;
  line-height: 1.4;
  /* 确保标题过长时截断 */
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示两行 */
  -webkit-box-orient: vertical;
}

/* 规则内容区域 */
.rule-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 规则描述区域 */
.rule-description {
  min-height: 60px;
  display: flex;
  align-items: flex-start;
  background-color: rgba(249, 250, 252, 0.7);
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #e6e8eb;
  transition: all 0.3s ease;
}

.rule-description:hover {
  border-left-color: #409eff;
  background-color: rgba(249, 250, 252, 0.9);
}

.description-text {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  word-break: break-word;
  cursor: help;
  width: 100%;
}

.description-text p {
  margin: 0;
}

/* 增强列表项样式 */
.description-text .list-number {
  font-weight: 600;
  color: #409eff;
  display: inline-block;
  min-width: 20px;
}

.rule-description-placeholder {
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(249, 250, 252, 0.7);
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #e6e8eb;
  transition: all 0.3s ease;
}

.placeholder-text {
  margin: 0;
  font-size: 14px;
  color: #C0C4CC;
  font-style: italic;
}

/* 操作按钮区域 - 固定在底部 */
.rule-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(240, 242, 245, 0.8);
}

/* 按钮样式 - 现代化设计 */
.btn {
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  text-align: center;
  line-height: 1.4;
  min-width: 100px;
  position: relative;
  overflow: hidden;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: translateY(0);
}

/* 添加波纹效果 */
.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: width 0.6s ease-out, height 0.6s ease-out, opacity 0.6s ease-out;
}

.btn:active::before {
  width: 300%;
  height: 300%;
  opacity: 1;
  transition: width 0s, height 0s;
}

.btn-secondary {
  background: white;
  color: #606266;
  border-color: #dcdfe6;
}

.btn-secondary:hover {
  background: #f5f7fa;
  border-color: #409eff;
  color: #409eff;
}

.btn-primary {
  background: linear-gradient(135deg, #409eff, #53a8ff);
  color: white;
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #53a8ff, #66b1ff);
  border-color: #53a8ff;
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
}

.btn-icon {
  font-size: 14px;
}

/* 表格样式优化 */
.rules-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.table-action-btn {
  transition: all 0.3s ease;
}

.table-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1199px) {
  .rule-card {
    min-height:180;
    padding: 20px;
  }

  .rule-name {
    font-size: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }
}

@media (max-width: 767px) {
  .rule-dashboard {
    padding: 16px;
  }

  .dashboard-header {
    padding: 20px;
    margin-bottom: 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-title {
    font-size: 22px;
  }

  .page-description {
    font-size: 14px;
    max-width: 100%;
  }

  .header-actions {
    width: 100%;
  }

  .refresh-button {
    width: 100%;
  }

  .rule-card {
    min-height: 140px;
    padding: 16px;
  }

  .rule-name {
    font-size: 16px;
    max-width: 65%;
  }

  .rule-actions {
    flex-direction: column;
    gap: 8px;
  }

  .btn {
    width: 100%;
    padding: 10px 16px;
  }
}

@media (max-width: 575px) {
  .rule-dashboard {
    padding: 12px;
  }

  .dashboard-header {
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 12px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  /* 响应式网格样式已通过内联样式实现 */

  .rule-card {
    min-height: 140px;
    padding: 16px;
    border-radius: 10px;
    margin-bottom: 4px;
  }

  .rule-name {
    font-size: 15px;
    line-height: 1.3;
    max-width: 60%;
  }

  .description-text {
    font-size: 13px;
  }

  .placeholder-text {
    font-size: 13px;
  }

  .rule-description,
  .rule-description-placeholder {
    padding: 10px;
    min-height: 50px;
  }
}

/* 响应式网格布局 */
.responsive-grid {
  /* 大屏幕：3列（修改为最多3列） */
  grid-template-columns: repeat(3, 1fr) !important;
}

@media (max-width: 1199px) and (min-width: 768px) {
  .responsive-grid {
    /* 中屏幕：3列 */
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 18px !important;
  }
}

@media (max-width: 767px) and (min-width: 576px) {
  .responsive-grid {
    /* 小屏幕：2列 */
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 16px !important;
  }
}

@media (max-width: 575px) {
  .responsive-grid {
    /* 超小屏幕：1列 */
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }
}
</style>
