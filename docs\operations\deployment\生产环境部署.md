# 规则验证系统部署指南

## 🎯 概述

本指南提供规则验证系统主从架构的完整部署配置，包括Git版本管理、Docker容器化部署和安全配置最佳实践。

## 📁 项目结构说明

### 核心组件
- **主节点（Master）**: FastAPI + SQLAlchemy，负责规则管理和Web界面
- **从节点（Slave）**: 轻量级规则验证服务，支持离线模式
- **前端（Frontend）**: Vue 3 + Element Plus，独立部署

### 部署场景
- **公司服务器**: 主节点 + 前端 + 数据库
- **医院内网**: 从节点（支持离线模式）

## 🔧 Git版本管理配置

### 1. 使用推荐的.gitignore
```bash
# 替换现有的.gitignore
cp .gitignore.recommended .gitignore
```

### 2. 敏感信息管理
```bash
# 创建环境配置文件
cp .env.template .env
# 编辑.env文件，填入实际配置值
```

### 3. 提交前检查清单
- [ ] 确认.env文件未被跟踪
- [ ] 确认日志文件未被包含
- [ ] 确认缓存文件未被包含
- [ ] 确认生成的模板文件未被包含

## 🐳 Docker部署配置

### 1. 主节点部署

#### 构建主节点镜像
```bash
# 使用主节点专用配置
cp .dockerignore.master .dockerignore
docker build -f Dockerfile.master -t rule-master:latest .
```

#### 运行主节点
```bash
docker run -d \
  --name rule-master \
  -p 18001:18001 \
  -e MODE=master \
  -e DATABASE_URL="mysql+pymysql://user:pass@host:3306/db" \
  -e MASTER_API_SECRET_KEY="your_secret_key" \
  -v ./logs:/app/logs \
  -v ./rules_cache:/app/rules_cache \
  rule-master:latest
```

### 2. 从节点部署

#### 构建从节点镜像
```bash
# 使用从节点专用配置
cp .dockerignore.slave .dockerignore
docker build -f Dockerfile.slave -t rule-slave:latest .
```

#### 运行从节点
```bash
docker run -d \
  --name rule-slave \
  -p 18002:18001 \
  -e MODE=slave \
  -e MASTER_API_ENDPOINT="http://master-host:18001" \
  -e SLAVE_API_KEY="your_secret_key" \
  -e ENABLE_RULE_SYNC=true \
  -v ./logs:/app/logs \
  -v ./rules_cache:/app/rules_cache \
  rule-slave:latest
```

### 3. 使用Docker Compose（推荐）

#### 完整部署
```bash
# 配置环境变量
cp .env.template .env
# 编辑.env文件

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f rule-master
```

#### 仅从节点部署（医院内网）
```bash
# 创建从节点专用compose文件
cat > docker-compose.slave.yml << EOF
version: '3.8'
services:
  rule-slave:
    build:
      context: .
      dockerfile: Dockerfile.slave
    container_name: rule-slave
    restart: unless-stopped
    environment:
      - MODE=slave
      - MASTER_API_ENDPOINT=\${MASTER_API_ENDPOINT}
      - SLAVE_API_KEY=\${SLAVE_API_KEY}
      - ENABLE_RULE_SYNC=\${ENABLE_RULE_SYNC:-true}
    ports:
      - "18001:18001"
    volumes:
      - ./logs:/app/logs
      - ./rules_cache:/app/rules_cache
EOF

# 启动从节点
docker-compose -f docker-compose.slave.yml up -d
```

## 🔒 安全配置

### 1. 环境变量安全
- 使用强密码和复杂密钥
- 定期轮换API密钥
- 生产环境使用不同的密钥

### 2. 网络安全
- 配置防火墙规则
- 使用HTTPS（生产环境）
- 限制API访问来源

### 3. 容器安全
- 使用非root用户运行
- 定期更新基础镜像
- 扫描镜像漏洞

## 📊 监控和维护

### 1. 健康检查
```bash
# 检查服务状态
curl http://localhost:18001/health

# 检查从节点同步状态
curl http://localhost:18002/api/sync/status
```

### 2. 日志管理
```bash
# 查看应用日志
docker-compose logs -f rule-master

# 日志轮转配置已内置
# 日志文件位置: ./logs/
```

### 3. 数据备份
```bash
# 数据库备份
docker exec rule-mysql mysqldump -u root -p rule_management > backup.sql

# 规则缓存备份
cp rules_cache.json.gz backup/
```

## 🚀 性能优化

### 1. 镜像优化
- 使用多阶段构建
- 最小化镜像层数
- 清理不必要文件

### 2. 运行时优化
- 配置合适的资源限制
- 使用健康检查
- 配置重启策略

### 3. 网络优化
- 使用内部网络通信
- 配置连接池
- 启用HTTP/2（如适用）

## 🔧 故障排除

### 1. 常见问题
- 端口冲突: 修改docker-compose.yml中的端口映射
- 权限问题: 检查文件权限和用户配置
- 网络问题: 检查防火墙和网络配置

### 2. 调试命令
```bash
# 进入容器调试
docker exec -it rule-master bash

# 查看容器资源使用
docker stats

# 查看网络连接
docker network ls
docker network inspect rule_rule-network
```

## 📋 部署检查清单

### 部署前
- [ ] 配置环境变量文件
- [ ] 检查网络端口可用性
- [ ] 准备数据库连接信息
- [ ] 配置防火墙规则

### 部署后
- [ ] 验证服务健康状态
- [ ] 测试API接口功能
- [ ] 检查日志输出正常
- [ ] 验证主从节点同步
- [ ] 测试前端界面访问

### 生产环境额外检查
- [ ] 配置HTTPS证书
- [ ] 设置监控告警
- [ ] 配置数据备份策略
- [ ] 制定灾难恢复计划
