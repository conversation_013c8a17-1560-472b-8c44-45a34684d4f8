"""
患者数据分析器单元测试
测试从复杂患者数据中提取代码的功能
"""

import unittest
from unittest.mock import Mock

from core.patient_data_analyzer import PatientDataAnalyzer
from models.patient import PatientData, DiagnoseInfo, DiagnosisList, OperationList


class TestPatientDataAnalyzer(unittest.TestCase):
    """测试患者数据分析器"""

    def setUp(self):
        self.analyzer = PatientDataAnalyzer()

    def create_mock_patient_data(self, fees=None, diagnosis_info=None, surgery=None):
        """创建符合实际PatientData模型的模拟患者数据"""
        patient = Mock(spec=PatientData)
        patient.fees = fees or []
        patient.Diagnosis = diagnosis_info  # 单个DiagnoseInfo对象，不是列表

        if surgery:
            patient.surgery = surgery

        return patient

    def create_mock_fee_item(self, ybdm=None, yb_code=None, name=None, type_=None):
        """创建模拟费用项目"""
        fee = Mock()
        if ybdm:
            fee.ybdm = ybdm
        if yb_code:
            fee.yb_code = yb_code
        if name:
            fee.name = name
        if type_:
            fee.type = type_
        return fee

    def create_mock_diagnosis_info(
        self, out_patient_code=None, admission_code=None, ch_icd_code=None, diagnosis_list=None, operation_list=None
    ):
        """创建符合DiagnoseInfo模型的模拟诊断信息"""
        diag_info = Mock(spec=DiagnoseInfo)
        diag_info.outPatientDiagnosisICDCode = out_patient_code
        diag_info.diagnoseICDCodeOnAdmission = admission_code
        diag_info.outPatientDiagnosisChICDCode = ch_icd_code
        diag_info.diagnosis = diagnosis_list or []
        diag_info.operation = operation_list or []
        return diag_info

    def create_mock_diagnosis_list_item(self, diagnosis_icd_code=None, diagnosis_name=None):
        """创建模拟诊断列表项目"""
        diag_item = Mock(spec=DiagnosisList)
        diag_item.diagnosisICDCode = diagnosis_icd_code
        diag_item.diagnosisName = diagnosis_name
        return diag_item

    def create_mock_operation_item(self, operation_icd_code=None, operation_name=None):
        """创建模拟手术项目"""
        op_item = Mock(spec=OperationList)
        op_item.operationICDCode = operation_icd_code
        op_item.operationName = operation_name
        return op_item

    def test_extract_codes_basic(self):
        """测试基本代码提取功能"""
        # 创建测试数据 - 符合实际数据模型
        fees = [self.create_mock_fee_item(ybdm="Y001"), self.create_mock_fee_item(yb_code="Y002")]

        # 创建诊断列表项目
        diagnosis_list = [
            self.create_mock_diagnosis_list_item(diagnosis_icd_code="D001"),
            self.create_mock_diagnosis_list_item(diagnosis_icd_code="D002"),
        ]

        # 创建诊断信息对象
        diagnosis_info = self.create_mock_diagnosis_info(
            out_patient_code="D003", admission_code="D004", diagnosis_list=diagnosis_list
        )

        patient_data = self.create_mock_patient_data(fees=fees, diagnosis_info=diagnosis_info)

        # 执行提取
        result = self.analyzer.extract_codes(patient_data)

        # 验证结果
        self.assertIn("Y001", result.yb_codes)
        self.assertIn("Y002", result.yb_codes)
        self.assertIn("D003", result.diag_codes)  # 门急诊代码
        self.assertIn("D004", result.diag_codes)  # 入院诊断代码
        self.assertIn("D001", result.diag_codes)  # 诊断列表代码
        self.assertIn("D002", result.diag_codes)  # 诊断列表代码
        self.assertTrue(result.extraction_time > 0)

    def test_extract_yb_codes_from_dict(self):
        """测试从字典结构中提取医保代码"""
        fees = [{"ybdm": "Y001", "name": "药品A"}, {"yb_code": "Y002", "amount": 100}, {"insurance_code": "Y003"}]

        patient_data = self.create_mock_patient_data(fees=fees)

        result = self.analyzer.extract_codes(patient_data)

        self.assertIn("Y001", result.yb_codes)
        self.assertIn("Y002", result.yb_codes)
        self.assertIn("Y003", result.yb_codes)

    def test_extract_yb_codes_empty_values(self):
        """测试处理空值和无效值"""
        fees = [
            self.create_mock_fee_item(ybdm="Y001"),
            self.create_mock_fee_item(ybdm=""),  # 空字符串
            self.create_mock_fee_item(ybdm="   "),  # 空白字符
            self.create_mock_fee_item(ybdm=None),  # None值
        ]

        patient_data = self.create_mock_patient_data(fees=fees)

        result = self.analyzer.extract_codes(patient_data)

        # 只应该提取到有效的代码
        self.assertEqual(len(result.yb_codes), 1)
        self.assertIn("Y001", result.yb_codes)

    def test_extract_diagnosis_codes_various_formats(self):
        """测试提取各种格式的诊断代码"""
        # 创建各种格式的诊断列表项目
        diagnosis_list = [
            self.create_mock_diagnosis_list_item(diagnosis_icd_code="I10"),
            self.create_mock_diagnosis_list_item(diagnosis_icd_code="E11.9"),
            self.create_mock_diagnosis_list_item(diagnosis_icd_code="K59.1,M79.3"),  # 逗号分隔
        ]

        # 创建诊断信息对象
        diagnosis_info = self.create_mock_diagnosis_info(
            out_patient_code="H25.9", ch_icd_code="Z87.891", diagnosis_list=diagnosis_list
        )

        patient_data = self.create_mock_patient_data(diagnosis_info=diagnosis_info)

        result = self.analyzer.extract_codes(patient_data)

        # 验证各种来源的诊断代码
        self.assertIn("H25.9", result.diag_codes)  # 门急诊西医代码
        self.assertIn("Z87.891", result.diag_codes)  # 门急诊中医代码
        self.assertIn("I10", result.diag_codes)  # 诊断列表代码
        self.assertIn("E11.9", result.diag_codes)  # 诊断列表代码
        self.assertIn("K59.1", result.diag_codes)  # 逗号分隔代码
        self.assertIn("M79.3", result.diag_codes)  # 逗号分隔代码

    def test_extract_diagnosis_from_string(self):
        """测试从字符串中提取诊断代码（逗号分隔）"""
        # 创建包含逗号分隔代码的诊断信息
        diagnosis_info = self.create_mock_diagnosis_info(
            out_patient_code="I10,E11.9",  # 逗号分隔的代码
            admission_code="K59.1",
        )

        patient_data = self.create_mock_patient_data(diagnosis_info=diagnosis_info)

        result = self.analyzer.extract_codes(patient_data)

        # 验证逗号分隔的代码被正确分解
        self.assertIn("I10", result.diag_codes)
        self.assertIn("E11.9", result.diag_codes)
        self.assertIn("K59.1", result.diag_codes)

    def test_extract_surgery_codes_from_fees(self):
        """测试从费用项目中提取手术代码"""
        fees = [
            self.create_mock_fee_item(ybdm="S001", name="阑尾切除术", type_="手术"),
            self.create_mock_fee_item(ybdm="Y001", name="药品A", type_="药品"),
            {"ybdm": "S002", "name": "胆囊切除术", "type": "surgical"},
        ]

        patient_data = self.create_mock_patient_data(fees=fees)

        result = self.analyzer.extract_codes(patient_data)

        # 应该从手术相关费用中提取代码
        self.assertIn("S001", result.surgery_codes)
        self.assertIn("S002", result.surgery_codes)
        # Y001不是手术相关，不应该在手术代码中
        self.assertNotIn("Y001", result.surgery_codes)

    def test_extract_surgery_codes_from_surgery_field(self):
        """测试从手术字段中提取手术代码"""
        # 创建手术操作列表
        operation_list = [
            self.create_mock_operation_item(operation_icd_code="S001", operation_name="阑尾切除术"),
            self.create_mock_operation_item(operation_icd_code="S002,S003", operation_name="胆囊切除术"),  # 逗号分隔
        ]

        # 创建诊断信息对象包含手术信息
        diagnosis_info = self.create_mock_diagnosis_info(operation_list=operation_list)

        patient_data = self.create_mock_patient_data(diagnosis_info=diagnosis_info)

        result = self.analyzer.extract_codes(patient_data)

        # 验证手术代码被正确提取
        self.assertIn("S001", result.surgery_codes)
        self.assertIn("S002", result.surgery_codes)  # 逗号分隔的第一个
        self.assertIn("S003", result.surgery_codes)  # 逗号分隔的第二个

    def test_extract_surgery_codes_comprehensive(self):
        """测试手术代码的综合提取功能"""
        # 创建手术操作列表
        operation_list = [
            self.create_mock_operation_item(operation_icd_code="04.43", operation_name="胃切除术"),
            self.create_mock_operation_item(operation_icd_code="84.12,84.13", operation_name="复合手术"),
        ]

        # 创建包含手术相关费用的列表
        fees = [
            self.create_mock_fee_item(ybdm="S100", name="腹腔镜手术费", type_="手术"),
            self.create_mock_fee_item(ybdm="Y001", name="普通药品", type_="药品"),  # 非手术相关
        ]

        # 创建诊断信息
        diagnosis_info = self.create_mock_diagnosis_info(operation_list=operation_list)

        patient_data = self.create_mock_patient_data(fees=fees, diagnosis_info=diagnosis_info)

        result = self.analyzer.extract_codes(patient_data)

        # 验证手术代码来源
        self.assertIn("04.43", result.surgery_codes)  # 来自operation列表
        self.assertIn("84.12", result.surgery_codes)  # 来自operation列表（逗号分隔）
        self.assertIn("84.13", result.surgery_codes)  # 来自operation列表（逗号分隔）
        self.assertIn("S100", result.surgery_codes)  # 来自手术费用项目

        # 验证非手术项目不被包含在手术代码中
        self.assertNotIn("Y001", result.surgery_codes)

        # 但Y001应该在医保代码中
        self.assertIn("Y001", result.yb_codes)

    def test_extract_codes_from_nested_dict(self):
        """测试从嵌套字典中提取代码"""
        fees = [{"item": {"ybdm": "Y001", "details": {"yb_code": "Y002"}}}]

        patient_data = self.create_mock_patient_data(fees=fees)

        result = self.analyzer.extract_codes(patient_data)

        self.assertIn("Y001", result.yb_codes)
        self.assertIn("Y002", result.yb_codes)

    def test_extract_codes_exception_handling(self):
        """测试异常处理"""
        # 创建会引发异常的患者数据
        problematic_patient = Mock()
        problematic_patient.fees = None  # 可能引发异常
        problematic_patient.Diagnosis = None

        # 应该不抛出异常，返回空结果
        result = self.analyzer.extract_codes(problematic_patient)

        self.assertEqual(len(result.yb_codes), 0)
        self.assertEqual(len(result.diag_codes), 0)
        self.assertEqual(len(result.surgery_codes), 0)
        self.assertTrue(result.extraction_time >= 0)

    def test_performance_tracking(self):
        """测试性能统计功能"""
        # 重置统计
        self.analyzer.reset_stats()

        # 执行几次提取
        for _ in range(3):
            patient_data = self.create_mock_patient_data(fees=[self.create_mock_fee_item(ybdm="Y001")])
            self.analyzer.extract_codes(patient_data)

        # 检查统计信息
        stats = self.analyzer.get_performance_stats()

        self.assertEqual(stats["extraction_count"], 3)
        self.assertTrue(stats["total_extraction_time_ms"] > 0)
        self.assertTrue(stats["avg_extraction_time_ms"] > 0)
        self.assertEqual(stats["error_count"], 0)
        self.assertEqual(stats["error_rate"], 0.0)

    def test_extract_codes_from_dict_recursive(self):
        """测试递归提取字典中的代码"""
        codes = self.analyzer._extract_codes_from_dict(
            {
                "level1": {"ybdm": "Y001", "level2": {"yb_code": "Y002", "level3": {"insurance_code": "Y003"}}},
                "direct_code": "Y004",
            },
            ["ybdm", "yb_code", "insurance_code", "direct_code"],
        )

        self.assertIn("Y001", codes)
        self.assertIn("Y002", codes)
        self.assertIn("Y003", codes)
        self.assertIn("Y004", codes)

    def test_extract_diagnosis_from_field_string(self):
        """测试从字符串字段提取诊断代码"""
        # 逗号分隔的字符串
        codes = self.analyzer._extract_diagnosis_from_field("I10,E11.9,K59.1")
        self.assertEqual(codes, {"I10", "E11.9", "K59.1"})

        # 单个字符串
        codes = self.analyzer._extract_diagnosis_from_field("I10")
        self.assertEqual(codes, {"I10"})

        # 空字符串
        codes = self.analyzer._extract_diagnosis_from_field("")
        self.assertEqual(len(codes), 0)

    def test_extract_diagnosis_from_field_list(self):
        """测试从列表字段提取诊断代码"""
        # 字符串列表
        codes = self.analyzer._extract_diagnosis_from_field(["I10", "E11.9"])
        self.assertEqual(codes, {"I10", "E11.9"})

        # 字典列表
        codes = self.analyzer._extract_diagnosis_from_field([{"icd10": "I10"}, {"code": "E11.9"}])
        self.assertEqual(codes, {"I10", "E11.9"})

    def test_extract_surgery_from_fee_item_surgery_related(self):
        """测试从手术相关费用项目提取代码"""
        # 手术相关费用
        surgery_fee = self.create_mock_fee_item(ybdm="S001", name="阑尾切除术", type_="手术")

        codes = self.analyzer._extract_surgery_from_fee_item(surgery_fee)
        self.assertIn("S001", codes)

        # 非手术相关费用
        drug_fee = self.create_mock_fee_item(ybdm="Y001", name="阿司匹林", type_="药品")

        codes = self.analyzer._extract_surgery_from_fee_item(drug_fee)
        self.assertEqual(len(codes), 0)

    def test_extract_surgery_from_fee_item_dict(self):
        """测试从字典格式费用项目提取手术代码"""
        # 手术相关的字典费用
        surgery_fee_dict = {"ybdm": "S001", "name": "腹腔镜手术", "type": "surgery"}

        codes = self.analyzer._extract_surgery_from_fee_item(surgery_fee_dict)
        self.assertIn("S001", codes)

    def test_reset_stats(self):
        """测试重置统计信息"""
        # 先执行一些操作
        patient_data = self.create_mock_patient_data(fees=[self.create_mock_fee_item(ybdm="Y001")])
        self.analyzer.extract_codes(patient_data)

        # 确认有统计数据
        stats_before = self.analyzer.get_performance_stats()
        self.assertTrue(stats_before["extraction_count"] > 0)

        # 重置统计
        self.analyzer.reset_stats()

        # 确认统计已重置
        stats_after = self.analyzer.get_performance_stats()
        self.assertEqual(stats_after["extraction_count"], 0)
        self.assertEqual(stats_after["total_extraction_time_ms"], 0)
        self.assertEqual(stats_after["error_count"], 0)

    def test_extract_codes_performance(self):
        """测试代码提取性能"""
        # 创建较大的测试数据集
        fees = []
        for i in range(100):
            fees.append(self.create_mock_fee_item(ybdm=f"Y{i:03d}"))

        # 创建大量诊断列表项目
        diagnosis_list = []
        for i in range(50):
            diagnosis_list.append(self.create_mock_diagnosis_list_item(diagnosis_icd_code=f"D{i:03d}"))

        # 创建诊断信息对象
        diagnosis_info = self.create_mock_diagnosis_info(
            out_patient_code="H00.001", admission_code="H00.002", diagnosis_list=diagnosis_list
        )

        patient_data = self.create_mock_patient_data(fees=fees, diagnosis_info=diagnosis_info)

        # 执行提取并测量时间
        result = self.analyzer.extract_codes(patient_data)

        # 验证提取时间是否合理（大数据集允许稍长时间）
        self.assertTrue(result.extraction_time < 50.0, f"提取时间过长: {result.extraction_time}ms")

        # 验证提取结果数量
        self.assertEqual(len(result.yb_codes), 100)
        # 诊断代码包括：2个直接代码 + 50个列表代码 = 52个
        self.assertEqual(len(result.diag_codes), 52)


if __name__ == "__main__":
    unittest.main()
