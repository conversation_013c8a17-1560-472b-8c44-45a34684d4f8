"""
错误日志增强器
基于现有日志系统，提供错误处理专用的增强功能
"""

import platform
import time
from collections import defaultdict, deque
from dataclasses import asdict, dataclass
from typing import Any

import psutil

from core.logging.logging_system import log as logger
from core.middleware.request_tracking import request_tracker


@dataclass
class ErrorContext:
    """错误上下文信息"""

    request_id: str
    endpoint: str
    method: str
    error_type: str
    error_message: str
    error_category: str
    severity: str
    timestamp: float
    user_context: dict[str, Any]
    system_info: dict[str, Any]
    request_chain: dict[str, Any]


class ErrorLogEnhancer:
    """
    错误日志增强器
    基于现有日志系统，提供错误处理专用功能
    """

    def __init__(self):
        """初始化错误日志增强器"""
        # 复用现有的logger实例
        self.logger = logger

        # 错误统计和分析
        self.error_stats = ErrorStatistics()

        # 系统信息缓存
        self._system_info_cache = None
        self._system_info_cache_time = 0
        self._system_info_cache_ttl = 300  # 5分钟缓存

    def enhance_error_logging(
        self,
        error: Exception,
        request_id: str,
        endpoint: str,
        method: str = "UNKNOWN",
        user_context: dict[str, Any] | None = None,
    ):
        """
        增强错误日志记录

        Args:
            error: 异常对象
            request_id: 请求追踪ID
            endpoint: API端点
            method: HTTP方法
            user_context: 用户上下文信息
        """
        try:
            # 构建错误上下文
            error_context = self._build_error_context(
                error=error,
                request_id=request_id,
                endpoint=endpoint,
                method=method,
                user_context=user_context or {}
            )

            # 使用现有logger记录增强的错误信息
            self._log_enhanced_error(error_context)

            # 错误统计和分析
            self.error_stats.record_error(error_context)

        except Exception as e:
            # 如果增强日志记录失败，使用基础日志记录
            # 安全地处理异常字符串转换，避免参数字典相关的字符串问题
            try:
                error_msg = str(e)
            except Exception:
                error_msg = f"{type(e).__name__}: <字符串转换失败>"

            try:
                original_error_msg = str(error)
            except Exception:
                original_error_msg = f"{type(error).__name__}: <字符串转换失败>"

            self.logger.error(
                f"Error in enhanced logging: {error_msg}",
                extra={
                    "request_id": request_id,
                    "original_error": original_error_msg,
                    "endpoint": endpoint,
                    "enhancement_error_type": type(e).__name__,
                    "original_error_type": type(error).__name__,
                },
            )

    def _build_error_context(
        self,
        error: Exception,
        request_id: str,
        endpoint: str,
        method: str,
        user_context: dict[str, Any]
    ) -> ErrorContext:
        """
        构建错误上下文信息

        Args:
            error: 异常对象
            request_id: 请求追踪ID
            endpoint: API端点
            method: HTTP方法
            user_context: 用户上下文信息

        Returns:
            ErrorContext: 错误上下文对象
        """
        error_type = type(error).__name__

        # 安全地获取错误消息，避免字符串转换问题
        try:
            error_message = str(error)
        except Exception:
            error_message = f"{error_type}: <错误消息获取失败>"

        # 确定错误类别
        error_category = self._determine_error_category(error)

        # 确定错误严重程度
        severity = self._determine_error_severity(error, error_category)

        # 获取系统信息
        system_info = self._get_system_info()

        # 安全地获取请求链路信息
        try:
            request_chain = request_tracker.get_chain(request_id) or {}
        except Exception as e:
            # 如果获取请求链路信息失败，记录警告并使用空字典
            # 安全地处理异常字符串转换
            try:
                error_msg = str(e)
            except Exception:
                error_msg = f"{type(e).__name__}: <字符串转换失败>"
            self.logger.warning(f"Failed to get request chain for {request_id}: {error_msg}")
            request_chain = {}

        return ErrorContext(
            request_id=request_id,
            endpoint=endpoint,
            method=method,
            error_type=error_type,
            error_message=error_message,
            error_category=error_category,
            severity=severity,
            timestamp=time.perf_counter(),
            user_context=user_context,
            system_info=system_info,
            request_chain=request_chain,
        )

    def _log_enhanced_error(self, error_context: ErrorContext):
        """
        记录增强的错误日志

        Args:
            error_context: 错误上下文
        """
        # 构建日志消息
        log_message = (
            f"Enhanced Error: {error_context.endpoint} - "
            f"{error_context.error_type}: {error_context.error_message}"
        )

        # 构建额外信息
        extra_info = asdict(error_context)

        # 根据严重程度选择日志级别
        if error_context.severity == "critical":
            self.logger.critical(log_message, extra=extra_info)
        elif error_context.severity == "error":
            self.logger.error(log_message, extra=extra_info)
        elif error_context.severity == "warning":
            self.logger.warning(log_message, extra=extra_info)
        else:
            self.logger.info(log_message, extra=extra_info)

    def _determine_error_category(self, error: Exception) -> str:
        """
        确定错误类别

        Args:
            error: 异常对象

        Returns:
            str: 错误类别
        """
        # 安全地获取错误字符串，避免字符串转换问题
        try:
            error_str = str(error).lower()
        except Exception:
            error_str = ""

        error_type = type(error).__name__.lower()

        if "validation" in error_type or "validation" in error_str:
            return "validation"
        elif "database" in error_str or "sql" in error_str or "programmingerror" in error_type:
            return "database"
        elif "network" in error_str or "connection" in error_str:
            return "network"
        elif "cache" in error_str or "redis" in error_str:
            return "cache"
        elif "file" in error_str or "io" in error_type:
            return "filesystem"
        elif "memory" in error_type:
            return "memory"
        elif "permission" in error_str or "auth" in error_str:
            return "auth"
        elif "timeout" in error_str:
            return "timeout"
        else:
            return "unknown"

    def _determine_error_severity(self, error: Exception, category: str) -> str:
        """
        确定错误严重程度

        Args:
            error: 异常对象
            category: 错误类别

        Returns:
            str: 严重程度
        """

        # 系统级错误
        if isinstance(error, MemoryError | SystemError):
            return "critical"

        # 数据库和缓存错误
        if category in ["database", "cache"]:
            return "error"

        # 网络和文件系统错误
        if category in ["network", "filesystem"]:
            return "error"

        # 验证和认证错误
        if category in ["validation", "auth"]:
            return "warning"

        # 超时错误
        if category == "timeout":
            return "warning"

        # 默认为错误级别
        return "error"

    def _get_system_info(self) -> dict[str, Any]:
        """
        获取系统信息（带缓存）

        Returns:
            Dict: 系统信息
        """
        current_time = time.perf_counter()

        # 检查缓存是否有效
        if self._system_info_cache and current_time - self._system_info_cache_time < self._system_info_cache_ttl:
            return self._system_info_cache

        try:
            # 收集系统信息
            system_info = {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                "memory_available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
                "memory_percent": psutil.virtual_memory().percent,
                "cpu_percent": psutil.cpu_percent(interval=0.1),
                "disk_usage_percent": psutil.disk_usage("/").percent,
                "process_count": len(psutil.pids()),
                "timestamp": current_time,
            }

            # 更新缓存
            self._system_info_cache = system_info
            self._system_info_cache_time = current_time

            return system_info

        except Exception as e:
            # 如果获取系统信息失败，返回基本信息
            # 安全地处理异常字符串转换
            try:
                error_msg = str(e)
            except Exception:
                error_msg = f"{type(e).__name__}: <字符串转换失败>"

            self.logger.warning(f"Failed to get system info: {error_msg}")
            return {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "error": f"Failed to get detailed system info: {error_msg}",
                "timestamp": current_time,
            }

    def get_error_summary(self) -> dict[str, Any]:
        """
        获取错误摘要信息

        Returns:
            Dict: 错误摘要
        """
        return self.error_stats.get_error_summary()


class ErrorStatistics:
    """
    错误统计分析器
    提供错误趋势、热点分析等功能
    """

    def __init__(self, max_history: int = 1000):
        """
        初始化错误统计器

        Args:
            max_history: 最大历史记录数量
        """
        self.max_history = max_history

        # 错误计数
        self.error_counts = defaultdict(int)
        self.endpoint_errors = defaultdict(int)
        self.category_errors = defaultdict(int)
        self.severity_errors = defaultdict(int)

        # 错误时间线
        self.error_timeline = deque(maxlen=max_history)

        # 错误详情历史
        self.error_history = deque(maxlen=max_history)

    def record_error(self, error_context: ErrorContext):
        """
        记录错误统计信息

        Args:
            error_context: 错误上下文
        """
        # 更新计数
        error_key = f"{error_context.endpoint}:{error_context.error_type}"
        self.error_counts[error_key] += 1
        self.endpoint_errors[error_context.endpoint] += 1
        self.category_errors[error_context.error_category] += 1
        self.severity_errors[error_context.severity] += 1

        # 添加到时间线
        timeline_entry = {
            "timestamp": error_context.timestamp,
            "endpoint": error_context.endpoint,
            "error_type": error_context.error_type,
            "category": error_context.error_category,
            "severity": error_context.severity,
        }
        self.error_timeline.append(timeline_entry)

        # 添加到历史记录
        self.error_history.append(error_context)

    def get_error_summary(self) -> dict[str, Any]:
        """
        获取错误摘要报告

        Returns:
            Dict: 错误摘要
        """
        total_errors = sum(self.error_counts.values())

        # 获取热点端点（错误最多的前5个）
        hot_endpoints = sorted(
            self.endpoint_errors.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]

        # 获取最近的错误（最近10个）
        recent_errors = list(self.error_timeline)[-10:]

        # 计算错误率趋势（最近1小时）
        current_time = time.perf_counter()
        recent_hour_errors = [
            entry for entry in self.error_timeline
            if current_time - entry["timestamp"] < 3600
        ]

        return {
            "total_errors": total_errors,
            "error_types": dict(self.error_counts),
            "endpoint_errors": dict(self.endpoint_errors),
            "category_distribution": dict(self.category_errors),
            "severity_distribution": dict(self.severity_errors),
            "hot_endpoints": hot_endpoints,
            "recent_errors": recent_errors,
            "recent_hour_count": len(recent_hour_errors),
            "error_rate_per_hour": len(recent_hour_errors),
            "summary_timestamp": current_time,
        }


# 全局错误日志增强器实例
error_log_enhancer = ErrorLogEnhancer()
