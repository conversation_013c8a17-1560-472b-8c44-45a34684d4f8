/**
 * UI组件相关的TypeScript类型定义
 */

// 基础组件Props类型
export interface BaseComponentProps {
  id?: string
  class?: string | string[] | Record<string, boolean>
  style?: string | Record<string, any>
  disabled?: boolean
  loading?: boolean
}

// 按钮组件类型
export interface ButtonProps extends BaseComponentProps {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  size?: 'large' | 'default' | 'small'
  icon?: string
  round?: boolean
  circle?: boolean
  plain?: boolean
  text?: boolean
  bg?: boolean
  link?: boolean
  native_type?: 'button' | 'submit' | 'reset'
  auto_focus?: boolean
  color?: string
  dark?: boolean
  tag?: string
}

// 表格组件类型
export interface TableColumn {
  prop?: string
  label: string
  width?: string | number
  min_width?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean | 'custom'
  sort_method?: (a: any, b: any) => number
  sort_by?: string | string[] | ((row: any, index: number) => string)
  resizable?: boolean
  formatter?: (row: any, column: TableColumn, cellValue: any, index: number) => any
  show_overflow_tooltip?: boolean
  align?: 'left' | 'center' | 'right'
  header_align?: 'left' | 'center' | 'right'
  class_name?: string
  label_class_name?: string
  selectable?: (row: any, index: number) => boolean
  reserve_selection?: boolean
  filters?: TableFilter[]
  filter_method?: (value: any, row: any, column: TableColumn) => boolean
  filter_multiple?: boolean
  filter_placement?: string
}

export interface TableFilter {
  text: string
  value: any
}

export interface TableProps extends BaseComponentProps {
  data: any[]
  height?: string | number
  max_height?: string | number
  stripe?: boolean
  border?: boolean
  size?: 'large' | 'default' | 'small'
  fit?: boolean
  show_header?: boolean
  highlight_current_row?: boolean
  current_row_key?: string | number
  row_class_name?: string | ((row: any, index: number) => string)
  row_style?: Record<string, any> | ((row: any, index: number) => Record<string, any>)
  cell_class_name?: string | ((row: any, column: TableColumn, rowIndex: number, columnIndex: number) => string)
  cell_style?: Record<string, any> | ((row: any, column: TableColumn, rowIndex: number, columnIndex: number) => Record<string, any>)
  header_row_class_name?: string | ((row: any, index: number) => string)
  header_row_style?: Record<string, any> | ((row: any, index: number) => Record<string, any>)
  header_cell_class_name?: string | ((row: any, column: TableColumn, rowIndex: number, columnIndex: number) => string)
  header_cell_style?: Record<string, any> | ((row: any, column: TableColumn, rowIndex: number, columnIndex: number) => Record<string, any>)
  row_key?: string | ((row: any) => string)
  empty_text?: string
  default_expand_all?: boolean
  expand_row_keys?: any[]
  default_sort?: { prop: string; order: 'ascending' | 'descending' }
  tooltip_effect?: 'dark' | 'light'
  show_summary?: boolean
  sum_text?: string
  summary_method?: (param: { columns: TableColumn[]; data: any[] }) => any[]
  span_method?: (param: { row: any; column: TableColumn; rowIndex: number; columnIndex: number }) => number[] | { rowspan: number; colspan: number }
  select_on_indeterminate?: boolean
  indent?: number
  lazy?: boolean
  load?: (row: any, treeNode: any, resolve: (data: any[]) => void) => void
  tree_props?: { hasChildren?: string; children?: string }
  table_layout?: 'fixed' | 'auto'
  scrollbar_always_on?: boolean
  flexible?: boolean
}

// 表单组件类型
export interface FormItemProps extends BaseComponentProps {
  label?: string
  label_width?: string | number
  prop?: string
  required?: boolean
  rules?: FormRule | FormRule[]
  error?: string
  validate_status?: 'success' | 'warning' | 'error' | 'validating'
  for?: string
  inline_message?: boolean | string
  show_message?: boolean
  size?: 'large' | 'default' | 'small'
}

export interface FormRule {
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email'
  required?: boolean
  pattern?: RegExp
  min?: number
  max?: number
  len?: number
  enum?: any[]
  whitespace?: boolean
  fields?: Record<string, FormRule>
  options?: any
  transform?: (value: any) => any
  message?: string | ((rule: any, value: any, callback: any, source?: any, options?: any) => string)
  asyncValidator?: (rule: any, value: any, callback: any, source?: any, options?: any) => void
  validator?: (rule: any, value: any, callback: any, source?: any, options?: any) => boolean | Error | Error[] | void
  trigger?: 'blur' | 'change' | string[]
}

// 分页组件类型
export interface PaginationProps extends BaseComponentProps {
  small?: boolean
  background?: boolean
  page_size?: number
  default_page_size?: number
  total?: number
  page_count?: number
  pager_count?: number
  current_page?: number
  default_current_page?: number
  layout?: string
  page_sizes?: number[]
  popper_class?: string
  prev_text?: string
  next_text?: string
  hide_on_single_page?: boolean
}

// 对话框组件类型
export interface DialogProps extends BaseComponentProps {
  model_value?: boolean
  title?: string
  width?: string | number
  fullscreen?: boolean
  top?: string
  modal?: boolean
  modal_class?: string
  append_to_body?: boolean
  lock_scroll?: boolean
  custom_class?: string
  open_delay?: number
  close_delay?: number
  close_on_click_modal?: boolean
  close_on_press_escape?: boolean
  show_close?: boolean
  before_close?: (done: () => void) => void
  center?: boolean
  destroy_on_close?: boolean
  close_icon?: string
  z_index?: number
  header_aria_level?: string
}

// 抽屉组件类型
export interface DrawerProps extends BaseComponentProps {
  model_value?: boolean
  title?: string
  size?: string | number
  direction?: 'ltr' | 'rtl' | 'ttb' | 'btt'
  modal?: boolean
  modal_class?: string
  append_to_body?: boolean
  lock_scroll?: boolean
  custom_class?: string
  open_delay?: number
  close_delay?: number
  close_on_click_modal?: boolean
  close_on_press_escape?: boolean
  show_close?: boolean
  before_close?: (done: () => void) => void
  destroy_on_close?: boolean
  close_icon?: string
  z_index?: number
  header_aria_level?: string
}

// 消息组件类型
export interface MessageOptions {
  message?: string
  type?: 'success' | 'warning' | 'info' | 'error'
  icon_class?: string
  dangerously_use_html_string?: boolean
  custom_class?: string
  duration?: number
  show_close?: boolean
  center?: boolean
  on_close?: () => void
  offset?: number
  append_to?: string | HTMLElement
  z_index?: number
  group?: boolean
  repeat_num?: number
}

// 通知组件类型
export interface NotificationOptions {
  title?: string
  message?: string
  type?: 'success' | 'warning' | 'info' | 'error'
  icon_class?: string
  custom_class?: string
  duration?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  show_close?: boolean
  on_close?: () => void
  on_click?: () => void
  offset?: number
  append_to?: string | HTMLElement
  z_index?: number
}

// 加载组件类型
export interface LoadingOptions {
  target?: string | HTMLElement
  body?: boolean
  fullscreen?: boolean
  lock?: boolean
  text?: string
  spinner?: string
  background?: string
  custom_class?: string
  svg?: string
  svg_view_box?: string
}

// 主题相关类型
export interface ThemeConfig {
  primary_color?: string
  success_color?: string
  warning_color?: string
  danger_color?: string
  info_color?: string
  text_color?: string
  border_color?: string
  background_color?: string
  font_family?: string
  font_size?: string
  border_radius?: string
  box_shadow?: string
}

// 响应式断点类型
export interface Breakpoints {
  xs: number
  sm: number
  md: number
  lg: number
  xl: number
  xxl: number
}

// 布局相关类型
export interface LayoutProps {
  direction?: 'horizontal' | 'vertical'
  gutter?: number
  justify?: 'start' | 'end' | 'center' | 'space-around' | 'space-between' | 'space-evenly'
  align?: 'top' | 'middle' | 'bottom'
  wrap?: boolean
  tag?: string
}

export interface ColProps extends BaseComponentProps {
  span?: number
  offset?: number
  push?: number
  pull?: number
  xs?: number | ColSize
  sm?: number | ColSize
  md?: number | ColSize
  lg?: number | ColSize
  xl?: number | ColSize
  tag?: string
}

export interface ColSize {
  span?: number
  offset?: number
  push?: number
  pull?: number
}

// 事件类型
export interface ComponentEvent<T = any> {
  type: string
  target: T
  currentTarget: T
  preventDefault: () => void
  stopPropagation: () => void
  stopImmediatePropagation: () => void
}

// 组件实例类型
export interface ComponentInstance {
  $el: HTMLElement
  $props: Record<string, any>
  $emit: (event: string, ...args: any[]) => void
  $slots: Record<string, any>
  $refs: Record<string, any>
  $parent: ComponentInstance | null
  $root: ComponentInstance
}
