"""
Master节点降级状态同步API路由
处理slave节点的降级状态同步请求，提供降级状态广播和版本管理
"""

import hashlib
import json
import time
from typing import Any

from fastapi import APIRouter, Request
from pydantic import BaseModel, Field

from api.dependencies.auth import get_api_key_dependency
from core.constants.error_codes import ErrorCodes
from core.degradation_core import DegradationLevel
from core.degradation_manager import get_degradation_manager
from core.logging.logging_system import log as logger
from core.middleware.request_tracking import request_tracker
from models.api import ApiResponse

# 降级同步路由（需要API密钥认证）
degradation_router = APIRouter(
    prefix="/api/v1/degradation",
    tags=["Degradation Sync (Secure)"],
    dependencies=[get_api_key_dependency()],
)


class DegradationStateInfo(BaseModel):
    """降级状态信息"""

    current_level: str = Field(..., description="当前降级级别")
    previous_level: str = Field(..., description="前一个降级级别")
    last_change_time: float = Field(..., description="最后变更时间戳")
    active_triggers: list[str] = Field(default_factory=list, description="活跃的触发器")
    is_manual_override: bool = Field(default=False, description="是否手动干预")
    override_reason: str | None = Field(None, description="手动干预原因")
    executed_actions_count: int = Field(default=0, description="已执行动作数量")
    degradation_duration: float = Field(default=0.0, description="降级持续时间")


class DegradationVersionInfo(BaseModel):
    """降级版本信息"""

    version: str = Field(..., description="状态版本号")
    timestamp: float = Field(..., description="版本时间戳")
    checksum: str = Field(..., description="状态校验和")


class DegradationSyncResponse(BaseModel):
    """降级同步响应"""

    state: DegradationStateInfo = Field(..., description="降级状态")
    version: DegradationVersionInfo = Field(..., description="版本信息")
    components_status: dict[str, Any] = Field(default_factory=dict, description="组件状态")
    metrics: dict[str, Any] = Field(default_factory=dict, description="降级指标")


class DegradationEventInfo(BaseModel):
    """降级事件信息"""

    event_type: str = Field(..., description="事件类型")
    timestamp: float = Field(..., description="事件时间戳")
    level: str = Field(..., description="降级级别")
    trigger_type: str | None = Field(None, description="触发类型")
    trigger_value: float | None = Field(None, description="触发值")
    metadata: dict[str, Any] = Field(default_factory=dict, description="事件元数据")


class DegradationHistoryResponse(BaseModel):
    """降级历史响应"""

    events: list[DegradationEventInfo] = Field(default_factory=list, description="事件列表")
    transitions: list[dict[str, Any]] = Field(default_factory=list, description="状态转换历史")
    total_count: int = Field(default=0, description="总记录数")


@degradation_router.get("/version", response_model=ApiResponse[DegradationVersionInfo])
async def get_degradation_version(request: Request):
    """
    获取当前降级状态的版本信息

    Args:
        request: HTTP请求对象

    Returns:
        ApiResponse[DegradationVersionInfo]: 降级状态版本信息
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting degradation version: {request_id}")

        # 添加请求追踪事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "degradation_version_request",
                {"endpoint": "/api/v1/degradation/version", "operation": "get_version"},
            )

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 获取当前状态
        current_status = degradation_manager.get_current_status()

        # 生成状态校验和
        state_data = {
            "current_level": current_status["current_level"],
            "last_change_time": current_status["last_change_time"],
            "active_triggers": sorted(current_status["active_triggers"]),
            "is_manual_override": current_status["is_manual_override"],
        }

        state_json = json.dumps(state_data, sort_keys=True)
        checksum = hashlib.md5(state_json.encode()).hexdigest()

        # 生成版本号（基于时间戳和校验和）
        version = f"{int(current_status['last_change_time'])}_{checksum[:8]}"

        version_info = DegradationVersionInfo(version=version, timestamp=time.perf_counter(), checksum=checksum)

        # 添加成功事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "degradation_version_success",
                {"version": version, "checksum": checksum},
            )

        return ApiResponse.success_response(
            data=version_info,
            message="降级状态版本获取成功",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get degradation version: {e}", exc_info=True)

        # 添加错误事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "degradation_version_error",
                {"error": str(e), "error_type": type(e).__name__},
            )

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="获取降级状态版本失败",
            request_id=request_id,
        )


@degradation_router.get("/sync", response_model=ApiResponse[DegradationSyncResponse])
async def sync_degradation_state(
    request: Request, version: str | None = None, include_metrics: bool = True, include_components: bool = True
):
    """
    同步降级状态信息

    Args:
        request: HTTP请求对象
        version: 客户端当前版本号，用于增量同步
        include_metrics: 是否包含指标信息
        include_components: 是否包含组件状态

    Returns:
        ApiResponse[DegradationSyncResponse]: 降级状态同步信息
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(
            f"Syncing degradation state: {request_id}",
            extra={
                "client_version": version,
                "include_metrics": include_metrics,
                "include_components": include_components,
            },
        )

        # 添加请求追踪事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "degradation_sync_request",
                {
                    "endpoint": "/api/v1/degradation/sync",
                    "client_version": version,
                    "include_metrics": include_metrics,
                    "include_components": include_components,
                },
            )

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 获取当前状态
        current_status = degradation_manager.get_current_status()

        # 构建状态信息
        state_info = DegradationStateInfo(
            current_level=current_status["current_level"],
            previous_level=current_status["previous_level"],
            last_change_time=current_status["last_change_time"],
            active_triggers=current_status["active_triggers"],
            is_manual_override=current_status["is_manual_override"],
            override_reason=current_status["override_reason"],
            executed_actions_count=current_status["executed_actions_count"],
            degradation_duration=current_status["degradation_duration"],
        )

        # 生成版本信息
        state_data = {
            "current_level": state_info.current_level,
            "last_change_time": state_info.last_change_time,
            "active_triggers": sorted(state_info.active_triggers),
            "is_manual_override": state_info.is_manual_override,
        }

        state_json = json.dumps(state_data, sort_keys=True)
        checksum = hashlib.md5(state_json.encode()).hexdigest()
        current_version = f"{int(state_info.last_change_time)}_{checksum[:8]}"

        version_info = DegradationVersionInfo(version=current_version, timestamp=time.perf_counter(), checksum=checksum)

        # 检查是否需要完整同步
        need_full_sync = True
        if version and version == current_version:
            need_full_sync = False
            logger.debug(f"Client version {version} is up to date, skipping full sync")

        # 构建响应数据
        sync_response = DegradationSyncResponse(state=state_info, version=version_info)

        # 根据需要添加组件状态
        if include_components and need_full_sync:
            from core.degradation_adapters import get_adapter_manager

            adapter_manager = get_adapter_manager()
            sync_response.components_status = adapter_manager.get_adapters_status()

        # 根据需要添加指标信息
        if include_metrics and need_full_sync:
            sync_response.metrics = degradation_manager.get_metrics()

        # 添加成功事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "degradation_sync_success",
                {
                    "current_version": current_version,
                    "need_full_sync": need_full_sync,
                    "components_included": include_components and need_full_sync,
                    "metrics_included": include_metrics and need_full_sync,
                },
            )

        return ApiResponse.success_response(
            data=sync_response,
            message="降级状态同步成功" if need_full_sync else "降级状态无变化",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to sync degradation state: {e}", exc_info=True)

        # 添加错误事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "degradation_sync_error",
                {"error": str(e), "error_type": type(e).__name__},
            )

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="降级状态同步失败",
            request_id=request_id,
        )


@degradation_router.get("/history", response_model=ApiResponse[DegradationHistoryResponse])
async def get_degradation_history(request: Request, limit: int = 50, event_types: str | None = None):
    """
    获取降级历史记录

    Args:
        request: HTTP请求对象
        limit: 返回记录数量限制
        event_types: 事件类型过滤（逗号分隔）

    Returns:
        ApiResponse[DegradationHistoryResponse]: 降级历史记录
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting degradation history: {request_id}")

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 获取事件历史
        events = degradation_manager.get_event_history(limit)

        # 过滤事件类型
        if event_types:
            filter_types = set(event_types.split(","))
            events = [event for event in events if event.event_type.value in filter_types]

        # 转换事件格式
        event_infos = []
        for event in events:
            event_info = DegradationEventInfo(
                event_type=event.event_type.value,
                timestamp=event.timestamp,
                level=event.level.value,
                trigger_type=event.trigger_type.value if event.trigger_type else None,
                trigger_value=event.trigger_value,
                metadata=event.metadata,
            )
            event_infos.append(event_info)

        # 获取状态转换历史
        transitions = degradation_manager.get_degradation_history(limit)

        history_response = DegradationHistoryResponse(
            events=event_infos, transitions=transitions, total_count=len(event_infos)
        )

        return ApiResponse.success_response(
            data=history_response,
            message="降级历史获取成功",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get degradation history: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="获取降级历史失败",
            request_id=request_id,
        )


class ManualDegradationRequest(BaseModel):
    """手动降级请求"""

    level: str = Field(..., description="目标降级级别")
    reason: str = Field(..., description="降级原因")
    force: bool = Field(default=False, description="是否强制执行")


@degradation_router.post("/manual-trigger", response_model=ApiResponse[dict[str, Any]])
async def manual_trigger_degradation(request: Request, degradation_request: ManualDegradationRequest):
    """
    手动触发降级

    Args:
        request: HTTP请求对象
        degradation_request: 降级请求参数

    Returns:
        ApiResponse[Dict[str, Any]]: 操作结果
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(
            f"Manual degradation trigger: {request_id}",
            extra={
                "target_level": degradation_request.level,
                "reason": degradation_request.reason,
                "force": degradation_request.force,
            },
        )

        # 验证降级级别
        try:
            target_level = DegradationLevel(degradation_request.level)
        except ValueError:
            return ApiResponse.error_response(
                code=ErrorCodes.INVALID_REQUEST_PARAMETER,
                message=f"无效的降级级别: {degradation_request.level}",
                request_id=request_id,
            )

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 执行手动降级
        success = degradation_manager.manual_trigger_degradation(target_level, degradation_request.reason)

        if success:
            # 获取更新后的状态
            current_status = degradation_manager.get_current_status()

            result = {
                "success": True,
                "current_level": current_status["current_level"],
                "previous_level": current_status["previous_level"],
                "reason": degradation_request.reason,
                "timestamp": time.perf_counter(),
            }

            return ApiResponse.success_response(
                data=result,
                message="手动降级执行成功",
                request_id=request_id,
            )
        else:
            return ApiResponse.error_response(
                code=ErrorCodes.OPERATION_FAILED,
                message="手动降级执行失败",
                request_id=request_id,
            )

    except Exception as e:
        logger.error(f"Failed to trigger manual degradation: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="手动降级执行异常",
            request_id=request_id,
        )


@degradation_router.post("/manual-recover", response_model=ApiResponse[dict[str, Any]])
async def manual_recover_degradation(request: Request, reason: str = "Manual recovery from API"):
    """
    手动恢复正常状态

    Args:
        request: HTTP请求对象
        reason: 恢复原因

    Returns:
        ApiResponse[Dict[str, Any]]: 操作结果
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Manual degradation recovery: {request_id}", extra={"reason": reason})

        # 获取降级管理器
        degradation_manager = get_degradation_manager()

        # 执行手动恢复
        success = degradation_manager.manual_recover(reason)

        if success:
            # 获取更新后的状态
            current_status = degradation_manager.get_current_status()

            result = {
                "success": True,
                "current_level": current_status["current_level"],
                "previous_level": current_status["previous_level"],
                "reason": reason,
                "timestamp": time.perf_counter(),
            }

            return ApiResponse.success_response(
                data=result,
                message="手动恢复执行成功",
                request_id=request_id,
            )
        else:
            return ApiResponse.error_response(
                code=ErrorCodes.OPERATION_FAILED,
                message="手动恢复执行失败",
                request_id=request_id,
            )

    except Exception as e:
        logger.error(f"Failed to recover from degradation: {e}", exc_info=True)

        return ApiResponse.error_response(
            code=ErrorCodes.INTERNAL_SERVER_ERROR,
            message="手动恢复执行异常",
            request_id=request_id,
        )
