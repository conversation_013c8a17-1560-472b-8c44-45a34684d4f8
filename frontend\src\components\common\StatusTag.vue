<template>
  <div class="status-tag-wrapper"
       :class="[`status-${status.toLowerCase()}`, { 'is-round': round }]"
       @mouseenter="isHovered = true"
       @mouseleave="isHovered = false">
    <div class="status-icon-container">
      <el-icon v-if="icon || defaultIcon" :size="iconSize" class="status-icon">
        <component :is="icon || defaultIcon" />
      </el-icon>
      <span v-if="isHovered" class="status-pulse"></span>
    </div>
    <span class="status-text">{{ displayText }}</span>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import {
  CircleCheck,
  Warning,
  InfoFilled,
  Clock,
  CircleCloseFilled,
  StarFilled,
  Refresh
} from '@element-plus/icons-vue'

const props = defineProps({
  status: {
    type: String,
    required: true
  },
  statusMap: {
    type: Object,
    default: () => ({})
  },
  typeMap: {
    type: Object,
    default: () => ({})
  },
  icon: {
    type: [String, Object],
    default: null
  },
  iconSize: {
    type: Number,
    default: 14
  },
  effect: {
    type: String,
    default: 'light',
    validator: (value) => ['dark', 'light', 'plain'].includes(value)
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['large', 'default', 'small'].includes(value)
  },
  round: {
    type: Boolean,
    default: true
  },
  closable: {
    type: Boolean,
    default: false
  },
  disableTransitions: {
    type: Boolean,
    default: false
  },
  hit: {
    type: Boolean,
    default: false
  },
  customColor: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['close'])
const isHovered = ref(false)

// 默认状态映射
const defaultStatusMap = {
  'NEW': '新规则',
  'CHANGED': '逻辑变更',
  'READY': '就绪',
  'DEPRECATED': '已弃用',
  'ACTIVE': '激活',
  'INACTIVE': '未激活',
  'ONLINE': '在线',
  'OFFLINE': '离线',
  'SUCCESS': '成功',
  'FAILED': '失败',
  'PENDING': '等待中',
  'PROCESSING': '处理中'
}

// 默认类型映射
const defaultTypeMap = {
  'NEW': 'warning',
  'CHANGED': 'warning',
  'READY': 'success',
  'DEPRECATED': 'info',
  'ACTIVE': 'success',
  'INACTIVE': 'info',
  'ONLINE': 'success',
  'OFFLINE': 'danger',
  'SUCCESS': 'success',
  'FAILED': 'danger',
  'PENDING': 'warning',
  'PROCESSING': 'primary'
}

// 默认图标映射
const defaultIconMap = {
  'NEW': StarFilled,
  'CHANGED': Refresh,
  'READY': CircleCheck,
  'DEPRECATED': InfoFilled,
  'ACTIVE': CircleCheck,
  'INACTIVE': Clock,
  'ONLINE': CircleCheck,
  'OFFLINE': CircleCloseFilled,
  'SUCCESS': CircleCheck,
  'FAILED': CircleCloseFilled,
  'PENDING': Clock,
  'PROCESSING': Refresh
}

// 显示文本
const displayText = computed(() => {
  const statusMap = { ...defaultStatusMap, ...props.statusMap }
  return statusMap[props.status] || props.status
})

// 标签类型
const tagType = computed(() => {
  const typeMap = { ...defaultTypeMap, ...props.typeMap }
  return typeMap[props.status] || 'info'
})

// 默认图标
const defaultIcon = computed(() => {
  return defaultIconMap[props.status]
})

// 关闭事件处理
const handleClose = () => {
  emit('close', props.status)
}
</script>

<style scoped>
.status-tag-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.status-tag-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.status-tag-wrapper:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.status-tag-wrapper:hover::before {
  opacity: 1;
}

.status-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.status-text {
  line-height: 1.4;
  transition: transform 0.3s ease;
}

.status-tag-wrapper:hover .status-text {
  transform: translateX(2px);
}

/* 脉冲动画效果 */
.status-pulse {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.15;
  transform: scale(0);
  animation: pulse 1.5s ease-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 0.15;
  }
  50% {
    opacity: 0.1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 状态变体 */
.status-ready {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
  border-color: rgba(103, 194, 58, 0.2);
}

.status-new {
  background-color: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
  border-color: rgba(230, 162, 60, 0.2);
}

.status-changed {
  background-color: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
  border-color: rgba(230, 162, 60, 0.2);
}

.status-deprecated {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
  border-color: rgba(144, 147, 153, 0.2);
}

.status-active {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
  border-color: rgba(103, 194, 58, 0.2);
}

.status-inactive {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
  border-color: rgba(144, 147, 153, 0.2);
}

.status-online {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
  border-color: rgba(103, 194, 58, 0.2);
}

.status-offline {
  background-color: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
  border-color: rgba(245, 108, 108, 0.2);
}

.status-success {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
  border-color: rgba(103, 194, 58, 0.2);
}

.status-failed {
  background-color: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
  border-color: rgba(245, 108, 108, 0.2);
}

.status-pending {
  background-color: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
  border-color: rgba(230, 162, 60, 0.2);
}

.status-processing {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
  border-color: rgba(64, 158, 255, 0.2);
}

/* 尺寸变体 */
.status-tag-wrapper.large {
  padding: 6px 16px;
  font-size: 14px;
  border-radius: 24px;
}

.status-tag-wrapper.small {
  padding: 2px 8px;
  font-size: 11px;
  border-radius: 16px;
}

/* 圆角变体 */
.status-tag-wrapper.is-round {
  border-radius: 100px;
}

/* 响应式调整 */
@media (max-width: 767px) {
  .status-tag-wrapper {
    padding: 3px 10px;
    font-size: 11px;
  }

  .status-icon {
    font-size: 12px;
  }
}
</style>
