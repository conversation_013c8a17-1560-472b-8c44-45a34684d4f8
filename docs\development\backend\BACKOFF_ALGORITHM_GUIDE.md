# 指数退避算法使用指南

## 1. 算法概述

指数退避算法是一种智能的重试延迟策略，通过指数增长的方式计算重试间隔，有效避免雷群效应，提高系统稳定性。

### 1.1 核心特性

- **指数增长**：延迟时间按指数规律增长，快速减少重试频率
- **抖动机制**：随机化延迟时间，避免多个客户端同时重试
- **最大延迟限制**：防止延迟时间过长影响用户体验
- **多种策略**：支持指数、线性、固定和自定义退避策略
- **线程安全**：支持多线程并发使用
- **高性能**：优化的算法实现，支持高频调用

## 2. 快速开始

### 2.1 基础使用

```python
from core.http_retry.backoff import calculate_backoff_delay

# 计算第0次重试的延迟（1秒）
delay = calculate_backoff_delay(0)
print(f"第0次重试延迟: {delay}秒")  # 输出: ~1秒

# 计算第3次重试的延迟（8秒）
delay = calculate_backoff_delay(3)
print(f"第3次重试延迟: {delay}秒")  # 输出: ~8秒
```

### 2.2 自定义参数

```python
# 自定义退避参数
delay = calculate_backoff_delay(
    attempt=2,              # 第2次重试
    base_delay=2.0,         # 基础延迟2秒
    max_delay=30.0,         # 最大延迟30秒
    backoff_factor=1.5,     # 退避因子1.5
    jitter=True             # 启用抖动
)
print(f"自定义延迟: {delay}秒")  # 输出: ~4.5秒 ± 抖动
```

## 3. 退避策略详解

### 3.1 指数退避（默认）

指数退避是最常用的策略，延迟时间按指数规律增长：

```python
# 公式: delay = min(base_delay * (backoff_factor ** attempt), max_delay)

from core.http_retry.backoff import calculate_backoff_delay

# 指数退避示例（base_delay=1, backoff_factor=2）
for i in range(5):
    delay = calculate_backoff_delay(i, jitter=False)
    print(f"第{i}次重试: {delay}秒")

# 输出:
# 第0次重试: 1.0秒   (1 * 2^0 = 1)
# 第1次重试: 2.0秒   (1 * 2^1 = 2)
# 第2次重试: 4.0秒   (1 * 2^2 = 4)
# 第3次重试: 8.0秒   (1 * 2^3 = 8)
# 第4次重试: 16.0秒  (1 * 2^4 = 16)
```

### 3.2 线性退避

线性退避提供更温和的延迟增长：

```python
# 公式: delay = min(base_delay * (1 + attempt * backoff_factor), max_delay)

delay = calculate_backoff_delay(
    attempt=2,
    strategy="linear",
    base_delay=1.0,
    backoff_factor=1.0,
    jitter=False
)
print(f"线性退避延迟: {delay}秒")  # 输出: 3.0秒 (1 * (1 + 2 * 1) = 3)
```

### 3.3 固定延迟

固定延迟对所有重试使用相同的延迟时间：

```python
delay = calculate_backoff_delay(
    attempt=5,              # 任意重试次数
    strategy="fixed",
    base_delay=2.0,
    jitter=False
)
print(f"固定延迟: {delay}秒")  # 输出: 2.0秒
```

### 3.4 自定义策略

```python
from core.http_retry.backoff import BackoffConfig, CustomBackoff

def custom_backoff_func(attempt, config):
    """自定义退避函数：平方增长"""
    return config.base_delay * (attempt + 1) ** 2

config = BackoffConfig(
    strategy="custom",
    base_delay=1.0,
    jitter=False
)

algorithm = CustomBackoff(config, custom_backoff_func)

for i in range(4):
    delay = algorithm.calculate_delay(i)
    print(f"第{i}次重试: {delay}秒")

# 输出:
# 第0次重试: 1.0秒   (1 * 1^2 = 1)
# 第1次重试: 4.0秒   (1 * 2^2 = 4)
# 第2次重试: 9.0秒   (1 * 3^2 = 9)
# 第3次重试: 16.0秒  (1 * 4^2 = 16)
```

## 4. 抖动机制

抖动机制通过随机化延迟时间来避免雷群效应：

### 4.1 默认抖动

```python
# 默认抖动范围：50%-100%
delays = []
for _ in range(5):
    delay = calculate_backoff_delay(2, jitter=True)  # 第2次重试，基础延迟4秒
    delays.append(round(delay, 2))

print(f"带抖动的延迟: {delays}")
# 输出示例: [3.2, 2.8, 3.9, 2.1, 3.6] (范围在2.0-4.0秒之间)
```

### 4.2 自定义抖动

```python
from core.http_retry.backoff import BackoffConfig, ExponentialBackoff

config = BackoffConfig(
    base_delay=10.0,
    jitter=True,
    jitter_min=0.8,         # 最小80%
    jitter_max=1.2          # 最大120%
)

algorithm = ExponentialBackoff(config)
delay = algorithm.calculate_delay(0)
print(f"自定义抖动延迟: {delay}秒")  # 输出: 8.0-12.0秒之间
```

## 5. 高级配置

### 5.1 配置类使用

```python
from core.http_retry.backoff import BackoffConfig, BackoffStrategy, ExponentialBackoff

# 创建配置
config = BackoffConfig(
    strategy=BackoffStrategy.EXPONENTIAL,
    base_delay=1.0,
    max_delay=60.0,
    backoff_factor=2.0,
    jitter=True,
    jitter_min=0.5,
    jitter_max=1.0
)

# 创建算法实例
algorithm = ExponentialBackoff(config)

# 计算延迟
for attempt in range(3):
    delay = algorithm.calculate_delay(attempt)
    print(f"第{attempt}次重试: {delay:.2f}秒")
```

### 5.2 算法缓存

```python
from core.http_retry.backoff import get_backoff_algorithm, clear_algorithm_cache

# 获取缓存的算法实例
config1 = BackoffConfig(base_delay=1.0)
config2 = BackoffConfig(base_delay=1.0)  # 相同配置

alg1 = get_backoff_algorithm(config1)
alg2 = get_backoff_algorithm(config2)

print(f"相同配置使用同一实例: {alg1 is alg2}")  # 输出: True

# 清除缓存
clear_algorithm_cache()
```

## 6. 性能优化

### 6.1 性能基准测试

```python
from core.http_retry.backoff import benchmark_backoff_algorithms

# 运行性能测试
results = benchmark_backoff_algorithms(max_attempts=10, iterations=1000)

for strategy, metrics in results.items():
    print(f"{strategy}策略:")
    print(f"  总耗时: {metrics['total_time']:.3f}秒")
    print(f"  平均耗时: {metrics['avg_time_per_call']:.6f}秒")
    print(f"  计算速度: {metrics['calls_per_second']:.0f} calls/sec")
```

### 6.2 线程安全

算法实现是线程安全的，支持多线程并发使用：

```python
import threading
from core.http_retry.backoff import calculate_backoff_delay

def worker():
    for attempt in range(5):
        delay = calculate_backoff_delay(attempt)
        print(f"线程{threading.current_thread().name}: 第{attempt}次重试 {delay:.2f}秒")

# 创建多个线程
threads = [threading.Thread(target=worker, name=f"Worker-{i}") for i in range(3)]

for thread in threads:
    thread.start()

for thread in threads:
    thread.join()
```

## 7. 实际应用场景

### 7.1 HTTP请求重试

```python
import asyncio
from core.http_retry.backoff import calculate_backoff_delay

async def http_request_with_retry(url, max_attempts=3):
    """带重试的HTTP请求示例"""
    
    for attempt in range(max_attempts):
        try:
            # 模拟HTTP请求
            response = await make_http_request(url)
            return response
            
        except Exception as e:
            if attempt < max_attempts - 1:
                # 计算退避延迟
                delay = calculate_backoff_delay(
                    attempt=attempt,
                    base_delay=1.0,
                    max_delay=30.0,
                    backoff_factor=2.0
                )
                
                print(f"请求失败，{delay:.2f}秒后重试...")
                await asyncio.sleep(delay)
            else:
                raise e
```

### 7.2 数据库连接重试

```python
from core.http_retry.backoff import calculate_backoff_delay
import time

def connect_database_with_retry(connection_string, max_attempts=5):
    """带重试的数据库连接示例"""
    
    for attempt in range(max_attempts):
        try:
            # 尝试连接数据库
            connection = create_database_connection(connection_string)
            return connection
            
        except ConnectionError as e:
            if attempt < max_attempts - 1:
                # 使用线性退避，避免过长等待
                delay = calculate_backoff_delay(
                    attempt=attempt,
                    strategy="linear",
                    base_delay=2.0,
                    max_delay=10.0,
                    backoff_factor=1.0
                )
                
                print(f"数据库连接失败，{delay:.2f}秒后重试...")
                time.sleep(delay)
            else:
                raise e
```

### 7.3 微服务调用重试

```python
from core.http_retry.backoff import BackoffConfig, ExponentialBackoff

class ServiceClient:
    """微服务客户端示例"""
    
    def __init__(self):
        # 为微服务调用配置保守的退避策略
        self.backoff_config = BackoffConfig(
            strategy=BackoffStrategy.EXPONENTIAL,
            base_delay=0.5,      # 较短的基础延迟
            max_delay=5.0,       # 较短的最大延迟
            backoff_factor=1.5,  # 较小的退避因子
            jitter=True
        )
        self.backoff_algorithm = ExponentialBackoff(self.backoff_config)
    
    async def call_service(self, service_name, method, data, max_attempts=3):
        """调用微服务"""
        
        for attempt in range(max_attempts):
            try:
                response = await self._make_service_call(service_name, method, data)
                return response
                
            except ServiceUnavailableError:
                if attempt < max_attempts - 1:
                    delay = self.backoff_algorithm.calculate_delay(attempt)
                    print(f"服务{service_name}不可用，{delay:.2f}秒后重试...")
                    await asyncio.sleep(delay)
                else:
                    raise
```

## 8. 最佳实践

### 8.1 参数选择建议

**基础延迟（base_delay）：**
- 快速响应服务：0.1-0.5秒
- 一般Web服务：1-2秒
- 数据库操作：2-5秒
- 批处理任务：5-10秒

**退避因子（backoff_factor）：**
- 保守策略：1.5-2.0
- 标准策略：2.0-3.0
- 激进策略：3.0-5.0

**最大延迟（max_delay）：**
- 实时服务：5-10秒
- 一般服务：30-60秒
- 批处理：300-600秒

### 8.2 抖动使用建议

- **总是启用抖动**：避免雷群效应
- **调整抖动范围**：根据负载情况调整
- **监控重试分布**：确保重试时间分散

### 8.3 策略选择建议

- **指数退避**：适用于大多数场景，快速减少重试频率
- **线性退避**：适用于对延迟敏感的场景
- **固定延迟**：适用于已知最佳重试间隔的场景
- **自定义策略**：适用于特殊业务需求

---

**文档版本：** 1.0  
**更新时间：** 2025-07-01  
**适用版本：** CQ-008及以后版本
