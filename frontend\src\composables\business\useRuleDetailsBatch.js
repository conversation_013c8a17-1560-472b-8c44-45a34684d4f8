import { ref, computed } from 'vue'
import { useRuleDetailsStore } from '@/stores/ruleDetails'
import { useRulesStore } from '@/stores/rules'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

/**
 * 规则明细批量操作 Composable
 * 提供批量创建、更新、删除等功能
 */
export function useRuleDetailsBatch(ruleKey) {
  // Store 实例
  const ruleDetailsStore = useRuleDetailsStore()
  const rulesStore = useRulesStore()

  // 从 Store 获取状态
  const { selectedDetails, batchLoading, operationStatus } = ruleDetailsStore

  // 本地状态
  const currentRuleKey = ref(ruleKey || '')
  const batchOperationProgress = ref({
    total: 0,
    completed: 0,
    failed: 0,
    errors: []
  })

  // ==================== 计算属性 ====================

  // 是否有选中的明细
  const hasSelectedDetails = computed(() => selectedDetails.length > 0)

  // 选中明细的数量
  const selectedCount = computed(() => selectedDetails.length)

  // 批量操作进度百分比
  const progressPercentage = computed(() => {
    if (batchOperationProgress.value.total === 0) return 0
    return Math.round(
      (batchOperationProgress.value.completed / batchOperationProgress.value.total) * 100
    )
  })

  // 是否正在执行批量操作
  const isBatchProcessing = computed(() => batchLoading.value)

  // ==================== 批量创建 ====================

  /**
   * 批量创建规则明细
   * @param {Array} detailsData - 明细数据数组
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 批量创建结果
   */
  const batchCreateDetails = async (detailsData, options = {}) => {
    if (!currentRuleKey.value) {
      ElMessage.error('缺少规则键')
      return null
    }

    if (!Array.isArray(detailsData) || detailsData.length === 0) {
      ElMessage.error('没有要创建的明细数据')
      return null
    }

    try {
      // 确认操作
      if (!options.skipConfirm) {
        await ElMessageBox.confirm(
          `确定要批量创建 ${detailsData.length} 条规则明细吗？`,
          '确认批量创建',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )
      }

      // 初始化进度
      batchOperationProgress.value = {
        total: detailsData.length,
        completed: 0,
        failed: 0,
        errors: []
      }

      // 执行批量创建
      const result = await ruleDetailsStore.batchCreateDetails(
        currentRuleKey.value,
        detailsData
      )

      // 更新进度
      if (result) {
        batchOperationProgress.value.completed = result.successCount || 0
        batchOperationProgress.value.failed = result.errors?.length || 0
        batchOperationProgress.value.errors = result.errors || []

        // 显示结果通知
        showBatchOperationResult('创建', result)

        // 刷新规则状态
        await rulesStore.refreshRule(currentRuleKey.value)
      }

      return result
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量创建失败:', error)
        ElMessage.error('批量创建失败')
      }
      return null
    }
  }

  // ==================== 批量更新 ====================

  /**
   * 批量更新规则明细
   * @param {Array} updates - 更新数据数组
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 批量更新结果
   */
  const batchUpdateDetails = async (updates, options = {}) => {
    if (!currentRuleKey.value) {
      ElMessage.error('缺少规则键')
      return null
    }

    if (!Array.isArray(updates) || updates.length === 0) {
      ElMessage.error('没有要更新的明细数据')
      return null
    }

    try {
      // 确认操作
      if (!options.skipConfirm) {
        await ElMessageBox.confirm(
          `确定要批量更新 ${updates.length} 条规则明细吗？`,
          '确认批量更新',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
      }

      // 初始化进度
      batchOperationProgress.value = {
        total: updates.length,
        completed: 0,
        failed: 0,
        errors: []
      }

      // 执行批量更新
      const result = await ruleDetailsStore.batchUpdateDetails(
        currentRuleKey.value,
        updates
      )

      // 更新进度
      if (result) {
        batchOperationProgress.value.completed = result.successCount || 0
        batchOperationProgress.value.failed = result.errors?.length || 0
        batchOperationProgress.value.errors = result.errors || []

        // 显示结果通知
        showBatchOperationResult('更新', result)

        // 刷新规则状态
        await rulesStore.refreshRule(currentRuleKey.value)
      }

      return result
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量更新失败:', error)
        ElMessage.error('批量更新失败')
      }
      return null
    }
  }

  /**
   * 批量更新选中的明细
   * @param {Object} updateData - 更新数据
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 批量更新结果
   */
  const batchUpdateSelected = async (updateData, options = {}) => {
    if (selectedDetails.length === 0) {
      ElMessage.error('请先选择要更新的明细')
      return null
    }

    const updates = selectedDetails.map(detail => ({
      id: detail.id,
      data: updateData
    }))

    return await batchUpdateDetails(updates, options)
  }

  // ==================== 批量删除 ====================

  /**
   * 批量删除规则明细
   * @param {Array} detailIds - 明细ID数组
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 批量删除结果
   */
  const batchDeleteDetails = async (detailIds, options = {}) => {
    if (!currentRuleKey.value) {
      ElMessage.error('缺少规则键')
      return null
    }

    if (!Array.isArray(detailIds) || detailIds.length === 0) {
      ElMessage.error('没有要删除的明细')
      return null
    }

    try {
      // 确认操作
      if (!options.skipConfirm) {
        await ElMessageBox.confirm(
          `确定要批量删除 ${detailIds.length} 条规则明细吗？此操作不可恢复！`,
          '确认批量删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'error'
          }
        )
      }

      // 初始化进度
      batchOperationProgress.value = {
        total: detailIds.length,
        completed: 0,
        failed: 0,
        errors: []
      }

      // 执行批量删除
      const result = await ruleDetailsStore.batchDeleteDetails(
        currentRuleKey.value,
        detailIds
      )

      // 更新进度
      if (result) {
        batchOperationProgress.value.completed = result.successCount || 0
        batchOperationProgress.value.failed = result.errors?.length || 0
        batchOperationProgress.value.errors = result.errors || []

        // 显示结果通知
        showBatchOperationResult('删除', result)

        // 清除选择
        ruleDetailsStore.clearSelection()

        // 刷新规则状态
        await rulesStore.refreshRule(currentRuleKey.value)
      }

      return result
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量删除失败:', error)
        ElMessage.error('批量删除失败')
      }
      return null
    }
  }

  /**
   * 批量删除选中的明细
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 批量删除结果
   */
  const batchDeleteSelected = async (options = {}) => {
    if (selectedDetails.length === 0) {
      ElMessage.error('请先选择要删除的明细')
      return null
    }

    const detailIds = selectedDetails.map(detail => detail.id)
    return await batchDeleteDetails(detailIds, options)
  }

  // ==================== 增量操作 ====================

  /**
   * 增量数据上传
   * @param {Array} incrementalData - 增量数据
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 增量上传结果
   */
  const incrementalUpload = async (incrementalData, options = {}) => {
    if (!currentRuleKey.value) {
      ElMessage.error('缺少规则键')
      return null
    }

    if (!Array.isArray(incrementalData) || incrementalData.length === 0) {
      ElMessage.error('没有增量数据')
      return null
    }

    try {
      // 分析增量数据
      const analysis = analyzeIncrementalData(incrementalData)

      // 确认操作
      if (!options.skipConfirm) {
        await ElMessageBox.confirm(
          `增量数据分析结果：\n` +
          `新增：${analysis.create.length} 条\n` +
          `更新：${analysis.update.length} 条\n` +
          `删除：${analysis.delete.length} 条\n\n` +
          `确定要执行增量上传吗？`,
          '确认增量上传',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )
      }

      const results = {
        create: null,
        update: null,
        delete: null,
        totalSuccess: 0,
        totalFailed: 0,
        errors: []
      }

      // 执行增量操作
      if (analysis.create.length > 0) {
        results.create = await batchCreateDetails(analysis.create, { skipConfirm: true })
        if (results.create) {
          results.totalSuccess += results.create.successCount || 0
          results.totalFailed += results.create.errors?.length || 0
          results.errors.push(...(results.create.errors || []))
        }
      }

      if (analysis.update.length > 0) {
        results.update = await batchUpdateDetails(analysis.update, { skipConfirm: true })
        if (results.update) {
          results.totalSuccess += results.update.successCount || 0
          results.totalFailed += results.update.errors?.length || 0
          results.errors.push(...(results.update.errors || []))
        }
      }

      if (analysis.delete.length > 0) {
        results.delete = await batchDeleteDetails(analysis.delete, { skipConfirm: true })
        if (results.delete) {
          results.totalSuccess += results.delete.successCount || 0
          results.totalFailed += results.delete.errors?.length || 0
          results.errors.push(...(results.delete.errors || []))
        }
      }

      // 显示增量上传结果
      showIncrementalUploadResult(results)

      return results
    } catch (error) {
      if (error !== 'cancel') {
        console.error('增量上传失败:', error)
        ElMessage.error('增量上传失败')
      }
      return null
    }
  }

  // ==================== 辅助方法 ====================

  /**
   * 分析增量数据
   * @param {Array} data - 增量数据
   * @returns {Object} 分析结果
   */
  const analyzeIncrementalData = (data) => {
    const analysis = {
      create: [],
      update: [],
      delete: []
    }

    data.forEach(item => {
      switch (item.operation?.toUpperCase()) {
        case 'CREATE':
        case 'INSERT':
          analysis.create.push(item.data || item)
          break
        case 'UPDATE':
          analysis.update.push({
            id: item.id,
            data: item.data || item
          })
          break
        case 'DELETE':
          analysis.delete.push(item.id)
          break
        default:
          // 默认为创建操作
          analysis.create.push(item.data || item)
      }
    })

    return analysis
  }

  /**
   * 显示批量操作结果
   * @param {string} operation - 操作类型
   * @param {Object} result - 操作结果
   */
  const showBatchOperationResult = (operation, result) => {
    const { successCount = 0, errors = [] } = result

    if (errors.length === 0) {
      ElNotification.success({
        title: `批量${operation}成功`,
        message: `成功${operation} ${successCount} 条记录`,
        duration: 3000
      })
    } else {
      ElNotification.warning({
        title: `批量${operation}完成`,
        message: `成功 ${successCount} 条，失败 ${errors.length} 条`,
        duration: 5000
      })
    }
  }

  /**
   * 显示增量上传结果
   * @param {Object} results - 上传结果
   */
  const showIncrementalUploadResult = (results) => {
    const { totalSuccess, totalFailed } = results

    if (totalFailed === 0) {
      ElNotification.success({
        title: '增量上传成功',
        message: `成功处理 ${totalSuccess} 条记录`,
        duration: 3000
      })
    } else {
      ElNotification.warning({
        title: '增量上传完成',
        message: `成功 ${totalSuccess} 条，失败 ${totalFailed} 条`,
        duration: 5000
      })
    }
  }

  /**
   * 重置批量操作进度
   */
  const resetBatchProgress = () => {
    batchOperationProgress.value = {
      total: 0,
      completed: 0,
      failed: 0,
      errors: []
    }
  }

  // ==================== 返回接口 ====================

  return {
    // 状态
    currentRuleKey,
    batchOperationProgress,
    selectedDetails,
    batchLoading,
    operationStatus,

    // 计算属性
    hasSelectedDetails,
    selectedCount,
    progressPercentage,
    isBatchProcessing,

    // 批量操作方法
    batchCreateDetails,
    batchUpdateDetails,
    batchUpdateSelected,
    batchDeleteDetails,
    batchDeleteSelected,

    // 增量操作
    incrementalUpload,

    // 辅助方法
    analyzeIncrementalData,
    resetBatchProgress
  }
}
