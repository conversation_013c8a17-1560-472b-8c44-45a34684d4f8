"""
任务恢复服务
负责在服务启动时恢复未完成的注册任务，确保任务的连续性和数据一致性
集成补偿事务处理和数据一致性检查功能
"""

import asyncio
from typing import Any

from config.settings import get_settings
from core.logging.logging_system import log as logger
from services.task_status_manager import TaskStatus, get_task_status_manager


class TaskRecoveryService:
    """
    任务恢复服务

    负责在服务启动时恢复未完成的注册任务，
    分析任务状态并决定恢复策略。
    """

    def __init__(self):
        """初始化任务恢复服务"""
        self.settings = get_settings()
        self.task_manager = get_task_status_manager()

        # 延迟初始化补偿和一致性检查服务
        self.compensation_service = None
        self.consistency_checker = None

        # 定期检查任务
        self._consistency_check_task = None
        self._compensation_process_task = None

        logger.info("TaskRecoveryService初始化完成")

    async def recover_tasks_on_startup(self) -> dict[str, Any]:
        """
        启动时恢复任务

        Returns:
            Dict: 恢复结果统计
        """
        if not self.settings.TASK_RECOVERY_ENABLED or not self.settings.TASK_RECOVERY_ON_STARTUP:
            logger.info("任务恢复功能已禁用")
            return {"enabled": False, "message": "任务恢复功能已禁用"}

        if self.settings.TASK_STATUS_STORAGE_TYPE != "database":
            logger.info("当前使用内存存储，无需恢复任务")
            return {"enabled": False, "message": "内存存储模式无需恢复任务"}

        logger.info("开始恢复未完成的注册任务...")

        try:
            # 获取需要恢复的任务
            recovery_tasks = await self.task_manager.get_tasks_needing_recovery()

            if not recovery_tasks:
                logger.info("没有需要恢复的任务")
                return {
                    "enabled": True,
                    "total_tasks": 0,
                    "recovered_tasks": 0,
                    "failed_tasks": 0,
                    "message": "没有需要恢复的任务",
                }

            logger.info(f"发现 {len(recovery_tasks)} 个需要恢复的任务")

            # 分析和恢复任务
            recovery_stats = {
                "enabled": True,
                "total_tasks": len(recovery_tasks),
                "recovered_tasks": 0,
                "failed_tasks": 0,
                "skipped_tasks": 0,
                "details": [],
            }

            for task_info in recovery_tasks:
                try:
                    result = await self._recover_single_task(task_info)
                    recovery_stats["details"].append(result)

                    if result["action"] == "recovered":
                        recovery_stats["recovered_tasks"] += 1
                    elif result["action"] == "failed":
                        recovery_stats["failed_tasks"] += 1
                    else:
                        recovery_stats["skipped_tasks"] += 1

                except Exception as e:
                    logger.error(f"恢复任务 {task_info['task_id']} 时发生异常: {e}")
                    recovery_stats["failed_tasks"] += 1
                    recovery_stats["details"].append(
                        {"task_id": task_info["task_id"], "action": "failed", "reason": f"恢复过程异常: {e}"}
                    )

            logger.info(
                f"任务恢复完成 - "
                f"总计: {recovery_stats['total_tasks']}, "
                f"成功: {recovery_stats['recovered_tasks']}, "
                f"失败: {recovery_stats['failed_tasks']}, "
                f"跳过: {recovery_stats['skipped_tasks']}"
            )

            return recovery_stats

        except Exception as e:
            logger.error(f"任务恢复过程中发生异常: {e}")
            return {"enabled": True, "error": str(e), "message": "任务恢复过程中发生异常"}

    async def _recover_single_task(self, task_info: dict[str, Any]) -> dict[str, Any]:
        """
        恢复单个任务

        Args:
            task_info: 任务信息

        Returns:
            Dict: 恢复结果
        """
        task_id = task_info["task_id"]
        rule_key = task_info["rule_key"]
        status = task_info["status"]
        recovery_suggestion = task_info.get("recovery_suggestion", "")

        logger.debug(f"开始恢复任务 {task_id}, 状态: {status}, 建议: {recovery_suggestion}")

        # 根据恢复建议决定恢复策略
        if "可以重新调度" in recovery_suggestion:
            return await self._reschedule_task(task_info)
        elif "可以直接完成" in recovery_suggestion:
            return await self._complete_task(task_info)
        elif "需要补偿操作" in recovery_suggestion:
            return await self._mark_for_compensation(task_info)
        else:
            return await self._mark_for_manual_check(task_info)

    async def _reschedule_task(self, task_info: dict[str, Any]) -> dict[str, Any]:
        """
        重新调度任务

        Args:
            task_info: 任务信息

        Returns:
            Dict: 操作结果
        """
        task_id = task_info["task_id"]

        try:
            # 将任务重新添加到队列
            from master import REGISTRATION_QUEUE

            if REGISTRATION_QUEUE is None:
                return {"task_id": task_id, "action": "failed", "reason": "注册队列未初始化"}

            # 构建注册数据（这里需要从任务信息中重建）
            registration_data = {
                "rule_key": task_info["rule_key"],
                "excel_data": [],  # 注意：这里可能需要从其他地方获取原始数据
                "base_rule": None,  # 注意：这里可能需要从其他地方获取原始数据
            }

            # 重新添加到队列
            await REGISTRATION_QUEUE.put((task_id, registration_data))

            logger.info(f"任务 {task_id} 已重新调度到队列")

            return {
                "task_id": task_id,
                "action": "recovered",
                "method": "rescheduled",
                "reason": "任务已重新添加到处理队列",
            }

        except Exception as e:
            logger.error(f"重新调度任务 {task_id} 失败: {e}")
            return {"task_id": task_id, "action": "failed", "reason": f"重新调度失败: {e}"}

    async def _complete_task(self, task_info: dict[str, Any]) -> dict[str, Any]:
        """
        直接完成任务

        Args:
            task_info: 任务信息

        Returns:
            Dict: 操作结果
        """
        task_id = task_info["task_id"]

        try:
            # 更新任务状态为已完成
            success = await self.task_manager.update_task_status(
                task_id, TaskStatus.COMPLETED, message="任务在恢复过程中被标记为已完成"
            )

            if success:
                logger.info(f"任务 {task_id} 已标记为完成")
                return {
                    "task_id": task_id,
                    "action": "recovered",
                    "method": "completed",
                    "reason": "任务已完成，状态已更新",
                }
            else:
                return {"task_id": task_id, "action": "failed", "reason": "更新任务状态失败"}

        except Exception as e:
            logger.error(f"完成任务 {task_id} 失败: {e}")
            return {"task_id": task_id, "action": "failed", "reason": f"完成任务失败: {e}"}

    async def _mark_for_compensation(self, task_info: dict[str, Any]) -> dict[str, Any]:
        """
        标记需要补偿操作

        Args:
            task_info: 任务信息

        Returns:
            Dict: 操作结果
        """
        task_id = task_info["task_id"]

        try:
            # 更新任务状态为失败，并标记需要补偿
            success = await self.task_manager.update_task_status(
                task_id, TaskStatus.FAILED, message="任务需要补偿操作", error_message="数据一致性问题，需要手动处理"
            )

            if success:
                logger.warning(f"任务 {task_id} 已标记为需要补偿操作")
                return {
                    "task_id": task_id,
                    "action": "marked_compensation",
                    "method": "compensation_needed",
                    "reason": "任务存在数据一致性问题，需要补偿操作",
                }
            else:
                return {"task_id": task_id, "action": "failed", "reason": "标记补偿操作失败"}

        except Exception as e:
            logger.error(f"标记任务 {task_id} 补偿操作失败: {e}")
            return {"task_id": task_id, "action": "failed", "reason": f"标记补偿操作失败: {e}"}

    async def _mark_for_manual_check(self, task_info: dict[str, Any]) -> dict[str, Any]:
        """
        标记需要手动检查

        Args:
            task_info: 任务信息

        Returns:
            Dict: 操作结果
        """
        task_id = task_info["task_id"]

        logger.warning(f"任务 {task_id} 状态不明确，需要手动检查")

        return {
            "task_id": task_id,
            "action": "manual_check_needed",
            "method": "manual_intervention",
            "reason": "任务状态不明确，需要手动检查和处理",
        }

    async def initialize_compensation_services(self):
        """初始化补偿和一致性检查服务"""
        try:
            if self.compensation_service is None:
                from services.compensation_transaction_service import CompensationTransactionService

                self.compensation_service = CompensationTransactionService()
                await self.compensation_service.initialize_dependencies()
                logger.info("补偿事务服务初始化完成")

            if self.consistency_checker is None:
                from services.data_consistency_checker import DataConsistencyChecker

                self.consistency_checker = DataConsistencyChecker()
                await self.consistency_checker.initialize_dependencies()
                logger.info("数据一致性检查器初始化完成")

        except Exception as e:
            logger.error(f"初始化补偿服务失败: {e}")
            raise

    async def process_compensation_tasks(self) -> dict[str, Any]:
        """
        处理需要补偿的任务

        Returns:
            Dict: 处理结果统计
        """
        try:
            if not self.compensation_service:
                await self.initialize_compensation_services()

            # 获取需要补偿的任务
            compensation_tasks = await self.task_manager.get_tasks_needing_compensation()

            if not compensation_tasks:
                return {"processed": 0, "successful": 0, "failed": 0, "message": "无需要补偿的任务"}

            logger.info(f"开始处理 {len(compensation_tasks)} 个需要补偿的任务")

            processed = 0
            successful = 0
            failed = 0

            for task_info in compensation_tasks:
                try:
                    task_id = task_info["task_id"]

                    # 根据任务状态决定补偿策略
                    from services.compensation_transaction_service import CompensationStrategy

                    if task_info.get("status") == TaskStatus.FAILED.value:
                        strategy = CompensationStrategy.RETRY
                    else:
                        strategy = CompensationStrategy.MANUAL

                    # 执行补偿操作
                    result = await self.compensation_service.process_compensation(task_id, task_info, strategy)

                    processed += 1
                    if result.success:
                        successful += 1
                        logger.info(f"任务 {task_id} 补偿成功: {result.message}")

                        # 更新任务状态，清除补偿标记
                        if hasattr(self.task_manager, "mark_compensation_completed"):
                            await self.task_manager.mark_compensation_completed(task_id)
                    else:
                        failed += 1
                        logger.error(f"任务 {task_id} 补偿失败: {result.message}")

                except Exception as e:
                    processed += 1
                    failed += 1
                    logger.error(f"处理补偿任务异常: {e}", exc_info=True)

            logger.info(f"补偿任务处理完成，处理: {processed}, 成功: {successful}, 失败: {failed}")

            return {"processed": processed, "successful": successful, "failed": failed, "message": "补偿任务处理完成"}

        except Exception as e:
            logger.error(f"处理补偿任务失败: {e}", exc_info=True)
            return {"processed": 0, "successful": 0, "failed": 0, "error": str(e), "message": "补偿任务处理失败"}

    async def run_consistency_check(self, rule_key: str = None) -> dict[str, Any]:
        """
        运行数据一致性检查

        Args:
            rule_key: 可选的规则键，如果不提供则检查所有规则

        Returns:
            Dict: 检查结果统计
        """
        try:
            if not self.consistency_checker:
                await self.initialize_compensation_services()

            logger.info(f"开始数据一致性检查，规则: {rule_key or '所有规则'}")

            if rule_key:
                reports = [await self.consistency_checker.check_rule_consistency(rule_key)]
            else:
                reports = await self.consistency_checker.check_all_rules_consistency()

            # 统计结果
            total_rules = len(reports)
            consistent_rules = len([r for r in reports if r.is_consistent])
            inconsistent_rules = total_rules - consistent_rules
            total_inconsistencies = sum(len(r.inconsistencies) for r in reports)

            logger.info(f"一致性检查完成，规则: {total_rules}, 一致: {consistent_rules}, 不一致: {inconsistent_rules}")

            return {
                "total_rules": total_rules,
                "consistent_rules": consistent_rules,
                "inconsistent_rules": inconsistent_rules,
                "total_inconsistencies": total_inconsistencies,
                "reports": [r.to_dict() for r in reports],
                "message": "数据一致性检查完成",
            }

        except Exception as e:
            logger.error(f"数据一致性检查失败: {e}", exc_info=True)
            return {
                "total_rules": 0,
                "consistent_rules": 0,
                "inconsistent_rules": 0,
                "total_inconsistencies": 0,
                "error": str(e),
                "message": "数据一致性检查失败",
            }

    async def start_periodic_tasks(self):
        """启动定期任务"""
        try:
            # 启动定期补偿任务处理
            if self._compensation_process_task is None:
                self._compensation_process_task = asyncio.create_task(self._compensation_process_loop())
                logger.info("定期补偿任务处理已启动")

            # 启动定期一致性检查
            if self._consistency_check_task is None:
                self._consistency_check_task = asyncio.create_task(self._consistency_check_loop())
                logger.info("定期一致性检查已启动")

        except Exception as e:
            logger.error(f"启动定期任务失败: {e}")

    async def stop_periodic_tasks(self):
        """停止定期任务"""
        try:
            if self._compensation_process_task:
                self._compensation_process_task.cancel()
                try:
                    await self._compensation_process_task
                except asyncio.CancelledError:
                    pass
                self._compensation_process_task = None
                logger.info("定期补偿任务处理已停止")

            if self._consistency_check_task:
                self._consistency_check_task.cancel()
                try:
                    await self._consistency_check_task
                except asyncio.CancelledError:
                    pass
                self._consistency_check_task = None
                logger.info("定期一致性检查已停止")

        except Exception as e:
            logger.error(f"停止定期任务失败: {e}")

    async def _compensation_process_loop(self):
        """补偿任务处理循环"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时检查一次
                await self.process_compensation_tasks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"定期补偿任务处理异常: {e}")

    async def _consistency_check_loop(self):
        """一致性检查循环"""
        while True:
            try:
                await asyncio.sleep(86400)  # 每天检查一次
                await self.run_consistency_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"定期一致性检查异常: {e}")


# 全局任务恢复服务实例
_task_recovery_service = None


def get_task_recovery_service() -> TaskRecoveryService:
    """获取全局任务恢复服务实例"""
    global _task_recovery_service
    if _task_recovery_service is None:
        _task_recovery_service = TaskRecoveryService()
    return _task_recovery_service
