/**
 * 数据校验相关类型定义
 * 支持前端动态校验和与后端规则同步
 */

// 校验规则类型枚举
export enum ValidationRuleType {
  REQUIRED = 'required',
  MIN_LENGTH = 'min_length',
  MAX_LENGTH = 'max_length',
  MIN_VALUE = 'min_value',
  MAX_VALUE = 'max_value',
  PATTERN = 'pattern',
  EMAIL = 'email',
  URL = 'url',
  DATE_FORMAT = 'date_format',
  INTEGER = 'integer',
  FLOAT = 'float',
  ARRAY = 'array',
  ENUM = 'enum',
  CUSTOM = 'custom',
  RANGE = 'range'
}

// 校验错误严重程度
export enum ValidationSeverity {
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info'
}

// 校验规则接口
export interface ValidationRule {
  field_name: string
  chinese_name: string
  rule_type: ValidationRuleType
  rule_value: any
  error_message: string
  is_required: boolean
  priority: number
  severity?: ValidationSeverity
}

// 校验错误接口
export interface ValidationError {
  field_name: string
  chinese_name: string
  error_code: string
  error_message: string
  error_value: any
  rule_type: string
  suggestions: string[]
  severity: ValidationSeverity
}

// 校验结果接口
export interface ValidationResult {
  valid: boolean
  errors: ValidationError[]
  warnings: string[]
  field_count: number
  validated_fields: string[]
  duration: number
  metadata?: Record<string, any>
}

// Element Plus 表单规则接口
export interface ElementFormRule {
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email'
  required?: boolean
  pattern?: RegExp
  min?: number
  max?: number
  len?: number
  enum?: any[]
  whitespace?: boolean
  message?: string
  trigger?: 'blur' | 'change' | string[]
  validator?: (rule: any, value: any, callback: any) => void
  asyncValidator?: (rule: any, value: any, callback: any) => Promise<void>
}

// 字段校验配置接口
export interface FieldValidationConfig {
  field_name: string
  chinese_name: string
  rules: ValidationRule[]
  element_rules: ElementFormRule[]
  is_required: boolean
  data_type: string
  default_value?: any
}

// 规则模板校验配置接口
export interface RuleTemplateValidationConfig {
  rule_key: string
  rule_name: string
  fields: Record<string, FieldValidationConfig>
  generated_at: string
  version: string
}

// 实时校验选项接口
export interface RealTimeValidationOptions {
  debounce_delay: number
  validate_on_blur: boolean
  validate_on_change: boolean
  show_success_feedback: boolean
  auto_focus_error: boolean
}

// 批量校验进度接口
export interface BatchValidationProgress {
  total: number
  processed: number
  valid: number
  invalid: number
  current_field?: string
  progress_percentage: number
  estimated_remaining_time?: number
}

// 校验状态接口
export interface ValidationState {
  is_validating: boolean
  has_errors: boolean
  error_count: number
  warning_count: number
  last_validation_time?: Date
  validation_duration?: number
}

// 校验缓存接口
export interface ValidationCache {
  rule_key: string
  config: RuleTemplateValidationConfig
  cached_at: Date
  expires_at: Date
  version: string
}

// 校验同步状态接口
export interface ValidationSyncStatus {
  is_syncing: boolean
  last_sync_time?: Date
  sync_version?: string
  sync_errors: string[]
  pending_updates: string[]
}

// 错误修复建议接口
export interface ValidationSuggestion {
  field_name: string
  error_type: string
  suggestion_text: string
  auto_fix_available: boolean
  fix_action?: () => void
}

// 校验事件接口
export interface ValidationEvent {
  type: 'field_validated' | 'form_validated' | 'batch_progress' | 'sync_completed'
  field_name?: string
  result?: ValidationResult
  progress?: BatchValidationProgress
  timestamp: Date
}

// 校验监听器类型
export type ValidationEventListener = (event: ValidationEvent) => void

// 校验配置选项接口
export interface ValidationEngineOptions {
  cache_enabled: boolean
  cache_ttl_minutes: number
  real_time_validation: RealTimeValidationOptions
  sync_interval_minutes: number
  max_batch_size: number
  performance_monitoring: boolean
}

// 导出默认配置
export const DEFAULT_VALIDATION_OPTIONS: ValidationEngineOptions = {
  cache_enabled: true,
  cache_ttl_minutes: 30,
  real_time_validation: {
    debounce_delay: 300,
    validate_on_blur: true,
    validate_on_change: true,
    show_success_feedback: false,
    auto_focus_error: true
  },
  sync_interval_minutes: 60,
  max_batch_size: 100,
  performance_monitoring: true
}

// 校验规则转换工具类型
export interface ValidationRuleConverter {
  toElementRule(rule: ValidationRule): ElementFormRule
  fromBackendRule(backendRule: any): ValidationRule
  generateErrorMessage(rule: ValidationRule, value: any): string
}

// 校验性能指标接口
export interface ValidationPerformanceMetrics {
  total_validations: number
  average_duration: number
  cache_hit_rate: number
  error_rate: number
  last_reset_time: Date
}
