"""
应用启动管理器
集成所有启动优化组件，管理完整的应用启动流程
"""

import asyncio
import time
from typing import Any

from config.settings import settings
from core.logging.logging_system import log as logger
from services.startup_optimizer import startup_optimizer


class ApplicationStartup:
    """应用启动管理器"""

    def __init__(self):
        self.startup_completed = False
        self.startup_success = False
        self.startup_time = 0.0
        self.startup_error = None

    async def initialize_application(self) -> bool:
        """初始化应用程序"""
        logger.info("开始应用程序初始化...")
        start_time = time.perf_counter()

        try:
            # 1. 基础组件初始化
            await self._initialize_basic_components()

            # 2. 启动优化（后台执行）
            if getattr(settings, "ENABLE_STARTUP_OPTIMIZATION", True):
                # 创建后台任务执行启动优化
                asyncio.create_task(self._background_startup_optimization())

            # 3. 标记启动完成
            self.startup_time = time.perf_counter() - start_time
            self.startup_completed = True
            self.startup_success = True

            logger.info(f"应用程序初始化完成，耗时 {self.startup_time:.1f}s")
            return True

        except Exception as e:
            self.startup_time = time.perf_counter() - start_time
            self.startup_completed = True
            self.startup_success = False
            self.startup_error = str(e)

            logger.error(f"应用程序初始化失败: {e}", exc_info=True)
            return False

    async def _initialize_basic_components(self):
        """初始化基础组件"""
        logger.info("初始化基础组件...")

        # 1. 初始化内存优化器
        from core.memory_optimizer import memory_optimizer
        memory_optimizer.start_monitoring()
        logger.info("内存优化器已启动")

        # 2. 初始化智能缓存
        logger.info("智能缓存已初始化")

        # 3. 初始化性能监控
        from core.performance_monitor import performance_monitor
        performance_monitor.start_monitoring()
        logger.info("性能监控已启动")

        # 4. 初始化动态进程池（如果需要）
        if getattr(settings, "MODE", "master") == "slave":
            from core.dynamic_process_pool import dynamic_process_pool
            await dynamic_process_pool.start()
            logger.info("动态进程池已启动")

    async def _background_startup_optimization(self):
        """后台启动优化"""
        try:
            logger.info("开始后台启动优化...")

            # 等待基础组件稳定
            await asyncio.sleep(1.0)

            # 执行启动优化
            success = await startup_optimizer.optimize_startup()

            if success:
                logger.info("后台启动优化完成")
            else:
                logger.warning("后台启动优化失败，但不影响服务运行")

        except Exception as e:
            logger.error(f"后台启动优化异常: {e}", exc_info=True)

    def get_startup_status(self) -> dict[str, Any]:
        """获取启动状态"""
        return {
            "startup_completed": self.startup_completed,
            "startup_success": self.startup_success,
            "startup_time": self.startup_time,
            "startup_error": self.startup_error,
            "optimization_status": startup_optimizer.get_startup_status() if self.startup_completed else None
        }

    def is_ready(self) -> bool:
        """检查应用是否就绪"""
        return self.startup_completed and self.startup_success


# 全局应用启动管理器实例
app_startup = ApplicationStartup()
