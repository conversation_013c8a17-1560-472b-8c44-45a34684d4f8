/**
 * 用户反馈系统类型定义
 * 包含Toast、Notification、Modal、Progress等反馈组件的类型
 */

// ==================== 基础反馈类型 ====================

/**
 * 反馈类型枚举
 */
export enum FeedbackType {
  TOAST = 'toast',
  NOTIFICATION = 'notification',
  MODAL = 'modal',
  PROGRESS = 'progress',
  SKELETON = 'skeleton',
  LOADING = 'loading'
}

/**
 * 反馈级别
 */
export enum FeedbackLevel {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error'
}

/**
 * 反馈位置
 */
export enum FeedbackPosition {
  TOP_LEFT = 'top-left',
  TOP_CENTER = 'top-center',
  TOP_RIGHT = 'top-right',
  BOTTOM_LEFT = 'bottom-left',
  BOTTOM_CENTER = 'bottom-center',
  BOTTOM_RIGHT = 'bottom-right',
  CENTER = 'center'
}

// ==================== Toast 类型 ====================

/**
 * Toast 配置选项
 */
export interface ToastOptions {
  type?: FeedbackLevel
  message: string
  duration?: number
  position?: FeedbackPosition
  showClose?: boolean
  onClose?: () => void
  customClass?: string
  icon?: string
  html?: boolean
}

/**
 * Toast 实例
 */
export interface ToastInstance {
  id: string
  options: ToastOptions
  timestamp: number
  close: () => void
}

// ==================== Notification 类型 ====================

/**
 * Notification 配置选项
 */
export interface NotificationOptions {
  type?: FeedbackLevel
  title: string
  message?: string
  duration?: number
  position?: FeedbackPosition
  showClose?: boolean
  persistent?: boolean
  actions?: NotificationAction[]
  onClose?: () => void
  onClick?: () => void
  customClass?: string
  icon?: string
  html?: boolean
}

/**
 * Notification 操作按钮
 */
export interface NotificationAction {
  label: string
  action: () => void
  type?: 'primary' | 'secondary' | 'danger'
  loading?: boolean
}

/**
 * Notification 实例
 */
export interface NotificationInstance {
  id: string
  options: NotificationOptions
  timestamp: number
  close: () => void
  update: (options: Partial<NotificationOptions>) => void
}

// ==================== Modal 类型 ====================

/**
 * Modal 配置选项
 */
export interface ModalOptions {
  title?: string
  content?: string
  type?: FeedbackLevel
  width?: string | number
  height?: string | number
  closable?: boolean
  maskClosable?: boolean
  keyboard?: boolean
  centered?: boolean
  zIndex?: number
  customClass?: string
  onOpen?: () => void
  onClose?: () => void
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
}

/**
 * 确认对话框选项
 */
export interface ConfirmOptions extends ModalOptions {
  confirmText?: string
  cancelText?: string
  confirmType?: 'primary' | 'danger'
  showCancel?: boolean
  loading?: boolean
}

/**
 * Modal 实例
 */
export interface ModalInstance {
  id: string
  options: ModalOptions
  visible: boolean
  loading: boolean
  open: () => void
  close: () => void
  confirm: () => Promise<void>
  cancel: () => void
  update: (options: Partial<ModalOptions>) => void
}

// ==================== Progress 类型 ====================

/**
 * Progress 配置选项
 */
export interface ProgressOptions {
  type?: 'line' | 'circle' | 'dashboard'
  percentage?: number
  status?: '' | 'success' | 'warning' | 'exception'
  strokeWidth?: number
  textInside?: boolean
  width?: number
  showText?: boolean
  color?: string | string[]
  format?: (percentage: number) => string
}

/**
 * Progress 实例
 */
export interface ProgressInstance {
  id: string
  options: ProgressOptions
  update: (percentage: number, status?: ProgressOptions['status']) => void
  finish: () => void
  error: () => void
}

// ==================== Loading 类型 ====================

/**
 * Loading 配置选项
 */
export interface LoadingOptions {
  target?: string | HTMLElement
  body?: boolean
  fullscreen?: boolean
  lock?: boolean
  text?: string
  spinner?: string
  background?: string
  customClass?: string
  svg?: string
  svgViewBox?: string
}

/**
 * Loading 实例
 */
export interface LoadingInstance {
  id: string
  options: LoadingOptions
  visible: boolean
  show: () => void
  hide: () => void
  setText: (text: string) => void
}

// ==================== Skeleton 类型 ====================

/**
 * Skeleton 配置选项
 */
export interface SkeletonOptions {
  rows?: number
  animated?: boolean
  avatar?: boolean
  avatarSize?: 'small' | 'medium' | 'large'
  avatarShape?: 'circle' | 'square'
  title?: boolean
  titleWidth?: string | number
  paragraph?: boolean
  paragraphRows?: number
  paragraphWidth?: string | number | (string | number)[]
  loading?: boolean
}

// ==================== 反馈管理器类型 ====================

/**
 * 反馈管理器配置
 */
export interface FeedbackManagerConfig {
  maxToasts?: number
  maxNotifications?: number
  defaultToastDuration?: number
  defaultNotificationDuration?: number
  defaultPosition?: FeedbackPosition
  enableQueue?: boolean
  queueSize?: number
}

/**
 * 反馈管理器状态
 */
export interface FeedbackManagerState {
  toasts: ToastInstance[]
  notifications: NotificationInstance[]
  modals: ModalInstance[]
  loadings: LoadingInstance[]
  progresses: ProgressInstance[]
}

/**
 * 反馈管理器接口
 */
export interface FeedbackManager {
  // Toast 方法
  toast: (options: ToastOptions) => ToastInstance
  success: (message: string, options?: Partial<ToastOptions>) => ToastInstance
  error: (message: string, options?: Partial<ToastOptions>) => ToastInstance
  warning: (message: string, options?: Partial<ToastOptions>) => ToastInstance
  info: (message: string, options?: Partial<ToastOptions>) => ToastInstance

  // Notification 方法
  notify: (options: NotificationOptions) => NotificationInstance
  notifySuccess: (title: string, message?: string, options?: Partial<NotificationOptions>) => NotificationInstance
  notifyError: (title: string, message?: string, options?: Partial<NotificationOptions>) => NotificationInstance
  notifyWarning: (title: string, message?: string, options?: Partial<NotificationOptions>) => NotificationInstance
  notifyInfo: (title: string, message?: string, options?: Partial<NotificationOptions>) => NotificationInstance

  // Modal 方法
  modal: (options: ModalOptions) => ModalInstance
  confirm: (options: ConfirmOptions) => Promise<boolean>
  alert: (message: string, title?: string, options?: Partial<ModalOptions>) => Promise<void>

  // Loading 方法
  loading: (options?: LoadingOptions) => LoadingInstance
  showLoading: (text?: string, options?: LoadingOptions) => LoadingInstance
  hideLoading: (instance?: LoadingInstance) => void

  // Progress 方法
  progress: (options?: ProgressOptions) => ProgressInstance

  // 管理方法
  clear: (type?: FeedbackType) => void
  clearAll: () => void
  getState: () => FeedbackManagerState
}

// ==================== 事件类型 ====================

/**
 * 反馈事件类型
 */
export enum FeedbackEventType {
  TOAST_SHOW = 'toast:show',
  TOAST_HIDE = 'toast:hide',
  NOTIFICATION_SHOW = 'notification:show',
  NOTIFICATION_HIDE = 'notification:hide',
  MODAL_OPEN = 'modal:open',
  MODAL_CLOSE = 'modal:close',
  LOADING_SHOW = 'loading:show',
  LOADING_HIDE = 'loading:hide'
}

/**
 * 反馈事件数据
 */
export interface FeedbackEvent {
  type: FeedbackEventType
  instance: ToastInstance | NotificationInstance | ModalInstance | LoadingInstance
  timestamp: number
}
