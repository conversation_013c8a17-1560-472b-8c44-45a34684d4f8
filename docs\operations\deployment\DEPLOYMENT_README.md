# 规则验证系统部署指南

## 📋 概述

本系统采用主从架构，支持独立部署：
- **主节点**：包含后端服务 + 前端界面，部署在公司服务器
- **从节点**：仅包含后端服务，部署在医院内网

## 🏗️ 部署架构

```
公司服务器                    医院内网
┌─────────────────┐          ┌─────────────────┐
│   主节点部署     │          │   从节点部署     │
│                 │          │                 │
│ ┌─────────────┐ │   HTTP   │ ┌─────────────┐ │
│ │ 前端服务    │ │ ◄──────► │ │ 从节点后端  │ │
│ │ (Vue3+Nginx)│ │   同步   │ │ (FastAPI)   │ │
│ └─────────────┘ │          │ └─────────────┘ │
│ ┌─────────────┐ │          │                 │
│ │ 主节点后端  │ │          │                 │
│ │ (FastAPI)   │ │          │                 │
│ └─────────────┘ │          │                 │
└─────────────────┘          └─────────────────┘
        │                            │
        ▼                            ▼
┌─────────────────┐          ┌─────────────────┐
│   公司数据库     │          │   医院数据库     │
│  (运维统一管理)  │          │  (运维统一管理)  │
└─────────────────┘          └─────────────────┘
```

## 🚀 快速部署

### 1. 主节点部署（公司服务器）

```powershell
# 1. 配置环境变量
Copy-Item .env.master.example .env.master
# 编辑 .env.master 配置实际参数

# 2. 启动主节点服务
.\scripts\deploy.ps1 master start -Pull

# 3. 验证部署
.\scripts\deploy.ps1 master status
```

### 2. 从节点部署（医院内网）

```powershell
# 1. 配置环境变量
Copy-Item .env.slave.example .env.slave
# 编辑 .env.slave 配置实际参数

# 2. 启动从节点服务
.\scripts\deploy.ps1 slave start -Pull

# 3. 验证部署
.\scripts\deploy.ps1 slave status
```

## 📁 文件说明

### Docker Compose 配置
- `docker-compose.master.yml` - 主节点配置（后端+前端）
- `docker-compose.slave.yml` - 从节点配置（仅后端）

### 环境变量配置
- `.env.master.example` - 主节点环境变量模板
- `.env.slave.example` - 从节点环境变量模板

### 部署脚本
- `scripts/deploy.ps1` - 统一部署脚本

## ⚙️ 配置说明

### 主节点关键配置

| 配置项 | 说明 | 示例 |
|--------|------|------|
| `REGISTRY_URL` | 镜像仓库地址 | `registry.company.com` |
| `DATABASE_URL` | 数据库连接 | `mysql+pymysql://user:pass@host:3306/db` |
| `MASTER_API_SECRET_KEY` | API密钥 | `your-secret-key` |
| `MASTER_PORT` | 后端端口 | `18001` |
| `FRONTEND_PORT` | 前端端口 | `3000` |

### 从节点关键配置

| 配置项 | 说明 | 示例 |
|--------|------|------|
| `MASTER_API_ENDPOINT` | 主节点地址 | `http://master-server:18001` |
| `SLAVE_API_KEY` | 从节点密钥 | `your-slave-key` |
| `ENABLE_RULE_SYNC` | 规则同步开关 | `true` |
| `SLAVE_PORT` | 服务端口 | `18002` |

## 🔧 常用操作

### 服务管理

```powershell
# 启动服务
.\scripts\deploy.ps1 master start
.\scripts\deploy.ps1 slave start

# 停止服务
.\scripts\deploy.ps1 master stop
.\scripts\deploy.ps1 slave stop

# 重启服务
.\scripts\deploy.ps1 master restart
.\scripts\deploy.ps1 slave restart

# 查看状态
.\scripts\deploy.ps1 master status
.\scripts\deploy.ps1 slave status

# 查看日志
.\scripts\deploy.ps1 master logs
.\scripts\deploy.ps1 slave logs

# 更新镜像
.\scripts\deploy.ps1 master pull
.\scripts\deploy.ps1 slave pull
```

### 镜像管理

```powershell
# 拉取最新镜像
docker pull registry.company.com/rule-master:latest
docker pull registry.company.com/rule-slave:latest
docker pull registry.company.com/rule-frontend:latest

# 查看本地镜像
docker images | findstr rule-

# 清理旧镜像
docker image prune -f
```

## 🔍 健康检查

### 服务健康状态

```powershell
# 检查容器状态
docker ps --filter "label=service.type"

# 检查健康状态
docker inspect rule-master | findstr Health
docker inspect rule-slave | findstr Health

# API健康检查
curl http://localhost:18001/health  # 主节点
curl http://localhost:18002/health  # 从节点
```

### 服务访问地址

- **主节点后端**: `http://localhost:18001`
- **前端界面**: `http://localhost:3000`
- **从节点后端**: `http://localhost:18002`

## 🛠️ 故障排除

### 常见问题

1. **镜像拉取失败**
   ```powershell
   # 检查镜像仓库连接
   docker login registry.company.com
   
   # 手动拉取镜像
   docker pull registry.company.com/rule-master:latest
   ```

2. **服务启动失败**
   ```powershell
   # 查看详细日志
   .\scripts\deploy.ps1 master logs
   
   # 检查配置文件
   docker-compose -f docker-compose.master.yml config
   ```

3. **网络连接问题**
   ```powershell
   # 检查网络配置
   docker network ls
   docker network inspect rule-master-network
   ```

### 日志位置

- **应用日志**: `./logs/`
- **Nginx日志**: `./logs/nginx/`
- **Docker日志**: `docker logs <container_name>`

## 📊 监控建议

### 关键指标监控

1. **服务可用性**
   - 容器运行状态
   - 健康检查状态
   - API响应时间

2. **性能指标**
   - CPU使用率
   - 内存使用率
   - 规则验证响应时间

3. **业务指标**
   - 规则同步状态
   - 验证请求量
   - 错误率统计

### 告警配置

建议配置以下告警：
- 服务不可用告警
- 高CPU/内存使用率告警
- 规则同步失败告警
- API响应时间过长告警

## 🔄 更新流程

### 版本更新

1. **拉取新版本镜像**
   ```powershell
   .\scripts\deploy.ps1 master pull
   ```

2. **滚动更新**
   ```powershell
   .\scripts\deploy.ps1 master restart
   ```

3. **验证更新**
   ```powershell
   .\scripts\deploy.ps1 master status
   ```

### 配置更新

1. **修改环境变量文件**
2. **重启服务应用配置**
   ```powershell
   .\scripts\deploy.ps1 master restart
   ```

---

**联系方式**
- 技术支持：请联系系统管理员
- 文档更新：2025-07-03
