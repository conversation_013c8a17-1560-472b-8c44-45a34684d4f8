#!/usr/bin/env python3
"""
检查数据库表结构工具
用于验证 rule_details 表是否存在以及表结构
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text

from config.settings import settings


def main():
    """主函数"""
    print("=" * 60)
    print("数据库表结构检查工具")
    print("=" * 60)

    # 获取数据库连接
    database_url = settings.get_database_url()
    if not database_url:
        print("❌ 无法获取数据库连接URL")
        return 1

    try:
        engine = create_engine(database_url)

        with engine.connect() as conn:
            # 1. 检查所有表
            print("\n1. 数据库中的所有表:")
            print("-" * 40)
            result = conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result]
            for table in sorted(tables):
                print(f"  - {table}")

            # 2. 检查 rule_details 表是否存在
            print("\n2. rule_details 表检查:")
            print("-" * 40)
            if "rule_details" in tables:
                print("  ✅ rule_details 表存在")

                # 显示表结构
                print("\n3. rule_details 表结构:")
                print("-" * 40)
                result = conn.execute(text("DESCRIBE rule_details"))
                for row in result:
                    field, type_, null, key, default, extra = row
                    print(f"  {field:<25} {type_:<20} {null:<5} {key:<5} {str(default):<10} {extra}")

                # 显示索引
                print("\n4. rule_details 表索引:")
                print("-" * 40)
                result = conn.execute(text("SHOW INDEX FROM rule_details"))
                for row in result:
                    table, non_unique, key_name, seq_in_index, column_name = row[:5]
                    print(f"  {key_name:<30} {column_name:<20} {'UNIQUE' if non_unique == 0 else 'NON-UNIQUE'}")

                # 检查数据量
                print("\n5. rule_details 表数据统计:")
                print("-" * 40)
                result = conn.execute(text("SELECT COUNT(*) FROM rule_details"))
                count = result.scalar()
                print(f"  总记录数: {count}")

            else:
                print("  ❌ rule_details 表不存在")

            # 3. 检查 rule_data_sets 表的迁移字段
            print("\n6. rule_data_sets 表迁移字段检查:")
            print("-" * 40)
            if "rule_data_sets" in tables:
                result = conn.execute(text("DESCRIBE rule_data_sets"))
                fields = [row[0] for row in result]

                migration_fields = ["migration_status", "migration_timestamp", "updated_at"]
                for field in migration_fields:
                    if field in fields:
                        print(f"  ✅ {field} 字段存在")
                    else:
                        print(f"  ❌ {field} 字段不存在")
            else:
                print("  ❌ rule_data_sets 表不存在")

    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return 1

    print("\n" + "=" * 60)
    print("表结构检查完成")
    print("=" * 60)
    return 0


if __name__ == "__main__":
    sys.exit(main())
