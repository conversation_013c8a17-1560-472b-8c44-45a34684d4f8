"""
主从节点一致性测试
验证从节点索引构建结果与主节点完全一致
"""

import json
import gzip
import os
import tempfile
import unittest
from unittest.mock import Mock, patch

from core.slave_node_index_builder import SlaveNodeIndexBuilder


class TestMasterSlaveConsistency(unittest.TestCase):
    """主从节点一致性测试"""

    def setUp(self):
        """设置测试环境"""
        # 模拟环境变量
        self.env_patcher = patch.dict(
            "os.environ",
            {
                "SLAVE_INDEX_MEMORY_LIMIT_MB": "300",
                "SLAVE_INDEX_GC_THRESHOLD": "500",
            },
        )
        self.env_patcher.start()

        # 创建临时缓存文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json.gz', delete=False)
        self.temp_file.close()

        # 模拟rule_index_manager
        self.mock_rule_index_manager = Mock()
        
        # 创建一致性测试数据
        self.test_rules_data = self._create_consistency_test_data()
        self._write_test_cache_file()

    def tearDown(self):
        """清理测试环境"""
        self.env_patcher.stop()
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def _create_consistency_test_data(self):
        """创建一致性测试数据"""
        rules = []
        
        # 创建各种类型的规则数据以测试一致性
        test_cases = [
            # 基本规则
            {
                "rule_key": "BASIC_RULE_001",
                "rule_name": "基本规则",
                "rule_type": "VALIDATION",
                "yb_code": "Y001",
                "diag_code": "D001",
                "fee_code": "F001",
                "rule_content": "基本规则内容",
                "is_active": True,
                "priority": 1,
                "extended_fields": "{}"
            },
            # 包含特殊字符的规则
            {
                "rule_key": "SPECIAL_CHAR_RULE_002",
                "rule_name": "特殊字符规则：测试\"引号\"和'单引号'",
                "rule_type": "VALIDATION",
                "yb_code": "Y002",
                "diag_code": "D002",
                "fee_code": "F002",
                "rule_content": "包含特殊字符：@#$%^&*()_+-={}[]|\\:;\"'<>?,./",
                "is_active": True,
                "priority": 2,
                "extended_fields": json.dumps({"special": "value with \"quotes\""})
            },
            # 长文本规则
            {
                "rule_key": "LONG_TEXT_RULE_003",
                "rule_name": "长文本规则",
                "rule_type": "VALIDATION",
                "yb_code": "Y003",
                "diag_code": "D003",
                "fee_code": "F003",
                "rule_content": "这是一个很长的规则内容" * 100,
                "is_active": True,
                "priority": 3,
                "extended_fields": json.dumps({"long_field": "x" * 1000})
            },
            # Unicode字符规则
            {
                "rule_key": "UNICODE_RULE_004",
                "rule_name": "Unicode规则：中文🔥emoji😀测试",
                "rule_type": "VALIDATION",
                "yb_code": "Y004",
                "diag_code": "D004",
                "fee_code": "F004",
                "rule_content": "包含Unicode字符：中文、日文（こんにちは）、emoji（🎉🚀💯）",
                "is_active": True,
                "priority": 4,
                "extended_fields": json.dumps({"unicode": "测试🔥emoji😀"})
            },
            # 空值和None测试
            {
                "rule_key": "NULL_TEST_RULE_005",
                "rule_name": "空值测试规则",
                "rule_type": "VALIDATION",
                "yb_code": "",
                "diag_code": None,
                "fee_code": "F005",
                "rule_content": "",
                "is_active": False,
                "priority": 0,
                "extended_fields": None
            },
            # 复杂JSON结构
            {
                "rule_key": "COMPLEX_JSON_RULE_006",
                "rule_name": "复杂JSON规则",
                "rule_type": "VALIDATION",
                "yb_code": "Y006",
                "diag_code": "D006",
                "fee_code": "F006",
                "rule_content": "复杂JSON结构测试",
                "is_active": True,
                "priority": 5,
                "extended_fields": json.dumps({
                    "nested": {
                        "array": [1, 2, 3, {"inner": "value"}],
                        "boolean": True,
                        "null_value": None,
                        "number": 123.456
                    }
                })
            }
        ]
        
        # 添加大量相似规则以测试批处理一致性
        for i in range(100):
            rule = {
                "rule_key": f"BATCH_RULE_{i:03d}",
                "rule_name": f"批处理规则{i}",
                "rule_type": "VALIDATION",
                "yb_code": f"Y{i:03d}",
                "diag_code": f"D{i:03d}",
                "fee_code": f"F{i:03d}",
                "rule_content": f"批处理规则内容{i}",
                "is_active": i % 2 == 0,
                "priority": i % 10,
                "extended_fields": json.dumps({"batch_id": i, "group": i // 10})
            }
            test_cases.append(rule)
        
        return test_cases

    def _write_test_cache_file(self):
        """写入测试缓存文件"""
        cache_data = {
            "rule_details": self.test_rules_data,
            "metadata": {
                "total_count": len(self.test_rules_data),
                "generated_time": 1234567890.123,  # 固定时间戳确保一致性
                "version": "1.0"
            }
        }
        
        with gzip.open(self.temp_file.name, 'wt', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, sort_keys=True)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_rule_data_parsing_consistency(self, mock_memory_optimizer_class):
        """测试规则数据解析一致性"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        
        # 加载规则数据
        rule_details = builder._load_rule_details_from_cache()
        
        # 验证数据完整性
        self.assertEqual(len(rule_details), len(self.test_rules_data))
        
        # 验证特殊字符处理
        special_char_rule = next((r for r in rule_details if r.rule_key == "SPECIAL_CHAR_RULE_002"), None)
        self.assertIsNotNone(special_char_rule)
        self.assertIn("引号", special_char_rule.rule_name)
        self.assertIn("@#$%", special_char_rule.rule_content)
        
        # 验证Unicode字符处理
        unicode_rule = next((r for r in rule_details if r.rule_key == "UNICODE_RULE_004"), None)
        self.assertIsNotNone(unicode_rule)
        self.assertIn("🔥", unicode_rule.rule_name)
        self.assertIn("emoji", unicode_rule.rule_content)
        
        # 验证空值处理
        null_test_rule = next((r for r in rule_details if r.rule_key == "NULL_TEST_RULE_005"), None)
        self.assertIsNotNone(null_test_rule)
        self.assertEqual(null_test_rule.yb_code, "")
        self.assertEqual(null_test_rule.diag_code, "")  # None应该转换为空字符串
        self.assertFalse(null_test_rule.is_active)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_index_building_consistency(self, mock_memory_optimizer_class):
        """测试索引构建一致性"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        # 记录传递给索引管理器的规则数据
        captured_rule_details = []
        
        def capture_rule_details(rule_details):
            captured_rule_details.extend(rule_details)
        
        self.mock_rule_index_manager.build_indexes_from_rule_details.side_effect = capture_rule_details

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        
        # 执行索引构建
        success = builder.build_index_from_cache_file()
        self.assertTrue(success)
        
        # 验证所有规则都被处理
        self.assertEqual(len(captured_rule_details), len(self.test_rules_data))
        
        # 验证规则顺序一致性（应该按照原始顺序）
        for i, rule_detail in enumerate(captured_rule_details):
            expected_rule_key = self.test_rules_data[i]["rule_key"]
            self.assertEqual(rule_detail.rule_key, expected_rule_key)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_batch_processing_consistency(self, mock_memory_optimizer_class):
        """测试批处理一致性"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        
        # 测试不同批次大小的一致性
        batch_sizes = [10, 50, 100, 500]
        results = []
        
        for batch_size in batch_sizes:
            # 重置mock
            self.mock_rule_index_manager.reset_mock()
            captured_rules = []
            
            def capture_rules(rule_details):
                captured_rules.extend(rule_details)
            
            self.mock_rule_index_manager.build_indexes_from_rule_details.side_effect = capture_rules
            
            # 设置批次大小
            builder._gc_threshold = batch_size
            
            # 执行构建
            success = builder.build_index_from_cache_file()
            self.assertTrue(success)
            
            # 记录结果
            results.append({
                "batch_size": batch_size,
                "rule_count": len(captured_rules),
                "rule_keys": [r.rule_key for r in captured_rules]
            })
        
        # 验证不同批次大小产生相同结果
        base_result = results[0]
        for result in results[1:]:
            self.assertEqual(result["rule_count"], base_result["rule_count"])
            self.assertEqual(result["rule_keys"], base_result["rule_keys"])

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_data_type_consistency(self, mock_memory_optimizer_class):
        """测试数据类型一致性"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        
        # 加载规则数据
        rule_details = builder._load_rule_details_from_cache()
        
        # 验证数据类型一致性
        for rule_detail in rule_details:
            # 字符串字段
            self.assertIsInstance(rule_detail.rule_key, str)
            self.assertIsInstance(rule_detail.rule_name, str)
            self.assertIsInstance(rule_detail.rule_type, str)
            self.assertIsInstance(rule_detail.rule_content, str)
            
            # 可选字符串字段（可能为空但应该是字符串）
            self.assertIsInstance(rule_detail.yb_code, str)
            self.assertIsInstance(rule_detail.diag_code, str)
            self.assertIsInstance(rule_detail.fee_code, str)
            
            # 布尔字段
            self.assertIsInstance(rule_detail.is_active, bool)
            
            # 整数字段
            self.assertIsInstance(rule_detail.priority, int)
            
            # JSON字段（应该是字符串）
            self.assertIsInstance(rule_detail.extended_fields, str)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_error_handling_consistency(self, mock_memory_optimizer_class):
        """测试错误处理一致性"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        
        # 模拟索引构建失败
        self.mock_rule_index_manager.build_indexes_from_rule_details.side_effect = Exception("构建失败")
        
        # 执行构建（应该失败）
        success = builder.build_index_from_cache_file()
        self.assertFalse(success)
        
        # 验证错误处理的一致性
        error_stats = builder.get_error_handler_status()
        self.assertGreater(error_stats["total_errors"], 0)
        
        # 验证性能统计的一致性
        perf_stats = builder.get_performance_stats()
        self.assertEqual(perf_stats.failed_builds, 1)
        self.assertGreater(perf_stats.build_errors, 0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_repeated_operations_consistency(self, mock_memory_optimizer_class):
        """测试重复操作一致性"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        
        # 执行多次相同操作
        results = []
        for i in range(5):
            # 重置mock以捕获每次调用
            self.mock_rule_index_manager.reset_mock()
            captured_rules = []
            
            def capture_rules(rule_details):
                captured_rules.extend(rule_details)
            
            self.mock_rule_index_manager.build_indexes_from_rule_details.side_effect = capture_rules
            
            # 执行构建
            success = builder.build_index_from_cache_file()
            self.assertTrue(success)
            
            # 记录结果
            results.append([r.rule_key for r in captured_rules])
        
        # 验证每次操作结果完全一致
        base_result = results[0]
        for result in results[1:]:
            self.assertEqual(result, base_result, "重复操作结果不一致")


if __name__ == "__main__":
    unittest.main()
