<template>
  <div class="task-status-tracker">
    <!-- 无任务信息时的显示 -->
    <div v-if="!taskInfo" class="no-task-info">
      <el-empty description="暂无任务信息" />
    </div>

    <!-- 任务状态卡片 -->
    <el-card v-else shadow="hover" class="status-card">
      <template #header>
        <div class="card-header">
          <div class="task-info">
            <el-icon :size="20" :class="statusIconClass">
              <component :is="statusIcon" />
            </el-icon>
            <span class="task-title">{{ taskTitle }}</span>
          </div>
          <div class="task-actions">
            <el-button
              :icon="Refresh"
              circle
              size="small"
              :loading="refreshing"
              @click="handleRefresh"
              title="刷新状态"
            />
            <el-button
              v-if="canCancel"
              :icon="Close"
              circle
              size="small"
              type="danger"
              @click="handleCancel"
              title="取消任务"
            />
          </div>
        </div>
      </template>

      <!-- 任务状态内容 -->
      <div class="status-content">
        <!-- 状态标签 -->
        <div class="status-badge">
          <el-tag
            :type="statusTagType"
            size="large"
            effect="dark"
            class="status-tag"
          >
            {{ statusText }}
          </el-tag>
          <span class="status-time">{{ formatTime(taskInfo?.created_at) }}</span>
        </div>

        <!-- 进度条 -->
        <div v-if="showProgress" class="progress-section">
          <el-progress
            :percentage="progressPercentage"
            :status="progressStatus"
            :stroke-width="12"
            :show-text="true"
            class="progress-bar"
          />
          <div class="progress-details">
            <span class="progress-message">{{ taskInfo?.current_message || '正在处理...' }}</span>
            <span class="progress-stats">
              {{ taskInfo?.completed_operations || 0 }} / {{ taskInfo?.total_operations || 0 }} 操作
            </span>
          </div>
        </div>

        <!-- 任务统计信息 -->
        <div v-if="taskInfo?.stats" class="stats-section">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">删除操作</span>
              <span class="stat-value">{{ taskInfo?.stats?.delete_operations || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">更新操作</span>
              <span class="stat-value">{{ taskInfo?.stats?.upsert_operations || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">成功操作</span>
              <span class="stat-value success">{{ taskInfo?.stats?.successful_operations || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">失败操作</span>
              <span class="stat-value error">{{ taskInfo?.stats?.failed_operations || 0 }}</span>
            </div>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="hasError" class="error-section">
          <el-alert
            :title="errorTitle"
            :description="taskInfo?.error_message"
            type="error"
            show-icon
            :closable="false"
          />
        </div>

        <!-- 执行时间 -->
        <div v-if="taskInfo?.execution_time" class="time-section">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="开始时间">
              {{ formatTime(taskInfo?.started_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="执行时间">
              {{ formatDuration(taskInfo?.execution_time) }}
            </el-descriptions-item>
            <el-descriptions-item v-if="taskInfo?.completed_at" label="完成时间">
              {{ formatTime(taskInfo?.completed_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="任务ID">
              <el-text type="info" size="small" class="task-id">{{ taskInfo?.task_id }}</el-text>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>

    <!-- 自动刷新控制 -->
    <div v-if="showAutoRefresh" class="auto-refresh-control">
      <el-switch
        v-model="autoRefreshEnabled"
        @change="toggleAutoRefresh"
        active-text="自动刷新"
        inactive-text=""
        size="small"
      />
      <span class="refresh-interval">每 {{ autoRefreshInterval / 1000 }}s</span>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import {
  Refresh,
  Close,
  Loading,
  CircleCheck,
  CircleCloseFilled,
  Clock,
  Warning
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  // 任务信息
  taskInfo: {
    type: Object,
    required: false,
    default: null,
    validator: (value) => {
      // 允许null值或包含status属性的对象
      return value === null || (typeof value === 'object' && value !== null)
    }
  },
  // 任务标题
  taskTitle: {
    type: String,
    default: '规则注册任务'
  },
  // 是否显示进度条
  showProgress: {
    type: Boolean,
    default: true
  },
  // 是否显示自动刷新控制
  showAutoRefresh: {
    type: Boolean,
    default: true
  },
  // 自动刷新间隔（毫秒）
  autoRefreshInterval: {
    type: Number,
    default: 3000
  },
  // 是否正在刷新
  refreshing: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['refresh', 'cancel'])

// 响应式数据
const autoRefreshEnabled = ref(false)
let autoRefreshTimer = null

// 计算属性
const statusIcon = computed(() => {
  const status = props.taskInfo?.status
  switch (status) {
    case 'pending': return Clock
    case 'running': return Loading
    case 'completed': return CircleCheck
    case 'failed': return CircleCloseFilled
    case 'cancelled': return Close
    default: return Clock
  }
})

const statusIconClass = computed(() => {
  const status = props.taskInfo?.status
  return {
    'status-icon': true,
    'status-pending': status === 'pending',
    'status-running': status === 'running',
    'status-completed': status === 'completed',
    'status-failed': status === 'failed',
    'status-cancelled': status === 'cancelled'
  }
})

const statusText = computed(() => {
  if (!props.taskInfo) return '暂无任务信息'
  const statusMap = {
    'pending': '等待中',
    'running': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return statusMap[props.taskInfo.status] || '未知状态'
})

const statusTagType = computed(() => {
  const status = props.taskInfo?.status
  switch (status) {
    case 'pending': return 'info'
    case 'running': return 'warning'
    case 'completed': return 'success'
    case 'failed': return 'danger'
    case 'cancelled': return 'info'
    default: return 'info'
  }
})

const progressPercentage = computed(() => {
  return Math.round(props.taskInfo?.progress_percentage || 0)
})

const progressStatus = computed(() => {
  const status = props.taskInfo?.status
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  if (status === 'running') return ''
  return ''
})

const canCancel = computed(() => {
  return props.taskInfo && ['pending', 'running'].includes(props.taskInfo.status)
})

const hasError = computed(() => {
  return props.taskInfo && props.taskInfo.status === 'failed' && props.taskInfo.error_message
})

const errorTitle = computed(() => {
  return '任务执行失败'
})

// 方法
const handleRefresh = () => {
  emit('refresh')
}

const handleCancel = () => {
  emit('cancel')
}

const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (autoRefreshTimer) return

  autoRefreshTimer = setInterval(() => {
    if (props.taskInfo && !['completed', 'failed', 'cancelled'].includes(props.taskInfo.status)) {
      emit('refresh')
    } else {
      // 任务已结束，停止自动刷新
      autoRefreshEnabled.value = false
      stopAutoRefresh()
    }
  }, props.autoRefreshInterval)
}

const stopAutoRefresh = () => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer)
    autoRefreshTimer = null
  }
}

const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatDuration = (seconds) => {
  if (!seconds) return '-'
  if (seconds < 60) return `${seconds.toFixed(1)}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}分${remainingSeconds}秒`
}

// 生命周期
onMounted(() => {
  // 如果任务正在运行，自动开启自动刷新
  if (props.taskInfo && ['pending', 'running'].includes(props.taskInfo.status)) {
    autoRefreshEnabled.value = true
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.task-status-tracker {
  width: 100%;
}

.no-task-info {
  padding: 40px 20px;
  text-align: center;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px dashed var(--el-border-color);
}

.status-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.task-actions {
  display: flex;
  gap: 8px;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-badge {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.progress-message {
  color: var(--el-text-color-primary);
}

.progress-stats {
  color: var(--el-text-color-secondary);
}

.stats-section {
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.stat-value.success {
  color: var(--el-color-success);
}

.stat-value.error {
  color: var(--el-color-danger);
}

.error-section {
  margin-top: 8px;
}

.time-section {
  margin-top: 8px;
}

.task-id {
  font-family: monospace;
  font-size: 11px;
}

.auto-refresh-control {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  font-size: 12px;
}

.refresh-interval {
  color: var(--el-text-color-secondary);
}

/* 状态图标样式 */
.status-icon {
  transition: all 0.3s ease;
}

.status-pending {
  color: var(--el-color-info);
}

.status-running {
  color: var(--el-color-warning);
  animation: pulse 2s infinite;
}

.status-completed {
  color: var(--el-color-success);
}

.status-failed {
  color: var(--el-color-danger);
}

.status-cancelled {
  color: var(--el-color-info);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .progress-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
