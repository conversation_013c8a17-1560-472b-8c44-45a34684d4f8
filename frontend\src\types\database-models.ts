/**
 * 数据库模型类型定义
 * 基于规则详情表三表结构重构
 *
 * 表结构：
 * - rule_template: 规则模板表
 * - rule_detail: 规则明细表
 * - rule_field_metadata: 字段元数据表
 */

// ==================== 规则模板表 (rule_template) ====================

/**
 * 规则模板状态枚举
 */
export enum RuleTemplateStatus {
  NEW = 'NEW',
  CHANGED = 'CHANGED',
  READY = 'READY',
  DEPRECATED = 'DEPRECATED'
}

/**
 * 规则模板接口
 */
export interface RuleTemplate {
  /** 主键ID */
  id: number

  /** 规则模板类型 */
  rule_key: string

  /** 规则类型 */
  rule_type: string

  /** 规则模板名称 */
  name: string

  /** 规则模板描述 */
  description?: string

  /** Python模块路径 */
  module_path?: string

  /** 文件哈希值 */
  file_hash?: string

  /** 规则模板状态 */
  status: RuleTemplateStatus

  /** 创建时间 */
  created_at: string

  /** 更新时间 */
  updated_at: string
}

/**
 * 规则模板创建数据接口
 */
export interface CreateRuleTemplateData {
  rule_key: string
  rule_type: string
  name: string
  description?: string
  module_path?: string
  file_hash?: string
  status?: RuleTemplateStatus
}

/**
 * 规则模板更新数据接口
 */
export interface UpdateRuleTemplateData {
  rule_type?: string
  name?: string
  description?: string
  module_path?: string
  file_hash?: string
  status?: RuleTemplateStatus
}

// ==================== 字段元数据表 (rule_field_metadata) ====================

/**
 * 字段数据类型枚举
 */
export enum FieldDataType {
  STRING = 'string',
  INTEGER = 'integer',
  ARRAY = 'array',
  BOOLEAN = 'boolean'
}

/**
 * 字段元数据接口
 */
export interface RuleFieldMetadata {
  /** 主键ID */
  id: number

  /** 规则模板类型 */
  rule_key: string

  /** 字段名称 */
  field_name: string

  /** 字段数据类型 */
  field_type: FieldDataType

  /** 是否必填 */
  is_required: boolean

  /** 是否为固定字段 */
  is_fixed_field: boolean

  /** 显示名称 */
  display_name: string

  /** 字段描述 */
  description?: string

  /** 验证规则（JSON格式） */
  validation_rule?: string

  /** 默认值 */
  default_value?: string

  /** Excel列顺序 */
  excel_column_order?: number

  /** 创建时间 */
  created_at: string
}

/**
 * 字段元数据创建数据接口
 */
export interface CreateRuleFieldMetadataData {
  rule_key: string
  field_name: string
  field_type: FieldDataType
  is_required?: boolean
  is_fixed_field?: boolean
  display_name: string
  description?: string
  validation_rule?: string
  default_value?: string
  excel_column_order?: number
}

/**
 * 字段元数据更新数据接口
 */
export interface UpdateRuleFieldMetadataData {
  field_type?: FieldDataType
  is_required?: boolean
  is_fixed_field?: boolean
  display_name?: string
  description?: string
  validation_rule?: string
  default_value?: string
  excel_column_order?: number
}

// ==================== 关联查询类型 ====================

/**
 * 规则模板与字段元数据关联接口
 */
export interface RuleTemplateWithFields extends RuleTemplate {
  /** 关联的字段元数据 */
  field_metadata: RuleFieldMetadata[]
}

/**
 * 规则明细与模板关联接口
 */
export interface RuleDetailWithTemplate {
  /** 规则明细数据 */
  detail: import('./ruleDetails').RuleDetail

  /** 关联的规则模板 */
  template: RuleTemplate

  /** 关联的字段元数据 */
  field_metadata: RuleFieldMetadata[]
}

// ==================== 查询参数类型 ====================

/**
 * 规则模板查询参数接口
 */
export interface RuleTemplateQueryParams {
  page?: number
  page_size?: number
  rule_type?: string
  status?: RuleTemplateStatus
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

/**
 * 字段元数据查询参数接口
 */
export interface RuleFieldMetadataQueryParams {
  rule_key?: string
  field_type?: FieldDataType
  is_required?: boolean
  is_fixed_field?: boolean
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// ==================== API响应类型 ====================

/**
 * 规则模板列表响应接口
 */
export interface RuleTemplateListResponse {
  items: RuleTemplate[]
  pagination: import('./ruleDetails').PaginationInfo
}

/**
 * 字段元数据列表响应接口
 */
export interface RuleFieldMetadataListResponse {
  items: RuleFieldMetadata[]
  pagination: import('./ruleDetails').PaginationInfo
}

// ==================== 统计数据类型 ====================

/**
 * 规则模板统计数据接口
 */
export interface RuleTemplateStats {
  total: number
  by_status: Record<RuleTemplateStatus, number>
  by_rule_type: Record<string, number>
  recent_activity: {
    created_today: number
    updated_today: number
  }
}

/**
 * 字段元数据统计数据接口
 */
export interface RuleFieldMetadataStats {
  total: number
  by_rule_key: Record<string, number>
  by_field_type: Record<FieldDataType, number>
  required_fields: number
  fixed_fields: number
}

// ==================== 工具函数类型 ====================

/**
 * 获取规则模板状态的中文名称
 */
export function getRuleTemplateStatusChineseName(status: RuleTemplateStatus): string {
  const statusNames = {
    [RuleTemplateStatus.NEW]: '新建',
    [RuleTemplateStatus.CHANGED]: '已变更',
    [RuleTemplateStatus.READY]: '就绪',
    [RuleTemplateStatus.DEPRECATED]: '已废弃'
  }
  return statusNames[status] || status
}

/**
 * 获取字段数据类型的中文名称
 */
export function getFieldDataTypeChineseName(type: FieldDataType): string {
  const typeNames = {
    [FieldDataType.STRING]: '字符串',
    [FieldDataType.INTEGER]: '整数',
    [FieldDataType.ARRAY]: '数组',
    [FieldDataType.BOOLEAN]: '布尔值'
  }
  return typeNames[type] || type
}

/**
 * 检查规则模板是否可编辑
 */
export function isRuleTemplateEditable(template: RuleTemplate): boolean {
  return template.status !== RuleTemplateStatus.DEPRECATED
}
