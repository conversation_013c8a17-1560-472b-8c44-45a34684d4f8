# 降级机制 API 参考文档

## 1. 概述

降级机制提供了完整的REST API接口，用于监控和管理系统的降级状态。API分为两类：
- **监控API**：用于查询状态和指标，无需认证
- **管理API**：用于控制降级行为，需要API密钥认证

### 1.1 基础信息
- **Base URL**: `http://your-domain/api/v1/degradation`
- **Content-Type**: `application/json`
- **认证方式**: Bearer <PERSON>ken (仅管理API)

### 1.2 响应格式
所有API响应都遵循统一的格式：

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": { ... },
  "request_id": "req_1234567890"
}
```

## 2. 监控API

### 2.1 获取降级状态

**接口**: `GET /status`

**描述**: 获取当前系统的降级状态信息

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取状态成功",
  "data": {
    "current_level": "normal",
    "previous_level": "light_degradation",
    "is_degraded": false,
    "last_change_time": 1672531200.0,
    "degradation_duration": 0.0,
    "active_triggers": [],
    "is_manual_override": false,
    "override_reason": null,
    "executed_actions_count": 0,
    "enabled": true,
    "running": true
  }
}
```

**字段说明**:
- `current_level`: 当前降级级别 (normal/light_degradation/moderate_degradation/severe_degradation)
- `previous_level`: 上一个降级级别
- `is_degraded`: 是否处于降级状态
- `last_change_time`: 最后状态变更时间戳
- `degradation_duration`: 降级持续时间（秒）
- `active_triggers`: 当前激活的触发器列表
- `is_manual_override`: 是否为手动覆盖状态
- `override_reason`: 手动覆盖原因
- `executed_actions_count`: 已执行的动作数量

### 2.2 获取组件状态

**接口**: `GET /components`

**描述**: 获取所有组件的降级状态

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取组件状态成功",
  "data": [
    {
      "component_name": "DynamicProcessPool",
      "current_level": "normal",
      "is_degraded": false,
      "last_change_time": 1672531200.0,
      "degradation_reason": "",
      "enabled": true,
      "original_config": {
        "max_workers": 8
      },
      "degraded_config": {}
    },
    {
      "component_name": "IntelligentCache",
      "current_level": "light_degradation",
      "is_degraded": true,
      "last_change_time": 1672531100.0,
      "degradation_reason": "CPU使用率过高",
      "enabled": true,
      "original_config": {
        "max_size": 1000
      },
      "degraded_config": {
        "max_size": 750
      }
    }
  ]
}
```

### 2.3 获取统计指标

**接口**: `GET /metrics`

**描述**: 获取降级机制的统计指标

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取指标成功",
  "data": {
    "total_degradations": 25,
    "successful_degradations": 24,
    "failed_degradations": 1,
    "total_recoveries": 20,
    "successful_recoveries": 20,
    "failed_recoveries": 0,
    "total_actions_executed": 75,
    "successful_actions": 73,
    "failed_actions": 2,
    "average_degradation_duration": 120.5,
    "current_uptime": 86400.0,
    "last_degradation_time": 1672531000.0,
    "degradation_success_rate": 0.96,
    "recovery_success_rate": 1.0
  }
}
```

### 2.4 获取事件历史

**接口**: `GET /events`

**描述**: 获取降级事件历史记录

**请求参数**:
- `limit` (可选): 返回记录数量，默认50，最大500
- `start_time` (可选): 开始时间戳
- `end_time` (可选): 结束时间戳
- `event_types` (可选): 事件类型过滤，逗号分隔

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取事件历史成功",
  "data": [
    {
      "event_type": "degradation_triggered",
      "timestamp": 1672531200.0,
      "level": "light_degradation",
      "trigger_type": "cpu_usage",
      "trigger_value": 75.5,
      "metadata": {
        "reason": "CPU使用率超过阈值",
        "threshold": 70.0,
        "duration": 30.0
      },
      "actions_count": 3
    }
  ]
}
```

### 2.5 获取性能影响

**接口**: `GET /performance`

**描述**: 获取降级对系统性能的影响评估

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取性能影响成功",
  "data": {
    "overall_status": "degraded",
    "estimated_performance_reduction": 25.0,
    "components_impact": [
      {
        "component": "DynamicProcessPool",
        "impact_level": "light",
        "performance_reduction": 25.0,
        "description": "工作进程数减少到6个"
      }
    ],
    "recovery_estimate": {
      "estimated_time": 300.0,
      "conditions": ["CPU使用率降至56%以下", "持续60秒"]
    }
  }
}
```

## 3. 管理API

### 3.1 手动触发降级

**接口**: `POST /trigger`

**描述**: 手动触发系统降级

**认证**: 需要Bearer Token

**请求参数**:
```json
{
  "level": "light_degradation",
  "reason": "预防性维护",
  "force": false
}
```

**参数说明**:
- `level`: 目标降级级别 (light_degradation/moderate_degradation/severe_degradation)
- `reason`: 降级原因说明
- `force`: 是否强制执行（忽略当前状态检查）

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "降级触发成功",
  "data": {
    "success": true,
    "current_level": "light_degradation",
    "previous_level": "normal",
    "timestamp": 1672531200.0,
    "executed_actions": 3,
    "affected_components": ["DynamicProcessPool", "IntelligentCache"]
  }
}
```

### 3.2 手动恢复

**接口**: `POST /recover`

**描述**: 手动恢复系统到正常状态

**认证**: 需要Bearer Token

**请求参数**:
- `reason` (查询参数): 恢复原因说明

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "恢复操作成功",
  "data": {
    "success": true,
    "current_level": "normal",
    "previous_level": "light_degradation",
    "timestamp": 1672531300.0,
    "recovery_duration": 100.0,
    "restored_components": ["DynamicProcessPool", "IntelligentCache"]
  }
}
```

### 3.3 设置降级级别

**接口**: `POST /level`

**描述**: 设置系统到指定的降级级别

**认证**: 需要Bearer Token

**请求参数**:
```json
{
  "level": "moderate_degradation",
  "reason": "系统维护",
  "duration": 3600
}
```

**参数说明**:
- `level`: 目标级别 (normal/light_degradation/moderate_degradation/severe_degradation)
- `reason`: 设置原因
- `duration`: 持续时间（秒），0表示永久生效

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "级别设置成功",
  "data": {
    "success": true,
    "current_level": "moderate_degradation",
    "previous_level": "normal",
    "timestamp": 1672531400.0,
    "duration": 3600,
    "auto_recovery_time": 1672535000.0
  }
}
```

## 4. 错误码说明

### 4.1 HTTP状态码
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 4.2 业务错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 4001 | 无效的降级级别 | 检查level参数值 |
| 4002 | 降级原因不能为空 | 提供reason参数 |
| 4003 | 系统未启用降级功能 | 检查配置文件 |
| 4004 | 当前状态不允许此操作 | 检查系统当前状态 |
| 5001 | 降级操作执行失败 | 检查系统日志 |
| 5002 | 组件响应超时 | 检查组件状态 |

### 4.3 错误响应示例
```json
{
  "success": false,
  "code": 4001,
  "message": "无效的降级级别: invalid_level",
  "data": null,
  "request_id": "req_1234567890"
}
```

## 5. 使用示例

### 5.1 Python示例
```python
import requests

# 获取当前状态
response = requests.get('http://localhost:8000/api/v1/degradation/status')
status = response.json()
print(f"当前级别: {status['data']['current_level']}")

# 手动触发降级
headers = {'Authorization': 'Bearer your_api_key'}
data = {
    'level': 'light_degradation',
    'reason': '预防性维护',
    'force': False
}
response = requests.post(
    'http://localhost:8000/api/v1/degradation/trigger',
    json=data,
    headers=headers
)
result = response.json()
print(f"降级结果: {result['data']['success']}")
```

### 5.2 curl示例
```bash
# 获取状态
curl -X GET http://localhost:8000/api/v1/degradation/status

# 手动触发降级
curl -X POST http://localhost:8000/api/v1/degradation/trigger \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_api_key" \
  -d '{
    "level": "light_degradation",
    "reason": "预防性维护",
    "force": false
  }'

# 手动恢复
curl -X POST "http://localhost:8000/api/v1/degradation/recover?reason=维护完成" \
  -H "Authorization: Bearer your_api_key"
```

## 6. 最佳实践

### 6.1 监控建议
- 定期查询系统状态（建议间隔30秒）
- 监控关键指标变化趋势
- 设置告警阈值和通知机制

### 6.2 操作建议
- 手动操作前先查询当前状态
- 提供详细的操作原因说明
- 重要操作使用force=false进行安全检查
- 记录所有手动操作的审计日志

### 6.3 错误处理
- 实现API调用的重试机制
- 处理网络超时和连接异常
- 记录API调用日志便于问题排查
