/**
 * 规则明细相关的 TypeScript 类型定义
 * 基于规则详情表三表结构重构
 *
 * 更新内容：
 * - 支持新的三表结构（rule_template, rule_detail, rule_field_metadata）
 * - 统一字段命名规范（level1, level2, level3等）
 * - 支持扩展字段的JSON存储
 * - 与后端API接口保持一致
 */

// 导入自动生成的类型定义
import type {
  RuleDetail as GeneratedRuleDetail,
  CreateRuleDetailData as GeneratedCreateRuleDetailData,
  UpdateRuleDetailData as GeneratedUpdateRuleDetailData
} from './generated-fields'

// 导入数据库模型类型
import type {
  RuleTemplate,
  RuleFieldMetadata
} from './database-models'

// ==================== 基础类型定义 ====================

/**
 * 规则明细状态枚举
 */
export enum RuleDetailStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DEPRECATED = 'DEPRECATED'
}

/**
 * 批量操作类型枚举
 */
export enum BatchOperationType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE'
}

/**
 * 排序方向枚举
 */
export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc'
}

// ==================== 数据模型类型 ====================

/**
 * 规则明细接口
 * 基于自动生成的类型，添加业务特定字段
 * 支持新的三表结构和扩展字段
 */
export interface RuleDetail extends GeneratedRuleDetail {
  // 业务特定字段
  id: number
  rule_key: string  // 关联rule_template表的rule_key字段
  status: RuleDetailStatus

  // 审计字段
  created_at: string
  updated_at: string
  created_by?: string
  updated_by?: string

  // 关联数据（可选，用于联合查询）
  template?: RuleTemplate
  field_metadata?: RuleFieldMetadata[]
}

/**
 * 规则明细创建数据接口
 * 基于自动生成的类型，添加业务特定字段
 * 支持扩展字段的JSON存储
 */
export interface CreateRuleDetailData extends GeneratedCreateRuleDetailData {
  rule_key: string  // 必填：关联的规则模板类型
  status?: RuleDetailStatus
  created_by?: string

  // 扩展字段验证
  extended_fields?: Record<string, any>
}

/**
 * 规则明细更新数据接口
 * 基于自动生成的类型，添加业务特定字段
 * 支持扩展字段的JSON存储
 */
export interface UpdateRuleDetailData extends GeneratedUpdateRuleDetailData {
  status?: RuleDetailStatus
  updated_by?: string

  // 扩展字段验证
  extended_fields?: Record<string, any>
}

// ==================== 查询和分页类型 ====================

/**
 * 分页信息接口
 */
export interface PaginationInfo {
  page: number
  pageSize: number
  total: number
  hasNext: boolean
  hasPrev: boolean
}

/**
 * 查询参数接口
 * 使用新的统一字段命名
 */
export interface RuleDetailsQueryParams {
  page?: number
  page_size?: number
  status?: RuleDetailStatus
  search?: string
  level1?: string
  level2?: string
  level3?: string
  type?: string
  sort_by?: string
  sort_order?: SortOrder
  pos?: string
  applicableArea?: string
  created_by?: string
  created_at_start?: string
  created_at_end?: string
}

/**
 * 搜索参数接口
 */
export interface SearchParams {
  keyword?: string
  fields?: string[]
  exact_match?: boolean
  case_sensitive?: boolean
}

/**
 * 过滤条件接口
 * 使用新的统一字段命名
 */
export interface FilterConditions {
  status?: RuleDetailStatus | null
  search?: string
  level1?: string | null
  level2?: string | null
  level3?: string | null
  type?: string | null
  sortBy?: string
  sortOrder?: SortOrder
}

// ==================== 批量操作类型 ====================

/**
 * 批量操作项接口
 */
export interface BatchOperationItem {
  operation: BatchOperationType
  id?: number
  data?: CreateRuleDetailData | UpdateRuleDetailData
}

/**
 * 批量更新项接口
 */
export interface BatchUpdateItem {
  id: number
  data: UpdateRuleDetailData
}

/**
 * 批量操作结果接口
 */
export interface BatchOperationResult {
  successCount: number
  failedCount: number
  errors: BatchOperationError[]
  updatedDetails?: RuleDetail[]
}

/**
 * 批量操作错误接口
 */
export interface BatchOperationError {
  id?: number
  operation: BatchOperationType
  error: string
  details?: any
}

/**
 * 增量数据项接口
 */
export interface IncrementalDataItem {
  operation: BatchOperationType
  id?: number
  data: CreateRuleDetailData | UpdateRuleDetailData
}

// ==================== API 响应类型 ====================

/**
 * API 响应基础接口
 */
export interface ApiResponse<T = any> {
  code: number
  success: boolean
  message: string
  data: T
}

/**
 * 分页响应接口
 */
export interface PaginationResponse<T = any> {
  items: T[]
  pagination: PaginationInfo
}

/**
 * 规则明细列表响应接口
 */
export interface RuleDetailsListResponse {
  items: RuleDetail[]
  pagination: PaginationInfo
}

/**
 * 规则明细详情响应接口（包含关联数据）
 */
export interface RuleDetailWithRelationsResponse {
  detail: RuleDetail
  template: RuleTemplate
  field_metadata: RuleFieldMetadata[]
}

/**
 * 规则明细批量响应接口
 */
export interface RuleDetailsBatchResponse {
  items: RuleDetailWithRelationsResponse[]
  pagination: PaginationInfo
  stats: {
    total_templates: number
    total_fields: number
    by_rule_key: Record<string, number>
  }
}

/**
 * 统计数据接口
 */
export interface RuleDetailsStats {
  total: number
  active: number
  inactive: number
  deleted: number
  by_category: Record<string, number>
  by_error_level: Record<string, number>
  by_status: Record<string, number>
  recent_activity: {
    created_today: number
    updated_today: number
    deleted_today: number
  }
}

// ==================== Store 状态类型 ====================

/**
 * 操作状态接口
 */
export interface OperationStatus {
  lastOperation: string | null
  operationTime: number | null
  affectedCount: number
  errors: BatchOperationError[]
}

/**
 * 批量操作进度接口
 */
export interface BatchOperationProgress {
  total: number
  completed: number
  failed: number
  errors: BatchOperationError[]
}

// ==================== 组件 Props 类型 ====================

/**
 * 表格列配置接口
 */
export interface TableColumn {
  prop: string
  label: string
  width?: number
  minWidth?: number
  fixed?: 'left' | 'right'
  sortable?: boolean
  showOverflowTooltip?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
}

/**
 * 表单字段配置接口
 */
export interface FormField {
  prop: string
  label: string
  type: 'input' | 'select' | 'date' | 'textarea' | 'number' | 'switch'
  required?: boolean
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  rules?: any[]
  span?: number
}

// ==================== 缓存相关类型 ====================

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expireTime: number
  accessCount: number
  lastAccess: number
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  expireTime: number
  maxSize: number
  useStorage: boolean
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  memory: {
    size: number
    maxSize: number
    usage: number
  }
  storage: {
    size: number
  }
  total: {
    size: number
  }
}

// ==================== 事件类型 ====================

/**
 * 规则明细事件接口
 */
export interface RuleDetailEvent {
  type: 'create' | 'update' | 'delete' | 'batch'
  ruleKey: string
  detailId?: number
  detailIds?: number[]
  data?: any
  timestamp: number
}

// ==================== 工具函数 ====================

/**
 * 获取字段的中文名称
 * 重新导出自动生成的工具函数
 */
export { getFieldChineseName, isFieldRequired, FIELD_CHINESE_NAMES } from './generated-fields'

/**
 * 获取规则状态的中文名称
 */
export function getRuleStatusChineseName(status: RuleDetailStatus): string {
  const statusNames = {
    [RuleDetailStatus.ACTIVE]: '启用',
    [RuleDetailStatus.INACTIVE]: '禁用',
    [RuleDetailStatus.DEPRECATED]: '已废弃'
  }
  return statusNames[status] || status
}

/**
 * 检查规则是否可编辑
 */
export function isRuleEditable(rule: RuleDetail): boolean {
  return rule.status !== RuleDetailStatus.DEPRECATED
}

/**
 * 获取规则的显示标题
 */
export function getRuleDisplayTitle(rule: RuleDetail): string {
  return rule.rule_name || rule.rule_id || '未命名规则'
}

// ==================== 说明 ====================
// 所有类型都已通过 export interface 或 export enum 直接导出
// 自动生成的类型 CommonFields, SpecificFields 通过 import type 引入并在需要时使用
