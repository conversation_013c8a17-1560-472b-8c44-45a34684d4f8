#!/usr/bin/env node

/**
 * 前端字段名标准化更新脚本
 * 将旧字段名批量替换为新的标准字段名
 */

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 字段映射配置
const FIELD_MAPPING = {
  // 核心错误级别字段
  'error_level_1': 'level1',
  'error_level_2': 'level2',
  'error_level_3': 'level3',

  // 其他标准化字段
  'error_severity': 'degree',
  'quality_basis': 'reference',
  'location_desc': 'detail_position',
  'prompt_field_code': 'prompted_fields1',
  'prompt_field_type': 'prompted_fields3',
  'rule_category': 'type',
  'applicable_business': 'pos',
  'applicable_region': 'applicableArea',
  'default_selected': 'default_use',
  'remark': 'remarks',
  'input_illustration': 'in_illustration'
}

// 需要更新的文件扩展名
const FILE_EXTENSIONS = ['.vue', '.js', '.ts']

// 需要更新的目录
const UPDATE_DIRECTORIES = [
  'src/components',
  'src/views',
  'src/composables',
  'src/stores',
  'src/api',
  'src/utils'
]

/**
 * 获取所有需要更新的文件
 */
function getAllFiles() {
  const files = []

  UPDATE_DIRECTORIES.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir)
    if (fs.existsSync(fullPath)) {
      const dirFiles = getFilesRecursively(fullPath)
      files.push(...dirFiles)
    }
  })

  return files.filter(file =>
    FILE_EXTENSIONS.some(ext => file.endsWith(ext))
  )
}

/**
 * 递归获取目录下的所有文件
 */
function getFilesRecursively(dir) {
  const files = []
  const items = fs.readdirSync(dir)

  items.forEach(item => {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)

    if (stat.isDirectory()) {
      files.push(...getFilesRecursively(fullPath))
    } else {
      files.push(fullPath)
    }
  })

  return files
}

/**
 * 更新单个文件中的字段名
 */
function updateFileFieldNames(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let hasChanges = false

    // 记录替换信息
    const replacements = []

    // 执行字段名替换
    Object.entries(FIELD_MAPPING).forEach(([oldField, newField]) => {
      // 匹配各种使用场景的正则表达式
      const patterns = [
        // 对象属性访问：obj.error_level_1
        new RegExp(`\\.${oldField}\\b`, 'g'),
        // 对象属性定义：{ error_level_1: value }
        new RegExp(`\\b${oldField}:`, 'g'),
        // 字符串字面量：'error_level_1'
        new RegExp(`'${oldField}'`, 'g'),
        // 双引号字符串："error_level_1"
        new RegExp(`"${oldField}"`, 'g'),
        // 模板字符串中的属性访问
        new RegExp(`\\$\\{[^}]*\\.${oldField}\\b[^}]*\\}`, 'g'),
        // Vue模板中的属性绑定：v-model="formData.error_level_1"
        new RegExp(`\\b${oldField}\\b(?=\\s*[\\]\\}\\)\\s]*[\\s\\n]*[>}])`, 'g')
      ]

      patterns.forEach((pattern, index) => {
        const matches = content.match(pattern)
        if (matches) {
          let replacement
          switch (index) {
            case 0: // .error_level_1
              replacement = `.${newField}`
              break
            case 1: // error_level_1:
              replacement = `${newField}:`
              break
            case 2: // 'error_level_1'
              replacement = `'${newField}'`
              break
            case 3: // "error_level_1"
              replacement = `"${newField}"`
              break
            case 4: // ${obj.error_level_1}
              replacement = content.match(pattern)[0].replace(oldField, newField)
              break
            case 5: // 其他情况
              replacement = newField
              break
          }

          const newContent = content.replace(pattern, replacement)
          if (newContent !== content) {
            content = newContent
            hasChanges = true
            replacements.push({
              oldField,
              newField,
              pattern: pattern.toString(),
              count: matches.length
            })
          }
        }
      })
    })

    // 如果有变更，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8')
      return {
        updated: true,
        replacements
      }
    }

    return { updated: false, replacements: [] }

  } catch (error) {
    console.error(`❌ 更新文件失败: ${filePath}`)
    console.error(error.message)
    return { updated: false, replacements: [], error: error.message }
  }
}

/**
 * 主执行函数
 */
function main() {
  console.log('🚀 开始前端字段名标准化更新...\n')

  // 获取所有需要更新的文件
  const files = getAllFiles()
  console.log(`📁 找到 ${files.length} 个文件需要检查\n`)

  // 统计信息
  let updatedFiles = 0
  let totalReplacements = 0
  const updateResults = []

  // 逐个更新文件
  files.forEach(file => {
    const relativePath = path.relative(process.cwd(), file)
    const result = updateFileFieldNames(file)

    if (result.error) {
      console.log(`❌ ${relativePath} - 更新失败: ${result.error}`)
    } else if (result.updated) {
      updatedFiles++
      const replacementCount = result.replacements.reduce((sum, r) => sum + r.count, 0)
      totalReplacements += replacementCount

      console.log(`✅ ${relativePath} - 更新成功 (${replacementCount} 处替换)`)
      result.replacements.forEach(r => {
        console.log(`   ${r.oldField} → ${r.newField} (${r.count} 次)`)
      })

      updateResults.push({
        file: relativePath,
        replacements: result.replacements
      })
    } else {
      console.log(`⚪ ${relativePath} - 无需更新`)
    }
  })

  // 输出统计结果
  console.log('\n📊 更新统计:')
  console.log(`- 检查文件: ${files.length}`)
  console.log(`- 更新文件: ${updatedFiles}`)
  console.log(`- 总替换数: ${totalReplacements}`)

  // 输出详细的字段映射统计
  console.log('\n📋 字段映射统计:')
  const fieldStats = {}
  updateResults.forEach(result => {
    result.replacements.forEach(r => {
      const key = `${r.oldField} → ${r.newField}`
      fieldStats[key] = (fieldStats[key] || 0) + r.count
    })
  })

  Object.entries(fieldStats)
    .sort(([, a], [, b]) => b - a)
    .forEach(([mapping, count]) => {
      console.log(`  ${mapping}: ${count} 次`)
    })

  if (updatedFiles > 0) {
    console.log('\n🎉 字段名标准化更新完成!')
    console.log('💡 建议运行以下命令验证更新结果:')
    console.log('   npm run build')
    console.log('   npm run type-check')
  } else {
    console.log('\n✨ 所有文件已是最新状态，无需更新')
  }
}

// 执行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export {
  FIELD_MAPPING,
  updateFileFieldNames,
  getAllFiles
}
