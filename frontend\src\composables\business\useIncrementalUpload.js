/**
 * 增量上传业务逻辑 Composable
 * 提供数据对比、增量上传等核心功能
 */

import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getRuleDetailsList, incrementalUploadRuleDetails } from '@/api/ruleDetails'
import { get } from '@/api/request'
import { useAppStore } from '@/stores/app'

/**
 * 增量上传 Composable
 * @param {string} ruleKey - 规则键
 * @returns {Object} 增量上传相关方法和状态
 */
export function useIncrementalUpload(ruleKey) {
  // 验证 ruleKey
  if (!ruleKey || typeof ruleKey !== 'string' || ruleKey.trim() === '') {
    console.error('useIncrementalUpload: ruleKey 无效:', ruleKey)
    throw new Error('ruleKey 不能为空')
  }

  const appStore = useAppStore()

  // 状态管理
  const existingData = ref([])
  const comparisonResult = ref({
    create: [],
    update: [],
    delete: []
  })
  const isComparing = ref(false)
  const isUploading = ref(false)
  const uploadProgress = ref(0)

  // 计算属性
  const totalOperations = computed(() => {
    return comparisonResult.value.create.length +
      comparisonResult.value.update.length +
      comparisonResult.value.delete.length
  })

  const operationSummary = computed(() => {
    return {
      create: comparisonResult.value.create.length,
      update: comparisonResult.value.update.length,
      delete: comparisonResult.value.delete.length,
      total: totalOperations.value
    }
  })

  /**
   * 验证规则模板是否存在
   */
  const validateRuleTemplate = async () => {
    try {
      appStore.addLoadingTask('validateRuleTemplate', '正在验证规则模板...')

      // 验证规则模板是否存在（通过API调用）

      // 通过获取规则状态来验证规则模板是否存在
      const response = await get(`/v1/rules/status`)

      if (response && response.success && response.data) {
        const ruleExists = response.data.some(rule => rule.rule_key === ruleKey)
        if (!ruleExists) {
          throw new Error(`规则模板 '${ruleKey}' 不存在`)
        }
        return true
      } else {
        // 如果获取规则状态失败，尝试直接获取规则明细（会触发模板验证）
        await getRuleDetailsList(ruleKey, { page: 1, page_size: 1 })
        return true
      }
    } catch (error) {
      console.error('规则模板验证失败:', error)

      let errorMessage = '规则模板验证失败'
      if (error.message && error.message.includes('不存在')) {
        errorMessage = `规则模板 '${ruleKey}' 不存在，请先创建规则模板或检查规则键是否正确`
      } else {
        errorMessage = error.message || '无法验证规则模板，请检查网络连接或联系管理员'
      }

      ElMessage.error(errorMessage)
      throw new Error(errorMessage)
    } finally {
      appStore.removeLoadingTask('validateRuleTemplate')
    }
  }

  /**
   * 获取现有数据
   */
  const fetchExistingData = async () => {
    try {
      appStore.addLoadingTask('fetchExistingData', '正在获取现有数据...')

      const response = await getRuleDetailsList(ruleKey, {
        page: 1,
        page_size: 10000, // 获取所有数据进行对比
        include_inactive: true // 包含非活跃数据
      })

      // 检查响应格式：增强API返回的是直接的数据对象，传统API返回的是包装格式
      if (response && response.items) {
        // 增强API返回格式：直接是分页数据对象
        existingData.value = response.items || []
        ElMessage.success(`获取到 ${existingData.value.length} 条现有数据`)
        return existingData.value
      } else if (response && response.success && response.data && response.data.items) {
        // 传统API返回格式：包装在success/data中
        existingData.value = response.data.items || []
        ElMessage.success(`获取到 ${existingData.value.length} 条现有数据`)
        return existingData.value
      } else {
        throw new Error(response?.message || '获取现有数据失败')
      }
    } catch (error) {
      console.error('获取现有数据失败:', error)

      // 增强错误处理，提供更详细的错误信息
      let errorMessage = '获取现有数据失败'

      if (error.response) {
        // HTTP响应错误
        const { status, data } = error.response
        if (status === 404 || (data && data.message && data.message.includes('不存在'))) {
          errorMessage = `规则模板 '${ruleKey}' 不存在，请先创建规则模板或检查规则键是否正确`
        } else if (status >= 500) {
          errorMessage = '服务器内部错误，请稍后重试或联系管理员'
        } else {
          errorMessage = data?.message || `请求失败 (状态码: ${status})`
        }
      } else if (error.request) {
        // 网络错误
        errorMessage = '网络连接失败，请检查网络设置或服务器状态'
      } else if (error.message) {
        // 其他错误
        if (error.message.includes('不存在')) {
          errorMessage = `规则模板 '${ruleKey}' 不存在，请先创建规则模板或检查规则键是否正确`
        } else {
          errorMessage = error.message
        }
      }

      ElMessage.error(errorMessage)
      throw new Error(errorMessage)
    } finally {
      appStore.removeLoadingTask('fetchExistingData')
    }
  }

  /**
   * 检测字段变更
   * @param {Object} newData - 新数据
   * @param {Object} existingData - 现有数据
   * @returns {Array} 变更字段列表
   */
  const detectChanges = (newData, existingData) => {
    const changes = []
    const fieldsToCompare = [
      'rule_name', 'error_level_1', 'error_level_2', 'error_level_3',
      'error_level_4', 'error_level_5', 'error_level_6', 'error_level_7',
      'error_level_8', 'error_level_9', 'error_level_10', 'status',
      'description', 'rule_type', 'priority', 'category'
    ]

    fieldsToCompare.forEach(field => {
      const newValue = newData[field]
      const existingValue = existingData[field]

      // 处理不同数据类型的比较
      if (String(newValue || '').trim() !== String(existingValue || '').trim()) {
        changes.push({
          field,
          oldValue: existingValue,
          newValue: newValue,
          fieldLabel: getFieldLabel(field)
        })
      }
    })

    return changes
  }

  /**
   * 获取字段标签
   * @param {string} fieldName - 字段名
   * @returns {string} 字段标签
   */
  const getFieldLabel = (fieldName) => {
    const fieldLabels = {
      rule_name: '规则名称',
      error_level_1: '错误级别1',
      error_level_2: '错误级别2',
      error_level_3: '错误级别3',
      error_level_4: '错误级别4',
      error_level_5: '错误级别5',
      error_level_6: '错误级别6',
      error_level_7: '错误级别7',
      error_level_8: '错误级别8',
      error_level_9: '错误级别9',
      error_level_10: '错误级别10',
      status: '状态',
      description: '描述',
      rule_type: '规则类型',
      priority: '优先级',
      category: '分类'
    }
    return fieldLabels[fieldName] || fieldName
  }

  /**
   * 执行数据对比
   * @param {Array} excelData - Excel解析的数据
   * @returns {Object} 对比结果
   */
  const compareData = async (excelData) => {
    try {
      isComparing.value = true
      appStore.addLoadingTask('compareData', '正在对比数据...')

      // 首先验证规则模板是否存在
      await validateRuleTemplate()

      // 如果没有现有数据，先获取
      if (existingData.value.length === 0) {
        await fetchExistingData()
      }

      // 创建现有数据的索引（以rule_id为键）
      const existingMap = new Map()
      existingData.value.forEach(item => {
        if (item.rule_id) {
          existingMap.set(item.rule_id, item)
        }
      })

      const operations = {
        create: [],
        update: [],
        delete: []
      }

      // 处理Excel数据
      excelData.forEach((excelItem, index) => {
        if (!excelItem.rule_id) {
          console.warn(`第${index + 1}行数据缺少rule_id，跳过处理`)
          return
        }

        const existing = existingMap.get(excelItem.rule_id)

        if (!existing) {
          // 新增操作
          operations.create.push({
            operation: 'CREATE',
            data: excelItem,
            changes: null,
            index: index + 1
          })
        } else {
          // 检查是否有变更
          const changes = detectChanges(excelItem, existing)
          if (changes.length > 0) {
            // 更新操作
            operations.update.push({
              operation: 'UPDATE',
              rule_detail_id: existing.rule_detail_id,
              data: excelItem,
              changes: changes,
              original: existing,
              index: index + 1
            })
          }
          // 标记为已处理
          existingMap.delete(excelItem.rule_id)
        }
      })

      // 处理删除操作（剩余的现有数据）
      // 注意：删除操作需要用户明确确认
      existingMap.forEach(item => {
        operations.delete.push({
          operation: 'DELETE',
          rule_detail_id: item.rule_detail_id,
          data: item,
          changes: null
        })
      })

      comparisonResult.value = operations

      ElMessage.success(
        `数据对比完成: 新增${operations.create.length}条, 更新${operations.update.length}条, 删除${operations.delete.length}条`
      )

      return operations
    } catch (error) {
      console.error('数据对比失败:', error)
      ElMessage.error(`数据对比失败: ${error.message}`)
      throw error
    } finally {
      isComparing.value = false
      appStore.removeLoadingTask('compareData')
    }
  }

  /**
   * 转换为API格式
   * @param {Object} operations - 操作列表
   * @param {Object} userConfirmations - 用户确认选项
   * @returns {Object} API格式的数据
   */
  const convertToApiFormat = (operations, userConfirmations = {}) => {
    const apiOperations = []

    // 处理新增操作
    if (userConfirmations.create !== false) {
      operations.create.forEach(item => {
        apiOperations.push({
          operation: 'CREATE',
          rule_detail_id: null,
          data: item.data
        })
      })
    }

    // 处理更新操作
    if (userConfirmations.update !== false) {
      operations.update.forEach(item => {
        apiOperations.push({
          operation: 'UPDATE',
          rule_detail_id: item.rule_detail_id,
          data: item.data
        })
      })
    }

    // 处理删除操作（默认不执行，需要用户明确确认）
    if (userConfirmations.delete === true) {
      operations.delete.forEach(item => {
        apiOperations.push({
          operation: 'DELETE',
          rule_detail_id: item.rule_detail_id,
          data: null
        })
      })
    }

    return { operations: apiOperations }
  }

  /**
   * 执行增量上传
   * @param {Object} userConfirmations - 用户确认选项
   * @returns {Object} 上传结果
   */
  const executeIncrementalUpload = async (userConfirmations = {}) => {
    try {
      isUploading.value = true
      uploadProgress.value = 0
      appStore.addLoadingTask('incrementalUpload', '正在执行增量上传...')

      // 转换为API格式
      const apiData = convertToApiFormat(comparisonResult.value, userConfirmations)

      if (apiData.operations.length === 0) {
        ElMessage.warning('没有需要执行的操作')
        return { success: true, data: { total_items: 0 } }
      }

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        if (uploadProgress.value < 90) {
          uploadProgress.value += 10
        }
      }, 200)

      // 执行增量上传
      const response = await incrementalUploadRuleDetails(ruleKey, apiData)

      clearInterval(progressInterval)
      uploadProgress.value = 100

      if (response.success) {
        ElMessage.success(`增量上传成功: 处理了${response.data.total_items}条记录`)
        return response
      } else {
        throw new Error(response.message || '增量上传失败')
      }
    } catch (error) {
      console.error('增量上传失败:', error)
      ElMessage.error(`增量上传失败: ${error.message}`)
      throw error
    } finally {
      isUploading.value = false
      uploadProgress.value = 0
      appStore.removeLoadingTask('incrementalUpload')
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    existingData.value = []
    comparisonResult.value = {
      create: [],
      update: [],
      delete: []
    }
    isComparing.value = false
    isUploading.value = false
    uploadProgress.value = 0
  }

  return {
    // 状态
    existingData,
    comparisonResult,
    isComparing,
    isUploading,
    uploadProgress,

    // 计算属性
    totalOperations,
    operationSummary,

    // 方法
    validateRuleTemplate,
    fetchExistingData,
    compareData,
    executeIncrementalUpload,
    resetState,

    // 工具方法
    detectChanges,
    getFieldLabel,
    convertToApiFormat
  }
}
