import inspect
import re
from pathlib import Path

import openpyxl
from loguru import logger

from rules.base_rules.base import BaseRule as BaseRuleLogic


class TemplateGenerator:
    """
    Dynamically generates Excel templates for rule classes.
    """

    def __init__(self, output_dir: str = "generated_templates"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.base_params = ("rule_id", "self")

    @staticmethod
    def get_parameter_mapping(rule_class: type[BaseRuleLogic]) -> dict[str, str]:
        """
        Creates a mapping from parameter names to their Chinese comments.
        This can be shared between the generator and the parser.
        """
        try:
            init_source = inspect.getsource(rule_class.__init__)
            all_params = inspect.signature(rule_class.__init__).parameters

            param_comments = {}
            for line in init_source.splitlines():
                match = re.search(r"^\s*(\w+)\s*:\s*.+?#\s*(.+?)\s*$", line)
                if match:
                    param_name, comment = match.group(1).strip(), match.group(2).strip()
                    param_comments[param_name] = comment

            # Ensure all params have a mapping, fallback to the name itself
            final_mapping = {name: param_comments.get(name, name) for name in all_params}
            return final_mapping
        except Exception:
            # On failure, return a mapping that just uses param names
            return {name: name for name in inspect.signature(rule_class.__init__).parameters}

    def generate_template(self, rule_class: type[BaseRuleLogic]) -> Path | None:
        """
        Generates an Excel template for a given rule class.
        It extracts column names from __init__ parameter comments.
        """
        rule_key = getattr(rule_class, "rule_key", None)
        rule_name = getattr(rule_class, "rule_name", rule_key)  # Use rule_key as fallback
        if not rule_key:
            logger.warning(f"Cannot generate template for class {rule_class.__name__}: rule_key not defined.")
            return None

        try:
            # 1. Get the parameter-to-comment mapping
            param_mapping = self.get_parameter_mapping(rule_class)

            # 2. Get ordered parameters from the signature
            all_params = inspect.signature(rule_class.__init__).parameters

            # 3. Generate headers, using comments as names and preserving order
            template_headers = []
            for name, param in all_params.items():
                if param.kind == param.POSITIONAL_OR_KEYWORD and name not in self.base_params:
                    header = param_mapping.get(name, name)
                    template_headers.append(header)

            if not template_headers:
                logger.warning(f"No specific parameters found for rule '{rule_key}'. Template will not be generated.")
                return None

            workbook = openpyxl.Workbook()
            sheet = workbook.active
            sheet.title = rule_name  # Set sheet name to rule's chinese name
            sheet.append(template_headers)

            # Add some styling to the header
            for cell in sheet[1]:
                cell.font = openpyxl.styles.Font(bold=True)

            # Use chinese rule_name for the filename
            output_path = self.output_dir / f"{rule_name}-规则模板.xlsx"
            workbook.save(output_path)
            logger.info(f"Successfully generated template for rule '{rule_key}' at {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Failed to generate template for rule '{rule_key}': {e}")
            return None
