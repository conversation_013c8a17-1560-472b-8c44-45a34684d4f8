"""
统一数据校验服务
整合患者数据校验和规则明细数据校验功能
支持元数据驱动的统一校验引擎
"""

import time
from dataclasses import dataclass
from typing import Any

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from core.rule_cache import RULE_CACHE
from models.database import RuleTemplate
from services.metadata_validation_service import BatchValidationResult, MetadataValidationService, ValidationRule
from services.rule_detail_service import ServiceError
from services.validation_service import ValidationService


@dataclass
class UnifiedValidationResult:
    """统一验证结果"""

    validation_type: str  # "patient_data" or "rule_detail"
    rule_key: str | None
    valid: bool
    errors: list[str]
    warnings: list[str]
    data: dict[str, Any]
    duration: float
    details: dict[str, Any]


class UnifiedValidationService:
    """统一数据校验服务"""

    def __init__(self, session: Session):
        """
        初始化服务

        Args:
            session: 数据库会话
        """
        self.session = session
        self.metadata_validation_service = MetadataValidationService(session)
        self.patient_validation_service = ValidationService()

    def validate_rule_detail_data(self, rule_key: str, data: dict[str, Any]) -> UnifiedValidationResult:
        """
        验证规则明细数据

        Args:
            rule_key: 规则模板键
            data: 待验证的数据

        Returns:
            UnifiedValidationResult: 统一验证结果
        """
        start_time = time.perf_counter()

        try:
            # 使用元数据验证服务
            result = self.metadata_validation_service.validate_rule_detail_data(rule_key, data)

            duration = time.perf_counter() - start_time

            unified_result = UnifiedValidationResult(
                validation_type="rule_detail",
                rule_key=rule_key,
                valid=result.valid,
                errors=result.errors,
                warnings=result.warnings,
                data=result.data,
                duration=duration,
                details={
                    "field_count": len(result.data),
                    "error_count": len(result.errors),
                    "warning_count": len(result.warnings),
                },
            )

            logger.debug(f"规则明细数据验证完成: rule_key={rule_key}, valid={result.valid}, duration={duration:.3f}s")

            return unified_result

        except Exception as e:
            duration = time.perf_counter() - start_time
            error_msg = f"规则明细数据验证失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)

            return UnifiedValidationResult(
                validation_type="rule_detail",
                rule_key=rule_key,
                valid=False,
                errors=[error_msg],
                warnings=[],
                data={},
                duration=duration,
                details={"error": str(e)},
            )

    def validate_batch_rule_detail_data(self, rule_key: str, data_list: list[dict[str, Any]]) -> BatchValidationResult:
        """
        批量验证规则明细数据

        Args:
            rule_key: 规则模板键
            data_list: 待验证的数据列表

        Returns:
            BatchValidationResult: 批量验证结果
        """
        try:
            return self.metadata_validation_service.validate_batch_data(rule_key, data_list)
        except Exception as e:
            error_msg = f"批量验证规则明细数据失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="BATCH_VALIDATION_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def validate_patient_data(
        self, patient_data: dict[str, Any], rule_ids: list[str] = None
    ) -> UnifiedValidationResult:
        """
        验证患者数据

        Args:
            patient_data: 患者数据
            rule_ids: 要验证的规则ID列表，为空时验证所有规则

        Returns:
            UnifiedValidationResult: 统一验证结果
        """
        start_time = time.perf_counter()

        try:
            # 如果没有指定规则ID，使用所有缓存的规则
            if rule_ids is None:
                rule_ids = list(RULE_CACHE.keys())

            # 使用患者数据验证服务
            violations = self.patient_validation_service.validate_patient_data(patient_data, rule_ids)

            duration = time.perf_counter() - start_time

            # 转换为统一格式
            errors = []
            warnings = []

            for violation in violations:
                if violation.output:
                    errors.append(f"规则 {violation.id}: {violation.output}")
                else:
                    warnings.append(f"规则 {violation.id} 被触发但无输出信息")

            unified_result = UnifiedValidationResult(
                validation_type="patient_data",
                rule_key=None,
                valid=len(violations) == 0,
                errors=errors,
                warnings=warnings,
                data=patient_data,
                duration=duration,
                details={
                    "total_rules": len(rule_ids),
                    "violated_rules": len(violations),
                    "violation_rate": len(violations) / len(rule_ids) if rule_ids else 0,
                },
            )

            logger.info(
                f"患者数据验证完成: rules={len(rule_ids)}, violations={len(violations)}, duration={duration:.3f}s"
            )

            return unified_result

        except Exception as e:
            duration = time.perf_counter() - start_time
            error_msg = f"患者数据验证失败: error={str(e)}"
            logger.error(error_msg)

            return UnifiedValidationResult(
                validation_type="patient_data",
                rule_key=None,
                valid=False,
                errors=[error_msg],
                warnings=[],
                data=patient_data,
                duration=duration,
                details={"error": str(e)},
            )

    def get_validation_rules(self, rule_key: str) -> list[ValidationRule]:
        """
        获取验证规则

        Args:
            rule_key: 规则模板键

        Returns:
            List[ValidationRule]: 验证规则列表
        """
        try:
            return self.metadata_validation_service.get_validation_rules(rule_key)
        except Exception as e:
            error_msg = f"获取验证规则失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="GET_VALIDATION_RULES_FAILED", details={"rule_key": rule_key, "error": str(e)}
            )

    def generate_frontend_validation_config(self, rule_key: str) -> dict[str, Any]:
        """
        生成前端验证配置

        Args:
            rule_key: 规则模板键

        Returns:
            Dict: 前端验证配置
        """
        try:
            return self.metadata_validation_service.generate_frontend_validation_config(rule_key)
        except Exception as e:
            error_msg = f"生成前端验证配置失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="GENERATE_FRONTEND_CONFIG_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def validate_rule_compatibility(self, rule_key: str, patient_data: dict[str, Any]) -> dict[str, Any]:
        """
        验证规则兼容性（检查规则明细数据是否能正确应用于患者数据验证）

        Args:
            rule_key: 规则模板键
            patient_data: 患者数据

        Returns:
            Dict: 兼容性验证结果
        """
        start_time = time.perf_counter()

        try:
            # 1. 检查规则是否在缓存中
            rule_instance = RULE_CACHE.get(rule_key)
            if not rule_instance:
                return {
                    "compatible": False,
                    "reason": f"规则 '{rule_key}' 未在缓存中找到",
                    "suggestions": ["检查规则是否已正确加载", "重新加载规则缓存"],
                }

            # 2. 检查规则模板是否存在
            template = self._get_rule_template(rule_key)
            if not template:
                return {
                    "compatible": False,
                    "reason": f"规则模板 '{rule_key}' 不存在",
                    "suggestions": ["检查规则模板是否已创建", "同步规则模板数据"],
                }

            # 3. 尝试执行规则验证
            try:
                rule_instance.validate(patient_data)  # noqa: F841
                validation_success = True
                validation_error = None
            except Exception as e:
                validation_success = False
                validation_error = str(e)

            # 4. 构建兼容性结果
            duration = time.perf_counter() - start_time

            compatibility_result = {
                "compatible": validation_success,
                "rule_key": rule_key,
                "rule_name": template.name,
                "validation_duration": duration,
                "details": {
                    "rule_cached": True,
                    "template_exists": True,
                    "validation_success": validation_success,
                    "validation_error": validation_error,
                },
            }

            if not validation_success:
                compatibility_result["reason"] = f"规则执行失败: {validation_error}"
                compatibility_result["suggestions"] = [
                    "检查患者数据格式是否正确",
                    "检查规则实现是否有问题",
                    "查看详细错误日志",
                ]

            logger.debug(
                f"规则兼容性验证: rule_key={rule_key}, compatible={validation_success}, duration={duration:.3f}s"
            )

            return compatibility_result

        except Exception as e:
            duration = time.perf_counter() - start_time
            error_msg = f"规则兼容性验证失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)

            return {
                "compatible": False,
                "rule_key": rule_key,
                "reason": error_msg,
                "validation_duration": duration,
                "suggestions": ["检查系统状态", "查看详细错误日志"],
                "details": {"error": str(e)},
            }

    def get_validation_statistics(self) -> dict[str, Any]:
        """
        获取验证统计信息

        Returns:
            Dict: 验证统计信息
        """
        try:
            # 获取规则缓存统计
            cached_rules = len(RULE_CACHE)

            # 获取模板统计
            template_count = self.session.query(RuleTemplate).count()

            # 获取有元数据的模板数量
            templates_with_metadata = (
                self.session.query(RuleTemplate).join(RuleTemplate.field_metadata).distinct().count()
            )

            return {
                "cached_rules": cached_rules,
                "total_templates": template_count,
                "templates_with_metadata": templates_with_metadata,
                "metadata_coverage": templates_with_metadata / template_count if template_count > 0 else 0,
                "validation_modes": {
                    "metadata_driven": templates_with_metadata,
                    "rule_class_driven": template_count - templates_with_metadata,
                },
            }

        except Exception as e:
            logger.error(f"获取验证统计信息失败: error={e}")
            return {
                "error": str(e),
                "cached_rules": 0,
                "total_templates": 0,
                "templates_with_metadata": 0,
                "metadata_coverage": 0,
            }

    def clear_cache(self):
        """清空所有缓存"""
        self.metadata_validation_service.clear_cache()
        logger.info("统一验证服务缓存已清空")

    def _get_rule_template(self, rule_key: str) -> RuleTemplate | None:
        """获取规则模板"""
        return self.session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()
