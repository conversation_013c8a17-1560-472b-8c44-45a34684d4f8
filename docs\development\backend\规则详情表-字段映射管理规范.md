# 字段映射管理规范

## 📋 概述

本文档规范了规则详情表三表结构（rule_template、rule_detail、rule_field_metadata）的字段映射配置管理，确保前后端字段命名的一致性和系统的可维护性。

## 🎯 设计目标

1. **统一字段命名**：消除前后端字段命名不一致的问题
2. **配置驱动**：通过配置文件管理字段映射，避免硬编码
3. **类型安全**：自动生成TypeScript类型定义，确保前端类型安全
4. **向后兼容**：支持字段别名，平滑处理历史字段名
5. **易于维护**：集中管理字段定义，降低维护成本

## 📁 配置文件结构

### 主配置文件：`data/field_mapping.json`

```json
{
  "metadata": {
    "version": "3.0.0",
    "last_updated": "2025-07-23",
    "description": "规则详情表三表结构字段映射权威配置",
    "tables": ["rule_template", "rule_detail", "rule_field_metadata"]
  },
  "table_definitions": {
    "rule_template": { /* 规则模板表字段定义 */ },
    "rule_detail": { /* 规则明细表字段定义 */ },
    "rule_field_metadata": { /* 字段元数据表字段定义 */ }
  },
  "table_relationships": { /* 表间关联关系定义 */ },
  "field_aliases": { /* 字段别名映射 */ },
  "validation_rules": { /* 验证规则定义 */ }
}
```

### 字段定义格式

每个字段包含以下属性：

```json
{
  "field_name": {
    "chinese_name": "字段中文名称",
    "data_type": "数据类型",
    "required": true/false,
    "max_length": 100,
    "description": "字段描述",
    "database_column": "数据库列名",
    "api_field": "API字段名",
    "excel_column": "Excel列名",
    "aliases": ["别名1", "别名2"],
    "validation_rules": ["required", "max_length:100"]
  }
}
```

## 🔧 核心工具类

### FieldMappingManager

字段映射管理的核心工具类，提供以下功能：

```python
from tools.field_mapping_manager import FieldMappingManager

# 初始化
manager = FieldMappingManager("data/field_mapping.json")

# 获取字段定义
field_def = manager.get_field_definition("level1")

# 获取中文名称
chinese_name = manager.get_chinese_name("level1")  # "一级错误类型"

# 获取标准字段名（处理别名）
standard_name = manager.get_standard_field_name("level1")  # "level1"

# 获取数据库列名
db_column = manager.get_database_column("level1")  # "level1"

# 获取API字段名
api_field = manager.get_api_field("level1")  # "level1"

# 获取Excel列名
excel_column = manager.get_excel_column("level1")  # "一级错误类型"

# 获取验证规则
rules = manager.get_validation_rules("level1")  # ["required", "max_length:255"]
```

### UnifiedDataMappingEngine

统一数据映射引擎，处理数据转换：

```python
from services.unified_data_mapping_engine import UnifiedDataMappingEngine

engine = UnifiedDataMappingEngine()

# 标准化字段名称
normalized_data = engine.normalize_field_names(raw_data)

# 转换为中文字段名
chinese_data = engine.convert_to_chinese_fields(data)

# 转换为数据库字段名
db_data = engine.convert_to_database_fields(data)

# 验证数据
validation_result = engine.validate_data(data, "drug_limit_adult_and_diag_exact")

# 分离固定字段和扩展字段
fixed_fields, extended_fields = engine.separate_fields(rule_data)
```

## 📝 使用规范

### 1. 字段命名规范

**注意**：
- 中英文字段名以 `data/field_mapping.json` 为准
- `data/field_mapping.json` 没有的才通过以下规范确定

#### 英文字段名
- 使用小写字母和下划线
- 语义明确，避免缩写
- 统一使用标准名称：`level1`, `level2`, `level3`

#### 中文字段名
- 使用规范的中文表述
- 保持简洁明了
- 统一格式：`一级错误类型`, `二级错误类型`, `三级错误类型`

### 2. 配置更新流程

#### 添加新字段
1. 在 `field_mapping.json` 中添加字段定义
2. 运行配置验证：`python tools/validate_field_mapping.py`
3. 生成TypeScript类型：`npm run generate:types`
4. 更新相关文档

#### 修改现有字段
1. 更新 `field_mapping.json` 中的字段定义
2. 如果修改字段名，添加别名映射
3. 运行配置验证和类型生成
4. 更新相关代码和文档

#### 删除字段
1. 将字段标记为 `deprecated`
2. 保留别名映射，确保向后兼容
3. 在下个版本中完全移除

### 3. 前端类型生成

自动生成TypeScript类型定义：

```bash
# 生成类型定义
npm run generate:types

# 生成的文件：frontend/src/types/generated-fields.ts
```

生成的类型文件包含：
- 通用字段接口
- 特定字段接口
- 字段中文名称映射
- 字段别名映射
- 工具函数

### 4. 数据库迁移

当字段定义发生变化时：

```python
# 生成迁移脚本
python tools/generate_migration.py --config data/field_mapping.json

# 执行迁移
alembic upgrade head
```

## 🧪 测试和验证

### 配置文件验证

```python
# 验证配置文件格式
python tools/validate_field_mapping.py

# 验证与数据库结构的一致性
python tools/validate_db_consistency.py
```

### 字段映射测试

```python
# 单元测试
pytest tests/test_field_mapping.py

# 集成测试
pytest tests/test_data_mapping_engine.py
```

## 🚨 注意事项

### 1. 配置文件管理
- 配置文件纳入版本控制
- 修改前务必备份
- 重大变更需要团队评审

### 2. 向后兼容性
- 新版本必须兼容旧字段名
- 使用别名映射处理历史字段
- 渐进式迁移，避免破坏性变更

### 3. 性能考虑
- 配置文件在应用启动时加载
- 支持配置热重载
- 缓存字段映射结果

### 4. 错误处理
- 配置文件格式错误时提供明确提示
- 字段映射失败时记录详细日志
- 提供降级机制，确保系统可用性

## 📚 相关文档

- [规则详情表重构实施文档](../../project/design/规则详情表重构实施文档.md)
- [数据库设计文档](../database/数据库设计文档.md)
- [API接口文档](../api/API接口文档.md)
- [前端开发规范](../../frontend/前端开发规范.md)

## 🔄 版本历史

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 3.0.0 | 2025-07-23 | 支持三表结构，重构字段映射机制 | 开发团队 |
| 2.0.1 | 2025-07-20 | 优化字段验证规则 | 开发团队 |
| 2.0.0 | 2025-07-15 | 统一字段命名规范 | 开发团队 |

---

**文档维护者**：规则验证系统开发团队  
**最后更新**：2025-07-23  
**下次评审**：2025-08-23
