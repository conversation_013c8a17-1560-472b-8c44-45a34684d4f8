"""
模拟数据生成器
提供各种测试场景的模拟数据
"""

import random
import string
from datetime import datetime
from typing import Any

from faker import Faker

fake = Faker('zh_CN')


class MockDataGenerator:
    """模拟数据生成器"""

    @staticmethod
    def generate_rule_key(prefix: str = "RULE") -> str:
        """生成规则键"""
        return f"{prefix}_{random.randint(1000, 9999)}"

    @staticmethod
    def generate_patient_id() -> str:
        """生成患者ID"""
        return f"P{random.randint(100000, 999999)}"

    @staticmethod
    def generate_rule_detail_id() -> str:
        """生成规则明细ID"""
        return f"RD{random.randint(10000, 99999)}"

    @staticmethod
    def generate_base_rule_data(**overrides) -> dict[str, Any]:
        """生成基础规则数据"""
        data = {
            "rule_key": MockDataGenerator.generate_rule_key(),
            "rule_name": fake.sentence(nb_words=4),
            "description": fake.text(max_nb_chars=200),
            "module_path": f"rules.{fake.word()}.{fake.word()}",
            "file_hash": ''.join(random.choices(string.ascii_lowercase + string.digits, k=32)),
            "status": random.choice(["DRAFT", "READY", "DISABLED"]),
            "created_by": fake.user_name(),
            "created_at": fake.date_time_between(start_date="-1y", end_date="now"),
        }
        data.update(overrides)
        return data

    @staticmethod
    def generate_rule_dataset_data(**overrides) -> dict[str, Any]:
        """生成规则数据集数据"""
        data = {
            "base_rule_id": random.randint(1, 100),
            "version": random.randint(1, 10),
            "is_active": random.choice([True, False]),
            "uploaded_by": fake.user_name(),
            "migration_status": random.choice(["PENDING", "IN_PROGRESS", "COMPLETED", "FAILED"]),
            "created_at": fake.date_time_between(start_date="-6m", end_date="now"),
        }
        data.update(overrides)
        return data

    @staticmethod
    def generate_rule_detail_data(**overrides) -> dict[str, Any]:
        """生成规则明细数据"""
        error_levels_1 = ["数据质量", "适应症限制", "费用控制", "医保政策"]
        error_levels_2 = ["完整性", "准确性", "年龄限制", "性别限制", "超标准", "重复收费"]
        error_levels_3 = ["字段缺失", "格式错误", "超出范围", "不符合条件"]

        data = {
            "dataset_id": random.randint(1, 50),
            "rule_detail_id": MockDataGenerator.generate_rule_detail_id(),
            "rule_name": fake.sentence(nb_words=3),
            "error_level_1": random.choice(error_levels_1),
            "error_level_2": random.choice(error_levels_2),
            "error_level_3": random.choice(error_levels_3),
            "error_reason": fake.text(max_nb_chars=100),
            "error_severity": random.choice(["低", "中", "高"]),
            "rule_category": random.choice(["药品", "诊疗", "服务设施"]),
            "applicable_scope": random.choice(["全部", "门诊", "住院"]),
            "status": random.choice(["ACTIVE", "INACTIVE", "PENDING"]),
            "created_at": fake.date_time_between(start_date="-3m", end_date="now"),
        }
        data.update(overrides)
        return data

    @staticmethod
    def generate_patient_data(**overrides) -> dict[str, Any]:
        """生成患者数据"""
        birth_date = fake.date_of_birth(minimum_age=0, maximum_age=100)
        age = (datetime.now().date() - birth_date).days // 365

        data = {
            "bah": MockDataGenerator.generate_patient_id(),
            "patientBasicInfo": {
                "gender": random.choice(["1", "2"]),  # 1-男, 2-女
                "age": age,
                "birthDate": int(birth_date.strftime("%Y%m%d")),
                "country": "CHN",
            },
            "feeItemList": MockDataGenerator.generate_fee_items(),
            "diagnosisList": MockDataGenerator.generate_diagnosis_list(),
            "operationList": MockDataGenerator.generate_operation_list(),
        }
        data.update(overrides)
        return data

    @staticmethod
    def generate_fee_items(count: int = None) -> list[dict[str, Any]]:
        """生成费用项目列表"""
        if count is None:
            count = random.randint(1, 5)

        items = []
        for _ in range(count):
            items.append({
                "itemCode": f"FEE{random.randint(1000, 9999)}",
                "itemName": fake.sentence(nb_words=2),
                "amount": round(random.uniform(10.0, 1000.0), 2),
                "quantity": random.randint(1, 10),
                "unit": random.choice(["次", "盒", "瓶", "支"]),
                "specification": fake.word(),
            })
        return items

    @staticmethod
    def generate_diagnosis_list(count: int = None) -> list[dict[str, Any]]:
        """生成诊断列表"""
        if count is None:
            count = random.randint(1, 3)

        diagnoses = []
        for i in range(count):
            diagnoses.append({
                "diagnosisCode": f"A{random.randint(10, 99)}.{random.randint(100, 999)}",
                "diagnosisName": fake.sentence(nb_words=3),
                "diagnosisType": "主诊断" if i == 0 else "次诊断",
                "diagnosisDate": fake.date_between(start_date="-1m", end_date="today").strftime("%Y-%m-%d"),
            })
        return diagnoses

    @staticmethod
    def generate_operation_list(count: int = None) -> list[dict[str, Any]]:
        """生成手术列表"""
        if count is None:
            count = random.randint(0, 2)

        operations = []
        for _ in range(count):
            operations.append({
                "operationCode": f"OP{random.randint(1000, 9999)}",
                "operationName": fake.sentence(nb_words=4),
                "operationDate": fake.date_between(start_date="-1m", end_date="today").strftime("%Y-%m-%d"),
                "operationLevel": random.choice(["一级", "二级", "三级", "四级"]),
            })
        return operations

    @staticmethod
    def generate_api_request_data(endpoint: str) -> dict[str, Any]:
        """生成API请求数据"""
        if endpoint == "create_rule_detail":
            return {
                "rule_detail_id": MockDataGenerator.generate_rule_detail_id(),
                "rule_name": fake.sentence(nb_words=3),
                "error_level_1": "数据质量",
                "error_level_2": "完整性",
                "status": "ACTIVE",
            }
        elif endpoint == "validate_patient":
            return MockDataGenerator.generate_patient_data()
        else:
            return {}

    @staticmethod
    def generate_batch_data(data_type: str, count: int = 10) -> list[dict[str, Any]]:
        """生成批量数据"""
        generators = {
            "base_rules": MockDataGenerator.generate_base_rule_data,
            "rule_datasets": MockDataGenerator.generate_rule_dataset_data,
            "rule_details": MockDataGenerator.generate_rule_detail_data,
            "patients": MockDataGenerator.generate_patient_data,
        }

        generator = generators.get(data_type)
        if not generator:
            raise ValueError(f"Unsupported data type: {data_type}")

        return [generator() for _ in range(count)]


# ============================================================================
# 预定义测试数据集
# ============================================================================

class TestDataSets:
    """预定义的测试数据集"""

    # 标准测试规则
    STANDARD_RULES = [
        {
            "rule_key": "AGE_LIMIT_001",
            "rule_name": "年龄限制规则",
            "description": "检查患者年龄是否符合药品适用范围",
            "status": "READY",
        },
        {
            "rule_key": "GENDER_LIMIT_001", 
            "rule_name": "性别限制规则",
            "description": "检查患者性别是否符合药品适用条件",
            "status": "READY",
        },
    ]

    # 边界测试数据
    BOUNDARY_TEST_DATA = {
        "min_age": 0,
        "max_age": 120,
        "min_amount": 0.01,
        "max_amount": 999999.99,
        "empty_string": "",
        "max_string": "x" * 1000,
    }

    # 错误测试数据
    ERROR_TEST_DATA = {
        "invalid_rule_key": "INVALID_RULE_999",
        "invalid_patient_id": "INVALID_PATIENT",
        "malformed_data": {"invalid": "data"},
        "null_values": None,
    }
