"""
规则索引管理器单元测试
测试规则索引的构建、查询和过滤功能
"""

import json
import unittest
from unittest.mock import Mock

from core.rule_index_manager import (
    FilterResult,
    IndexMetadata,
    PatientCodeExtraction,
    PrefixMatcher,
    RuleIndexManager,
    OptimizedTrieNode,
    DetailedPerformanceStats,
)
from core.slave_node_index_builder import SlaveNodeIndexBuilder


class TestOptimizedTrieNode(unittest.TestCase):
    """测试优化的Trie树节点"""

    def test_optimized_trie_node_creation(self):
        """测试优化Trie节点创建"""
        node = OptimizedTrieNode()
        self.assertEqual(len(node.children), 0)
        self.assertEqual(len(node.rule_ids), 0)
        self.assertFalse(node.is_end)

    def test_optimized_trie_node_memory_efficiency(self):
        """测试优化Trie节点的内存效率"""
        import sys

        # 创建大量节点测试内存使用
        nodes = [OptimizedTrieNode() for _ in range(1000)]

        # 验证__slots__确实减少了内存使用
        node = OptimizedTrieNode()
        # 优化后的节点不应该有__dict__属性
        self.assertFalse(hasattr(node, "__dict__"))

    def test_trie_node_operations(self):
        """测试Trie节点操作"""
        node = TrieNode()
        node.rule_ids.add("rule1")
        node.is_end = True

        self.assertEqual(len(node.rule_ids), 1)
        self.assertTrue("rule1" in node.rule_ids)
        self.assertTrue(node.is_end)


class TestPrefixMatcher(unittest.TestCase):
    """测试前缀匹配器"""

    def setUp(self):
        self.matcher = PrefixMatcher(threshold=3)

    def test_build_index_with_mapping(self):
        """测试使用映射表构建索引"""
        prefixes_to_rules = {
            "A01": {"rule1", "rule2"},
            "B02": {"rule3"},
        }

        self.matcher.build_index(prefixes_to_rules)

        self.assertFalse(self.matcher.use_trie)
        self.assertEqual(len(self.matcher.prefix_map), 2)
        self.assertEqual(self.matcher.prefix_map["A01"], {"rule1", "rule2"})

    def test_build_index_with_trie(self):
        """测试使用Trie树构建索引"""
        prefixes_to_rules = {
            "A01": {"rule1"},
            "A02": {"rule2"},
            "B01": {"rule3"},
            "B02": {"rule4"},
            "C01": {"rule5"},  # 超过阈值，使用Trie
        }

        self.matcher.build_index(prefixes_to_rules)

        self.assertTrue(self.matcher.use_trie)
        self.assertIsNotNone(self.matcher.trie_root)

    def test_find_matching_rules_with_mapping(self):
        """测试使用映射表查找匹配规则"""
        prefixes_to_rules = {
            "A01": {"rule1", "rule2"},
            "A012": {"rule3"},
        }

        self.matcher.build_index(prefixes_to_rules)

        # 测试完全匹配
        result = self.matcher.find_matching_rules("A01234")
        self.assertEqual(result, {"rule1", "rule2", "rule3"})

        # 测试部分匹配
        result = self.matcher.find_matching_rules("A01")
        self.assertEqual(result, {"rule1", "rule2"})

        # 测试无匹配
        result = self.matcher.find_matching_rules("B01")
        self.assertEqual(result, set())

    def test_find_matching_rules_with_trie(self):
        """测试使用Trie树查找匹配规则"""
        prefixes_to_rules = {
            "A01": {"rule1"},
            "A02": {"rule2"},
            "B01": {"rule3"},
            "B02": {"rule4"},
            "C01": {"rule5"},
        }

        self.matcher.build_index(prefixes_to_rules)

        # 测试匹配
        result = self.matcher.find_matching_rules("A01234")
        self.assertEqual(result, {"rule1"})

        # 测试无匹配
        result = self.matcher.find_matching_rules("D01")
        self.assertEqual(result, set())


class TestPatientCodeExtraction(unittest.TestCase):
    """测试患者代码提取结果"""

    def test_patient_code_extraction_creation(self):
        """测试患者代码提取结果创建"""
        extraction = PatientCodeExtraction(
            yb_codes={"Y001", "Y002"}, diag_codes={"D001"}, surgery_codes={"S001"}, extraction_time=1.5
        )

        self.assertEqual(len(extraction.yb_codes), 2)
        self.assertEqual(len(extraction.diag_codes), 1)
        self.assertEqual(len(extraction.surgery_codes), 1)
        self.assertEqual(extraction.extraction_time, 1.5)

    def test_get_all_codes(self):
        """测试获取所有代码"""
        extraction = PatientCodeExtraction(
            yb_codes={"Y001"}, diag_codes={"D001"}, surgery_codes={"S001"}, extraction_time=1.0
        )

        all_codes = extraction.get_all_codes()

        self.assertIn("yb_codes", all_codes)
        self.assertIn("diag_codes", all_codes)
        self.assertIn("surgery_codes", all_codes)
        self.assertEqual(all_codes["yb_codes"], {"Y001"})


class TestFilterResult(unittest.TestCase):
    """测试过滤结果"""

    def test_filter_result_creation(self):
        """测试过滤结果创建"""
        result = FilterResult(
            original_rule_count=100,
            filtered_rule_count=30,
            filter_rate=0.7,
            filter_time=2.5,
            filtered_rule_ids=["rule1", "rule2"],
        )

        self.assertEqual(result.original_rule_count, 100)
        self.assertEqual(result.filtered_rule_count, 30)
        self.assertEqual(result.filter_rate, 0.7)
        self.assertEqual(result.performance_gain, 0.7)

    def test_performance_gain_with_zero_rules(self):
        """测试零规则情况的性能提升计算"""
        result = FilterResult(
            original_rule_count=0, filtered_rule_count=0, filter_rate=0.0, filter_time=1.0, filtered_rule_ids=[]
        )

        self.assertEqual(result.performance_gain, 0.0)


class TestRuleIndexManager(unittest.TestCase):
    """测试规则索引管理器"""

    def setUp(self):
        self.manager = RuleIndexManager()

        # 模拟规则明细数据
        self.mock_rule_details = [
            self.create_mock_rule_detail(
                rule_id="rule1",
                yb_code="Y001,Y002",
                diag_whole_code="D001",
                diag_code_prefix="D0",
                fee_whole_code="F001",
                extended_fields='{"surgery_code": "S001"}',
            ),
            self.create_mock_rule_detail(
                rule_id="rule2", yb_code="Y003", diag_whole_code="D002", fee_code_prefix="F0", extended_fields="{}"
            ),
            self.create_mock_rule_detail(
                rule_id="rule3",
                yb_code="",  # 通用规则
                diag_whole_code="",
                extended_fields="{}",
            ),
        ]

    def create_mock_rule_detail(
        self,
        rule_id,
        yb_code="",
        diag_whole_code="",
        diag_code_prefix="",
        fee_whole_code="",
        fee_code_prefix="",
        extended_fields="{}",
    ):
        """创建模拟规则明细对象"""
        mock_detail = Mock()
        mock_detail.rule_id = rule_id
        mock_detail.yb_code = yb_code
        mock_detail.diag_whole_code = diag_whole_code
        mock_detail.diag_code_prefix = diag_code_prefix
        mock_detail.fee_whole_code = fee_whole_code
        mock_detail.fee_code_prefix = fee_code_prefix
        mock_detail.extended_fields = extended_fields
        return mock_detail

    def test_build_indexes_from_rule_details(self):
        """测试从规则明细构建索引"""
        self.manager.build_indexes_from_rule_details(self.mock_rule_details)

        # 检查索引是否就绪
        self.assertTrue(self.manager.is_ready())

        # 检查医保代码索引
        yb_index = self.manager.exact_match_indexes["yb_code"]
        self.assertIn("Y001", yb_index)
        self.assertIn("Y002", yb_index)
        self.assertIn("Y003", yb_index)
        self.assertEqual(yb_index["Y001"], {"rule1"})
        self.assertEqual(yb_index["Y003"], {"rule2"})

        # 检查诊断代码索引
        diag_index = self.manager.exact_match_indexes["diag_code"]
        self.assertIn("D001", diag_index)
        self.assertIn("D002", diag_index)

        # 检查手术代码索引
        surgery_index = self.manager.exact_match_indexes["surgery_code"]
        self.assertIn("S001", surgery_index)
        self.assertEqual(surgery_index["S001"], {"rule1"})

        # 检查通用规则
        self.assertIn("rule3", self.manager.universal_rules)

        # 检查索引元数据
        self.assertIsNotNone(self.manager.index_metadata)
        self.assertEqual(self.manager.index_metadata.rule_count, 3)

    def test_parse_comma_separated(self):
        """测试逗号分隔字符串解析"""
        # 正常情况
        result = self.manager._parse_comma_separated("A001,A002,A003")
        self.assertEqual(result, ["A001", "A002", "A003"])

        # 包含空格
        result = self.manager._parse_comma_separated("A001, A002 , A003")
        self.assertEqual(result, ["A001", "A002", "A003"])

        # 空字符串
        result = self.manager._parse_comma_separated("")
        self.assertEqual(result, [])

        # None值
        result = self.manager._parse_comma_separated(None)
        self.assertEqual(result, [])

        # 包含空项
        result = self.manager._parse_comma_separated("A001,,A003")
        self.assertEqual(result, ["A001", "A003"])

    def test_is_universal_rule(self):
        """测试通用规则判断"""
        # 有代码的规则
        rule_with_codes = self.create_mock_rule_detail("rule1", yb_code="Y001")
        self.assertFalse(self.manager._is_universal_rule(rule_with_codes))

        # 无代码的规则
        rule_without_codes = self.create_mock_rule_detail("rule2")
        self.assertTrue(self.manager._is_universal_rule(rule_without_codes))

        # 扩展字段有代码的规则
        rule_with_extended = self.create_mock_rule_detail("rule3", extended_fields='{"surgery_code": "S001"}')
        self.assertFalse(self.manager._is_universal_rule(rule_with_extended))

    def test_find_relevant_rules(self):
        """测试查找相关规则"""
        self.manager.build_indexes_from_rule_details(self.mock_rule_details)

        # 创建患者代码提取结果
        patient_codes = PatientCodeExtraction(
            yb_codes={"Y001", "F001"}, diag_codes={"D001"}, surgery_codes={"S001"}, extraction_time=1.0
        )

        # 查找相关规则
        relevant_rules = self.manager.find_relevant_rules(patient_codes)

        # 应该包含rule1（匹配Y001, D001, F001, S001）和rule3（通用规则）
        self.assertIn("rule1", relevant_rules)
        self.assertIn("rule3", relevant_rules)  # 通用规则

    def test_filter_rules(self):
        """测试规则过滤"""
        self.manager.build_indexes_from_rule_details(self.mock_rule_details)

        # 创建患者代码提取结果
        patient_codes = PatientCodeExtraction(
            yb_codes={"Y001"}, diag_codes={"D001"}, surgery_codes=set(), extraction_time=1.0
        )

        requested_rules = ["rule1", "rule2", "rule3"]

        # 执行过滤
        filter_result = self.manager.filter_rules(patient_codes, requested_rules)

        # 检查过滤结果
        self.assertEqual(filter_result.original_rule_count, 3)
        self.assertIn("rule1", filter_result.filtered_rule_ids)  # 匹配Y001和D001
        self.assertIn("rule3", filter_result.filtered_rule_ids)  # 通用规则
        self.assertTrue(filter_result.filter_rate > 0)  # 有过滤效果
        self.assertTrue(filter_result.filter_time > 0)  # 有执行时间

    def test_get_performance_stats(self):
        """测试性能统计获取"""
        self.manager.build_indexes_from_rule_details(self.mock_rule_details)

        # 执行一些查询以生成统计数据
        patient_codes = PatientCodeExtraction(yb_codes={"Y001"}, diag_codes=set(), surgery_codes=set(), extraction_time=1.0)
        self.manager.find_relevant_rules(patient_codes)

        # 获取统计信息
        stats = self.manager.get_performance_stats()

        self.assertIn("query_count", stats)
        self.assertIn("total_query_time_ms", stats)
        self.assertIn("avg_query_time_ms", stats)
        self.assertIn("index_metadata", stats)
        self.assertIn("universal_rules_count", stats)
        self.assertIn("exact_index_sizes", stats)

        self.assertTrue(stats["query_count"] > 0)
        self.assertEqual(stats["universal_rules_count"], 1)  # rule3

    def test_clear_indexes(self):
        """测试清空索引"""
        self.manager.build_indexes_from_rule_details(self.mock_rule_details)

        # 确认索引已构建
        self.assertTrue(len(self.manager.exact_match_indexes["yb_code"]) > 0)
        self.assertTrue(len(self.manager.universal_rules) > 0)

        # 清空索引
        self.manager._clear_indexes()

        # 确认索引已清空
        self.assertEqual(len(self.manager.exact_match_indexes["yb_code"]), 0)
        self.assertEqual(len(self.manager.universal_rules), 0)

    def test_thread_safety(self):
        """测试线程安全性（基础测试）"""
        import threading
        import time

        self.manager.build_indexes_from_rule_details(self.mock_rule_details)

        results = []
        errors = []

        def query_worker():
            try:
                patient_codes = PatientCodeExtraction(
                    yb_codes={"Y001"}, diag_codes=set(), surgery_codes=set(), extraction_time=1.0
                )
                for _ in range(10):
                    relevant_rules = self.manager.find_relevant_rules(patient_codes)
                    results.append(len(relevant_rules))
                    time.sleep(0.001)  # 模拟一些处理时间
            except Exception as e:
                errors.append(e)

        # 创建多个线程同时查询
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=query_worker)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 检查结果
        self.assertEqual(len(errors), 0, f"线程安全测试出现错误: {errors}")
        self.assertTrue(len(results) > 0, "应该有查询结果")

        # 所有查询结果应该一致（相同的患者代码应该得到相同的结果）
        if len(results) > 1:
            first_result = results[0]
            for result in results[1:]:
                self.assertEqual(result, first_result, "并发查询结果应该一致")


class TestIndexMetadata(unittest.TestCase):
    """测试索引元数据"""

    def test_index_metadata_creation(self):
        """测试索引元数据创建"""
        metadata = IndexMetadata(
            build_time=1625097600.0, rule_count=100, index_size=50, memory_usage_mb=2.5, build_duration_ms=150.0
        )

        self.assertEqual(metadata.rule_count, 100)
        self.assertEqual(metadata.index_size, 50)
        self.assertEqual(metadata.memory_usage_mb, 2.5)
        self.assertEqual(metadata.build_duration_ms, 150.0)


class TestPerformanceOptimizations(unittest.TestCase):
    """测试性能优化相关功能"""

    def setUp(self):
        self.manager = RuleIndexManager()

    def test_memory_usage_calculation(self):
        """测试内存使用计算"""
        # 创建大量规则测试内存计算
        large_rule_set = []
        for i in range(1000):
            rule = Mock()
            rule.rule_id = f"rule_{i}"
            rule.yb_code = f"Y{i:03d}"
            rule.diag_whole_code = f"D{i:03d}"
            rule.diag_code_prefix = ""
            rule.fee_whole_code = ""
            rule.fee_code_prefix = ""
            rule.extended_fields = "{}"
            large_rule_set.append(rule)

        # 构建索引
        self.manager.build_indexes_from_rule_details(large_rule_set)

        # 检查内存使用是否被正确计算
        self.assertIsNotNone(self.manager.index_metadata)
        self.assertTrue(self.manager.index_metadata.memory_usage_mb > 0)

        # 验证内存使用合理性（应该小于规则数据的50%）
        estimated_rule_data_size = len(large_rule_set) * 0.5  # 估算每个规则0.5MB
        memory_ratio = self.manager.index_metadata.memory_usage_mb / estimated_rule_data_size

        # 在测试环境中，这个比例应该是合理的
        self.assertLess(memory_ratio, 2.0, "内存使用比例过高")

    def test_query_performance(self):
        """测试查询性能"""
        # 创建大量规则
        large_rule_set = []
        for i in range(5000):
            rule = Mock()
            rule.rule_id = f"rule_{i}"
            rule.yb_code = f"Y{i:03d}"
            rule.diag_whole_code = f"D{i:03d}"
            rule.diag_code_prefix = f"D{i // 100}"  # 创建前缀
            rule.fee_whole_code = ""
            rule.fee_code_prefix = ""
            rule.extended_fields = "{}"
            large_rule_set.append(rule)

        # 构建索引
        start_time = time.perf_counter()
        self.manager.build_indexes_from_rule_details(large_rule_set)
        build_time = time.perf_counter() - start_time

        # 验证构建时间合理
        self.assertLess(build_time, 10.0, "索引构建时间过长")

        # 测试查询性能
        patient_codes = PatientCodeExtraction(
            yb_codes={"Y001", "Y002", "Y003"}, diag_codes={"D001", "D002"}, surgery_codes=set(), extraction_time=1.0
        )

        # 执行多次查询测试平均性能
        query_times = []
        for _ in range(100):
            start_time = time.perf_counter()
            relevant_rules = self.manager.find_relevant_rules(patient_codes)
            query_time = (time.perf_counter() - start_time) * 1000  # 转换为毫秒
            query_times.append(query_time)

        avg_query_time = sum(query_times) / len(query_times)

        # 验证查询时间符合要求（应该小于10ms）
        self.assertLess(avg_query_time, 10.0, f"平均查询时间过长: {avg_query_time:.2f}ms")

        # 获取详细性能统计
        detailed_stats = self.manager.get_detailed_performance_stats()
        self.assertIsInstance(detailed_stats, DetailedPerformanceStats)
        self.assertTrue(detailed_stats.query_count > 0)
        self.assertTrue(detailed_stats.index_efficiency >= 0.0)

    def test_concurrent_access_safety(self):
        """测试并发访问安全性"""
        import threading
        import time

        # 构建索引
        rule_set = []
        for i in range(100):
            rule = Mock()
            rule.rule_id = f"rule_{i}"
            rule.yb_code = f"Y{i:03d}"
            rule.diag_whole_code = f"D{i:03d}"
            rule.diag_code_prefix = ""
            rule.fee_whole_code = ""
            rule.fee_code_prefix = ""
            rule.extended_fields = "{}"
            rule_set.append(rule)

        self.manager.build_indexes_from_rule_details(rule_set)

        results = []
        errors = []

        def concurrent_worker(worker_id):
            try:
                for i in range(50):
                    patient_codes = PatientCodeExtraction(
                        yb_codes={f"Y{(worker_id * 10 + i) % 100:03d}"},
                        diag_codes=set(),
                        surgery_codes=set(),
                        extraction_time=1.0,
                    )

                    # 查询相关规则
                    relevant_rules = self.manager.find_relevant_rules(patient_codes)
                    results.append((worker_id, len(relevant_rules)))

                    # 执行过滤
                    filter_result = self.manager.filter_rules(patient_codes, [f"rule_{j}" for j in range(10)])

                    time.sleep(0.001)  # 模拟处理时间

            except Exception as e:
                errors.append((worker_id, e))

        # 创建多个线程并发访问
        threads = []
        for worker_id in range(10):
            thread = threading.Thread(target=concurrent_worker, args=(worker_id,))
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证结果
        self.assertEqual(len(errors), 0, f"并发访问出现错误: {errors}")
        self.assertTrue(len(results) > 0, "应该有查询结果")

        # 验证性能统计的一致性
        stats = self.manager.get_performance_stats()
        self.assertTrue(stats["query_count"] >= len(results))


class TestSlaveNodeIndexBuilder(unittest.TestCase):
    """测试从节点索引构建器"""

    def setUp(self):
        self.manager = RuleIndexManager()
        self.test_cache_file = "test_rules_cache.json.gz"

        # 创建测试用的规则缓存文件
        self._create_test_cache_file()

    def tearDown(self):
        # 清理测试文件
        import os

        if os.path.exists(self.test_cache_file):
            os.remove(self.test_cache_file)

    def _create_test_cache_file(self):
        """创建测试用的规则缓存文件"""
        import gzip
        import json

        test_data = {
            "rule_details": [
                {
                    "rule_id": "rule1",
                    "yb_code": "Y001,Y002",
                    "diag_whole_code": "D001",
                    "diag_code_prefix": "D0",
                    "fee_whole_code": "F001",
                    "fee_code_prefix": "",
                    "extended_fields": '{"surgery_code": "S001"}',
                },
                {
                    "rule_id": "rule2",
                    "yb_code": "Y003",
                    "diag_whole_code": "D002",
                    "diag_code_prefix": "",
                    "fee_whole_code": "",
                    "fee_code_prefix": "F0",
                    "extended_fields": "{}",
                },
                {
                    "rule_id": "rule3",
                    "yb_code": "",
                    "diag_whole_code": "",
                    "diag_code_prefix": "",
                    "fee_whole_code": "",
                    "fee_code_prefix": "",
                    "extended_fields": "{}",
                },
            ]
        }

        with gzip.open(self.test_cache_file, "wt", encoding="utf-8") as f:
            json.dump(test_data, f, ensure_ascii=False)

    def test_slave_node_index_building(self):
        """测试从节点索引构建"""
        builder = SlaveNodeIndexBuilder(self.manager, self.test_cache_file)

        # 构建索引
        success = builder.build_index_from_cache_file()

        # 验证构建成功
        self.assertTrue(success)
        self.assertTrue(self.manager.is_ready())

        # 验证索引内容与主节点构建的一致
        yb_index = self.manager.exact_match_indexes["yb_code"]
        self.assertIn("Y001", yb_index)
        self.assertIn("Y002", yb_index)
        self.assertIn("Y003", yb_index)

        # 验证通用规则
        self.assertIn("rule3", self.manager.universal_rules)

        # 验证索引元数据
        self.assertIsNotNone(self.manager.index_metadata)
        self.assertEqual(self.manager.index_metadata.rule_count, 3)

    def test_slave_node_with_missing_file(self):
        """测试从节点处理缺失文件的情况"""
        builder = SlaveNodeIndexBuilder(self.manager, "nonexistent_file.json.gz")

        # 构建应该失败
        success = builder.build_index_from_cache_file()
        self.assertFalse(success)

    def test_slave_node_with_invalid_file(self):
        """测试从节点处理无效文件的情况"""
        # 创建无效的缓存文件
        invalid_cache_file = "invalid_cache.json.gz"

        try:
            import gzip

            with gzip.open(invalid_cache_file, "wt", encoding="utf-8") as f:
                f.write("invalid json content")

            builder = SlaveNodeIndexBuilder(self.manager, invalid_cache_file)

            # 构建应该失败但不抛出异常
            success = builder.build_index_from_cache_file()
            self.assertFalse(success)

        finally:
            import os

            if os.path.exists(invalid_cache_file):
                os.remove(invalid_cache_file)


if __name__ == "__main__":
    unittest.main()
