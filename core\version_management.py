"""
统一版本管理模块

基于现有的版本管理逻辑（TemplateVersionManager、degradation.py版本生成、sync.py哈希计算），
抽象出统一的BaseVersionManager基类，并实现RuleSyncVersionManager专用版本管理器。
"""

import hashlib
import json
import time
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any

from core.logging.logging_system import log as logger


class VersionInfo:
    """版本信息数据类"""

    def __init__(
        self,
        version: str,
        timestamp: float,
        checksum: str,
        metadata: dict[str, Any] | None = None
    ):
        self.version = version
        self.timestamp = timestamp
        self.checksum = checksum
        self.metadata = metadata or {}

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "version": self.version,
            "timestamp": self.timestamp,
            "checksum": self.checksum,
            "metadata": self.metadata
        }


class ChangeItem:
    """变更项数据类"""

    def __init__(
        self,
        operation: str,  # "create", "update", "delete"
        key: str,
        old_value: Any = None,
        new_value: Any = None,
        metadata: dict[str, Any] | None = None
    ):
        self.operation = operation
        self.key = key
        self.old_value = old_value
        self.new_value = new_value
        self.metadata = metadata or {}

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "operation": self.operation,
            "key": self.key,
            "old_value": self.old_value,
            "new_value": self.new_value,
            "metadata": self.metadata
        }


class BaseVersionManager(ABC):
    """
    统一版本管理基类

    抽象了版本生成、比较、哈希计算等核心功能，
    基于现有的版本管理逻辑实现。
    """

    def __init__(self, version_file: str | None = None):
        """
        初始化版本管理器

        Args:
            version_file: 版本记录文件路径（可选）
        """
        self.version_file = Path(version_file) if version_file else None
        self.versions: dict[str, str] = {}

        if self.version_file:
            self.version_file.parent.mkdir(parents=True, exist_ok=True)
            self.versions = self._load_versions()

    def _load_versions(self) -> dict[str, str]:
        """加载版本记录"""
        if not self.version_file or not self.version_file.exists():
            return {}

        try:
            with open(self.version_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"加载版本记录失败: {e}")
            return {}

    def _save_versions(self):
        """保存版本记录"""
        if not self.version_file:
            return

        try:
            with open(self.version_file, "w", encoding="utf-8") as f:
                json.dump(self.versions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存版本记录失败: {e}")

    def calculate_data_hash(self, data: dict[str, Any] | str, algorithm: str = "md5") -> str:
        """
        计算数据哈希值

        Args:
            data: 要计算哈希的数据
            algorithm: 哈希算法 ("md5", "sha256")

        Returns:
            str: 哈希值
        """
        try:
            if isinstance(data, dict):
                # 确保字典键排序，保证一致性
                data_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
            else:
                data_str = str(data)

            if algorithm == "sha256":
                return hashlib.sha256(data_str.encode("utf-8")).hexdigest()
            else:  # 默认使用md5
                return hashlib.md5(data_str.encode("utf-8")).hexdigest()

        except Exception as e:
            logger.error(f"计算数据哈希失败: {e}")
            # 返回基于时间戳的fallback哈希
            fallback_data = f"fallback_{time.perf_counter()}"
            return hashlib.md5(fallback_data.encode("utf-8")).hexdigest()

    def generate_version(self, data: dict[str, Any], use_timestamp: bool = True) -> str:
        """
        生成版本号

        基于现有版本生成逻辑：时间戳 + 数据哈希

        Args:
            data: 版本数据
            use_timestamp: 是否使用时间戳前缀

        Returns:
            str: 版本号
        """
        try:
            data_hash = self.calculate_data_hash(data)

            if use_timestamp:
                # 使用时间戳+哈希格式（类似degradation.py的实现）
                timestamp = int(time.time())
                return f"{timestamp}_{data_hash[:8]}"
            else:
                # 仅使用哈希（类似sync.py的实现）
                return data_hash

        except Exception as e:
            logger.error(f"生成版本号失败: {e}")
            # 返回基于时间戳的fallback版本
            return f"fallback_{int(time.time())}"

    def compare_versions(self, v1: str, v2: str) -> int:
        """
        比较版本号

        Args:
            v1: 版本1
            v2: 版本2

        Returns:
            int: -1(v1<v2), 0(相等), 1(v1>v2)
        """
        if v1 == v2:
            return 0

        # 尝试解析时间戳+哈希格式的版本号
        try:
            if "_" in v1 and "_" in v2:
                ts1 = int(v1.split("_")[0])
                ts2 = int(v2.split("_")[0])

                if ts1 < ts2:
                    return -1
                elif ts1 > ts2:
                    return 1
                else:
                    # 时间戳相同，比较哈希
                    hash1 = v1.split("_", 1)[1]
                    hash2 = v2.split("_", 1)[1]
                    return -1 if hash1 < hash2 else (1 if hash1 > hash2 else 0)
        except (ValueError, IndexError):
            pass

        # 回退到字符串比较
        return -1 if v1 < v2 else (1 if v1 > v2 else 0)

    def get_current_version(self, key: str) -> str | None:
        """获取当前记录的版本"""
        return self.versions.get(key)

    def update_version_record(self, key: str, version: str):
        """更新版本记录"""
        self.versions[key] = version
        self._save_versions()
        logger.debug(f"更新版本记录: {key} -> {version}")

    def is_version_changed(self, key: str, current_data: dict[str, Any]) -> bool:
        """检查版本是否发生变化"""
        current_version = self.calculate_version(key, current_data)
        recorded_version = self.get_current_version(key)
        return current_version != recorded_version

    @abstractmethod
    def calculate_version(self, key: str, data: dict[str, Any]) -> str:
        """
        计算特定数据的版本号

        子类需要实现此方法来定义具体的版本计算逻辑

        Args:
            key: 数据键
            data: 数据内容

        Returns:
            str: 版本号
        """
        pass

    @abstractmethod
    def calculate_changes(self, from_version: str, to_version: str, **kwargs) -> list[ChangeItem]:
        """
        计算版本间的变更

        子类需要实现此方法来定义具体的变更计算逻辑

        Args:
            from_version: 源版本
            to_version: 目标版本
            **kwargs: 额外参数

        Returns:
            List[ChangeItem]: 变更列表
        """
        pass


class RuleSyncVersionManager(BaseVersionManager):
    """
    规则同步版本管理器

    专门用于规则同步的版本管理，实现增量变更计算和版本兼容性检查
    """

    def __init__(self, version_file: str = "data/rule_sync_versions.json"):
        """
        初始化规则同步版本管理器

        Args:
            version_file: 版本记录文件路径
        """
        super().__init__(version_file)
        self.rule_data_cache: dict[str, dict[str, Any]] = {}

    def calculate_version(self, key: str, data: dict[str, Any]) -> str:
        """
        计算规则数据的版本号

        Args:
            key: 规则键
            data: 规则数据

        Returns:
            str: 版本号
        """
        try:
            # 构建版本数据（类似TemplateVersionManager的实现）
            version_data = {
                "key": key,
                "data_hash": self.calculate_data_hash(data),
                "timestamp": time.time()
            }

            return self.generate_version(version_data, use_timestamp=True)

        except Exception as e:
            logger.error(f"计算规则版本失败: key={key}, error={e}")
            return self.generate_version({"key": key, "fallback": True})

    def calculate_changes(self, from_version: str, to_version: str, **kwargs) -> list[ChangeItem]:
        """
        计算版本间的变更

        Args:
            from_version: 源版本
            to_version: 目标版本
            **kwargs: 额外参数（如rule_keys, data_source等）

        Returns:
            List[ChangeItem]: 变更列表
        """
        changes = []

        try:
            logger.info(f"计算版本变更: {from_version} -> {to_version}")

            # 获取可选参数
            rule_keys = kwargs.get('rule_keys')
            data_source = kwargs.get('data_source')

            # 如果版本相同，无变更
            if from_version == to_version:
                logger.debug("版本相同，无变更")
                return changes

            # 获取版本数据快照
            from_data = self._get_version_snapshot(from_version, data_source)
            to_data = self._get_version_snapshot(to_version, data_source)

            # 如果指定了规则键，只比较指定的规则
            if rule_keys:
                from_filtered = {k: v for k, v in from_data.items() if k in rule_keys}
                to_filtered = {k: v for k, v in to_data.items() if k in rule_keys}
            else:
                from_filtered = from_data
                to_filtered = to_data

            # 计算变更
            changes.extend(self._calculate_data_changes(from_filtered, to_filtered))

            logger.info(f"版本变更计算完成: {len(changes)} 个变更")

        except Exception as e:
            logger.error(f"计算版本变更失败: {from_version} -> {to_version}, error={e}")

        return changes

    def _get_version_snapshot(self, version: str, data_source=None) -> dict[str, Any]:
        """
        获取版本数据快照

        Args:
            version: 版本号
            data_source: 数据源（可选）

        Returns:
            Dict[str, Any]: 版本数据快照
        """
        try:
            # 如果是当前版本，从缓存获取
            if version in self.rule_data_cache:
                return self.rule_data_cache[version]

            # 对于历史版本，这里需要从持久化存储加载
            # 在实际实现中，可能需要从数据库或文件系统加载历史快照
            logger.warning(f"历史版本快照暂未实现: {version}")

            # 返回空数据作为fallback
            return {}

        except Exception as e:
            logger.error(f"获取版本快照失败: version={version}, error={e}")
            return {}

    def _calculate_data_changes(self, from_data: dict[str, Any], to_data: dict[str, Any]) -> list[ChangeItem]:
        """
        计算数据变更

        Args:
            from_data: 源数据
            to_data: 目标数据

        Returns:
            List[ChangeItem]: 变更列表
        """
        changes = []

        try:
            all_keys = set(from_data.keys()) | set(to_data.keys())

            for key in all_keys:
                old_value = from_data.get(key)
                new_value = to_data.get(key)

                if old_value is None and new_value is not None:
                    # 新增
                    changes.append(ChangeItem(
                        operation="CREATE",
                        key=key,
                        old_value=None,
                        new_value=new_value,
                        metadata={"change_type": "create", "timestamp": time.time()}
                    ))
                elif old_value is not None and new_value is None:
                    # 删除
                    changes.append(ChangeItem(
                        operation="DELETE",
                        key=key,
                        old_value=old_value,
                        new_value=None,
                        metadata={"change_type": "delete", "timestamp": time.time()}
                    ))
                elif old_value != new_value:
                    # 修改
                    changes.append(ChangeItem(
                        operation="UPDATE",
                        key=key,
                        old_value=old_value,
                        new_value=new_value,
                        metadata={"change_type": "update", "timestamp": time.time()}
                    ))

        except Exception as e:
            logger.error(f"计算数据变更失败: {e}")

        return changes

    def cache_version_data(self, version: str, data: dict[str, Any]):
        """
        缓存版本数据快照

        Args:
            version: 版本号
            data: 版本数据
        """
        try:
            self.rule_data_cache[version] = data.copy()
            logger.debug(f"缓存版本数据: {version}, {len(data)} 项")

            # 限制缓存大小，保留最近10个版本
            if len(self.rule_data_cache) > 10:
                # 按版本时间戳排序，删除最旧的
                sorted_versions = sorted(
                    self.rule_data_cache.keys(),
                    key=lambda v: self._extract_timestamp(v)
                )
                for old_version in sorted_versions[:-10]:
                    del self.rule_data_cache[old_version]
                    logger.debug(f"清理旧版本缓存: {old_version}")

        except Exception as e:
            logger.error(f"缓存版本数据失败: version={version}, error={e}")

    def _extract_timestamp(self, version: str) -> float:
        """
        从版本号提取时间戳

        Args:
            version: 版本号

        Returns:
            float: 时间戳
        """
        try:
            if "_" in version:
                return float(version.split("_")[0])
            else:
                # 纯哈希版本，返回0作为默认值
                return 0.0
        except (ValueError, IndexError):
            return 0.0

    def is_version_compatible(self, version: str) -> bool:
        """
        检查版本兼容性

        Args:
            version: 要检查的版本号

        Returns:
            bool: 是否兼容
        """
        try:
            # 检查版本格式是否正确
            if "_" in version:
                timestamp_str, hash_part = version.split("_", 1)
                timestamp = int(timestamp_str)

                # 检查时间戳是否合理（不能太旧或太新）
                current_time = int(time.time())
                time_diff = abs(current_time - timestamp)

                # 允许1年的时间差
                max_time_diff = 365 * 24 * 3600

                return time_diff <= max_time_diff and len(hash_part) >= 8
            else:
                # 纯哈希格式也认为是兼容的
                return len(version) >= 8

        except (ValueError, IndexError):
            logger.warning(f"版本格式不正确: {version}")
            return False

    def cache_rule_data(self, key: str, data: dict[str, Any]):
        """缓存规则数据用于变更计算"""
        self.rule_data_cache[key] = data.copy()

    def get_cached_rule_data(self, key: str) -> dict[str, Any] | None:
        """获取缓存的规则数据"""
        return self.rule_data_cache.get(key)
