"""
性能统计API路由
提供超快速校验引擎的性能监控和统计信息
"""

from typing import Any

from fastapi import APIRouter

from core.logging.logging_system import log as logger
from models.api import ApiResponse

# 创建性能统计路由
performance_router = APIRouter(prefix="/api/v1/performance", tags=["Performance"])


@performance_router.get("/validation-stats", response_model=ApiResponse[dict[str, Any]])
async def get_validation_performance_stats():
    """
    获取校验性能统计信息
    包括超快速校验引擎的详细性能数据
    """
    try:
        from services.ultra_fast_rule_service import ultra_fast_rule_service

        # 获取超快速服务统计
        ultra_stats = ultra_fast_rule_service.get_ultra_performance_stats()

        return ApiResponse.success_response(
            data=ultra_stats,
            message="校验性能统计获取成功"
        )

    except Exception as e:
        logger.error(f"获取校验性能统计失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"获取性能统计失败: {str(e)}",
            error_code="PERFORMANCE_STATS_ERROR"
        )


@performance_router.get("/memory-stats", response_model=ApiResponse[dict[str, Any]])
async def get_memory_performance_stats():
    """
    获取内存使用统计信息
    """
    try:
        from core.memory_optimizer import memory_optimizer

        # 获取内存统计
        memory_stats = memory_optimizer.get_optimization_stats()

        return ApiResponse.success_response(
            data=memory_stats,
            message="内存统计获取成功"
        )

    except Exception as e:
        logger.error(f"获取内存统计失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"获取内存统计失败: {str(e)}",
            error_code="MEMORY_STATS_ERROR"
        )


@performance_router.get("/cache-stats", response_model=ApiResponse[dict[str, Any]])
async def get_cache_performance_stats():
    """
    获取缓存性能统计信息
    """
    try:
        from core.intelligent_cache import intelligent_cache

        # 获取缓存统计
        cache_stats = intelligent_cache.get_overall_stats()

        cache_summary = {}
        for cache_name, stats in cache_stats.items():
            cache_summary[cache_name] = {
                "hit_rate": stats.hit_rate,
                "memory_usage_mb": stats.memory_usage_mb,
                "total_requests": stats.total_requests,
                "cache_hits": stats.cache_hits,
                "cache_misses": stats.cache_misses,
                "evictions": stats.evictions,
            }

        return ApiResponse.success_response(
            data=cache_summary,
            message="缓存统计获取成功"
        )

    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"获取缓存统计失败: {str(e)}",
            error_code="CACHE_STATS_ERROR"
        )


@performance_router.get("/rule-filter-stats", response_model=ApiResponse[dict[str, Any]])
async def get_rule_filter_stats():
    """
    获取规则筛选统计信息
    """
    try:
        from services.rule_filtering_service import rule_filter

        # 获取规则筛选统计
        filter_stats = rule_filter.get_filter_statistics()

        return ApiResponse.success_response(
            data=filter_stats,
            message="规则筛选统计获取成功"
        )

    except Exception as e:
        logger.error(f"获取规则筛选统计失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"获取规则筛选统计失败: {str(e)}",
            error_code="FILTER_STATS_ERROR"
        )


@performance_router.get("/preprocessing-stats", response_model=ApiResponse[dict[str, Any]])
async def get_preprocessing_stats():
    """
    获取数据预处理统计信息
    """
    try:
        from services.patient_data_preprocessor import patient_preprocessor

        # 获取预处理统计
        preprocess_stats = patient_preprocessor.get_preprocessing_stats()

        return ApiResponse.success_response(
            data=preprocess_stats,
            message="数据预处理统计获取成功"
        )

    except Exception as e:
        logger.error(f"获取数据预处理统计失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"获取数据预处理统计失败: {str(e)}",
            error_code="PREPROCESSING_STATS_ERROR"
        )


@performance_router.get("/prefilter-stats", response_model=ApiResponse[dict[str, Any]])
async def get_prefilter_performance_stats():
    """
    获取规则预过滤性能统计信息
    包括过滤效果、性能指标、健康状态等
    """
    try:
        from core.rule_prefilter import rule_prefilter

        # 获取预过滤统计
        prefilter_stats = rule_prefilter.get_performance_stats()

        # 添加健康状态检查
        prefilter_stats["health_status"] = {
            "is_healthy": rule_prefilter.is_healthy(),
            "recommendations": rule_prefilter._get_health_recommendations(),
        }

        return ApiResponse.success_response(data=prefilter_stats, message="规则预过滤统计获取成功")

    except Exception as e:
        logger.error(f"获取规则预过滤统计失败: {e}", exc_info=True)
        return ApiResponse.error_response(message=f"获取预过滤统计失败: {str(e)}", error_code="PREFILTER_STATS_ERROR")


@performance_router.get("/comprehensive-stats", response_model=ApiResponse[dict[str, Any]])
async def get_comprehensive_performance_stats():
    """
    获取综合性能统计信息
    包含所有组件的性能数据
    """
    try:
        comprehensive_stats = {}

        # 校验性能统计
        try:
            from services.ultra_fast_rule_service import ultra_fast_rule_service

            comprehensive_stats["validation"] = ultra_fast_rule_service.get_ultra_performance_stats()
        except Exception as e:
            logger.warning(f"获取校验统计失败: {e}")
            comprehensive_stats["validation"] = {"error": str(e)}

        # 规则预过滤统计
        try:
            from core.rule_prefilter import rule_prefilter

            prefilter_stats = rule_prefilter.get_performance_stats()
            prefilter_stats["health_status"] = {
                "is_healthy": rule_prefilter.is_healthy(),
                "recommendations": rule_prefilter._get_health_recommendations(),
            }
            comprehensive_stats["prefilter"] = prefilter_stats
        except Exception as e:
            logger.warning(f"获取规则预过滤统计失败: {e}")
            comprehensive_stats["prefilter"] = {"error": str(e)}

        # 内存统计
        try:
            from core.memory_optimizer import memory_optimizer

            comprehensive_stats["memory"] = memory_optimizer.get_optimization_stats()
        except Exception as e:
            logger.warning(f"获取内存统计失败: {e}")
            comprehensive_stats["memory"] = {"error": str(e)}

        # 缓存统计
        try:
            from core.intelligent_cache import intelligent_cache

            cache_stats = intelligent_cache.get_overall_stats()
            comprehensive_stats["cache"] = {
                name: {
                    "hit_rate": stats.hit_rate,
                    "memory_usage_mb": stats.memory_usage_mb,
                    "total_requests": stats.total_requests,
                }
                for name, stats in cache_stats.items()
            }
        except Exception as e:
            logger.warning(f"获取缓存统计失败: {e}")
            comprehensive_stats["cache"] = {"error": str(e)}

        # 规则筛选统计
        try:
            from services.rule_filtering_service import rule_filter

            comprehensive_stats["rule_filter"] = rule_filter.get_filter_statistics()
        except Exception as e:
            logger.warning(f"获取规则筛选统计失败: {e}")
            comprehensive_stats["rule_filter"] = {"error": str(e)}

        # 数据预处理统计
        try:
            from services.patient_data_preprocessor import patient_preprocessor

            comprehensive_stats["preprocessing"] = patient_preprocessor.get_preprocessing_stats()
        except Exception as e:
            logger.warning(f"获取预处理统计失败: {e}")
            comprehensive_stats["preprocessing"] = {"error": str(e)}

        return ApiResponse.success_response(
            data=comprehensive_stats,
            message="综合性能统计获取成功"
        )

    except Exception as e:
        logger.error(f"获取综合性能统计失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"获取综合性能统计失败: {str(e)}",
            error_code="COMPREHENSIVE_STATS_ERROR"
        )


@performance_router.post("/optimize-memory", response_model=ApiResponse[dict[str, Any]])
async def optimize_memory(aggressive: bool = False):
    """
    手动触发内存优化

    Args:
        aggressive: 是否执行激进的内存优化
    """
    try:
        from core.memory_optimizer import memory_optimizer

        # 执行内存优化
        optimization_result = memory_optimizer.optimize_memory(aggressive=aggressive)

        return ApiResponse.success_response(
            data=optimization_result, message=f"内存优化完成（aggressive={aggressive}）"
        )

    except Exception as e:
        logger.error(f"内存优化失败: {e}", exc_info=True)
        return ApiResponse.error_response(message=f"内存优化失败: {str(e)}", error_code="MEMORY_OPTIMIZATION_ERROR")


@performance_router.post("/reset-prefilter-stats", response_model=ApiResponse[dict[str, Any]])
async def reset_prefilter_stats():
    """
    重置规则预过滤统计信息
    用于清除历史统计数据，重新开始监控
    """
    try:
        from core.rule_prefilter import rule_prefilter

        # 重置统计信息
        rule_prefilter.reset_stats()

        return ApiResponse.success_response(data={"message": "规则预过滤统计信息已重置"}, message="统计信息重置成功")

    except Exception as e:
        logger.error(f"重置规则预过滤统计失败: {e}", exc_info=True)
        return ApiResponse.error_response(message=f"重置统计信息失败: {str(e)}", error_code="RESET_PREFILTER_STATS_ERROR")


@performance_router.get("/startup-status", response_model=ApiResponse[dict[str, Any]])
async def get_startup_status():
    """
    获取启动状态和优化信息
    """
    try:
        from services.application_startup import app_startup

        startup_status = app_startup.get_startup_status()

        return ApiResponse.success_response(data=startup_status, message="启动状态获取成功")

    except Exception as e:
        logger.error(f"获取启动状态失败: {e}", exc_info=True)
        return ApiResponse.error_response(message=f"获取启动状态失败: {str(e)}", error_code="STARTUP_STATUS_ERROR")


@performance_router.get("/warmup-status", response_model=ApiResponse[dict[str, Any]])
async def get_warmup_status():
    """
    获取缓存预热状态
    """
    try:
        from services.warmup_manager import warmup_manager

        warmup_status = warmup_manager.get_warmup_status()

        return ApiResponse.success_response(data=warmup_status, message="预热状态获取成功")

    except Exception as e:
        logger.error(f"获取预热状态失败: {e}", exc_info=True)
        return ApiResponse.error_response(message=f"获取预热状态失败: {str(e)}", error_code="WARMUP_STATUS_ERROR")


@performance_router.post("/manual-warmup", response_model=ApiResponse[dict[str, Any]])
async def manual_warmup():
    """
    手动触发缓存预热
    """
    try:
        from services.warmup_manager import warmup_manager

        result = await warmup_manager.manual_warmup()

        return ApiResponse.success_response(data=result, message="手动预热完成")

    except Exception as e:
        logger.error(f"手动预热失败: {e}", exc_info=True)
        return ApiResponse.error_response(message=f"手动预热失败: {str(e)}", error_code="MANUAL_WARMUP_ERROR")


@performance_router.post("/manual-startup-optimization", response_model=ApiResponse[dict[str, Any]])
async def manual_startup_optimization():
    """
    手动触发启动优化
    """
    try:
        from services.startup_optimizer import startup_optimizer

        result = await startup_optimizer.manual_optimize()

        return ApiResponse.success_response(data=result, message="手动启动优化完成")

    except Exception as e:
        logger.error(f"手动启动优化失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"手动启动优化失败: {str(e)}",
            error_code="MANUAL_STARTUP_OPTIMIZATION_ERROR"
        )


@performance_router.post("/clear-cache", response_model=ApiResponse[dict[str, Any]])
async def clear_cache(cache_type: str = "all"):
    """
    清理缓存

    Args:
        cache_type: 缓存类型 (all, rule_result, patient_hash, computation)
    """
    try:
        from core.intelligent_cache import intelligent_cache

        if cache_type == "all":
            intelligent_cache.clear_all()
            message = "所有缓存已清理"
        elif cache_type == "rule_result":
            intelligent_cache.rule_result_cache.clear()
            message = "规则结果缓存已清理"
        elif cache_type == "patient_hash":
            intelligent_cache.patient_hash_cache.clear()
            message = "患者哈希缓存已清理"
        elif cache_type == "computation":
            intelligent_cache.computation_cache.clear()
            message = "计算结果缓存已清理"
        else:
            raise ValueError(f"不支持的缓存类型: {cache_type}")

        return ApiResponse.success_response(
            data={"cache_type": cache_type, "cleared": True},
            message=message
        )

    except Exception as e:
        logger.error(f"清理缓存失败: {e}", exc_info=True)
        return ApiResponse.error_response(
            message=f"清理缓存失败: {str(e)}",
            error_code="CACHE_CLEAR_ERROR"
        )
