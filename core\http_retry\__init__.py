"""
HTTP重试机制模块
提供指数退避重试算法、断路器模式和HTTP客户端重试装饰器
"""

from .backoff import (
    BackoffConfig,
    BackoffStrategy,
    CustomBackoff,
    ExponentialBackoff,
    FixedBackoff,
    LinearBackoff,
    calculate_backoff_delay,
)
from .circuit_breaker import (
    CircuitBreakerConfig,
    CircuitBreakerManager,
    CircuitBreakerState,
    SmartCircuitBreaker,
    get_circuit_breaker_manager,
)
from .retry_client import CircuitBreakerOpenError, RetryClient, RetryExhaustedError
from .retry_decorators import retry_on_exception, sync_retry_on_exception

__all__ = [
    # 退避策略
    "BackoffStrategy",
    "BackoffConfig",
    "calculate_backoff_delay",
    # 退避算法实现
    "ExponentialBackoff",
    "LinearBackoff",
    "FixedBackoff",
    "CustomBackoff",
    # 智能断路器
    "CircuitBreakerState",
    "CircuitBreakerConfig",
    "SmartCircuitBreaker",
    "CircuitBreakerManager",
    "get_circuit_breaker_manager",
    # HTTP重试客户端
    "RetryClient",
    "RetryExhaustedError",
    "CircuitBreakerOpenError",
    # 重试装饰器
    "retry_on_exception",
    "sync_retry_on_exception",
]

__version__ = "1.0.0"
__author__ = "CQ-008 Team"
__description__ = "HTTP重试机制模块，支持指数退避、断路器和智能重试"
