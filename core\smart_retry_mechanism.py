"""
智能重试机制模块
实现指数退避重试策略，提升系统容错能力

特点：
- 指数退避算法，避免雪崩效应
- 随机抖动，防止请求集中
- 根据异常类型选择重试策略
- 详细的重试指标收集
- 支持自定义重试条件
"""

import asyncio
import random
import time
from collections.abc import Callable
from dataclasses import dataclass, field
from typing import Any

from core.logging.logging_system import log as logger


@dataclass
class RetryMetrics:
    """重试指标"""
    total_attempts: int = 0
    total_successes: int = 0
    total_failures: int = 0
    total_retry_time_ms: float = 0.0
    max_attempts_used: int = 0
    exception_counts: dict[str, int] = field(default_factory=dict)

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_attempts == 0:
            return 0.0
        return self.total_successes / self.total_attempts

    @property
    def avg_retry_time_ms(self) -> float:
        """平均重试时间"""
        if self.total_failures == 0:
            return 0.0
        return self.total_retry_time_ms / self.total_failures

    def reset(self):
        """重置所有指标"""
        self.total_attempts = 0
        self.total_successes = 0
        self.total_failures = 0
        self.total_retry_time_ms = 0.0
        self.max_attempts_used = 0
        self.exception_counts.clear()


class RetryConfig:
    """重试配置"""

    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter_range: float = 0.1,
        timeout_per_attempt: float | None = None
    ):
        """
        初始化重试配置

        Args:
            max_attempts: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            exponential_base: 指数退避基数
            jitter_range: 随机抖动范围（0-1）
            timeout_per_attempt: 每次尝试的超时时间
        """
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter_range = jitter_range
        self.timeout_per_attempt = timeout_per_attempt

        # 可重试的异常类型
        self.retryable_exceptions = {
            ConnectionError,
            TimeoutError,
            OSError,
            RuntimeError
        }

        # 不可重试的异常类型
        self.non_retryable_exceptions = {
            ValueError,
            TypeError,
            AttributeError,
            KeyError
        }

    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """
        判断是否应该重试

        Args:
            exception: 发生的异常
            attempt: 当前尝试次数

        Returns:
            bool: 是否应该重试
        """
        if attempt >= self.max_attempts:
            return False

        # 检查异常类型
        exception_type = type(exception)

        # 明确不可重试的异常
        if any(issubclass(exception_type, exc) for exc in self.non_retryable_exceptions):
            return False

        # 明确可重试的异常
        if any(issubclass(exception_type, exc) for exc in self.retryable_exceptions):
            return True

        # 默认不重试未知异常
        return False

    def calculate_delay(self, attempt: int) -> float:
        """
        计算延迟时间

        Args:
            attempt: 当前尝试次数（从1开始）

        Returns:
            float: 延迟时间（秒）
        """
        # 指数退避计算
        delay = self.base_delay * (self.exponential_base ** (attempt - 1))

        # 限制最大延迟
        delay = min(delay, self.max_delay)

        # 添加随机抖动
        jitter = delay * self.jitter_range * (random.random() * 2 - 1)
        delay += jitter

        # 确保延迟不为负数
        return max(0.1, delay)


class SmartRetryMechanism:
    """
    智能重试机制

    实现特点：
    1. 指数退避算法
    2. 随机抖动防止请求集中
    3. 异常类型感知
    4. 性能指标收集
    5. 灵活的配置选项
    """

    def __init__(self, default_config: RetryConfig | None = None):
        """
        初始化智能重试机制

        Args:
            default_config: 默认重试配置
        """
        self.default_config = default_config or RetryConfig()
        self._metrics = RetryMetrics()

        logger.debug("智能重试机制初始化完成")

    @property
    def metrics(self) -> RetryMetrics:
        """获取重试指标"""
        return self._metrics

    def reset_metrics(self):
        """重置重试指标"""
        self._metrics.reset()

    async def execute_with_retry(
        self,
        func: Callable[..., Any],
        *args,
        config: RetryConfig | None = None,
        **kwargs
    ) -> Any:
        """
        执行函数并进行智能重试

        Args:
            func: 要执行的函数
            *args: 函数参数
            config: 重试配置，如果为None则使用默认配置
            **kwargs: 函数关键字参数

        Returns:
            Any: 函数执行结果

        Raises:
            Exception: 最后一次执行的异常
        """
        retry_config = config or self.default_config
        start_time = time.perf_counter()
        last_exception = None

        for attempt in range(1, retry_config.max_attempts + 1):
            self._metrics.total_attempts += 1
            self._metrics.max_attempts_used = max(self._metrics.max_attempts_used, attempt)

            try:
                # 设置单次尝试超时
                if retry_config.timeout_per_attempt:
                    result = await asyncio.wait_for(
                        func(*args, **kwargs),
                        timeout=retry_config.timeout_per_attempt
                    )
                else:
                    result = await func(*args, **kwargs)

                # 成功执行
                self._metrics.total_successes += 1
                if attempt > 1:
                    retry_time = (time.perf_counter() - start_time) * 1000
                    self._metrics.total_retry_time_ms += retry_time
                    logger.info(f"重试成功，第{attempt}次尝试，总耗时: {retry_time:.2f}ms")

                return result

            except Exception as e:
                last_exception = e
                exception_name = type(e).__name__

                # 更新异常统计
                self._metrics.exception_counts[exception_name] = (
                    self._metrics.exception_counts.get(exception_name, 0) + 1
                )

                # 判断是否应该重试
                if not retry_config.should_retry(e, attempt):
                    logger.warning(f"异常不可重试或达到最大重试次数: {e}")
                    break

                # 计算延迟时间
                if attempt < retry_config.max_attempts:
                    delay = retry_config.calculate_delay(attempt)
                    logger.warning(
                        f"第{attempt}次尝试失败: {e}，{delay:.2f}秒后重试"
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"达到最大重试次数，最后一次异常: {e}")

        # 所有重试都失败了
        self._metrics.total_failures += 1
        total_retry_time = (time.perf_counter() - start_time) * 1000
        self._metrics.total_retry_time_ms += total_retry_time

        logger.error(
            f"重试机制执行失败，总尝试次数: {retry_config.max_attempts}，"
            f"总耗时: {total_retry_time:.2f}ms"
        )

        # 抛出最后一次的异常
        if last_exception:
            raise last_exception
        else:
            raise RuntimeError("重试机制执行失败，但没有捕获到异常")

    def create_db_retry_config(self) -> RetryConfig:
        """创建数据库操作专用的重试配置"""
        config = RetryConfig(
            max_attempts=3,
            base_delay=1.0,
            max_delay=30.0,
            exponential_base=2.0,
            jitter_range=0.2,
            timeout_per_attempt=60.0
        )

        # 数据库相关的可重试异常
        config.retryable_exceptions.update({
            # SQLAlchemy相关异常
            Exception,  # 暂时包含所有异常，实际使用时应该更具体
        })

        return config

    def create_index_build_retry_config(self) -> RetryConfig:
        """创建索引构建专用的重试配置"""
        config = RetryConfig(
            max_attempts=2,  # 索引构建比较重，减少重试次数
            base_delay=5.0,  # 较长的基础延迟
            max_delay=60.0,
            exponential_base=2.0,
            jitter_range=0.1,
            timeout_per_attempt=120.0  # 索引构建需要更长时间
        )

        return config

    def get_status(self) -> dict[str, Any]:
        """获取重试机制状态"""
        return {
            "metrics": {
                "total_attempts": self._metrics.total_attempts,
                "total_successes": self._metrics.total_successes,
                "total_failures": self._metrics.total_failures,
                "success_rate": self._metrics.success_rate,
                "avg_retry_time_ms": self._metrics.avg_retry_time_ms,
                "max_attempts_used": self._metrics.max_attempts_used,
                "exception_counts": dict(self._metrics.exception_counts)
            },
            "default_config": {
                "max_attempts": self.default_config.max_attempts,
                "base_delay": self.default_config.base_delay,
                "max_delay": self.default_config.max_delay,
                "exponential_base": self.default_config.exponential_base,
                "jitter_range": self.default_config.jitter_range
            }
        }


# 全局重试机制实例
smart_retry = SmartRetryMechanism()
