import asyncio
import os
import uuid

import httpx

from config.settings import settings
from core.constants.error_codes import ErrorCodes
from core.logging.error_enhancement import error_log_enhancer
from core.logging.logging_system import log as logger
from core.utils.error_recovery import error_recovery_manager
from services.rule_loader import load_rules_from_file

# --- Constants ---
# Path to store the local copy of the rules package
LOCAL_RULES_PATH = "rules_cache.json.gz"
LOCAL_VERSION_PATH = "rules_version.txt"


class RuleSyncService:
    def __init__(self, loop: asyncio.AbstractEventLoop):
        self._loop = loop
        self._task: asyncio.Task = None
        self._sync_enabled = settings.ENABLE_RULE_SYNC
        self._sync_interval = settings.RULE_SYNC_INTERVAL
        self._sync_timeout = settings.RULE_SYNC_TIMEOUT
        self._max_retries = settings.RULE_SYNC_MAX_RETRIES
        self._retry_interval = settings.RULE_SYNC_RETRY_INTERVAL

        # 只有在同步启用时才创建HTTP客户端
        if self._sync_enabled:
            if not settings.MASTER_API_ENDPOINT:
                logger.error("MASTER_API_ENDPOINT is required when ENABLE_RULE_SYNC is True")
                self._sync_enabled = False
            elif not settings.SLAVE_API_KEY:
                logger.error("SLAVE_API_KEY is required when ENABLE_RULE_SYNC is True")
                self._sync_enabled = False
            else:
                self.client = httpx.AsyncClient(
                    base_url=settings.MASTER_API_ENDPOINT,
                    headers={"X-API-KEY": settings.SLAVE_API_KEY},
                    # proxies=settings.PROXY_URL,
                    timeout=self._sync_timeout,
                )
        else:
            self.client = None
            logger.info("Rule synchronization is disabled (offline mode)")

    async def start(self):
        """Starts the background synchronization task."""
        if not self._sync_enabled:
            logger.info("RuleSyncService is disabled (offline mode)")
            return

        logger.info("Starting RuleSyncService...")
        self._task = self._loop.create_task(self._sync_periodically())

    async def stop(self):
        """Stops the background synchronization task."""
        logger.info("Stopping RuleSyncService...")
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        if self.client:
            await self.client.aclose()
        logger.info("RuleSyncService stopped.")

    def _get_local_version(self) -> str:
        """Reads the version of the locally cached rules."""
        if not os.path.exists(LOCAL_VERSION_PATH):
            return "initial"
        with open(LOCAL_VERSION_PATH, "r") as f:
            return f.read().strip()

    async def _sync_periodically(self):
        """The main loop that runs periodically to check for rule updates."""
        if not self._sync_enabled:
            logger.info("Sync is disabled, exiting sync loop")
            return

        await asyncio.sleep(5)  # Initial delay
        retry_count = 0

        while True:
            try:
                logger.info("Checking for rule updates from master node...")
                local_version = self._get_local_version()

                # 1. Get remote version
                response = await self.client.get("/api/v1/rules/version")
                response.raise_for_status()
                response_data = response.json()

                # 处理统一响应格式
                if "data" in response_data and "version" in response_data["data"]:
                    remote_version = response_data["data"]["version"]
                elif "version" in response_data:
                    remote_version = response_data["version"]
                else:
                    logger.error(f"Invalid response format from master: {response_data}")
                    raise ValueError("Invalid response format: missing version field")

                # 2. Compare versions
                if remote_version == local_version:
                    logger.info("Rules are up to date.")
                    retry_count = 0  # 重置重试计数
                else:
                    logger.info(f"New rule version detected: {remote_version}. Downloading package...")
                    # 3. Download new package
                    await self.download_and_apply_rules(remote_version)
                    retry_count = 0  # 重置重试计数

            except httpx.HTTPStatusError as e:
                # 特殊处理认证错误
                if e.response.status_code == 401:
                    logger.error(
                        f"Authentication failed: API key mismatch. "
                        f"Check SLAVE_API_KEY configuration. "
                        f"Response: {e.response.text}"
                    )
                else:
                    logger.error(f"HTTP error during sync: {e.response.status_code} - {e.response.text}")

                # 增强错误日志记录
                sync_request_id = f"sync-{uuid.uuid4()}"
                error_log_enhancer.enhance_error_logging(
                    error=e,
                    request_id=sync_request_id,
                    endpoint="rule_sync",
                    method="GET",
                    user_context={
                        "sync_type": "periodic_sync",
                        "status_code": e.response.status_code,
                        "response_text": e.response.text[:500],  # 限制长度
                        "is_auth_error": e.response.status_code == 401,
                    },
                )

                # 尝试错误恢复
                await error_recovery_manager.handle_error_with_recovery(
                    error=e,
                    error_code=ErrorCodes.RULE_SYNC_FAILED,
                    context={"request_id": sync_request_id, "operation": "rule_sync"},
                )

            except Exception as e:
                logger.error(f"An unexpected error occurred during sync: {e}", exc_info=True)
                # 增强错误日志记录
                sync_request_id = f"sync-{uuid.uuid4()}"
                error_log_enhancer.enhance_error_logging(
                    error=e,
                    request_id=sync_request_id,
                    endpoint="rule_sync",
                    method="SYNC",
                    user_context={
                        "sync_type": "periodic_sync",
                        "operation": "unexpected_error",
                    },
                )

                # 尝试错误恢复
                await error_recovery_manager.handle_error_with_recovery(
                    error=e,
                    error_code=ErrorCodes.RULE_SYNC_FAILED,
                    context={"request_id": sync_request_id, "operation": "rule_sync"},
                )

            # 根据配置的间隔等待
            await asyncio.sleep(self._sync_interval)

    async def download_and_apply_rules(self, new_version: str):
        """Downloads, verifies, and applies the new rule package."""
        download_request_id = f"download-{uuid.uuid4()}"

        try:
            logger.info(f"Starting rule download: {download_request_id} - version {new_version}")

            # Download rules package
            response = await self.client.get("/api/v1/rules/export", timeout=120.0)
            response.raise_for_status()
            gzipped_package = response.content

            # TODO: Add more verification here (e.g., hash checking)

            # Securely write to disk
            temp_path = f"{LOCAL_RULES_PATH}.tmp"
            with open(temp_path, "wb") as f:
                f.write(gzipped_package)
            os.replace(temp_path, LOCAL_RULES_PATH)

            # Update local version file
            with open(LOCAL_VERSION_PATH, "w") as f:
                f.write(new_version)

            logger.info(f"Successfully downloaded and saved new rule package version: {new_version}")

            # Hot-reload the cache
            logger.info("Triggering hot-reload of rule cache from local file...")
            await load_rules_from_file()
            logger.info("Rule cache hot-reload completed.")

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error during rule download: {e.response.status_code} - {e.response.text}")
            # 增强错误日志记录
            error_log_enhancer.enhance_error_logging(
                error=e,
                request_id=download_request_id,
                endpoint="rule_download",
                method="GET",
                user_context={
                    "operation": "download_rules",
                    "version": new_version,
                    "status_code": e.response.status_code,
                    "response_text": e.response.text[:500],
                },
            )
            raise

        except OSError as e:
            logger.error(f"File system error during rule download: {e}")
            # 增强错误日志记录
            error_log_enhancer.enhance_error_logging(
                error=e,
                request_id=download_request_id,
                endpoint="rule_download",
                method="FILE",
                user_context={
                    "operation": "file_operations",
                    "version": new_version,
                    "temp_path": f"{LOCAL_RULES_PATH}.tmp",
                },
            )
            raise

        except Exception as e:
            logger.error("Unexpected error during rule download: {}", str(e), exc_info=True)
            # 增强错误日志记录
            error_log_enhancer.enhance_error_logging(
                error=e,
                request_id=download_request_id,
                endpoint="rule_download",
                method="DOWNLOAD",
                user_context={"operation": "download_and_apply", "version": new_version},
            )
            raise


def setup_sync_service(loop: asyncio.AbstractEventLoop) -> RuleSyncService:
    """Factory function to create and setup the sync service."""
    service = RuleSyncService(loop)
    return service
