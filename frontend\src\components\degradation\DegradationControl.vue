<template>
  <div class="degradation-control">
    <el-card shadow="hover">
      <template #header>
        <span class="title">手动控制</span>
      </template>

      <div class="control-content">
        <!-- 快速操作按钮 -->
        <div class="quick-actions">
          <el-button
            type="success"
            :icon="CircleCheck"
            :loading="operating.recover"
            :disabled="!isSystemDegraded || isAnyOperating"
            @click="showRecoverDialog"
          >
            恢复正常
          </el-button>

          <el-button
            type="warning"
            :icon="Warning"
            :loading="operating.trigger"
            :disabled="isAnyOperating"
            @click="showTriggerDialog"
          >
            触发降级
          </el-button>

          <el-button
            type="primary"
            :icon="Setting"
            :loading="operating.setLevel"
            :disabled="isAnyOperating"
            @click="showLevelDialog"
          >
            设置级别
          </el-button>
        </div>

        <!-- 当前状态提示 -->
        <div class="status-hint">
          <el-alert
            :title="getStatusHintTitle()"
            :description="getStatusHintDescription()"
            :type="getStatusHintType()"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </el-card>

    <!-- 恢复对话框 -->
    <el-dialog
      v-model="recoverDialogVisible"
      title="恢复正常状态"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="recoverForm" label-width="80px">
        <el-form-item label="恢复原因">
          <el-input
            v-model="recoverForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入恢复原因（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="recoverDialogVisible = false">取消</el-button>
        <el-button
          type="success"
          :loading="operating.recover"
          @click="executeRecover"
        >
          确认恢复
        </el-button>
      </template>
    </el-dialog>

    <!-- 触发降级对话框 -->
    <el-dialog
      v-model="triggerDialogVisible"
      title="触发降级"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="triggerForm" :rules="triggerRules" ref="triggerFormRef" label-width="80px">
        <el-form-item label="降级级别" prop="level">
          <el-select v-model="triggerForm.level" placeholder="请选择降级级别" style="width: 100%">
            <el-option
              v-for="level in degradationLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
              :disabled="level.value === 'normal'"
            >
              <div class="level-option">
                <span>{{ level.label }}</span>
                <el-tag :type="level.color" size="small">{{ level.description }}</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="降级原因" prop="reason">
          <el-input
            v-model="triggerForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入降级原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="triggerForm.force">
            强制执行（忽略当前状态检查）
          </el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="triggerDialogVisible = false">取消</el-button>
        <el-button
          type="warning"
          :loading="operating.trigger"
          @click="executeTrigger"
        >
          确认降级
        </el-button>
      </template>
    </el-dialog>

    <!-- 设置级别对话框 -->
    <el-dialog
      v-model="levelDialogVisible"
      title="设置降级级别"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="levelForm" :rules="levelRules" ref="levelFormRef" label-width="80px">
        <el-form-item label="目标级别" prop="level">
          <el-select v-model="levelForm.level" placeholder="请选择目标级别" style="width: 100%">
            <el-option
              v-for="level in allLevels"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            >
              <div class="level-option">
                <span>{{ level.label }}</span>
                <el-tag :type="level.color" size="small">{{ level.description }}</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="设置原因" prop="reason">
          <el-input
            v-model="levelForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入设置原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="持续时间">
          <el-input-number
            v-model="levelForm.duration"
            :min="0"
            :max="86400"
            :step="60"
            placeholder="持续时间（秒）"
            style="width: 100%"
          />
          <div class="form-hint">设置为0或留空表示永久生效，直到手动更改</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="levelDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="operating.setLevel"
          @click="executeSetLevel"
        >
          确认设置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessageBox } from 'element-plus'
import { CircleCheck, Warning, Setting } from '@element-plus/icons-vue'
import { useDegradationStore } from '../../stores/degradation'
import {
  DEGRADATION_LEVELS,
  DEGRADATION_LEVEL_NAMES,
  DEGRADATION_LEVEL_COLORS,
  formatDegradationLevel
} from '../../api/degradation'

// 使用降级状态 store
const degradationStore = useDegradationStore()

// 从 store 中获取状态和方法
const {
  currentStatus,
  operating,
  isSystemDegraded,
  isAnyOperating,
  triggerDegradationAction,
  recoverDegradationAction,
  setDegradationLevelAction
} = degradationStore

// 对话框显示状态
const recoverDialogVisible = ref(false)
const triggerDialogVisible = ref(false)
const levelDialogVisible = ref(false)

// 表单引用
const triggerFormRef = ref(null)
const levelFormRef = ref(null)

// 恢复表单
const recoverForm = ref({
  reason: '手动恢复操作'
})

// 触发降级表单
const triggerForm = ref({
  level: '',
  reason: '',
  force: false
})

// 设置级别表单
const levelForm = ref({
  level: '',
  reason: '',
  duration: null
})

// 降级级别选项（不包括正常状态）
const degradationLevels = computed(() => [
  {
    value: DEGRADATION_LEVELS.LIGHT,
    label: DEGRADATION_LEVEL_NAMES[DEGRADATION_LEVELS.LIGHT],
    color: DEGRADATION_LEVEL_COLORS[DEGRADATION_LEVELS.LIGHT],
    description: '轻度降级'
  },
  {
    value: DEGRADATION_LEVELS.MODERATE,
    label: DEGRADATION_LEVEL_NAMES[DEGRADATION_LEVELS.MODERATE],
    color: DEGRADATION_LEVEL_COLORS[DEGRADATION_LEVELS.MODERATE],
    description: '中度降级'
  },
  {
    value: DEGRADATION_LEVELS.SEVERE,
    label: DEGRADATION_LEVEL_NAMES[DEGRADATION_LEVELS.SEVERE],
    color: DEGRADATION_LEVEL_COLORS[DEGRADATION_LEVELS.SEVERE],
    description: '重度降级'
  }
])

// 所有级别选项（包括正常状态）
const allLevels = computed(() => [
  {
    value: DEGRADATION_LEVELS.NORMAL,
    label: DEGRADATION_LEVEL_NAMES[DEGRADATION_LEVELS.NORMAL],
    color: DEGRADATION_LEVEL_COLORS[DEGRADATION_LEVELS.NORMAL],
    description: '正常状态'
  },
  ...degradationLevels.value
])

// 表单验证规则
const triggerRules = {
  level: [
    { required: true, message: '请选择降级级别', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入降级原因', trigger: 'blur' },
    { min: 5, max: 200, message: '原因长度应在5-200字符之间', trigger: 'blur' }
  ]
}

const levelRules = {
  level: [
    { required: true, message: '请选择目标级别', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入设置原因', trigger: 'blur' },
    { min: 5, max: 200, message: '原因长度应在5-200字符之间', trigger: 'blur' }
  ]
}

// 获取状态提示标题
const getStatusHintTitle = () => {
  if (isSystemDegraded.value) {
    return `当前处于${formatDegradationLevel(currentStatus.current_level)}状态`
  }
  return '系统运行正常'
}

// 获取状态提示描述
const getStatusHintDescription = () => {
  if (isSystemDegraded.value) {
    const triggers = currentStatus.active_triggers.length > 0
      ? `，触发器：${currentStatus.active_triggers.join('、')}`
      : ''
    return `系统当前处于降级状态${triggers}。您可以手动恢复到正常状态或调整降级级别。`
  }
  return '系统当前运行正常。您可以手动触发降级进行测试或预防性维护。'
}

// 获取状态提示类型
const getStatusHintType = () => {
  if (isSystemDegraded.value) {
    return 'warning'
  }
  return 'success'
}

// 显示恢复对话框
const showRecoverDialog = () => {
  recoverForm.value.reason = '手动恢复操作'
  recoverDialogVisible.value = true
}

// 显示触发降级对话框
const showTriggerDialog = () => {
  triggerForm.value = {
    level: '',
    reason: '',
    force: false
  }
  triggerDialogVisible.value = true
}

// 显示设置级别对话框
const showLevelDialog = () => {
  levelForm.value = {
    level: currentStatus.current_level,
    reason: '',
    duration: null
  }
  levelDialogVisible.value = true
}

// 执行恢复操作
const executeRecover = async () => {
  try {
    await ElMessageBox.confirm(
      '确认要恢复系统到正常状态吗？此操作将取消所有降级措施。',
      '确认恢复',
      {
        type: 'warning',
        confirmButtonText: '确认恢复',
        cancelButtonText: '取消'
      }
    )

    const success = await recoverDegradationAction(recoverForm.value.reason)
    if (success) {
      recoverDialogVisible.value = false
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 执行触发降级操作
const executeTrigger = async () => {
  if (!triggerFormRef.value) return

  const valid = await triggerFormRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    const levelName = formatDegradationLevel(triggerForm.value.level)
    await ElMessageBox.confirm(
      `确认要将系统降级到${levelName}吗？此操作将影响系统性能。`,
      '确认降级',
      {
        type: 'warning',
        confirmButtonText: '确认降级',
        cancelButtonText: '取消'
      }
    )

    const success = await triggerDegradationAction(
      triggerForm.value.level,
      triggerForm.value.reason,
      triggerForm.value.force
    )

    if (success) {
      triggerDialogVisible.value = false
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 执行设置级别操作
const executeSetLevel = async () => {
  if (!levelFormRef.value) return

  const valid = await levelFormRef.value.validate().catch(() => false)
  if (!valid) return

  try {
    const levelName = formatDegradationLevel(levelForm.value.level)
    const durationText = levelForm.value.duration
      ? `，持续时间：${levelForm.value.duration}秒`
      : '，永久生效'

    await ElMessageBox.confirm(
      `确认要将系统级别设置为${levelName}吗？${durationText}`,
      '确认设置',
      {
        type: 'info',
        confirmButtonText: '确认设置',
        cancelButtonText: '取消'
      }
    )

    const success = await setDegradationLevelAction(
      levelForm.value.level,
      levelForm.value.reason,
      levelForm.value.duration
    )

    if (success) {
      levelDialogVisible.value = false
    }
  } catch (error) {
    // 用户取消操作
  }
}
</script>

<style scoped>
.degradation-control {
  width: 100%;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.control-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.quick-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.status-hint {
  margin-top: 10px;
}

.level-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.form-hint {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quick-actions {
    flex-direction: column;
  }

  .quick-actions .el-button {
    width: 100%;
  }
}
</style>
