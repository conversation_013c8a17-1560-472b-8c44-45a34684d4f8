"""
扩展同步API接口

包含增量同步、离线包管理等高级同步功能的API接口。
"""

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse

from api.dependencies.auth import get_api_key_dependency
from core.constants.error_codes import ErrorCodes
from core.logging.logging_system import log as logger
from core.middleware.request_tracking import request_tracker
from models.api import ApiResponse
from models.sync import (
    ChangesRequest,
    ChangesResponse,
    OfflinePackageRequest,
    PackageInfo,
    SyncAcknowledgment,
)
from services.offline_package_manager import get_offline_package_manager
from services.sync_coordinator import get_sync_coordinator

# 扩展同步路由
sync_extended_router = APIRouter(
    prefix="/api/v1/rules",
    tags=["Extended Rule Sync (Secure)"],
    dependencies=[get_api_key_dependency()],
)


@sync_extended_router.get("/sync/changes", response_model=ApiResponse[ChangesResponse])
async def get_incremental_changes(
    from_version: str,
    to_version: str = None,
    rule_keys: str = None,
    max_changes: int = 1000,
    request: Request = None,
):
    """
    获取增量变更

    Args:
        from_version: 源版本
        to_version: 目标版本（可选）
        rule_keys: 规则键列表，逗号分隔（可选）
        max_changes: 最大变更数量
        request: HTTP请求对象

    Returns:
        ApiResponse[ChangesResponse]: 变更响应
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"获取增量变更: {from_version} -> {to_version}, request_id={request_id}")

        # 解析规则键
        rule_keys_list = rule_keys.split(",") if rule_keys else None

        # 创建变更请求
        changes_request = ChangesRequest(
            from_version=from_version, to_version=to_version, rule_keys=rule_keys_list, max_changes=max_changes
        )

        # 添加请求跟踪事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "changes_request",
                {
                    "endpoint": "/api/v1/rules/sync/changes",
                    "from_version": from_version,
                    "to_version": to_version,
                    "rule_keys_count": len(rule_keys_list) if rule_keys_list else 0,
                },
            )

        # 获取同步协调器
        sync_coordinator = get_sync_coordinator()

        # 获取增量变更
        changes_response = await sync_coordinator.get_incremental_changes(changes_request)

        # 添加完成事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "changes_completed",
                {
                    "from_version": changes_response.from_version,
                    "to_version": changes_response.to_version,
                    "total_changes": changes_response.total_changes,
                    "has_more": changes_response.has_more,
                },
            )
            request_tracker.complete_request(request_id, success=True)

        return ApiResponse.success_response(data=changes_response, message="获取增量变更成功", request_id=request_id)

    except Exception as e:
        logger.error(f"获取增量变更失败: {e}", exc_info=True)

        # 添加错误事件
        if request_id:
            request_tracker.add_event(
                request_id, "changes_error", {"error": str(e), "from_version": from_version, "to_version": to_version}
            )
            request_tracker.complete_request(request_id, success=False)

        raise HTTPException(status_code=ErrorCodes.INTERNAL_SERVER_ERROR, detail="获取增量变更失败") from e


@sync_extended_router.post("/sync/acknowledge", response_model=ApiResponse[bool])
async def acknowledge_sync(acknowledgment: SyncAcknowledgment, request: Request):
    """
    确认同步完成

    Args:
        acknowledgment: 同步确认
        request: HTTP请求对象

    Returns:
        ApiResponse[bool]: 确认结果
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"确认同步完成: sync_id={acknowledgment.sync_id}, request_id={request_id}")

        # 添加请求跟踪事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "sync_acknowledge_request",
                {
                    "endpoint": "/api/v1/rules/sync/acknowledge",
                    "sync_id": acknowledgment.sync_id,
                    "node_id": acknowledgment.node_id,
                    "applied_version": acknowledgment.applied_version,
                },
            )

        # 获取同步协调器
        sync_coordinator = get_sync_coordinator()

        # 确认同步
        success = await sync_coordinator.acknowledge_sync(acknowledgment)

        # 添加完成事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "sync_acknowledge_completed",
                {
                    "sync_id": acknowledgment.sync_id,
                    "success": success,
                    "applied_changes": acknowledgment.applied_changes,
                },
            )
            request_tracker.complete_request(request_id, success=True)

        return ApiResponse.success_response(
            data=success, message="同步确认成功" if success else "同步确认失败", request_id=request_id
        )

    except Exception as e:
        logger.error(f"同步确认失败: {e}", exc_info=True)

        # 添加错误事件
        if request_id:
            request_tracker.add_event(
                request_id, "sync_acknowledge_error", {"error": str(e), "sync_id": acknowledgment.sync_id}
            )
            request_tracker.complete_request(request_id, success=False)

        raise HTTPException(status_code=ErrorCodes.INTERNAL_SERVER_ERROR, detail="同步确认失败") from e


# ===== 离线包管理API接口 =====


@sync_extended_router.post("/offline/generate", response_model=ApiResponse[PackageInfo])
async def generate_offline_package(package_request: OfflinePackageRequest, request: Request):
    """
    生成离线包

    Args:
        package_request: 离线包请求
        request: HTTP请求对象

    Returns:
        ApiResponse[PackageInfo]: 包信息
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"生成离线包: name={package_request.package_name}, request_id={request_id}")

        # 添加请求跟踪事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "offline_generate_request",
                {
                    "endpoint": "/api/v1/rules/offline/generate",
                    "package_name": package_request.package_name,
                    "rule_keys_count": len(package_request.rule_keys) if package_request.rule_keys else 0,
                    "compression_level": package_request.compression_level,
                },
            )

        # 获取离线包管理器
        package_manager = get_offline_package_manager()

        # 生成离线包
        package_info = await package_manager.generate_package(package_request)

        # 添加完成事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "offline_generate_completed",
                {
                    "package_id": package_info.package_id,
                    "package_name": package_info.package_name,
                    "size_bytes": package_info.size_bytes,
                    "rule_count": package_info.rule_count,
                },
            )
            request_tracker.complete_request(request_id, success=True)

        return ApiResponse.success_response(data=package_info, message="离线包生成成功", request_id=request_id)

    except Exception as e:
        logger.error(f"生成离线包失败: {e}", exc_info=True)

        # 添加错误事件
        if request_id:
            request_tracker.add_event(
                request_id, "offline_generate_error", {"error": str(e), "package_name": package_request.package_name}
            )
            request_tracker.complete_request(request_id, success=False)

        raise HTTPException(status_code=ErrorCodes.INTERNAL_SERVER_ERROR, detail="生成离线包失败") from e


@sync_extended_router.get("/offline/packages", response_model=ApiResponse[list[PackageInfo]])
async def list_offline_packages(request: Request):
    """
    列出可用的离线包

    Args:
        request: HTTP请求对象

    Returns:
        ApiResponse[list[PackageInfo]]: 包列表
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"列出离线包: request_id={request_id}")

        # 添加请求跟踪事件
        if request_id:
            request_tracker.add_event(
                request_id, "offline_list_request", {"endpoint": "/api/v1/rules/offline/packages"}
            )

        # 获取离线包管理器
        package_manager = get_offline_package_manager()

        # 列出包
        packages = await package_manager.list_packages()

        # 添加完成事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "offline_list_completed",
                {"total_packages": len(packages), "active_packages": len([p for p in packages if not p.is_expired])},
            )
            request_tracker.complete_request(request_id, success=True)

        return ApiResponse.success_response(data=packages, message="获取离线包列表成功", request_id=request_id)

    except Exception as e:
        logger.error(f"列出离线包失败: {e}", exc_info=True)

        # 添加错误事件
        if request_id:
            request_tracker.add_event(request_id, "offline_list_error", {"error": str(e)})
            request_tracker.complete_request(request_id, success=False)

        raise HTTPException(status_code=ErrorCodes.INTERNAL_SERVER_ERROR, detail="获取离线包列表失败") from e


@sync_extended_router.get("/offline/download/{package_id}")
async def download_offline_package(package_id: str, request: Request):
    """
    下载离线包

    Args:
        package_id: 包ID
        request: HTTP请求对象

    Returns:
        StreamingResponse: 包文件流
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"下载离线包: package_id={package_id}, request_id={request_id}")

        # 添加请求跟踪事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "offline_download_request",
                {"endpoint": f"/api/v1/rules/offline/download/{package_id}", "package_id": package_id},
            )

        # 获取离线包管理器
        package_manager = get_offline_package_manager()

        # 获取包信息
        package_info = await package_manager.get_package_info(package_id)
        if not package_info:
            raise HTTPException(status_code=ErrorCodes.RULE_NOT_FOUND, detail=f"离线包不存在: {package_id}")

        # 检查包是否过期
        if package_info.is_expired:
            logger.warning(f"尝试下载过期包: {package_id}")
            raise HTTPException(status_code=ErrorCodes.RULE_NOT_FOUND, detail=f"离线包已过期: {package_id}")

        # 获取包文件流
        package_stream = package_manager.get_package_stream(package_id)

        # 添加完成事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "offline_download_started",
                {
                    "package_id": package_id,
                    "package_name": package_info.package_name,
                    "size_bytes": package_info.size_bytes,
                },
            )
            request_tracker.complete_request(request_id, success=True)

        # 返回流式响应
        return StreamingResponse(
            package_stream,
            media_type="application/octet-stream",
            headers={
                "Content-Disposition": f"attachment; filename={package_info.package_name}.json.gz",
                "X-Package-ID": package_id,
                "X-Package-Version": package_info.version,
                "X-Package-Size": str(package_info.size_bytes),
                "X-Rule-Count": str(package_info.rule_count),
                "X-Request-ID": request_id or "unknown",
            },
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"下载离线包失败: package_id={package_id}, error={e}", exc_info=True)

        # 添加错误事件
        if request_id:
            request_tracker.add_event(request_id, "offline_download_error", {"error": str(e), "package_id": package_id})
            request_tracker.complete_request(request_id, success=False)

        raise HTTPException(status_code=ErrorCodes.INTERNAL_SERVER_ERROR, detail="下载离线包失败") from e


@sync_extended_router.get("/offline/packages/{package_id}", response_model=ApiResponse[PackageInfo])
async def get_package_info(package_id: str, request: Request):
    """
    获取包信息

    Args:
        package_id: 包ID
        request: HTTP请求对象

    Returns:
        ApiResponse[PackageInfo]: 包信息
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"获取包信息: package_id={package_id}, request_id={request_id}")

        # 添加请求跟踪事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "package_info_request",
                {"endpoint": f"/api/v1/rules/offline/packages/{package_id}", "package_id": package_id},
            )

        # 获取离线包管理器
        package_manager = get_offline_package_manager()

        # 获取包信息
        package_info = await package_manager.get_package_info(package_id)
        if not package_info:
            raise HTTPException(status_code=ErrorCodes.RULE_NOT_FOUND, detail=f"离线包不存在: {package_id}")

        # 添加完成事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "package_info_completed",
                {
                    "package_id": package_id,
                    "package_name": package_info.package_name,
                    "is_expired": package_info.is_expired,
                },
            )
            request_tracker.complete_request(request_id, success=True)

        return ApiResponse.success_response(data=package_info, message="获取包信息成功", request_id=request_id)

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取包信息失败: package_id={package_id}, error={e}", exc_info=True)

        # 添加错误事件
        if request_id:
            request_tracker.add_event(request_id, "package_info_error", {"error": str(e), "package_id": package_id})
            request_tracker.complete_request(request_id, success=False)

        raise HTTPException(status_code=ErrorCodes.INTERNAL_SERVER_ERROR, detail="获取包信息失败") from e
