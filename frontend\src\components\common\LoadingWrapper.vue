<template>
  <div
    class="loading-wrapper"
    :class="{ 'is-loading': loading }"
    :style="wrapperStyle"
    v-loading="loading"
    :element-loading-text="loadingText"
    :element-loading-spinner="spinner"
    :element-loading-background="background"
    element-loading-svg-view-box="-10, -10, 50, 50"
  >
    <slot />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: '加载中...'
  },
  spinner: {
    type: String,
    default: ''
  },
  background: {
    type: String,
    default: 'rgba(0, 0, 0, 0.7)'
  },
  minHeight: {
    type: String,
    default: '200px'
  }
})

const wrapperStyle = computed(() => ({
  minHeight: props.minHeight
}))
</script>

<style scoped>
.loading-wrapper {
  position: relative;
  width: 100%;
  transition: all 0.3s ease;
}

.loading-wrapper.is-loading {
  pointer-events: none;
}

.el-loading-mask {
  border-radius: 8px;
}
</style>
