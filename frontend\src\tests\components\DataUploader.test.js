/**
 * DataUploader 组件单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mountComponent, waitForDOMUpdate, createMockApiResponse } from '../utils/testHelpers'
import DataUploader from '@/views/DataUploader.vue'

// Mock API 模块
vi.mock('@/api/rules', () => ({
  uploadRuleData: vi.fn(),
  validateRuleData: vi.fn(),
  getRulesList: vi.fn()
}))

// Mock stores
vi.mock('@/stores/app', () => ({
  useAppStore: () => ({
    setLoading: vi.fn(),
    setError: vi.fn(),
    clearError: vi.fn(),
    showSuccess: vi.fn()
  })
}))

describe('DataUploader 组件测试', () => {
  let wrapper
  let mockRulesApi
  let mockAppStore

  beforeEach(async () => {
    vi.clearAllMocks()

    mockRulesApi = await import('@/api/rules')
    mockAppStore = (await import('@/stores/app')).useAppStore()

    // Mock 成功的API响应
    mockRulesApi.uploadRuleData.mockResolvedValue(
      createMockApiResponse({ success_count: 100, error_count: 0 })
    )
    mockRulesApi.validateRuleData.mockResolvedValue(
      createMockApiResponse({ valid: true, errors: [] })
    )
    mockRulesApi.getRulesList.mockResolvedValue(
      createMockApiResponse([{ rule_key: 'test-rule', rule_name: '测试规则' }])
    )
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('组件渲染', () => {
    it('应该正确渲染基本结构', async () => {
      wrapper = mountComponent(DataUploader, {
        props: {
          ruleKey: 'test-rule'
        }
      })
      await waitForDOMUpdate()

      // 检查页面标题 - 修正选择器
      expect(wrapper.find('[data-testid="page-title"]').text()).toContain('数据上传')

      // 检查上传区域 - 修正选择器
      expect(wrapper.find('.upload-section').exists()).toBe(true)

      // 检查内容卡片
      expect(wrapper.find('.content-card').exists()).toBe(true)
    })

    it('应该显示上传模式选择', async () => {
      wrapper = mountComponent(DataUploader, {
        props: {
          ruleKey: 'test-rule'
        }
      })
      await waitForDOMUpdate()

      // 检查模式选择器组件存在
      expect(wrapper.findComponent({ name: 'UploadModeSelector' }).exists()).toBe(true)

      // 检查默认模式文本
      expect(wrapper.text()).toContain('全量上传')
    })

    it('应该显示规则选择下拉框', async () => {
      wrapper = mountComponent(DataUploader, {
        props: {
          ruleKey: 'test-rule'
        }
      })
      await waitForDOMUpdate()

      // 检查页面显示规则信息
      expect(wrapper.text()).toContain('规则:')
      expect(wrapper.text()).toContain('test-rule')
    })
  })

  describe('文件上传功能', () => {
    beforeEach(async () => {
      wrapper = mountComponent(DataUploader, {
        props: {
          ruleKey: 'test-rule'
        }
      })
      await waitForDOMUpdate()
    })

    it('应该支持Excel文件上传', async () => {
      // 由于组件使用了FileUploadArea子组件，我们检查组件是否存在
      expect(wrapper.findComponent({ name: 'FileUploadArea' }).exists()).toBe(true)

      // 检查上传相关的文本提示
      expect(wrapper.text()).toContain('点击上传')
    })

    it('应该验证文件类型', async () => {
      // 检查组件是否显示文件类型限制提示
      expect(wrapper.text()).toContain('.xlsx')
      expect(wrapper.text()).toContain('.xls')
    })

    it('应该验证文件大小', async () => {
      // 检查组件是否显示文件大小限制提示
      expect(wrapper.text()).toContain('10MB')
    })
  })

  describe('上传模式切换', () => {
    beforeEach(async () => {
      wrapper = mountComponent(DataUploader, {
        props: {
          ruleKey: 'test-rule'
        }
      })
      await waitForDOMUpdate()
    })

    it('应该支持切换到增量上传模式', async () => {
      // 检查上传模式选择器组件存在
      expect(wrapper.findComponent({ name: 'UploadModeSelector' }).exists()).toBe(true)

      // 检查增量上传相关文本
      expect(wrapper.text()).toContain('增量上传')
    })

    it('应该在增量模式下显示数据对比', async () => {
      // 检查组件包含增量上传相关的说明文本
      expect(wrapper.text()).toContain('增量上传')
      expect(wrapper.text()).toContain('只上传变更的数据')
    })
  })

  describe('数据验证', () => {
    beforeEach(async () => {
      wrapper = mountComponent(DataUploader)
      await waitForDOMUpdate()
    })

    it('应该在上传前验证数据', async () => {
      // 检查组件包含验证相关的文本
      expect(wrapper.text()).toContain('验证数据')
      expect(wrapper.text()).toContain('格式确认')
    })

    it('应该显示验证错误', async () => {
      // 检查组件包含错误处理相关的文本
      expect(wrapper.text()).toContain('数据预览')
    })
  })

  describe('上传进度', () => {
    beforeEach(async () => {
      wrapper = mountComponent(DataUploader, {
        props: {
          ruleKey: 'test-rule'
        }
      })
      await waitForDOMUpdate()
    })

    it('应该显示上传进度', async () => {
      // 检查步骤指示器组件存在
      expect(wrapper.findComponent({ name: 'RegistrationSteps' }).exists()).toBe(true)

      // 检查进度相关文本
      expect(wrapper.text()).toContain('提交数据')
    })

    it('应该支持取消上传', async () => {
      // 检查组件包含重新开始相关的功能
      expect(wrapper.text()).toContain('返回仪表盘')
    })
  })

  describe('错误处理', () => {
    it('应该处理上传失败', async () => {
      wrapper = mountComponent(DataUploader, {
        props: {
          ruleKey: 'test-rule'
        }
      })
      await waitForDOMUpdate()

      // 检查组件包含错误处理相关的文本
      expect(wrapper.text()).toContain('数据上传')
    })
  })
})
