"""
统一数据映射引擎单元测试
"""

import json
import tempfile
from pathlib import Path

import pytest

from services.unified_data_mapping_engine import UnifiedDataMappingEngine


@pytest.mark.unit
@pytest.mark.data_mapping
class TestUnifiedDataMappingEngine:
    """统一数据映射引擎测试类"""

    @pytest.fixture
    def sample_config(self):
        """示例配置数据"""
        return {
            "metadata": {
                "version": "3.1.0",
                "last_updated": "2025-07-24",
                "description": "测试配置"
            },
            "field_definitions": {
                "common_fields": {
                    "rule_name": {
                        "chinese_name": "规则名称",
                        "data_type": "string",
                        "required": True,
                        "database_column": "rule_name",
                        "api_field": "rule_name",
                        "excel_column": "规则名称",
                        "validation_rules": ["required", "max_length:500"],
                    },
                    "level1": {
                        "chinese_name": "一级错误类型",
                        "data_type": "string",
                        "required": True,
                        "database_column": "level1",
                        "api_field": "level1",
                        "excel_column": "一级错误类型",
                        "validation_rules": ["required", "max_length:100"],
                    },
                    "yb_code": {
                        "chinese_name": "药品编码",
                        "data_type": "array",
                        "required": True,
                        "database_column": "yb_code",
                        "api_field": "yb_code",
                        "excel_column": "药品编码",
                        "validation_rules": ["required", "array"],
                    },
                    "start_date": {
                        "chinese_name": "开始日期",
                        "data_type": "string",
                        "required": True,
                        "database_column": "start_date",
                        "api_field": "start_date",
                        "excel_column": "开始日期",
                        "validation_rules": ["required", "date_format"],
                    },
                },
                "specific_fields": {
                    "age_threshold": {
                        "chinese_name": "年龄阈值",
                        "data_type": "integer",
                        "required": False,
                        "database_column": "age_threshold",
                        "api_field": "age_threshold",
                        "excel_column": "年龄阈值",
                        "validation_rules": ["integer", "min:0", "max:150"],
                    }
                },
            },
            "rule_type_mappings": {
                "test_rule_type": {
                    "name": "测试规则类型",
                    "required_fields": ["rule_name", "level1"],
                    "optional_fields": ["age_threshold"],
                }
            },
        }

    @pytest.fixture
    def temp_config_file(self, sample_config):
        """创建临时配置文件"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False, encoding="utf-8") as f:
            json.dump(sample_config, f, ensure_ascii=False, indent=2)
            temp_path = f.name

        yield temp_path

        # 清理临时文件
        Path(temp_path).unlink(missing_ok=True)

    @pytest.fixture
    def engine(self, temp_config_file):
        """创建测试引擎实例"""
        return UnifiedDataMappingEngine(temp_config_file)

    class TestInitialization:
        """初始化测试组"""

        def test_init_should_initialize_successfully(self, temp_config_file):
            """初始化应该成功"""
            engine = UnifiedDataMappingEngine(temp_config_file)

            assert engine.field_manager is not None
            assert len(engine._fixed_field_names) > 0
            assert "rule_name" in engine._fixed_field_names
            assert "level1" in engine._fixed_field_names

    class TestFieldNameNormalization:
        """字段名称标准化测试组"""

        def test_normalize_field_names_with_valid_data_should_normalize_successfully(self, engine):
            """使用有效数据标准化字段名称应该成功"""
            data = {"rule_name": "测试规则", "level1": "一级错误", "unknown_field": "未知字段"}

            normalized = engine.normalize_field_names(data)

            # 标准化后字段名应该保持不变（因为已经是标准名称）
            assert normalized["rule_name"] == "测试规则"
            assert normalized["level1"] == "一级错误"
            assert normalized["unknown_field"] == "未知字段"

        def test_normalize_field_names_with_empty_data_should_return_empty_dict(self, engine):
            """使用空数据标准化字段名称应该返回空字典"""
            assert engine.normalize_field_names({}) == {}
            assert engine.normalize_field_names(None) == {}

    class TestFieldConversion:
        """字段转换测试组"""

        def test_convert_to_chinese_fields_should_convert_successfully(self, engine):
            """转换为中文字段名应该成功"""
            data = {"rule_name": "测试规则", "level1": "一级错误", "unknown_field": "未知字段"}

            chinese_data = engine.convert_to_chinese_fields(data)

            assert chinese_data["规则名称"] == "测试规则"
            assert chinese_data["一级错误类型"] == "一级错误"
            assert chinese_data["unknown_field"] == "未知字段"  # 未知字段保持原名

        def test_convert_to_database_fields_should_convert_successfully(self, engine):
            """转换为数据库字段名应该成功"""
            data = {"rule_name": "测试规则", "level1": "一级错误"}

            db_data = engine.convert_to_database_fields(data)

            assert db_data["rule_name"] == "测试规则"
            assert db_data["level1"] == "一级错误"

        def test_convert_to_api_fields_should_convert_successfully(self, engine):
            """转换为API字段名应该成功"""
            data = {"rule_name": "测试规则", "level1": "一级错误"}

            api_data = engine.convert_to_api_fields(data)

            assert api_data["rule_name"] == "测试规则"
            assert api_data["level1"] == "一级错误"

    class TestDataValidation:
        """数据验证测试组"""

        def test_validate_data_with_valid_data_should_pass(self, engine):
            """使用有效数据验证应该通过"""
            data = {
                "rule_name": "测试规则",
                "level1": "一级错误",
                "yb_code": ["A001", "A002"],
                "start_date": "2025-01-01",
            }

            result = engine.validate_data(data, "test_rule_type")

            assert result["valid"] is True
            assert len(result["errors"]) == 0

        def test_validate_data_with_missing_required_fields_should_fail(self, engine):
            """使用缺少必填字段的数据验证应该失败"""
            data = {
                "rule_name": "测试规则"
                # 缺少 level1 必填字段
            }

            result = engine.validate_data(data, "test_rule_type")

            assert result["valid"] is False
            assert len(result["errors"]) > 0
            assert any("一级错误类型" in error for error in result["errors"])

        def test_validate_data_with_field_format_errors_should_fail(self, engine):
            """使用字段格式错误的数据验证应该失败"""
            data = {
                "rule_name": "",  # 空值，但是必填
                "level1": "一级错误",
                "start_date": "invalid-date",  # 无效日期格式
                "age_threshold": "not_a_number",  # 无效整数
            }

            result = engine.validate_data(data)

            assert result["valid"] is False
            assert len(result["errors"]) > 0

        def test_validate_required_fields_should_identify_missing_fields(self, engine):
            """验证必填字段应该识别缺失字段"""
            data = {"rule_name": "测试规则"}

            errors = engine.validate_required_fields(data, "test_rule_type")

            # 应该有一个错误，因为缺少 level1
            assert len(errors) == 1
            assert "一级错误类型" in errors[0]

    class TestFieldSeparation:
        """字段分离测试组"""

        def test_separate_fields_should_separate_correctly(self, engine):
            """分离字段应该正确分离"""
            data = {
                "rule_name": "测试规则",
                "level1": "一级错误",
                "yb_code": ["A001", "A002"],
                "age_threshold": 18,
                "custom_field": "自定义字段",
            }

            fixed_fields, extended_fields = engine.separate_fields(data)

            # 检查固定字段
            assert "rule_name" in fixed_fields
            assert "level1" in fixed_fields
            assert "yb_code" in fixed_fields
            assert fixed_fields["yb_code"] == "A001,A002"  # 数组字段应该转换为逗号分隔字符串

            # 检查扩展字段
            assert "age_threshold" in extended_fields
            assert "custom_field" in extended_fields

        def test_merge_fields_should_merge_correctly(self, engine):
            """合并字段应该正确合并"""
            fixed_fields = {"rule_name": "测试规则", "level1": "一级错误"}

            extended_fields = {"age_threshold": 18, "custom_field": "自定义字段"}

            merged = engine.merge_fields(fixed_fields, extended_fields)

            assert "rule_name" in merged
            assert "level1" in merged
            assert "age_threshold" in merged
            assert "custom_field" in merged
            assert len(merged) == 4

    class TestArrayFieldProcessing:
        """数组字段处理测试组"""

        def test_is_array_field_should_identify_array_fields_correctly(self, engine):
            """识别数组字段应该正确"""
            assert engine._is_array_field("yb_code") is True
            assert engine._is_array_field("rule_name") is False
            assert engine._is_array_field("unknown_field") is False

        def test_process_array_field_with_list_should_convert_to_comma_separated_string(self, engine):
            """处理列表类型的数组字段应该转换为逗号分隔字符串"""
            result = engine._process_array_field(["A001", "A002", "A003"])
            assert result == "A001,A002,A003"

        def test_process_array_field_with_string_should_return_string(self, engine):
            """处理字符串类型的数组字段应该返回字符串"""
            result = engine._process_array_field("A001,A002")
            assert result == "A001,A002"

        def test_process_array_field_with_none_should_return_empty_string(self, engine):
            """处理None值的数组字段应该返回空字符串"""
            result = engine._process_array_field(None)
            assert result == ""

        def test_process_array_field_with_other_type_should_convert_to_string(self, engine):
            """处理其他类型的数组字段应该转换为字符串"""
            result = engine._process_array_field(123)
            assert result == "123"

    class TestStructuredFormatConversion:
        """结构化格式转换测试组"""

        def test_convert_to_structured_format_should_convert_correctly(self, engine):
            """转换为结构化格式应该正确转换"""
            data = {"rule_name": "测试规则", "level1": "一级错误", "age_threshold": 18, "custom_field": "自定义字段"}

            structured = engine.convert_to_structured_format(data, "test_rule_key")

            assert "rule_name" in structured
            assert "level1" in structured
            assert "rule_key" in structured
            assert structured["rule_key"] == "test_rule_key"
            assert "extended_fields" in structured

            # 检查扩展字段是否正确序列化
            import json

            extended_fields = json.loads(structured["extended_fields"])
            assert "age_threshold" in extended_fields
            assert "custom_field" in extended_fields

        def test_convert_from_structured_format_should_convert_correctly(self, engine):
            """从结构化格式转换应该正确转换"""
            structured_data = {
                "rule_name": "测试规则",
                "level1": "一级错误",
                "rule_key": "test_rule_key",
                "extended_fields": '{"age_threshold": 18, "custom_field": "自定义字段"}',
            }

            data = engine.convert_from_structured_format(structured_data)

            assert "rule_name" in data
            assert "level1" in data
            assert "rule_key" in data
            assert "age_threshold" in data
            assert "custom_field" in data
            assert data["age_threshold"] == 18
            assert data["custom_field"] == "自定义字段"
            assert "extended_fields" not in data  # 应该被解析并移除

        def test_convert_from_structured_format_with_invalid_json_should_handle_gracefully(self, engine):
            """从结构化格式转换时JSON无效应该优雅处理"""
            structured_data = {"rule_name": "测试规则", "extended_fields": "invalid json"}

            data = engine.convert_from_structured_format(structured_data)

            # 应该只包含固定字段，扩展字段解析失败
            assert "rule_name" in data
            assert "extended_fields" not in data

    class TestEngineInfo:
        """引擎信息测试组"""

        def test_get_engine_info_should_return_engine_information(self, engine):
            """获取引擎信息应该返回引擎信息"""
            info = engine.get_engine_info()

            assert info["engine_name"] == "UnifiedDataMappingEngine"
            assert info["version"] == "2.0.0"
            assert "fixed_fields_count" in info
            assert "field_manager_info" in info
            assert "supported_operations" in info
            assert len(info["supported_operations"]) > 0
