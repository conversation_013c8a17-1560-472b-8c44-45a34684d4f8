/**
 * 生成类型文件功能测试
 * 测试自动生成的类型定义和工具函数
 */

import { describe, it, expect } from 'vitest'
import {
  FIELD_CHINESE_NAMES,
  FIELD_DATA_TYPES,
  FIELD_VALIDATION_RULES,
  getFieldChineseName,
  getFieldDataType,
  getFieldValidationRules,
  isFieldRequired
} from '../../src/types/generated-fields.ts'

describe('生成的字段类型定义', () => {
  describe('字段映射常量', () => {
    it('FIELD_CHINESE_NAMES应该包含必要字段', () => {
      expect(FIELD_CHINESE_NAMES).toBeDefined()
      expect(FIELD_CHINESE_NAMES.rule_id).toBe('规则ID')
      expect(FIELD_CHINESE_NAMES.rule_name).toBe('规则名称')
      expect(FIELD_CHINESE_NAMES.level1).toBe('一级错误类型')
      expect(FIELD_CHINESE_NAMES.level2).toBe('二级错误类型')
      expect(FIELD_CHINESE_NAMES.level3).toBe('三级错误类型')
    })

    it('FIELD_DATA_TYPES应该包含正确的数据类型', () => {
      expect(FIELD_DATA_TYPES).toBeDefined()
      expect(FIELD_DATA_TYPES.rule_id).toBe('string')
      expect(FIELD_DATA_TYPES.rule_name).toBe('string')
    })

    it('FIELD_VALIDATION_RULES应该包含验证规则', () => {
      expect(FIELD_VALIDATION_RULES).toBeDefined()
      expect(Array.isArray(FIELD_VALIDATION_RULES.rule_name)).toBe(true)
    })
  })

  describe('工具函数', () => {
    it('getFieldChineseName应该返回正确的中文名称', () => {
      expect(getFieldChineseName('rule_id')).toBe('规则ID')
      expect(getFieldChineseName('rule_name')).toBe('规则名称')
      expect(getFieldChineseName('level1')).toBe('一级错误类型')
      expect(getFieldChineseName('nonexistent')).toBe('nonexistent')
    })

    it('getFieldDataType应该返回正确的数据类型', () => {
      expect(getFieldDataType('rule_id')).toBe('string')
      expect(getFieldDataType('rule_name')).toBe('string')
      expect(getFieldDataType('nonexistent')).toBe('string')
    })

    it('getFieldValidationRules应该返回验证规则数组', () => {
      const rules = getFieldValidationRules('rule_name')
      expect(Array.isArray(rules)).toBe(true)
      
      const emptyRules = getFieldValidationRules('nonexistent')
      expect(Array.isArray(emptyRules)).toBe(true)
      expect(emptyRules.length).toBe(0)
    })

    it('isFieldRequired应该正确判断必填字段', () => {
      // 测试必填字段
      expect(isFieldRequired('rule_name')).toBe(true)
      expect(isFieldRequired('level1')).toBe(true)
      
      // 测试可选字段
      expect(isFieldRequired('rule_id')).toBe(false)
      expect(isFieldRequired('remarks')).toBe(false)
      
      // 测试不存在的字段
      expect(isFieldRequired('nonexistent')).toBe(false)
    })
  })

  describe('字段覆盖性检查', () => {
    it('应该包含所有核心字段', () => {
      const coreFields = [
        'rule_id', 'rule_name', 'level1', 'level2', 'level3',
        'error_reason', 'degree', 'reference', 'detail_position',
        'type', 'pos', 'applicableArea', 'default_use'
      ]
      
      coreFields.forEach(field => {
        expect(FIELD_CHINESE_NAMES[field]).toBeDefined()
        expect(typeof FIELD_CHINESE_NAMES[field]).toBe('string')
        expect(FIELD_CHINESE_NAMES[field].length).toBeGreaterThan(0)
      })
    })

    it('中文名称应该不为空', () => {
      Object.values(FIELD_CHINESE_NAMES).forEach(chineseName => {
        expect(typeof chineseName).toBe('string')
        expect(chineseName.length).toBeGreaterThan(0)
      })
    })

    it('数据类型应该有效', () => {
      const validTypes = ['string', 'text', 'integer', 'array', 'boolean']
      Object.values(FIELD_DATA_TYPES).forEach(dataType => {
        expect(validTypes).toContain(dataType)
      })
    })
  })

  describe('类型一致性检查', () => {
    it('字段映射常量应该包含相同的字段', () => {
      const chineseNameFields = Object.keys(FIELD_CHINESE_NAMES)
      const dataTypeFields = Object.keys(FIELD_DATA_TYPES)
      const validationRuleFields = Object.keys(FIELD_VALIDATION_RULES)
      
      expect(chineseNameFields.sort()).toEqual(dataTypeFields.sort())
      expect(chineseNameFields.sort()).toEqual(validationRuleFields.sort())
    })
  })
})
