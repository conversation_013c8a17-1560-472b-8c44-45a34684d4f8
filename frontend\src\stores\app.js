/**
 * 全局应用状态管理
 * 企业级状态管理架构的核心 Store，管理应用级状态
 */

import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { loadingDebugger } from '@/utils/loadingDebugger'
import { ElMessage, ElNotification } from 'element-plus'

/**
 * 全局应用状态 Store
 */
export const useAppStore = defineStore('app', () => {
  // ==================== 全局加载状态 ====================

  /**
   * 全局加载状态
   */
  const globalLoading = ref(false)

  /**
   * 加载任务集合（用于管理多个并发加载任务）
   */
  const loadingTasks = ref(new Set())

  /**
   * 当前加载消息
   */
  const loadingMessage = ref('加载中...')

  /**
   * 加载进度（0-100）
   */
  const loadingProgress = ref(0)

  // ==================== 全局错误状态 ====================

  /**
   * 当前错误
   */
  const currentError = ref(null)

  /**
   * 错误历史记录
   */
  const errorHistory = ref([])

  /**
   * 错误计数器
   */
  const errorCount = ref(0)

  /**
   * 最后错误时间
   */
  const lastErrorTime = ref(null)

  // ==================== 通知系统状态 ====================

  /**
   * 通知列表
   */
  const notifications = ref([])

  /**
   * Toast 列表
   */
  const toasts = ref([])

  /**
   * 最大通知数量
   */
  const maxNotifications = ref(5)

  /**
   * 最大 Toast 数量
   */
  const maxToasts = ref(3)

  // ==================== 应用配置状态 ====================

  /**
   * 应用主题
   */
  const theme = ref('light')

  /**
   * 语言设置
   */
  const locale = ref('zh-CN')

  /**
   * 调试模式
   */
  const debugMode = ref(process.env.NODE_ENV === 'development')

  // ==================== 用户权限状态 ====================

  /**
   * 用户角色列表
   */
  const userRoles = ref(['user']) // 默认用户角色

  /**
   * 用户权限列表
   */
  const userPermissions = ref([])

  /**
   * 当前用户信息
   */
  const currentUser = ref({
    id: null,
    username: '',
    email: '',
    avatar: '',
    lastLoginTime: null
  })

  // ==================== 计算属性 ====================

  /**
   * 是否有活跃的加载任务
   */
  const hasLoadingTasks = computed(() => loadingTasks.value.size > 0)

  /**
   * 是否有错误
   */
  const hasError = computed(() => currentError.value !== null)

  /**
   * 错误率（最近100次操作）
   */
  const errorRate = computed(() => {
    const recentErrors = errorHistory.value.slice(-100)
    return recentErrors.length > 0 ? (recentErrors.filter(e => !e.resolved).length / recentErrors.length) * 100 : 0
  })

  /**
   * 通知数量
   */
  const notificationCount = computed(() => notifications.value.length)

  /**
   * Toast 数量
   */
  const toastCount = computed(() => toasts.value.length)

  /**
   * 是否为管理员
   */
  const isAdmin = computed(() => userRoles.value.includes('admin'))

  /**
   * 是否已登录
   */
  const isLoggedIn = computed(() => currentUser.value.id !== null)

  // ==================== 加载状态管理 ====================

  /**
   * 添加加载任务
   * @param {string} taskId - 任务ID
   * @param {string} message - 加载消息
   */
  const addLoadingTask = (taskId, message = '加载中...') => {
    loadingTasks.value.add(taskId)
    loadingMessage.value = message
    globalLoading.value = true

    // 记录到调试器
    loadingDebugger.logTask('ADD', taskId, message)

    if (debugMode.value) {
      console.debug(`[AppStore] Loading task added: ${taskId}`, { message, totalTasks: loadingTasks.value.size })
    }
  }

  /**
   * 移除加载任务
   * @param {string} taskId - 任务ID
   */
  const removeLoadingTask = (taskId) => {
    const wasPresent = loadingTasks.value.has(taskId)
    loadingTasks.value.delete(taskId)

    // 记录到调试器
    loadingDebugger.logTask('REMOVE', taskId, wasPresent ? 'success' : 'not_found')

    // 如果没有加载任务了，关闭全局加载
    if (loadingTasks.value.size === 0) {
      globalLoading.value = false
      loadingProgress.value = 0
      loadingMessage.value = '加载中...'
    }

    if (debugMode.value) {
      console.debug(`[AppStore] Loading task removed: ${taskId}`, {
        wasPresent,
        totalTasks: loadingTasks.value.size
      })
    }
  }

  /**
   * 更新加载进度
   * @param {number} progress - 进度值（0-100）
   */
  const updateLoadingProgress = (progress) => {
    loadingProgress.value = Math.max(0, Math.min(100, progress))
  }

  /**
   * 清除所有加载任务
   */
  const clearLoadingTasks = () => {
    loadingTasks.value.clear()
    globalLoading.value = false
    loadingProgress.value = 0
    loadingMessage.value = '加载中...'
  }

  // ==================== 错误状态管理 ====================

  /**
   * 设置错误
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   */
  const setError = (error, context = {}) => {
    const errorRecord = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      error,
      timestamp: Date.now(),
      context,
      resolved: false
    }

    currentError.value = error
    errorHistory.value.push(errorRecord)
    errorCount.value++
    lastErrorTime.value = Date.now()

    // 限制错误历史长度
    if (errorHistory.value.length > 1000) {
      errorHistory.value = errorHistory.value.slice(-500)
    }

    if (debugMode.value) {
      console.error('[AppStore] Error set:', error, context)
    }

    return errorRecord.id
  }

  /**
   * 清除当前错误
   */
  const clearError = () => {
    currentError.value = null
  }

  /**
   * 标记错误为已解决
   * @param {string} errorId - 错误ID
   */
  const resolveError = (errorId) => {
    const errorRecord = errorHistory.value.find(e => e.id === errorId)
    if (errorRecord) {
      errorRecord.resolved = true
      errorRecord.resolvedAt = Date.now()
    }
  }

  /**
   * 清除错误历史
   */
  const clearErrorHistory = () => {
    errorHistory.value = []
    errorCount.value = 0
    lastErrorTime.value = null
  }

  // ==================== 通知系统管理 ====================

  /**
   * 添加通知
   * @param {Object} notification - 通知对象
   */
  const addNotification = (notification) => {
    const notificationWithId = {
      id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      ...notification
    }

    notifications.value.push(notificationWithId)

    // 限制通知数量
    if (notifications.value.length > maxNotifications.value) {
      notifications.value = notifications.value.slice(-maxNotifications.value)
    }

    return notificationWithId.id
  }

  /**
   * 移除通知
   * @param {string} notificationId - 通知ID
   */
  const removeNotification = (notificationId) => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  /**
   * 添加 Toast
   * @param {Object} toast - Toast 对象
   */
  const addToast = (toast) => {
    const toastWithId = {
      id: `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      duration: 3000,
      ...toast
    }

    toasts.value.push(toastWithId)

    // 限制 Toast 数量
    if (toasts.value.length > maxToasts.value) {
      toasts.value = toasts.value.slice(-maxToasts.value)
    }

    // 自动移除 Toast
    if (toastWithId.duration > 0) {
      setTimeout(() => {
        removeToast(toastWithId.id)
      }, toastWithId.duration)
    }

    return toastWithId.id
  }

  /**
   * 移除 Toast
   * @param {string} toastId - Toast ID
   */
  const removeToast = (toastId) => {
    const index = toasts.value.findIndex(t => t.id === toastId)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }

  /**
   * 清除所有通知
   */
  const clearNotifications = () => {
    notifications.value = []
  }

  /**
   * 清除所有 Toast
   */
  const clearToasts = () => {
    toasts.value = []
  }

  // ==================== 应用配置管理 ====================

  /**
   * 设置主题
   * @param {string} newTheme - 新主题
   */
  const setTheme = (newTheme) => {
    theme.value = newTheme
    // 这里可以添加主题切换逻辑
    document.documentElement.setAttribute('data-theme', newTheme)
  }

  /**
   * 设置语言
   * @param {string} newLocale - 新语言
   */
  const setLocale = (newLocale) => {
    locale.value = newLocale
    // 这里可以添加语言切换逻辑
  }

  /**
   * 切换调试模式
   */
  const toggleDebugMode = () => {
    debugMode.value = !debugMode.value
  }

  // ==================== 用户权限管理 ====================

  /**
   * 设置用户角色
   * @param {Array} roles - 角色数组
   */
  const setUserRoles = (roles) => {
    userRoles.value = Array.isArray(roles) ? roles : [roles]

    if (debugMode.value) {
      console.debug('[AppStore] User roles updated:', userRoles.value)
    }
  }

  /**
   * 添加用户角色
   * @param {string} role - 角色名称
   */
  const addUserRole = (role) => {
    if (!userRoles.value.includes(role)) {
      userRoles.value.push(role)
    }
  }

  /**
   * 移除用户角色
   * @param {string} role - 角色名称
   */
  const removeUserRole = (role) => {
    const index = userRoles.value.indexOf(role)
    if (index > -1) {
      userRoles.value.splice(index, 1)
    }
  }

  /**
   * 检查用户是否有指定角色
   * @param {string|Array} roles - 角色名称或角色数组
   * @returns {boolean}
   */
  const hasRole = (roles) => {
    const roleArray = Array.isArray(roles) ? roles : [roles]
    return roleArray.some(role => userRoles.value.includes(role))
  }

  /**
   * 设置用户权限
   * @param {Array} permissions - 权限数组
   */
  const setUserPermissions = (permissions) => {
    userPermissions.value = Array.isArray(permissions) ? permissions : [permissions]
  }

  /**
   * 检查用户是否有指定权限
   * @param {string|Array} permissions - 权限名称或权限数组
   * @returns {boolean}
   */
  const hasPermission = (permissions) => {
    const permissionArray = Array.isArray(permissions) ? permissions : [permissions]
    return permissionArray.some(permission => userPermissions.value.includes(permission))
  }

  /**
   * 设置当前用户信息
   * @param {Object} user - 用户信息
   */
  const setCurrentUser = (user) => {
    currentUser.value = {
      ...currentUser.value,
      ...user,
      lastLoginTime: Date.now()
    }

    if (debugMode.value) {
      console.debug('[AppStore] Current user updated:', currentUser.value)
    }
  }

  /**
   * 用户登出
   */
  const logout = () => {
    currentUser.value = {
      id: null,
      username: '',
      email: '',
      avatar: '',
      lastLoginTime: null
    }
    userRoles.value = ['guest']
    userPermissions.value = []

    if (debugMode.value) {
      console.debug('[AppStore] User logged out')
    }
  }

  // ==================== 重置和清理 ====================

  /**
   * 重置所有状态
   */
  const resetStore = () => {
    // 重置加载状态
    clearLoadingTasks()

    // 重置错误状态
    currentError.value = null
    clearErrorHistory()

    // 重置通知状态
    clearNotifications()
    clearToasts()

    // 重置配置
    theme.value = 'light'
    locale.value = 'zh-CN'
    debugMode.value = process.env.NODE_ENV === 'development'

    // 重置用户状态
    logout()
  }

  // ==================== 监听器 ====================

  // 监听全局加载状态变化
  watch(globalLoading, (newValue) => {
    if (debugMode.value) {
      console.debug(`[AppStore] Global loading: ${newValue}`)
    }
  })

  // 监听错误状态变化
  watch(currentError, (newError) => {
    if (newError && debugMode.value) {
      console.error('[AppStore] Current error changed:', newError)
    }
  })

  // ==================== 返回公共接口 ====================

  return {
    // 加载状态
    globalLoading,
    loadingTasks,
    loadingMessage,
    loadingProgress,
    hasLoadingTasks,

    // 错误状态
    currentError,
    errorHistory,
    errorCount,
    lastErrorTime,
    hasError,
    errorRate,

    // 通知状态
    notifications,
    toasts,
    maxNotifications,
    maxToasts,
    notificationCount,
    toastCount,

    // 应用配置
    theme,
    locale,
    debugMode,

    // 用户权限状态
    userRoles,
    userPermissions,
    currentUser,
    isAdmin,
    isLoggedIn,

    // 加载状态管理方法
    addLoadingTask,
    removeLoadingTask,
    updateLoadingProgress,
    clearLoadingTasks,

    // 错误状态管理方法
    setError,
    clearError,
    resolveError,
    clearErrorHistory,

    // 通知系统管理方法
    addNotification,
    removeNotification,
    addToast,
    removeToast,
    clearNotifications,
    clearToasts,

    // 应用配置管理方法
    setTheme,
    setLocale,
    toggleDebugMode,

    // 用户权限管理方法
    setUserRoles,
    addUserRole,
    removeUserRole,
    hasRole,
    setUserPermissions,
    hasPermission,
    setCurrentUser,
    logout,

    // 重置和清理方法
    resetStore
  }
})
