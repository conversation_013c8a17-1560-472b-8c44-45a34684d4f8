"""
服务层重构验证测试
验证重构后的服务层功能是否正常工作
"""

import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

import pytest
from sqlalchemy.orm import Session

from models.database import RuleTemplate
from services.excel_template_service import ColumnDefinition, ExcelTemplateService, TemplateMetadata
from services.metadata_validation_service import MetadataValidationService, ValidationRule
from services.rule_data_sync_service import RuleDataSyncService
from services.rule_detail_service import RuleDetailService, ServiceError, ValidationResult
from services.unified_template_service import UnifiedTemplateService
from services.unified_validation_service import UnifiedValidationService


class TestRuleDetailServiceRefactor:
    """测试RuleDetailService重构功能"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_session = Mock(spec=Session)
        self.service = RuleDetailService(self.mock_session)

    def test_service_exception_creation(self):
        """测试ServiceException创建"""
        exception = ServiceError("测试错误", error_code="TEST_ERROR", details={"key": "value"})

        assert exception.message == "测试错误"
        assert exception.error_code == "TEST_ERROR"
        assert exception.details == {"key": "value"}
        assert exception.timestamp > 0

    def test_validation_result_creation(self):
        """测试ValidationResult创建"""
        result = ValidationResult(valid=True, errors=["错误1"], warnings=["警告1"], data={"field": "value"})

        assert result.valid is True
        assert result.errors == ["错误1"]
        assert result.warnings == ["警告1"]
        assert result.data == {"field": "value"}

        result_dict = result.to_dict()
        assert result_dict["valid"] is True
        assert result_dict["errors"] == ["错误1"]

    def test_cache_functionality(self):
        """测试缓存功能"""
        # 测试缓存清空
        self.service._template_cache["test"] = "value"
        self.service._metadata_cache["test"] = "value"

        self.service.clear_cache()

        assert len(self.service._template_cache) == 0
        assert len(self.service._metadata_cache) == 0

    @patch("services.rule_detail_service.logger")
    def test_log_operation(self, mock_logger):
        """测试操作日志记录"""
        self.service._log_operation(operation="test_operation", rule_key="test_rule", duration=0.5, success=True)

        mock_logger.info.assert_called_once()
        call_args = mock_logger.info.call_args
        assert "test_operation" in call_args[0][0]

    def test_handle_service_error(self):
        """测试错误处理"""
        test_error = ValueError("测试错误")

        service_error = self.service._handle_service_error(
            operation="test_operation", error=test_error, rule_key="test_rule"
        )

        assert isinstance(service_error, ServiceError)
        assert "test_operation" in service_error.message
        assert service_error.details["rule_key"] == "test_rule"


class TestExcelTemplateService:
    """测试ExcelTemplateService功能"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_session = Mock(spec=Session)
        self.temp_dir = tempfile.mkdtemp()
        self.service = ExcelTemplateService(self.mock_session, self.temp_dir)

    def test_column_definition_creation(self):
        """测试ColumnDefinition创建"""
        column = ColumnDefinition(
            field_name="test_field",
            chinese_name="测试字段",
            data_type="string",
            is_required=True,
            max_length=100,
            validation_rules=["required"],
            default_value="默认值",
            description="字段描述",
            excel_order=1,
        )

        assert column.field_name == "test_field"
        assert column.chinese_name == "测试字段"
        assert column.is_required is True
        assert column.max_length == 100

    def test_template_metadata_creation(self):
        """测试TemplateMetadata创建"""
        columns = [
            ColumnDefinition("field1", "字段1", "string", True),
            ColumnDefinition("field2", "字段2", "integer", False),
        ]

        metadata = TemplateMetadata(
            rule_key="test_rule",
            rule_name="测试规则",
            template_name="测试模板",
            columns=columns,
            total_columns=2,
            required_columns=1,
            optional_columns=1,
            description="模板描述",
        )

        assert metadata.rule_key == "test_rule"
        assert metadata.total_columns == 2
        assert metadata.required_columns == 1
        assert metadata.optional_columns == 1

    def test_style_initialization(self):
        """测试样式初始化"""
        assert self.service.header_font is not None
        assert self.service.header_fill is not None
        assert self.service.border is not None
        assert self.service.data_font is not None

    def test_get_rule_template_mock(self):
        """测试获取规则模板（模拟）"""
        # 模拟数据库查询
        mock_template = Mock(spec=RuleTemplate)
        mock_template.rule_key = "test_rule"
        mock_template.name = "测试规则"

        self.mock_session.query.return_value.filter.return_value.first.return_value = mock_template

        result = self.service._get_rule_template("test_rule")
        assert result == mock_template


class TestMetadataValidationService:
    """测试MetadataValidationService功能"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_session = Mock(spec=Session)
        self.service = MetadataValidationService(self.mock_session)

    def test_validation_rule_creation(self):
        """测试ValidationRule创建"""
        rule = ValidationRule(
            field_name="test_field",
            chinese_name="测试字段",
            rule_type="required",
            rule_value=True,
            error_message="字段不能为空",
            warning_message="建议填写",
        )

        assert rule.field_name == "test_field"
        assert rule.chinese_name == "测试字段"
        assert rule.rule_type == "required"
        assert rule.rule_value is True

    def test_parse_validation_rule_string(self):
        """测试验证规则字符串解析"""
        # 测试最大长度规则
        rule = self.service._parse_validation_rule_string("max_length:100", "test_field", "测试字段")

        assert rule is not None
        assert rule.rule_type == "max_length"
        assert rule.rule_value == 100
        assert "100个字符" in rule.error_message

    def test_validate_field_by_rule(self):
        """测试字段验证"""
        # 测试必填验证
        required_rule = ValidationRule(
            field_name="test_field",
            chinese_name="测试字段",
            rule_type="required",
            rule_value=True,
            error_message="字段不能为空",
        )

        # 空值应该失败
        error = self.service._validate_field_by_rule(None, required_rule)
        assert error == "字段不能为空"

        # 有值应该通过
        error = self.service._validate_field_by_rule("有值", required_rule)
        assert error is None

    def test_cache_functionality(self):
        """测试缓存功能"""
        self.service._validation_cache["test"] = []
        self.service.clear_cache()
        assert len(self.service._validation_cache) == 0


class TestUnifiedServices:
    """测试统一服务功能"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_session = Mock(spec=Session)
        self.temp_dir = tempfile.mkdtemp()

    def test_unified_template_service_initialization(self):
        """测试统一模板服务初始化"""
        service = UnifiedTemplateService(self.mock_session, self.temp_dir)

        assert service.session == self.mock_session
        assert service.output_dir == Path(self.temp_dir)
        assert service.excel_service is not None
        assert service.base_params == ("rule_id", "self")

    def test_unified_validation_service_initialization(self):
        """测试统一验证服务初始化"""
        service = UnifiedValidationService(self.mock_session)

        assert service.session == self.mock_session
        assert service.metadata_validation_service is not None
        assert service.patient_validation_service is not None

    def test_rule_data_sync_service_initialization(self):
        """测试规则数据同步服务初始化"""
        cache_file = Path(self.temp_dir) / "test_cache.json.gz"
        service = RuleDataSyncService(self.mock_session, str(cache_file))

        assert service.session == self.mock_session
        assert service.cache_file_path == cache_file
        assert service.data_mapping_engine is not None
        assert service.compression_level == 6
        assert service.batch_size == 1000


class TestServiceIntegration:
    """测试服务集成功能"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_session = Mock(spec=Session)

    def test_service_error_propagation(self):
        """测试服务错误传播"""
        service = RuleDetailService(self.mock_session)

        # 模拟数据库错误
        self.mock_session.query.side_effect = Exception("数据库连接失败")

        with pytest.raises(ServiceError) as exc_info:
            service.create_rule_detail("test_rule", {"field": "value"})

        assert "数据库连接失败" in str(exc_info.value)

    def test_validation_result_consistency(self):
        """测试验证结果一致性"""
        # 测试不同服务返回的验证结果格式一致性
        result1 = ValidationResult(valid=True, errors=[], warnings=[], data={})

        # 验证to_dict方法返回的格式
        result_dict = result1.to_dict()
        expected_keys = {"valid", "errors", "warnings", "data"}
        assert set(result_dict.keys()) == expected_keys

    @patch("services.rule_detail_service.logger")
    def test_logging_consistency(self, mock_logger):
        """测试日志记录一致性"""
        service = RuleDetailService(self.mock_session)

        # 测试成功日志
        service._log_operation("test_op", "test_rule", "test_id", 0.1, True)
        mock_logger.info.assert_called()

        # 测试失败日志
        service._log_operation("test_op", "test_rule", "test_id", 0.1, False, "错误信息")
        mock_logger.error.assert_called()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
