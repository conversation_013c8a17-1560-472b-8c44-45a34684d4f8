-- 规则详情表三表结构重建脚本
-- 版本：v1.0
-- 日期：2025-07-24
-- 说明：删除旧表，重建带外键关联的新表结构

-- =====================================================
-- 第一步：删除所有旧表（清理历史包袱）
-- =====================================================

-- 删除旧的规则相关表
DROP TABLE IF EXISTS rule_details;
DROP TABLE IF EXISTS rule_data_sets;
DROP TABLE IF EXISTS base_rules;

-- 删除现有的不完整新表
DROP TABLE IF EXISTS rule_field_metadata;
DROP TABLE IF EXISTS rule_detail;
DROP TABLE IF EXISTS rule_template;

-- =====================================================
-- 第二步：创建新的三表结构（含外键关联）
-- =====================================================

-- 1. 规则模板表（主表）
CREATE TABLE rule_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_key VARCHAR(100) NOT NULL UNIQUE COMMENT '规则模板类型（业务主键）',
    rule_type VARCHAR(100) NOT NULL COMMENT '规则类型',
    name VARCHAR(500) NOT NULL COMMENT '规则模板名称',
    description TEXT COMMENT '规则模板描述',
    module_path VARCHAR(500) COMMENT 'Python module path',
    file_hash VARCHAR(64) COMMENT 'SHA-256 hash',
    status ENUM('NEW', 'CHANGED', 'READY', 'DEPRECATED') DEFAULT 'NEW' COMMENT '规则模板状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE KEY uk_rule_template_key (rule_key),
    INDEX idx_rule_template_type (rule_type),
    INDEX idx_rule_template_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='规则模板表';

-- 2. 规则明细表（子表）
CREATE TABLE rule_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_id VARCHAR(100) NOT NULL COMMENT '规则ID',
    rule_key VARCHAR(100) NOT NULL COMMENT '规则模板类型',
    
    -- 通用字段（使用标准命名）
    rule_name VARCHAR(500) NOT NULL COMMENT '规则名称',
    level1 VARCHAR(255) NOT NULL COMMENT '一级错误类型',
    level2 VARCHAR(255) NOT NULL COMMENT '二级错误类型',
    level3 VARCHAR(255) NOT NULL COMMENT '三级错误类型',
    error_reason TEXT NOT NULL COMMENT '错误原因',
    degree VARCHAR(50) NOT NULL COMMENT '错误程度（无约束）',
    reference TEXT NOT NULL COMMENT '质控依据或参考资料',
    detail_position VARCHAR(100) NOT NULL COMMENT '具体位置描述',
    prompted_fields3 VARCHAR(100) COMMENT '提示字段类型',
    prompted_fields1 VARCHAR(100) NOT NULL COMMENT '提示字段编码',
    type VARCHAR(100) NOT NULL COMMENT '规则类别',
    pos VARCHAR(100) NOT NULL COMMENT '适用业务',
    applicableArea VARCHAR(100) NOT NULL COMMENT '适用地区',
    default_use VARCHAR(50) NOT NULL COMMENT '默认选用',
    remarks TEXT COMMENT '备注信息',
    in_illustration TEXT COMMENT '入参说明',
    start_date VARCHAR(20) NOT NULL COMMENT '开始日期',
    end_date VARCHAR(20) NOT NULL COMMENT '结束日期',
    
    -- 固定的高频字段
    yb_code TEXT COMMENT '药品编码，逗号分隔',
    diag_whole_code TEXT COMMENT '完整诊断编码，逗号分隔',
    diag_code_prefix TEXT COMMENT '诊断编码前缀，逗号分隔',
    diag_name_keyword VARCHAR(200) COMMENT '诊断名称关键字，逗号分隔',
    fee_whole_code TEXT COMMENT '药品/诊疗项目完整编码，逗号分隔',
    fee_code_prefix TEXT COMMENT '药品/诊疗项目编码前缀，逗号分隔',
    
    -- 扩展字段
    extended_fields TEXT COMMENT 'JSON格式的扩展字段',
    
    -- 元数据
    status ENUM('ACTIVE', 'INACTIVE', 'DEPRECATED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束（关键！）
    FOREIGN KEY fk_rule_detail_template (rule_key) REFERENCES rule_template(rule_key) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- 索引
    INDEX idx_rule_detail_rule_key (rule_key),
    INDEX idx_rule_detail_rule_id (rule_id),
    INDEX idx_rule_detail_status (status),
    INDEX idx_rule_detail_key_status (rule_key, status),
    INDEX idx_rule_detail_yb_code (yb_code(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='规则明细表';

-- 3. 字段元数据表（子表）
CREATE TABLE rule_field_metadata (
    id INT PRIMARY KEY AUTO_INCREMENT,
    rule_key VARCHAR(100) NOT NULL COMMENT '规则模板类型',
    field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
    field_type ENUM('string', 'integer', 'array', 'boolean') NOT NULL COMMENT '字段类型',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    is_fixed_field BOOLEAN DEFAULT FALSE COMMENT '是否为固定字段',
    display_name VARCHAR(200) NOT NULL COMMENT '显示名称',
    description TEXT COMMENT '字段描述',
    validation_rule TEXT COMMENT 'JSON格式的校验规则',
    default_value TEXT COMMENT '默认值',
    excel_column_order INT COMMENT 'Excel列顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束（关键！）
    FOREIGN KEY fk_rule_field_template (rule_key) REFERENCES rule_template(rule_key) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- 索引和约束
    UNIQUE KEY uk_rule_field (rule_key, field_name),
    INDEX idx_rule_field_metadata_rule_key (rule_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字段元数据表';

-- =====================================================
-- 第三步：验证表结构
-- =====================================================

-- 显示表结构
SHOW CREATE TABLE rule_template;
SHOW CREATE TABLE rule_detail;
SHOW CREATE TABLE rule_field_metadata;

-- 验证外键约束
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN ('rule_detail', 'rule_field_metadata');

-- =====================================================
-- 完成提示
-- =====================================================
SELECT '数据库表结构重建完成！' AS message;
