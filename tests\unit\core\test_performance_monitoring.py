"""
从节点性能监控和统计功能测试
验证SlaveIndexPerformanceStats类和相关功能
"""

import os
import tempfile
import time
import unittest
from unittest.mock import Mock, patch

from core.slave_node_index_builder import SlaveNodeIndexBuilder, SlaveIndexPerformanceStats, IndexHotReloader


class TestPerformanceMonitoring(unittest.TestCase):
    """性能监控测试类"""

    def setUp(self):
        """设置测试环境"""
        # 不再需要模拟环境变量，使用settings配置

        # 创建临时缓存文件
        self.temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".json.gz", delete=False)
        self.temp_file.close()

        # 模拟rule_index_manager
        self.mock_rule_index_manager = Mock()

    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def test_slave_index_performance_stats_initialization(self):
        """测试性能统计类初始化"""
        stats = SlaveIndexPerformanceStats()

        # 验证初始值
        self.assertEqual(stats.build_count, 0)
        self.assertEqual(stats.successful_builds, 0)
        self.assertEqual(stats.failed_builds, 0)
        self.assertEqual(stats.avg_build_time_ms, 0.0)
        self.assertEqual(stats.max_build_time_ms, 0.0)
        self.assertEqual(stats.min_build_time_ms, float("inf"))

        # 验证时间戳设置
        self.assertGreater(stats.stats_start_time, 0)

    def test_record_build_success(self):
        """测试记录构建成功"""
        stats = SlaveIndexPerformanceStats()

        # 记录第一次构建成功
        stats.record_build_success(build_time_ms=1000.0, memory_mb=200.0, rules_count=100)

        self.assertEqual(stats.build_count, 1)
        self.assertEqual(stats.successful_builds, 1)
        self.assertEqual(stats.failed_builds, 0)
        self.assertEqual(stats.avg_build_time_ms, 1000.0)
        self.assertEqual(stats.max_build_time_ms, 1000.0)
        self.assertEqual(stats.min_build_time_ms, 1000.0)
        self.assertEqual(stats.total_rules_processed, 100)
        self.assertEqual(stats.avg_memory_usage_mb, 200.0)
        self.assertEqual(stats.max_memory_usage_mb, 200.0)

        # 记录第二次构建成功
        stats.record_build_success(build_time_ms=1500.0, memory_mb=250.0, rules_count=150)

        self.assertEqual(stats.build_count, 2)
        self.assertEqual(stats.successful_builds, 2)
        self.assertEqual(stats.avg_build_time_ms, 1250.0)  # (1000 + 1500) / 2
        self.assertEqual(stats.max_build_time_ms, 1500.0)
        self.assertEqual(stats.min_build_time_ms, 1000.0)
        self.assertEqual(stats.total_rules_processed, 250)
        self.assertEqual(stats.avg_memory_usage_mb, 225.0)  # (200 + 250) / 2
        self.assertEqual(stats.max_memory_usage_mb, 250.0)

    def test_record_build_failure(self):
        """测试记录构建失败"""
        stats = SlaveIndexPerformanceStats()

        stats.record_build_failure(error_msg="Test error", memory_mb=180.0)

        self.assertEqual(stats.build_count, 1)
        self.assertEqual(stats.successful_builds, 0)
        self.assertEqual(stats.failed_builds, 1)
        self.assertEqual(stats.build_errors, 1)
        self.assertEqual(stats.avg_memory_usage_mb, 180.0)

    def test_record_hot_reload_success(self):
        """测试记录热重载成功"""
        stats = SlaveIndexPerformanceStats()

        stats.record_hot_reload_success(reload_time_ms=500.0)

        self.assertEqual(stats.hot_reload_count, 1)
        self.assertEqual(stats.successful_hot_reloads, 1)
        self.assertEqual(stats.failed_hot_reloads, 0)
        self.assertEqual(stats.avg_hot_reload_time_ms, 500.0)
        self.assertEqual(stats.max_hot_reload_time_ms, 500.0)

    def test_record_hot_reload_failure(self):
        """测试记录热重载失败"""
        stats = SlaveIndexPerformanceStats()

        stats.record_hot_reload_failure()

        self.assertEqual(stats.hot_reload_count, 1)
        self.assertEqual(stats.successful_hot_reloads, 0)
        self.assertEqual(stats.failed_hot_reloads, 1)

    def test_success_rates(self):
        """测试成功率计算"""
        stats = SlaveIndexPerformanceStats()

        # 测试初始状态
        self.assertEqual(stats.get_success_rate(), 0.0)
        self.assertEqual(stats.get_hot_reload_success_rate(), 0.0)

        # 记录一些数据
        stats.record_build_success(1000.0, 200.0, 100)
        stats.record_build_success(1200.0, 220.0, 120)
        stats.record_build_failure("error")

        stats.record_hot_reload_success(400.0)
        stats.record_hot_reload_failure()

        # 验证成功率
        self.assertAlmostEqual(stats.get_success_rate(), 66.67, places=1)  # 2/3 * 100
        self.assertEqual(stats.get_hot_reload_success_rate(), 50.0)  # 1/2 * 100

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_performance_stats_integration(self, mock_memory_optimizer_class):
        """测试性能统计集成"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        # 模拟内存状态
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 验证性能统计已初始化
        self.assertIsInstance(builder.performance_stats, SlaveIndexPerformanceStats)

        # 获取性能统计
        stats = builder.get_performance_stats()
        self.assertIsInstance(stats, SlaveIndexPerformanceStats)

        # 获取性能报告
        report = builder.get_performance_report()
        self.assertIsInstance(report, dict)
        self.assertIn("summary", report)
        self.assertIn("build_performance", report)
        self.assertIn("memory_performance", report)
        self.assertIn("hot_reload_performance", report)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_performance_anomaly_detection(self, mock_memory_optimizer_class):
        """测试性能异常检测"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 测试正常情况
        anomalies = builder.check_performance_anomalies()
        self.assertFalse(anomalies["has_anomalies"])
        self.assertEqual(anomalies["anomaly_count"], 0)

        # 模拟低成功率异常
        stats = builder.performance_stats
        for i in range(10):
            if i < 3:
                stats.record_build_success(1000.0, 200.0, 100)
            else:
                stats.record_build_failure("error")

        anomalies = builder.check_performance_anomalies()
        self.assertTrue(anomalies["has_anomalies"])
        self.assertGreater(anomalies["anomaly_count"], 0)

        # 检查是否包含低成功率异常
        anomaly_types = [a["type"] for a in anomalies["anomalies"]]
        self.assertIn("low_build_success_rate", anomaly_types)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_hot_reload_performance_integration(self, mock_memory_optimizer_class):
        """测试热重载性能统计集成"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 模拟成功的重载
        builder.build_index_from_cache_file = Mock(return_value=True)

        # 执行重载（这会触发性能统计）
        # 注意：由于异步操作的复杂性，这里主要测试统计方法的调用
        initial_count = builder.performance_stats.hot_reload_count

        # 直接调用统计方法
        builder.performance_stats.record_hot_reload_success(1000.0)

        self.assertEqual(builder.performance_stats.hot_reload_count, initial_count + 1)
        self.assertEqual(builder.performance_stats.successful_hot_reloads, 1)

    def test_performance_stats_reset(self):
        """测试性能统计重置"""
        stats = SlaveIndexPerformanceStats()

        # 添加一些数据
        stats.record_build_success(1000.0, 200.0, 100)
        stats.record_hot_reload_success(500.0)
        stats.record_memory_optimization()

        # 验证数据已记录
        self.assertGreater(stats.build_count, 0)
        self.assertGreater(stats.hot_reload_count, 0)
        self.assertGreater(stats.memory_optimization_count, 0)

        # 重置统计
        stats.reset_stats()

        # 验证数据已重置
        self.assertEqual(stats.build_count, 0)
        self.assertEqual(stats.hot_reload_count, 0)
        self.assertEqual(stats.memory_optimization_count, 0)
        self.assertGreater(stats.stats_start_time, 0)  # 重置后时间戳应该更新

    def test_throughput_calculation(self):
        """测试吞吐量计算"""
        stats = SlaveIndexPerformanceStats()

        # 手动设置开始时间为1分钟前
        stats.stats_start_time = time.time() - 60  # 1分钟前

        # 模拟一些处理
        stats.record_build_success(1000.0, 200.0, 1000)  # 1000条规则
        stats.record_build_success(1200.0, 220.0, 1500)  # 1500条规则

        # 获取吞吐量（规则/分钟）
        throughput = stats.get_avg_throughput()

        # 应该是2500条规则/分钟
        self.assertAlmostEqual(throughput, 2500.0, places=0)


if __name__ == "__main__":
    unittest.main()
