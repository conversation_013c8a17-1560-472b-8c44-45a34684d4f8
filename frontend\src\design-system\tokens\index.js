/**
 * 设计Token统一导出
 * 整合所有设计token，提供统一的设计系统接口
 */

import { colors } from './colors'
import { typography } from './typography'
import { spacingTokens } from './spacing'

// 阴影设计Token
export const shadows = {
  // 基础阴影
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',

  // 彩色阴影
  colored: {
    primary: '0 4px 14px 0 rgba(59, 130, 246, 0.15)',
    success: '0 4px 14px 0 rgba(34, 197, 94, 0.15)',
    warning: '0 4px 14px 0 rgba(249, 115, 22, 0.15)',
    error: '0 4px 14px 0 rgba(239, 68, 68, 0.15)'
  },

  // 悬浮阴影
  hover: {
    sm: '0 2px 4px 0 rgba(0, 0, 0, 0.1)',
    md: '0 6px 12px -2px rgba(0, 0, 0, 0.15)',
    lg: '0 12px 24px -4px rgba(0, 0, 0, 0.15)'
  },

  // 焦点阴影
  focus: {
    primary: '0 0 0 3px rgba(59, 130, 246, 0.1)',
    success: '0 0 0 3px rgba(34, 197, 94, 0.1)',
    warning: '0 0 0 3px rgba(249, 115, 22, 0.1)',
    error: '0 0 0 3px rgba(239, 68, 68, 0.1)'
  }
}

// 边框半径设计Token
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px'
}

// 边框宽度设计Token
export const borderWidth = {
  0: '0',
  base: '1px',
  2: '2px',
  4: '4px',
  8: '8px'
}

// 透明度设计Token
export const opacity = {
  0: '0',
  5: '0.05',
  10: '0.1',
  20: '0.2',
  25: '0.25',
  30: '0.3',
  40: '0.4',
  50: '0.5',
  60: '0.6',
  70: '0.7',
  75: '0.75',
  80: '0.8',
  90: '0.9',
  95: '0.95',
  100: '1'
}

// Z-index设计Token
export const zIndex = {
  auto: 'auto',
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  dropdown: '1000',
  sticky: '1020',
  fixed: '1030',
  modal: '1040',
  popover: '1050',
  tooltip: '1060',
  toast: '1070',
  max: '9999'
}

// 过渡动画设计Token
export const transitions = {
  // 持续时间
  duration: {
    75: '75ms',
    100: '100ms',
    150: '150ms',
    200: '200ms',
    300: '300ms',
    500: '500ms',
    700: '700ms',
    1000: '1000ms'
  },

  // 缓动函数
  timing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    smooth: 'cubic-bezier(0.4, 0, 0.2, 1)'
  },

  // 预设过渡
  presets: {
    fast: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    normal: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    slow: 'all 500ms cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'all 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55)'
  }
}

// 断点设计Token
export const breakpoints = {
  xs: '0px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
}

// 组件尺寸预设
export const componentSizes = {
  // 按钮尺寸
  button: {
    xs: {
      height: spacingTokens.size.button.xs,
      padding: spacingTokens.padding.button.xs,
      fontSize: typography.fontSize.xs,
      borderRadius: borderRadius.sm
    },
    sm: {
      height: spacingTokens.size.button.sm,
      padding: spacingTokens.padding.button.sm,
      fontSize: typography.fontSize.sm,
      borderRadius: borderRadius.base
    },
    md: {
      height: spacingTokens.size.button.md,
      padding: spacingTokens.padding.button.md,
      fontSize: typography.fontSize.base,
      borderRadius: borderRadius.md
    },
    lg: {
      height: spacingTokens.size.button.lg,
      padding: spacingTokens.padding.button.lg,
      fontSize: typography.fontSize.lg,
      borderRadius: borderRadius.lg
    },
    xl: {
      height: spacingTokens.size.button.xl,
      padding: spacingTokens.padding.button.xl,
      fontSize: typography.fontSize.xl,
      borderRadius: borderRadius.xl
    }
  },

  // 输入框尺寸
  input: {
    xs: {
      height: spacingTokens.size.input.xs,
      padding: spacingTokens.padding.input.xs,
      fontSize: typography.fontSize.xs,
      borderRadius: borderRadius.sm
    },
    sm: {
      height: spacingTokens.size.input.sm,
      padding: spacingTokens.padding.input.sm,
      fontSize: typography.fontSize.sm,
      borderRadius: borderRadius.base
    },
    md: {
      height: spacingTokens.size.input.md,
      padding: spacingTokens.padding.input.md,
      fontSize: typography.fontSize.base,
      borderRadius: borderRadius.md
    },
    lg: {
      height: spacingTokens.size.input.lg,
      padding: spacingTokens.padding.input.lg,
      fontSize: typography.fontSize.lg,
      borderRadius: borderRadius.lg
    },
    xl: {
      height: spacingTokens.size.input.xl,
      padding: spacingTokens.padding.input.xl,
      fontSize: typography.fontSize.xl,
      borderRadius: borderRadius.xl
    }
  }
}

// 设计系统工具函数
export const designSystemUtils = {
  // 获取组件样式
  getComponentStyle: (component, size = 'md', variant = 'default') => {
    const componentConfig = componentSizes[component]
    if (!componentConfig) return {}

    const sizeConfig = componentConfig[size] || componentConfig.md
    return sizeConfig
  },

  // 生成响应式样式
  generateResponsiveStyle: (property, values) => {
    const breakpointKeys = Object.keys(breakpoints)
    const styles = {}

    breakpointKeys.forEach((breakpoint, index) => {
      if (values[breakpoint]) {
        if (index === 0) {
          styles[property] = values[breakpoint]
        } else {
          styles[`@media (min-width: ${breakpoints[breakpoint]})`] = {
            [property]: values[breakpoint]
          }
        }
      }
    })

    return styles
  },

  // 获取主题变量
  getThemeVariables: (theme = 'light') => {
    return {
      // CSS自定义属性
      '--color-primary': colors.semantic.primary.main,
      '--color-success': colors.semantic.success.main,
      '--color-warning': colors.semantic.warning.main,
      '--color-error': colors.semantic.error.main,
      '--color-text-primary': colors.text.primary,
      '--color-text-secondary': colors.text.secondary,
      '--color-background': colors.background.default,
      '--color-border': colors.border.default,
      '--font-family-primary': typography.fontFamily.primary,
      '--font-size-base': typography.fontSize.base,
      '--spacing-base': spacingTokens.spacing[4],
      '--border-radius-base': borderRadius.base,
      '--shadow-base': shadows.base,
      '--transition-base': transitions.presets.normal
    }
  }
}

// 统一导出所有设计token
export const designTokens = {
  colors,
  typography,
  spacing: spacingTokens,
  shadows,
  borderRadius,
  borderWidth,
  opacity,
  zIndex,
  transitions,
  breakpoints,
  componentSizes,
  utils: designSystemUtils
}

// 默认导出
export default designTokens

// 单独导出各个模块
export {
  colors,
  typography,
  spacingTokens as spacing
}
