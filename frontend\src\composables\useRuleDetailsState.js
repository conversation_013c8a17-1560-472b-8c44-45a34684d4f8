/**
 * 规则详情状态管理组合式函数
 * 提供规则详情相关的状态管理、缓存优化和错误恢复功能
 */

import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRuleDetailsStore } from '@/stores/ruleDetails'
import { useAsyncState } from '@/composables/core/useAsyncState'
import { useErrorRecovery } from '@/composables/core/useErrorRecovery'
import { useFeedback } from '@/composables/ui/useFeedback'

/**
 * 规则详情状态管理
 * @param {Object} options - 配置选项
 * @returns {Object} 状态管理对象
 */
export function useRuleDetailsState(options = {}) {
  const {
    ruleKey = null,
    autoLoad = true,
    enableCache = true,
    enableErrorRecovery = true,
    retryCount = 3,
    retryDelay = [1000, 2000, 4000]
  } = options

  // Store 实例
  const store = useRuleDetailsStore()
  const feedback = useFeedback()

  // 本地状态
  const currentRuleKey = ref(ruleKey)
  const isInitialized = ref(false)
  const lastError = ref(null)

  // 错误恢复配置
  const errorRecovery = useErrorRecovery({
    retryCount,
    retryDelay,
    onRetry: (attempt) => {
      feedback.toastInfo(`正在重试获取规则详情 (${attempt}/${retryCount})`)
    },
    onMaxRetriesReached: () => {
      feedback.toastError('获取规则详情失败，已达到最大重试次数')
    }
  })

  // 异步状态管理
  const detailsListState = useAsyncState(
    async (ruleKey, params) => {
      if (!ruleKey) return null
      return await store.fetchDetailsList(ruleKey, params)
    },
    {
      retryCount: enableErrorRecovery ? retryCount : 0,
      retryDelay,
      cacheKey: computed(() => `rule_details_${currentRuleKey.value}`),
      cacheTTL: 5 * 60 * 1000, // 5分钟
      enableCache,
      onSuccess: (data) => {
        lastError.value = null
        feedback.toastSuccess('规则详情加载成功')
      },
      onError: (error) => {
        lastError.value = error
        if (enableErrorRecovery) {
          errorRecovery.recover(error)
        }
      }
    }
  )

  // 计算属性
  const isLoading = computed(() =>
    store.isLoading || detailsListState.isLoading.value
  )

  const hasError = computed(() =>
    lastError.value !== null || store.operationStatus.errors.length > 0
  )

  const canOperate = computed(() =>
    !isLoading.value && !hasError.value && isInitialized.value
  )

  const stateInfo = computed(() => ({
    isLoading: isLoading.value,
    hasError: hasError.value,
    canOperate: canOperate.value,
    isInitialized: isInitialized.value,
    currentRuleKey: currentRuleKey.value,
    detailsCount: store.detailsCount,
    selectedCount: store.selectedCount,
    dataQuality: store.dataQuality,
    operationSummary: store.operationSummary
  }))

  // 方法
  const initialize = async (newRuleKey = null) => {
    if (newRuleKey) {
      currentRuleKey.value = newRuleKey
    }

    if (!currentRuleKey.value) {
      console.warn('规则键为空，无法初始化')
      return false
    }

    try {
      isInitialized.value = false

      if (autoLoad) {
        await loadDetails()
      }

      isInitialized.value = true
      return true
    } catch (error) {
      console.error('初始化失败:', error)
      lastError.value = error
      return false
    }
  }

  const loadDetails = async (params = {}) => {
    if (!currentRuleKey.value) return null

    try {
      const result = await detailsListState.execute(currentRuleKey.value, params)
      return result
    } catch (error) {
      console.error('加载规则详情失败:', error)
      throw error
    }
  }

  const refreshDetails = async () => {
    if (!currentRuleKey.value) return null

    // 清除缓存并重新加载
    detailsListState.clearCache()
    return await loadDetails()
  }

  const switchRule = async (newRuleKey) => {
    if (newRuleKey === currentRuleKey.value) return

    // 清理当前状态
    store.clearCurrentDetail()
    store.clearSelection()
    lastError.value = null

    // 切换到新规则
    currentRuleKey.value = newRuleKey
    await initialize(newRuleKey)
  }

  const clearError = () => {
    lastError.value = null
    store.operationStatus.errors = []
  }

  const getStateSnapshot = () => ({
    ruleKey: currentRuleKey.value,
    detailsList: store.detailsList,
    currentDetail: store.currentDetail,
    selectedDetails: store.selectedDetails,
    pagination: store.pagination,
    filters: store.filters,
    timestamp: Date.now()
  })

  const restoreStateSnapshot = (snapshot) => {
    if (!snapshot || snapshot.ruleKey !== currentRuleKey.value) return false

    try {
      store.detailsList = snapshot.detailsList || []
      store.currentDetail = snapshot.currentDetail || null
      store.selectedDetails = snapshot.selectedDetails || []
      store.pagination = { ...store.pagination, ...snapshot.pagination }
      store.filters = { ...store.filters, ...snapshot.filters }
      return true
    } catch (error) {
      console.error('恢复状态快照失败:', error)
      return false
    }
  }

  // 监听器
  watch(
    () => currentRuleKey.value,
    (newKey, oldKey) => {
      if (newKey !== oldKey && newKey && autoLoad) {
        initialize(newKey)
      }
    }
  )

  // 生命周期
  onMounted(() => {
    if (currentRuleKey.value && autoLoad) {
      initialize()
    }
  })

  onUnmounted(() => {
    // 清理资源
    detailsListState.cleanup?.()
    errorRecovery.cleanup?.()
  })

  return {
    // 状态
    currentRuleKey,
    isInitialized,
    lastError,

    // 计算属性
    isLoading,
    hasError,
    canOperate,
    stateInfo,

    // Store 状态代理
    detailsList: computed(() => store.detailsList),
    currentDetail: computed(() => store.currentDetail),
    selectedDetails: computed(() => store.selectedDetails),
    pagination: computed(() => store.pagination),
    filters: computed(() => store.filters),

    // 方法
    initialize,
    loadDetails,
    refreshDetails,
    switchRule,
    clearError,
    getStateSnapshot,
    restoreStateSnapshot,

    // Store 方法代理
    fetchDetailById: store.fetchDetailById,
    createDetail: store.createDetail,
    updateDetail: store.updateDetail,
    deleteDetail: store.deleteDetail,
    searchDetails: store.searchDetails,
    updateFilters: store.updateFilters,
    resetFilters: store.resetFilters,
    selectDetail: store.selectDetail,
    clearSelection: store.clearSelection,

    // 异步状态
    detailsListState
  }
}

/**
 * 规则详情批量操作管理
 * @param {Object} ruleDetailsState - 规则详情状态管理实例
 * @returns {Object} 批量操作管理对象
 */
export function useRuleDetailsBatch(ruleDetailsState) {
  const store = useRuleDetailsStore()
  const feedback = useFeedback()

  const batchOperationState = ref({
    isProcessing: false,
    currentOperation: null,
    progress: 0,
    total: 0,
    results: []
  })

  const startBatchOperation = (operationType, items) => {
    batchOperationState.value = {
      isProcessing: true,
      currentOperation: operationType,
      progress: 0,
      total: items.length,
      results: []
    }
  }

  const updateBatchProgress = (completed, result = null) => {
    batchOperationState.value.progress = completed
    if (result) {
      batchOperationState.value.results.push(result)
    }
  }

  const finishBatchOperation = () => {
    const { currentOperation, total, results } = batchOperationState.value
    const successCount = results.filter(r => r.success).length

    feedback.toastSuccess(
      `${currentOperation}完成：成功 ${successCount}/${total} 项`
    )

    batchOperationState.value.isProcessing = false
  }

  return {
    batchOperationState,
    startBatchOperation,
    updateBatchProgress,
    finishBatchOperation,

    // 批量操作方法
    batchCreate: store.batchCreateDetails,
    batchUpdate: store.batchUpdateDetails,
    batchDelete: store.batchDeleteDetails
  }
}
