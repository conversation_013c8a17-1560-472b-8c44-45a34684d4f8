/**
 * 状态管理基础功能测试
 * 验证Store的基本状态管理功能
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'

// Mock所有外部依赖
vi.mock('@/composables/core/useAsyncState', () => ({
  useAsyncState: vi.fn(() => ({
    isLoading: { value: false },
    execute: vi.fn()
  }))
}))

vi.mock('@/composables/core/useStateMachine', () => ({
  useStateMachine: vi.fn(() => ({
    start: vi.fn(),
    success: vi.fn(),
    error: vi.fn()
  }))
}))

vi.mock('@/composables/ui/useFeedback', () => ({
  useFeedback: vi.fn(() => ({
    toastSuccess: vi.fn(),
    toastError: vi.fn(),
    toastInfo: vi.fn()
  }))
}))

vi.mock('../app', () => ({
  useAppStore: vi.fn(() => ({
    addLoadingTask: vi.fn(),
    removeLoadingTask: vi.fn(),
    setError: vi.fn()
  }))
}))

vi.mock('@/api/enhancedRuleDetails', () => ({
  enhancedRuleDetailsApi: {
    initialize: vi.fn().mockResolvedValue(true),
    getCacheStats: vi.fn().mockResolvedValue({ size: 10 }),
    clearCache: vi.fn()
  }
}))

vi.mock('@/utils/enhancedErrorHandler', () => ({
  enhancedErrorHandler: {
    handle: vi.fn()
  }
}))

vi.mock('../../api/rules', () => ({
  getRulesStatus: vi.fn().mockResolvedValue({
    success: true,
    data: []
  })
}))

describe('状态管理基础功能', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('RuleDetailsStore 基础状态', () => {
    it('应该能够导入和初始化', async () => {
      const { useRuleDetailsStore } = await import('../ruleDetails.js')
      const store = useRuleDetailsStore()

      expect(store).toBeDefined()
      expect(store.detailsList).toEqual([])
      expect(store.currentDetail).toBeNull()
      expect(store.selectedDetails).toEqual([])
    })

    it('应该有正确的计算属性', async () => {
      const { useRuleDetailsStore } = await import('../ruleDetails.js')
      const store = useRuleDetailsStore()

      expect(store.detailsCount).toBe(0)
      expect(store.hasDetails).toBe(false)
      expect(store.selectedCount).toBe(0)
      expect(store.hasSelected).toBe(false)
    })

    it('应该有增强的状态管理功能', async () => {
      const { useRuleDetailsStore } = await import('../ruleDetails.js')
      const store = useRuleDetailsStore()

      expect(store.operationStatus).toBeDefined()
      expect(store.operationStatus.retryCount).toBe(0)
      expect(store.operationStatus.maxRetries).toBe(3)
      expect(store.operationStatus.isRecovering).toBe(false)
    })

    it('应该有缓存管理器', async () => {
      const { useRuleDetailsStore } = await import('../ruleDetails.js')
      const store = useRuleDetailsStore()

      expect(store.cacheManager).toBeDefined()
      expect(store.cacheManager.autoCleanup).toBe(true)
      expect(store.cacheManager.maxAge).toBe(5 * 60 * 1000)
    })
  })

  describe('RulesStore 基础状态', () => {
    it('应该能够导入和初始化', async () => {
      const { useRulesStore } = await import('../rules.js')
      const store = useRulesStore()

      expect(store).toBeDefined()
      expect(store.rules).toEqual([])
      expect(store.loading).toBe(false)
      expect(store.currentRule).toBeNull()
    })

    it('应该有缓存配置', async () => {
      const { useRulesStore } = await import('../rules.js')
      const store = useRulesStore()

      expect(store.cacheConfig).toBeDefined()
      expect(store.cacheConfig.maxSize).toBe(50)
      expect(store.cacheConfig.ttl).toBe(10 * 60 * 1000)
      expect(store.cacheConfig.autoCleanup).toBe(true)
    })

    it('应该有操作指标', async () => {
      const { useRulesStore } = await import('../rules.js')
      const store = useRulesStore()

      expect(store.operationMetrics).toBeDefined()
      expect(store.operationMetrics.totalRequests).toBe(0)
      expect(store.operationMetrics.successfulRequests).toBe(0)
      expect(store.operationMetrics.failedRequests).toBe(0)
    })

    it('应该有增强的计算属性', async () => {
      const { useRulesStore } = await import('../rules.js')
      const store = useRulesStore()

      expect(store.loadingState).toBeDefined()
      expect(store.cachePerformance).toBeDefined()
      expect(store.systemHealth).toBeDefined()
    })
  })

  describe('组合式函数', () => {
    it('应该能够导入useRuleDetailsState', async () => {
      const { useRuleDetailsState } = await import('../../composables/useRuleDetailsState.js')

      expect(useRuleDetailsState).toBeDefined()
      expect(typeof useRuleDetailsState).toBe('function')
    })

    it('应该能够导入useRulesCache', async () => {
      const { useRulesCache } = await import('../../composables/useRulesCache.js')

      expect(useRulesCache).toBeDefined()
      expect(typeof useRulesCache).toBe('function')
    })

    it('应该有正确的缓存策略枚举', async () => {
      const { CacheStrategy, CachePriority } = await import('../../composables/useRulesCache.js')

      expect(CacheStrategy).toBeDefined()
      expect(CacheStrategy.MEMORY_ONLY).toBe('memory_only')
      expect(CacheStrategy.HYBRID).toBe('hybrid')

      expect(CachePriority).toBeDefined()
      expect(CachePriority.LOW).toBe(1)
      expect(CachePriority.HIGH).toBe(3)
    })
  })

  describe('状态管理集成', () => {
    it('应该能够创建和使用状态管理实例', async () => {
      const { useRuleDetailsState } = await import('../../composables/useRuleDetailsState.js')

      const stateManager = useRuleDetailsState({
        ruleKey: 'test-rule',
        autoLoad: false
      })

      expect(stateManager).toBeDefined()
      expect(stateManager.currentRuleKey.value).toBe('test-rule')
      expect(stateManager.isInitialized.value).toBe(false)
    })

    it('应该能够创建和使用缓存管理实例', async () => {
      const { useRulesCache, CacheStrategy } = await import('../../composables/useRulesCache.js')

      const cacheManager = useRulesCache({
        strategy: CacheStrategy.MEMORY_ONLY,
        maxMemorySize: 20
      })

      expect(cacheManager).toBeDefined()
      expect(cacheManager.strategy).toBe(CacheStrategy.MEMORY_ONLY)
      expect(cacheManager.maxMemorySize).toBe(20)
    })
  })

  describe('错误处理', () => {
    it('应该能够处理Store初始化错误', async () => {
      // 模拟初始化错误
      const mockError = new Error('初始化失败')
      vi.mocked(vi.fn()).mockRejectedValueOnce(mockError)

      const { useRuleDetailsStore } = await import('../ruleDetails.js')
      const store = useRuleDetailsStore()

      // Store应该仍然可以创建，但会有错误处理
      expect(store).toBeDefined()
    })
  })

  describe('性能指标', () => {
    it('应该有updateMetrics方法', async () => {
      const { useRulesStore } = await import('../rules.js')
      const store = useRulesStore()

      expect(typeof store.updateMetrics).toBe('function')
      expect(store.operationMetrics).toBeDefined()
    })

    it('应该能够计算系统健康状态', async () => {
      const { useRulesStore } = await import('../rules.js')
      const store = useRulesStore()

      const health = store.systemHealth
      expect(health).toBeDefined()
      expect(health.status).toBeDefined()
      expect(health.score).toBeDefined()
    })
  })
})
