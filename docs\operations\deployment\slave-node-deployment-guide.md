# 子节点部署指南

本文档提供子节点（Slave Node）的完整部署指南，包括离线模式和在线同步模式的配置方法。

## 概述

子节点是规则验证系统的高性能验证组件，专门负责规则验证处理。子节点的核心特点：

- **无数据库依赖**：完全脱离数据库运行
- **高性能验证**：专注于规则验证性能优化
- **离线支持**：支持完全离线模式运行
- **灵活部署**：适合各种网络环境

## 部署前准备

### 系统要求

- **操作系统**：Linux/Windows/macOS
- **Python版本**：3.12+
- **内存**：建议 4GB+
- **CPU**：建议 4核+
- **磁盘空间**：1GB+

### 必需文件

1. **应用代码**：完整的项目代码
2. **规则缓存文件**：`rules_cache.json.gz`（从主节点获取）
3. **配置文件**：`.env`（子节点专用配置）

## 配置文件设置

### 1. 创建子节点配置

复制 `.env.slave.template` 为 `.env`：

```bash
cp .env.slave.template .env
```

### 2. 基础配置

```bash
# ===== 应用模式配置 =====
MODE=slave
RUN_MODE=PROD

# ===== 服务器配置 =====
SERVER_HOST=0.0.0.0
SERVER_PORT=18001
LOG_LEVEL=INFO
```

### 3. 离线模式配置（推荐）

```bash
# ===== 规则同步配置 =====
ENABLE_RULE_SYNC=false

# ===== 性能优化配置 =====
ENABLE_PERFORMANCE_OPTIMIZATION=true
WORKERS=8
WORKER_COUNT=8
PROCESS_POOL_SIZE=4
CACHE_SIZE=1000
QUEUE_MAX_SIZE=1000
```

### 4. 在线同步模式配置

```bash
# ===== 主节点连接配置 =====
MASTER_API_ENDPOINT=http://***************:18001
SLAVE_API_KEY=your_secure_api_key_here

# ===== 规则同步配置 =====
ENABLE_RULE_SYNC=true
RULE_SYNC_INTERVAL=60
RULE_SYNC_TIMEOUT=120.0
RULE_SYNC_MAX_RETRIES=3
RULE_SYNC_RETRY_INTERVAL=30.0
```

## 部署步骤

### 方式一：离线模式部署（推荐）

#### 1. 准备规则缓存文件

从主节点获取最新的规则缓存文件：

```bash
# 从主节点复制规则缓存文件
scp user@master-node:/path/to/rules_cache.json.gz ./
```

或者通过主节点API下载：

```bash
curl -H "Authorization: Bearer your_api_key" \
     http://master-node:18001/api/v1/rules/export \
     -o rules_cache.json.gz
```

#### 2. 验证缓存文件

```bash
# 检查文件大小（应该 > 100KB）
ls -lh rules_cache.json.gz

# 验证文件格式
file rules_cache.json.gz
```

#### 3. 安装依赖

```bash
pip install -r requirements.txt
```

#### 4. 启动服务

```bash
# 直接启动
python slave.py

# 或使用 uvicorn
uvicorn slave:app --host 0.0.0.0 --port 18001

# 后台运行
nohup uvicorn slave:app --host 0.0.0.0 --port 18001 > slave.log 2>&1 &
```

### 方式二：在线同步模式部署

#### 1. 配置主节点连接

确保网络连接到主节点，并配置正确的API密钥。

#### 2. 启动服务

```bash
uvicorn slave:app --host 0.0.0.0 --port 18001
```

服务启动时会自动从主节点同步规则。

## 验证部署

### 1. 健康检查

```bash
curl http://localhost:18001/
```

期望响应：
```json
{
  "code": 200,
  "success": true,
  "message": "从节点运行正常",
  "data": {
    "status": "healthy",
    "node_type": "slave",
    "service": "Rule Validation Service"
  }
}
```

### 2. 规则信息检查

```bash
curl http://localhost:18001/rules
```

期望响应：
```json
{
  "code": 200,
  "success": true,
  "message": "从节点规则信息获取成功，共加载 1418 个规则",
  "data": {
    "node_type": "slave",
    "total_rules": 1418,
    "cache_status": "loaded"
  }
}
```

### 3. 验证功能测试

```bash
curl -X POST http://localhost:18001/api/v1/validate \
     -H "Content-Type: application/json" \
     -d '{
       "patientInfo": {
         "bah": "TEST123456",
         "name": "测试患者",
         "gender": "男",
         "age": 45
       },
       "ids": ["rule_id_example"]
     }'
```

## Docker 部署

### 1. 使用预构建镜像

```bash
# 拉取镜像
docker pull registry.company.com/rule-slave:latest

# 运行容器
docker run -d \
  --name rule-slave \
  --network host \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/rules_cache.json.gz:/app/rules_cache.json.gz \
  registry.company.com/rule-slave:latest
```

### 2. 使用 docker-compose

```yaml
version: '3.8'
services:
  rule-slave:
    image: registry.company.com/rule-slave:latest
    container_name: rule-slave
    network_mode: host
    volumes:
      - ./.env:/app/.env
      - ./rules_cache.json.gz:/app/rules_cache.json.gz
      - ./logs:/app/logs
    restart: unless-stopped
```

启动：
```bash
docker-compose up -d
```

## 性能调优

### 1. 工作进程配置

根据服务器配置调整工作进程数：

```bash
# CPU 核心数
WORKERS=8
WORKER_COUNT=8

# 进程池大小（建议为 CPU 核心数的一半）
PROCESS_POOL_SIZE=4
```

### 2. 内存配置

```bash
# 缓存大小
CACHE_SIZE=1000

# 队列大小
QUEUE_MAX_SIZE=1000
```

### 3. 日志配置

```bash
# 生产环境日志配置
LOG_LEVEL=WARNING
LOG_ROTATION=50 MB
LOG_RETENTION=30 days
LOG_COMPRESSION=gz
```

## 监控和维护

### 1. 日志监控

```bash
# 查看实时日志
tail -f logs/slave_*.log

# 检查错误日志
grep -i error logs/slave_*.log
```

### 2. 性能监控

```bash
# 检查进程状态
ps aux | grep slave

# 检查内存使用
free -h

# 检查CPU使用
top -p $(pgrep -f slave)
```

### 3. 规则更新

#### 离线模式更新

```bash
# 1. 从主节点获取新的规则缓存文件
scp user@master-node:/path/to/rules_cache.json.gz ./rules_cache.json.gz.new

# 2. 备份当前文件
mv rules_cache.json.gz rules_cache.json.gz.backup

# 3. 使用新文件
mv rules_cache.json.gz.new rules_cache.json.gz

# 4. 重启服务
systemctl restart rule-slave
```

#### 在线模式更新

在线模式下，规则会自动同步，无需手动更新。

## 故障排查

### 常见问题

#### 1. 服务启动失败

**症状**：服务无法启动
**检查**：
```bash
# 检查配置文件
cat .env

# 检查端口占用
netstat -tlnp | grep 18001

# 检查日志
tail -f logs/slave_*.log
```

#### 2. 规则加载失败

**症状**：规则数量为0或加载错误
**检查**：
```bash
# 检查缓存文件
ls -lh rules_cache.json.gz
file rules_cache.json.gz

# 检查文件权限
ls -la rules_cache.json.gz
```

#### 3. 验证请求失败

**症状**：API返回错误
**检查**：
```bash
# 检查服务状态
curl http://localhost:18001/

# 检查规则状态
curl http://localhost:18001/rules

# 检查日志
grep -i error logs/slave_*.log
```

### 错误代码对照

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| 500 | 内部服务器错误 | 检查日志，重启服务 |
| 404 | 接口不存在 | 检查API路径 |
| 422 | 请求参数错误 | 检查请求格式 |

## 安全建议

1. **网络安全**：
   - 使用防火墙限制访问端口
   - 配置HTTPS（生产环境）
   - 定期更新API密钥

2. **文件权限**：
   ```bash
   chmod 600 .env
   chmod 644 rules_cache.json.gz
   ```

3. **日志安全**：
   - 定期清理日志文件
   - 避免在日志中记录敏感信息

## 配置验证工具

使用配置验证脚本检查配置正确性：

```bash
# 验证默认配置文件
python tools/validate_slave_config.py

# 验证指定配置文件
python tools/validate_slave_config.py custom.env
```

## 支持和联系

如遇到问题，请：

1. 使用配置验证工具检查配置
2. 查看本文档的故障排查章节
3. 检查项目的 GitHub Issues
4. 查看修复记录：`.issues/slave-node-database-connection-fix.md`
5. 联系技术支持团队

## 相关文档

- [主从架构设计](../README.md#主从架构设计)
- [配置管理指南](../README.md#配置管理)
- [修复记录](../.issues/slave-node-database-connection-fix.md)
- [Docker部署指南](../docker-compose.slave.yml)

---

**版本**：v1.1
**更新时间**：2025-07-11
**适用版本**：规则验证系统 v2.1+
**修复版本**：SLAVE-DB-001
