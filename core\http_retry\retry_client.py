"""
HTTP重试客户端实现
提供透明的重试功能，集成指数退避和断路器模式
"""

import asyncio
import logging
import time
from typing import Any

import httpx

from config.retry_config import get_retry_config_manager

from .backoff import BackoffStrategy, calculate_backoff_delay
from .circuit_breaker import CircuitBreakerConfig, get_circuit_breaker_manager

logger = logging.getLogger(__name__)


class RetryExhaustedError(Exception):
    """重试次数耗尽异常"""

    def __init__(self, message: str, last_exception: Exception, attempts: int):
        super().__init__(message)
        self.last_exception = last_exception
        self.attempts = attempts


class CircuitBreakerOpenError(Exception):
    """断路器打开异常"""

    def __init__(self, message: str, circuit_name: str):
        super().__init__(message)
        self.circuit_name = circuit_name


class RetryClient:
    """HTTP重试客户端

    包装httpx.AsyncClient，提供透明的重试功能
    """

    def __init__(
        self,
        base_client: httpx.AsyncClient | None = None,
        retry_config: dict[str, Any] | None = None,
        circuit_breaker_name: str | None = None,
        enable_circuit_breaker: bool = True,
    ):
        """
        初始化重试客户端

        Args:
            base_client: 基础HTTP客户端，如果为None则创建默认客户端
            retry_config: 重试配置，如果为None则使用全局配置
            circuit_breaker_name: 断路器名称，如果为None则使用默认名称
            enable_circuit_breaker: 是否启用断路器
        """
        self.base_client = base_client or httpx.AsyncClient()
        self.enable_circuit_breaker = enable_circuit_breaker

        # 初始化配置管理器
        self.config_manager = get_retry_config_manager()
        self.retry_config = self._get_retry_config(retry_config)

        # 初始化断路器
        self.circuit_breaker = None
        if enable_circuit_breaker:
            self.circuit_breaker_manager = get_circuit_breaker_manager()
            self.circuit_breaker_name = circuit_breaker_name or "http_client"
            self._init_circuit_breaker()

        # 重试统计
        self.retry_stats = {
            "total_requests": 0,
            "retry_requests": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "circuit_breaker_trips": 0,
        }

        logger.info(f"RetryClient初始化完成，断路器: {self.enable_circuit_breaker}")

    def _get_retry_config(self, custom_config: dict[str, Any] | None) -> dict[str, Any]:
        """获取重试配置"""
        # 默认配置
        default_config = {
            "enabled": True,
            "max_attempts": 3,
            "base_delay": 1.0,
            "max_delay": 60.0,
            "backoff_factor": 2.0,
            "jitter": True,
            "retryable_status_codes": [408, 429, 500, 502, 503, 504],
            "retry_on_connection_error": True,
            "retry_on_timeout_error": True,
        }

        # 尝试从配置管理器获取配置
        if self.config_manager:
            try:
                config = self.config_manager.get_retry_config()
                default_config.update(
                    {
                        "enabled": config.enabled,
                        "max_attempts": config.max_attempts,
                        "base_delay": config.base_delay,
                        "max_delay": config.max_delay,
                        "backoff_factor": config.backoff_factor,
                        "jitter": config.jitter,
                        "retryable_status_codes": config.retryable_status_codes,
                        "retry_on_connection_error": config.retry_on_connection_error,
                        "retry_on_timeout_error": config.retry_on_timeout_error,
                    }
                )
            except Exception as e:
                logger.warning(f"获取重试配置失败，使用默认配置: {e}")

        # 应用自定义配置
        if custom_config:
            default_config.update(custom_config)

        return default_config

    def _init_circuit_breaker(self):
        """初始化断路器"""
        try:
            # 获取断路器配置
            circuit_config = None
            if self.config_manager:
                retry_circuit_config = self.config_manager.get_circuit_breaker_config()
                if retry_circuit_config:
                    # 将config.retry_config.CircuitBreakerConfig转换为core.http_retry.circuit_breaker.CircuitBreakerConfig  # noqa: E501
                    circuit_config = CircuitBreakerConfig(
                        failure_threshold=retry_circuit_config.failure_threshold,
                        failure_rate_threshold=retry_circuit_config.failure_rate_threshold,
                        recovery_timeout=retry_circuit_config.recovery_timeout,
                        window_size=retry_circuit_config.window_size,
                        min_requests_threshold=10,  # 智能断路器特有参数，使用默认值
                        half_open_max_calls=retry_circuit_config.half_open_max_calls,
                        enable_metrics=True,  # 智能断路器特有参数，启用指标收集
                    )

            if not circuit_config:
                # 使用默认断路器配置
                circuit_config = CircuitBreakerConfig(
                    failure_threshold=5,
                    failure_rate_threshold=0.5,
                    recovery_timeout=60.0,
                    window_size=100,
                    half_open_max_calls=3,
                )

            self.circuit_breaker = self.circuit_breaker_manager.get_circuit_breaker(
                self.circuit_breaker_name, circuit_config
            )

        except Exception as e:
            logger.warning(f"断路器初始化失败，禁用断路器功能: {e}")
            self.enable_circuit_breaker = False
            self.circuit_breaker = None

    def _is_retryable_error(self, error: Exception, response: httpx.Response | None = None) -> bool:
        """判断错误是否可重试"""
        # 检查HTTP状态码
        if response is not None:
            status_code = response.status_code
            if status_code in self.retry_config["retryable_status_codes"]:
                return True
            # 4xx错误（除了408, 429）通常不可重试
            if 400 <= status_code < 500 and status_code not in [408, 429]:
                return False

        # 检查异常类型
        error_type = type(error).__name__

        # 连接错误
        if "ConnectError" in error_type and self.retry_config["retry_on_connection_error"]:
            return True

        # 超时错误
        if "TimeoutException" in error_type and self.retry_config["retry_on_timeout_error"]:
            return True

        # httpx特定错误
        if isinstance(error, httpx.ConnectError | httpx.TimeoutException):
            return True

        # 网络相关错误
        if isinstance(error, ConnectionError | TimeoutError):
            return True

        return False

    async def request(self, method: str, url: str | httpx.URL, **kwargs) -> httpx.Response:
        """
        执行带重试的HTTP请求

        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数

        Returns:
            httpx.Response: HTTP响应

        Raises:
            RetryExhaustedError: 重试次数耗尽
            CircuitBreakerOpenError: 断路器打开
        """
        self.retry_stats["total_requests"] += 1

        # 检查重试是否启用
        if not self.retry_config["enabled"]:
            return await self.base_client.request(method, url, **kwargs)

        # 检查断路器状态
        if self.enable_circuit_breaker and self.circuit_breaker:
            if not self.circuit_breaker.can_execute():
                self.retry_stats["circuit_breaker_trips"] += 1
                raise CircuitBreakerOpenError(f"断路器 '{self.circuit_breaker_name}' 已打开", self.circuit_breaker_name)

        max_attempts = self.retry_config["max_attempts"]
        last_exception = None

        for attempt in range(max_attempts):
            try:
                start_time = time.perf_counter()

                # 执行HTTP请求
                response = await self.base_client.request(method, url, **kwargs)

                # 检查响应状态码
                if self._is_retryable_error(None, response):
                    # 状态码表示可重试的错误
                    if attempt < max_attempts - 1:
                        # 记录断路器失败
                        if self.enable_circuit_breaker and self.circuit_breaker:
                            self.circuit_breaker.record_failure(f"HTTP_{response.status_code}")

                        # 计算重试延迟
                        delay = self._calculate_retry_delay(attempt)

                        # 记录重试日志
                        self._log_retry_attempt(
                            method, url, attempt, max_attempts, delay, f"HTTP_{response.status_code}"
                        )

                        await asyncio.sleep(delay)
                        continue
                    else:
                        # 最后一次尝试，抛出HTTP错误
                        response.raise_for_status()

                # 请求成功
                response_time = time.perf_counter() - start_time

                # 记录断路器成功
                if self.enable_circuit_breaker and self.circuit_breaker:
                    self.circuit_breaker.record_success(response_time)

                # 记录重试统计
                if attempt > 0:
                    self.retry_stats["successful_retries"] += 1
                    self._log_retry_success(method, url, attempt + 1, response_time)

                return response

            except Exception as e:
                last_exception = e

                # 检查是否可重试
                if not self._is_retryable_error(e):
                    # 不可重试的错误，直接抛出
                    if self.enable_circuit_breaker and self.circuit_breaker:
                        self.circuit_breaker.record_failure(type(e).__name__)
                    raise e

                # 记录断路器失败
                if self.enable_circuit_breaker and self.circuit_breaker:
                    self.circuit_breaker.record_failure(type(e).__name__)

                # 如果不是最后一次尝试，进行重试
                if attempt < max_attempts - 1:
                    # 计算重试延迟
                    delay = self._calculate_retry_delay(attempt)

                    # 记录重试日志
                    self._log_retry_attempt(method, url, attempt, max_attempts, delay, type(e).__name__)

                    await asyncio.sleep(delay)
                    continue

        # 所有重试都失败了
        self.retry_stats["failed_retries"] += 1
        self._log_retry_exhausted(method, url, max_attempts, last_exception)

        raise RetryExhaustedError(
            f"重试 {max_attempts} 次后仍然失败: {str(last_exception)}", last_exception, max_attempts
        )

    def _calculate_retry_delay(self, attempt: int) -> float:
        """计算重试延迟时间"""
        return calculate_backoff_delay(
            attempt=attempt,
            base_delay=self.retry_config["base_delay"],
            max_delay=self.retry_config["max_delay"],
            backoff_factor=self.retry_config["backoff_factor"],
            jitter=self.retry_config["jitter"],
            strategy=BackoffStrategy.EXPONENTIAL,
        )

    def _log_retry_attempt(
        self, method: str, url: str | httpx.URL, attempt: int, max_attempts: int, delay: float, error_type: str
    ):
        """记录重试尝试日志"""
        logger.info(
            f"HTTP请求重试 (第 {attempt + 1}/{max_attempts} 次)",
            extra={
                "method": method,
                "url": str(url),
                "attempt": attempt + 1,
                "max_attempts": max_attempts,
                "retry_delay": delay,
                "error_type": error_type,
                "circuit_breaker": self.circuit_breaker_name if self.enable_circuit_breaker else None,
            },
        )

    def _log_retry_success(self, method: str, url: str | httpx.URL, attempts: int, response_time: float):
        """记录重试成功日志"""
        logger.info(
            f"HTTP请求重试成功 (第 {attempts} 次尝试)",
            extra={
                "method": method,
                "url": str(url),
                "total_attempts": attempts,
                "response_time": response_time,
                "success_after_retries": True,
            },
        )

    def _log_retry_exhausted(self, method: str, url: str | httpx.URL, max_attempts: int, last_exception: Exception):
        """记录重试耗尽日志"""
        logger.warning(
            f"HTTP请求重试耗尽 ({max_attempts} 次)",
            extra={
                "method": method,
                "url": str(url),
                "max_attempts": max_attempts,
                "last_error": str(last_exception),
                "error_type": type(last_exception).__name__,
            },
        )

    # 便捷方法
    async def get(self, url: str | httpx.URL, **kwargs) -> httpx.Response:
        """GET请求"""
        return await self.request("GET", url, **kwargs)

    async def post(self, url: str | httpx.URL, **kwargs) -> httpx.Response:
        """POST请求"""
        return await self.request("POST", url, **kwargs)

    async def put(self, url: str | httpx.URL, **kwargs) -> httpx.Response:
        """PUT请求"""
        return await self.request("PUT", url, **kwargs)

    async def delete(self, url: str | httpx.URL, **kwargs) -> httpx.Response:
        """DELETE请求"""
        return await self.request("DELETE", url, **kwargs)

    async def patch(self, url: str | httpx.URL, **kwargs) -> httpx.Response:
        """PATCH请求"""
        return await self.request("PATCH", url, **kwargs)

    def get_stats(self) -> dict[str, Any]:
        """获取重试统计信息"""
        stats = self.retry_stats.copy()
        stats["retry_rate"] = stats["retry_requests"] / max(stats["total_requests"], 1)
        stats["retry_success_rate"] = stats["successful_retries"] / max(stats["retry_requests"], 1)

        # 添加断路器状态
        if self.enable_circuit_breaker and self.circuit_breaker:
            stats["circuit_breaker_status"] = self.circuit_breaker.get_status()

        return stats

    async def close(self):
        """关闭客户端"""
        await self.base_client.aclose()

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
