<template>
  <el-container class="app-container">
    <el-header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <h1 class="app-title">智能规则管理系统</h1>
        </div>
        <div class="nav-section">
          <el-menu
            :default-active="activeMenu"
            mode="horizontal"
            :ellipsis="false"
            background-color="transparent"
            text-color="#303133"
            active-text-color="#409EFF"
            @select="handleMenuSelect"
          >
            <el-menu-item index="/">
              <el-icon><Grid /></el-icon>
              <span>模板仪表盘</span>
            </el-menu-item>
            <el-sub-menu index="rules">
              <template #title>
                <el-icon><Document /></el-icon>
                <span>规则管理</span>
              </template>
              <el-menu-item index="/rules/management">
                <el-icon><Setting /></el-icon>
                <span>规则配置</span>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="tools" v-if="isAdmin">
              <template #title>
                <el-icon><Tools /></el-icon>
                <span>系统工具</span>
              </template>
              <el-menu-item index="/test">
                <el-icon><Connection /></el-icon>
                <span>连接测试</span>
              </el-menu-item>
              <el-menu-item index="/monitoring/degradation">
                <el-icon><Monitor /></el-icon>
                <span>降级监控</span>
              </el-menu-item>
              <el-menu-item index="/demo/rule-details">
                <el-icon><DataLine /></el-icon>
                <span>演示页面</span>
              </el-menu-item>
            </el-sub-menu>

          </el-menu>
        </div>
      </div>
    </el-header>
    <el-main class="app-main">
      <!-- 面包屑导航 -->
      <BreadcrumbNavigation v-if="showBreadcrumb" />

      <!-- 主要内容区域 -->
      <div class="main-content">
        <router-view></router-view>
      </div>
    </el-main>
  </el-container>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Grid, Connection, Document, Setting, Tools, Monitor, DataLine } from '@element-plus/icons-vue'
import BreadcrumbNavigation from './components/common/BreadcrumbNavigation.vue'
import { useAppStore } from './stores/app'
import { storeToRefs } from 'pinia'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

// 从store获取响应式状态
const { isAdmin } = storeToRefs(appStore)

const activeMenu = computed(() => {
  // 处理子路由的激活状态
  const path = route.path
  if (path.startsWith('/rules/')) {
    return 'rules'
  } else if (path.startsWith('/monitoring/') || path === '/test' || path.startsWith('/demo/')) {
    return 'tools'
  }
  return path
})

// 控制面包屑导航显示
const showBreadcrumb = computed(() => {
  // 在首页和错误页面不显示面包屑
  const hideBreadcrumbRoutes = ['/', '/unauthorized', '/404']
  return !hideBreadcrumbRoutes.includes(route.path) && route.meta.breadcrumb?.length > 1
})

const handleMenuSelect = (index) => {
  if (index !== route.path) {
    router.push(index)
  }
}
</script>

<style>
:root {
  --el-color-primary: #409EFF;
  --app-bg-color: #f0f2f5;
  --header-bg-color: #ffffff;
  --header-text-color: #303133;
  --header-height: 60px;
  --main-content-width: 1400px;
}

body {
  margin: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: var(--app-bg-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-container {
  min-height: 100vh;
}

.app-header {
  background-color: var(--header-bg-color);
  color: var(--header-text-color);
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: var(--header-height);
}

.header-content {
  max-width: var(--main-content-width);
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo-section {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--header-text-color);
}

.nav-section {
  flex-grow: 1;
}

.app-main {
  padding: 0;
  background-color: var(--app-bg-color);
  min-height: calc(100vh - var(--header-height));
  max-width: var(--main-content-width);
  margin: 0 auto;
  box-sizing: border-box;
}

.main-content {
  padding: 24px;
  width: 100% !important;
  max-width: none !important;
  min-width: 100% !important;
  box-sizing: border-box;
}

/* Element Plus Menu style override */
.nav-section .el-menu {
  border-bottom: none;
  height: var(--header-height);
  justify-content: flex-end;
}

.nav-section .el-menu--horizontal > .el-menu-item {
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  height: 100%;
  line-height: var(--header-height);
  padding: 0 20px;
}

.nav-section .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover {
  background-color: transparent !important;
  color: var(--el-color-primary) !important;
}

.nav-section .el-menu--horizontal > .el-menu-item.is-active {
  border-bottom-color: var(--el-color-primary);
  color: var(--el-color-primary) !important;
  background-color: transparent !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }

  .header-content {
    padding: 0 12px;
  }

  .app-title {
    font-size: 18px;
  }

  .nav-section .el-menu--horizontal {
    justify-content: center;
  }

  .nav-section .el-menu--horizontal > .el-menu-item {
    padding: 0 10px;
  }
}
</style>