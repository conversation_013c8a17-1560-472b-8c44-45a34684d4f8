"""
持久化任务状态管理器
负责将异步注册任务的状态、进度跟踪和生命周期管理持久化到数据库
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from typing import Any

from sqlalchemy import and_, desc, func
from sqlalchemy.exc import SQLAlchemyError

from core.db_session import get_session_factory
from core.logging.logging_system import log as logger
from models.registration_task import RegistrationTaskStatus
from services.task_status_manager import TaskStatus


class TransactionStep(Enum):
    """事务步骤枚举"""

    DATABASE_SAVE = "database_save"  # 数据库保存
    EXTERNAL_REGISTER = "external_register"  # 外部注册
    STATUS_UPDATE = "status_update"  # 状态更新


class PersistentTaskStatusManager:
    """
    持久化任务状态管理器

    负责将异步注册任务的状态、进度跟踪和生命周期管理持久化到数据库。
    支持任务恢复、数据一致性检查和补偿事务。
    """

    def __init__(self):
        """初始化持久化任务状态管理器"""
        self.session_factory = get_session_factory()
        self._cleanup_interval = 3600  # 1小时清理一次
        self._task_retention_days = 7  # 7天保留时间
        self._cleanup_task = None

        # 统计信息（内存缓存，定期同步到数据库）
        self._manager_stats = {
            "total_tasks_created": 0,
            "total_tasks_completed": 0,
            "total_tasks_failed": 0,
            "total_tasks_cancelled": 0,
            "active_tasks_count": 0,
        }

        logger.info("PersistentTaskStatusManager初始化完成")

    async def create_task(
        self, task_type: str, rule_key: str, total_operations: int = 0, user_id: str | None = None
    ) -> str:
        """
        创建新任务

        Args:
            task_type: 任务类型
            rule_key: 规则键值
            total_operations: 总操作数
            user_id: 用户ID

        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())

        try:
            with self.session_factory() as session:
                task_record = RegistrationTaskStatus(
                    task_id=task_id,
                    task_type=task_type,
                    rule_key=rule_key,
                    total_operations=total_operations,
                    user_id=user_id,
                    status=TaskStatus.PENDING.value,
                    stats={
                        "delete_operations": 0,
                        "upsert_operations": 0,
                        "successful_operations": 0,
                        "failed_operations": 0,
                        "retry_count": 0,
                    },
                    transaction_steps={
                        TransactionStep.DATABASE_SAVE.value: "pending",
                        TransactionStep.EXTERNAL_REGISTER.value: "pending",
                        TransactionStep.STATUS_UPDATE.value: "pending",
                    },
                )

                session.add(task_record)
                session.commit()

                # 更新统计信息
                self._manager_stats["total_tasks_created"] += 1
                self._manager_stats["active_tasks_count"] += 1

        except SQLAlchemyError as e:
            logger.error(f"创建任务失败: {e}")
            raise Exception(f"创建任务失败: {e}") from None

        logger.info(f"创建任务: {task_id}, 类型: {task_type}, 规则: {rule_key}")
        return task_id

    async def update_task_status(
        self, task_id: str, status: TaskStatus, message: str | None = None, error_message: str | None = None
    ) -> bool:
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            message: 状态消息
            error_message: 错误消息

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.session_factory() as session:
                task_record = (
                    session.query(RegistrationTaskStatus).filter(RegistrationTaskStatus.task_id == task_id).first()
                )

                if not task_record:
                    logger.warning(f"任务 {task_id} 不存在")
                    return False

                old_status = task_record.status
                task_record.status = status.value

                if message:
                    task_record.current_message = message

                if error_message:
                    task_record.error_message = error_message

                # 更新时间戳
                current_time = datetime.now()
                if status == TaskStatus.RUNNING and old_status == TaskStatus.PENDING.value:
                    task_record.started_at = current_time
                elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    task_record.completed_at = current_time
                    if task_record.started_at:
                        task_record.execution_time = (current_time - task_record.started_at).total_seconds()

                    # 更新统计信息
                    self._manager_stats["active_tasks_count"] -= 1
                    if status == TaskStatus.COMPLETED:
                        self._manager_stats["total_tasks_completed"] += 1
                    elif status == TaskStatus.FAILED:
                        self._manager_stats["total_tasks_failed"] += 1
                    elif status == TaskStatus.CANCELLED:
                        self._manager_stats["total_tasks_cancelled"] += 1

                session.commit()

        except SQLAlchemyError as e:
            logger.error(f"更新任务状态失败: {e}")
            return False

        logger.debug(f"任务 {task_id} 状态更新: {old_status} -> {status.value}")
        return True

    async def update_task_progress(
        self, task_id: str, completed_operations: int, message: str | None = None
    ) -> bool:
        """
        更新任务进度

        Args:
            task_id: 任务ID
            completed_operations: 已完成操作数
            message: 进度消息

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.session_factory() as session:
                task_record = (
                    session.query(RegistrationTaskStatus).filter(RegistrationTaskStatus.task_id == task_id).first()
                )

                if not task_record:
                    logger.warning(f"任务 {task_id} 不存在")
                    return False

                task_record.completed_operations = completed_operations

                # 计算进度百分比
                if task_record.total_operations > 0:
                    progress = min(100.0, (completed_operations / task_record.total_operations) * 100.0)
                    task_record.progress_percentage = Decimal(str(progress))
                else:
                    task_record.progress_percentage = Decimal("0.0")

                if message:
                    task_record.current_message = message

                session.commit()

        except SQLAlchemyError as e:
            logger.error(f"更新任务进度失败: {e}")
            return False

        logger.debug(
            f"任务 {task_id} 进度更新: {completed_operations}/{task_record.total_operations}"
            f"({float(task_record.progress_percentage):.1f}%)"
        )
        return True

    async def update_task_stats(self, task_id: str, stats: dict[str, Any]) -> bool:
        """
        更新任务统计信息

        Args:
            task_id: 任务ID
            stats: 统计信息字典

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.session_factory() as session:
                task_record = (
                    session.query(RegistrationTaskStatus).filter(RegistrationTaskStatus.task_id == task_id).first()
                )

                if not task_record:
                    logger.warning(f"任务 {task_id} 不存在")
                    return False

                # 合并统计信息
                current_stats = task_record.stats or {}
                current_stats.update(stats)
                task_record.stats = current_stats

                session.commit()

        except SQLAlchemyError as e:
            logger.error(f"更新任务统计信息失败: {e}")
            return False

        logger.debug(f"任务 {task_id} 统计信息已更新")
        return True

    async def set_task_result(self, task_id: str, result_data: Any) -> bool:
        """
        设置任务结果数据

        Args:
            task_id: 任务ID
            result_data: 结果数据

        Returns:
            bool: 设置是否成功
        """
        try:
            with self.session_factory() as session:
                task_record = (
                    session.query(RegistrationTaskStatus).filter(RegistrationTaskStatus.task_id == task_id).first()
                )

                if not task_record:
                    logger.warning(f"任务 {task_id} 不存在")
                    return False

                task_record.result_data = result_data
                session.commit()

        except SQLAlchemyError as e:
            logger.error(f"设置任务结果数据失败: {e}")
            return False

        logger.debug(f"任务 {task_id} 结果数据已设置")
        return True

    async def get_task_info(self, task_id: str) -> dict[str, Any] | None:
        """
        获取任务信息

        Args:
            task_id: 任务ID

        Returns:
            Optional[Dict]: 任务信息字典，不存在时返回None
        """
        try:
            with self.session_factory() as session:
                task_record = (
                    session.query(RegistrationTaskStatus).filter(RegistrationTaskStatus.task_id == task_id).first()
                )

                if not task_record:
                    return None

                return task_record.to_dict()

        except SQLAlchemyError as e:
            logger.error(f"获取任务信息失败: {e}")
            return None

    async def list_tasks(
        self,
        status_filter: TaskStatus | None = None,
        rule_key_filter: str | None = None,
        user_id_filter: str | None = None,
        limit: int = 100,
    ) -> list[dict[str, Any]]:
        """
        列出任务

        Args:
            status_filter: 状态过滤器
            rule_key_filter: 规则键值过滤器
            user_id_filter: 用户ID过滤器
            limit: 返回数量限制

        Returns:
            List[Dict]: 任务信息列表
        """
        try:
            with self.session_factory() as session:
                query = session.query(RegistrationTaskStatus)

                # 应用过滤器
                if status_filter:
                    query = query.filter(RegistrationTaskStatus.status == status_filter.value)
                if rule_key_filter:
                    query = query.filter(RegistrationTaskStatus.rule_key == rule_key_filter)
                if user_id_filter:
                    query = query.filter(RegistrationTaskStatus.user_id == user_id_filter)

                # 按创建时间倒序排列并限制数量
                query = query.order_by(desc(RegistrationTaskStatus.created_at)).limit(limit)

                tasks = []
                for task_record in query.all():
                    tasks.append(task_record.to_dict())

                return tasks

        except SQLAlchemyError as e:
            logger.error(f"列出任务失败: {e}")
            return []

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 取消是否成功
        """
        return await self.update_task_status(task_id, TaskStatus.CANCELLED, message="任务已被取消")

    async def cleanup_old_tasks(self):
        """清理过期任务"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self._task_retention_days)

            with self.session_factory() as session:
                # 查询需要清理的任务
                old_tasks = (
                    session.query(RegistrationTaskStatus)
                    .filter(
                        and_(
                            RegistrationTaskStatus.created_at < cutoff_date,
                            RegistrationTaskStatus.status.in_(
                                [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value, TaskStatus.CANCELLED.value]
                            ),
                        )
                    )
                    .all()
                )

                cleanup_count = len(old_tasks)

                if cleanup_count > 0:
                    # 删除过期任务
                    for task in old_tasks:
                        session.delete(task)

                    session.commit()
                    logger.info(f"清理了 {cleanup_count} 个过期任务")

        except SQLAlchemyError as e:
            logger.error(f"清理过期任务失败: {e}")

    async def get_manager_stats(self) -> dict[str, Any]:
        """
        获取管理器统计信息

        Returns:
            Dict: 统计信息
        """
        try:
            with self.session_factory() as session:
                # 从数据库获取实时统计信息
                total_tasks = session.query(func.count(RegistrationTaskStatus.task_id)).scalar()

                # 按状态统计
                status_counts = {}
                for status in TaskStatus:
                    count = (
                        session.query(func.count(RegistrationTaskStatus.task_id))
                        .filter(RegistrationTaskStatus.status == status.value)
                        .scalar()
                    )
                    status_counts[status.value] = count

                stats = self._manager_stats.copy()
                stats["total_tasks_in_database"] = total_tasks
                stats["current_tasks_by_status"] = status_counts

                return stats

        except SQLAlchemyError as e:
            logger.error(f"获取管理器统计信息失败: {e}")
            return self._manager_stats.copy()

    async def update_transaction_step(
        self, task_id: str, step: TransactionStep, status: str, details: dict[str, Any] | None = None
    ) -> bool:
        """
        更新事务步骤状态

        Args:
            task_id: 任务ID
            step: 事务步骤
            status: 步骤状态 (pending, completed, failed)
            details: 步骤详细信息

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.session_factory() as session:
                task_record = (
                    session.query(RegistrationTaskStatus).filter(RegistrationTaskStatus.task_id == task_id).first()
                )

                if not task_record:
                    logger.warning(f"任务 {task_id} 不存在")
                    return False

                # 更新事务步骤状态
                transaction_steps = task_record.transaction_steps or {}
                transaction_steps[step.value] = {
                    "status": status,
                    "timestamp": datetime.now().isoformat(),
                    "details": details or {},
                }
                task_record.transaction_steps = transaction_steps

                # 更新最后完成的步骤
                if status == "completed":
                    task_record.last_step_completed = step.value

                # 检查是否需要补偿操作
                if status == "failed":
                    task_record.compensation_needed = True

                session.commit()

        except SQLAlchemyError as e:
            logger.error(f"更新事务步骤失败: {e}")
            return False

        logger.debug(f"任务 {task_id} 事务步骤 {step.value} 状态更新为: {status}")
        return True

    async def get_tasks_needing_recovery(self) -> list[dict[str, Any]]:
        """
        获取需要恢复的任务

        Returns:
            List[Dict]: 需要恢复的任务列表
        """
        try:
            with self.session_factory() as session:
                # 查询状态为RUNNING或PENDING的任务（可能因为服务重启而中断）
                recovery_tasks = (
                    session.query(RegistrationTaskStatus)
                    .filter(RegistrationTaskStatus.status.in_([TaskStatus.PENDING.value, TaskStatus.RUNNING.value]))
                    .all()
                )

                tasks = []
                for task_record in recovery_tasks:
                    task_dict = task_record.to_dict()
                    # 添加恢复建议
                    task_dict["recovery_suggestion"] = self._analyze_recovery_strategy(task_record)
                    tasks.append(task_dict)

                return tasks

        except SQLAlchemyError as e:
            logger.error(f"获取需要恢复的任务失败: {e}")
            return []

    def _analyze_recovery_strategy(self, task_record: RegistrationTaskStatus) -> str:
        """
        分析任务恢复策略

        Args:
            task_record: 任务记录

        Returns:
            str: 恢复策略建议
        """
        transaction_steps = task_record.transaction_steps or {}

        # 检查各个步骤的状态
        db_step = transaction_steps.get(TransactionStep.DATABASE_SAVE.value, {})
        register_step = transaction_steps.get(TransactionStep.EXTERNAL_REGISTER.value, {})

        if db_step.get("status") == "completed" and register_step.get("status") == "failed":
            return "需要补偿操作：回滚数据库更改或重试外部注册"
        elif db_step.get("status") == "completed" and register_step.get("status") == "completed":
            return "可以直接完成：更新任务状态为已完成"
        elif task_record.status == TaskStatus.PENDING.value:
            return "可以重新调度：将任务重新添加到队列"
        else:
            return "需要手动检查：任务状态不明确"

    async def mark_compensation_needed(self, task_id: str, reason: str | None = None) -> bool:
        """
        标记任务需要补偿操作

        Args:
            task_id: 任务ID
            reason: 需要补偿的原因

        Returns:
            bool: 标记是否成功
        """
        try:
            with self.session_factory() as session:
                task_record = session.query(RegistrationTaskStatus).filter_by(task_id=task_id).first()

                if not task_record:
                    logger.warning(f"任务 {task_id} 不存在，无法标记补偿")
                    return False

                task_record.compensation_needed = True

                # 更新事务步骤信息，记录补偿原因
                if task_record.transaction_steps is None:
                    task_record.transaction_steps = {}

                task_record.transaction_steps["compensation_reason"] = reason or "外部注册失败"
                task_record.transaction_steps["compensation_marked_at"] = datetime.now().isoformat()

                session.commit()

                logger.info(f"任务 {task_id} 已标记需要补偿操作，原因: {reason}")
                return True

        except SQLAlchemyError as e:
            logger.error(f"标记任务补偿失败: {e}")
            return False

    async def get_tasks_needing_compensation(self) -> list[dict[str, Any]]:
        """
        获取需要补偿操作的任务

        Returns:
            List[Dict]: 需要补偿操作的任务列表
        """
        try:
            with self.session_factory() as session:
                compensation_tasks = (
                    session.query(RegistrationTaskStatus)
                    .filter(RegistrationTaskStatus.compensation_needed == True)
                    .all()
                )

                tasks = []
                for task_record in compensation_tasks:
                    tasks.append(task_record.to_dict())

                return tasks

        except SQLAlchemyError as e:
            logger.error(f"获取需要补偿操作的任务失败: {e}")
            return []

    async def start_cleanup_task(self):
        """启动清理任务"""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("任务清理循环已启动")

    async def stop_cleanup_task(self):
        """停止清理任务"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
            logger.info("任务清理循环已停止")

    async def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(self._cleanup_interval)
                await self.cleanup_old_tasks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"任务清理过程中发生错误: {e}")


# 全局持久化任务状态管理器实例
_persistent_task_status_manager: PersistentTaskStatusManager | None = None


def get_persistent_task_status_manager() -> PersistentTaskStatusManager:
    """获取全局持久化任务状态管理器实例"""
    global _persistent_task_status_manager
    if _persistent_task_status_manager is None:
        _persistent_task_status_manager = PersistentTaskStatusManager()
    return _persistent_task_status_manager
