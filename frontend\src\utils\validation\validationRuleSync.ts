/**
 * 校验规则同步服务
 * 与后端FrontendValidationGenerator集成，自动同步校验规则
 */

import {
  ValidationRule,
  ElementFormRule,
  RuleTemplateValidationConfig,
  ValidationSyncStatus,
  ValidationRuleType
} from './validationTypes'
import { enhancedErrorHandler } from '../enhancedErrorHandler'

export class ValidationRuleSync {
  private syncStatus: ValidationSyncStatus
  private syncInterval: number | null = null
  private ruleConfigs: Map<string, RuleTemplateValidationConfig> = new Map()
  private eventListeners: Map<string, Function[]> = new Map()

  constructor() {
    this.syncStatus = {
      is_syncing: false,
      sync_errors: [],
      pending_updates: []
    }
  }

  /**
   * 启动自动同步
   */
  startAutoSync(intervalMinutes: number = 60): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
    }

    this.syncInterval = window.setInterval(() => {
      this.syncAllRules().catch(error => {
        console.error('自动同步失败:', error)
      })
    }, intervalMinutes * 60 * 1000)

    console.log(`校验规则自动同步已启动，间隔: ${intervalMinutes}分钟`)
  }

  /**
   * 停止自动同步
   */
  stopAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
      console.log('校验规则自动同步已停止')
    }
  }

  /**
   * 同步所有规则
   */
  async syncAllRules(): Promise<void> {
    if (this.syncStatus.is_syncing) {
      console.warn('同步正在进行中，跳过本次同步')
      return
    }

    this.syncStatus.is_syncing = true
    this.syncStatus.sync_errors = []
    this.syncStatus.pending_updates = []

    try {
      // 获取所有规则键列表
      const ruleKeys = await this.fetchRuleKeysList()

      // 同步每个规则
      for (const ruleKey of ruleKeys) {
        try {
          await this.syncRuleConfig(ruleKey)
          console.log(`规则 ${ruleKey} 同步成功`)
        } catch (error) {
          const errorMsg = `规则 ${ruleKey} 同步失败: ${error instanceof Error ? error.message : String(error)}`
          this.syncStatus.sync_errors.push(errorMsg)
          console.error(errorMsg)
        }
      }

      this.syncStatus.last_sync_time = new Date()
      this.emitEvent('sync_completed', {
        success: this.syncStatus.sync_errors.length === 0,
        errors: this.syncStatus.sync_errors
      })

    } catch (error) {
      const errorMsg = `同步过程失败: ${error instanceof Error ? error.message : String(error)}`
      this.syncStatus.sync_errors.push(errorMsg)
      console.error(errorMsg)

      enhancedErrorHandler.handleError(error, {
        context: 'ValidationRuleSync.syncAllRules'
      })
    } finally {
      this.syncStatus.is_syncing = false
    }
  }

  /**
   * 同步单个规则配置
   */
  async syncRuleConfig(ruleKey: string): Promise<RuleTemplateValidationConfig> {
    try {
      const response = await fetch(`/api/v1/validation/frontend-rules/${ruleKey}`, {
        headers: {
          'X-API-KEY': 'a_very_secret_key_for_development',
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        // 如果接口不存在（404），使用默认配置而不是抛出错误
        if (response.status === 404) {
          console.warn(`规则 ${ruleKey} 的前端校验配置接口不存在，使用默认配置`)
          const defaultConfig = this.createDefaultConfig(ruleKey)
          this.ruleConfigs.set(ruleKey, defaultConfig)
          return defaultConfig
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      if (!data.success) {
        throw new Error(data.message || '获取前端校验规则失败')
      }

      const config = this.parseRuleConfig(data.data)
      this.ruleConfigs.set(ruleKey, config)

      return config

    } catch (error: any) {
      // 对于网络错误或其他错误，也使用默认配置
      if (error?.message?.includes('fetch') || error?.message?.includes('请求的接口不存在')) {
        console.warn(`同步规则 ${ruleKey} 失败，使用默认配置:`, error.message)
        const defaultConfig = this.createDefaultConfig(ruleKey)
        this.ruleConfigs.set(ruleKey, defaultConfig)
        return defaultConfig
      }

      console.error(`同步规则 ${ruleKey} 失败:`, error)
      throw error
    }
  }

  /**
   * 创建默认配置
   */
  private createDefaultConfig(ruleKey: string): RuleTemplateValidationConfig {
    return {
      rule_key: ruleKey,
      fields: {
        // 基础字段的默认配置
        rule_name: {
          field_name: 'rule_name',
          chinese_name: '规则名称',
          rules: [
            {
              field_name: 'rule_name',
              chinese_name: '规则名称',
              rule_type: ValidationRuleType.REQUIRED,
              rule_value: true,
              error_message: '规则名称不能为空',
              is_required: true,
              priority: 0
            }
          ],
          element_rules: [
            { required: true, message: '规则名称不能为空', trigger: 'blur' }
          ],
          is_required: true,
          data_type: 'string',
          default_value: undefined
        },
        level1: {
          field_name: 'level1',
          chinese_name: '一级错误类型',
          rules: [
            {
              field_name: 'level1',
              chinese_name: '一级错误类型',
              rule_type: ValidationRuleType.REQUIRED,
              rule_value: true,
              error_message: '一级错误类型不能为空',
              is_required: true,
              priority: 0
            }
          ],
          element_rules: [
            { required: true, message: '一级错误类型不能为空', trigger: 'blur' }
          ],
          is_required: true,
          data_type: 'string',
          default_value: undefined
        },
        level2: {
          field_name: 'level2',
          chinese_name: '二级错误类型',
          rules: [],
          element_rules: [],
          is_required: false,
          data_type: 'string',
          default_value: undefined
        }
      },
      rule_name: `规则模板 - ${ruleKey}`,
      generated_at: new Date().toISOString(),
      version: '1.0.0'
    }
  }

  /**
   * 获取规则键列表
   */
  private async fetchRuleKeysList(): Promise<string[]> {
    try {
      const response = await fetch('/api/v1/validation/rule-keys', {
        headers: {
          'X-API-KEY': 'a_very_secret_key_for_development',
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      if (!data.success) {
        throw new Error(data.message || '获取规则键列表失败')
      }

      return data.data.rule_keys || []

    } catch (error) {
      console.error('获取规则键列表失败:', error)
      throw error
    }
  }

  /**
   * 解析规则配置
   */
  private parseRuleConfig(backendData: any): RuleTemplateValidationConfig {
    const config: RuleTemplateValidationConfig = {
      rule_key: backendData.rule_key,
      rule_name: backendData.rule_name || backendData.rule_key,
      fields: {},
      generated_at: backendData.generated_at || new Date().toISOString(),
      version: backendData.version || '1.0.0'
    }

    // 解析字段配置
    if (backendData.fields) {
      for (const [fieldName, fieldData] of Object.entries(backendData.fields)) {
        config.fields[fieldName] = {
          field_name: fieldName,
          chinese_name: (fieldData as any).chinese_name || fieldName,
          rules: this.parseValidationRules(fieldData as any),
          element_rules: this.convertToElementRules((fieldData as any).rules || []),
          is_required: (fieldData as any).is_required || false,
          data_type: (fieldData as any).data_type || 'string',
          default_value: (fieldData as any).default_value
        }
      }
    }

    return config
  }

  /**
   * 解析校验规则
   */
  private parseValidationRules(fieldData: any): ValidationRule[] {
    const rules: ValidationRule[] = []

    if (fieldData.rules) {
      for (const ruleData of fieldData.rules) {
        const rule: ValidationRule = {
          field_name: fieldData.field_name || '',
          chinese_name: fieldData.chinese_name || '',
          rule_type: this.mapRuleType(ruleData.type),
          rule_value: ruleData.value,
          error_message: ruleData.message || `${fieldData.chinese_name}校验失败`,
          is_required: ruleData.type === 'required',
          priority: ruleData.priority || 0
        }
        rules.push(rule)
      }
    }

    return rules
  }

  /**
   * 转换为Element Plus规则
   */
  private convertToElementRules(backendRules: any[]): ElementFormRule[] {
    const elementRules: ElementFormRule[] = []

    for (const rule of backendRules) {
      const elementRule: ElementFormRule = {
        message: rule.message || '校验失败',
        trigger: rule.trigger || 'blur'
      }

      switch (rule.type) {
        case 'required':
          elementRule.required = true
          break

        case 'min_length':
          elementRule.min = rule.value
          elementRule.type = 'string'
          break

        case 'max_length':
          elementRule.max = rule.value
          elementRule.type = 'string'
          break

        case 'pattern':
          elementRule.pattern = new RegExp(rule.value)
          break

        case 'email':
          elementRule.type = 'email'
          break

        case 'integer':
          elementRule.type = 'integer'
          break

        case 'float':
          elementRule.type = 'float'
          break

        case 'array':
          elementRule.type = 'array'
          break

        case 'min_value':
        case 'max_value':
          // 自定义校验器
          elementRule.validator = this.createCustomValidator(rule)
          break
      }

      elementRules.push(elementRule)
    }

    return elementRules
  }

  /**
   * 创建自定义校验器
   */
  private createCustomValidator(rule: any): (rule: any, value: any, callback: any) => void {
    return (_, value, callback) => {
      if (value === null || value === undefined || value === '') {
        callback()
        return
      }

      const numValue = Number(value)
      if (isNaN(numValue)) {
        callback(new Error('请输入有效的数字'))
        return
      }

      if (rule.type === 'min_value' && numValue < rule.value) {
        callback(new Error(rule.message || `值不能小于${rule.value}`))
        return
      }

      if (rule.type === 'max_value' && numValue > rule.value) {
        callback(new Error(rule.message || `值不能大于${rule.value}`))
        return
      }

      callback()
    }
  }

  /**
   * 映射规则类型
   */
  private mapRuleType(backendType: string): ValidationRuleType {
    const typeMap: Record<string, ValidationRuleType> = {
      'required': ValidationRuleType.REQUIRED,
      'min_length': ValidationRuleType.MIN_LENGTH,
      'max_length': ValidationRuleType.MAX_LENGTH,
      'min_value': ValidationRuleType.MIN_VALUE,
      'max_value': ValidationRuleType.MAX_VALUE,
      'pattern': ValidationRuleType.PATTERN,
      'email': ValidationRuleType.EMAIL,
      'date_format': ValidationRuleType.DATE_FORMAT,
      'integer': ValidationRuleType.INTEGER,
      'float': ValidationRuleType.FLOAT,
      'array': ValidationRuleType.ARRAY
    }

    return typeMap[backendType] || ValidationRuleType.CUSTOM
  }

  /**
   * 获取规则配置
   */
  getRuleConfig(ruleKey: string): RuleTemplateValidationConfig | null {
    return this.ruleConfigs.get(ruleKey) || null
  }

  /**
   * 获取所有规则配置
   */
  getAllRuleConfigs(): Map<string, RuleTemplateValidationConfig> {
    return new Map(this.ruleConfigs)
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): ValidationSyncStatus {
    return { ...this.syncStatus }
  }

  /**
   * 发送事件
   */
  private emitEvent(eventType: string, data: any): void {
    const listeners = this.eventListeners.get(eventType) || []
    listeners.forEach(listener => {
      try {
        listener({ type: eventType, ...data, timestamp: new Date() })
      } catch (error) {
        console.error('事件监听器执行失败:', error)
      }
    })
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventType: string, listener: Function): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, [])
    }
    this.eventListeners.get(eventType)!.push(listener)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventType: string, listener: Function): void {
    const listeners = this.eventListeners.get(eventType) || []
    const index = listeners.indexOf(listener)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopAutoSync()
    this.ruleConfigs.clear()
    this.eventListeners.clear()
  }
}

// 创建全局实例
export const validationRuleSync = new ValidationRuleSync()
