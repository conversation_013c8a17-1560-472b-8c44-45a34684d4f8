"""
缓存预热管理器
在服务启动时自动预热常用规则筛选结果和患者数据模式，确保首次请求能够命中缓存
"""

import asyncio
import time
from dataclasses import dataclass
from typing import Any

from config.settings import settings
from core.logging.logging_system import log as logger
from core.memory_optimizer import memory_optimizer
from models.patient import PatientData
from services.ultra_fast_rule_service import ultra_fast_rule_service


@dataclass
class WarmupStats:
    """预热统计信息"""

    total_patterns: int = 0
    successful_patterns: int = 0
    failed_patterns: int = 0
    total_time_seconds: float = 0.0
    memory_used_mb: float = 0.0
    cache_entries_created: int = 0

    @property
    def success_rate(self) -> float:
        return (self.successful_patterns / self.total_patterns * 100) if self.total_patterns > 0 else 0.0


@dataclass
class WarmupPattern:
    """预热模式定义"""

    name: str
    patient_data: PatientData
    rule_ids: list[str]
    priority: int = 1  # 1=高优先级, 2=中优先级, 3=低优先级
    description: str = ""


class WarmupPatternGenerator:
    """预热模式生成器 - 生成典型患者数据模式"""

    def __init__(self):
        self.patterns: list[WarmupPattern] = []
        self._generate_common_patterns()

    def _generate_common_patterns(self):
        """生成常见患者模式"""
        # 基础模式：不同性别和年龄组合
        base_patterns = [
            {"gender": "男", "age": 35, "name": "中年男性"},
            {"gender": "女", "age": 28, "name": "青年女性"},
            {"gender": "男", "age": 65, "name": "老年男性"},
            {"gender": "女", "age": 72, "name": "老年女性"},
            {"gender": "男", "age": 8, "name": "儿童男性"},
            {"gender": "女", "age": 6, "name": "儿童女性"},
        ]

        # 常见诊断模式
        common_diagnoses = [
            ["I10", "E11.9"],  # 高血压 + 糖尿病
            ["J44.1"],  # 慢性阻塞性肺病
            ["N18.6"],  # 慢性肾病
            ["I25.9"],  # 冠心病
            ["K29.5"],  # 胃炎
        ]

        # 生成组合模式
        for i, base in enumerate(base_patterns):
            for j, diagnoses in enumerate(common_diagnoses):
                pattern_name = f"{base['name']}_{j + 1}"
                patient_data = self._create_patient_data(gender=base["gender"], age=base["age"], diagnoses=diagnoses)

                # 优先级：常见组合为高优先级
                priority = 1 if i < 2 and j < 2 else 2 if i < 4 else 3

                self.patterns.append(
                    WarmupPattern(
                        name=pattern_name,
                        patient_data=patient_data,
                        rule_ids=[],  # 将在预热时获取所有规则
                        priority=priority,
                        description=f"{base['name']}患者，诊断：{', '.join(diagnoses)}",
                    )
                )

    def _create_patient_data(self, gender: str, age: int, diagnoses: list[str]) -> PatientData:
        """创建患者数据"""
        from models.patient import DiagnoseInfo, DiagnosisList, FeeItem, PatientBasicInfo, PatientData

        # 基本信息
        basic_info = PatientBasicInfo(
            gender="1" if gender == "男" else "2",  # 使用代码格式
            genderDescribe=gender,
            age=age,
        )

        # 诊断信息
        diagnosis_items = [
            DiagnosisList(
                diagnosisICDCode=code,
                diagnosisName=f"诊断_{code}",
                diagnosisType="1" if i == 0 else "2",  # 1=主诊断, 2=次诊断
                isPrincipalDiagnosis="1" if i == 0 else "0",
                displayOrder=str(i + 1),
            )
            for i, code in enumerate(diagnoses)
        ]

        diagnosis_info = DiagnoseInfo(diagnosis=diagnosis_items)

        # 费用信息（生成一些典型费用）
        fees = [
            FeeItem(
                ybdm=f"YBDM_{i:03d}",
                je=100.0 + i * 50,
                sl=1.0,
                xh=f"XH_{i:03d}",
                fymc=f"费用项目_{i}",
                create_date=int(time.time() * 1000),  # 毫秒时间戳
            )
            for i in range(5)  # 生成5个费用项目
        ]

        bah = f"TEST_{gender}_{age}_{int(time.time())}"

        return PatientData(bah=bah, basic_information=basic_info, Diagnosis=diagnosis_info, fees=fees)

    def get_patterns_by_priority(self, priority: int) -> list[WarmupPattern]:
        """按优先级获取模式"""
        return [p for p in self.patterns if p.priority == priority]

    def get_all_patterns(self) -> list[WarmupPattern]:
        """获取所有模式"""
        return self.patterns.copy()


class WarmupManager:
    """缓存预热管理器"""

    def __init__(self):
        self.pattern_generator = WarmupPatternGenerator()
        self.is_warming_up = False
        self.warmup_stats = WarmupStats()
        self.warmup_task: asyncio.Task | None = None

        # 配置参数
        self.enabled = getattr(settings, "ENABLE_STARTUP_WARMUP", True)
        self.timeout_seconds = getattr(settings, "WARMUP_TIMEOUT_SECONDS", 300)  # 5分钟
        self.memory_limit_mb = getattr(settings, "WARMUP_MEMORY_LIMIT_MB", 200)  # 200MB用于预热
        self.batch_size = getattr(settings, "WARMUP_BATCH_SIZE", 5)
        self.max_concurrent = getattr(settings, "WARMUP_MAX_CONCURRENT", 3)

        logger.info(f"WarmupManager initialized: enabled={self.enabled}, timeout={self.timeout_seconds}s")

    async def start_warmup(self) -> bool:
        """启动预热过程"""
        if not self.enabled:
            logger.info("预热功能已禁用，跳过预热")
            return True

        if self.is_warming_up:
            logger.warning("预热过程已在进行中")
            return False

        logger.info("开始缓存预热...")
        self.is_warming_up = True
        self.warmup_stats = WarmupStats()

        try:
            # 创建预热任务
            self.warmup_task = asyncio.create_task(self._warmup_process())

            # 等待预热完成或超时
            await asyncio.wait_for(self.warmup_task, timeout=self.timeout_seconds)

            logger.info(
                f"预热完成：成功 {self.warmup_stats.successful_patterns}/{self.warmup_stats.total_patterns} 个模式，"
                f"成功率 {self.warmup_stats.success_rate:.1f}%，"
                f"耗时 {self.warmup_stats.total_time_seconds:.1f}s，"
                f"内存使用 {self.warmup_stats.memory_used_mb:.1f}MB"
            )

            return self.warmup_stats.success_rate >= 80.0  # 成功率>=80%认为预热成功

        except asyncio.TimeoutError:  # noqa: UP041
            logger.warning(f"预热超时（{self.timeout_seconds}s），部分模式可能未完成预热")
            return self.warmup_stats.success_rate >= 50.0  # 超时情况下成功率>=50%也可接受

        except Exception as e:
            logger.error(f"预热过程异常: {e}", exc_info=True)
            return False

        finally:
            self.is_warming_up = False

    async def _warmup_process(self):
        """预热处理过程"""
        start_time = time.perf_counter()

        # 获取所有规则ID
        from core.rule_cache import RULE_CACHE

        all_rule_ids = list(RULE_CACHE.keys())

        if not all_rule_ids:
            logger.warning("规则缓存为空，无法进行预热")
            return

        # 按优先级分批预热
        for priority in [1, 2, 3]:
            patterns = self.pattern_generator.get_patterns_by_priority(priority)
            if not patterns:
                continue

            logger.info(f"开始预热优先级 {priority} 的模式（{len(patterns)} 个）")

            # 检查内存压力
            memory_stats = memory_optimizer.get_memory_stats()
            if memory_stats.process_memory_mb > (1024 - self.memory_limit_mb):
                logger.warning(f"内存使用过高 ({memory_stats.process_memory_mb:.1f}MB)，停止预热")
                break

            # 分批处理
            for i in range(0, len(patterns), self.batch_size):
                batch = patterns[i : i + self.batch_size]
                await self._warmup_batch(batch, all_rule_ids)

                # 检查是否需要停止
                if not self.is_warming_up:
                    break

        self.warmup_stats.total_time_seconds = time.perf_counter() - start_time

    async def _warmup_batch(self, patterns: list[WarmupPattern], all_rule_ids: list[str]):
        """预热一批模式"""
        semaphore = asyncio.Semaphore(self.max_concurrent)

        async def warmup_single_pattern(pattern: WarmupPattern):
            async with semaphore:
                await self._warmup_single_pattern(pattern, all_rule_ids)

        # 并发预热
        tasks = [warmup_single_pattern(pattern) for pattern in patterns]
        await asyncio.gather(*tasks, return_exceptions=True)

    async def _warmup_single_pattern(self, pattern: WarmupPattern, all_rule_ids: list[str]):
        """预热单个模式"""
        try:
            self.warmup_stats.total_patterns += 1

            logger.debug(f"预热模式: {pattern.name}")

            # 执行超快速校验（这会触发所有缓存的构建）
            violations, ultra_stats = await ultra_fast_rule_service.validate_rules_ultra_fast(
                pattern.patient_data, all_rule_ids
            )

            self.warmup_stats.successful_patterns += 1
            self.warmup_stats.cache_entries_created += 1
            self.warmup_stats.memory_used_mb += ultra_stats.memory_usage_mb

            logger.debug(
                f"模式 {pattern.name} 预热成功："
                f"筛选 {ultra_stats.filtered_rules_count}/{ultra_stats.total_rules_candidate} 规则，"
                f"耗时 {ultra_stats.total_time_ms:.1f}ms"
            )

        except Exception as e:
            self.warmup_stats.failed_patterns += 1
            logger.warning(f"模式 {pattern.name} 预热失败: {e}")

    def get_warmup_status(self) -> dict[str, Any]:
        """获取预热状态"""
        return {
            "enabled": self.enabled,
            "is_warming_up": self.is_warming_up,
            "stats": {
                "total_patterns": self.warmup_stats.total_patterns,
                "successful_patterns": self.warmup_stats.successful_patterns,
                "failed_patterns": self.warmup_stats.failed_patterns,
                "success_rate": self.warmup_stats.success_rate,
                "total_time_seconds": self.warmup_stats.total_time_seconds,
                "memory_used_mb": self.warmup_stats.memory_used_mb,
                "cache_entries_created": self.warmup_stats.cache_entries_created,
            },
            "config": {
                "timeout_seconds": self.timeout_seconds,
                "memory_limit_mb": self.memory_limit_mb,
                "batch_size": self.batch_size,
                "max_concurrent": self.max_concurrent,
            },
        }

    async def manual_warmup(self) -> dict[str, Any]:
        """手动触发预热"""
        if self.is_warming_up:
            return {"success": False, "message": "预热过程已在进行中"}

        logger.info("手动触发缓存预热")
        success = await self.start_warmup()

        return {
            "success": success,
            "message": "预热完成" if success else "预热失败",
            "stats": self.get_warmup_status()["stats"],
        }


# 全局预热管理器实例
warmup_manager = WarmupManager()
