# rule_data_sets 表架构重构 - 数据迁移操作手册

**版本**: 1.0  
**创建时间**: 2025-07-12  
**适用范围**: rule_data_sets 表架构重构项目  
**操作人员**: 数据库管理员、系统运维人员  

## 📋 概述

本手册详细说明了从现有 JSON 存储方式迁移到新的关系型表结构的完整操作流程，包括迁移前准备、执行步骤、验证方法和应急处理。

## ⚠️ 重要提醒

- **数据安全**: 迁移前必须创建完整的数据备份
- **停机时间**: 建议在业务低峰期执行迁移操作
- **回滚准备**: 确保回滚方案已经测试验证
- **权限要求**: 需要数据库管理员权限

## 🔧 环境要求

### 系统要求
- Python 3.8+
- MySQL 5.7+ 或 8.0+
- 足够的磁盘空间（建议预留当前数据量的 3 倍空间）

### 依赖检查
```bash
# 检查 Python 环境
python --version

# 检查必要的包
pip list | grep -E "(sqlalchemy|alembic|pytest)"

# 检查数据库连接
mysql -h <host> -u <username> -p -e "SELECT VERSION();"
```

## 📊 迁移前评估

### 1. 数据量评估
```sql
-- 检查待迁移的数据集数量
SELECT COUNT(*) as total_datasets FROM rule_data_sets;

-- 检查 JSON 数据大小
SELECT 
    COUNT(*) as datasets_with_data,
    AVG(JSON_LENGTH(data_set)) as avg_json_length,
    MAX(JSON_LENGTH(data_set)) as max_json_length
FROM rule_data_sets 
WHERE data_set IS NOT NULL;

-- 估算迁移后的记录数
SELECT 
    SUM(JSON_LENGTH(data_set)) as estimated_details
FROM rule_data_sets 
WHERE data_set IS NOT NULL AND JSON_VALID(data_set);
```

### 2. 系统资源检查
```bash
# 检查磁盘空间
df -h

# 检查内存使用
free -h

# 检查数据库状态
mysql -e "SHOW PROCESSLIST;"
mysql -e "SHOW ENGINE INNODB STATUS\G" | grep -A 20 "TRANSACTIONS"
```

## 🚀 迁移执行流程

### 阶段 1: 准备阶段

#### 1.1 数据库结构更新
```bash
# 执行数据库迁移脚本
cd /path/to/project
alembic upgrade head

# 验证新表结构
mysql -e "DESCRIBE rule_details;"
mysql -e "SHOW INDEX FROM rule_details;"
```

#### 1.2 创建数据备份
```python
from services.migration_rollback_service import MigrationRollbackService
from core.database import get_session_factory

# 创建备份服务
session_factory = get_session_factory()
rollback_service = MigrationRollbackService(session_factory)

# 创建迁移前备份
backup_result = rollback_service.create_backup_before_migration()
print(f"备份ID: {backup_result['backup_id']}")
print(f"备份文件数: {backup_result['backed_up_datasets']}")
```

#### 1.3 迁移前验证
```python
from services.migration_data_validator import MigrationDataValidator

# 创建验证器
validator = MigrationDataValidator(session_factory)

# 执行迁移前验证
pre_validation = validator.validate_pre_migration()
print(f"验证结果: {'通过' if pre_validation['overall_valid'] else '失败'}")
print(f"有效数据集: {pre_validation['valid_datasets']}")
print(f"无效数据集: {pre_validation['invalid_datasets']}")

# 如果验证失败，检查错误详情
if not pre_validation['overall_valid']:
    for error in pre_validation['validation_errors']:
        print(f"错误: {error}")
```

### 阶段 2: 迁移执行

#### 2.1 试运行模式
```python
from services.rule_data_migration_enhanced import EnhancedRuleDataMigration

# 创建迁移服务
migration_service = EnhancedRuleDataMigration(
    session_factory, 
    batch_size=100,  # 根据系统性能调整
    enable_validation=True
)

# 执行试运行
dry_run_result = migration_service.migrate_all_datasets(dry_run=True)
print(f"试运行结果:")
print(f"- 总数据集: {dry_run_result['total_datasets']}")
print(f"- 预期成功: {dry_run_result['successful_datasets']}")
print(f"- 预期明细数: {dry_run_result['total_details']}")
```

#### 2.2 正式迁移
```python
# 设置进度监控
def progress_callback(progress, dataset_id, detail_count):
    print(f"进度: {progress:.1f}% - 数据集 {dataset_id} 完成 ({detail_count} 条明细)")

migration_service = EnhancedRuleDataMigration(
    session_factory,
    batch_size=100,
    enable_validation=True,
    progress_callback=progress_callback
)

# 执行正式迁移
print("开始正式迁移...")
migration_result = migration_service.migrate_all_datasets(
    dry_run=False,
    continue_on_error=True  # 遇到错误继续处理其他数据集
)

print(f"迁移完成:")
print(f"- 成功数据集: {migration_result['successful_datasets']}")
print(f"- 失败数据集: {migration_result['failed_datasets']}")
print(f"- 迁移明细数: {migration_result['total_details']}")
print(f"- 耗时: {migration_result['duration_seconds']:.2f} 秒")

# 检查错误
if migration_result['errors']:
    print("迁移错误:")
    for error in migration_result['errors']:
        print(f"- {error}")
```

### 阶段 3: 迁移后验证

#### 3.1 数据完整性验证
```python
# 执行迁移后验证
post_validation = validator.validate_post_migration()
print(f"迁移后验证: {'通过' if post_validation['overall_valid'] else '失败'}")

if not post_validation['overall_valid']:
    print("发现问题:")
    for issue in post_validation['integrity_issues']:
        print(f"- {issue}")
    
    print("缺失记录:")
    for missing in post_validation['missing_records']:
        print(f"- {missing}")
```

#### 3.2 数据一致性检查
```python
# 检查关键数据集的一致性
critical_dataset_ids = [1, 2, 3]  # 替换为实际的关键数据集ID

for dataset_id in critical_dataset_ids:
    consistency_result = validator.validate_data_consistency(dataset_id)
    print(f"数据集 {dataset_id} 一致性: {'通过' if consistency_result['consistent'] else '失败'}")
    
    if not consistency_result['consistent']:
        for mismatch in consistency_result['field_mismatches']:
            print(f"  字段不匹配: {mismatch}")
```

#### 3.3 性能验证
```sql
-- 验证查询性能
EXPLAIN SELECT * FROM rule_details WHERE dataset_id = 1;
EXPLAIN SELECT * FROM rule_details WHERE rule_detail_id = 'rule_001';
EXPLAIN SELECT * FROM rule_details WHERE error_level_1 = '一级错误';

-- 检查索引使用情况
SHOW INDEX FROM rule_details;
```

## 🔄 应急处理

### 迁移失败处理

#### 1. 部分迁移失败
```python
# 查看失败的数据集
failed_datasets = []
for dataset_id in range(1, 100):  # 根据实际范围调整
    try:
        consistency_result = validator.validate_data_consistency(dataset_id)
        if not consistency_result['consistent']:
            failed_datasets.append(dataset_id)
    except:
        failed_datasets.append(dataset_id)

print(f"失败的数据集: {failed_datasets}")

# 重新迁移失败的数据集
for dataset_id in failed_datasets:
    try:
        result = migration_service.migrate_single_dataset_by_id(dataset_id, dry_run=False)
        print(f"数据集 {dataset_id} 重新迁移: {'成功' if result['successful_datasets'] > 0 else '失败'}")
    except Exception as e:
        print(f"数据集 {dataset_id} 重新迁移失败: {e}")
```

#### 2. 完整回滚
```python
# 使用备份进行回滚
rollback_result = rollback_service.rollback_migration(
    backup_id=backup_result['backup_id']  # 使用之前创建的备份ID
)

print(f"回滚结果:")
print(f"- 回滚数据集: {rollback_result['rollback_datasets']}")
print(f"- 删除明细数: {rollback_result['deleted_details']}")
print(f"- 恢复数据集: {rollback_result['restored_datasets']}")

# 验证回滚结果
verification_result = rollback_service.verify_rollback(rollback_result['rollback_id'])
print(f"回滚验证: {'通过' if verification_result['verification_passed'] else '失败'}")
```

#### 3. 紧急回滚
```python
# 在极端情况下使用紧急回滚
emergency_result = rollback_service.emergency_rollback()
print(f"紧急回滚完成:")
print(f"- 删除明细数: {emergency_result['total_details_deleted']}")
print(f"- 重置数据集: {emergency_result['total_datasets_reset']}")
```

## 📈 性能优化建议

### 1. 批量大小调优
```python
# 根据系统性能测试不同的批量大小
batch_sizes = [50, 100, 200, 500]
for batch_size in batch_sizes:
    service = EnhancedRuleDataMigration(session_factory, batch_size=batch_size)
    # 使用小数据集测试性能
    result = service.migrate_single_dataset_by_id(test_dataset_id, dry_run=True)
    print(f"批量大小 {batch_size}: {result['duration_seconds']:.2f} 秒")
```

### 2. 数据库优化
```sql
-- 临时禁用外键检查（谨慎使用）
SET FOREIGN_KEY_CHECKS = 0;

-- 调整 InnoDB 缓冲池大小
SET GLOBAL innodb_buffer_pool_size = 2147483648;  -- 2GB

-- 优化批量插入
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL sync_binlog = 0;

-- 迁移完成后恢复设置
SET FOREIGN_KEY_CHECKS = 1;
SET GLOBAL innodb_flush_log_at_trx_commit = 1;
SET GLOBAL sync_binlog = 1;
```

## ✅ 迁移完成检查清单

### 数据验证
- [ ] 所有数据集迁移状态为 COMPLETED
- [ ] 迁移前后记录数量一致
- [ ] 关键字段数据一致性验证通过
- [ ] 查询性能测试通过

### 系统验证
- [ ] 应用程序正常启动
- [ ] 关键业务功能测试通过
- [ ] API 接口响应正常
- [ ] 前端页面显示正确

### 清理工作
- [ ] 清理临时文件和日志
- [ ] 更新系统文档
- [ ] 通知相关团队迁移完成
- [ ] 安排后续监控

## 📞 联系信息

**技术支持**: 开发团队  
**紧急联系**: 系统管理员  
**文档维护**: AI Assistant  

---

**最后更新**: 2025-07-12  
**版本**: 1.0
