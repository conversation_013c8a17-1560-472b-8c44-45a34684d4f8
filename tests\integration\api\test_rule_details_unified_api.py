#!/usr/bin/env python3
"""
规则明细统一API完整集成测试
测试统一后的API接口的完整CRUD功能和业务逻辑
"""


import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from models.database import RuleTemplate, RuleTemplateStatusEnum


class TestRuleDetailsUnifiedAPI:
    """规则明细统一API完整集成测试类"""

    @pytest.fixture
    def test_template(self, db_session: Session):
        """创建测试规则模板"""
        template = RuleTemplate(
            rule_key="test_unified_api",
            rule_type="unified_test",
            name="统一API测试规则模板",
            description="用于统一API测试的规则模板",
            status=RuleTemplateStatusEnum.READY,
        )
        db_session.add(template)
        db_session.commit()
        db_session.refresh(template)
        return template

    @pytest.fixture
    def sample_rule_detail_data(self):
        """示例规则明细数据"""
        return {
            "rule_id": "TEST_RULE_001",
            "rule_name": "统一API测试规则明细",
            "level1": "药品适应症",
            "level2": "限制使用",
            "level3": "年龄限制",
            "error_reason": "该药品仅限成人使用",
            "degree": "严重",
            "reference": "药品说明书第3条",
            "detail_position": "处方明细",
            "prompted_fields1": "drug_code",
            "prompted_fields3": "药品编码",
            "type": "药品规则",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "remarks": "测试备注信息",
            "in_illustration": "输入药品编码和患者年龄",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": ["XB05BA01", "XB05BA02"],
            "diag_whole_code": ["I10.x00"],
            "diag_code_prefix": ["I10"],
            "diag_name_keyword": "高血压",
            "fee_whole_code": ["310101001"],
            "fee_code_prefix": ["3101"],
            "age_threshold": 18,
            "limit_days": 30,
        }

    def test_create_rule_detail_success(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试成功创建规则明细"""
        response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        # 验证响应格式
        assert response_data.get("success") is True
        assert response_data.get("code") == 200
        assert response_data.get("message") == "规则明细创建成功"
        assert "data" in response_data
        assert "timestamp" in response_data

        # 验证返回的数据
        created_data = response_data["data"]
        assert created_data["rule_name"] == sample_rule_detail_data["rule_name"]
        assert created_data["level1"] == sample_rule_detail_data["level1"]
        assert created_data["level2"] == sample_rule_detail_data["level2"]
        assert created_data["level3"] == sample_rule_detail_data["level3"]
        assert created_data["rule_key"] == test_template.rule_key
        assert "id" in created_data
        assert "rule_id" in created_data
        assert "created_at" in created_data
        assert "updated_at" in created_data

    def test_create_rule_detail_validation_failure(self, client: TestClient, test_template):
        """测试创建规则明细时的数据验证失败"""
        incomplete_data = {
            "rule_name": "不完整的规则明细",
            # 缺少必填字段
        }

        response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=incomplete_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        # 应该返回验证失败
        assert response_data.get("success") is False
        assert response_data.get("code") == 422  # 数据验证失败

    def test_get_rule_details_list_empty(self, client: TestClient, test_template):
        """测试查询空的规则明细列表"""
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={"page": 1, "page_size": 20},
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is True
        assert response_data.get("code") == 200

        data = response_data["data"]
        assert data["total"] == 0
        assert data["items"] == []
        assert data["page"] == 1
        assert data["page_size"] == 20
        assert data["total_pages"] == 0

    def test_get_rule_details_list_with_data(
        self, client: TestClient, test_template, sample_rule_detail_data, db_session: Session
    ):
        """测试查询包含数据的规则明细列表"""
        # 先创建一些测试数据
        for i in range(3):
            detail_data = sample_rule_detail_data.copy()
            detail_data["rule_name"] = f"测试规则明细{i+1}"

            response = client.post(
                f"/api/v1/rules/details/{test_template.rule_key}",
                json=detail_data,
                headers={"X-API-KEY": "a_very_secret_key_for_development"},
            )
            assert response.status_code == 200

        # 查询列表
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={"page": 1, "page_size": 10},
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is True
        data = response_data["data"]
        assert data["total"] == 3
        assert len(data["items"]) == 3
        assert data["page"] == 1
        assert data["page_size"] == 10

        # 验证返回的数据结构
        for item in data["items"]:
            assert "id" in item
            assert "rule_id" in item
            assert "rule_key" in item
            assert "rule_name" in item
            assert "level1" in item
            assert "level2" in item
            assert "level3" in item
            assert "created_at" in item
            assert "updated_at" in item

    def test_get_rule_details_list_with_filters(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试带过滤条件的查询"""
        # 先创建测试数据
        response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )
        assert response.status_code == 200

        # 测试过滤查询
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={
                "page": 1,
                "page_size": 10,
                "level1": "药品适应症",
                "level2": "限制使用",
                "type": "药品规则",
                "status": "ACTIVE",
            },
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

    def test_get_rule_details_list_with_search(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试搜索功能"""
        # 先创建测试数据
        response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )
        assert response.status_code == 200

        # 测试搜索
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={"page": 1, "page_size": 10, "search": "统一API测试"},
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data.get("success") is True

    def test_get_rule_details_list_pagination(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试分页功能"""
        # 先创建多条测试数据
        for i in range(15):
            detail_data = sample_rule_detail_data.copy()
            detail_data["rule_name"] = f"分页测试规则明细{i+1}"

            response = client.post(
                f"/api/v1/rules/details/{test_template.rule_key}",
                json=detail_data,
                headers={"X-API-KEY": "a_very_secret_key_for_development"},
            )
            assert response.status_code == 200

        # 测试第一页
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={"page": 1, "page_size": 10},
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()
        data = response_data["data"]
        assert data["total"] == 15
        assert len(data["items"]) == 10
        assert data["page"] == 1
        assert data["total_pages"] == 2

        # 测试第二页
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={"page": 2, "page_size": 10},
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()
        data = response_data["data"]
        assert data["total"] == 15
        assert len(data["items"]) == 5
        assert data["page"] == 2
        assert data["total_pages"] == 2

    def test_get_single_rule_detail_success(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试成功查询单条规则明细"""
        # 先创建一条规则明细
        create_response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )
        assert create_response.status_code == 200
        response_json = create_response.json()
        created_data = response_json["data"]
        detail_id = created_data["id"]

        # 查询单条规则明细
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}/{detail_id}",
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is True
        assert response_data.get("code") == 200

        data = response_data["data"]
        assert data["id"] == detail_id
        assert data["rule_name"] == sample_rule_detail_data["rule_name"]
        assert data["level1"] == sample_rule_detail_data["level1"]

    def test_get_single_rule_detail_not_found(self, client: TestClient, test_template):
        """测试查询不存在的规则明细"""
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}/99999",
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is False
        assert response_data.get("code") == 605  # 规则明细不存在

    def test_update_rule_detail_success(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试成功更新规则明细"""
        # 先创建一条规则明细
        create_response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )
        assert create_response.status_code == 200
        created_data = create_response.json()["data"]
        detail_id = created_data["id"]

        # 更新数据
        update_data = {
            "rule_name": "更新后的规则名称",
            "level1": "更新后的一级错误类型",
            "error_reason": "更新后的错误原因",
            "remarks": "更新后的备注信息",
        }

        response = client.put(
            f"/api/v1/rules/details/{test_template.rule_key}/{detail_id}",
            json=update_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is True
        assert response_data.get("code") == 200
        assert response_data.get("message") == "规则明细更新成功"

        updated_data = response_data["data"]
        assert updated_data["rule_name"] == update_data["rule_name"]
        assert updated_data["level1"] == update_data["level1"]
        assert updated_data["error_reason"] == update_data["error_reason"]
        assert updated_data["remarks"] == update_data["remarks"]

    def test_update_rule_detail_not_found(self, client: TestClient, test_template):
        """测试更新不存在的规则明细"""
        update_data = {"rule_name": "更新不存在的规则"}

        response = client.put(
            f"/api/v1/rules/details/{test_template.rule_key}/99999",
            json=update_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is False
        assert response_data.get("code") == 605  # 规则明细不存在

    def test_delete_rule_detail_success(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试成功删除规则明细"""
        # 先创建一条规则明细
        create_response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )
        assert create_response.status_code == 200
        created_data = create_response.json()["data"]
        detail_id = created_data["id"]

        # 删除规则明细
        response = client.delete(
            f"/api/v1/rules/details/{test_template.rule_key}/{detail_id}",
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is True
        assert response_data.get("code") == 200
        assert response_data.get("message") == "规则明细删除成功"
        assert response_data.get("data") is None

        # 验证已删除
        get_response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}/{detail_id}",
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )
        assert get_response.status_code == 200
        get_data = get_response.json()
        assert get_data.get("success") is False
        assert get_data.get("code") == 605  # 规则明细不存在

    def test_delete_rule_detail_not_found(self, client: TestClient, test_template):
        """测试删除不存在的规则明细"""
        response = client.delete(
            f"/api/v1/rules/details/{test_template.rule_key}/99999",
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is False
        assert response_data.get("code") == 605  # 规则明细不存在

    def test_batch_operations_success(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试成功的批量操作"""
        # 先创建一条规则明细用于更新和删除
        create_response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )
        assert create_response.status_code == 200
        created_data = create_response.json()["data"]
        rule_id = created_data["rule_id"]

        # 批量操作数据
        batch_data = {
            "operations": [
                {
                    "operation": "CREATE",
                    "data": {
                        "rule_name": "批量创建规则1",
                        "level1": "药品适应症",
                        "level2": "限制使用",
                        "level3": "年龄限制",
                        "error_reason": "批量创建的错误原因",
                        "degree": "严重",
                        "reference": "批量创建的质控依据",
                        "detail_position": "处方明细",
                        "prompted_fields1": "drug_code",
                        "prompted_fields3": "药品编码",
                        "type": "药品规则",
                        "pos": "门诊",
                        "applicableArea": "全国",
                        "default_use": "是",
                        "start_date": "2024-01-01",
                        "end_date": "2024-12-31",
                    },
                },
                {
                    "operation": "UPDATE",
                    "rule_id": rule_id,
                    "data": {"rule_name": "批量更新后的规则名称", "error_reason": "批量更新后的错误原因"},
                },
                {
                    "operation": "CREATE",
                    "data": {
                        "rule_name": "批量创建规则2",
                        "level1": "诊疗项目",
                        "level2": "重复使用",
                        "level3": "时间限制",
                        "error_reason": "批量创建的错误原因2",
                        "degree": "一般",
                        "reference": "批量创建的质控依据2",
                        "detail_position": "诊疗明细",
                        "prompted_fields1": "item_code",
                        "prompted_fields3": "项目编码",
                        "type": "诊疗规则",
                        "pos": "住院",
                        "applicableArea": "全国",
                        "default_use": "是",
                        "start_date": "2024-01-01",
                        "end_date": "2024-12-31",
                    },
                },
            ]
        }

        response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}/batch",
            json=batch_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is True
        assert response_data.get("code") == 200
        assert response_data.get("message") == "批量操作完成"

        data = response_data["data"]
        assert data["total_operations"] == 3
        assert data["successful_operations"] >= 2  # 至少成功2个操作
        assert "results" in data
        assert len(data["results"]) == 3

    def test_batch_operations_partial_failure(self, client: TestClient, test_template):
        """测试部分失败的批量操作"""
        batch_data = {
            "operations": [
                {
                    "operation": "CREATE",
                    "data": {
                        "rule_name": "有效的批量创建规则",
                        "level1": "药品适应症",
                        "level2": "限制使用",
                        "level3": "年龄限制",
                        "error_reason": "有效的错误原因",
                        "degree": "严重",
                        "reference": "有效的质控依据",
                        "detail_position": "处方明细",
                        "prompted_fields1": "drug_code",
                        "prompted_fields3": "药品编码",
                        "type": "药品规则",
                        "pos": "门诊",
                        "applicableArea": "全国",
                        "default_use": "是",
                        "start_date": "2024-01-01",
                        "end_date": "2024-12-31",
                    },
                },
                {"operation": "UPDATE", "rule_id": "nonexistent_rule_id", "data": {"rule_name": "更新不存在的规则"}},
                {"operation": "DELETE", "rule_id": "another_nonexistent_rule_id"},
            ]
        }

        response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}/batch",
            json=batch_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        # 批量操作可能部分成功
        data = response_data["data"]
        assert data["total_operations"] == 3
        assert data["successful_operations"] >= 1  # 至少有一个成功
        assert data["failed_operations"] >= 1  # 至少有一个失败

    def test_incremental_upload_success(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试成功的增量上传"""
        incremental_data = {
            "data": [
                {
                    "rule_name": "增量上传规则1",
                    "level1": "药品适应症",
                    "level2": "限制使用",
                    "level3": "年龄限制",
                    "error_reason": "增量上传的错误原因1",
                    "degree": "严重",
                    "reference": "增量上传的质控依据1",
                    "detail_position": "处方明细",
                    "prompted_fields1": "drug_code",
                    "prompted_fields3": "药品编码",
                    "type": "药品规则",
                    "pos": "门诊",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31",
                },
                {
                    "rule_name": "增量上传规则2",
                    "level1": "诊疗项目",
                    "level2": "重复使用",
                    "level3": "时间限制",
                    "error_reason": "增量上传的错误原因2",
                    "degree": "一般",
                    "reference": "增量上传的质控依据2",
                    "detail_position": "诊疗明细",
                    "prompted_fields1": "item_code",
                    "prompted_fields3": "项目编码",
                    "type": "诊疗规则",
                    "pos": "住院",
                    "applicableArea": "全国",
                    "default_use": "是",
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31",
                },
            ],
            "options": {"upsert": True, "delete_missing": False},
        }

        response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}/incremental",
            json=incremental_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is True
        assert response_data.get("code") == 200

        data = response_data["data"]
        assert "processed_count" in data
        assert "created_count" in data
        assert "updated_count" in data
        assert data["processed_count"] == 2

    def test_api_authentication_failure(self, client: TestClient, test_template):
        """测试API认证失败"""
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={"page": 1, "page_size": 10},
            # 缺少认证头
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is False
        assert response_data.get("code") == 403  # 认证失败

    def test_api_authentication_invalid_key(self, client: TestClient, test_template):
        """测试无效的API密钥"""
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={"page": 1, "page_size": 10},
            headers={"X-API-KEY": "invalid_api_key"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is False
        assert response_data.get("code") == 401  # 认证失败

    def test_nonexistent_rule_key(self, client: TestClient):
        """测试不存在的规则键"""
        response = client.get(
            "/api/v1/rules/details/nonexistent_rule_key",
            params={"page": 1, "page_size": 10},
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        assert response_data.get("success") is False
        assert response_data.get("code") == 602  # 规则不存在

    def test_api_response_format_consistency(self, client: TestClient, test_template):
        """测试API响应格式的一致性"""
        response = client.get(
            f"/api/v1/rules/details/{test_template.rule_key}",
            params={"page": 1, "page_size": 10},
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )

        assert response.status_code == 200
        response_data = response.json()

        # 验证统一响应格式
        required_fields = ["success", "code", "message", "data", "timestamp"]
        for field in required_fields:
            assert field in response_data, f"响应中缺少必需字段: {field}"

        # 验证数据类型
        assert isinstance(response_data["success"], bool)
        assert isinstance(response_data["code"], int)
        assert isinstance(response_data["message"], str)
        assert isinstance(response_data["timestamp"], int | float)

    def test_standard_field_names_consistency(self, client: TestClient, test_template, sample_rule_detail_data):
        """测试标准字段名的一致性"""
        # 创建规则明细
        create_response = client.post(
            f"/api/v1/rules/details/{test_template.rule_key}",
            json=sample_rule_detail_data,
            headers={"X-API-KEY": "a_very_secret_key_for_development"},
        )
        assert create_response.status_code == 200
        response_json = create_response.json()
        created_data = response_json["data"]

        # 验证返回的数据使用标准字段名
        standard_fields = [
            "rule_name",
            "level1",
            "level2",
            "level3",
            "error_reason",
            "degree",
            "reference",
            "detail_position",
            "prompted_fields1",
            "prompted_fields3",
            "type",
            "pos",
            "applicableArea",
            "default_use",
            "remarks",
            "in_illustration",
            "start_date",
            "end_date",
            "yb_code",
            "diag_whole_code",
            "diag_code_prefix",
            "diag_name_keyword",
            "fee_whole_code",
            "fee_code_prefix",
            "extended_fields",
        ]

        for field in standard_fields:
            if field in sample_rule_detail_data:
                assert field in created_data, f"响应中缺少标准字段: {field}"
                assert created_data[field] == sample_rule_detail_data[field], f"字段 {field} 的值不匹配"
