# HTTP重试配置使用指南

## 1. 配置概述

本指南介绍如何配置和使用HTTP重试机制。重试配置系统支持环境特定配置、运行时调整和详细的验证机制。

## 2. 配置项说明

### 2.1 基础重试配置

```python
# HTTP重试功能总开关
HTTP_RETRY_ENABLED: bool = True

# 基础重试参数
HTTP_RETRY_MAX_ATTEMPTS: int = 3          # 最大重试次数
HTTP_RETRY_BASE_DELAY: float = 1.0        # 基础延迟时间(秒)
HTTP_RETRY_MAX_DELAY: float = 60.0        # 最大延迟时间(秒)
HTTP_RETRY_BACKOFF_FACTOR: float = 2.0    # 退避因子
HTTP_RETRY_JITTER: bool = True            # 是否启用抖动

# 可重试状态码配置
HTTP_RETRY_STATUS_CODES: str = "408,429,500,502,503,504"

# 可重试异常类型配置
HTTP_RETRY_ON_CONNECTION_ERROR: bool = True
HTTP_RETRY_ON_TIMEOUT_ERROR: bool = True
HTTP_RETRY_ON_DNS_ERROR: bool = True
```

### 2.2 断路器配置

```python
# 断路器功能开关
CIRCUIT_BREAKER_ENABLED: bool = True

# 断路器基础参数
CIRCUIT_BREAKER_FAILURE_THRESHOLD: int = 5           # 失败阈值(次数)
CIRCUIT_BREAKER_FAILURE_RATE_THRESHOLD: float = 0.5  # 失败率阈值(0.0-1.0)
CIRCUIT_BREAKER_RECOVERY_TIMEOUT: float = 60.0       # 恢复超时(秒)
CIRCUIT_BREAKER_WINDOW_SIZE: int = 100               # 滑动窗口大小
CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS: int = 3         # 半开状态最大调用数
```

### 2.3 环境特定配置

#### 开发环境（更激进的重试策略）
```python
HTTP_RETRY_DEV_ENABLED: bool = True
HTTP_RETRY_DEV_MAX_ATTEMPTS: int = 5
HTTP_RETRY_DEV_BASE_DELAY: float = 0.5
HTTP_RETRY_DEV_MAX_DELAY: float = 30.0
HTTP_RETRY_DEV_BACKOFF_FACTOR: float = 1.5
```

#### 测试环境（快速失败）
```python
HTTP_RETRY_TEST_ENABLED: bool = True
HTTP_RETRY_TEST_MAX_ATTEMPTS: int = 2
HTTP_RETRY_TEST_BASE_DELAY: float = 0.1
HTTP_RETRY_TEST_MAX_DELAY: float = 5.0
HTTP_RETRY_TEST_BACKOFF_FACTOR: float = 2.0
```

#### 生产环境（保守策略）
```python
HTTP_RETRY_PROD_ENABLED: bool = True
HTTP_RETRY_PROD_MAX_ATTEMPTS: int = 3
HTTP_RETRY_PROD_BASE_DELAY: float = 2.0
HTTP_RETRY_PROD_MAX_DELAY: float = 120.0
HTTP_RETRY_PROD_BACKOFF_FACTOR: float = 2.0
```

### 2.4 监控配置

```python
# 重试指标收集开关
HTTP_RETRY_METRICS_ENABLED: bool = True

# 重试指标保留时间(秒)
HTTP_RETRY_METRICS_RETENTION: int = 3600  # 1小时

# 重试统计窗口大小
HTTP_RETRY_STATS_WINDOW_SIZE: int = 1000
```

## 3. 使用方法

### 3.1 基础使用

```python
from config.settings import get_settings
from config.retry_config import get_retry_config_manager

# 获取配置
settings = get_settings()
config_manager = get_retry_config_manager(settings)

# 获取当前环境的重试配置
retry_config = config_manager.get_retry_config()
circuit_config = config_manager.get_circuit_breaker_config()

print(f"重试启用: {retry_config.enabled}")
print(f"最大重试次数: {retry_config.max_attempts}")
print(f"断路器启用: {circuit_config.enabled}")
```

### 3.2 配置验证

```python
from config.config_validator import validate_retry_config

# 验证配置
result = validate_retry_config()

if result["valid"]:
    print("配置验证通过")
else:
    print("配置验证失败:")
    for error in result["errors"]:
        print(f"  - {error}")
```

### 3.3 运行时配置更新

```python
# 重新加载配置
config_manager.reload_config()

# 获取配置摘要
summary = config_manager.get_config_summary()
print(f"当前环境: {summary['environment']}")
```

## 4. 环境变量配置

可以通过环境变量覆盖默认配置：

```bash
# 基础配置
export HTTP_RETRY_ENABLED=true
export HTTP_RETRY_MAX_ATTEMPTS=5
export HTTP_RETRY_BASE_DELAY=2.0

# 断路器配置
export CIRCUIT_BREAKER_ENABLED=true
export CIRCUIT_BREAKER_FAILURE_THRESHOLD=10

# 环境模式
export RUN_MODE=PROD
```

## 5. 配置最佳实践

### 5.1 开发环境建议

- 启用更多重试次数便于调试
- 使用较短的延迟时间提高开发效率
- 启用详细的监控和日志

### 5.2 测试环境建议

- 使用快速失败策略减少测试时间
- 启用重试但限制次数
- 确保测试的确定性

### 5.3 生产环境建议

- 使用保守的重试策略
- 设置合理的超时时间
- 启用完整的监控和告警

## 6. 故障排查

### 6.1 配置验证失败

```bash
# 运行配置验证工具
python config/config_validator.py
```

### 6.2 常见配置错误

1. **重试次数过多**
   - 问题：导致长时间等待
   - 解决：减少MAX_ATTEMPTS或增加MAX_DELAY限制

2. **延迟时间不合理**
   - 问题：BASE_DELAY > MAX_DELAY
   - 解决：确保MAX_DELAY >= BASE_DELAY

3. **退避因子过大**
   - 问题：延迟增长过快
   - 解决：使用1.5-3.0之间的退避因子

### 6.3 性能调优

1. **减少重试开销**
   - 合理设置重试次数
   - 使用智能错误分类
   - 启用断路器保护

2. **优化延迟策略**
   - 根据服务特性调整基础延迟
   - 使用抖动避免雷群效应
   - 设置合理的最大延迟

## 7. 配置示例

### 7.1 高可用服务配置

```python
# 对于关键服务，使用更多重试
HTTP_RETRY_MAX_ATTEMPTS = 5
HTTP_RETRY_BASE_DELAY = 1.0
HTTP_RETRY_MAX_DELAY = 30.0
HTTP_RETRY_BACKOFF_FACTOR = 1.5

# 更敏感的断路器
CIRCUIT_BREAKER_FAILURE_THRESHOLD = 3
CIRCUIT_BREAKER_RECOVERY_TIMEOUT = 30.0
```

### 7.2 快速响应服务配置

```python
# 对于实时服务，使用快速失败
HTTP_RETRY_MAX_ATTEMPTS = 2
HTTP_RETRY_BASE_DELAY = 0.5
HTTP_RETRY_MAX_DELAY = 5.0
HTTP_RETRY_BACKOFF_FACTOR = 2.0

# 快速断路器
CIRCUIT_BREAKER_FAILURE_THRESHOLD = 2
CIRCUIT_BREAKER_RECOVERY_TIMEOUT = 10.0
```

### 7.3 批处理服务配置

```python
# 对于批处理，可以容忍更长延迟
HTTP_RETRY_MAX_ATTEMPTS = 10
HTTP_RETRY_BASE_DELAY = 5.0
HTTP_RETRY_MAX_DELAY = 300.0
HTTP_RETRY_BACKOFF_FACTOR = 2.0

# 更宽松的断路器
CIRCUIT_BREAKER_FAILURE_THRESHOLD = 10
CIRCUIT_BREAKER_RECOVERY_TIMEOUT = 120.0
```

## 8. 监控和告警

### 8.1 关键指标

- 重试率：retry_requests / total_requests
- 重试成功率：successful_retries / retry_requests
- 平均重试延迟：average_retry_delay
- 断路器触发次数：circuit_breaker_trips

### 8.2 告警阈值建议

- 重试率 > 10%：可能存在服务问题
- 重试成功率 < 50%：重试策略可能需要调整
- 断路器频繁触发：下游服务可能不稳定

---

**文档版本：** 1.0  
**更新时间：** 2025-07-01  
**适用版本：** CQ-008及以后版本
