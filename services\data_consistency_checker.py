"""
数据一致性检查机制
负责比较本地数据和外部数据的一致性，提供检查报告和修复建议
"""

from datetime import datetime
from enum import Enum
from typing import Any

from sqlalchemy.exc import SQLAlchemyError

from core.db_session import get_session_factory
from core.logging.logging_system import log as logger
from models.database import RuleDetail, RuleTemplate
from services.data_mapping_engine import DataMappingEngine
from services.rule_registration_service import RuleRegistrationService


class InconsistencyType(Enum):
    """数据不一致类型"""

    LOCAL_ONLY = "local_only"  # 仅本地存在
    REMOTE_ONLY = "remote_only"  # 仅远程存在
    DATA_MISMATCH = "data_mismatch"  # 数据不匹配
    VERSION_MISMATCH = "version_mismatch"  # 版本不匹配


class ConsistencyReport:
    """一致性检查报告"""

    def __init__(self, rule_key: str):
        self.rule_key = rule_key
        self.check_time = datetime.now()
        self.is_consistent = True
        self.inconsistencies = []
        self.local_count = 0
        self.remote_count = 0
        self.matched_count = 0
        self.summary = {}
        self.repair_suggestions = []

    def add_inconsistency(self, inconsistency_type: InconsistencyType, rule_id: str, details: dict[str, Any]):
        """添加不一致项"""
        self.is_consistent = False
        self.inconsistencies.append({
            "type": inconsistency_type.value,
            "rule_id": rule_id,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "rule_key": self.rule_key,
            "check_time": self.check_time.isoformat(),
            "is_consistent": self.is_consistent,
            "local_count": self.local_count,
            "remote_count": self.remote_count,
            "matched_count": self.matched_count,
            "inconsistency_count": len(self.inconsistencies),
            "inconsistencies": self.inconsistencies,
            "summary": self.summary,
            "repair_suggestions": self.repair_suggestions
        }


class DataConsistencyChecker:
    """
    数据一致性检查器

    负责比较本地数据库和外部注册服务的数据一致性，
    生成详细的检查报告和修复建议。
    """

    def __init__(self):
        """初始化数据一致性检查器"""
        self.session_factory = get_session_factory()
        self.registration_service = None
        self.mapping_engine = None

        # 统计信息
        self.stats = {
            "total_checks": 0,
            "consistent_rules": 0,
            "inconsistent_rules": 0,
            "total_inconsistencies": 0,
            "last_check_time": None,
        }

        logger.info("DataConsistencyChecker初始化完成")

    async def initialize_dependencies(self):
        """初始化依赖服务"""
        try:
            self.registration_service = RuleRegistrationService()
            self.mapping_engine = DataMappingEngine()

            logger.info("数据一致性检查器依赖初始化完成")
        except Exception as e:
            logger.error(f"数据一致性检查器依赖初始化失败: {e}")
            raise

    async def check_rule_consistency(self, rule_key: str) -> ConsistencyReport:
        """
        检查单个规则的数据一致性

        Args:
            rule_key: 规则键

        Returns:
            ConsistencyReport: 一致性检查报告
        """
        try:
            if not self.registration_service:
                await self.initialize_dependencies()

            self.stats["total_checks"] += 1
            self.stats["last_check_time"] = datetime.now().isoformat()

            logger.info(f"开始检查规则 {rule_key} 的数据一致性")

            report = ConsistencyReport(rule_key)

            # 获取本地数据
            local_data = await self._get_local_rule_data(rule_key)
            report.local_count = len(local_data)

            # 获取远程数据
            remote_data = await self._get_remote_rule_data(rule_key)
            report.remote_count = len(remote_data)

            # 比较数据一致性
            await self._compare_data_consistency(local_data, remote_data, report)

            # 生成修复建议
            self._generate_repair_suggestions(report)

            # 更新统计信息
            if report.is_consistent:
                self.stats["consistent_rules"] += 1
            else:
                self.stats["inconsistent_rules"] += 1
                self.stats["total_inconsistencies"] += len(report.inconsistencies)

            logger.info(f"规则 {rule_key} 一致性检查完成，结果: {'一致' if report.is_consistent else '不一致'}")
            return report

        except Exception as e:
            logger.error(f"检查规则 {rule_key} 一致性失败: {e}", exc_info=True)
            report = ConsistencyReport(rule_key)
            report.is_consistent = False
            report.summary = {"error": str(e)}
            return report

    async def check_all_rules_consistency(self) -> list[ConsistencyReport]:
        """
        检查所有规则的数据一致性

        Returns:
            List[ConsistencyReport]: 所有规则的一致性检查报告
        """
        try:
            logger.info("开始检查所有规则的数据一致性")

            # 获取所有活跃的规则键
            rule_keys = await self._get_all_active_rule_keys()

            reports = []
            for rule_key in rule_keys:
                report = await self.check_rule_consistency(rule_key)
                reports.append(report)

            logger.info(f"所有规则一致性检查完成，共检查 {len(reports)} 个规则")
            return reports

        except Exception as e:
            logger.error(f"检查所有规则一致性失败: {e}", exc_info=True)
            return []

    async def _get_local_rule_data(self, rule_key: str) -> dict[str, dict[str, Any]]:
        """
        获取本地规则数据

        Args:
            rule_key: 规则键

        Returns:
            Dict: 本地规则数据，以rule_id为键
        """
        try:
            local_data = {}

            with self.session_factory() as session:
                # 获取RuleTemplate信息
                rule_template = session.query(RuleTemplate).filter_by(rule_key=rule_key).first()
                if not rule_template:
                    logger.warning(f"未找到规则 {rule_key} 的模板信息")
                    return local_data

                # 获取RuleDetail数据
                rule_details = session.query(RuleDetail).filter_by(rule_key=rule_key).all()

                for detail in rule_details:
                    # 将数据映射为注册格式，使用新的数据结构
                    detail_data = detail.to_dict()
                    mapped_data = self.mapping_engine.map_to_registration_format(
                        detail_data, rule_template, "UPSERT"
                    )

                    for item in mapped_data:
                        rule_id = item.get("id")
                        if rule_id:
                            local_data[rule_id] = {
                                "id": rule_id,
                                "name": item.get("name"),
                                "outputs": item.get("outputs"),
                                "script": item.get("script"),
                                "createTime": item.get("createTime"),
                                "detail_id": detail.id,
                                "version": getattr(detail, 'version', 1)
                            }

            logger.debug(f"获取到规则 {rule_key} 的本地数据 {len(local_data)} 条")
            return local_data

        except Exception as e:
            logger.error(f"获取规则 {rule_key} 本地数据失败: {e}")
            return {}

    async def _get_remote_rule_data(self, rule_key: str) -> dict[str, dict[str, Any]]:
        """
        获取远程规则数据

        Args:
            rule_key: 规则键

        Returns:
            Dict: 远程规则数据，以rule_id为键
        """
        try:
            # 注意：这里需要根据实际的外部注册服务API来实现
            # 目前假设注册服务提供了查询接口

            # TODO: 实现实际的远程数据获取逻辑
            # 这里先返回空数据，实际实现时需要调用外部服务的查询API

            logger.warning(f"远程数据获取功能尚未实现，规则 {rule_key} 返回空数据")
            return {}

        except Exception as e:
            logger.error(f"获取规则 {rule_key} 远程数据失败: {e}")
            return {}

    async def _compare_data_consistency(
        self, 
        local_data: dict[str, dict[str, Any]], 
        remote_data: dict[str, dict[str, Any]], 
        report: ConsistencyReport
    ):
        """
        比较本地和远程数据的一致性

        Args:
            local_data: 本地数据
            remote_data: 远程数据
            report: 一致性报告
        """
        try:
            local_ids = set(local_data.keys())
            remote_ids = set(remote_data.keys())

            # 仅本地存在的数据
            local_only_ids = local_ids - remote_ids
            for rule_id in local_only_ids:
                report.add_inconsistency(
                    InconsistencyType.LOCAL_ONLY,
                    rule_id,
                    {
                        "local_data": local_data[rule_id],
                        "description": "数据仅在本地存在，远程缺失"
                    }
                )

            # 仅远程存在的数据
            remote_only_ids = remote_ids - local_ids
            for rule_id in remote_only_ids:
                report.add_inconsistency(
                    InconsistencyType.REMOTE_ONLY,
                    rule_id,
                    {
                        "remote_data": remote_data[rule_id],
                        "description": "数据仅在远程存在，本地缺失"
                    }
                )

            # 两边都存在的数据，检查内容是否一致
            common_ids = local_ids & remote_ids
            for rule_id in common_ids:
                local_item = local_data[rule_id]
                remote_item = remote_data[rule_id]

                # 比较关键字段
                mismatches = []
                if local_item.get("name") != remote_item.get("name"):
                    mismatches.append("name")

                if local_item.get("outputs") != remote_item.get("outputs"):
                    mismatches.append("outputs")

                if local_item.get("script") != remote_item.get("script"):
                    mismatches.append("script")

                if mismatches:
                    report.add_inconsistency(
                        InconsistencyType.DATA_MISMATCH,
                        rule_id,
                        {
                            "local_data": local_item,
                            "remote_data": remote_item,
                            "mismatched_fields": mismatches,
                            "description": f"数据内容不匹配，字段: {', '.join(mismatches)}"
                        }
                    )
                else:
                    report.matched_count += 1

            # 生成摘要
            report.summary = {
                "local_only_count": len(local_only_ids),
                "remote_only_count": len(remote_only_ids),
                "matched_count": report.matched_count,
                "data_mismatch_count": len([i for i in report.inconsistencies if i["type"] == InconsistencyType.DATA_MISMATCH.value])
            }

        except Exception as e:
            logger.error(f"比较数据一致性失败: {e}")
            report.summary = {"error": str(e)}

    def _generate_repair_suggestions(self, report: ConsistencyReport):
        """
        生成修复建议

        Args:
            report: 一致性报告
        """
        try:
            suggestions = []

            if report.is_consistent:
                suggestions.append({
                    "type": "no_action",
                    "description": "数据一致，无需修复",
                    "priority": "low"
                })
            else:
                # 根据不一致类型生成建议
                local_only_count = report.summary.get("local_only_count", 0)
                remote_only_count = report.summary.get("remote_only_count", 0)
                data_mismatch_count = report.summary.get("data_mismatch_count", 0)

                if local_only_count > 0:
                    suggestions.append({
                        "type": "sync_to_remote",
                        "description": f"将 {local_only_count} 条本地数据同步到远程",
                        "priority": "high",
                        "action": "重新执行外部注册"
                    })

                if remote_only_count > 0:
                    suggestions.append({
                        "type": "sync_to_local",
                        "description": f"将 {remote_only_count} 条远程数据同步到本地",
                        "priority": "medium",
                        "action": "从远程拉取数据并保存到本地"
                    })

                if data_mismatch_count > 0:
                    suggestions.append({
                        "type": "resolve_conflicts",
                        "description": f"解决 {data_mismatch_count} 条数据冲突",
                        "priority": "high",
                        "action": "手动检查并决定以本地或远程数据为准"
                    })

            report.repair_suggestions = suggestions

        except Exception as e:
            logger.error(f"生成修复建议失败: {e}")
            report.repair_suggestions = [{"type": "error", "description": f"生成建议失败: {e}"}]

    async def _get_all_active_rule_keys(self) -> list[str]:
        """
        获取所有活跃的规则键

        Returns:
            List[str]: 规则键列表
        """
        try:
            rule_keys = []

            with self.session_factory() as session:
                rule_templates = session.query(RuleTemplate).all()
                rule_keys = [template.rule_key for template in rule_templates]

            logger.debug(f"获取到 {len(rule_keys)} 个活跃规则键")
            return rule_keys

        except SQLAlchemyError as e:
            logger.error(f"获取活跃规则键失败: {e}")
            return []

    def get_stats(self) -> dict[str, Any]:
        """
        获取检查器统计信息

        Returns:
            Dict: 统计信息
        """
        return self.stats.copy()

    async def health_check(self) -> dict[str, Any]:
        """
        健康检查

        Returns:
            Dict: 健康状态信息
        """
        try:
            # 检查依赖服务
            if not self.registration_service:
                await self.initialize_dependencies()

            # 检查注册服务健康状态
            registration_health = await self.registration_service.health_check()

            return {
                "healthy": True,
                "service": "DataConsistencyChecker",
                "dependencies": {
                    "registration_service": registration_health.get("healthy", False),
                    "mapping_engine": self.mapping_engine is not None,
                },
                "stats": self.get_stats(),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"数据一致性检查器健康检查失败: {e}")
            return {
                "healthy": False,
                "service": "DataConsistencyChecker",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
