# 前端环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# 应用基础路径（用于不同部署环境）
VITE_BASE_URL=/

# 后端API地址
VITE_API_URL=http://localhost:18001

# 应用标题
VITE_APP_TITLE=智能规则管理系统

# API密钥（开发环境）
VITE_API_KEY=a_very_secret_key_for_development

# 是否启用调试模式
VITE_DEBUG=true

# 上传文件大小限制（MB）
VITE_MAX_FILE_SIZE=10

# 分页默认大小
VITE_DEFAULT_PAGE_SIZE=15

# 请求超时时间（毫秒）
VITE_REQUEST_TIMEOUT=30000

# Docker部署配置
BUILD_VERSION=1.0.0
FRONTEND_PORT=80
FRONTEND_HOST=localhost
FRONTEND_DEV_PORT=3000
BACKEND_PORT=18001
BACKEND_HOST=api.localhost
BACKEND_DEV_PORT=18001
NGINX_HOST=localhost

# 数据库配置
DATABASE_URL=sqlite:///./rules.db

# 后端API配置
MASTER_API_SECRET_KEY=your_secret_key_here
CORS_ORIGINS=http://localhost
LOG_LEVEL=INFO
