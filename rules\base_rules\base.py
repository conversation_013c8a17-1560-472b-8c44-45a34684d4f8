from abc import ABC, abstractmethod
from datetime import datetime
from typing import TYPE_CHECKING, Any

from models import PatientData, RuleResult

# 避免循环导入
if TYPE_CHECKING:
    from services.patient_data_preprocessor import UltraOptimizedPatientData


class BaseRule(ABC):
    """
    规则基类

    所有规则必须继承此基类并实现validate方法
    """

    # 定义数组字段列表
    ARRAY_FIELDS = [
        'yb_code',
        'diag_whole_code',
        'diag_code_prefix',
        'fee_whole_code',
        'fee_code_prefix'
    ]

    def __init__(self, rule_id: str, **kwargs):
        """
        初始化规则基类

        Args:
            rule_id: 规则ID
        """
        self.rule_id = rule_id
        # 使用 kwargs 捕获所有其他参数，以便子类可以定义自己的构造函数
        # 同时允许在不修改基类的情况下，从数据集中传递任意参数。
        for key, value in kwargs.items():
            if key in self.ARRAY_FIELDS:
                # 字符串转换为列表
                if isinstance(value, str):
                    setattr(self, key, self._convert_string_to_set(value))
                elif isinstance(value, list):
                    setattr(self, key, set(value))
            else:
                setattr(self, key, value)

    @classmethod
    def _convert_string_to_set(cls, value: str) -> set[str]:
        """将逗号分隔字符串转换为集合"""
        if not value:
            return set()
        return set(item.strip() for item in value.split(",") if item.strip())

    @abstractmethod
    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        验证患者数据是否符合规则。

        Args:
            patient_data: 包含患者信息的字典。

        Returns:
            如果触发规则，则返回一个 ValidationResult 实例，否则返回 None。
        """
        raise NotImplementedError

    def validate_ultra_fast(self, ultra_patient_data: "UltraOptimizedPatientData") -> RuleResult | None:
        """
        使用超优化患者数据进行快速校验

        默认实现：创建适配器将超优化数据转换为标准格式，然后调用标准校验方法
        子类可以重写此方法以实现更高效的校验逻辑

        Args:
            ultra_patient_data: 超优化患者数据

        Returns:
            如果触发规则，则返回一个 RuleResult 实例，否则返回 None
        """
        # 默认适配器实现：将超优化数据转换为标准PatientData格式
        try:
            patient_data = self._create_patient_data_adapter(ultra_patient_data)
            return self.validate(patient_data)
        except Exception as e:
            # 如果适配器失败，记录日志但不抛出异常
            from core.logging.logging_system import log as logger
            logger.warning(f"规则 {self.rule_id} 超快速校验适配器失败，跳过: {e}")
            return None

    def _create_patient_data_adapter(self, ultra_patient_data: "UltraOptimizedPatientData") -> PatientData:
        """
        创建患者数据适配器，将超优化数据转换为标准格式

        这是一个轻量级的适配器，只创建规则需要的数据结构
        子类可以重写此方法以提供更优化的适配逻辑

        Args:
            ultra_patient_data: 超优化患者数据

        Returns:
            标准PatientData格式（仅包含必要字段）
        """
        from models.patient import FeeItem, PatientBasicInfo

        # 创建基本信息
        basic_info = None
        if ultra_patient_data.quick_features.get('gender') or ultra_patient_data.quick_features.get('age'):
            basic_info = PatientBasicInfo(
                gender=ultra_patient_data.quick_features.get('gender'),
                age=ultra_patient_data.quick_features.get('age'),
                birthDate=None  # 如果需要可以从ultra_patient_data获取
            )

        # 创建费用列表（按需转换，避免全量转换）
        fees = []
        if hasattr(self, 'yb_code') and self.yb_code:
            # 只获取规则需要的费用项
            relevant_fees = ultra_patient_data.get_fees_by_codes_ultra_fast(list(self.yb_code))
            for ultra_fee in relevant_fees:
                fee_item = FeeItem(
                    id=ultra_fee.id,
                    ybdm=ultra_fee.ybdm,
                    sl=ultra_fee.sl,
                    je=ultra_fee.je,
                    jzsj=ultra_fee.jzsj,
                    fymc=ultra_fee.fymc,
                    ksdm=ultra_fee.ksdm,
                    ksmc=ultra_fee.ksmc,
                    fydm=ultra_fee.fydm,
                    xh=ultra_fee.xh,
                    dw=ultra_fee.dw
                )
                fees.append(fee_item)

        # 创建轻量级PatientData
        patient_data = PatientData(
            bah=ultra_patient_data.bah,
            basic_information=basic_info,
            fees=fees if fees else None,
            # 其他字段按需添加
            process=None,
            Diagnosis=None  # 诊断信息如需要可以从ultra_patient_data.diagnosis_codes_set构建
        )

        return patient_data

    def get_rule_info(self) -> dict[str, Any]:
        """获取规则信息"""
        return {
            "rule_id": self.rule_id,
            "rule_name": self.rule_name,
            "level1": self.level1,
            "level2": self.level2,
            "level3": self.level3,
        }

    def _trans_timestamp_to_date(self, timestamp: int) -> str:
        """将时间戳转换为日期字符串"""
        return datetime.fromtimestamp(timestamp).strftime("%Y%m%d")

    def __repr__(self):
        return f"<{self.__class__.__name__}(rule_id='{self.rule_id}')>"
