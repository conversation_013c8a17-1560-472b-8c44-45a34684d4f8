"""
HTTP重试配置管理模块
提供重试配置的解析、验证和环境特定配置支持
"""

import logging
from dataclasses import dataclass

from config.settings import Settings

logger = logging.getLogger(__name__)


@dataclass
class RetryConfig:
    """HTTP重试配置数据类"""

    # 基础配置
    enabled: bool
    max_attempts: int
    base_delay: float
    max_delay: float
    backoff_factor: float
    jitter: bool

    # 可重试条件
    retryable_status_codes: list[int]
    retry_on_connection_error: bool
    retry_on_timeout_error: bool
    retry_on_dns_error: bool

    # 监控配置
    metrics_enabled: bool

    def __post_init__(self):
        """配置验证"""
        self._validate_config()

    def _validate_config(self):
        """验证配置参数的合理性"""
        if self.max_attempts < 1:
            raise ValueError("max_attempts必须大于等于1")

        if self.base_delay <= 0:
            raise ValueError("base_delay必须大于0")

        if self.max_delay < self.base_delay:
            raise ValueError("max_delay必须大于等于base_delay")

        if self.backoff_factor <= 1:
            raise ValueError("backoff_factor必须大于1")

        # 验证状态码
        for code in self.retryable_status_codes:
            if not (100 <= code <= 599):
                raise ValueError(f"无效的HTTP状态码: {code}")


@dataclass
class CircuitBreakerConfig:
    """断路器配置数据类"""

    enabled: bool
    failure_threshold: int
    failure_rate_threshold: float
    recovery_timeout: float
    window_size: int
    half_open_max_calls: int

    def __post_init__(self):
        """配置验证"""
        self._validate_config()

    def _validate_config(self):
        """验证断路器配置参数"""
        if self.failure_threshold < 1:
            raise ValueError("failure_threshold必须大于等于1")

        if not (0.0 <= self.failure_rate_threshold <= 1.0):
            raise ValueError("failure_rate_threshold必须在0.0到1.0之间")

        if self.recovery_timeout <= 0:
            raise ValueError("recovery_timeout必须大于0")

        if self.window_size < 1:
            raise ValueError("window_size必须大于等于1")

        if self.half_open_max_calls < 1:
            raise ValueError("half_open_max_calls必须大于等于1")


class RetryConfigManager:
    """重试配置管理器"""

    def __init__(self, settings: Settings):
        self.settings = settings
        self._retry_config: RetryConfig | None = None
        self._circuit_breaker_config: CircuitBreakerConfig | None = None

    def get_retry_config(self) -> RetryConfig:
        """获取当前环境的重试配置"""
        if self._retry_config is None:
            self._retry_config = self._build_retry_config()
        return self._retry_config

    def get_circuit_breaker_config(self) -> CircuitBreakerConfig:
        """获取当前环境的断路器配置"""
        if self._circuit_breaker_config is None:
            self._circuit_breaker_config = self._build_circuit_breaker_config()
        return self._circuit_breaker_config

    def _build_retry_config(self) -> RetryConfig:
        """根据当前环境构建重试配置"""
        # 检测当前环境
        env = self._detect_environment()

        # 获取环境特定配置
        if env == "DEV":
            enabled = self.settings.HTTP_RETRY_DEV_ENABLED
            max_attempts = self.settings.HTTP_RETRY_DEV_MAX_ATTEMPTS
            base_delay = self.settings.HTTP_RETRY_DEV_BASE_DELAY
            max_delay = self.settings.HTTP_RETRY_DEV_MAX_DELAY
            backoff_factor = self.settings.HTTP_RETRY_DEV_BACKOFF_FACTOR
        elif env == "TEST":
            enabled = self.settings.HTTP_RETRY_TEST_ENABLED
            max_attempts = self.settings.HTTP_RETRY_TEST_MAX_ATTEMPTS
            base_delay = self.settings.HTTP_RETRY_TEST_BASE_DELAY
            max_delay = self.settings.HTTP_RETRY_TEST_MAX_DELAY
            backoff_factor = self.settings.HTTP_RETRY_TEST_BACKOFF_FACTOR
        elif env == "PROD":
            enabled = self.settings.HTTP_RETRY_PROD_ENABLED
            max_attempts = self.settings.HTTP_RETRY_PROD_MAX_ATTEMPTS
            base_delay = self.settings.HTTP_RETRY_PROD_BASE_DELAY
            max_delay = self.settings.HTTP_RETRY_PROD_MAX_DELAY
            backoff_factor = self.settings.HTTP_RETRY_PROD_BACKOFF_FACTOR
        else:
            # 使用默认配置
            enabled = self.settings.HTTP_RETRY_ENABLED
            max_attempts = self.settings.HTTP_RETRY_MAX_ATTEMPTS
            base_delay = self.settings.HTTP_RETRY_BASE_DELAY
            max_delay = self.settings.HTTP_RETRY_MAX_DELAY
            backoff_factor = self.settings.HTTP_RETRY_BACKOFF_FACTOR

        # 解析可重试状态码
        retryable_status_codes = self._parse_status_codes(self.settings.HTTP_RETRY_STATUS_CODES)

        return RetryConfig(
            enabled=enabled,
            max_attempts=max_attempts,
            base_delay=base_delay,
            max_delay=max_delay,
            backoff_factor=backoff_factor,
            jitter=self.settings.HTTP_RETRY_JITTER,
            retryable_status_codes=retryable_status_codes,
            retry_on_connection_error=self.settings.HTTP_RETRY_ON_CONNECTION_ERROR,
            retry_on_timeout_error=self.settings.HTTP_RETRY_ON_TIMEOUT_ERROR,
            retry_on_dns_error=self.settings.HTTP_RETRY_ON_DNS_ERROR,
            metrics_enabled=self.settings.HTTP_RETRY_METRICS_ENABLED,
        )

    def _build_circuit_breaker_config(self) -> CircuitBreakerConfig:
        """构建断路器配置"""
        return CircuitBreakerConfig(
            enabled=self.settings.CIRCUIT_BREAKER_ENABLED,
            failure_threshold=self.settings.CIRCUIT_BREAKER_FAILURE_THRESHOLD,
            failure_rate_threshold=self.settings.CIRCUIT_BREAKER_FAILURE_RATE_THRESHOLD,
            recovery_timeout=self.settings.CIRCUIT_BREAKER_RECOVERY_TIMEOUT,
            window_size=self.settings.CIRCUIT_BREAKER_WINDOW_SIZE,
            half_open_max_calls=self.settings.CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS,
        )

    def _detect_environment(self) -> str:
        """检测当前运行环境"""
        run_mode = self.settings.RUN_MODE.upper()

        # 环境映射
        env_mapping = {
            "DEV": "DEV",
            "DEVELOPMENT": "DEV",
            "TEST": "TEST",
            "TESTING": "TEST",
            "PROD": "PROD",
            "PRODUCTION": "PROD",
        }

        return env_mapping.get(run_mode, "PROD")  # 默认为生产环境

    def _parse_status_codes(self, status_codes_str: str) -> list[int]:
        """解析状态码字符串为整数列表"""
        # 默认的可重试状态码
        default_codes = [408, 429, 500, 502, 503, 504]

        if not status_codes_str:
            logger.warning("状态码字符串为空，使用默认状态码")
            return default_codes

        try:
            codes = []
            for code_str in status_codes_str.split(","):
                code_str = code_str.strip()
                if code_str:
                    codes.append(int(code_str))

            # 确保至少有一个状态码
            if not codes:
                logger.warning("解析后的状态码列表为空，使用默认状态码")
                return default_codes

            return codes
        except (ValueError, AttributeError) as e:
            logger.error(f"解析HTTP状态码失败: {status_codes_str}, 错误: {e}")
            # 返回默认的可重试状态码
            return default_codes

    def reload_config(self):
        """重新加载配置（用于运行时配置更新）"""
        self._retry_config = None
        self._circuit_breaker_config = None
        logger.info("重试配置已重新加载")

    def get_config_summary(self) -> dict:
        """获取配置摘要信息"""
        retry_config = self.get_retry_config()
        circuit_config = self.get_circuit_breaker_config()

        return {
            "environment": self._detect_environment(),
            "retry": {
                "enabled": retry_config.enabled,
                "max_attempts": retry_config.max_attempts,
                "base_delay": retry_config.base_delay,
                "max_delay": retry_config.max_delay,
                "backoff_factor": retry_config.backoff_factor,
                "retryable_status_codes": retry_config.retryable_status_codes,
            },
            "circuit_breaker": {
                "enabled": circuit_config.enabled,
                "failure_threshold": circuit_config.failure_threshold,
                "failure_rate_threshold": circuit_config.failure_rate_threshold,
                "recovery_timeout": circuit_config.recovery_timeout,
            },
        }


# 全局配置管理器实例
def get_retry_config_manager(settings: Settings | None = None) -> RetryConfigManager:
    """获取重试配置管理器实例"""
    if settings is None:
        from config.settings import get_settings

        settings = get_settings()

    return RetryConfigManager(settings)
