/**
 * 规则详情管理Composable
 * 封装规则详情的获取、展示和操作逻辑
 */
import { ref, computed, watch, toRef } from 'vue'
import { useRulesStore } from '@/stores/rules'
import { useRuleDetailsStore } from '@/stores/ruleDetails'
import { ElMessage } from 'element-plus'
import { enhancedErrorHandler } from '@/utils/enhancedErrorHandler'

/**
 * 规则详情功能
 */
export function useRuleDetail() {
  const rulesStore = useRulesStore()
  const ruleDetailsStore = useRuleDetailsStore() // 新增

  // Store状态 - 安全的响应式访问
  const currentRule = toRef(rulesStore, 'currentRule')
  const ruleSchema = toRef(rulesStore, 'ruleSchema')
  const ruleStatistics = toRef(rulesStore, 'ruleStatistics')
  const detailLoading = toRef(rulesStore, 'detailLoading')

  // 规则明细状态 - 使用专门的ruleDetailsStore
  const currentRuleDetails = toRef(ruleDetailsStore, 'detailsList')
  const detailsCount = toRef(ruleDetailsStore, 'detailsCount')
  const ruleDetailsLoading = toRef(ruleDetailsStore, 'loading')
  const hasRuleDetails = toRef(ruleDetailsStore, 'hasDetails')
  const ruleDetailsCount = computed(() => ruleDetailsStore.detailsCount)

  // 本地状态
  const isVisible = ref(false)
  const currentRuleKey = ref('')

  // 性能监控状态
  const performanceStats = ref({
    fetchRuleDetailTime: 0,
    fetchRuleDetailsListTime: 0,
    lastUpdateTime: null
  })

  // 计算属性
  const drawerTitle = computed(() => {
    return currentRule.value?.rule_name ? `规则详情 - ${currentRule.value.rule_name}` : '规则详情'
  })

  const hasRuleData = computed(() => {
    return currentRule.value?.rule_key
  })

  const ruleSchemaFormatted = computed(() => {
    const schema = ruleSchema.value || []
    if (!Array.isArray(schema)) return []
    return schema.map(item => ({
      ...item,
      typeDisplay: formatParameterType(item?.type),
      requiredDisplay: item?.required ? '必填' : '可选',
      description: getParameterDescription(item)
    }))
  })

  const ruleStatsFormatted = computed(() => {
    const stats = ruleStatistics.value || {}
    if (!stats || typeof stats !== 'object') return []
    return Object.entries(stats).map(([key, value]) => ({
      key,
      label: formatStatLabel(key),
      value: formatStatValue(key, value)
    }))
  })

  // 获取规则详情
  const fetchRuleDetail = async (ruleKey) => {
    if (!ruleKey) return null

    const startTime = performance.now()
    try {
      currentRuleKey.value = ruleKey
      const result = await rulesStore.fetchRuleDetail(ruleKey)

      // 记录性能数据
      const endTime = performance.now()
      performanceStats.value.fetchRuleDetailTime = endTime - startTime
      performanceStats.value.lastUpdateTime = Date.now()

      return result
    } catch (error) {
      enhancedErrorHandler.handleError(error, {
        context: 'useRuleDetail.fetchRuleDetail',
        showMessage: true,
        enableRetry: false
      })
      return null
    }
  }

  // 清除当前规则
  const clearCurrentRule = () => {
    rulesStore.clearCurrentRule()
    currentRuleKey.value = ''
  }

  // 显示规则详情
  const showRuleDetail = async (ruleKey) => {
    if (ruleKey) {
      await fetchRuleDetail(ruleKey)
      isVisible.value = true
    }
  }

  // 隐藏规则详情
  const hideRuleDetail = () => {
    isVisible.value = false
    clearCurrentRule()
  }

  // 监听可见性变化
  watch(isVisible, (newVisible) => {
    if (!newVisible) {
      clearCurrentRule()
    }
  })

  // 格式化参数类型
  const formatParameterType = (type) => {
    const typeMap = {
      'string': '文本',
      'number': '数字',
      'boolean': '布尔',
      'date': '日期',
      'datetime': '日期时间',
      'array': '数组',
      'object': '对象'
    }
    return typeMap[type] || type
  }

  // 获取参数描述
  const getParameterDescription = (param) => {
    const parts = []

    if (param.description) {
      parts.push(param.description)
    }

    if (param.example) {
      parts.push(`示例: ${param.example}`)
    }

    if (param.constraints) {
      parts.push(`约束: ${param.constraints}`)
    }

    return parts.join(' | ') || '暂无说明'
  }

  // 格式化统计标签
  const formatStatLabel = (key) => {
    const labelMap = {
      'total_executions': '总执行次数',
      'success_rate': '成功率',
      'avg_execution_time': '平均执行时间',
      'last_execution': '最后执行时间',
      'error_count': '错误次数',
      'last_error': '最后错误时间'
    }
    return labelMap[key] || key
  }

  // 格式化统计值
  const formatStatValue = (key, value) => {
    if (value === null || value === undefined) {
      return '暂无数据'
    }

    switch (key) {
      case 'success_rate':
        return `${(value * 100).toFixed(1)}%`
      case 'avg_execution_time':
        return `${value}ms`
      case 'last_execution':
      case 'last_error':
        return new Date(value).toLocaleString()
      default:
        return String(value)
    }
  }

  // 获取类型标签类型
  const getTypeTagType = (type) => {
    const typeTagMap = {
      'string': '',
      'number': 'success',
      'boolean': 'warning',
      'date': 'info',
      'datetime': 'info',
      'array': 'danger',
      'object': 'danger'
    }
    return typeTagMap[type] || ''
  }

  // ==================== 规则明细相关方法 ====================

  /**
   * 获取规则明细列表
   * @param {string} ruleKey - 规则键
   * @returns {Promise<Array>} 明细列表
   */
  const fetchRuleDetailsList = async (ruleKey) => {
    if (!ruleKey) return []

    const startTime = performance.now()
    try {
      const result = await ruleDetailsStore.fetchDetailsList(ruleKey)

      // 记录性能数据
      const endTime = performance.now()
      performanceStats.value.fetchRuleDetailsListTime = endTime - startTime
      performanceStats.value.lastUpdateTime = Date.now()

      return result?.items || []
    } catch (error) {
      enhancedErrorHandler.handleError(error, {
        context: 'useRuleDetail.fetchRuleDetailsList',
        showMessage: true,
        enableRetry: false
      })
      return []
    }
  }

  /**
   * 获取规则明细数量
   * @param {string} ruleKey - 规则键
   * @returns {Promise<number>} 明细数量
   */
  const getRuleDetailsCount = async (ruleKey) => {
    if (!ruleKey) return 0

    try {
      const result = await ruleDetailsStore.fetchDetailsList(ruleKey)
      return result?.total || 0
    } catch (error) {
      enhancedErrorHandler.handle(error, {
        context: 'useRuleDetail.getRuleDetailsCount',
        showMessage: false,
        enableRetry: false
      })
      return 0
    }
  }

  /**
   * 刷新规则明细
   * @param {string} ruleKey - 规则键
   * @returns {Promise<Array>} 明细列表
   */
  const refreshRuleDetails = async (ruleKey) => {
    if (!ruleKey) return []

    try {
      // 清除缓存并重新获取
      await ruleDetailsStore.clearEnhancedCache(ruleKey)
      const result = await ruleDetailsStore.fetchDetailsList(ruleKey)
      return result?.items || []
    } catch (error) {
      enhancedErrorHandler.handleError(error, {
        context: 'useRuleDetail.refreshRuleDetails',
        showMessage: true,
        enableRetry: false
      })
      return []
    }
  }

  /**
   * 获取性能统计信息
   * @returns {Object} 性能统计
   */
  const getPerformanceStats = () => {
    return {
      ...performanceStats.value,
      averageResponseTime: (performanceStats.value.fetchRuleDetailTime + performanceStats.value.fetchRuleDetailsListTime) / 2
    }
  }

  /**
   * 重置性能统计
   */
  const resetPerformanceStats = () => {
    performanceStats.value = {
      fetchRuleDetailTime: 0,
      fetchRuleDetailsListTime: 0,
      lastUpdateTime: null
    }
  }

  return {
    // 响应式数据
    currentRule,
    ruleSchema,
    ruleStatistics,
    detailLoading,
    isVisible,
    currentRuleKey,
    drawerTitle,
    hasRuleData,
    ruleSchemaFormatted,
    ruleStatsFormatted,

    // 规则明细状态（新增）
    currentRuleDetails,
    detailsCount,
    ruleDetailsLoading,
    hasRuleDetails,
    ruleDetailsCount,

    // 方法
    fetchRuleDetail,
    clearCurrentRule,
    showRuleDetail,
    hideRuleDetail,
    formatParameterType,
    getParameterDescription,
    formatStatLabel,
    formatStatValue,
    getTypeTagType,

    // 规则明细方法
    fetchRuleDetailsList,
    getRuleDetailsCount,
    refreshRuleDetails,

    // 性能监控
    performanceStats,
    getPerformanceStats,
    resetPerformanceStats
  }
}

/**
 * 规则详情抽屉管理
 */
export function useRuleDetailDrawer(props, emit) {
  const {
    currentRule,
    ruleSchema,
    ruleStatistics,
    detailLoading,
    drawerTitle,
    ruleSchemaFormatted,
    ruleStatsFormatted,
    fetchRuleDetail,
    clearCurrentRule,
    getTypeTagType
  } = useRuleDetail()

  // 抽屉可见性
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 监听ruleKey变化
  watch(() => props.ruleKey, async (newRuleKey) => {
    if (newRuleKey && visible.value) {
      await fetchRuleDetail(newRuleKey)
    }
  }, { immediate: true })

  // 监听抽屉显示状态
  watch(visible, async (newVisible) => {
    if (newVisible && props.ruleKey) {
      await fetchRuleDetail(props.ruleKey)
    } else if (!newVisible) {
      clearCurrentRule()
    }
  })

  // 关闭抽屉
  const handleClose = () => {
    visible.value = false
  }

  // 下载模板
  const handleDownloadTemplate = () => {
    if (currentRule.value) {
      emit('download-template', currentRule.value)
    }
  }

  // 上传数据
  const handleUploadData = () => {
    if (currentRule.value) {
      emit('upload-data', currentRule.value)
    }
  }

  return {
    // 响应式数据
    visible,
    currentRule,
    ruleSchema,
    ruleStatistics,
    detailLoading,
    drawerTitle,
    ruleSchemaFormatted,
    ruleStatsFormatted,

    // 方法
    handleClose,
    handleDownloadTemplate,
    handleUploadData,
    getTypeTagType
  }
}
