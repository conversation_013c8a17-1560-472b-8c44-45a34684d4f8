/**
 * 规则Store缓存管理测试
 * 测试智能缓存、性能监控和状态管理功能
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useRulesStore } from '../rules.js'

// Mock API
vi.mock('../../api/rules', () => ({
  getRulesStatus: vi.fn(),
  getRuleDetail: vi.fn(),
  getRuleSchema: vi.fn(),
  getRuleStatistics: vi.fn(),
  downloadRuleTemplate: vi.fn(),
  confirmRuleSubmission: vi.fn()
}))

// Mock 依赖
vi.mock('@/composables/core/useAsyncState', () => ({
  useAsyncState: vi.fn(() => ({
    isLoading: { value: false },
    execute: vi.fn()
  }))
}))

vi.mock('@/composables/core/useStateMachine', () => ({
  useStateMachine: vi.fn(() => ({
    start: vi.fn(),
    success: vi.fn(),
    error: vi.fn()
  }))
}))

vi.mock('@/composables/core/useFeedback', () => ({
  useFeedback: vi.fn(() => ({
    toastSuccess: vi.fn(),
    toastError: vi.fn()
  }))
}))

vi.mock('../app', () => ({
  useAppStore: vi.fn(() => ({
    addLoadingTask: vi.fn(),
    removeLoadingTask: vi.fn()
  }))
}))

describe('RulesStore 缓存管理', () => {
  let store

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useRulesStore()
    vi.clearAllTimers()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.clearAllMocks()
    vi.useRealTimers()
  })

  describe('缓存配置', () => {
    it('应该初始化默认缓存配置', () => {
      expect(store.cacheConfig.maxSize).toBe(50)
      expect(store.cacheConfig.ttl).toBe(10 * 60 * 1000)
      expect(store.cacheConfig.autoCleanup).toBe(true)
      expect(store.cacheConfig.compressionEnabled).toBe(true)
    })

    it('应该初始化操作指标', () => {
      expect(store.operationMetrics.totalRequests).toBe(0)
      expect(store.operationMetrics.successfulRequests).toBe(0)
      expect(store.operationMetrics.failedRequests).toBe(0)
      expect(store.operationMetrics.averageResponseTime).toBe(0)
    })
  })

  describe('缓存性能指标', () => {
    it('应该正确计算缓存命中率', () => {
      store.operationMetrics.totalRequests = 100
      store.operationMetrics.successfulRequests = 85

      const performance = store.cachePerformance
      expect(performance.rulesHitRate).toBe('85.00')
      expect(performance.cacheSize).toBe(0) // 初始为空
      expect(performance.maxCacheSize).toBe(150) // 50 * 3
    })

    it('应该正确计算缓存利用率', () => {
      // 添加一些缓存数据
      store.rulesCache.value.set('test1', { data: 'test1', timestamp: Date.now() })
      store.schemaCache.value.set('test2', { data: 'test2', timestamp: Date.now() })
      store.statsCache.value.set('test3', { data: 'test3', timestamp: Date.now() })

      const performance = store.cachePerformance
      expect(performance.cacheSize).toBe(3)
      expect(parseFloat(performance.cacheUtilization)).toBeCloseTo(2.0, 1) // 3/150 * 100
    })
  })

  describe('系统健康状态', () => {
    it('应该正确评估健康状态 - 健康', () => {
      store.operationMetrics.totalRequests = 100
      store.operationMetrics.successfulRequests = 98
      store.operationMetrics.failedRequests = 2

      const health = store.systemHealth
      expect(health.status).toBe('healthy')
      expect(health.score).toBe(98)
      expect(health.metrics.successRate).toBe('98.00')
    })

    it('应该正确评估健康状态 - 警告', () => {
      store.operationMetrics.totalRequests = 100
      store.operationMetrics.successfulRequests = 85
      store.operationMetrics.failedRequests = 15

      const health = store.systemHealth
      expect(health.status).toBe('warning')
      expect(health.score).toBe(85)
    })

    it('应该正确评估健康状态 - 严重', () => {
      store.operationMetrics.totalRequests = 100
      store.operationMetrics.successfulRequests = 70
      store.operationMetrics.failedRequests = 30

      const health = store.systemHealth
      expect(health.status).toBe('critical')
      expect(health.score).toBe(70)
    })

    it('应该处理无请求的情况', () => {
      store.operationMetrics.totalRequests = 0

      const health = store.systemHealth
      expect(health.status).toBe('healthy')
      expect(health.score).toBe(100)
      expect(health.metrics.successRate).toBe('100.00')
    })
  })

  describe('缓存清理', () => {
    beforeEach(() => {
      // 设置测试数据
      const now = Date.now()
      const oldTime = now - 15 * 60 * 1000 // 15分钟前

      store.rulesCache.value.set('fresh', { data: 'fresh', timestamp: now })
      store.rulesCache.value.set('stale', { data: 'stale', timestamp: oldTime })
      store.schemaCache.value.set('fresh_schema', { data: 'fresh', timestamp: now })
      store.schemaCache.value.set('stale_schema', { data: 'stale', timestamp: oldTime })
    })

    it('应该清理过期缓存', () => {
      expect(store.rulesCache.value.size).toBe(2)
      expect(store.schemaCache.value.size).toBe(2)

      store.cleanupCache()

      // 应该只保留新鲜的缓存
      expect(store.rulesCache.value.has('fresh')).toBe(true)
      expect(store.rulesCache.value.has('stale')).toBe(false)
      expect(store.schemaCache.value.has('fresh_schema')).toBe(true)
      expect(store.schemaCache.value.has('stale_schema')).toBe(false)
    })

    it('应该在缓存过大时清理最旧的条目', () => {
      // 填满缓存
      for (let i = 0; i < 60; i++) {
        store.rulesCache.value.set(`rule_${i}`, {
          data: `data_${i}`,
          timestamp: Date.now() - i * 1000
        })
      }

      expect(store.rulesCache.value.size).toBe(62) // 60 + 2个测试数据

      store.cleanupCache()

      // 应该清理掉一些最旧的条目
      expect(store.rulesCache.value.size).toBeLessThan(62)
    })
  })

  describe('性能指标更新', () => {
    it('应该正确更新成功指标', () => {
      store.updateMetrics(true, 150)

      expect(store.operationMetrics.totalRequests).toBe(1)
      expect(store.operationMetrics.successfulRequests).toBe(1)
      expect(store.operationMetrics.failedRequests).toBe(0)
      expect(store.operationMetrics.averageResponseTime).toBe(150)
      expect(store.operationMetrics.lastOperationTime).toBeDefined()
    })

    it('应该正确更新失败指标', () => {
      store.updateMetrics(false, 300)

      expect(store.operationMetrics.totalRequests).toBe(1)
      expect(store.operationMetrics.successfulRequests).toBe(0)
      expect(store.operationMetrics.failedRequests).toBe(1)
      expect(store.operationMetrics.averageResponseTime).toBe(300)
    })

    it('应该正确计算平均响应时间', () => {
      store.updateMetrics(true, 100)
      store.updateMetrics(true, 200)
      store.updateMetrics(true, 300)

      expect(store.operationMetrics.averageResponseTime).toBe(200)
    })
  })

  describe('自动缓存清理', () => {
    it('应该在请求数达到阈值时自动清理', () => {
      const cleanupSpy = vi.spyOn(store, 'cleanupCache')

      // 模拟100次请求
      for (let i = 1; i <= 100; i++) {
        store.updateMetrics(true, 100)
      }

      expect(cleanupSpy).toHaveBeenCalledTimes(1)
    })

    it('应该在200次请求时再次清理', () => {
      const cleanupSpy = vi.spyOn(store, 'cleanupCache')

      // 模拟200次请求
      for (let i = 1; i <= 200; i++) {
        store.updateMetrics(true, 100)
      }

      expect(cleanupSpy).toHaveBeenCalledTimes(2)
    })
  })

  describe('增强规则获取', () => {
    it('应该使用缓存数据', async () => {
      const { getRulesStatus } = await import('../../api/rules')

      // 设置缓存数据
      store.rules = [{ rule_key: 'test1' }]
      store.lastFetchTime = Date.now()

      const result = await store.fetchRules(false)

      expect(result).toEqual([{ rule_key: 'test1' }])
      expect(getRulesStatus).not.toHaveBeenCalled()
    })

    it('应该在强制刷新时忽略缓存', async () => {
      const { getRulesStatus } = await import('../../api/rules')
      getRulesStatus.mockResolvedValue({
        success: true,
        data: [{ rule_key: 'new_rule' }]
      })

      // 设置缓存数据
      store.rules = [{ rule_key: 'old_rule' }]
      store.lastFetchTime = Date.now()

      const result = await store.fetchRules(true)

      expect(getRulesStatus).toHaveBeenCalled()
      expect(store.rules).toEqual([{ rule_key: 'new_rule' }])
    })

    it('应该在缓存过期时重新获取', async () => {
      const { getRulesStatus } = await import('../../api/rules')
      getRulesStatus.mockResolvedValue({
        success: true,
        data: [{ rule_key: 'fresh_rule' }]
      })

      // 设置过期的缓存数据
      store.rules = [{ rule_key: 'expired_rule' }]
      store.lastFetchTime = Date.now() - 15 * 60 * 1000 // 15分钟前

      const result = await store.fetchRules(false)

      expect(getRulesStatus).toHaveBeenCalled()
      expect(store.rules).toEqual([{ rule_key: 'fresh_rule' }])
    })

    it('应该正确更新缓存和指标', async () => {
      const { getRulesStatus } = await import('../../api/rules')
      getRulesStatus.mockResolvedValue({
        success: true,
        data: [
          { rule_key: 'rule1' },
          { rule_key: 'rule2' }
        ]
      })

      await store.fetchRules(true)

      expect(store.rulesCache.value.has('rule1')).toBe(true)
      expect(store.rulesCache.value.has('rule2')).toBe(true)
      expect(store.operationMetrics.totalRequests).toBe(1)
      expect(store.operationMetrics.successfulRequests).toBe(1)
    })
  })
})
