"""
优化的规则基类
实现预编译、快速预检查、向量化计算等优化策略
"""

import re
import time
from functools import lru_cache
from re import Pattern
from typing import Any

from core.logging.logging_system import log as logger
from models.patient import PatientData
from models.rule import RuleOutput, RuleResult
from rules.base_rules.base import BaseRule


class OptimizedBaseRule(BaseRule):
    """
    优化的规则基类
    提供预编译、快速预检查、向量化计算等优化功能
    """

    def __init__(self, rule_id: str, **kwargs):
        """
        初始化优化规则基类

        Args:
            rule_id: 规则ID
            **kwargs: 其他规则参数
        """
        super().__init__(rule_id, **kwargs)

        # 预编译的正则表达式缓存
        self._compiled_patterns: dict[str, Pattern] = {}

        # 预计算的常用值
        self._precomputed_values: dict[str, Any] = {}

        # 快速预检查缓存
        self._precheck_cache: dict[str, bool] = {}

        # 初始化优化组件
        self._initialize_optimizations()

    def _initialize_optimizations(self):
        """初始化优化组件"""
        # 预编译常用正则表达式
        self._precompile_patterns()

        # 预计算常用值
        self._precompute_values()

        # 初始化快速预检查
        self._initialize_precheck()

    def _precompile_patterns(self):
        """预编译正则表达式"""
        # 常用的正则表达式模式
        common_patterns = {
            "timestamp": r"^\d{10,13}$",  # 时间戳格式
            "gender_male": r"^(男|1)$",  # 男性标识
            "gender_female": r"^(女|0|2)$",  # 女性标识
            "self_pay": r"^400$",  # 自费标识
            "digits_only": r"^\d+$",  # 纯数字
        }

        for name, pattern in common_patterns.items():
            try:
                self._compiled_patterns[name] = re.compile(pattern, re.IGNORECASE)
            except re.error as e:
                logger.warning(f"Failed to compile pattern '{name}': {e}")

    def _precompute_values(self):
        """预计算常用值"""
        # 预计算医保代码集合（如果有）
        if hasattr(self, "yb_codes") and self.yb_codes:
            self._precomputed_values["yb_codes_set"] = set(self.yb_codes)

        # 预计算性别限制
        if hasattr(self, "gender_limit"):
            self._precomputed_values["gender_limit_normalized"] = str(self.gender_limit).lower()

        # 预计算年龄限制
        if hasattr(self, "age_min"):
            self._precomputed_values["age_min"] = int(self.age_min) if self.age_min else None
        if hasattr(self, "age_max"):
            self._precomputed_values["age_max"] = int(self.age_max) if self.age_max else None

    def _initialize_precheck(self):
        """初始化快速预检查"""
        # 基于规则类型设置预检查策略
        rule_type = getattr(self, "rule_key", "")

        if "gender" in rule_type or "male" in rule_type or "female" in rule_type:
            self._precheck_cache["requires_gender_check"] = True

        if "age" in rule_type or "pediatric" in rule_type:
            self._precheck_cache["requires_age_check"] = True

        if "drug" in rule_type or "medication" in rule_type:
            self._precheck_cache["requires_drug_check"] = True

    def fast_precheck(self, patient_data: PatientData) -> bool:
        """
        快速预检查，判断是否需要执行完整规则

        Args:
            patient_data: 患者数据

        Returns:
            True表示需要执行完整规则，False表示可以跳过
        """
        # 性别检查
        if self._precheck_cache.get("requires_gender_check", False):
            if not self._check_gender_applicable(patient_data):
                return False

        # 年龄检查
        if self._precheck_cache.get("requires_age_check", False):
            if not self._check_age_applicable(patient_data):
                return False

        # 药品检查
        if self._precheck_cache.get("requires_drug_check", False):
            if not self._check_drug_applicable(patient_data):
                return False

        return True

    def _check_gender_applicable(self, patient_data: PatientData) -> bool:
        """检查性别是否适用"""
        if not patient_data.basic_information or not patient_data.basic_information.gender:
            return True  # 无性别信息时继续检查

        gender = str(patient_data.basic_information.gender).lower()

        # 如果是男性限制规则
        if hasattr(self, "rule_key") and "male" in self.rule_key:
            return gender in ("男", "1")

        # 如果是女性限制规则
        if hasattr(self, "rule_key") and "female" in self.rule_key:
            return gender in ("女", "0", "2")

        return True

    def _check_age_applicable(self, patient_data: PatientData) -> bool:
        """检查年龄是否适用"""
        if not patient_data.basic_information or patient_data.basic_information.age is None:
            return True  # 无年龄信息时继续检查

        age = patient_data.basic_information.age

        # 检查年龄范围
        age_min = self._precomputed_values.get("age_min")
        age_max = self._precomputed_values.get("age_max")

        if age_min is not None and age < age_min:
            return False

        if age_max is not None and age > age_max:
            return False

        return True

    def _check_drug_applicable(self, patient_data: PatientData) -> bool:
        """检查是否有相关药品"""
        if not hasattr(self, "yb_codes") or not self.yb_codes:
            return True

        yb_codes_set = self._precomputed_values.get("yb_codes_set", set(self.yb_codes))

        # 快速检查是否有匹配的药品编码
        for fee in patient_data.fees:
            if fee.ybdm in yb_codes_set:
                return True

        return False

    def get_relevant_fees(self, patient_data: PatientData) -> list[Any]:
        """
        获取相关的费用项（向量化处理）

        Args:
            patient_data: 患者数据

        Returns:
            相关费用项列表
        """
        if not hasattr(self, "yb_codes") or not self.yb_codes:
            return patient_data.fees

        yb_codes_set = self._precomputed_values.get("yb_codes_set", set(self.yb_codes))

        # 向量化筛选相关费用项
        relevant_fees = []
        for fee in patient_data.fees:
            if fee.ybdm in yb_codes_set:
                # 验证时间戳格式
                if self._is_valid_timestamp(fee.jzsj):
                    relevant_fees.append(fee)

        return relevant_fees

    def _is_valid_timestamp(self, timestamp) -> bool:
        """快速验证时间戳格式"""
        if not timestamp:
            return False

        timestamp_str = str(timestamp)
        pattern = self._compiled_patterns.get("timestamp")

        if pattern:
            return bool(pattern.match(timestamp_str))
        else:
            # 回退到基本检查
            return timestamp_str.isdigit() and len(timestamp_str) >= 10

    def calculate_aggregates(self, fees: list[Any]) -> tuple[float, float, int, set[str]]:
        """
        向量化计算聚合值

        Args:
            fees: 费用项列表

        Returns:
            (总数量, 总金额, 总天数, 日期集合)
        """
        if not fees:
            return 0.0, 0.0, 0, set()

        total_quantity = 0.0
        total_amount = 0.0
        date_set = set()

        # 向量化计算
        for fee in fees:
            total_quantity += fee.sl or 0.0
            total_amount += fee.je or 0.0

            # 转换时间戳为日期
            if self._is_valid_timestamp(fee.jzsj):
                date_str = self._trans_timestamp_to_date(int(str(fee.jzsj)[:10]))
                date_set.add(date_str)

        return total_quantity, total_amount, len(date_set), date_set

    @lru_cache(maxsize=1000)
    def _trans_timestamp_to_date(self, timestamp: int) -> str:
        """
        缓存的时间戳转日期函数

        Args:
            timestamp: 时间戳（秒）

        Returns:
            日期字符串 (YYYY-MM-DD)
        """
        try:
            return time.strftime("%Y-%m-%d", time.localtime(timestamp))
        except (ValueError, OSError):
            return "1970-01-01"  # 默认日期

    def create_optimized_result(self, **kwargs) -> RuleResult:
        """
        创建优化的规则结果

        Args:
            **kwargs: 结果参数

        Returns:
            规则结果对象
        """
        # 使用对象池（如果可用）
        try:
            from core.object_pool import object_pool_manager

            result = object_pool_manager.rule_result_pool.borrow()
            output = object_pool_manager.rule_output_pool.borrow()

            # 设置输出属性
            for key, value in kwargs.items():
                if hasattr(output, key):
                    setattr(output, key, value)

            result.id = self.rule_id
            result.output = output

            return result

        except ImportError:
            # 回退到直接创建
            output = RuleOutput(**kwargs)
            return RuleResult(id=self.rule_id, output=output)

    def validate_optimized(self, patient_data: PatientData) -> RuleResult | None:
        """
        优化的验证方法

        Args:
            patient_data: 患者数据

        Returns:
            规则结果或None
        """
        # 快速预检查
        if not self.fast_precheck(patient_data):
            return None

        # 调用具体的验证逻辑
        return self.validate(patient_data)

    def get_optimization_stats(self) -> dict[str, Any]:
        """获取优化统计信息"""
        return {
            "rule_id": self.rule_id,
            "compiled_patterns": len(self._compiled_patterns),
            "precomputed_values": len(self._precomputed_values),
            "precheck_cache": len(self._precheck_cache),
            "has_yb_codes": hasattr(self, "yb_codes") and bool(self.yb_codes),
            "yb_codes_count": len(getattr(self, "yb_codes", [])),
        }
