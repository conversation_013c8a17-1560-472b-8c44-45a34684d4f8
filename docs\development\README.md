# 开发文档

本目录包含面向开发人员的技术文档，涵盖系统架构、API接口、前后端开发和测试等内容。

## 📋 文档结构

### 🏗️ 系统架构 (`architecture/`)
- [系统架构概览.md](architecture/系统架构概览.md) - 整体架构设计和技术选型
- [主从架构设计.md](architecture/主从架构设计.md) - Master-Slave架构详细设计
- [数据库设计.md](architecture/数据库设计.md) - 数据库架构和模型设计
- [性能优化架构.md](architecture/性能优化架构.md) - 性能优化方案和架构

### 🔌 API接口 (`api/`)
- [API快速参考.md](api/API快速参考.md) - API接口快速参考
- [规则管理API.md](api/规则管理API.md) - 规则管理相关接口
- [规则详情API.md](api/规则详情API.md) - 规则详情CRUD接口
- [同步API.md](api/同步API.md) - 主从节点同步接口
- [降级API.md](api/降级API.md) - 系统降级相关接口

### 🎨 前端开发 (`frontend/`)
- [组件使用指南.md](frontend/组件使用指南.md) - Vue组件开发指南
- [设计系统.md](frontend/设计系统.md) - UI设计系统和规范
- [状态管理.md](frontend/状态管理.md) - Pinia状态管理方案
- [TypeScript类型生成工具使用指南.md](frontend/TypeScript类型生成工具使用指南.md) - 自动类型生成工具使用指南

### ⚙️ 后端开发 (`backend/`)
- [服务层设计.md](backend/服务层设计.md) - 后端服务层架构
- [数据模型.md](backend/数据模型.md) - 数据库模型和ORM
- [规则引擎.md](backend/规则引擎.md) - 规则引擎设计和实现

### 🧪 测试文档 (`testing/`)
- [测试策略.md](testing/测试策略.md) - 测试策略和规范
- [单元测试.md](testing/单元测试.md) - 单元测试指南
- [集成测试.md](testing/集成测试.md) - 集成测试指南

## 🎯 开发指南

### 新开发者入门
1. **了解架构**: 阅读 [系统架构概览.md](architecture/系统架构概览.md)
2. **熟悉API**: 查看 [API快速参考.md](api/API快速参考.md)
3. **环境搭建**: 参考用户指南中的安装部署文档
4. **代码规范**: 遵循项目代码规范和最佳实践

### 功能开发流程
1. **需求分析**: 理解功能需求和技术要求
2. **设计方案**: 参考架构文档设计技术方案
3. **编码实现**: 遵循代码规范进行开发
4. **测试验证**: 编写和执行相关测试
5. **文档更新**: 更新相关技术文档

### API开发规范
1. **RESTful设计**: 遵循RESTful API设计原则
2. **统一响应**: 使用统一的响应格式
3. **错误处理**: 实现完善的错误处理机制
4. **文档同步**: 及时更新API文档

## 🔧 开发工具

### 推荐工具
- **IDE**: VS Code / PyCharm
- **API测试**: Postman / Insomnia
- **数据库**: MySQL Workbench / DBeaver
- **版本控制**: Git

### 开发环境
- **Python**: 3.12+
- **Node.js**: 18+
- **数据库**: MySQL 8.0+
- **容器**: Docker & Docker Compose

## 📝 贡献指南

### 代码贡献
1. Fork项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request

### 文档贡献
1. 发现文档问题或改进点
2. 修改相关文档
3. 更新变更记录
4. 提交文档变更

---

**维护说明**: 开发文档应保持技术准确性和时效性，重大架构变更需要及时更新相关文档。
