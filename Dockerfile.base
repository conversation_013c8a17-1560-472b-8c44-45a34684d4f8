# ===== 基础运行环境镜像 (Base Runtime) =====
# 此镜像包含Python 3.12运行环境、系统依赖和通用Python依赖
# 不包含任何项目代码，专为构建缓存优化设计

# ===== 构建阶段 - 依赖安装 =====
FROM python:3.12-slim AS dependency-builder

# 设置构建参数
ARG DEBIAN_FRONTEND=noninteractive
ARG PIP_NO_CACHE_DIR=1
ARG PIP_DISABLE_PIP_VERSION_CHECK=1

# 设置工作目录
WORKDIR /build

# 安装构建依赖（最小化集合）
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    pkg-config \
    default-libmysqlclient-dev \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 升级pip和构建工具
RUN pip install --no-cache-dir --upgrade \
    pip==24.3.1 \
    setuptools==75.6.0 \
    wheel==0.45.1

# 复制依赖文件（利用Docker层缓存）
COPY requirements.txt .

# 安装Python依赖到用户目录（便于后续复制）
RUN pip install --no-cache-dir --user -r requirements.txt \
    && find /usr/local/lib/python3.12 -name '*.pyc' -delete \
    && find /usr/local/lib/python3.12 -name '__pycache__' -delete

# ===== 运行时基础镜像 =====
FROM python:3.12-slim AS base-runtime

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app \
    PATH=/root/.local/bin:/usr/local/bin:$PATH

# 设置构建参数
ARG DEBIAN_FRONTEND=noninteractive

# 安装运行时依赖（最小化）
RUN apt-get update && apt-get install -y --no-install-recommends \
    # 数据库客户端库
    default-libmysqlclient-dev \
    # 网络工具（健康检查用）
    curl \
    vim \
    # 时区数据
    tzdata \
    # 进程管理工具
    procps \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 配置vim支持中文
RUN echo "set encoding=utf-8\n\
    set fileencodings=utf-8,ucs-bom,gb18030,gbk,gb2312,cp936\n\
    set termencoding=utf-8\n\
    syntax on\n\
    set number\n\
    set ruler\n\
    set showcmd\n\
    set nocompatible\n\
    set cursorline\n\
    set laststatus=2\n\
    set statusline=%F%m%r%h%w\ [FORMAT=%{&ff}]\ [TYPE=%Y]\ [POS=%l,%v][%p%%]\ %{strftime(\\\"%Y/%m/%d\ -\ %H:%M\\\")}\n\
    set showmatch\n\
    set mouse=\n\
    set paste\n\
    filetype indent off" > /root/.vimrc \
    && echo "alias ll='ls -lh'" >> /etc/bash.bashrc

# 设置时区为中国标准时间
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

# 创建应用目录结构
RUN mkdir -p /app/logs /app/rules_cache /app/data

# 从builder阶段复制Python包
COPY --from=dependency-builder /root/.local /root/.local
COPY --from=dependency-builder /usr/local/lib/python3.12/site-packages/ /usr/local/lib/python3.12/site-packages/
COPY --from=dependency-builder /usr/local/bin/ /usr/local/bin/
COPY --from=dependency-builder /usr/local/lib/python3.12/lib-dynload/ /usr/local/lib/python3.12/lib-dynload/

# 设置工作目录
WORKDIR /app

# 创建通用健康检查脚本
RUN echo '#!/bin/bash\n\
# 通用健康检查脚本\n\
PORT=${SERVER_PORT:-18001}\n\
curl -f http://localhost:${PORT}/health || exit 1' > /app/healthcheck.sh \
    && chmod +x /app/healthcheck.sh

# 创建启动前检查脚本
RUN echo '#!/bin/bash\n\
# 启动前环境检查\n\
echo "=== 环境检查 ==="\n\
echo "Python版本: $(python --version)"\n\
echo "工作目录: $(pwd)"\n\
echo "用户: $(whoami)"\n\
echo "环境变量:"\n\
env | grep -E "(MODE|RUN_MODE|SERVER_|DATABASE_|LOG_)" | sort\n\
echo "=== 检查完成 ==="' > /app/env-check.sh \
    && chmod +x /app/env-check.sh

# 验证Python环境
RUN python -c "import fastapi, uvicorn, pydantic, requests, loguru, psutil; print('✓ 基础依赖验证成功')"

# 设置默认标签
LABEL maintainer="Rule Validation System" \
      version="1.0.0" \
      description="Base runtime environment for rule validation system" \
      python.version="3.12" \
      type="base-runtime"

# 默认命令（子镜像会覆盖）
CMD ["/app/env-check.sh"]
