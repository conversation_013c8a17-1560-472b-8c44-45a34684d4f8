"""
统一数据映射引擎增强功能测试
测试重构后的UnifiedDataMappingEngine的新功能
"""

import json
import tempfile
from pathlib import Path

import pytest

from core.constants.error_codes import ErrorCodes
from services.unified_data_mapping_engine import (
    UnifiedDataMappingEngine,
    ValidationResult,
)


class TestUnifiedDataMappingEngineEnhanced:
    """统一数据映射引擎增强功能测试"""

    @pytest.fixture
    def temp_config_file(self):
        """创建临时配置文件"""
        config_data = {
            "metadata": {"version": "3.1.0", "description": "测试配置"},
            "field_definitions": {
                "common_fields": {
                    "rule_name": {
                        "chinese_name": "规则名称",
                        "data_type": "string",
                        "required": True,
                        "max_length": 500,
                        "database_column": "rule_name",
                        "api_field": "rule_name",
                        "excel_column": "规则名称",
                        "validation_rules": ["required", "max_length:500"],
                    },
                    "level1": {
                        "chinese_name": "一级错误类型",
                        "data_type": "string",
                        "required": True,
                        "max_length": 100,
                        "database_column": "level1",
                        "api_field": "level1",
                        "excel_column": "一级错误类型",
                        "validation_rules": ["required", "max_length:100"],
                    },
                },
                "specific_fields": {
                    "age_threshold": {
                        "chinese_name": "年龄阈值",
                        "data_type": "integer",
                        "required": False,
                        "database_column": "age_threshold",
                        "api_field": "age_threshold",
                        "excel_column": "年龄阈值",
                        "validation_rules": ["integer", "min:0", "max:150"],
                    }
                },
            },
            "rule_type_mappings": {
                "test_rule_type": {
                    "name": "测试规则类型",
                    "required_fields": ["rule_name", "level1"],
                    "optional_fields": ["age_threshold"],
                }
            },
        }

        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False, encoding="utf-8") as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        yield temp_file
        Path(temp_file).unlink()

    @pytest.fixture
    def engine(self, temp_config_file):
        """创建引擎实例"""
        return UnifiedDataMappingEngine(temp_config_file, enable_cache=True, cache_size=100)

    class TestEnhancedValidation:
        """增强验证功能测试"""

        def test_validate_data_should_return_validation_result(self, engine):
            """验证数据应该返回ValidationResult对象"""
            data = {"rule_name": "测试规则", "level1": "一级错误"}

            result = engine.validate_data(data)

            assert isinstance(result, ValidationResult)
            assert result.valid is True
            assert len(result.errors) == 0
            assert len(result.warnings) == 0
            assert len(result.suggestions) == 0

        def test_validate_data_with_errors_should_provide_suggestions(self, engine):
            """验证有错误的数据应该提供修复建议"""
            data = {"rule_name": "", "level1": "一级错误"}

            result = engine.validate_data(data)

            assert result.valid is False
            assert len(result.errors) > 0
            assert len(result.suggestions) > 0
            assert any("规则名称" in suggestion for suggestion in result.suggestions)

        def test_batch_validate_data_should_process_multiple_records(self, engine):
            """批量验证应该处理多条记录"""
            data_list = [
                {"rule_name": "测试规则1", "level1": "一级错误"},
                {"rule_name": "测试规则2", "level1": "一级错误"},
                {"rule_name": "", "level1": "一级错误"},  # 无效数据
            ]

            results = engine.batch_validate_data(data_list)

            assert len(results) == 3
            assert results[0].valid is True
            assert results[1].valid is True
            assert results[2].valid is False

    class TestCachingMechanism:
        """缓存机制测试"""

        def test_caching_should_improve_performance(self, engine):
            """缓存应该提升性能"""
            data = {"rule_name": "测试规则", "level1": "一级错误"}

            # 第一次验证
            result1 = engine.validate_data(data)
            stats1 = engine.get_stats()

            # 第二次验证（应该使用缓存）
            result2 = engine.validate_data(data)
            stats2 = engine.get_stats()

            assert result1.valid == result2.valid
            assert stats2["cache_hits"] > stats1["cache_hits"]

        def test_clear_cache_should_reset_cache(self, engine):
            """清空缓存应该重置缓存"""
            data = {"rule_name": "测试规则", "level1": "一级错误"}

            # 验证数据以填充缓存
            engine.validate_data(data)
            # stats_before = engine.get_stats()

            # 清空缓存
            engine.clear_cache()

            # 再次验证
            engine.validate_data(data)
            stats_after = engine.get_stats()

            assert stats_after["cache_hits"] == 0

    class TestBatchProcessing:
        """批量处理测试"""

        def test_batch_normalize_field_names_should_process_multiple_records(self, engine):
            """批量标准化字段名应该处理多条记录"""
            data_list = [
                {"rule_name": "测试规则1", "level1": "一级错误1"},
                {"rule_name": "测试规则2", "level1": "一级错误2"},
            ]

            normalized_list = engine.batch_normalize_field_names(data_list)

            assert len(normalized_list) == 2
            assert all("rule_name" in data for data in normalized_list)
            assert all("level1" in data for data in normalized_list)

    class TestMasterSlaveSupport:
        """Master-Slave架构支持测试"""

        def test_serialize_and_deserialize_should_preserve_data(self, engine):
            """序列化和反序列化应该保持数据完整性"""
            original_data = {
                "rule_name": "测试规则",
                "level1": "一级错误",
                "age_threshold": 18,
                "custom_field": "自定义字段",
            }

            # 序列化
            serialized = engine.serialize_for_slave_node(original_data)
            assert isinstance(serialized, bytes)

            # 反序列化
            deserialized = engine.deserialize_from_master_node(serialized)

            # 验证数据完整性
            assert deserialized["rule_name"] == original_data["rule_name"]
            assert deserialized["level1"] == original_data["level1"]
            assert deserialized["age_threshold"] == original_data["age_threshold"]
            assert deserialized["custom_field"] == original_data["custom_field"]

        def test_batch_serialize_and_deserialize_should_preserve_data(self, engine):
            """批量序列化和反序列化应该保持数据完整性"""
            original_data_list = [
                {"rule_name": "测试规则1", "level1": "一级错误1", "age_threshold": 18},
                {"rule_name": "测试规则2", "level1": "一级错误2", "age_threshold": 25},
            ]

            # 批量序列化
            serialized = engine.batch_serialize_for_slave_node(original_data_list)
            assert isinstance(serialized, bytes)

            # 批量反序列化
            deserialized_list = engine.batch_deserialize_from_master_node(serialized)

            # 验证数据完整性
            assert len(deserialized_list) == len(original_data_list)
            for original, deserialized in zip(original_data_list, deserialized_list, strict=True):
                assert deserialized["rule_name"] == original["rule_name"]
                assert deserialized["level1"] == original["level1"]
                assert deserialized["age_threshold"] == original["age_threshold"]

        def test_compression_should_reduce_data_size(self, engine):
            """压缩应该减少数据大小"""
            large_data = {
                "rule_name": "测试规则" * 100,
                "level1": "一级错误" * 100,
                "description": "这是一个很长的描述" * 200,
            }

            # 不压缩
            uncompressed = engine.serialize_for_slave_node(large_data, compression=False)

            # 压缩
            compressed = engine.serialize_for_slave_node(large_data, compression=True)

            # 压缩后应该更小
            assert len(compressed) < len(uncompressed)

    class TestStatistics:
        """统计信息测试"""

        def test_get_stats_should_return_performance_metrics(self, engine):
            """获取统计信息应该返回性能指标"""
            # 执行一些操作
            data = {"rule_name": "测试规则", "level1": "一级错误"}
            engine.validate_data(data)
            engine.normalize_field_names(data)

            stats = engine.get_stats()

            assert "total_validations" in stats
            assert "cache_hits" in stats
            assert "cache_misses" in stats
            assert "batch_operations" in stats
            assert "total_processing_time" in stats
            assert "cache_hit_rate" in stats
            assert "avg_processing_time" in stats

        def test_get_engine_info_should_return_comprehensive_info(self, engine):
            """获取引擎信息应该返回全面的信息"""
            info = engine.get_engine_info()

            assert info["engine_name"] == "UnifiedDataMappingEngine"
            assert info["version"] == "2.0.0"
            assert "fixed_fields_count" in info
            assert "cache_enabled" in info
            assert "cache_size" in info
            assert "stats" in info
            assert "supported_operations" in info
            assert "features" in info

            # 检查新功能是否在支持的操作中
            operations = info["supported_operations"]
            assert "batch_normalize_field_names" in operations
            assert "batch_validate_data" in operations
            assert "serialize_for_slave_node" in operations
            assert "deserialize_from_master_node" in operations

    class TestApiResponseFormat:
        """API响应格式测试"""

        def test_validation_result_to_api_response_data(self, engine):
            """ValidationResult应该能转换为API响应数据格式"""
            data = {"rule_name": "测试规则", "level1": "一级错误"}
            result = engine.validate_data(data)

            api_data = result.to_api_response_data()

            assert "validation_result" in api_data
            validation_result = api_data["validation_result"]
            assert validation_result["valid"] is True
            assert "errors" in validation_result
            assert "warnings" in validation_result
            assert "suggestions" in validation_result
            assert "error_count" in validation_result
            assert "warning_count" in validation_result
            assert "suggestion_count" in validation_result
            assert "timestamp" in validation_result

        def test_validation_result_to_api_response_format_success(self, engine):
            """成功的ValidationResult应该转换为正确的API响应格式"""
            data = {"rule_name": "测试规则", "level1": "一级错误"}
            result = engine.validate_data(data)

            api_response = result.to_api_response_format()

            assert api_response["code"] == ErrorCodes.SUCCESS
            assert api_response["success"] is True
            assert api_response["message"] == "数据验证通过"
            assert "data" in api_response
            assert "timestamp" in api_response

        def test_validation_result_to_api_response_format_error(self, engine):
            """失败的ValidationResult应该转换为正确的API响应格式"""
            data = {"rule_name": "", "level1": "一级错误"}  # 缺少必填字段
            result = engine.validate_data(data)

            api_response = result.to_api_response_format()

            assert api_response["code"] == ErrorCodes.RULE_DETAIL_VALIDATION_FAILED
            assert api_response["success"] is False
            assert "数据验证失败" in api_response["message"]
            assert "个错误" in api_response["message"]
            assert "data" in api_response
            assert "timestamp" in api_response
