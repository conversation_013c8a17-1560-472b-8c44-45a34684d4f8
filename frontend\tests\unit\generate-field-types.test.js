/**
 * 类型生成脚本测试
 * 测试TypeScript类型生成功能
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import TypeScriptGenerator from '../../scripts/generate-field-types.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

describe('TypeScript类型生成器', () => {
  let generator
  let testConfigPath
  let testOutputPath

  beforeEach(() => {
    // 创建测试配置文件
    testConfigPath = path.join(__dirname, 'test-field-mapping.json')
    testOutputPath = path.join(__dirname, 'test-generated-fields.ts')
    
    const testConfig = {
      metadata: {
        version: "3.1.0",
        description: "测试配置"
      },
      field_definitions: {
        common_fields: {
          rule_id: {
            chinese_name: "规则ID",
            data_type: "string",
            required: false,
            description: "规则的唯一标识符"
          },
          rule_name: {
            chinese_name: "规则名称", 
            data_type: "string",
            required: true,
            description: "规则的显示名称"
          }
        },
        specific_fields: {
          age_threshold: {
            chinese_name: "年龄阈值",
            data_type: "integer",
            required: false,
            description: "年龄阈值"
          }
        }
      },
      rule_type_mappings: {
        test_rule: {
          name: "测试规则"
        }
      }
    }
    
    fs.writeFileSync(testConfigPath, JSON.stringify(testConfig, null, 2))
    generator = new TypeScriptGenerator(testConfigPath)
  })

  afterEach(() => {
    // 清理测试文件
    if (fs.existsSync(testConfigPath)) {
      fs.unlinkSync(testConfigPath)
    }
    if (fs.existsSync(testOutputPath)) {
      fs.unlinkSync(testOutputPath)
    }
  })

  describe('配置加载', () => {
    it('应该成功加载配置文件', () => {
      expect(generator.config).toBeDefined()
      expect(generator.config.metadata.version).toBe('3.1.0')
    })

    it('应该正确解析字段定义', () => {
      const commonFields = generator.config.field_definitions.common_fields
      expect(commonFields.rule_id).toBeDefined()
      expect(commonFields.rule_name).toBeDefined()
    })
  })

  describe('类型生成', () => {
    it('应该生成正确的TypeScript类型', () => {
      const content = generator.generateTypes()
      
      // 检查基本结构
      expect(content).toContain('export interface CommonFields')
      expect(content).toContain('export interface SpecificFields')
      expect(content).toContain('export interface RuleDetail')
      
      // 检查字段定义
      expect(content).toContain('rule_id?: string')
      expect(content).toContain('rule_name: string')
      expect(content).toContain('age_threshold?: number')
      
      // 检查常量定义
      expect(content).toContain('export const FIELD_CHINESE_NAMES')
      expect(content).toContain('rule_id: \'规则ID\'')
      expect(content).toContain('rule_name: \'规则名称\'')
    })

    it('应该正确转换数据类型', () => {
      expect(generator.getTypeScriptType('string')).toBe('string')
      expect(generator.getTypeScriptType('integer')).toBe('number')
      expect(generator.getTypeScriptType('array')).toBe('string[]')
      expect(generator.getTypeScriptType('boolean')).toBe('boolean')
      expect(generator.getTypeScriptType('unknown')).toBe('any')
    })
  })

  describe('文件生成', () => {
    it('应该成功生成类型文件', () => {
      const outputPath = generator.saveToFile(testOutputPath)
      
      expect(fs.existsSync(outputPath)).toBe(true)
      
      const content = fs.readFileSync(outputPath, 'utf8')
      expect(content).toContain('// 自动生成的字段类型定义')
      expect(content).toContain('export interface CommonFields')
    })
  })

  describe('工具函数生成', () => {
    it('应该生成工具函数', () => {
      const content = generator.generateTypes()
      
      expect(content).toContain('export function getFieldChineseName')
      expect(content).toContain('export function getFieldDataType')
      expect(content).toContain('export function isFieldRequired')
    })
  })

  describe('错误处理', () => {
    it('应该处理配置文件不存在的情况', () => {
      expect(() => {
        new TypeScriptGenerator('/nonexistent/path.json')
      }).toThrow()
    })
  })
})
