/**
 * API缓存管理器
 * 实现基于版本的智能缓存策略，支持内存和持久化双层缓存
 */

import type { CacheConfig, CacheItem, CacheStats } from '../types/apiEnhanced'

/**
 * API缓存管理器类
 */
export class ApiCache {
  private memoryCache: Map<string, CacheItem> = new Map()
  private config: CacheConfig
  private stats: CacheStats = {
    totalItems: 0,
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    memoryUsage: 0
  }

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      ttl: 5 * 60 * 1000,           // 默认5分钟
      maxSize: 100,                 // 默认最大100个条目
      enablePersistence: true,      // 默认启用持久化
      versionBased: true,           // 默认基于版本缓存
      keyPrefix: 'api_cache_',      // 默认键前缀
      ...config
    }

    // 启动清理定时器
    this.startCleanupTimer()
  }

  /**
   * 生成缓存键
   * @param key 原始键
   * @returns 完整的缓存键
   */
  private generateCacheKey(key: string): string {
    return `${this.config.keyPrefix}${key}`
  }

  /**
   * 获取缓存数据
   * @param key 缓存键
   * @returns 缓存数据或null
   */
  get<T>(key: string): T | null {
    const cacheKey = this.generateCacheKey(key)
    const item = this.memoryCache.get(cacheKey)

    if (!item) {
      this.stats.missCount++
      this.updateHitRate()

      // 尝试从持久化存储获取
      if (this.config.enablePersistence) {
        return this.getFromPersistentCache<T>(cacheKey)
      }

      return null
    }

    // 检查是否过期
    if (this.isExpired(item)) {
      this.memoryCache.delete(cacheKey)
      this.stats.missCount++
      this.updateHitRate()

      // 从持久化存储中也删除
      if (this.config.enablePersistence) {
        this.removeFromPersistentCache(cacheKey)
      }

      return null
    }

    // 更新访问统计
    item.accessCount++
    item.lastAccessed = Date.now()

    this.stats.hitCount++
    this.updateHitRate()

    return item.data as T
  }

  /**
   * 设置缓存数据
   * @param key 缓存键
   * @param data 数据
   * @param version 版本号（可选）
   * @param customTtl 自定义TTL（可选）
   */
  set<T>(key: string, data: T, version?: string, customTtl?: number): void {
    const cacheKey = this.generateCacheKey(key)
    const ttl = customTtl || this.config.ttl
    const now = Date.now()

    const item: CacheItem<T> = {
      data,
      timestamp: now,
      version,
      ttl,
      accessCount: 1,
      lastAccessed: now
    }

    // 检查缓存大小限制
    if (this.memoryCache.size >= this.config.maxSize) {
      this.evictLeastRecentlyUsed()
    }

    this.memoryCache.set(cacheKey, item)
    this.stats.totalItems = this.memoryCache.size

    // 持久化存储
    if (this.config.enablePersistence) {
      this.saveToPersistentCache(cacheKey, item)
    }
  }

  /**
   * 检查缓存是否存在且未过期
   * @param key 缓存键
   * @param version 版本号（可选）
   * @returns 是否存在有效缓存
   */
  has(key: string, version?: string): boolean {
    const cacheKey = this.generateCacheKey(key)
    const item = this.memoryCache.get(cacheKey)

    if (!item || this.isExpired(item)) {
      return false
    }

    // 如果启用版本检查
    if (this.config.versionBased && version && item.version !== version) {
      return false
    }

    return true
  }

  /**
   * 删除缓存项
   * @param key 缓存键
   */
  delete(key: string): boolean {
    const cacheKey = this.generateCacheKey(key)
    const deleted = this.memoryCache.delete(cacheKey)

    if (deleted) {
      this.stats.totalItems = this.memoryCache.size
    }

    // 从持久化存储中删除
    if (this.config.enablePersistence) {
      this.removeFromPersistentCache(cacheKey)
    }

    return deleted
  }

  /**
   * 批量删除缓存（支持模式匹配）
   * @param pattern 匹配模式（支持通配符*）
   */
  invalidate(pattern: string): number {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    let deletedCount = 0

    for (const key of this.memoryCache.keys()) {
      if (regex.test(key)) {
        this.memoryCache.delete(key)
        deletedCount++

        // 从持久化存储中删除
        if (this.config.enablePersistence) {
          this.removeFromPersistentCache(key)
        }
      }
    }

    this.stats.totalItems = this.memoryCache.size
    return deletedCount
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.memoryCache.clear()
    this.stats.totalItems = 0
    this.stats.hitCount = 0
    this.stats.missCount = 0
    this.stats.hitRate = 0

    // 清空持久化存储
    if (this.config.enablePersistence) {
      this.clearPersistentCache()
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    this.updateMemoryUsage()
    return { ...this.stats }
  }

  /**
   * 检查缓存项是否过期
   * @param item 缓存项
   * @returns 是否过期
   */
  private isExpired(item: CacheItem): boolean {
    return Date.now() - item.timestamp > item.ttl
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    const total = this.stats.hitCount + this.stats.missCount
    this.stats.hitRate = total > 0 ? this.stats.hitCount / total : 0
  }

  /**
   * 更新内存使用情况
   */
  private updateMemoryUsage(): void {
    let totalSize = 0
    let oldestTimestamp = Date.now()
    let newestTimestamp = 0

    for (const item of this.memoryCache.values()) {
      // 估算内存使用（简化计算）
      totalSize += JSON.stringify(item.data).length

      if (item.timestamp < oldestTimestamp) {
        oldestTimestamp = item.timestamp
      }
      if (item.timestamp > newestTimestamp) {
        newestTimestamp = item.timestamp
      }
    }

    this.stats.memoryUsage = totalSize
    this.stats.oldestItem = oldestTimestamp
    this.stats.newestItem = newestTimestamp
  }

  /**
   * 淘汰最近最少使用的缓存项
   */
  private evictLeastRecentlyUsed(): void {
    let lruKey: string | null = null
    let lruTime = Date.now()

    for (const [key, item] of this.memoryCache.entries()) {
      if (item.lastAccessed < lruTime) {
        lruTime = item.lastAccessed
        lruKey = key
      }
    }

    if (lruKey) {
      this.memoryCache.delete(lruKey)

      // 从持久化存储中删除
      if (this.config.enablePersistence) {
        this.removeFromPersistentCache(lruKey)
      }
    }
  }

  /**
   * 从持久化存储获取数据
   * @param key 缓存键
   * @returns 缓存数据或null
   */
  private getFromPersistentCache<T>(key: string): T | null {
    try {
      const stored = localStorage.getItem(key)
      if (!stored) return null

      const item: CacheItem<T> = JSON.parse(stored)

      // 检查是否过期
      if (this.isExpired(item)) {
        localStorage.removeItem(key)
        return null
      }

      // 恢复到内存缓存
      this.memoryCache.set(key, item)
      this.stats.totalItems = this.memoryCache.size

      return item.data
    } catch (error) {
      console.warn('Failed to get from persistent cache:', error)
      return null
    }
  }

  /**
   * 保存到持久化存储
   * @param key 缓存键
   * @param item 缓存项
   */
  private saveToPersistentCache(key: string, item: CacheItem): void {
    try {
      localStorage.setItem(key, JSON.stringify(item))
    } catch (error) {
      console.warn('Failed to save to persistent cache:', error)
    }
  }

  /**
   * 从持久化存储删除
   * @param key 缓存键
   */
  private removeFromPersistentCache(key: string): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('Failed to remove from persistent cache:', error)
    }
  }

  /**
   * 清空持久化存储
   */
  private clearPersistentCache(): void {
    try {
      const keys = Object.keys(localStorage)
      for (const key of keys) {
        if (key.startsWith(this.config.keyPrefix)) {
          localStorage.removeItem(key)
        }
      }
    } catch (error) {
      console.warn('Failed to clear persistent cache:', error)
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanupExpiredItems()
    }, 5 * 60 * 1000)
  }

  /**
   * 清理过期的缓存项
   */
  private cleanupExpiredItems(): void {
    const expiredKeys: string[] = []

    for (const [key, item] of this.memoryCache.entries()) {
      if (this.isExpired(item)) {
        expiredKeys.push(key)
      }
    }

    for (const key of expiredKeys) {
      this.memoryCache.delete(key)

      // 从持久化存储中删除
      if (this.config.enablePersistence) {
        this.removeFromPersistentCache(key)
      }
    }

    if (expiredKeys.length > 0) {
      this.stats.totalItems = this.memoryCache.size
      console.log(`Cleaned up ${expiredKeys.length} expired cache items`)
    }
  }
}

// 创建默认缓存实例
export const defaultApiCache = new ApiCache()

// 导出类型和实例
export default ApiCache
