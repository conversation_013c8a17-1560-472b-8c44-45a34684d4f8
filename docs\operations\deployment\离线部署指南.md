# 离线部署指南

本文档介绍如何在完全封闭的内网环境中部署和运行规则验证系统的子节点。

## 概述

规则验证系统支持两种部署模式：

1. **在线模式**：子节点自动从主节点同步规则数据
2. **离线模式**：子节点仅依赖本地规则文件运行，适用于完全封闭的内网环境

## 离线模式配置

### 1. 环境变量配置

在子节点的配置文件或环境变量中设置：

```bash
# 禁用规则同步功能
ENABLE_RULE_SYNC=false

# 其他必要配置
MODE=slave
LOG_LEVEL=INFO
```

### 2. 配置文件示例

创建 `.env` 文件：

```env
# 应用模式
MODE=slave

# 禁用规则同步（离线模式）
ENABLE_RULE_SYNC=false

# 日志配置
LOG_LEVEL=INFO
LOG_STDOUT_ENABLED=true

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=18002

# 性能配置
WORKER_COUNT=8
QUEUE_MAX_SIZE=1000
```

## 规则文件准备

### 1. 从主节点导出规则文件

在有网络连接的环境中，使用规则文件管理工具从主节点导出规则：

```bash
# 方法1：使用管理工具（推荐）
python tools/rule_file_manager.py export -o rules_cache.json.gz

# 方法2：通过API导出
curl -H "X-API-KEY: your_api_key" \
     http://master-node:18001/api/v1/rules/export \
     -o rules_cache.json.gz
```

### 2. 验证规则文件

在部署前验证规则文件的完整性：

```bash
python tools/rule_file_manager.py validate rules_cache.json.gz
```

输出示例：
```
Validation result for rules_cache.json.gz:
Valid: True

File Information:
  total_count: 150
  version: 38910e1b...
  export_timestamp: 2024-01-15T10:30:00
  file_size: 2048576
  compression: gzip
  valid_datasets: 150
```

### 3. 文件格式说明

规则文件采用gzip压缩的JSON格式，包含以下结构：

```json
{
  "version": "规则版本哈希",
  "rule_datasets": [
    {
      "id": 1,
      "base_rule_id": 1,
      "data_set": [...],
      "version": 1,
      "is_active": true,
      "base_rule": {
        "rule_key": "drug_limit_male",
        "rule_name": "男性用药限制",
        "module_path": "rules.base_rules.drug_limit_male"
      }
    }
  ],
  "export_timestamp": "2024-01-15T10:30:00",
  "total_count": 150
}
```

## 部署步骤

### 1. 准备部署环境

```bash
# 创建工作目录
mkdir -p /opt/rule_slave
cd /opt/rule_slave

# 复制应用文件
cp -r /path/to/source/* .

# 复制规则文件到工作目录
cp rules_cache.json.gz .

# 设置权限
chmod +x tools/rule_file_manager.py
```

### 2. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 或使用conda
conda env create -f environment.yml
```

### 3. 配置应用

创建配置文件 `.env`：

```env
MODE=slave
ENABLE_RULE_SYNC=false
SERVER_HOST=0.0.0.0
SERVER_PORT=18002
LOG_LEVEL=INFO
```

### 4. 启动服务

```bash
# 直接启动
python slave.py

# 或使用uvicorn
uvicorn slave:app --host 0.0.0.0 --port 18002

# 使用systemd服务（推荐生产环境）
sudo systemctl start rule-slave
```

### 5. 验证部署

```bash
# 检查服务状态
curl http://localhost:18002/health

# 测试规则验证
curl -X POST http://localhost:18002/api/v1/validate \
     -H "Content-Type: application/json" \
     -d '{"patientInfo": {...}, "ids": ["rule_id_1"]}'
```

## 规则更新

### 1. 手动更新规则

当需要更新规则时：

```bash
# 1. 获取新的规则文件（从主节点导出）
# 2. 验证新规则文件
python tools/rule_file_manager.py validate new_rules_cache.json.gz

# 3. 导入新规则文件
python tools/rule_file_manager.py import new_rules_cache.json.gz

# 4. 重启服务
sudo systemctl restart rule-slave
```

### 2. 批量部署更新

对于多个子节点的批量更新：

```bash
#!/bin/bash
# update_rules.sh

RULE_FILE="rules_cache.json.gz"
NODES=("node1" "node2" "node3")

for node in "${NODES[@]}"; do
    echo "Updating rules on $node..."
    
    # 复制规则文件
    scp $RULE_FILE $node:/opt/rule_slave/
    
    # 远程导入并重启
    ssh $node "cd /opt/rule_slave && \
               python tools/rule_file_manager.py import $RULE_FILE && \
               sudo systemctl restart rule-slave"
    
    echo "Update completed on $node"
done
```

## 监控和维护

### 1. 日志监控

```bash
# 查看应用日志
tail -f logs/app.log

# 查看系统服务日志
journalctl -u rule-slave -f
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:18002/health

# 检查规则加载状态
curl http://localhost:18002/api/v1/status
```

### 3. 性能监控

监控关键指标：
- CPU使用率
- 内存使用率
- 请求响应时间
- 规则验证成功率

## 故障排除

### 1. 常见问题

**问题：服务启动失败，提示"No local rules found"**

解决方案：
```bash
# 检查规则文件是否存在
ls -la rules_cache.json.gz

# 验证规则文件格式
python tools/rule_file_manager.py validate rules_cache.json.gz

# 重新导入规则文件
python tools/rule_file_manager.py import rules_cache.json.gz
```

**问题：规则验证返回错误**

解决方案：
```bash
# 检查规则文件版本
python tools/rule_file_manager.py info rules_cache.json.gz

# 检查应用日志
tail -f logs/app.log

# 重启服务
sudo systemctl restart rule-slave
```

### 2. 调试模式

启用调试模式获取更详细的日志：

```env
LOG_LEVEL=DEBUG
LOG_STDOUT_ENABLED=true
```

## 安全考虑

### 1. 文件权限

```bash
# 设置适当的文件权限
chmod 600 .env
chmod 644 rules_cache.json.gz
chmod 755 tools/rule_file_manager.py
```

### 2. 网络安全

- 确保只有必要的端口对外开放
- 使用防火墙限制访问
- 定期更新系统和依赖包

### 3. 数据完整性

- 定期验证规则文件完整性
- 备份重要配置文件
- 监控文件变化

## 性能优化

### 1. 系统配置

```bash
# 调整系统参数
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
echo 'fs.file-max = 65536' >> /etc/sysctl.conf
sysctl -p
```

### 2. 应用配置

```env
# 优化工作进程数
WORKER_COUNT=16

# 调整队列大小
QUEUE_MAX_SIZE=2000

# 启用性能监控
PERFORMANCE_MONITORING_ENABLED=true
```

## 总结

离线部署模式为完全封闭的内网环境提供了可靠的规则验证服务。通过合理的配置和维护，可以确保系统在离线环境中稳定运行。

关键要点：
1. 正确配置 `ENABLE_RULE_SYNC=false`
2. 确保规则文件格式正确且完整
3. 建立规则更新的标准流程
4. 实施适当的监控和维护措施
