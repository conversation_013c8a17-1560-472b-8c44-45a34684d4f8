"""
业务状态码标准化定义
定义系统中所有业务状态码，确保主从节点使用一致的错误码体系
"""

from enum import IntEnum


class ErrorCodes(IntEnum):
    """
    业务状态码枚举
    使用HTTP状态码作为基础，扩展业务特定的错误码
    """

    # ===== 成功状态 =====
    SUCCESS = 200

    # ===== 客户端错误 (4xx) =====
    BAD_REQUEST = 400                    # 请求参数错误
    UNAUTHORIZED = 401                   # 未授权访问
    FORBIDDEN = 403                      # 禁止访问
    NOT_FOUND = 404                      # 资源不存在
    METHOD_NOT_ALLOWED = 405             # 请求方法不允许
    REQUEST_TIMEOUT = 408                # 请求超时
    CONFLICT = 409                       # 资源冲突
    VALIDATION_ERROR = 422               # 请求数据验证失败
    TOO_MANY_REQUESTS = 429              # 请求过于频繁

    # ===== 服务器错误 (5xx) =====
    INTERNAL_ERROR = 500                 # 服务器内部错误
    NOT_IMPLEMENTED = 501                # 功能未实现
    BAD_GATEWAY = 502                    # 网关错误
    SERVICE_UNAVAILABLE = 503            # 服务不可用
    GATEWAY_TIMEOUT = 504                # 网关超时

    # ===== 业务错误 (6xx) =====
    RULE_VALIDATION_FAILED = 600         # 规则验证失败
    RULE_SYNC_FAILED = 601               # 规则同步失败
    RULE_NOT_FOUND = 602                 # 规则不存在
    RULE_EXECUTION_ERROR = 603           # 规则执行错误
    RULE_CACHE_ERROR = 604               # 规则缓存错误

    # 规则明细相关错误码 (605-619)
    RULE_DETAIL_NOT_FOUND = 605                      # 规则明细不存在
    RULE_DETAIL_ALREADY_EXISTS = 606                 # 规则明细已存在
    RULE_DETAIL_VALIDATION_FAILED = 607              # 规则明细验证失败
    RULE_DETAIL_BATCH_OPERATION_FAILED = 608         # 规则明细批量操作失败
    RULE_DETAIL_INCREMENTAL_OPERATION_FAILED = 609   # 规则明细增量操作失败

    # 兼容性相关错误码 (620-629)
    FORMAT_CONVERSION_FAILED = 620       # 格式转换失败
    LEGACY_FORMAT_NOT_SUPPORTED = 621    # 传统格式不支持
    MIGRATION_IN_PROGRESS = 622          # 迁移进行中
    MIGRATION_FAILED = 623               # 迁移失败
    DATA_INCONSISTENCY_DETECTED = 624    # 数据不一致检测

    # ===== 数据错误 (7xx) =====
    DATA_VALIDATION_ERROR = 700          # 数据验证错误
    DATA_FORMAT_ERROR = 701              # 数据格式错误
    DATA_MISSING_ERROR = 702             # 数据缺失错误
    DATA_DUPLICATE_ERROR = 703           # 数据重复错误
    DATA_PROCESSING_ERROR = 704          # 数据处理错误

    # ===== 系统错误 (8xx) =====
    DATABASE_ERROR = 800                 # 数据库错误
    CACHE_ERROR = 801                    # 缓存错误
    NETWORK_ERROR = 802                  # 网络错误
    FILE_SYSTEM_ERROR = 803              # 文件系统错误
    MEMORY_ERROR = 804                   # 内存错误
    CPU_ERROR = 805                      # CPU错误

    # ===== 认证授权错误 (9xx) =====
    API_KEY_MISSING = 900                # API密钥缺失
    API_KEY_INVALID = 901                # API密钥无效
    API_KEY_EXPIRED = 902                # API密钥过期
    PERMISSION_DENIED = 903              # 权限不足
    SESSION_EXPIRED = 904                # 会话过期


class ErrorCodeHelper:
    """错误码辅助工具类"""

    @staticmethod
    def is_success(code: int) -> bool:
        """判断是否为成功状态码"""
        return code == ErrorCodes.SUCCESS

    @staticmethod
    def is_client_error(code: int) -> bool:
        """判断是否为客户端错误"""
        return 400 <= code < 500

    @staticmethod
    def is_server_error(code: int) -> bool:
        """判断是否为服务器错误"""
        return 500 <= code < 600

    @staticmethod
    def is_business_error(code: int) -> bool:
        """判断是否为业务错误"""
        return 600 <= code < 700

    @staticmethod
    def is_data_error(code: int) -> bool:
        """判断是否为数据错误"""
        return 700 <= code < 800

    @staticmethod
    def is_system_error(code: int) -> bool:
        """判断是否为系统错误"""
        return 800 <= code < 900

    @staticmethod
    def is_auth_error(code: int) -> bool:
        """判断是否为认证授权错误"""
        return 900 <= code < 1000

    @staticmethod
    def get_error_category(code: int) -> str:
        """获取错误类别"""
        if ErrorCodeHelper.is_success(code):
            return "success"
        elif ErrorCodeHelper.is_client_error(code):
            return "client_error"
        elif ErrorCodeHelper.is_server_error(code):
            return "server_error"
        elif ErrorCodeHelper.is_business_error(code):
            return "business_error"
        elif ErrorCodeHelper.is_data_error(code):
            return "data_error"
        elif ErrorCodeHelper.is_system_error(code):
            return "system_error"
        elif ErrorCodeHelper.is_auth_error(code):
            return "auth_error"
        else:
            return "unknown"

    @staticmethod
    def get_error_severity(code: int) -> str:
        """获取错误严重程度"""
        if ErrorCodeHelper.is_success(code):
            return "info"
        elif ErrorCodeHelper.is_client_error(code):
            return "warning"
        elif ErrorCodeHelper.is_server_error(code) or ErrorCodeHelper.is_system_error(code):
            return "error"
        elif ErrorCodeHelper.is_business_error(code) or ErrorCodeHelper.is_data_error(code):
            return "warning"
        elif ErrorCodeHelper.is_auth_error(code):
            return "warning"
        else:
            return "error"


# 错误码到HTTP状态码的映射（用于特殊情况）
ERROR_CODE_TO_HTTP_STATUS: dict[int, int] = {
    ErrorCodes.SUCCESS: 200,
    ErrorCodes.BAD_REQUEST: 400,
    ErrorCodes.UNAUTHORIZED: 401,
    ErrorCodes.FORBIDDEN: 403,
    ErrorCodes.NOT_FOUND: 404,
    ErrorCodes.METHOD_NOT_ALLOWED: 405,
    ErrorCodes.REQUEST_TIMEOUT: 408,
    ErrorCodes.CONFLICT: 409,
    ErrorCodes.VALIDATION_ERROR: 422,
    ErrorCodes.TOO_MANY_REQUESTS: 429,
    ErrorCodes.INTERNAL_ERROR: 500,
    ErrorCodes.NOT_IMPLEMENTED: 501,
    ErrorCodes.BAD_GATEWAY: 502,
    ErrorCodes.SERVICE_UNAVAILABLE: 503,
    ErrorCodes.GATEWAY_TIMEOUT: 504,
}


def get_http_status_for_error_code(error_code: int) -> int:
    """
    根据业务错误码获取对应的HTTP状态码

    Args:
        error_code: 业务错误码

    Returns:
        int: 对应的HTTP状态码，默认返回200（统一响应策略）
    """
    # 在统一响应策略下，所有响应都使用HTTP 200
    # 这个函数保留用于特殊情况或未来可能的策略调整
    return ERROR_CODE_TO_HTTP_STATUS.get(error_code, 200)
