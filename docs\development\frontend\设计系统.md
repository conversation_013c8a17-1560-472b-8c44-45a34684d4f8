# 设计系统文档

## 概述

本设计系统基于Vue 3 + Element Plus构建，提供统一的设计语言和组件库，确保整个应用的UI一致性和开发效率。

## 设计原则

### 1. 一致性 (Consistency)
- 统一的视觉语言和交互模式
- 标准化的组件API设计
- 一致的命名规范和代码风格

### 2. 可访问性 (Accessibility)
- 符合WCAG 2.1 AA标准
- 支持键盘导航和屏幕阅读器
- 合理的颜色对比度和字体大小

### 3. 可扩展性 (Scalability)
- 模块化的组件设计
- 灵活的主题定制能力
- 响应式设计支持

### 4. 性能优化 (Performance)
- 按需加载和代码分割
- 优化的渲染性能
- 最小化的包体积

## 设计Token

### 颜色系统

#### 基础调色板
```javascript
// 主色调 - 蓝色系
blue: {
  50: '#eff6ff',
  500: '#3b82f6', // 主色
  900: '#1e3a8a'
}

// 语义化颜色
semantic: {
  primary: { main: '#3b82f6' },
  success: { main: '#22c55e' },
  warning: { main: '#f97316' },
  error: { main: '#ef4444' }
}
```

#### 状态颜色
- **NEW**: 蓝色 (#3b82f6) - 新规则
- **CHANGED**: 橙色 (#f97316) - 逻辑变更
- **READY**: 绿色 (#22c55e) - 就绪
- **DEPRECATED**: 红色 (#ef4444) - 已弃用

### 字体系统

#### 字体族
```css
/* 主要字体 */
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;

/* 等宽字体 */
font-family: "Fira Code", "JetBrains Mono", "Source Code Pro", monospace;
```

#### 字体大小
- **xs**: 0.75rem (12px)
- **sm**: 0.875rem (14px)
- **base**: 1rem (16px)
- **lg**: 1.125rem (18px)
- **xl**: 1.25rem (20px)

### 间距系统

基于8px网格系统：
- **xs**: 4px
- **sm**: 8px
- **md**: 12px
- **lg**: 16px
- **xl**: 24px

### 阴影系统

```css
/* 基础阴影 */
sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
base: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'

/* 悬浮阴影 */
hover: '0 6px 12px -2px rgba(0, 0, 0, 0.15)'
```

## 基础组件

### RButton 按钮组件

#### 基本用法
```vue
<template>
  <RButton variant="primary" size="md" @click="handleClick">
    点击按钮
  </RButton>
</template>
```

#### Props
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| variant | String | 'primary' | 按钮类型：primary/secondary/success/warning/error |
| size | String | 'md' | 按钮尺寸：xs/sm/md/lg/xl |
| icon | String/Object | null | 图标 |
| loading | Boolean | false | 加载状态 |
| disabled | Boolean | false | 禁用状态 |

#### 变体示例
```vue
<!-- 主要按钮 -->
<RButton variant="primary">主要按钮</RButton>

<!-- 次要按钮 -->
<RButton variant="secondary">次要按钮</RButton>

<!-- 成功按钮 -->
<RButton variant="success">成功按钮</RButton>

<!-- 带图标按钮 -->
<RButton variant="primary" :icon="Plus">添加</RButton>

<!-- 加载状态 -->
<RButton variant="primary" :loading="true">加载中</RButton>
```

### RCard 卡片组件

#### 基本用法
```vue
<template>
  <RCard title="卡片标题" size="md" variant="outlined">
    <template #header>
      <h3>自定义头部</h3>
    </template>
    
    <p>卡片内容</p>
    
    <template #footer>
      <RButton variant="primary">操作</RButton>
    </template>
  </RCard>
</template>
```

#### Props
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | '' | 卡片标题 |
| variant | String | 'default' | 卡片变体：default/outlined/elevated/filled |
| size | String | 'md' | 卡片尺寸：xs/sm/md/lg/xl |
| hoverable | Boolean | false | 是否可悬浮 |
| clickable | Boolean | false | 是否可点击 |

### RTable 表格组件

#### 基本用法
```vue
<template>
  <RTable 
    :data="tableData" 
    :columns="columns"
    size="md"
    :bordered="true"
    :striped="true"
    :show-pagination="true"
    @row-click="handleRowClick"
  />
</template>

<script setup>
const columns = [
  { key: 'name', title: '名称', sortable: true },
  { key: 'status', title: '状态', slot: 'status' },
  { key: 'date', title: '日期', formatter: formatDate }
]
</script>
```

### RModal 模态框组件

#### 基本用法
```vue
<template>
  <RModal 
    v-model:visible="visible"
    title="模态框标题"
    size="md"
    :show-default-footer="true"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <p>模态框内容</p>
  </RModal>
</template>
```

## 组件开发规范

### 1. 命名规范

#### 组件命名
- 基础组件：以 `R` 前缀开头，如 `RButton`、`RCard`
- 业务组件：使用描述性名称，如 `RuleStatusCard`、`DataUploader`

#### Props命名
- 使用camelCase命名
- 布尔类型props使用is/has/can/should前缀
- 事件处理器使用on前缀

#### 事件命名
- 使用kebab-case命名
- 使用动词描述事件，如 `click`、`change`、`submit`

### 2. API设计规范

#### Props设计
```javascript
// 好的Props设计
const props = defineProps({
  // 明确的类型定义
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary'].includes(value)
  },
  
  // 合理的默认值
  size: {
    type: String,
    default: 'md'
  },
  
  // 清晰的描述
  disabled: {
    type: Boolean,
    default: false
  }
})
```

#### 事件设计
```javascript
// 统一的事件命名和参数
const emit = defineEmits([
  'click',        // (event) => void
  'change',       // (value, oldValue) => void
  'update:value'  // (value) => void - v-model支持
])
```

### 3. 样式规范

#### CSS类命名
使用BEM命名规范：
```css
/* 块 */
.r-button { }

/* 元素 */
.r-button__icon { }
.r-button__content { }

/* 修饰符 */
.r-button--primary { }
.r-button--disabled { }
```

#### 响应式设计
```css
/* 移动端优先 */
.component {
  /* 基础样式 */
}

@media (min-width: 640px) {
  .component {
    /* 平板样式 */
  }
}

@media (min-width: 1024px) {
  .component {
    /* 桌面样式 */
  }
}
```

### 4. 文档规范

#### 组件文档结构
```markdown
# 组件名称

## 概述
组件的简要描述和用途

## 基本用法
基础使用示例

## API
### Props
### Events
### Slots

## 示例
### 基础示例
### 高级示例

## 注意事项
使用时的注意事项和最佳实践
```

## 最佳实践

### 1. 组件设计
- 保持组件的单一职责
- 提供合理的默认值
- 支持插槽自定义
- 考虑可访问性

### 2. 性能优化
- 使用v-memo优化列表渲染
- 合理使用computed和watch
- 避免不必要的响应式数据

### 3. 类型安全
- 使用TypeScript定义Props类型
- 提供完整的类型定义文件
- 使用泛型支持灵活的数据类型

### 4. 测试
- 编写单元测试覆盖主要功能
- 测试不同的Props组合
- 测试事件触发和响应

## 更新日志

### v1.0.0 (2025-06-30)
- 初始版本发布
- 完成基础组件库：RButton、RCard、RTable、RModal
- 建立完整的设计Token系统
- 重构业务组件使用新设计系统
