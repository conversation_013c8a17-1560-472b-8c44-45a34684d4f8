"""
高级索引引擎测试模块
测试多类型代码索引实现的各种索引类型和自适应选择机制

测试覆盖：
1. 各种索引类型的基本功能
2. 索引性能和内存使用
3. 自适应索引选择算法
4. 混合查询场景
5. 异常处理和边界条件
"""

import time
import unittest
from unittest.mock import patch

from core.advanced_index_engine import (
    AdaptiveIndexSelector,
    BPlusTreeIndex,
    CompressedTrieIndex,
    HashIndex,
    InvertedIndex,
    MultiTypeIndexEngine,
    advanced_index_engine,
)


class TestHashIndex(unittest.TestCase):
    """哈希索引测试"""
    
    def setUp(self):
        self.index = HashIndex("test_hash")
        self.test_data = {
            'A001': {'rule1', 'rule2'},
            'B002': {'rule2', 'rule3'},
            'C003': {'rule1', 'rule3'},
        }
    
    def test_hash_index_build_and_query(self):
        """测试哈希索引构建和查询"""
        # 构建索引
        self.index.build(self.test_data)
        
        # 精确查询
        result = self.index.query('A001')
        self.assertEqual(result, {'rule1', 'rule2'})
        
        result = self.index.query('B002')
        self.assertEqual(result, {'rule2', 'rule3'})
        
        # 查询不存在的键
        result = self.index.query('D004')
        self.assertEqual(result, set())
    
    def test_hash_index_memory_usage(self):
        """测试内存使用计算"""
        self.index.build(self.test_data)
        memory_usage = self.index.get_memory_usage()
        
        self.assertGreater(memory_usage, 0)
        self.assertIsInstance(memory_usage, float)
    
    def test_hash_index_stats(self):
        """测试统计信息"""
        self.index.build(self.test_data)
        
        # 执行一些查询
        self.index.query('A001')
        self.index.query('B002')
        self.index.query('NOTFOUND')
        
        stats = self.index.get_stats()
        
        self.assertEqual(stats.index_type, 'HashIndex')
        self.assertEqual(stats.total_keys, 3)
        self.assertEqual(stats.total_values, 6)  # rule1出现2次，rule2出现2次，rule3出现2次
        self.assertGreater(stats.memory_usage_mb, 0)
        self.assertGreater(stats.avg_query_time_us, 0)
        
        # 命中率应该是 2/3 ≈ 0.67
        self.assertAlmostEqual(stats.hit_rate, 2/3, places=2)
    
    def test_hash_index_does_not_support_prefix(self):
        """测试哈希索引不支持前缀查询"""
        self.assertFalse(self.index.supports_prefix_query())


class TestBPlusTreeIndex(unittest.TestCase):
    """B+树索引测试"""
    
    def setUp(self):
        self.index = BPlusTreeIndex("test_btree", order=4)
        self.test_data = {
            'A001': {'rule1', 'rule2'},
            'A002': {'rule1', 'rule3'},
            'B001': {'rule2', 'rule3'},
            'B002': {'rule3', 'rule4'},
            'C001': {'rule1', 'rule4'},
        }
    
    def test_btree_build_and_query(self):
        """测试B+树构建和查询"""
        self.index.build(self.test_data)
        
        # 精确查询
        result = self.index.query('A001')
        self.assertEqual(result, {'rule1', 'rule2'})
        
        result = self.index.query('B002')
        self.assertEqual(result, {'rule3', 'rule4'})
        
        # 查询不存在的键
        result = self.index.query('D001')
        self.assertEqual(result, set())
    
    def test_btree_range_query(self):
        """测试范围查询"""
        self.index.build(self.test_data)
        
        # 范围查询 A001 to B001 (包含)
        result = self.index.range_query('A001', 'B001')
        expected = {'rule1', 'rule2', 'rule3'}  # A001, A002, B001的并集
        self.assertEqual(result, expected)
        
        # 单个键的范围查询
        result = self.index.range_query('A001', 'A001')
        self.assertEqual(result, {'rule1', 'rule2'})
    
    def test_btree_prefix_query(self):
        """测试前缀查询"""
        self.index.build(self.test_data)
        
        # 前缀查询 'A'
        result = self.index.prefix_query('A')
        expected = {'rule1', 'rule2', 'rule3'}  # A001和A002的并集
        self.assertEqual(result, expected)
        
        # 前缀查询 'B00'
        result = self.index.prefix_query('B00')
        expected = {'rule2', 'rule3', 'rule4'}  # B001和B002的并集
        self.assertEqual(result, expected)
        
        # 查询不存在的前缀
        result = self.index.prefix_query('X')
        self.assertEqual(result, set())
    
    def test_btree_supports_prefix(self):
        """测试B+树支持前缀查询"""
        self.assertTrue(self.index.supports_prefix_query())


class TestInvertedIndex(unittest.TestCase):
    """倒排索引测试"""
    
    def setUp(self):
        self.index = InvertedIndex("test_inverted")
        self.test_data = {
            'A001': {'rule1', 'rule2'},
            'A002': {'rule2', 'rule3'},
            'B001': {'rule1', 'rule3'},
        }
    
    def test_inverted_index_build_and_query(self):
        """测试倒排索引构建和正向查询"""
        self.index.build(self.test_data)
        
        # 正向查询
        result = self.index.query('A001')
        self.assertEqual(result, {'rule1', 'rule2'})
        
        result = self.index.query('B001')
        self.assertEqual(result, {'rule1', 'rule3'})
    
    def test_inverted_index_reverse_query(self):
        """测试倒排查询"""
        self.index.build(self.test_data)
        
        # 倒排查询 - 根据值查找键
        result = self.index.reverse_query('rule1')
        self.assertEqual(result, {'A001', 'B001'})
        
        result = self.index.reverse_query('rule2')
        self.assertEqual(result, {'A001', 'A002'})
        
        result = self.index.reverse_query('rule3')
        self.assertEqual(result, {'A002', 'B001'})
        
        # 查询不存在的值
        result = self.index.reverse_query('rule4')
        self.assertEqual(result, set())
    
    def test_inverted_index_multi_value_query(self):
        """测试多值查询"""
        self.index.build(self.test_data)
        
        # OR查询
        result = self.index.multi_value_query(['rule1', 'rule2'], 'OR')
        expected = {'A001', 'A002', 'B001'}  # 所有包含rule1或rule2的键
        self.assertEqual(result, expected)
        
        # AND查询
        result = self.index.multi_value_query(['rule1', 'rule2'], 'AND')
        expected = {'A001'}  # 同时包含rule1和rule2的键
        self.assertEqual(result, expected)
        
        # 空值查询
        result = self.index.multi_value_query([], 'OR')
        self.assertEqual(result, set())
    
    def test_inverted_index_invalid_operation(self):
        """测试无效操作"""
        self.index.build(self.test_data)
        
        with self.assertRaises(ValueError):
            self.index.multi_value_query(['rule1'], 'INVALID')


class TestCompressedTrieIndex(unittest.TestCase):
    """压缩Trie索引测试"""
    
    def setUp(self):
        self.index = CompressedTrieIndex("test_trie")
        self.test_data = {
            'apple': {'rule1', 'rule2'},
            'application': {'rule2', 'rule3'},
            'apply': {'rule1', 'rule3'},
            'banana': {'rule4'},
            'band': {'rule4', 'rule5'},
        }
    
    def test_trie_build_and_query(self):
        """测试Trie索引构建和精确查询"""
        self.index.build(self.test_data)
        
        # 精确查询
        result = self.index.query('apple')
        self.assertEqual(result, {'rule1', 'rule2'})
        
        result = self.index.query('application')
        self.assertEqual(result, {'rule2', 'rule3'})
        
        result = self.index.query('banana')
        self.assertEqual(result, {'rule4'})
        
        # 查询不存在的键
        result = self.index.query('app')  # 前缀存在但不是完整键
        self.assertEqual(result, set())
        
        result = self.index.query('orange')
        self.assertEqual(result, set())
    
    def test_trie_prefix_query(self):
        """测试前缀查询"""
        self.index.build(self.test_data)
        
        # 前缀查询 'app'
        result = self.index.prefix_query('app')
        expected = {'rule1', 'rule2', 'rule3'}  # apple, application, apply的并集
        self.assertEqual(result, expected)
        
        # 前缀查询 'ban'
        result = self.index.prefix_query('ban')
        expected = {'rule4', 'rule5'}  # banana, band的并集
        self.assertEqual(result, expected)
        
        # 精确匹配作为前缀
        result = self.index.prefix_query('apple')
        expected = {'rule1', 'rule2'}  # 只有apple本身
        self.assertEqual(result, expected)
        
        # 查询不存在的前缀
        result = self.index.prefix_query('xyz')
        self.assertEqual(result, set())
    
    def test_trie_supports_prefix(self):
        """测试Trie支持前缀查询"""
        self.assertTrue(self.index.supports_prefix_query())
    
    def test_trie_compression(self):
        """测试路径压缩效果"""
        # 创建具有长公共前缀的数据
        long_prefix_data = {
            'verylongprefix1': {'rule1'},
            'verylongprefix2': {'rule2'},
            'verylongprefix3': {'rule3'},
        }
        
        self.index.build(long_prefix_data)
        
        # 验证功能正常
        result = self.index.query('verylongprefix1')
        self.assertEqual(result, {'rule1'})
        
        result = self.index.prefix_query('verylong')
        self.assertEqual(result, {'rule1', 'rule2', 'rule3'})


class TestAdaptiveIndexSelector(unittest.TestCase):
    """自适应索引选择器测试"""
    
    def setUp(self):
        self.selector = AdaptiveIndexSelector()
    
    def test_select_hash_index_for_exact_queries(self):
        """测试精确查询场景选择哈希索引"""
        data = {
            'A001': {'rule1'},
            'B002': {'rule2'},
            'C003': {'rule3'},
        }
        
        # 主要是精确查询
        query_patterns = {'exact': 0.9, 'prefix': 0.1}
        
        index = self.selector.select_optimal_index(data, query_patterns)
        
        self.assertIsInstance(index, HashIndex)
    
    def test_select_trie_index_for_prefix_queries(self):
        """测试前缀查询场景选择Trie索引"""
        # 具有高前缀重叠的数据
        data = {
            'prefix001': {'rule1'},
            'prefix002': {'rule2'},
            'prefix003': {'rule3'},
            'prefix004': {'rule4'},
        }
        
        # 主要是前缀查询
        query_patterns = {'exact': 0.3, 'prefix': 0.7}
        
        index = self.selector.select_optimal_index(data, query_patterns)
        
        self.assertIsInstance(index, CompressedTrieIndex)
    
    def test_select_btree_index_for_range_queries(self):
        """测试范围查询场景选择B+树索引"""
        data = {
            'A001': {'rule1'},
            'A002': {'rule2'},
            'B001': {'rule3'},
            'B002': {'rule4'},
        }
        
        # 需要范围查询
        query_patterns = {'exact': 0.4, 'prefix': 0.3, 'range': 0.3}
        
        index = self.selector.select_optimal_index(data, query_patterns)
        
        self.assertIsInstance(index, BPlusTreeIndex)
    
    def test_select_inverted_index_for_multivalue_queries(self):
        """测试多值查询场景选择倒排索引"""
        data = {
            'A001': {'rule1', 'rule2', 'rule3'},
            'A002': {'rule2', 'rule3', 'rule4'},
            'B001': {'rule1', 'rule4', 'rule5'},
        }
        
        # 多值查询为主
        query_patterns = {'exact': 0.3, 'multivalue': 0.7}
        
        index = self.selector.select_optimal_index(data, query_patterns)
        
        self.assertIsInstance(index, InvertedIndex)
    
    def test_memory_limit_consideration(self):
        """测试内存限制考虑"""
        # 大量数据
        data = {f'key{i:04d}': {f'rule{i}'} for i in range(1000)}
        
        query_patterns = {'exact': 0.8, 'prefix': 0.2}
        
        # 严格的内存限制
        index = self.selector.select_optimal_index(data, query_patterns, memory_limit_mb=1.0)
        
        # 应该选择内存效率更高的索引
        self.assertIsInstance(index, (CompressedTrieIndex, InvertedIndex))
    
    def test_analyze_data_features(self):
        """测试数据特征分析"""
        data = {
            'abc123': {'rule1', 'rule2'},
            'abc456': {'rule2', 'rule3'},
            'xyz789': {'rule1'},
        }
        
        features = self.selector._analyze_data_features(data)
        
        self.assertEqual(features['key_count'], 3)
        self.assertEqual(features['avg_key_length'], 6)
        self.assertEqual(features['total_values'], 5)
        self.assertGreater(features['prefix_overlap_ratio'], 0)  # abc有公共前缀
    
    def test_empty_data_handling(self):
        """测试空数据处理"""
        data = {}
        query_patterns = {'exact': 1.0}
        
        index = self.selector.select_optimal_index(data, query_patterns)
        
        self.assertIsInstance(index, HashIndex)  # 默认选择哈希索引


class TestMultiTypeIndexEngine(unittest.TestCase):
    """多类型索引引擎测试"""
    
    def setUp(self):
        self.engine = MultiTypeIndexEngine()
        self.test_data = {
            'A001': {'rule1', 'rule2'},
            'A002': {'rule2', 'rule3'},
            'B001': {'rule1', 'rule3'},
        }
    
    def tearDown(self):
        self.engine.clear_all_indexes()
    
    def test_create_specific_index_type(self):
        """测试创建指定类型的索引"""
        # 创建哈希索引
        index = self.engine.create_index('test_hash', self.test_data, index_type='hash')
        self.assertIsInstance(index, HashIndex)
        
        # 创建B+树索引
        index = self.engine.create_index('test_btree', self.test_data, index_type='btree')
        self.assertIsInstance(index, BPlusTreeIndex)
        
        # 创建倒排索引
        index = self.engine.create_index('test_inverted', self.test_data, index_type='inverted')
        self.assertIsInstance(index, InvertedIndex)
        
        # 创建Trie索引
        index = self.engine.create_index('test_trie', self.test_data, index_type='trie')
        self.assertIsInstance(index, CompressedTrieIndex)
    
    def test_create_adaptive_index(self):
        """测试自适应索引创建"""
        # 不指定类型，让引擎自动选择
        index = self.engine.create_index('test_adaptive', self.test_data)
        
        self.assertIsNotNone(index)
        self.assertEqual(index.name, 'test_adaptive')
    
    def test_query_index(self):
        """测试索引查询"""
        self.engine.create_index('test_index', self.test_data, index_type='hash')
        
        result = self.engine.query_index('test_index', 'A001')
        self.assertEqual(result, {'rule1', 'rule2'})
        
        result = self.engine.query_index('test_index', 'NOTFOUND')
        self.assertEqual(result, set())
        
        # 查询不存在的索引
        result = self.engine.query_index('nonexistent', 'A001')
        self.assertEqual(result, set())
    
    def test_prefix_query_index(self):
        """测试前缀查询"""
        # 创建支持前缀查询的索引
        self.engine.create_index('test_trie', self.test_data, index_type='trie')
        
        # 修改测试数据以便更好地测试前缀查询
        prefix_data = {
            'apple': {'rule1'},
            'application': {'rule2'},
            'banana': {'rule3'},
        }
        self.engine.create_index('prefix_test', prefix_data, index_type='trie')
        
        result = self.engine.prefix_query_index('prefix_test', 'app')
        self.assertEqual(result, {'rule1', 'rule2'})
        
        # 对不支持前缀查询的索引进行前缀查询
        result = self.engine.prefix_query_index('test_index', 'A')  # 假设test_index是哈希索引
        self.assertEqual(result, set())
        
        # 查询不存在的索引
        result = self.engine.prefix_query_index('nonexistent', 'A')
        self.assertEqual(result, set())
    
    def test_get_index_stats(self):
        """测试获取索引统计"""
        index = self.engine.create_index('stats_test', self.test_data, index_type='hash')
        
        # 执行一些查询以生成统计
        self.engine.query_index('stats_test', 'A001')
        self.engine.query_index('stats_test', 'B001')
        
        stats = self.engine.get_index_stats('stats_test')
        
        self.assertIsNotNone(stats)
        self.assertEqual(stats.index_type, 'HashIndex')
        self.assertEqual(stats.total_keys, 3)
        self.assertGreater(stats.memory_usage_mb, 0)
        
        # 获取不存在索引的统计
        stats = self.engine.get_index_stats('nonexistent')
        self.assertIsNone(stats)
    
    def test_get_all_stats(self):
        """测试获取所有索引统计"""
        self.engine.create_index('index1', self.test_data, index_type='hash')
        self.engine.create_index('index2', self.test_data, index_type='btree')
        
        all_stats = self.engine.get_all_stats()
        
        self.assertEqual(len(all_stats), 2)
        self.assertIn('index1', all_stats)
        self.assertIn('index2', all_stats)
        self.assertEqual(all_stats['index1'].index_type, 'HashIndex')
        self.assertEqual(all_stats['index2'].index_type, 'BPlusTreeIndex')
    
    def test_update_query_patterns(self):
        """测试更新查询模式"""
        new_patterns = {'exact': 0.5, 'prefix': 0.3, 'range': 0.2}
        
        self.engine.update_query_patterns(new_patterns)
        
        # 验证模式已更新（和为1.0）
        updated_patterns = self.engine._query_patterns
        self.assertAlmostEqual(sum(updated_patterns.values()), 1.0, places=2)
        self.assertAlmostEqual(updated_patterns['exact'], 0.5, places=2)
        self.assertAlmostEqual(updated_patterns['prefix'], 0.3, places=2)
        self.assertAlmostEqual(updated_patterns['range'], 0.2, places=2)
    
    def test_get_performance_report(self):
        """测试性能报告"""
        self.engine.create_index('perf_test1', self.test_data, index_type='hash')
        self.engine.create_index('perf_test2', self.test_data, index_type='btree')
        
        report = self.engine.get_performance_report()
        
        self.assertEqual(report['total_indexes'], 2)
        self.assertGreater(report['total_memory_mb'], 0)
        self.assertIn('HashIndex', report['index_type_distribution'])
        self.assertIn('BPlusTreeIndex', report['index_type_distribution'])
        self.assertGreater(report['avg_build_time_ms'], 0)
    
    def test_clear_index(self):
        """测试清除单个索引"""
        self.engine.create_index('clear_test', self.test_data, index_type='hash')
        
        # 确认索引存在
        self.assertIsNotNone(self.engine.get_index_stats('clear_test'))
        
        # 清除索引
        success = self.engine.clear_index('clear_test')
        self.assertTrue(success)
        
        # 确认索引已被清除
        self.assertIsNone(self.engine.get_index_stats('clear_test'))
        
        # 清除不存在的索引
        success = self.engine.clear_index('nonexistent')
        self.assertFalse(success)
    
    def test_clear_all_indexes(self):
        """测试清除所有索引"""
        self.engine.create_index('clear_all_test1', self.test_data, index_type='hash')
        self.engine.create_index('clear_all_test2', self.test_data, index_type='btree')
        
        # 确认索引存在
        self.assertEqual(len(self.engine.get_all_stats()), 2)
        
        # 清除所有索引
        self.engine.clear_all_indexes()
        
        # 确认所有索引已被清除
        self.assertEqual(len(self.engine.get_all_stats()), 0)
    
    def test_empty_data_handling(self):
        """测试空数据处理"""
        empty_data = {}
        
        index = self.engine.create_index('empty_test', empty_data, index_type='hash')
        
        self.assertIsInstance(index, HashIndex)
        
        # 查询空索引
        result = self.engine.query_index('empty_test', 'any_key')
        self.assertEqual(result, set())
    
    def test_invalid_index_type_fallback(self):
        """测试无效索引类型的回退处理"""
        with patch('core.advanced_index_engine.logger') as mock_logger:
            index = self.engine.create_index('invalid_test', self.test_data, index_type='invalid_type')
            
            # 应该回退到哈希索引
            self.assertIsInstance(index, HashIndex)
            
            # 应该记录警告
            mock_logger.warning.assert_called()


class TestGlobalInstance(unittest.TestCase):
    """全局实例测试"""
    
    def test_global_instance_exists(self):
        """测试全局实例存在"""
        self.assertIsInstance(advanced_index_engine, MultiTypeIndexEngine)
    
    def test_global_instance_functionality(self):
        """测试全局实例功能"""
        test_data = {'key1': {'value1'}}
        
        try:
            index = advanced_index_engine.create_index('global_test', test_data, index_type='hash')
            self.assertIsInstance(index, HashIndex)
            
            result = advanced_index_engine.query_index('global_test', 'key1')
            self.assertEqual(result, {'value1'})
            
        finally:
            # 清理
            advanced_index_engine.clear_all_indexes()


if __name__ == '__main__':
    unittest.main()