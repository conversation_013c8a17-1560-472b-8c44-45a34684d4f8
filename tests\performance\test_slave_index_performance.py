"""
从节点索引构建器性能测试
验证5秒热重载要求和系统性能指标
"""

import json
import gzip
import os
import tempfile
import time
import threading
import unittest
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor, as_completed

from core.slave_node_index_builder import SlaveNodeIndexBuilder, IndexHotReloader


class TestSlaveIndexPerformance(unittest.TestCase):
    """从节点索引构建器性能测试"""

    def setUp(self):
        """设置测试环境"""
        # 模拟环境变量
        self.env_patcher = patch.dict(
            "os.environ",
            {
                "SLAVE_INDEX_MEMORY_LIMIT_MB": "500",
                "SLAVE_INDEX_GC_THRESHOLD": "1000",
            },
        )
        self.env_patcher.start()

        # 创建临时缓存文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json.gz', delete=False)
        self.temp_file.close()

        # 模拟rule_index_manager
        self.mock_rule_index_manager = Mock()
        self.mock_rule_index_manager.build_indexes_from_rule_details = Mock()

    def tearDown(self):
        """清理测试环境"""
        self.env_patcher.stop()
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def _create_large_test_data(self, rule_count: int):
        """创建大量测试数据"""
        rules = []
        for i in range(rule_count):
            rule = {
                "rule_id": f"PERF_TEST_RULE_{i:06d}",  # 修复：添加必需的rule_id字段
                "rule_key": f"PERF_TEST_RULE_{i:06d}",
                "rule_name": f"性能测试规则{i}",
                "rule_type": "VALIDATION",
                "yb_code": f"Y{i:04d}",
                "diag_code": f"D{i:04d}",
                "fee_code": f"F{i:04d}",
                "rule_content": f"性能测试规则内容{i}" * 10,  # 增加内容长度
                "is_active": True,
                "priority": i % 10,
                "extended_fields": json.dumps({
                    "test_field_1": f"value_{i}",
                    "test_field_2": list(range(i % 100)),
                    "test_field_3": {"nested": {"data": f"nested_value_{i}"}}
                })
            }
            rules.append(rule)
        return rules

    def _write_performance_test_cache(self, rule_count: int):
        """写入性能测试缓存文件"""
        rules_data = self._create_large_test_data(rule_count)
        cache_data = {
            "rule_details": rules_data,
            "metadata": {
                "total_count": len(rules_data),
                "generated_time": time.time(),
                "version": "1.0"
            }
        }
        
        with gzip.open(self.temp_file.name, 'wt', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_hot_reload_5_second_requirement(self, mock_memory_optimizer_class):
        """测试5秒热重载要求"""
        # 模拟内存优化器
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        # 创建中等规模的测试数据
        self._write_performance_test_cache(5000)

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 模拟成功的重载
        builder.build_index_from_cache_file = Mock(return_value=True)

        # 测试热重载性能
        start_time = time.perf_counter()
        success = reloader.reload_index()
        reload_time = time.perf_counter() - start_time

        # 验证5秒要求
        self.assertTrue(success)
        self.assertLess(reload_time, 5.0, f"热重载耗时 {reload_time:.2f}s 超过5秒要求")

        # 验证性能统计
        stats = builder.get_performance_stats()
        self.assertGreater(stats.hot_reload_count, 0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_large_dataset_build_performance(self, mock_memory_optimizer_class):
        """测试大数据集构建性能"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 300.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        # 创建大规模测试数据
        rule_counts = [1000, 5000, 10000, 20000]
        performance_results = []

        for rule_count in rule_counts:
            self._write_performance_test_cache(rule_count)
            
            builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
            
            # 测量构建时间
            start_time = time.perf_counter()
            success = builder.build_index_from_cache_file()
            build_time = time.perf_counter() - start_time
            
            self.assertTrue(success)
            
            # 记录性能结果
            stats = builder.get_performance_stats()
            performance_results.append({
                "rule_count": rule_count,
                "build_time_ms": build_time * 1000,
                "throughput_rules_per_sec": rule_count / build_time if build_time > 0 else 0,
                "memory_usage_mb": stats.avg_memory_usage_mb
            })

        # 验证性能趋势
        print("\n=== 大数据集构建性能测试结果 ===")
        for result in performance_results:
            print(f"规则数量: {result['rule_count']:,}, "
                  f"构建时间: {result['build_time_ms']:.2f}ms, "
                  f"吞吐量: {result['throughput_rules_per_sec']:.0f} 规则/秒, "
                  f"内存使用: {result['memory_usage_mb']:.1f}MB")

        # 验证吞吐量要求（至少1000规则/秒）
        for result in performance_results:
            self.assertGreater(result['throughput_rules_per_sec'], 1000,
                             f"规则数量 {result['rule_count']} 的吞吐量过低")

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_concurrent_operations_performance(self, mock_memory_optimizer_class):
        """测试并发操作性能"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        self._write_performance_test_cache(2000)

        def build_index_task():
            """单个构建任务"""
            builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
            start_time = time.perf_counter()
            success = builder.build_index_from_cache_file()
            build_time = time.perf_counter() - start_time
            return success, build_time

        # 并发执行多个构建任务
        concurrent_count = 5
        with ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            futures = [executor.submit(build_index_task) for _ in range(concurrent_count)]
            
            results = []
            for future in as_completed(futures):
                success, build_time = future.result()
                results.append((success, build_time))

        # 验证所有任务都成功
        for success, build_time in results:
            self.assertTrue(success)
            self.assertLess(build_time, 10.0)  # 并发情况下允许更长时间

        print(f"\n=== 并发操作性能测试结果 ===")
        print(f"并发任务数: {concurrent_count}")
        print(f"平均构建时间: {sum(t for _, t in results) / len(results):.2f}s")
        print(f"最大构建时间: {max(t for _, t in results):.2f}s")

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_memory_usage_under_load(self, mock_memory_optimizer_class):
        """测试负载下的内存使用"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        # 模拟内存使用增长
        memory_values = [200.0, 250.0, 280.0, 290.0, 295.0]
        memory_iter = iter(memory_values)
        
        def get_memory_stats():
            try:
                memory_mb = next(memory_iter)
            except StopIteration:
                memory_mb = 295.0
            
            mock_stats = Mock()
            mock_stats.is_over_threshold = memory_mb > 270.0
            mock_stats.process_memory_mb = memory_mb
            return mock_stats
        
        mock_memory_optimizer.get_memory_stats.side_effect = get_memory_stats

        self._write_performance_test_cache(10000)
        
        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        
        # 执行构建
        success = builder.build_index_from_cache_file()
        self.assertTrue(success)

        # 验证内存优化被触发
        mock_memory_optimizer.optimize_memory.assert_called()
        
        # 验证性能统计记录了内存优化
        stats = builder.get_performance_stats()
        self.assertGreater(stats.memory_optimization_count, 0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_stress_test_continuous_operations(self, mock_memory_optimizer_class):
        """压力测试：连续操作"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        self._write_performance_test_cache(1000)
        
        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        
        # 连续执行多次构建
        operation_count = 50
        success_count = 0
        total_time = 0
        
        for i in range(operation_count):
            start_time = time.perf_counter()
            success = builder.build_index_from_cache_file()
            operation_time = time.perf_counter() - start_time
            
            if success:
                success_count += 1
            total_time += operation_time

        # 验证成功率
        success_rate = (success_count / operation_count) * 100
        self.assertGreater(success_rate, 95.0, f"成功率 {success_rate:.1f}% 过低")

        # 验证平均性能
        avg_time = total_time / operation_count
        self.assertLess(avg_time, 2.0, f"平均操作时间 {avg_time:.2f}s 过长")

        print(f"\n=== 压力测试结果 ===")
        print(f"操作次数: {operation_count}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"平均操作时间: {avg_time:.3f}s")
        print(f"总耗时: {total_time:.2f}s")

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_performance_degradation_impact(self, mock_memory_optimizer_class):
        """测试性能降级影响"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer
        
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        self._write_performance_test_cache(5000)
        
        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)
        
        # 测试正常状态性能
        start_time = time.perf_counter()
        success = builder.build_index_from_cache_file()
        normal_time = time.perf_counter() - start_time
        self.assertTrue(success)

        # 触发降级
        for i in range(6):  # 超过错误阈值
            try:
                raise Exception(f"测试错误 {i}")
            except Exception as e:
                builder.error_handler.handle_build_error(e)

        # 验证已进入降级模式
        self.assertTrue(builder.is_in_degradation_mode())

        # 测试降级状态性能
        start_time = time.perf_counter()
        success = builder.build_index_from_cache_file()
        degraded_time = time.perf_counter() - start_time
        self.assertTrue(success)

        # 验证降级对性能的影响在可接受范围内
        performance_impact = (degraded_time - normal_time) / normal_time * 100
        self.assertLess(performance_impact, 50.0, f"降级性能影响 {performance_impact:.1f}% 过大")

        print(f"\n=== 性能降级影响测试结果 ===")
        print(f"正常状态时间: {normal_time:.3f}s")
        print(f"降级状态时间: {degraded_time:.3f}s")
        print(f"性能影响: {performance_impact:.1f}%")


if __name__ == "__main__":
    unittest.main()
