"""
BatchValidationProcessor 测试
测试批量校验处理器功能
"""

from unittest.mock import Mock, patch

import pytest

from services.batch_validation_processor import (
    BatchValidationProcessor,
    BatchValidationReport,
)
from services.rule_detail_service import ServiceError
from services.validation_rule_engine import ValidationResult


class TestBatchValidationProcessor:
    """BatchValidationProcessor 测试类"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock()

    @pytest.fixture
    def batch_processor(self, mock_session):
        """创建批量校验处理器实例"""
        return BatchValidationProcessor(mock_session, max_workers=2, batch_size=10)

    @pytest.fixture
    def sample_data_list(self):
        """示例数据列表"""
        return [
            {"patient_name": "张三", "age": 25, "gender": "男"},
            {"patient_name": "李四", "age": 30, "gender": "女"},
            {"patient_name": "", "age": -5, "gender": "未知"},  # 包含错误的数据
            {"patient_name": "王五", "age": 35, "gender": "男"},
        ]

    @pytest.fixture
    def mock_validation_results(self):
        """模拟校验结果"""
        return [
            # 第一条数据：校验通过
            ValidationResult(
                valid=True,
                errors=[],
                warnings=[],
                field_count=3,
                validated_fields=["patient_name", "age", "gender"],
                duration=0.01,
            ),
            # 第二条数据：校验通过
            ValidationResult(
                valid=True,
                errors=[],
                warnings=[],
                field_count=3,
                validated_fields=["patient_name", "age", "gender"],
                duration=0.01,
            ),
            # 第三条数据：校验失败
            ValidationResult(
                valid=False,
                errors=[
                    {
                        "field_name": "patient_name",
                        "chinese_name": "患者姓名",
                        "error_code": "REQUIRED_FIELD_MISSING",
                        "error_message": "患者姓名不能为空",
                        "error_value": "",
                        "rule_type": "required",
                        "suggestions": ["请填写患者姓名"],
                    },
                    {
                        "field_name": "age",
                        "chinese_name": "年龄",
                        "error_code": "MIN_VALUE_NOT_MET",
                        "error_message": "年龄不能小于0",
                        "error_value": -5,
                        "rule_type": "min_value",
                        "suggestions": ["年龄的值不能小于0"],
                    },
                ],
                warnings=["性别字段建议使用标准值"],
                field_count=3,
                validated_fields=["patient_name", "age", "gender"],
                duration=0.02,
            ),
            # 第四条数据：校验通过
            ValidationResult(
                valid=True,
                errors=[],
                warnings=[],
                field_count=3,
                validated_fields=["patient_name", "age", "gender"],
                duration=0.01,
            ),
        ]

    def test_validate_batch_sequential(self, batch_processor, sample_data_list, mock_validation_results):
        """测试顺序批量校验"""
        # 模拟ValidationRuleEngine的validate_data方法
        with patch.object(batch_processor.validation_engine, "validate_data") as mock_validate:
            mock_validate.side_effect = mock_validation_results

            # 执行测试
            report = batch_processor.validate_batch(
                rule_key="test_rule", data_list=sample_data_list, parallel=False, include_valid_data=False
            )

            # 验证结果
            assert isinstance(report, BatchValidationReport)
            assert report.total_items == 4
            assert report.valid_items == 3
            assert report.invalid_items == 1
            assert report.success_rate == 0.75
            assert len(report.errors) == 1
            assert len(report.warnings) == 1

            # 验证错误详情
            error = report.errors[0]
            assert error.index == 2  # 第三条数据（索引为2）
            assert len(error.errors) == 2  # 两个错误

    def test_validate_batch_parallel(self, batch_processor, sample_data_list, mock_validation_results):
        """测试并行批量校验"""
        # 模拟ValidationRuleEngine的validate_data方法
        with patch.object(batch_processor.validation_engine, "validate_data") as mock_validate:
            mock_validate.side_effect = mock_validation_results

            # 执行测试
            report = batch_processor.validate_batch(
                rule_key="test_rule", data_list=sample_data_list, parallel=True, include_valid_data=False
            )

            # 验证结果
            assert isinstance(report, BatchValidationReport)
            assert report.total_items == 4
            assert report.valid_items == 3
            assert report.invalid_items == 1
            assert report.success_rate == 0.75

    def test_validate_batch_empty_data(self, batch_processor):
        """测试空数据列表"""
        # 执行测试
        report = batch_processor.validate_batch(rule_key="test_rule", data_list=[], parallel=False)

        # 验证结果
        assert report.total_items == 0
        assert report.valid_items == 0
        assert report.invalid_items == 0
        assert report.success_rate == 0.0
        assert len(report.errors) == 0

    def test_validate_batch_single_item(self, batch_processor):
        """测试单条数据校验"""
        # 模拟校验结果
        mock_result = ValidationResult(
            valid=True, errors=[], warnings=[], field_count=1, validated_fields=["test_field"], duration=0.01
        )

        with patch.object(batch_processor.validation_engine, "validate_data") as mock_validate:
            mock_validate.return_value = mock_result

            # 执行测试
            report = batch_processor.validate_batch(
                rule_key="test_rule",
                data_list=[{"test_field": "test_value"}],
                parallel=True,  # 单条数据应该自动使用顺序处理
            )

            # 验证结果
            assert report.total_items == 1
            assert report.valid_items == 1
            assert report.success_rate == 1.0

    def test_validate_batch_with_exceptions(self, batch_processor, sample_data_list):
        """测试校验过程中的异常处理"""
        # 模拟ValidationRuleEngine抛出异常
        with patch.object(batch_processor.validation_engine, "validate_data") as mock_validate:
            mock_validate.side_effect = [
                ValidationResult(valid=True, errors=[], warnings=[], field_count=1, validated_fields=[], duration=0.01),
                Exception("校验异常"),
                ValidationResult(valid=True, errors=[], warnings=[], field_count=1, validated_fields=[], duration=0.01),
                ValidationResult(valid=True, errors=[], warnings=[], field_count=1, validated_fields=[], duration=0.01),
            ]

            # 执行测试
            report = batch_processor.validate_batch(rule_key="test_rule", data_list=sample_data_list, parallel=False)

            # 验证结果
            assert report.total_items == 4
            assert report.valid_items == 3
            assert report.invalid_items == 1
            assert len(report.errors) == 1

            # 验证异常被正确处理
            error = report.errors[0]
            assert error.index == 1  # 第二条数据出现异常
            assert any("校验异常" in str(e.get("error_message", "")) for e in error.errors)

    def test_validate_batch_include_valid_data(self, batch_processor, mock_validation_results):
        """测试包含有效数据的报告"""
        sample_data = [{"test_field": "test_value"}]

        with patch.object(batch_processor.validation_engine, "validate_data") as mock_validate:
            mock_validate.return_value = mock_validation_results[2]  # 使用有错误的结果

            # 执行测试
            report = batch_processor.validate_batch(
                rule_key="test_rule", data_list=sample_data, parallel=False, include_valid_data=True
            )

            # 验证结果
            assert len(report.errors) == 1
            assert report.errors[0].data == sample_data[0]  # 应该包含原始数据

    def test_batch_validation_report_to_dict(self, batch_processor, sample_data_list, mock_validation_results):
        """测试批量校验报告转换为字典"""
        with patch.object(batch_processor.validation_engine, "validate_data") as mock_validate:
            mock_validate.side_effect = mock_validation_results

            # 执行测试
            report = batch_processor.validate_batch(rule_key="test_rule", data_list=sample_data_list, parallel=False)

            # 转换为字典
            report_dict = report.to_dict()

            # 验证字典结构
            assert "total_items" in report_dict
            assert "valid_items" in report_dict
            assert "invalid_items" in report_dict
            assert "success_rate" in report_dict
            assert "total_duration" in report_dict
            assert "average_duration" in report_dict
            assert "errors" in report_dict
            assert "warnings" in report_dict
            assert "metadata" in report_dict

            # 验证错误格式
            if report_dict["errors"]:
                error = report_dict["errors"][0]
                assert "index" in error
                assert "row_id" in error
                assert "errors" in error
                assert "warnings" in error
                assert "data" in error

    def test_get_validation_summary(self, batch_processor):
        """测试获取校验规则摘要"""
        # 模拟ValidationRuleEngine的get_validation_rules方法
        mock_rules = [
            Mock(rule_type=Mock(value="required"), field_name="field1", is_required=True),
            Mock(rule_type=Mock(value="max_length"), field_name="field1", is_required=False),
            Mock(rule_type=Mock(value="required"), field_name="field2", is_required=True),
        ]

        with patch.object(batch_processor.validation_engine, "get_validation_rules") as mock_get_rules:
            mock_get_rules.return_value = mock_rules

            with patch.object(batch_processor.validation_engine, "get_cache_stats") as mock_cache_stats:
                mock_cache_stats.return_value = {"cache_size": 1}

                # 执行测试
                summary = batch_processor.get_validation_summary("test_rule")

                # 验证结果
                assert summary["rule_key"] == "test_rule"
                assert summary["total_rules"] == 3
                assert "rule_types" in summary
                assert "required_fields" in summary
                assert "cache_stats" in summary
                assert len(summary["required_fields"]) == 2  # field1 和 field2

    def test_get_validation_summary_error(self, batch_processor):
        """测试获取校验规则摘要时的错误处理"""
        # 模拟ValidationRuleEngine抛出异常
        with patch.object(batch_processor.validation_engine, "get_validation_rules") as mock_get_rules:
            mock_get_rules.side_effect = Exception("获取规则失败")

            # 执行测试
            summary = batch_processor.get_validation_summary("test_rule")

            # 验证结果
            assert summary["rule_key"] == "test_rule"
            assert "error" in summary
            assert "获取规则失败" in summary["error"]

    def test_batch_validation_performance(self, batch_processor, sample_data_list, mock_validation_results):
        """测试批量校验性能指标"""
        with patch.object(batch_processor.validation_engine, "validate_data") as mock_validate:
            mock_validate.side_effect = mock_validation_results

            # 执行测试
            report = batch_processor.validate_batch(rule_key="test_rule", data_list=sample_data_list, parallel=False)

            # 验证性能指标
            assert report.total_duration >= 0  # 允许为0
            assert report.average_duration >= 0  # 允许为0
            assert "validation_duration" in report.metadata
            assert "processing_overhead" in report.metadata
            assert report.metadata["processing_overhead"] >= 0

    def test_batch_validation_metadata(self, batch_processor, sample_data_list, mock_validation_results):
        """测试批量校验元数据"""
        with patch.object(batch_processor.validation_engine, "validate_data") as mock_validate:
            mock_validate.side_effect = mock_validation_results

            # 执行测试
            report = batch_processor.validate_batch(rule_key="test_rule", data_list=sample_data_list, parallel=True)

            # 验证元数据
            metadata = report.metadata
            assert metadata["rule_key"] == "test_rule"
            assert metadata["max_workers"] == batch_processor.max_workers
            assert metadata["batch_size"] == batch_processor.batch_size
            assert "validated_at" in metadata

    def test_service_error_handling(self, batch_processor):
        """测试服务错误处理"""
        # 模拟ValidationRuleEngine的validate_data方法抛出ServiceError
        with patch.object(batch_processor.validation_engine, "validate_data") as mock_validate:
            mock_validate.side_effect = ServiceError("数据库连接失败", error_code="DATABASE_CONNECTION_FAILED")

            # 执行测试
            with pytest.raises(ServiceError) as exc_info:
                batch_processor.validate_batch(rule_key="test_rule", data_list=[{"test": "data"}], parallel=False)

            # 验证异常
            assert exc_info.value.error_code == "BATCH_VALIDATION_FAILED"
            assert "数据库连接失败" in str(exc_info.value)
