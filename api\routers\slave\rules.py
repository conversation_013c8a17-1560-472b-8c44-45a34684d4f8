"""
Rules router for slave node - provides basic rule information.
"""

from fastapi import APIRouter, Request

from core.logging.logging_system import log as logger
from core.middleware.request_tracking import request_tracker
from core.rule_cache import RULE_CACHE
from models.api import ApiResponse

# Rules router (no authentication required for basic rule info)
rules_router = APIRouter(tags=["Rules"])


@rules_router.get("/rules", response_model=ApiResponse[dict])
async def get_rules_info(request: Request):
    """
    Get basic information about rules loaded in the slave node.

    Args:
        request: HTTP request object

    Returns:
        ApiResponse[dict]: Basic rule information
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting rules info: {request_id}")

        # Add request tracking event
        if request_id:
            request_tracker.add_event(
                request_id,
                "rules_info_request",
                {"endpoint": "/rules", "operation": "get_rules_info"},
            )

        # Get basic rule information from cache
        rule_count = len(RULE_CACHE)
        rule_keys = list(RULE_CACHE.keys()) if rule_count > 0 else []

        rules_info = {
            "node_type": "slave",
            "total_rules": rule_count,
            "rule_keys": rule_keys[:10],  # Only show first 10 rule keys
            "has_more": rule_count > 10,
            "cache_status": "loaded" if rule_count > 0 else "empty",
        }

        # Add completion event
        if request_id:
            request_tracker.add_event(
                request_id,
                "rules_info_completed",
                {"rule_count": rule_count, "success": True},
            )
            request_tracker.complete_request(request_id, success=True)

        logger.info(f"Rules info retrieved: {request_id} - {rule_count} rules loaded")

        return ApiResponse.success_response(
            data=rules_info,
            message=f"从节点规则信息获取成功，共加载 {rule_count} 个规则",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get rules info: {request_id} - {str(e)}", exc_info=True)

        # Add error event
        if request_id:
            request_tracker.add_event(
                request_id,
                "rules_info_error",
                {"error": str(e), "success": False},
            )
            request_tracker.complete_request(request_id, success=False)

        # Let the unified error handler deal with this
        raise


@rules_router.get("/rules/status", response_model=ApiResponse[dict])
async def get_rules_status(request: Request):
    """
    Get status information about rules in the slave node.

    Args:
        request: HTTP request object

    Returns:
        ApiResponse[dict]: Rule status information
    """
    request_id = getattr(request.state, "request_id", None)

    try:
        logger.info(f"Getting rules status: {request_id}")

        # Add request tracking event
        if request_id:
            request_tracker.add_event(
                request_id,
                "rules_status_request",
                {"endpoint": "/rules/status", "operation": "get_rules_status"},
            )

        # Get rule status information
        rule_count = len(RULE_CACHE)

        status_info = {
            "node_type": "slave",
            "total_rules": rule_count,
            "cache_status": "loaded" if rule_count > 0 else "empty",
            "sync_enabled": True,  # This would come from settings in a real implementation
            "last_sync": None,  # This would come from sync service in a real implementation
        }

        # Add completion event
        if request_id:
            request_tracker.add_event(
                request_id,
                "rules_status_completed",
                {"rule_count": rule_count, "success": True},
            )
            request_tracker.complete_request(request_id, success=True)

        logger.info(f"Rules status retrieved: {request_id} - {rule_count} rules")

        return ApiResponse.success_response(
            data=status_info,
            message="从节点规则状态获取成功",
            request_id=request_id,
        )

    except Exception as e:
        logger.error(f"Failed to get rules status: {request_id} - {str(e)}", exc_info=True)

        # Add error event
        if request_id:
            request_tracker.add_event(
                request_id,
                "rules_status_error",
                {"error": str(e), "success": False},
            )
            request_tracker.complete_request(request_id, success=False)

        # Let the unified error handler deal with this
        raise
