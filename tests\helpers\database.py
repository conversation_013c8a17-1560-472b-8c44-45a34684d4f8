"""
数据库测试工具
提供数据库测试相关的辅助函数
"""

from collections.abc import Generator
from contextlib import contextmanager
from typing import Any

import pytest
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from models.database import Base, BaseRule, RuleDataSet, RuleDetail


class DatabaseTestHelper:
    """数据库测试辅助类"""

    def __init__(self, session):
        self.session = session

    def create_test_base_rule(self, **kwargs) -> BaseRule:
        """创建测试基础规则"""
        default_data = {
            "rule_key": f"TEST_RULE_{id(kwargs)}",
            "rule_name": "测试规则",
            "description": "测试规则描述",
            "module_path": "test.module.path",
            "file_hash": "test_hash",
            "status": "READY",
            "created_by": "test_user",
        }
        default_data.update(kwargs)

        rule = BaseRule(**default_data)
        self.session.add(rule)
        self.session.commit()
        self.session.refresh(rule)
        return rule

    def create_test_rule_dataset(self, base_rule_id: int = None, **kwargs) -> RuleDataSet:
        """创建测试规则数据集"""
        if base_rule_id is None:
            base_rule = self.create_test_base_rule()
            base_rule_id = base_rule.id

        default_data = {
            "base_rule_id": base_rule_id,
            "version": 1,
            "is_active": True,
            "uploaded_by": "test_user",
            "migration_status": "PENDING",
        }
        default_data.update(kwargs)

        dataset = RuleDataSet(**default_data)
        self.session.add(dataset)
        self.session.commit()
        self.session.refresh(dataset)
        return dataset

    def create_test_rule_detail(self, dataset_id: int = None, **kwargs) -> RuleDetail:
        """创建测试规则明细"""
        if dataset_id is None:
            dataset = self.create_test_rule_dataset()
            dataset_id = dataset.id

        default_data = {
            "dataset_id": dataset_id,
            "rule_detail_id": f"TEST_DETAIL_{id(kwargs)}",
            "rule_name": "测试明细规则",
            "error_level_1": "数据质量",
            "error_level_2": "完整性",
            "status": "ACTIVE",
        }
        default_data.update(kwargs)

        detail = RuleDetail(**default_data)
        self.session.add(detail)
        self.session.commit()
        self.session.refresh(detail)
        return detail

    def create_complete_test_data(self) -> dict[str, Any]:
        """创建完整的测试数据链"""
        base_rule = self.create_test_base_rule()
        dataset = self.create_test_rule_dataset(base_rule_id=base_rule.id)
        details = [
            self.create_test_rule_detail(dataset_id=dataset.id, rule_detail_id=f"DETAIL_{i}")
            for i in range(3)
        ]

        return {
            "base_rule": base_rule,
            "dataset": dataset,
            "details": details,
        }

    def cleanup_test_data(self, rule_key_prefix: str = "TEST_"):
        """清理测试数据"""
        try:
            # 删除规则明细
            self.session.execute(
                text("DELETE FROM rule_details WHERE rule_detail_id LIKE :prefix"),
                {"prefix": f"{rule_key_prefix}%"}
            )

            # 删除规则数据集
            self.session.execute(
                text("DELETE FROM rule_data_sets WHERE uploaded_by = 'test_user'")
            )

            # 删除基础规则
            self.session.execute(
                text("DELETE FROM base_rules WHERE rule_key LIKE :prefix"),
                {"prefix": f"{rule_key_prefix}%"}
            )

            self.session.commit()
        except Exception:
            self.session.rollback()
            raise

    def count_records(self, table_name: str) -> int:
        """统计表记录数"""
        result = self.session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
        return result.scalar()

    def get_record_by_id(self, model_class, record_id: int):
        """根据ID获取记录"""
        return self.session.query(model_class).filter(model_class.id == record_id).first()


@contextmanager
def temporary_database() -> Generator[sessionmaker, None, None]:
    """临时数据库上下文管理器"""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )

    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)  # noqa: N806

    try:
        yield SessionLocal
    finally:
        Base.metadata.drop_all(engine)
        engine.dispose()


@pytest.fixture
def db_test_helper(test_db_session):
    """数据库测试辅助器夹具"""
    return DatabaseTestHelper(test_db_session)


@pytest.fixture
def clean_database(test_db_session):
    """清理数据库夹具"""
    yield test_db_session

    # 测试后清理所有测试数据
    try:
        test_db_session.execute(text("DELETE FROM rule_details WHERE rule_detail_id LIKE 'TEST_%'"))
        test_db_session.execute(text("DELETE FROM rule_data_sets WHERE uploaded_by = 'test_user'"))
        test_db_session.execute(text("DELETE FROM base_rules WHERE rule_key LIKE 'TEST_%'"))
        test_db_session.commit()
    except Exception:
        test_db_session.rollback()


def assert_database_state(session, expected_counts: dict[str, int]):
    """断言数据库状态"""
    for table_name, expected_count in expected_counts.items():
        actual_count = session.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
        assert actual_count == expected_count, \
            f"表 {table_name} 期望 {expected_count} 条记录，实际 {actual_count} 条"


def create_test_data_batch(session, data_type: str, count: int = 5) -> list[Any]:
    """批量创建测试数据"""
    helper = DatabaseTestHelper(session)

    if data_type == "base_rules":
        return [helper.create_test_base_rule(rule_key=f"BATCH_RULE_{i}") for i in range(count)]
    elif data_type == "rule_datasets":
        base_rule = helper.create_test_base_rule()
        return [helper.create_test_rule_dataset(base_rule_id=base_rule.id, version=i+1) for i in range(count)]
    elif data_type == "rule_details":
        dataset = helper.create_test_rule_dataset()
        return [helper.create_test_rule_detail(dataset_id=dataset.id, rule_detail_id=f"BATCH_DETAIL_{i}") for i in range(count)]  # noqa: E501
    else:
        raise ValueError(f"Unsupported data type: {data_type}")


class DatabaseTransactionHelper:
    """数据库事务测试辅助类"""

    def __init__(self, session):
        self.session = session
        self.savepoints = []

    def create_savepoint(self, name: str = None):
        """创建保存点"""
        if name is None:
            name = f"sp_{len(self.savepoints)}"

        self.session.execute(text(f"SAVEPOINT {name}"))
        self.savepoints.append(name)
        return name

    def rollback_to_savepoint(self, name: str = None):
        """回滚到保存点"""
        if name is None and self.savepoints:
            name = self.savepoints.pop()

        if name:
            self.session.execute(text(f"ROLLBACK TO SAVEPOINT {name}"))

    def release_savepoint(self, name: str = None):
        """释放保存点"""
        if name is None and self.savepoints:
            name = self.savepoints.pop()

        if name:
            self.session.execute(text(f"RELEASE SAVEPOINT {name}"))


@pytest.fixture
def db_transaction_helper(test_db_session):
    """数据库事务辅助器夹具"""
    return DatabaseTransactionHelper(test_db_session)
