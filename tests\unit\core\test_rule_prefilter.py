"""
规则预过滤器单元测试
测试规则预过滤的完整流程和异常处理
"""

import unittest
from unittest.mock import Mock, patch

from config.settings import settings
from core.rule_prefilter import RulePreFilter
from core.rule_index_manager import FilterResult, PatientCodeExtraction
from models.patient import PatientData


class TestRulePreFilter(unittest.TestCase):
    """测试规则预过滤器"""
    
    def setUp(self):
        self.prefilter = RulePreFilter()
        
        # 重置统计信息
        self.prefilter.reset_stats()
    
    def create_mock_patient_data(self):
        """创建模拟患者数据"""
        patient = Mock(spec=PatientData)
        patient.bah = "TEST001"
        return patient
    
    @patch('core.rule_prefilter.rule_index_manager')
    @patch('core.rule_prefilter.patient_data_analyzer')
    @patch('core.rule_prefilter.settings')
    def test_filter_rules_for_patient_success(self, mock_settings, mock_analyzer, mock_index_manager):
        """测试成功的规则过滤流程"""
        # 配置模拟
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_settings.PREFILTER_TIMEOUT_MS = 10.0
        
        mock_index_manager.is_ready.return_value = True
        
        # 模拟患者代码提取结果
        mock_patient_codes = PatientCodeExtraction(
            yb_codes={"Y001", "Y002"},
            diag_codes={"D001"},
            surgery_codes=set(),
            extraction_time=0.5
        )
        mock_analyzer.extract_codes.return_value = mock_patient_codes
        
        # 模拟过滤结果
        mock_filter_result = FilterResult(
            original_rule_count=100,
            filtered_rule_count=30,
            filter_rate=0.7,
            filter_time=2.5,
            filtered_rule_ids=["rule1", "rule2", "rule3"]
        )
        mock_index_manager.filter_rules.return_value = mock_filter_result
        
        # 执行测试
        patient_data = self.create_mock_patient_data()
        requested_rules = ["rule1", "rule2", "rule3", "rule4", "rule5"]
        
        result = self.prefilter.filter_rules_for_patient(patient_data, requested_rules)
        
        # 验证结果
        self.assertEqual(result.original_rule_count, 100)
        self.assertEqual(result.filtered_rule_count, 30)
        self.assertEqual(result.filter_rate, 0.7)
        self.assertEqual(result.filtered_rule_ids, ["rule1", "rule2", "rule3"])
        
        # 验证调用
        mock_analyzer.extract_codes.assert_called_once_with(patient_data)
        mock_index_manager.filter_rules.assert_called_once_with(mock_patient_codes, requested_rules)
    
    @patch('core.rule_prefilter.settings')
    def test_filter_rules_disabled(self, mock_settings):
        """测试过滤功能未启用的情况"""
        mock_settings.ENABLE_RULE_PREFILTER = False
        
        patient_data = self.create_mock_patient_data()
        requested_rules = ["rule1", "rule2", "rule3"]
        
        result = self.prefilter.filter_rules_for_patient(patient_data, requested_rules)
        
        # 应该返回未过滤的结果
        self.assertEqual(result.original_rule_count, 3)
        self.assertEqual(result.filtered_rule_count, 3)
        self.assertEqual(result.filter_rate, 0.0)
        self.assertEqual(result.filtered_rule_ids, requested_rules)
    
    @patch('core.rule_prefilter.rule_index_manager')
    @patch('core.rule_prefilter.settings')
    def test_filter_rules_index_not_ready(self, mock_settings, mock_index_manager):
        """测试索引未就绪的情况"""
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_index_manager.is_ready.return_value = False
        
        patient_data = self.create_mock_patient_data()
        requested_rules = ["rule1", "rule2"]
        
        result = self.prefilter.filter_rules_for_patient(patient_data, requested_rules)
        
        # 应该触发降级，返回未过滤结果
        self.assertEqual(result.filter_rate, 0.0)
        self.assertEqual(result.filtered_rule_ids, requested_rules)
        
        # 检查降级计数
        stats = self.prefilter.get_performance_stats()
        self.assertEqual(stats['fallback_count'], 1)
    
    @patch('core.rule_prefilter.rule_index_manager')
    @patch('core.rule_prefilter.patient_data_analyzer')
    @patch('core.rule_prefilter.settings')
    def test_filter_rules_no_valid_codes(self, mock_settings, mock_analyzer, mock_index_manager):
        """测试患者无有效代码的情况"""
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_index_manager.is_ready.return_value = True
        
        # 模拟无有效代码的患者
        mock_patient_codes = PatientCodeExtraction(
            yb_codes=set(),
            diag_codes=set(),
            surgery_codes=set(),
            extraction_time=0.1
        )
        mock_analyzer.extract_codes.return_value = mock_patient_codes
        
        patient_data = self.create_mock_patient_data()
        requested_rules = ["rule1", "rule2"]
        
        result = self.prefilter.filter_rules_for_patient(patient_data, requested_rules)
        
        # 应该保留所有规则
        self.assertEqual(result.filter_rate, 0.0)
        self.assertEqual(result.filtered_rule_ids, requested_rules)
    
    @patch('core.rule_prefilter.rule_index_manager')
    @patch('core.rule_prefilter.patient_data_analyzer')
    @patch('core.rule_prefilter.settings')
    def test_filter_rules_timeout(self, mock_settings, mock_analyzer, mock_index_manager):
        """测试过滤超时的情况"""
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_settings.PREFILTER_TIMEOUT_MS = 5.0  # 设置较短的超时时间
        mock_index_manager.is_ready.return_value = True
        
        # 模拟有效患者代码
        mock_patient_codes = PatientCodeExtraction(
            yb_codes={"Y001"},
            diag_codes=set(),
            surgery_codes=set(),
            extraction_time=0.5
        )
        mock_analyzer.extract_codes.return_value = mock_patient_codes
        
        # 模拟超时的过滤结果
        mock_filter_result = FilterResult(
            original_rule_count=100,
            filtered_rule_count=30,
            filter_rate=0.7,
            filter_time=10.0,  # 超过超时时间
            filtered_rule_ids=["rule1", "rule2"]
        )
        mock_index_manager.filter_rules.return_value = mock_filter_result
        
        patient_data = self.create_mock_patient_data()
        requested_rules = ["rule1", "rule2", "rule3"]
        
        result = self.prefilter.filter_rules_for_patient(patient_data, requested_rules)
        
        # 应该触发超时降级
        self.assertEqual(result.filter_rate, 0.0)
        self.assertEqual(result.filtered_rule_ids, requested_rules)
        
        # 检查超时计数
        stats = self.prefilter.get_performance_stats()
        self.assertEqual(stats['timeout_count'], 1)
    
    @patch('core.rule_prefilter.rule_index_manager')
    @patch('core.rule_prefilter.patient_data_analyzer')
    @patch('core.rule_prefilter.settings')
    def test_filter_rules_exception_handling(self, mock_settings, mock_analyzer, mock_index_manager):
        """测试异常处理"""
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_index_manager.is_ready.return_value = True
        
        # 模拟患者代码提取异常
        mock_analyzer.extract_codes.side_effect = Exception("提取失败")
        
        patient_data = self.create_mock_patient_data()
        requested_rules = ["rule1", "rule2"]
        
        result = self.prefilter.filter_rules_for_patient(patient_data, requested_rules)
        
        # 应该返回降级结果
        self.assertEqual(result.filter_rate, 0.0)
        self.assertEqual(result.filtered_rule_ids, requested_rules)
        
        # 检查降级计数
        stats = self.prefilter.get_performance_stats()
        self.assertEqual(stats['fallback_count'], 1)
    
    def test_get_performance_stats(self):
        """测试性能统计获取"""
        # 执行一些操作以生成统计数据
        with patch('core.rule_prefilter.settings') as mock_settings:
            mock_settings.ENABLE_RULE_PREFILTER = False
            
            patient_data = self.create_mock_patient_data()
            self.prefilter.filter_rules_for_patient(patient_data, ["rule1", "rule2"])
        
        stats = self.prefilter.get_performance_stats()
        
        # 检查统计字段
        expected_fields = [
            'filter_count', 'total_filter_time_ms', 'avg_filter_time_ms',
            'total_original_rules', 'total_filtered_rules', 'overall_filter_rate',
            'fallback_count', 'timeout_count', 'fallback_rate', 'timeout_rate',
            'filter_enabled', 'filter_timeout_ms',
            'index_manager_stats', 'patient_analyzer_stats'
        ]
        
        for field in expected_fields:
            self.assertIn(field, stats)
        
        # 检查基本统计
        self.assertEqual(stats['filter_count'], 1)
        self.assertTrue(stats['total_filter_time_ms'] >= 0)
    
    def test_reset_stats(self):
        """测试重置统计信息"""
        # 先生成一些统计数据
        with patch('core.rule_prefilter.settings') as mock_settings:
            mock_settings.ENABLE_RULE_PREFILTER = False
            
            patient_data = self.create_mock_patient_data()
            self.prefilter.filter_rules_for_patient(patient_data, ["rule1"])
        
        # 确认有统计数据
        stats_before = self.prefilter.get_performance_stats()
        self.assertTrue(stats_before['filter_count'] > 0)
        
        # 重置统计
        self.prefilter.reset_stats()
        
        # 检查统计已重置
        stats_after = self.prefilter.get_performance_stats()
        self.assertEqual(stats_after['filter_count'], 0)
        self.assertEqual(stats_after['total_filter_time_ms'], 0)
        self.assertEqual(stats_after['fallback_count'], 0)
        self.assertEqual(stats_after['timeout_count'], 0)
    
    @patch('core.rule_prefilter.rule_index_manager')
    @patch('core.rule_prefilter.settings')
    def test_is_healthy_enabled_index_ready(self, mock_settings, mock_index_manager):
        """测试健康状态检查 - 启用且索引就绪"""
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_index_manager.is_ready.return_value = True
        
        self.assertTrue(self.prefilter.is_healthy())
    
    @patch('core.rule_prefilter.rule_index_manager')
    @patch('core.rule_prefilter.settings')  
    def test_is_healthy_enabled_index_not_ready(self, mock_settings, mock_index_manager):
        """测试健康状态检查 - 启用但索引未就绪"""
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_index_manager.is_ready.return_value = False
        
        self.assertFalse(self.prefilter.is_healthy())
    
    @patch('core.rule_prefilter.settings')
    def test_is_healthy_disabled(self, mock_settings):
        """测试健康状态检查 - 未启用"""
        mock_settings.ENABLE_RULE_PREFILTER = False
        
        self.assertTrue(self.prefilter.is_healthy())  # 未启用时认为健康
    
    @patch('core.rule_prefilter.settings')
    def test_is_healthy_high_fallback_rate(self, mock_settings):
        """测试健康状态检查 - 高降级率"""
        mock_settings.ENABLE_RULE_PREFILTER = True
        mock_settings.PREFILTER_FALLBACK_THRESHOLD = 0.1
        
        # 模拟高降级率
        self.prefilter._filter_count = 20
        self.prefilter._fallback_count = 5  # 25%降级率，超过10%阈值
        
        with patch('core.rule_prefilter.rule_index_manager') as mock_index_manager:
            mock_index_manager.is_ready.return_value = True
            self.assertFalse(self.prefilter.is_healthy())
    
    def test_get_health_report(self):
        """测试健康状态报告"""
        with patch('core.rule_prefilter.settings') as mock_settings:
            mock_settings.ENABLE_RULE_PREFILTER = True
            
            with patch('core.rule_prefilter.rule_index_manager') as mock_index_manager:
                mock_index_manager.is_ready.return_value = True
                
                report = self.prefilter.get_health_report()
                
                # 检查报告字段
                expected_fields = [
                    'is_healthy', 'filter_enabled', 'index_ready',
                    'fallback_rate', 'timeout_rate', 'avg_filter_time_ms',
                    'overall_filter_rate', 'recommendations'
                ]
                
                for field in expected_fields:
                    self.assertIn(field, report)
                
                self.assertTrue(report['is_healthy'])
                self.assertTrue(report['filter_enabled'])
                self.assertTrue(report['index_ready'])
                self.assertIsInstance(report['recommendations'], list)
    
    def test_get_health_recommendations(self):
        """测试健康状态建议"""
        # 测试未启用的建议
        with patch('core.rule_prefilter.settings') as mock_settings:
            mock_settings.ENABLE_RULE_PREFILTER = False
            
            recommendations = self.prefilter._get_health_recommendations()
            self.assertTrue(any("未启用" in rec for rec in recommendations))
        
        # 测试索引未就绪的建议
        with patch('core.rule_prefilter.settings') as mock_settings, \
             patch('core.rule_prefilter.rule_index_manager') as mock_index_manager:
            
            mock_settings.ENABLE_RULE_PREFILTER = True
            mock_index_manager.is_ready.return_value = False
            
            recommendations = self.prefilter._get_health_recommendations()
            self.assertTrue(any("索引未就绪" in rec for rec in recommendations))
    
    def test_has_valid_codes(self):
        """测试有效代码检查"""
        # 有医保代码
        codes_with_yb = PatientCodeExtraction(
            yb_codes={"Y001"},
            diag_codes=set(),
            surgery_codes=set(),
            extraction_time=1.0
        )
        self.assertTrue(self.prefilter._has_valid_codes(codes_with_yb))
        
        # 有诊断代码
        codes_with_diag = PatientCodeExtraction(
            yb_codes=set(),
            diag_codes={"D001"},
            surgery_codes=set(),
            extraction_time=1.0
        )
        self.assertTrue(self.prefilter._has_valid_codes(codes_with_diag))
        
        # 有手术代码
        codes_with_surgery = PatientCodeExtraction(
            yb_codes=set(),
            diag_codes=set(),
            surgery_codes={"S001"},
            extraction_time=1.0
        )
        self.assertTrue(self.prefilter._has_valid_codes(codes_with_surgery))
        
        # 无任何代码
        codes_empty = PatientCodeExtraction(
            yb_codes=set(),
            diag_codes=set(),
            surgery_codes=set(),
            extraction_time=1.0
        )
        self.assertFalse(self.prefilter._has_valid_codes(codes_empty))
    
    def test_create_no_filter_result(self):
        """测试创建未过滤结果"""
        import time
        
        requested_rules = ["rule1", "rule2", "rule3"]
        start_time = time.perf_counter()
        
        result = self.prefilter._create_no_filter_result(requested_rules, start_time, "测试原因")
        
        self.assertEqual(result.original_rule_count, 3)
        self.assertEqual(result.filtered_rule_count, 3)
        self.assertEqual(result.filter_rate, 0.0)
        self.assertEqual(result.filtered_rule_ids, requested_rules)
        self.assertTrue(result.filter_time >= 0)
    
    def test_update_statistics(self):
        """测试更新统计信息"""
        # 重置统计
        self.prefilter.reset_stats()
        
        # 创建过滤结果
        filter_result = FilterResult(
            original_rule_count=100,
            filtered_rule_count=30,
            filter_rate=0.7,
            filter_time=2.5,
            filtered_rule_ids=["rule1", "rule2"]
        )
        
        # 更新统计
        self.prefilter._update_statistics(filter_result)
        
        # 检查统计信息
        self.assertEqual(self.prefilter._filter_count, 1)
        self.assertEqual(self.prefilter._total_filter_time, 2.5)
        self.assertEqual(self.prefilter._total_original_rules, 100)
        self.assertEqual(self.prefilter._total_filtered_rules, 30)


if __name__ == '__main__':
    unittest.main()