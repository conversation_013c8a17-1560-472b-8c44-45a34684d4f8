"""
内存优化器
监控系统内存使用情况，并在接近限制时自动优化
确保系统内存使用始终低于1GB
"""

import gc
import threading
import time
from dataclasses import dataclass

import psutil

from config.settings import settings
from core.intelligent_cache import intelligent_cache
from core.logging.logging_system import log as logger


@dataclass
class MemoryStats:
    """内存统计信息"""

    total_mb: float
    used_mb: float
    available_mb: float
    usage_percentage: float
    process_memory_mb: float
    cache_memory_mb: float
    threshold_mb: float

    @property
    def is_over_threshold(self) -> bool:
        """是否超过阈值"""
        return self.process_memory_mb > self.threshold_mb


class MemoryOptimizer:
    """
    内存优化器
    实时监控内存使用，在接近限制时自动清理和优化
    """

    def __init__(self, memory_limit_mb: int = None):
        """
        初始化内存优化器

        Args:
            memory_limit_mb: 内存限制（MB），默认从配置获取
        """
        self.memory_limit_mb = memory_limit_mb or getattr(settings, "ULTRA_FAST_MEMORY_LIMIT_MB", 1024)
        self.warning_threshold = self.memory_limit_mb * 0.8  # 80%时开始警告
        self.critical_threshold = self.memory_limit_mb * 0.9  # 90%时开始清理

        # 监控配置
        self.monitoring_interval = 5.0  # 监控间隔（秒）
        self.is_monitoring = False
        self.monitor_thread: threading.Thread | None = None

        # 统计信息
        self.cleanup_count = 0
        self.total_memory_freed_mb = 0.0
        self.last_cleanup_time = 0.0

        # 进程对象
        self.process = psutil.Process()

        logger.info(f"内存优化器初始化完成，限制: {self.memory_limit_mb}MB")

    def start_monitoring(self):
        """启动内存监控"""
        if self.is_monitoring:
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("内存监控已启动")

    def stop_monitoring(self):
        """停止内存监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        logger.info("内存监控已停止")

    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                stats = self.get_memory_stats()

                if stats.process_memory_mb > self.critical_threshold:
                    logger.warning(
                        f"内存使用超过临界值: {stats.process_memory_mb:.1f}MB "
                        f"(限制: {self.memory_limit_mb}MB)，开始清理"
                    )
                    self.optimize_memory(aggressive=True)

                elif stats.process_memory_mb > self.warning_threshold:
                    logger.info(f"内存使用超过警告值: {stats.process_memory_mb:.1f}MB (限制: {self.memory_limit_mb}MB)")
                    self.optimize_memory(aggressive=False)

                time.sleep(self.monitoring_interval)

            except Exception as e:
                logger.error(f"内存监控异常: {e}")
                time.sleep(self.monitoring_interval)

    def get_memory_stats(self) -> MemoryStats:
        """获取内存统计信息"""
        # 系统内存
        system_memory = psutil.virtual_memory()

        # 进程内存
        process_memory = self.process.memory_info()
        process_memory_mb = process_memory.rss / (1024 * 1024)

        # 缓存内存估算
        cache_memory_mb = 0.0
        try:
            cache_stats = intelligent_cache.get_overall_stats()
            cache_memory_mb = sum(stat.memory_usage_mb for stat in cache_stats.values())
        except Exception:
            pass

        return MemoryStats(
            total_mb=system_memory.total / (1024 * 1024),
            used_mb=system_memory.used / (1024 * 1024),
            available_mb=system_memory.available / (1024 * 1024),
            usage_percentage=system_memory.percent,
            process_memory_mb=process_memory_mb,
            cache_memory_mb=cache_memory_mb,
            threshold_mb=self.memory_limit_mb,
        )

    def optimize_memory(self, aggressive: bool = False) -> dict[str, float]:
        """
        优化内存使用

        Args:
            aggressive: 是否执行激进的清理策略

        Returns:
            清理统计信息
        """
        start_time = time.perf_counter()
        before_stats = self.get_memory_stats()
        freed_memory = {}

        logger.info(f"开始内存优化（aggressive={aggressive}）...")

        # 1. 垃圾回收
        if aggressive:
            # 激进模式：执行完整的垃圾回收
            collected = 0
            for generation in range(3):
                collected += gc.collect()
            freed_memory["garbage_collection"] = collected
            logger.debug(f"垃圾回收清理了 {collected} 个对象")
        else:
            # 温和模式：只执行快速垃圾回收
            collected = gc.collect(0)  # 只清理最新一代
            freed_memory["garbage_collection"] = collected

        # 2. 缓存清理
        cache_memory_before = before_stats.cache_memory_mb
        if aggressive:
            # 激进模式：清空所有缓存
            intelligent_cache.clear_all()
            freed_memory["cache_cleared"] = cache_memory_before
            logger.debug(f"清空所有缓存，释放约 {cache_memory_before:.1f}MB")
        else:
            # 温和模式：清理部分缓存
            # 这里可以实现更精细的缓存清理策略
            try:
                # 清理计算结果缓存（最不重要）
                intelligent_cache.computation_cache.clear()
                freed_memory["computation_cache_cleared"] = cache_memory_before * 0.2  # 估算
                logger.debug("清理计算结果缓存")
            except Exception as e:
                logger.warning(f"缓存清理失败: {e}")

        # 3. 对象池优化
        try:
            from core.object_pool import object_pool_manager

            if hasattr(object_pool_manager, "cleanup_idle_objects"):
                cleaned = object_pool_manager.cleanup_idle_objects(aggressive=aggressive)
                freed_memory["object_pool_cleanup"] = cleaned
                logger.debug(f"对象池清理了 {cleaned} 个空闲对象")
        except ImportError:
            pass
        except Exception as e:
            logger.warning(f"对象池清理失败: {e}")

        # 4. 进程特定优化
        if aggressive:
            try:
                # 释放进程内存给操作系统
                import ctypes

                if hasattr(ctypes, "windll"):  # Windows
                    ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1, -1)
                elif hasattr(psutil, "Process"):  # Linux可以使用其他方法
                    pass
            except Exception as e:
                logger.debug(f"进程内存压缩失败: {e}")

        # 计算优化效果
        after_stats = self.get_memory_stats()
        memory_freed_mb = before_stats.process_memory_mb - after_stats.process_memory_mb
        optimization_time = (time.perf_counter() - start_time) * 1000

        # 更新统计
        self.cleanup_count += 1
        self.total_memory_freed_mb += memory_freed_mb
        self.last_cleanup_time = time.perf_counter()

        logger.info(
            f"内存优化完成：释放 {memory_freed_mb:.1f}MB，"
            f"{before_stats.process_memory_mb:.1f}MB -> {after_stats.process_memory_mb:.1f}MB，"
            f"耗时 {optimization_time:.1f}ms"
        )

        freed_memory["total_freed_mb"] = memory_freed_mb
        freed_memory["optimization_time_ms"] = optimization_time

        return freed_memory

    def check_memory_pressure(self) -> tuple[bool, str]:
        """
        检查内存压力

        Returns:
            tuple: (是否有内存压力, 压力级别描述)
        """
        stats = self.get_memory_stats()

        if stats.process_memory_mb > self.critical_threshold:
            return True, "critical"
        elif stats.process_memory_mb > self.warning_threshold:
            return True, "warning"
        else:
            return False, "normal"

    def get_optimization_stats(self) -> dict[str, any]:
        """获取优化统计信息"""
        stats = self.get_memory_stats()
        has_pressure, pressure_level = self.check_memory_pressure()

        return {
            "current_memory_mb": stats.process_memory_mb,
            "memory_limit_mb": self.memory_limit_mb,
            "usage_percentage": (stats.process_memory_mb / self.memory_limit_mb) * 100,
            "has_memory_pressure": has_pressure,
            "pressure_level": pressure_level,
            "cleanup_count": self.cleanup_count,
            "total_memory_freed_mb": self.total_memory_freed_mb,
            "last_cleanup_time": self.last_cleanup_time,
            "is_monitoring": self.is_monitoring,
            "cache_memory_mb": stats.cache_memory_mb,
            "system_memory_usage_percentage": stats.usage_percentage,
            "system_available_memory_mb": stats.available_mb,
        }

    def force_optimize(self, target_memory_mb: float | None = None) -> dict[str, float]:
        """
        强制优化内存到目标值

        Args:
            target_memory_mb: 目标内存使用量（MB），如果不指定则使用警告阈值

        Returns:
            优化结果统计
        """
        if target_memory_mb is None:
            target_memory_mb = self.warning_threshold

        current_stats = self.get_memory_stats()

        if current_stats.process_memory_mb <= target_memory_mb:
            logger.info(f"当前内存使用 {current_stats.process_memory_mb:.1f}MB 已低于目标 {target_memory_mb:.1f}MB")
            return {"target_achieved": True, "memory_freed": 0.0}

        logger.info(f"强制优化内存：{current_stats.process_memory_mb:.1f}MB -> {target_memory_mb:.1f}MB")

        # 执行多轮优化
        max_attempts = 3
        total_freed = 0.0

        for attempt in range(max_attempts):
            result = self.optimize_memory(aggressive=True)
            total_freed += result.get("total_freed_mb", 0.0)

            current_stats = self.get_memory_stats()
            if current_stats.process_memory_mb <= target_memory_mb:
                logger.info(f"目标达成：{current_stats.process_memory_mb:.1f}MB <= {target_memory_mb:.1f}MB")
                break

            if attempt < max_attempts - 1:
                logger.info(f"第 {attempt + 1} 轮优化完成，继续...")
                time.sleep(1.0)  # 短暂等待让系统稳定

        return {
            "target_achieved": current_stats.process_memory_mb <= target_memory_mb,
            "final_memory_mb": current_stats.process_memory_mb,
            "total_freed_mb": total_freed,
            "attempts": attempt + 1,
        }


# 全局内存优化器实例
memory_optimizer = MemoryOptimizer()

# 如果启用了性能监控，自动启动内存监控
if getattr(settings, "ENABLE_PERFORMANCE_MONITORING", True):
    memory_optimizer.start_monitoring()
