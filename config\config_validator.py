"""
配置验证模块
验证重试配置的合理性和一致性
"""

import logging
from typing import Any

from config.retry_config import RetryConfigManager
from config.settings import Settings

logger = logging.getLogger(__name__)


class ConfigValidationError(Exception):
    """配置验证错误"""
    pass


class RetryConfigValidator:
    """重试配置验证器"""

    def __init__(self, settings: Settings):
        self.settings = settings
        self.config_manager = RetryConfigManager(settings)

    def validate_all(self) -> dict[str, Any]:
        """验证所有重试相关配置"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "config_summary": {}
        }

        try:
            # 验证基础重试配置
            self._validate_basic_retry_config(validation_result)

            # 验证断路器配置
            self._validate_circuit_breaker_config(validation_result)

            # 验证环境特定配置
            self._validate_environment_configs(validation_result)

            # 验证配置一致性
            self._validate_config_consistency(validation_result)

            # 生成配置摘要
            validation_result["config_summary"] = self.config_manager.get_config_summary()

        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"配置验证过程中发生错误: {str(e)}")

        # 如果有错误，标记为无效
        if validation_result["errors"]:
            validation_result["valid"] = False

        return validation_result

    def _validate_basic_retry_config(self, result: dict[str, Any]):
        """验证基础重试配置"""
        # 验证重试次数
        if self.settings.HTTP_RETRY_MAX_ATTEMPTS < 1:
            result["errors"].append("HTTP_RETRY_MAX_ATTEMPTS必须大于等于1")
        elif self.settings.HTTP_RETRY_MAX_ATTEMPTS > 10:
            result["warnings"].append("HTTP_RETRY_MAX_ATTEMPTS过大(>10)，可能导致长时间等待")

        # 验证延迟配置
        if self.settings.HTTP_RETRY_BASE_DELAY <= 0:
            result["errors"].append("HTTP_RETRY_BASE_DELAY必须大于0")

        if self.settings.HTTP_RETRY_MAX_DELAY < self.settings.HTTP_RETRY_BASE_DELAY:
            result["errors"].append("HTTP_RETRY_MAX_DELAY必须大于等于HTTP_RETRY_BASE_DELAY")

        if self.settings.HTTP_RETRY_MAX_DELAY > 300:  # 5分钟
            result["warnings"].append("HTTP_RETRY_MAX_DELAY过大(>300秒)，可能影响用户体验")

        # 验证退避因子
        if self.settings.HTTP_RETRY_BACKOFF_FACTOR <= 1:
            result["errors"].append("HTTP_RETRY_BACKOFF_FACTOR必须大于1")
        elif self.settings.HTTP_RETRY_BACKOFF_FACTOR > 5:
            result["warnings"].append("HTTP_RETRY_BACKOFF_FACTOR过大(>5)，延迟增长过快")

        # 验证状态码配置
        try:
            status_codes = [int(code.strip()) for code in self.settings.HTTP_RETRY_STATUS_CODES.split(",")]
            for code in status_codes:
                if not (100 <= code <= 599):
                    result["errors"].append(f"无效的HTTP状态码: {code}")
        except ValueError:
            result["errors"].append("HTTP_RETRY_STATUS_CODES格式错误，应为逗号分隔的数字")

    def _validate_circuit_breaker_config(self, result: dict[str, Any]):
        """验证断路器配置"""
        # 验证失败阈值
        if self.settings.CIRCUIT_BREAKER_FAILURE_THRESHOLD < 1:
            result["errors"].append("CIRCUIT_BREAKER_FAILURE_THRESHOLD必须大于等于1")

        # 验证失败率阈值
        if not (0.0 <= self.settings.CIRCUIT_BREAKER_FAILURE_RATE_THRESHOLD <= 1.0):
            result["errors"].append("CIRCUIT_BREAKER_FAILURE_RATE_THRESHOLD必须在0.0到1.0之间")

        # 验证恢复超时
        if self.settings.CIRCUIT_BREAKER_RECOVERY_TIMEOUT <= 0:
            result["errors"].append("CIRCUIT_BREAKER_RECOVERY_TIMEOUT必须大于0")
        elif self.settings.CIRCUIT_BREAKER_RECOVERY_TIMEOUT > 600:  # 10分钟
            result["warnings"].append("CIRCUIT_BREAKER_RECOVERY_TIMEOUT过大(>600秒)，恢复时间过长")

        # 验证窗口大小
        if self.settings.CIRCUIT_BREAKER_WINDOW_SIZE < 10:
            result["warnings"].append("CIRCUIT_BREAKER_WINDOW_SIZE过小(<10)，统计可能不准确")
        elif self.settings.CIRCUIT_BREAKER_WINDOW_SIZE > 1000:
            result["warnings"].append("CIRCUIT_BREAKER_WINDOW_SIZE过大(>1000)，可能消耗过多内存")

        # 验证半开状态最大调用数
        if self.settings.CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS < 1:
            result["errors"].append("CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS必须大于等于1")

    def _validate_environment_configs(self, result: dict[str, Any]):
        """验证环境特定配置"""
        environments = ["DEV", "TEST", "PROD"]

        for env in environments:
            env_prefix = f"HTTP_RETRY_{env}_"

            # 获取环境特定配置
            max_attempts = getattr(self.settings, f"{env_prefix}MAX_ATTEMPTS")
            base_delay = getattr(self.settings, f"{env_prefix}BASE_DELAY")
            max_delay = getattr(self.settings, f"{env_prefix}MAX_DELAY")
            backoff_factor = getattr(self.settings, f"{env_prefix}BACKOFF_FACTOR")

            # 验证环境特定配置
            if max_attempts < 1:
                result["errors"].append(f"{env}环境: MAX_ATTEMPTS必须大于等于1")

            if base_delay <= 0:
                result["errors"].append(f"{env}环境: BASE_DELAY必须大于0")

            if max_delay < base_delay:
                result["errors"].append(f"{env}环境: MAX_DELAY必须大于等于BASE_DELAY")

            if backoff_factor <= 1:
                result["errors"].append(f"{env}环境: BACKOFF_FACTOR必须大于1")

    def _validate_config_consistency(self, result: dict[str, Any]):
        """验证配置一致性"""
        # 检查生产环境配置是否过于激进
        if (self.settings.HTTP_RETRY_PROD_MAX_ATTEMPTS > 5 or 
            self.settings.HTTP_RETRY_PROD_BASE_DELAY < 1.0):
            result["warnings"].append("生产环境重试配置可能过于激进，建议使用更保守的设置")

        # 检查测试环境配置是否会影响测试速度
        if (self.settings.HTTP_RETRY_TEST_MAX_ATTEMPTS > 3 or 
            self.settings.HTTP_RETRY_TEST_BASE_DELAY > 1.0):
            result["warnings"].append("测试环境重试配置可能影响测试执行速度")

        # 检查开发环境配置是否合理
        if self.settings.HTTP_RETRY_DEV_MAX_ATTEMPTS > 10:
            result["warnings"].append("开发环境重试次数过多，可能影响开发效率")

    def validate_and_raise(self):
        """验证配置，如果有错误则抛出异常"""
        validation_result = self.validate_all()

        if not validation_result["valid"]:
            error_msg = "重试配置验证失败:\n" + "\n".join(validation_result["errors"])
            raise ConfigValidationError(error_msg)

        # 记录警告
        for warning in validation_result["warnings"]:
            logger.warning(f"配置警告: {warning}")

        logger.info("重试配置验证通过")
        return validation_result


def validate_retry_config(settings: Settings = None) -> dict[str, Any]:
    """验证重试配置的便捷函数"""
    if settings is None:
        from config.settings import get_settings
        settings = get_settings()

    validator = RetryConfigValidator(settings)
    return validator.validate_all()


def validate_retry_config_and_raise(settings: Settings = None):
    """验证重试配置，如果有错误则抛出异常"""
    if settings is None:
        from config.settings import get_settings
        settings = get_settings()

    validator = RetryConfigValidator(settings)
    return validator.validate_and_raise()


if __name__ == "__main__":
    # 命令行验证工具
    import sys

    try:
        result = validate_retry_config()

        print("=== 重试配置验证结果 ===")
        print(f"验证状态: {'通过' if result['valid'] else '失败'}")

        if result["errors"]:
            print("\n错误:")
            for error in result["errors"]:
                print(f"  - {error}")

        if result["warnings"]:
            print("\n警告:")
            for warning in result["warnings"]:
                print(f"  - {warning}")

        print("\n配置摘要:")
        summary = result["config_summary"]
        print(f"  环境: {summary['environment']}")
        print(f"  重试启用: {summary['retry']['enabled']}")
        print(f"  最大重试次数: {summary['retry']['max_attempts']}")
        print(f"  基础延迟: {summary['retry']['base_delay']}秒")
        print(f"  最大延迟: {summary['retry']['max_delay']}秒")
        print(f"  断路器启用: {summary['circuit_breaker']['enabled']}")

        sys.exit(0 if result["valid"] else 1)

    except Exception as e:
        print(f"验证过程中发生错误: {e}")
        sys.exit(1)
