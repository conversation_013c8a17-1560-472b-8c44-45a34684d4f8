# ===== Python相关 =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.venv/
venv/
env/

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#Pipfile.lock

# PEP 582; used by e.g. github.com/<PERSON>-<PERSON>on<PERSON>/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ===== 环境配置和敏感信息 =====
# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# 数据库文件
*.db
*.sqlite
*.sqlite3

# ===== 运行时文件 =====
# 日志文件
logs/
*.log
_log/

# 缓存文件
rules_cache.json*
rules_version.txt
*.cache

# 临时文件
tmp/
temp/
.tmp/
*.tmp

# 进程文件
*.pid
*.seed
*.pid.lock

# ===== 生成文件 =====
# 生成的模板和数据文件
generated_templates/
*.xlsx
*.xls
333.json
_data/

# ===== 开发工具 =====
# IDE配置
.vscode/
.idea/
*.swp
*.swo
*~
settings.json

# AI工具配置
.roo
memory-bank/
.cursorignore
.claude/
CLAUDE.md

# 代码检查缓存
.ruff_cache/

# ===== 文档和演示 =====
# 本地文档（保留README.md等核心文档）
documentation/
demo/

# ===== 测试相关 =====
# 测试临时文件
validate_test.py
generate.py
test_*.db

# ===== 前端相关 =====
# 依赖目录
frontend/node_modules/
frontend/.pnp
frontend/.pnp.js

# 构建产物
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/

# 前端缓存
frontend/.cache/
frontend/.parcel-cache/

# 前端日志
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/pnpm-debug.log*

# 前端环境文件
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# 前端测试
frontend/coverage/
frontend/.nyc_output/

# ===== 系统文件 =====
# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# ===== 本地文档 =====
docs/deprecated/
docs/archive/

# ===== 压缩文件 =====
*.zip
*.tar.gz
*.rar

# ===== 测试文件 =====
tests/*/reports/

# ===== 本地备份文件 =====
backup/

# ===== 其他 =====
# CMake
CMakeFiles/
CMakeCache.txt
CMakeScripts/
CTestTestfile.cmake
cmake_install.cmake
compile_commands.json
Makefile
examples/

# End of file
