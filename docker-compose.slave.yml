# ===== 从节点生产环境 Docker Compose 配置 =====
# 只包含从节点后端服务，无需数据库连接

# 重要说明：
# - 从节点完全脱离数据库运行
# - 使用本地 rules_cache.json.gz 文件加载规则
# - 支持离线模式部署（ENABLE_RULE_SYNC=false）

version: '3.8'

services:
  # ===== 从节点后端服务 =====
  sub-rule-slave:
    image: registry.yadingdata.com/library/sub-rule-slave:test_1.0.0_V4
    container_name: sub-rule-slave
    restart: unless-stopped
    volumes:
      # 持久化存储（生产环境路径）
      - /home/<USER>/services/sub-rule-slave/data:/app/data
      - /home/<USER>/services/sub-rule-slave/logs:/app/logs
      - ./slave.env:/app/.env:ro
      - ./rules_cache.json.gz:/app/rules_cache.json.gz:ro
    network_mode: host
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
