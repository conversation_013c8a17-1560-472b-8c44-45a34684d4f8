"""
规则预过滤器集成测试
验证完整的规则预过滤工作流程
"""

import unittest
from unittest.mock import Mock

from core.patient_data_analyzer import patient_data_analyzer
from core.rule_index_manager import rule_index_manager
from core.rule_prefilter import rule_prefilter
from models.patient import PatientData


class TestRulePrefilterIntegration(unittest.TestCase):
    """规则预过滤器集成测试"""

    def setUp(self):
        """设置测试环境"""
        # 重置所有组件状态
        rule_index_manager._clear_indexes()
        rule_prefilter.reset_stats()
        patient_data_analyzer.reset_stats()

        # 生成测试规则数据
        self.test_rules = self._create_test_rules()

        # 构建索引
        rule_index_manager.build_indexes_from_rule_details(self.test_rules)

    def _create_test_rules(self):
        """创建测试规则数据"""
        rules = []

        # 药品规则
        drug_rule = Mock()
        drug_rule.rule_id = "drug_rule_001"
        drug_rule.yb_code = "Y001,Y002"
        drug_rule.diag_whole_code = ""
        drug_rule.diag_code_prefix = ""
        drug_rule.fee_whole_code = ""
        drug_rule.fee_code_prefix = ""
        drug_rule.extended_fields = "{}"
        rules.append(drug_rule)

        # 诊断规则
        diag_rule = Mock()
        diag_rule.rule_id = "diag_rule_002"
        diag_rule.yb_code = ""
        diag_rule.diag_whole_code = "I10,E11.9"
        diag_rule.diag_code_prefix = "I1"
        diag_rule.fee_whole_code = ""
        diag_rule.fee_code_prefix = ""
        diag_rule.extended_fields = "{}"
        rules.append(diag_rule)

        # 手术规则
        surgery_rule = Mock()
        surgery_rule.rule_id = "surgery_rule_003"
        surgery_rule.yb_code = ""
        surgery_rule.diag_whole_code = ""
        surgery_rule.diag_code_prefix = ""
        surgery_rule.fee_whole_code = ""
        surgery_rule.fee_code_prefix = ""
        surgery_rule.extended_fields = '{"surgery_code": "S001,S002"}'
        rules.append(surgery_rule)

        # 组合规则
        combo_rule = Mock()
        combo_rule.rule_id = "combo_rule_004"
        combo_rule.yb_code = "Y003"
        combo_rule.diag_whole_code = "K59.1"
        combo_rule.diag_code_prefix = ""
        combo_rule.fee_whole_code = "F001"
        combo_rule.fee_code_prefix = ""
        combo_rule.extended_fields = "{}"
        rules.append(combo_rule)

        # 通用规则（无特定代码限制）
        universal_rule = Mock()
        universal_rule.rule_id = "universal_rule_005"
        universal_rule.yb_code = ""
        universal_rule.diag_whole_code = ""
        universal_rule.diag_code_prefix = ""
        universal_rule.fee_whole_code = ""
        universal_rule.fee_code_prefix = ""
        universal_rule.extended_fields = "{}"
        rules.append(universal_rule)

        return rules

    def _create_test_patient(self, yb_codes=None, diag_codes=None, surgery_codes=None):
        """创建测试患者数据"""
        patient = Mock(spec=PatientData)

        # 创建费用明细
        fees = []
        if yb_codes:
            for code in yb_codes:
                fee = Mock()
                fee.ybdm = code
                fees.append(fee)
        patient.fees = fees

        # 创建诊断信息 - 修复数据结构以匹配PatientDataAnalyzer期望
        diagnosis_info = Mock()

        # 设置诊断代码字段
        if diag_codes and len(diag_codes) > 0:
            # 设置门急诊西医疾病代码
            diagnosis_info.outPatientDiagnosisICDCode = diag_codes[0]

            # 如果有多个诊断代码，设置诊断列表
            if len(diag_codes) > 1:
                diagnosis_list = []
                for code in diag_codes[1:]:
                    diag_item = Mock()
                    diag_item.diagnosisICDCode = code
                    diagnosis_list.append(diag_item)
                diagnosis_info.diagnosis = diagnosis_list
            else:
                diagnosis_info.diagnosis = []
        else:
            diagnosis_info.outPatientDiagnosisICDCode = ""
            diagnosis_info.diagnosis = []

        # 设置其他诊断字段为空
        diagnosis_info.outPatientDiagnosisChICDCode = ""
        diagnosis_info.diagnoseICDCodeOnAdmission = ""

        # 创建手术信息 - 修复数据结构以匹配PatientDataAnalyzer期望
        if surgery_codes:
            operations = []
            for code in surgery_codes:
                op_item = Mock()
                op_item.operationICDCode = code
                operations.append(op_item)
            diagnosis_info.operation = operations
        else:
            diagnosis_info.operation = []

        patient.Diagnosis = diagnosis_info

        return patient

    def test_complete_filtering_workflow(self):
        """测试完整的过滤工作流程"""
        # 创建有多种代码的患者
        patient = self._create_test_patient(
            yb_codes=["Y001", "Y003"],  # 匹配drug_rule_001和combo_rule_004
            diag_codes=["I10", "K59.1"],  # 匹配diag_rule_002和combo_rule_004
            surgery_codes=["S001"],  # 匹配surgery_rule_003
        )

        # 请求所有规则
        all_rule_ids = [rule.rule_id for rule in self.test_rules]

        # 启用过滤功能
        import core.rule_prefilter

        original_setting = getattr(core.rule_prefilter.settings, "ENABLE_RULE_PREFILTER", False)
        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = True

        try:
            # 执行过滤
            filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)

            # 验证过滤结果
            self.assertEqual(filter_result.original_rule_count, 5)

            # 应该包含相关规则和通用规则
            expected_rules = {
                "drug_rule_001",  # 匹配Y001
                "diag_rule_002",  # 匹配I10（前缀I1也匹配）
                "surgery_rule_003",  # 匹配S001
                "combo_rule_004",  # 匹配Y003, K59.1
                "universal_rule_005",  # 通用规则
            }

            filtered_rule_set = set(filter_result.filtered_rule_ids)
            self.assertEqual(filtered_rule_set, expected_rules)

            # 验证过滤率（这种情况下应该保留所有规则）
            self.assertEqual(filter_result.filter_rate, 0.0)

            # 验证过滤时间合理
            self.assertLess(filter_result.filter_time, 10.0)

        finally:
            # 恢复原始设置
            core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = original_setting

    def test_partial_matching_scenario(self):
        """测试部分匹配场景"""
        # 创建只有部分匹配的患者
        patient = self._create_test_patient(
            yb_codes=["Y001"],  # 只匹配drug_rule_001
            diag_codes=["Z99.9"],  # 不匹配任何诊断规则
        )

        all_rule_ids = [rule.rule_id for rule in self.test_rules]

        # 启用过滤功能
        import core.rule_prefilter

        original_setting = getattr(core.rule_prefilter.settings, "ENABLE_RULE_PREFILTER", False)
        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = True

        try:
            filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)

            # 应该只保留匹配的规则和通用规则
            expected_rules = {
                "drug_rule_001",  # 匹配Y001
                "universal_rule_005",  # 通用规则
            }

            filtered_rule_set = set(filter_result.filtered_rule_ids)
            self.assertEqual(filtered_rule_set, expected_rules)

            # 验证过滤率
            expected_filter_rate = 1 - (2 / 5)  # 保留2个，过滤3个
            self.assertAlmostEqual(filter_result.filter_rate, expected_filter_rate, places=2)

        finally:
            core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = original_setting

    def test_no_matching_scenario(self):
        """测试无匹配场景"""
        # 创建无匹配代码的患者
        patient = self._create_test_patient(
            yb_codes=["Z999"],  # 不匹配任何规则
            diag_codes=["Z99.9"],  # 不匹配任何规则
        )

        all_rule_ids = [rule.rule_id for rule in self.test_rules]

        # 启用过滤功能
        import core.rule_prefilter

        original_setting = getattr(core.rule_prefilter.settings, "ENABLE_RULE_PREFILTER", False)
        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = True

        try:
            filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)

            # 应该只保留通用规则
            expected_rules = {"universal_rule_005"}

            filtered_rule_set = set(filter_result.filtered_rule_ids)
            self.assertEqual(filtered_rule_set, expected_rules)

            # 验证过滤率
            expected_filter_rate = 1 - (1 / 5)  # 保留1个，过滤4个
            self.assertAlmostEqual(filter_result.filter_rate, expected_filter_rate, places=2)

        finally:
            core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = original_setting

    def test_prefix_matching(self):
        """测试前缀匹配功能"""
        # 创建匹配诊断前缀的患者
        patient = self._create_test_patient(
            diag_codes=["I15.0"]  # 应该匹配I1前缀
        )

        all_rule_ids = [rule.rule_id for rule in self.test_rules]

        # 启用过滤功能
        import core.rule_prefilter

        original_setting = getattr(core.rule_prefilter.settings, "ENABLE_RULE_PREFILTER", False)
        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = True

        try:
            filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)

            # 应该包含前缀匹配的诊断规则和通用规则
            expected_rules = {
                "diag_rule_002",  # 前缀I1匹配I15.0
                "universal_rule_005",  # 通用规则
            }

            filtered_rule_set = set(filter_result.filtered_rule_ids)
            self.assertEqual(filtered_rule_set, expected_rules)

        finally:
            core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = original_setting

    def test_disabled_filtering(self):
        """测试过滤功能禁用情况"""
        patient = self._create_test_patient(yb_codes=["Y001"])
        all_rule_ids = [rule.rule_id for rule in self.test_rules]

        # 确保过滤功能禁用
        import core.rule_prefilter

        original_setting = getattr(core.rule_prefilter.settings, "ENABLE_RULE_PREFILTER", False)
        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = False

        try:
            filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)

            # 应该返回所有规则（无过滤）
            self.assertEqual(filter_result.original_rule_count, 5)
            self.assertEqual(filter_result.filtered_rule_count, 5)
            self.assertEqual(filter_result.filter_rate, 0.0)
            self.assertEqual(set(filter_result.filtered_rule_ids), set(all_rule_ids))

        finally:
            core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = original_setting

    def test_performance_statistics(self):
        """测试性能统计功能"""
        patient = self._create_test_patient(yb_codes=["Y001"])
        all_rule_ids = [rule.rule_id for rule in self.test_rules]

        # 重置统计
        rule_prefilter.reset_stats()

        # 启用过滤功能
        import core.rule_prefilter

        original_setting = getattr(core.rule_prefilter.settings, "ENABLE_RULE_PREFILTER", False)
        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = True

        try:
            # 执行多次过滤
            for _ in range(5):
                rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)

            # 检查统计信息
            stats = rule_prefilter.get_performance_stats()

            self.assertEqual(stats["filter_count"], 5)
            self.assertTrue(stats["total_filter_time_ms"] > 0)
            self.assertTrue(stats["avg_filter_time_ms"] > 0)
            self.assertTrue(stats["total_original_rules"] > 0)
            self.assertTrue(stats["total_filtered_rules"] > 0)

            # 检查健康状态
            health_report = rule_prefilter.get_health_report()
            self.assertTrue(health_report["is_healthy"])
            self.assertTrue(health_report["filter_enabled"])
            self.assertTrue(health_report["index_ready"])

        finally:
            core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = original_setting

    def test_error_handling_and_fallback(self):
        """测试错误处理和降级机制"""
        patient = self._create_test_patient(yb_codes=["Y001"])
        all_rule_ids = [rule.rule_id for rule in self.test_rules]

        # 启用过滤功能
        import core.rule_prefilter

        original_setting = getattr(core.rule_prefilter.settings, "ENABLE_RULE_PREFILTER", False)
        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = True

        try:
            # 模拟患者数据分析器异常
            with unittest.mock.patch.object(patient_data_analyzer, "extract_codes", side_effect=Exception("模拟异常")):
                filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)

                # 应该降级到返回所有规则
                self.assertEqual(filter_result.filter_rate, 0.0)
                self.assertEqual(len(filter_result.filtered_rule_ids), len(all_rule_ids))

                # 检查降级统计
                stats = rule_prefilter.get_performance_stats()
                self.assertTrue(stats["fallback_count"] > 0)

        except ImportError:
            # unittest.mock在某些版本中可能不可用，跳过此测试
            self.skipTest("unittest.mock not available")

        finally:
            core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = original_setting

    def test_integration_performance(self):
        """测试集成性能"""
        # 创建多个不同的患者
        patients = [
            self._create_test_patient(yb_codes=["Y001"]),
            self._create_test_patient(diag_codes=["I10"]),
            self._create_test_patient(surgery_codes=["S001"]),
            self._create_test_patient(yb_codes=["Y003"], diag_codes=["K59.1"]),
        ]

        all_rule_ids = [rule.rule_id for rule in self.test_rules]

        # 启用过滤功能
        import core.rule_prefilter

        original_setting = getattr(core.rule_prefilter.settings, "ENABLE_RULE_PREFILTER", False)
        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = True

        try:
            import time

            start_time = time.perf_counter()

            # 执行批量过滤
            results = []
            for patient in patients:
                for _ in range(10):  # 每个患者过滤10次
                    result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)
                    results.append(result)

            total_time = (time.perf_counter() - start_time) * 1000
            avg_time_per_filter = total_time / len(results)

            print("\n=== 集成性能测试结果 ===")
            print(f"总过滤次数: {len(results)}")
            print(f"总耗时: {total_time:.2f}ms")
            print(f"平均每次过滤: {avg_time_per_filter:.3f}ms")

            # 验证性能目标
            self.assertLess(avg_time_per_filter, 5.0, f"集成过滤时间过长: {avg_time_per_filter:.3f}ms > 5.0ms")

            # 验证结果一致性
            for result in results:
                self.assertTrue(result.filter_time >= 0)
                self.assertTrue(0 <= result.filter_rate <= 1)
                self.assertIsInstance(result.filtered_rule_ids, list)

        finally:
            core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = original_setting


if __name__ == "__main__":
    unittest.main(verbosity=2)
