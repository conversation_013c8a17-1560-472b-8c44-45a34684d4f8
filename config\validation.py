"""
配置验证模块

提供配置项验证、健康检查和配置模板验证功能，
解决规则注册配置复杂性带来的风险。
"""

import asyncio
import logging
from typing import Any
from urllib.parse import urlparse

import httpx

from config.settings import get_settings

logger = logging.getLogger(__name__)


class ConfigValidationError(Exception):
    """配置验证错误"""

    def __init__(self, message: str, field: str = None, details: dict = None):
        self.message = message
        self.field = field
        self.details = details or {}
        super().__init__(self.message)


class RuleRegistrationConfigValidator:
    """规则注册配置验证器"""

    def __init__(self):
        self.settings = get_settings()
        self.validation_errors: list[ConfigValidationError] = []
        self.validation_warnings: list[str] = []

    def validate_all(self) -> tuple[bool, list[str], list[str]]:
        """
        验证所有规则注册相关配置

        Returns:
            Tuple[bool, List[str], List[str]]: (是否通过验证, 错误列表, 警告列表)
        """
        self.validation_errors.clear()
        self.validation_warnings.clear()

        try:
            # 1. 验证基础配置
            self._validate_basic_config()

            # 2. 验证网络配置
            self._validate_network_config()

            # 3. 验证性能配置
            self._validate_performance_config()

            # 4. 验证超时配置
            self._validate_timeout_config()

            # 5. 验证Worker配置
            self._validate_worker_config()

        except Exception as e:
            logger.error(f"配置验证过程中发生异常: {e}", exc_info=True)
            self.validation_errors.append(
                ConfigValidationError(f"配置验证异常: {e}", details={"exception": str(e)})
            )

        # 整理结果
        error_messages = [error.message for error in self.validation_errors]
        is_valid = len(self.validation_errors) == 0

        return is_valid, error_messages, self.validation_warnings

    def _validate_basic_config(self):
        """验证基础配置项"""
        # 验证功能开关
        if not isinstance(self.settings.RULE_REGISTRATION_ENABLED, bool):
            self.validation_errors.append(
                ConfigValidationError(
                    "RULE_REGISTRATION_ENABLED 必须是布尔值",
                    field="RULE_REGISTRATION_ENABLED"
                )
            )

        # 验证服务地址
        if not self.settings.RULE_REGISTRATION_HOST:
            self.validation_errors.append(
                ConfigValidationError(
                    "RULE_REGISTRATION_HOST 不能为空",
                    field="RULE_REGISTRATION_HOST"
                )
            )
        else:
            self._validate_url(self.settings.RULE_REGISTRATION_HOST, "RULE_REGISTRATION_HOST")

    def _validate_network_config(self):
        """验证网络相关配置"""
        # 验证重试次数
        if not isinstance(self.settings.RULE_REGISTRATION_MAX_RETRIES, int) \
            or self.settings.RULE_REGISTRATION_MAX_RETRIES < 0:
            self.validation_errors.append(
                ConfigValidationError(
                    "RULE_REGISTRATION_MAX_RETRIES 必须是非负整数",
                    field="RULE_REGISTRATION_MAX_RETRIES"
                )
            )
        elif self.settings.RULE_REGISTRATION_MAX_RETRIES > 10:
            self.validation_warnings.append(
                "RULE_REGISTRATION_MAX_RETRIES 设置过高(>10)，可能导致长时间阻塞"
            )

    def _validate_performance_config(self):
        """验证性能相关配置"""
        # 验证批处理大小
        if not isinstance(self.settings.RULE_REGISTRATION_BATCH_SIZE, int) \
            or self.settings.RULE_REGISTRATION_BATCH_SIZE <= 0:
            self.validation_errors.append(
                ConfigValidationError(
                    "RULE_REGISTRATION_BATCH_SIZE 必须是正整数",
                    field="RULE_REGISTRATION_BATCH_SIZE"
                )
            )
        elif self.settings.RULE_REGISTRATION_BATCH_SIZE > 1000:
            self.validation_warnings.append(
                "RULE_REGISTRATION_BATCH_SIZE 设置过高(>1000)，可能导致内存压力"
            )

        # 验证队列大小
        if not isinstance(self.settings.RULE_REGISTRATION_QUEUE_MAX_SIZE, int) \
            or self.settings.RULE_REGISTRATION_QUEUE_MAX_SIZE <= 0:
            self.validation_errors.append(
                ConfigValidationError(
                    "RULE_REGISTRATION_QUEUE_MAX_SIZE 必须是正整数",
                    field="RULE_REGISTRATION_QUEUE_MAX_SIZE"
                )
            )

    def _validate_timeout_config(self):
        """验证超时配置"""
        # 验证网络超时
        if not isinstance(self.settings.RULE_REGISTRATION_TIMEOUT, int | float) \
            or self.settings.RULE_REGISTRATION_TIMEOUT <= 0:
            self.validation_errors.append(
                ConfigValidationError(
                    "RULE_REGISTRATION_TIMEOUT 必须是正数",
                    field="RULE_REGISTRATION_TIMEOUT"
                )
            )
        elif self.settings.RULE_REGISTRATION_TIMEOUT > 300:
            self.validation_warnings.append(
                "RULE_REGISTRATION_TIMEOUT 设置过高(>300秒)，可能导致长时间等待"
            )

        # 验证任务超时
        if not isinstance(self.settings.RULE_REGISTRATION_TASK_TIMEOUT, int | float) \
            or self.settings.RULE_REGISTRATION_TASK_TIMEOUT <= 0:
            self.validation_errors.append(
                ConfigValidationError(
                    "RULE_REGISTRATION_TASK_TIMEOUT 必须是正数",
                    field="RULE_REGISTRATION_TASK_TIMEOUT"
                )
            )

        # 检查超时配置的合理性
        if (self.settings.RULE_REGISTRATION_TASK_TIMEOUT <= self.settings.RULE_REGISTRATION_TIMEOUT):
            self.validation_warnings.append(
                "RULE_REGISTRATION_TASK_TIMEOUT 应该大于 RULE_REGISTRATION_TIMEOUT"
            )

    def _validate_worker_config(self):
        """验证Worker配置"""
        if not isinstance(self.settings.REGISTRATION_WORKER_COUNT, int) or self.settings.REGISTRATION_WORKER_COUNT <= 0:
            self.validation_errors.append(
                ConfigValidationError(
                    "REGISTRATION_WORKER_COUNT 必须是正整数",
                    field="REGISTRATION_WORKER_COUNT"
                )
            )
        elif self.settings.REGISTRATION_WORKER_COUNT > 8:
            self.validation_warnings.append(
                "REGISTRATION_WORKER_COUNT 设置过高(>8)，可能导致资源竞争"
            )

    def _validate_url(self, url: str, field_name: str):
        """验证URL格式"""
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                self.validation_errors.append(
                    ConfigValidationError(
                        f"{field_name} URL格式无效: {url}",
                        field=field_name,
                        details={"url": url}
                    )
                )
            elif parsed.scheme not in ["http", "https"]:
                self.validation_errors.append(
                    ConfigValidationError(
                        f"{field_name} 必须使用 http 或 https 协议",
                        field=field_name,
                        details={"url": url, "scheme": parsed.scheme},
                    )
                )
        except Exception as e:
            self.validation_errors.append(
                ConfigValidationError(
                    f"{field_name} URL解析失败: {e}",
                    field=field_name,
                    details={"url": url, "error": str(e)}
                )
            )


class RuleRegistrationHealthChecker:
    """规则注册服务健康检查器"""

    def __init__(self):
        self.settings = get_settings()

    async def check_registration_service_health(self) -> dict[str, Any]:
        """
        检查规则注册服务的健康状态

        Returns:
            Dict[str, Any]: 健康检查结果
        """
        result = {
            "healthy": False,
            "service_url": self.settings.RULE_REGISTRATION_HOST,
            "response_time": None,
            "error": None,
            "details": {},
        }

        if not self.settings.RULE_REGISTRATION_ENABLED:
            result.update({
                "healthy": True,
                "message": "规则注册功能已禁用，跳过健康检查"
            })
            return result

        try:
            import time

            start_time = time.perf_counter()

            async with httpx.AsyncClient(timeout=self.settings.RULE_REGISTRATION_TIMEOUT) as client:
                # 尝试访问健康检查端点
                health_url = f"{self.settings.RULE_REGISTRATION_HOST.rstrip('/')}/health"
                response = await client.get(health_url)

                response_time = time.perf_counter() - start_time
                result["response_time"] = response_time

                if response.status_code == 200:
                    result["healthy"] = True
                    result["details"] = {
                        "status_code": response.status_code,
                        "response_time": response_time
                    }
                else:
                    result["error"] = f"服务返回状态码: {response.status_code}"
                    result["details"] = {
                        "status_code": response.status_code,
                        "response_text": response.text[:200],  # 限制响应文本长度
                    }

        except httpx.TimeoutException:
            result["error"] = f"连接超时 (>{self.settings.RULE_REGISTRATION_TIMEOUT}秒)"
        except httpx.ConnectError:
            result["error"] = "无法连接到服务"
        except Exception as e:
            result["error"] = f"健康检查异常: {e}"
            logger.error(f"规则注册服务健康检查异常: {e}", exc_info=True)

        return result

    async def check_configuration_health(self) -> dict[str, Any]:
        """
        检查配置的健康状态

        Returns:
            Dict[str, Any]: 配置健康检查结果
        """
        validator = RuleRegistrationConfigValidator()
        is_valid, errors, warnings = validator.validate_all()

        return {
            "healthy": is_valid,
            "errors": errors,
            "warnings": warnings,
            "total_errors": len(errors),
            "total_warnings": len(warnings),
        }


def validate_rule_registration_config() -> tuple[bool, list[str], list[str]]:
    """
    验证规则注册配置的便捷函数

    Returns:
        Tuple[bool, List[str], List[str]]: (是否通过验证, 错误列表, 警告列表)
    """
    validator = RuleRegistrationConfigValidator()
    return validator.validate_all()


async def check_rule_registration_health() -> dict[str, Any]:
    """
    检查规则注册服务健康状态的便捷函数

    Returns:
        Dict[str, Any]: 健康检查结果
    """
    checker = RuleRegistrationHealthChecker()

    # 并行执行配置检查和服务检查
    config_health, service_health = await asyncio.gather(
        checker.check_configuration_health(),
        checker.check_registration_service_health(),
        return_exceptions=True
    )

    # 处理异常情况
    if isinstance(config_health, Exception):
        config_health = {
            "healthy": False,
            "error": f"配置检查异常: {config_health}",
            "errors": [str(config_health)],
            "warnings": [],
        }

    if isinstance(service_health, Exception):
        service_health = {
            "healthy": False,
            "error": f"服务检查异常: {service_health}"
        }

    return {
        "overall_healthy": config_health.get("healthy", False) and service_health.get("healthy", False),
        "configuration": config_health,
        "service": service_health,
        "timestamp": asyncio.get_event_loop().time(),
    }
