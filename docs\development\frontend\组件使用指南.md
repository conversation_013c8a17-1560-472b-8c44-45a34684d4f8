# 组件使用指南

## 快速开始

### 安装和配置

1. **引入设计系统**
```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'
import designSystem from './design-system/components'

const app = createApp(App)

// 全局注册组件
app.use(designSystem)

app.mount('#app')
```

2. **按需引入**
```javascript
// 在组件中按需引入
import { RButton, RCard } from '@/design-system/components'

export default {
  components: {
    RButton,
    RCard
  }
}
```

3. **引入设计Token**
```javascript
// 在组件中使用设计Token
import { designTokens } from '@/design-system/tokens'

// 在样式中使用
const buttonStyle = {
  backgroundColor: designTokens.colors.semantic.primary.main,
  padding: designTokens.spacing.semantic.component.md
}
```

## 组件详细使用说明

### RButton 按钮组件

#### 基础用法
```vue
<template>
  <!-- 基础按钮 -->
  <RButton>默认按钮</RButton>
  
  <!-- 不同变体 -->
  <RButton variant="primary">主要按钮</RButton>
  <RButton variant="secondary">次要按钮</RButton>
  <RButton variant="success">成功按钮</RButton>
  <RButton variant="warning">警告按钮</RButton>
  <RButton variant="error">错误按钮</RButton>
</template>
```

#### 尺寸和状态
```vue
<template>
  <!-- 不同尺寸 -->
  <RButton size="xs">超小按钮</RButton>
  <RButton size="sm">小按钮</RButton>
  <RButton size="md">中等按钮</RButton>
  <RButton size="lg">大按钮</RButton>
  <RButton size="xl">超大按钮</RButton>
  
  <!-- 状态 -->
  <RButton :loading="true">加载中</RButton>
  <RButton :disabled="true">禁用按钮</RButton>
</template>
```

#### 图标和形状
```vue
<template>
  <!-- 带图标 -->
  <RButton :icon="Plus" icon-position="left">添加</RButton>
  <RButton :icon="Download" icon-position="right">下载</RButton>
  
  <!-- 只有图标 -->
  <RButton :icon="Search" />
  
  <!-- 形状变体 -->
  <RButton :round="true">圆角按钮</RButton>
  <RButton :circle="true" :icon="Plus" />
  
  <!-- 块级按钮 -->
  <RButton :block="true">块级按钮</RButton>
</template>
```

#### 事件处理
```vue
<template>
  <RButton @click="handleClick">点击我</RButton>
</template>

<script setup>
const handleClick = (event) => {
  console.log('按钮被点击', event)
}
</script>
```

### RCard 卡片组件

#### 基础用法
```vue
<template>
  <!-- 简单卡片 -->
  <RCard title="卡片标题">
    <p>这是卡片内容</p>
  </RCard>
  
  <!-- 自定义头部 -->
  <RCard>
    <template #header>
      <div style="display: flex; justify-content: space-between;">
        <h3>自定义标题</h3>
        <RButton size="sm">操作</RButton>
      </div>
    </template>
    
    <p>卡片内容</p>
  </RCard>
</template>
```

#### 变体和交互
```vue
<template>
  <!-- 不同变体 -->
  <RCard variant="default">默认卡片</RCard>
  <RCard variant="outlined">边框卡片</RCard>
  <RCard variant="elevated">阴影卡片</RCard>
  <RCard variant="filled">填充卡片</RCard>
  
  <!-- 交互状态 -->
  <RCard :hoverable="true">悬浮效果</RCard>
  <RCard :clickable="true" @click="handleCardClick">可点击卡片</RCard>
</template>
```

#### 完整示例
```vue
<template>
  <RCard 
    title="规则详情"
    size="lg"
    variant="outlined"
    :hoverable="true"
  >
    <template #actions>
      <RButton size="sm" variant="text">编辑</RButton>
      <RButton size="sm" variant="text">删除</RButton>
    </template>
    
    <div class="rule-info">
      <p><strong>规则名称：</strong>{{ rule.name }}</p>
      <p><strong>状态：</strong>{{ rule.status }}</p>
      <p><strong>更新时间：</strong>{{ rule.updatedAt }}</p>
    </div>
    
    <template #footer>
      <div style="display: flex; gap: 8px;">
        <RButton variant="secondary">取消</RButton>
        <RButton variant="primary">确认</RButton>
      </div>
    </template>
  </RCard>
</template>
```

### RTable 表格组件

#### 基础配置
```vue
<template>
  <RTable 
    :data="tableData"
    :columns="columns"
    :bordered="true"
    :striped="true"
    :hoverable="true"
  />
</template>

<script setup>
import { ref } from 'vue'

const tableData = ref([
  { id: 1, name: '规则1', status: 'READY', date: '2025-06-30' },
  { id: 2, name: '规则2', status: 'NEW', date: '2025-06-29' }
])

const columns = ref([
  {
    key: 'name',
    title: '规则名称',
    sortable: true,
    width: 200
  },
  {
    key: 'status',
    title: '状态',
    slot: 'status', // 使用插槽自定义渲染
    align: 'center'
  },
  {
    key: 'date',
    title: '日期',
    formatter: (value) => new Date(value).toLocaleDateString()
  }
])
</script>
```

#### 自定义渲染
```vue
<template>
  <RTable :data="tableData" :columns="columns">
    <!-- 状态列自定义渲染 -->
    <template #status="{ row, value }">
      <el-tag :type="getStatusType(value)">
        {{ getStatusText(value) }}
      </el-tag>
    </template>
    
    <!-- 操作列 -->
    <template #actions="{ row }">
      <RButton size="sm" @click="editRow(row)">编辑</RButton>
      <RButton size="sm" variant="error" @click="deleteRow(row)">删除</RButton>
    </template>
  </RTable>
</template>
```

#### 分页和排序
```vue
<template>
  <RTable 
    :data="tableData"
    :columns="columns"
    :show-pagination="true"
    :page-size="10"
    :page-sizes="[10, 20, 50]"
    @sort-change="handleSortChange"
    @page-change="handlePageChange"
  />
</template>

<script setup>
const handleSortChange = (sortConfig) => {
  console.log('排序变化:', sortConfig)
  // { key: 'name', order: 'asc' }
}

const handlePageChange = (page) => {
  console.log('页码变化:', page)
}
</script>
```

### RModal 模态框组件

#### 基础用法
```vue
<template>
  <RButton @click="showModal = true">打开模态框</RButton>
  
  <RModal 
    v-model:visible="showModal"
    title="确认操作"
    size="md"
    :show-default-footer="true"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <p>确定要执行此操作吗？</p>
  </RModal>
</template>

<script setup>
import { ref } from 'vue'

const showModal = ref(false)

const handleConfirm = () => {
  console.log('确认操作')
  showModal.value = false
}

const handleCancel = () => {
  console.log('取消操作')
}
</script>
```

#### 自定义内容
```vue
<template>
  <RModal 
    v-model:visible="visible"
    title="用户信息"
    size="lg"
    :mask-closable="false"
  >
    <!-- 自定义头部 -->
    <template #header>
      <div class="custom-header">
        <h2>编辑用户</h2>
        <span class="user-id">#{{ user.id }}</span>
      </div>
    </template>
    
    <!-- 表单内容 -->
    <el-form :model="user" label-width="80px">
      <el-form-item label="姓名">
        <el-input v-model="user.name" />
      </el-form-item>
      <el-form-item label="邮箱">
        <el-input v-model="user.email" />
      </el-form-item>
    </el-form>
    
    <!-- 自定义底部 -->
    <template #footer>
      <div class="custom-footer">
        <RButton variant="secondary" @click="resetForm">重置</RButton>
        <RButton variant="primary" :loading="saving" @click="saveUser">
          保存
        </RButton>
      </div>
    </template>
  </RModal>
</template>
```

## 常见使用模式

### 1. 表单操作模式
```vue
<template>
  <div class="form-actions">
    <RButton variant="secondary" @click="handleReset">重置</RButton>
    <RButton variant="primary" :loading="submitting" @click="handleSubmit">
      提交
    </RButton>
  </div>
</template>
```

### 2. 列表操作模式
```vue
<template>
  <div class="list-actions">
    <RButton :icon="Plus" @click="handleAdd">添加</RButton>
    <RButton :icon="Download" @click="handleExport">导出</RButton>
    <RButton :icon="Refresh" @click="handleRefresh">刷新</RButton>
  </div>
</template>
```

### 3. 卡片网格模式
```vue
<template>
  <div class="card-grid">
    <RCard 
      v-for="item in items" 
      :key="item.id"
      :hoverable="true"
      :clickable="true"
      @click="handleItemClick(item)"
    >
      <h3>{{ item.title }}</h3>
      <p>{{ item.description }}</p>
      
      <template #footer>
        <div class="card-actions">
          <RButton size="sm" variant="text">查看</RButton>
          <RButton size="sm" variant="text">编辑</RButton>
        </div>
      </template>
    </RCard>
  </div>
</template>

<style scoped>
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}
</style>
```

## 主题定制

### 1. CSS变量覆盖
```css
:root {
  --r-color-primary: #1890ff;
  --r-color-success: #52c41a;
  --r-color-warning: #faad14;
  --r-color-error: #f5222d;
  
  --r-font-size-base: 14px;
  --r-border-radius-base: 6px;
  --r-spacing-base: 8px;
}
```

### 2. 设计Token定制
```javascript
// custom-tokens.js
import { designTokens } from '@/design-system/tokens'

export const customTokens = {
  ...designTokens,
  colors: {
    ...designTokens.colors,
    semantic: {
      ...designTokens.colors.semantic,
      primary: {
        main: '#1890ff',
        light: '#40a9ff',
        dark: '#096dd9'
      }
    }
  }
}
```

## 性能优化建议

### 1. 按需加载
```javascript
// 只引入需要的组件
import { RButton } from '@/design-system/components/base/RButton.vue'
```

### 2. 合理使用v-memo
```vue
<template>
  <RCard v-for="item in items" :key="item.id" v-memo="[item.id, item.status]">
    <!-- 卡片内容 -->
  </RCard>
</template>
```

### 3. 避免不必要的响应式
```javascript
// 使用shallowRef处理大量数据
import { shallowRef } from 'vue'

const tableData = shallowRef([])
```

## 常见问题

### Q: 如何自定义组件样式？
A: 可以通过CSS变量、类名覆盖或传入自定义样式对象来定制样式。

### Q: 组件支持哪些浏览器？
A: 支持现代浏览器，包括Chrome 60+、Firefox 60+、Safari 12+、Edge 79+。

### Q: 如何处理组件的类型提示？
A: 项目已包含完整的TypeScript类型定义，IDE会自动提供类型提示。

### Q: 组件是否支持国际化？
A: 组件本身不包含文本，国际化需要在使用时通过props传入对应语言的文本。
