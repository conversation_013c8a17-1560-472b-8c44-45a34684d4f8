"""
性能测试专用配置
提供性能测试所需的fixtures和配置
"""

import time
from typing import Any

import psutil
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from models.database import Base

# ============================================================================
# 性能测试专用fixtures
# ============================================================================

@pytest.fixture
def performance_monitor():
    """性能监控器"""
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.end_time = None
            self.start_memory = None
            self.end_memory = None

        def start(self):
            self.start_time = time.time()
            self.start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        def stop(self):
            self.end_time = time.time()
            self.end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        def get_metrics(self) -> dict[str, Any]:
            return {
                "execution_time": self.end_time - self.start_time if self.end_time and self.start_time else 0,
                "memory_usage": self.end_memory - self.start_memory if self.end_memory and self.start_memory else 0,
                "peak_memory": self.end_memory if self.end_memory else 0,
            }

    return PerformanceMonitor()


@pytest.fixture
def performance_thresholds():
    """性能测试阈值"""
    return {
        "max_response_time": 2.0,  # 最大响应时间（秒）
        "max_memory_usage": 200,   # 最大内存使用（MB）
        "max_cpu_usage": 90,       # 最大CPU使用率（%）
    }


# ============================================================================
# 性能测试配置
# ============================================================================

@pytest.fixture(autouse=True)
def performance_test_setup():
    """性能测试自动设置"""
    # 性能测试需要稳定的环境
    # 可能需要预热、清理缓存等操作
    yield



@pytest.fixture
def db_session(shared_db_engine):
    """数据库会话（与client fixture兼容）"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=shared_db_engine)  # noqa: N806
    session = TestingSessionLocal()

    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def shared_db_engine():
    """共享的测试数据库引擎"""
    import os

    from sqlalchemy import event

    # 检查是否设置了使用MySQL进行集成测试
    use_mysql = os.environ.get("USE_MYSQL_FOR_INTEGRATION_TEST", "false").lower() == "true"

    if use_mysql:
        # 使用MySQL进行集成测试
        database_url = "mysql+pymysql://rule_user:mysql_password@192.168.100.192:3306/rule_service_test"
        engine = create_engine(
            database_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=3600,
            pool_pre_ping=True,
        )

        # 删除并重新创建所有表
        Base.metadata.drop_all(engine)
        Base.metadata.create_all(engine)
        yield engine
        Base.metadata.drop_all(engine)
        engine.dispose()
    else:
        # 默认使用SQLite内存数据库
        engine = create_engine(
            "sqlite:///:memory:",
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
        )

        # 启用SQLite外键约束
        @event.listens_for(engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.close()

        # 删除并重新创建所有表
        Base.metadata.drop_all(engine)
        Base.metadata.create_all(engine)
        yield engine
        Base.metadata.drop_all(engine)
        engine.dispose()



@pytest.fixture
def client(shared_db_engine):
    """FastAPI测试客户端"""
    import os

    from fastapi import FastAPI
    from fastapi.testclient import TestClient

    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    os.environ["RUN_MODE"] = "TEST"
    os.environ["MASTER_API_SECRET_KEY"] = "a_very_secret_key_for_development"

    # 创建简化的测试应用
    app = FastAPI(title="Test App", version="1.0.0")

    # 只包含规则详情相关的路由
    from api.routers.master.rule_details import rule_details_router

    app.include_router(rule_details_router)

    # 覆盖数据库依赖为测试数据库
    from core.db_session import get_db_session

    def override_get_db_session():
        # 使用共享的测试数据库引擎
        TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=shared_db_engine)  # noqa: N806
        session = TestingSessionLocal()
        try:
            yield session
        finally:
            session.close()

    app.dependency_overrides[get_db_session] = override_get_db_session

    # 添加统一错误处理
    try:
        from api.middleware.error_handling import UnifiedErrorHandlingMiddleware

        UnifiedErrorHandlingMiddleware.register_handlers(app)
    except ImportError:
        # 如果错误处理中间件不存在，跳过
        pass

    with TestClient(app) as test_client:
        yield test_client

    # 清理环境变量
    os.environ.pop("TESTING", None)
    os.environ.pop("RUN_MODE", None)
    os.environ.pop("MASTER_API_SECRET_KEY", None)
