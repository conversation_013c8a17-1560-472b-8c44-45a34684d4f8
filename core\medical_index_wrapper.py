"""
医疗专用索引包装器
专门针对医疗规则系统的多类型代码索引实现

主要功能：
1. 医保代码索引 (yb_code, fee_whole_code, fee_code_prefix)
2. 诊断代码索引 (diag_whole_code, diag_code_prefix)  
3. 手术代码索引 (从 extended_fields 提取)
4. 与现有 PatientDataAnalyzer 和 RuleIndexManager 深度集成

技术特点：
- 自动索引构建和维护
- 多类型代码统一管理
- 高性能过滤算法
- 与现有系统无缝集成
"""

import json
import time
from dataclasses import dataclass
from typing import Any

from core.advanced_index_engine import MultiTypeIndexEngine, advanced_index_engine
from core.logging.logging_system import log as logger
from core.patient_data_analyzer import PatientCodeExtraction


@dataclass
class MedicalIndexStats:
    """医疗索引统计信息"""
    total_rules: int
    indexed_rules: int
    yb_code_count: int
    diag_code_count: int
    surgery_code_count: int
    generic_rule_count: int
    build_time_ms: float
    memory_usage_mb: float


@dataclass
class FilterResult:
    """过滤结果"""
    original_rule_count: int
    filtered_rule_count: int
    filter_rate: float
    filter_time_ms: float
    relevant_rules: set[str]


class MedicalIndexWrapper:
    """
    医疗专用索引包装器

    整合多种医疗代码索引，提供统一的规则过滤接口
    与现有 PatientDataAnalyzer 和 RuleIndexManager 深度集成
    """

    def __init__(self, index_engine: MultiTypeIndexEngine = None):
        self.index_engine = index_engine or advanced_index_engine

        # 医疗代码索引
        self._yb_code_index_name = "medical_yb_codes"
        self._diag_code_index_name = "medical_diag_codes"  
        self._surgery_code_index_name = "medical_surgery_codes"

        # 通用规则集合（无特定代码限制的规则）
        self._generic_rules: set[str] = set()

        # 统计信息
        self._build_time = 0.0
        self._last_build_timestamp = 0.0
        self._filter_count = 0
        self._total_filter_time = 0.0

        logger.info("医疗索引包装器初始化完成")

    def build_indexes_from_rule_details(self, rule_details: list[dict[str, Any]]) -> MedicalIndexStats:
        """
        从规则详情列表构建医疗索引

        Args:
            rule_details: 规则详情列表，包含各种医疗代码字段

        Returns:
            MedicalIndexStats: 索引构建统计信息
        """
        start_time = time.perf_counter()

        # 初始化数据容器
        yb_code_data: dict[str, set[str]] = {}
        diag_code_data: dict[str, set[str]] = {}
        surgery_code_data: dict[str, set[str]] = {}

        total_rules = len(rule_details)
        indexed_rules = 0
        generic_rule_count = 0

        logger.info(f"开始构建医疗索引，规则总数: {total_rules}")

        for rule_detail in rule_details:
            rule_id = str(rule_detail.get('id', ''))
            if not rule_id:
                continue

            has_specific_code = False

            # 1. 处理医保代码
            yb_codes = self._extract_yb_codes_from_rule(rule_detail)
            if yb_codes:
                has_specific_code = True
                for code in yb_codes:
                    if code not in yb_code_data:
                        yb_code_data[code] = set()
                    yb_code_data[code].add(rule_id)

            # 2. 处理诊断代码  
            diag_codes = self._extract_diag_codes_from_rule(rule_detail)
            if diag_codes:
                has_specific_code = True
                for code in diag_codes:
                    if code not in diag_code_data:
                        diag_code_data[code] = set()
                    diag_code_data[code].add(rule_id)

            # 3. 处理手术代码
            surgery_codes = self._extract_surgery_codes_from_rule(rule_detail)  
            if surgery_codes:
                has_specific_code = True
                for code in surgery_codes:
                    if code not in surgery_code_data:
                        surgery_code_data[code] = set()
                    surgery_code_data[code].add(rule_id)

            # 4. 记录通用规则（无特定代码限制）
            if not has_specific_code:
                self._generic_rules.add(rule_id)
                generic_rule_count += 1
            else:
                indexed_rules += 1

        # 构建各类型索引
        if yb_code_data:
            self.index_engine.create_index(
                self._yb_code_index_name, 
                yb_code_data, 
                index_type='hash'  # 医保代码主要是精确匹配
            )
            logger.info(f"医保代码索引构建完成，代码数量: {len(yb_code_data)}")

        if diag_code_data:
            self.index_engine.create_index(
                self._diag_code_index_name,
                diag_code_data,
                index_type='trie'  # 诊断代码支持前缀匹配
            )
            logger.info(f"诊断代码索引构建完成，代码数量: {len(diag_code_data)}")

        if surgery_code_data:
            self.index_engine.create_index(
                self._surgery_code_index_name,
                surgery_code_data, 
                index_type='hash'  # 手术代码主要是精确匹配
            )
            logger.info(f"手术代码索引构建完成，代码数量: {len(surgery_code_data)}")

        # 记录构建时间
        self._build_time = (time.perf_counter() - start_time) * 1000
        self._last_build_timestamp = time.time()

        # 计算总内存使用
        total_memory = sum(
            stats.memory_usage_mb for stats in self.index_engine.get_all_stats().values()
        )

        stats = MedicalIndexStats(
            total_rules=total_rules,
            indexed_rules=indexed_rules,
            yb_code_count=len(yb_code_data),
            diag_code_count=len(diag_code_data), 
            surgery_code_count=len(surgery_code_data),
            generic_rule_count=generic_rule_count,
            build_time_ms=self._build_time,
            memory_usage_mb=total_memory
        )

        logger.info(
            f"医疗索引构建完成 - 耗时: {self._build_time:.2f}ms, "
            f"索引规则: {indexed_rules}/{total_rules}, "
            f"通用规则: {generic_rule_count}, "
            f"内存使用: {total_memory:.2f}MB"
        )

        return stats

    def filter_relevant_rules(
            self,
            patient_codes: PatientCodeExtraction, 
            requested_rule_ids: set[str]
        ) -> FilterResult:
        """
        根据患者代码过滤相关规则

        Args:
            patient_codes: 患者代码提取结果
            requested_rule_ids: 请求的规则ID集合

        Returns:
            FilterResult: 过滤结果
        """
        start_time = time.perf_counter()

        original_count = len(requested_rule_ids)
        relevant_rules = set()

        # 1. 添加通用规则（始终相关）
        relevant_rules.update(self._generic_rules & requested_rule_ids)

        # 2. 根据医保代码查找相关规则
        for yb_code in patient_codes.yb_codes:
            rule_ids = self.index_engine.query_index(self._yb_code_index_name, yb_code)
            relevant_rules.update(rule_ids & requested_rule_ids)

        # 3. 根据诊断代码查找相关规则
        for diag_code in patient_codes.diag_codes:
            # 精确匹配
            rule_ids = self.index_engine.query_index(self._diag_code_index_name, diag_code)
            relevant_rules.update(rule_ids & requested_rule_ids)

            # 前缀匹配（支持诊断代码的层级结构）
            if len(diag_code) > 3:  # ICD-10代码通常有层级结构
                prefix_rule_ids = self.index_engine.prefix_query_index(
                    self._diag_code_index_name, 
                    diag_code[:3]  # 使用前3位作为前缀
                )
                relevant_rules.update(prefix_rule_ids & requested_rule_ids)

        # 4. 根据手术代码查找相关规则
        for surgery_code in patient_codes.surgery_codes:
            rule_ids = self.index_engine.query_index(self._surgery_code_index_name, surgery_code)
            relevant_rules.update(rule_ids & requested_rule_ids)

        # 计算过滤统计
        filtered_count = len(relevant_rules)
        filter_rate = 1.0 - (filtered_count / original_count) if original_count > 0 else 0.0
        filter_time = (time.perf_counter() - start_time) * 1000

        # 更新统计信息
        self._filter_count += 1
        self._total_filter_time += filter_time

        result = FilterResult(
            original_rule_count=original_count,
            filtered_rule_count=filtered_count,
            filter_rate=filter_rate,
            filter_time_ms=filter_time,
            relevant_rules=relevant_rules
        )

        logger.debug(
            f"规则过滤完成 - 原始规则: {original_count}, "
            f"相关规则: {filtered_count}, "
            f"过滤率: {filter_rate:.1%}, "
            f"耗时: {filter_time:.3f}ms"
        )

        return result

    def _extract_yb_codes_from_rule(self, rule_detail: dict[str, Any]) -> set[str]:
        """从规则详情中提取医保代码"""
        codes = set()

        # 提取 yb_code 字段
        yb_code = rule_detail.get('yb_code')
        if yb_code and isinstance(yb_code, str):
            codes.add(yb_code.strip())

        # 提取 fee_whole_code 字段
        fee_whole_code = rule_detail.get('fee_whole_code')
        if fee_whole_code and isinstance(fee_whole_code, str):
            codes.add(fee_whole_code.strip())

        # 提取 fee_code_prefix 字段（前缀码也作为精确匹配项）
        fee_code_prefix = rule_detail.get('fee_code_prefix')
        if fee_code_prefix and isinstance(fee_code_prefix, str):
            codes.add(fee_code_prefix.strip())

        return {code for code in codes if code}

    def _extract_diag_codes_from_rule(self, rule_detail: dict[str, Any]) -> set[str]:
        """从规则详情中提取诊断代码"""
        codes = set()

        # 提取 diag_whole_code 字段
        diag_whole_code = rule_detail.get('diag_whole_code')
        if diag_whole_code and isinstance(diag_whole_code, str):
            codes.add(diag_whole_code.strip())

        # 提取 diag_code_prefix 字段
        diag_code_prefix = rule_detail.get('diag_code_prefix')
        if diag_code_prefix and isinstance(diag_code_prefix, str):
            codes.add(diag_code_prefix.strip())

        return {code for code in codes if code}

    def _extract_surgery_codes_from_rule(self, rule_detail: dict[str, Any]) -> set[str]:
        """从规则详情的 extended_fields 中提取手术代码"""
        codes = set()

        # 解析 extended_fields JSON 字段
        extended_fields = rule_detail.get('extended_fields')
        if not extended_fields:
            return codes

        try:
            if isinstance(extended_fields, str):
                extended_data = json.loads(extended_fields)
            elif isinstance(extended_fields, dict):
                extended_data = extended_fields
            else:
                return codes

            # 提取各种可能的手术代码字段
            surgery_fields = [
                'surgery_code', 'operation_code', 'surgical_procedure_code',
                'ss_code', 'ssbm', 'procedure_code'
            ]

            for field in surgery_fields:
                value = extended_data.get(field)
                if value:
                    if isinstance(value, str):
                        codes.add(value.strip())
                    elif isinstance(value, list):
                        for item in value:
                            if isinstance(item, str):
                                codes.add(item.strip())

        except (json.JSONDecodeError, TypeError) as e:
            logger.debug(f"解析 extended_fields 失败: {e}")

        return {code for code in codes if code}

    def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计信息"""
        avg_filter_time = (
            self._total_filter_time / self._filter_count 
            if self._filter_count > 0 else 0.0
        )

        index_stats = self.index_engine.get_all_stats()

        return {
            'build_time_ms': self._build_time,
            'last_build_timestamp': self._last_build_timestamp,
            'filter_count': self._filter_count,
            'total_filter_time_ms': self._total_filter_time,
            'avg_filter_time_ms': avg_filter_time,
            'generic_rule_count': len(self._generic_rules),
            'index_stats': {name: {
                'index_type': stats.index_type,
                'total_keys': stats.total_keys,
                'memory_usage_mb': stats.memory_usage_mb,
                'hit_rate': stats.hit_rate
            } for name, stats in index_stats.items()}
        }

    def clear_indexes(self) -> None:
        """清除所有索引"""
        self.index_engine.clear_all_indexes()
        self._generic_rules.clear()
        self._filter_count = 0
        self._total_filter_time = 0.0
        logger.info("医疗索引已清除")


# 全局实例
medical_index_wrapper = MedicalIndexWrapper()
