"""
降级配置管理模块
提供降级配置的解析、验证和环境特定配置支持
"""

import logging
from dataclasses import dataclass

from config.settings import Settings

logger = logging.getLogger(__name__)


@dataclass
class DegradationThresholds:
    """降级阈值配置数据类"""

    # CPU阈值
    cpu_l1_threshold: float
    cpu_l2_threshold: float
    cpu_l3_threshold: float
    cpu_l1_recovery: float
    cpu_l2_recovery: float
    cpu_l3_recovery: float

    # 内存阈值
    memory_l1_threshold: float
    memory_l2_threshold: float
    memory_l3_threshold: float
    memory_l1_recovery: float
    memory_l2_recovery: float
    memory_l3_recovery: float

    # 错误率阈值
    error_l1_threshold: float
    error_l2_threshold: float
    error_l3_threshold: float
    error_l1_recovery: float
    error_l2_recovery: float
    error_l3_recovery: float

    # 队列长度阈值
    queue_l1_threshold: int
    queue_l2_threshold: int
    queue_l3_threshold: int
    queue_l1_recovery: int
    queue_l2_recovery: int
    queue_l3_recovery: int

    def __post_init__(self):
        """配置验证"""
        self._validate_thresholds()

    def _validate_thresholds(self):
        """验证阈值配置的合理性"""
        # 验证CPU阈值递增
        if not (0 <= self.cpu_l1_threshold <= self.cpu_l2_threshold <= self.cpu_l3_threshold <= 100):
            raise ValueError("CPU降级阈值必须递增且在0-100范围内")

        # 验证CPU恢复阈值合理性
        if not (0 <= self.cpu_l1_recovery < self.cpu_l1_threshold):
            raise ValueError("CPU L1恢复阈值必须小于触发阈值")
        if not (0 <= self.cpu_l2_recovery < self.cpu_l2_threshold):
            raise ValueError("CPU L2恢复阈值必须小于触发阈值")
        if not (0 <= self.cpu_l3_recovery < self.cpu_l3_threshold):
            raise ValueError("CPU L3恢复阈值必须小于触发阈值")

        # 验证内存阈值递增
        if not (0 <= self.memory_l1_threshold <= self.memory_l2_threshold <= self.memory_l3_threshold <= 100):
            raise ValueError("内存降级阈值必须递增且在0-100范围内")

        # 验证内存恢复阈值合理性
        if not (0 <= self.memory_l1_recovery < self.memory_l1_threshold):
            raise ValueError("内存 L1恢复阈值必须小于触发阈值")
        if not (0 <= self.memory_l2_recovery < self.memory_l2_threshold):
            raise ValueError("内存 L2恢复阈值必须小于触发阈值")
        if not (0 <= self.memory_l3_recovery < self.memory_l3_threshold):
            raise ValueError("内存 L3恢复阈值必须小于触发阈值")

        # 验证错误率阈值递增
        if not (0 <= self.error_l1_threshold <= self.error_l2_threshold <= self.error_l3_threshold <= 100):
            raise ValueError("错误率降级阈值必须递增且在0-100范围内")

        # 验证错误率恢复阈值合理性
        if not (0 <= self.error_l1_recovery < self.error_l1_threshold):
            raise ValueError("错误率 L1恢复阈值必须小于触发阈值")
        if not (0 <= self.error_l2_recovery < self.error_l2_threshold):
            raise ValueError("错误率 L2恢复阈值必须小于触发阈值")
        if not (0 <= self.error_l3_recovery < self.error_l3_threshold):
            raise ValueError("错误率 L3恢复阈值必须小于触发阈值")

        # 验证队列长度阈值递增
        if not (0 <= self.queue_l1_threshold <= self.queue_l2_threshold <= self.queue_l3_threshold):
            raise ValueError("队列长度降级阈值必须递增且大于等于0")

        # 验证队列长度恢复阈值合理性
        if not (0 <= self.queue_l1_recovery < self.queue_l1_threshold):
            raise ValueError("队列长度 L1恢复阈值必须小于触发阈值")
        if not (0 <= self.queue_l2_recovery < self.queue_l2_threshold):
            raise ValueError("队列长度 L2恢复阈值必须小于触发阈值")
        if not (0 <= self.queue_l3_recovery < self.queue_l3_threshold):
            raise ValueError("队列长度 L3恢复阈值必须小于触发阈值")


@dataclass
class DegradationConfig:
    """降级配置数据类"""

    # 基础配置
    enabled: bool
    thresholds: DegradationThresholds

    # 检测配置
    window_size: int
    min_samples: int
    check_interval: float

    # 状态管理配置
    min_duration: float
    recovery_delay: float

    # 监控配置
    metrics_enabled: bool
    metrics_retention: int
    event_history_size: int
    stats_window_size: int

    # 同步配置
    sync_interval: float
    persistence_enabled: bool

    def __post_init__(self):
        """配置验证"""
        self._validate_config()

    def _validate_config(self):
        """验证配置参数的合理性"""
        if self.window_size < 1:
            raise ValueError("window_size必须大于等于1")

        if self.min_samples < 1:
            raise ValueError("min_samples必须大于等于1")

        if self.min_samples > self.window_size:
            raise ValueError("min_samples不能大于window_size")

        if self.check_interval <= 0:
            raise ValueError("check_interval必须大于0")

        if self.min_duration < 0:
            raise ValueError("min_duration必须大于等于0")

        if self.recovery_delay < 0:
            raise ValueError("recovery_delay必须大于等于0")

        if self.metrics_retention <= 0:
            raise ValueError("metrics_retention必须大于0")

        if self.event_history_size <= 0:
            raise ValueError("event_history_size必须大于0")

        if self.stats_window_size <= 0:
            raise ValueError("stats_window_size必须大于0")

        if self.sync_interval <= 0:
            raise ValueError("sync_interval必须大于0")


class DegradationConfigManager:
    """降级配置管理器"""

    def __init__(self, settings: Settings):
        self.settings = settings
        self._config_cache: DegradationConfig | None = None
        self._environment = self._detect_environment()

    def _detect_environment(self) -> str:
        """检测当前运行环境"""
        run_mode = self.settings.RUN_MODE.upper()
        if run_mode in ["DEV", "DEVELOPMENT"]:
            return "dev"
        elif run_mode in ["TEST", "TESTING"]:
            return "test"
        elif run_mode in ["PROD", "PRODUCTION"]:
            return "prod"
        else:
            logger.warning(f"未知的运行环境: {run_mode}，使用默认配置")
            return "default"

    def get_config(self, force_refresh: bool = False) -> DegradationConfig:
        """
        获取降级配置

        Args:
            force_refresh: 是否强制刷新配置缓存

        Returns:
            DegradationConfig: 降级配置对象
        """
        if self._config_cache is None or force_refresh:
            self._config_cache = self._build_config()

        return self._config_cache

    def _build_config(self) -> DegradationConfig:
        """构建降级配置"""
        # 获取环境特定的配置值
        enabled = self._get_env_config("ENABLED", self.settings.DEGRADATION_ENABLED)

        # 构建阈值配置
        thresholds = self._build_thresholds()

        # 获取检测配置
        window_size = self._get_env_config("WINDOW_SIZE", self.settings.DEGRADATION_WINDOW_SIZE)
        min_samples = self._get_env_config("MIN_SAMPLES", self.settings.DEGRADATION_MIN_SAMPLES)
        check_interval = self._get_env_config("CHECK_INTERVAL", self.settings.DEGRADATION_CHECK_INTERVAL)

        # 获取状态管理配置
        min_duration = self._get_env_config("MIN_DURATION", self.settings.DEGRADATION_MIN_DURATION)
        recovery_delay = self._get_env_config("RECOVERY_DELAY", self.settings.DEGRADATION_RECOVERY_DELAY)

        # 获取监控配置
        metrics_enabled = self.settings.DEGRADATION_METRICS_ENABLED
        metrics_retention = self.settings.DEGRADATION_METRICS_RETENTION
        event_history_size = self.settings.DEGRADATION_EVENT_HISTORY_SIZE
        stats_window_size = self.settings.DEGRADATION_STATS_WINDOW_SIZE

        # 获取同步配置
        sync_interval = self.settings.DEGRADATION_SYNC_INTERVAL
        persistence_enabled = self.settings.DEGRADATION_PERSISTENCE_ENABLED

        return DegradationConfig(
            enabled=enabled,
            thresholds=thresholds,
            window_size=window_size,
            min_samples=min_samples,
            check_interval=check_interval,
            min_duration=min_duration,
            recovery_delay=recovery_delay,
            metrics_enabled=metrics_enabled,
            metrics_retention=metrics_retention,
            event_history_size=event_history_size,
            stats_window_size=stats_window_size,
            sync_interval=sync_interval,
            persistence_enabled=persistence_enabled,
        )

    def _build_thresholds(self) -> DegradationThresholds:
        """构建阈值配置"""
        return DegradationThresholds(
            # CPU阈值
            cpu_l1_threshold=self._get_env_config("CPU_L1_THRESHOLD", self.settings.DEGRADATION_CPU_L1_THRESHOLD),
            cpu_l2_threshold=self._get_env_config("CPU_L2_THRESHOLD", self.settings.DEGRADATION_CPU_L2_THRESHOLD),
            cpu_l3_threshold=self._get_env_config("CPU_L3_THRESHOLD", self.settings.DEGRADATION_CPU_L3_THRESHOLD),
            cpu_l1_recovery=self._get_env_config("CPU_L1_RECOVERY", self.settings.DEGRADATION_CPU_L1_RECOVERY),
            cpu_l2_recovery=self._get_env_config("CPU_L2_RECOVERY", self.settings.DEGRADATION_CPU_L2_RECOVERY),
            cpu_l3_recovery=self._get_env_config("CPU_L3_RECOVERY", self.settings.DEGRADATION_CPU_L3_RECOVERY),
            # 内存阈值
            memory_l1_threshold=self._get_env_config(
                "MEMORY_L1_THRESHOLD", self.settings.DEGRADATION_MEMORY_L1_THRESHOLD
            ),
            memory_l2_threshold=self._get_env_config(
                "MEMORY_L2_THRESHOLD", self.settings.DEGRADATION_MEMORY_L2_THRESHOLD
            ),
            memory_l3_threshold=self._get_env_config(
                "MEMORY_L3_THRESHOLD", self.settings.DEGRADATION_MEMORY_L3_THRESHOLD
            ),
            memory_l1_recovery=self._get_env_config("MEMORY_L1_RECOVERY", self.settings.DEGRADATION_MEMORY_L1_RECOVERY),
            memory_l2_recovery=self._get_env_config("MEMORY_L2_RECOVERY", self.settings.DEGRADATION_MEMORY_L2_RECOVERY),
            memory_l3_recovery=self._get_env_config("MEMORY_L3_RECOVERY", self.settings.DEGRADATION_MEMORY_L3_RECOVERY),
            # 错误率阈值
            error_l1_threshold=self._get_env_config("ERROR_L1_THRESHOLD", self.settings.DEGRADATION_ERROR_L1_THRESHOLD),
            error_l2_threshold=self._get_env_config("ERROR_L2_THRESHOLD", self.settings.DEGRADATION_ERROR_L2_THRESHOLD),
            error_l3_threshold=self._get_env_config("ERROR_L3_THRESHOLD", self.settings.DEGRADATION_ERROR_L3_THRESHOLD),
            error_l1_recovery=self._get_env_config("ERROR_L1_RECOVERY", self.settings.DEGRADATION_ERROR_L1_RECOVERY),
            error_l2_recovery=self._get_env_config("ERROR_L2_RECOVERY", self.settings.DEGRADATION_ERROR_L2_RECOVERY),
            error_l3_recovery=self._get_env_config("ERROR_L3_RECOVERY", self.settings.DEGRADATION_ERROR_L3_RECOVERY),
            # 队列长度阈值
            queue_l1_threshold=self._get_env_config("QUEUE_L1_THRESHOLD", self.settings.DEGRADATION_QUEUE_L1_THRESHOLD),
            queue_l2_threshold=self._get_env_config("QUEUE_L2_THRESHOLD", self.settings.DEGRADATION_QUEUE_L2_THRESHOLD),
            queue_l3_threshold=self._get_env_config("QUEUE_L3_THRESHOLD", self.settings.DEGRADATION_QUEUE_L3_THRESHOLD),
            queue_l1_recovery=self._get_env_config("QUEUE_L1_RECOVERY", self.settings.DEGRADATION_QUEUE_L1_RECOVERY),
            queue_l2_recovery=self._get_env_config("QUEUE_L2_RECOVERY", self.settings.DEGRADATION_QUEUE_L2_RECOVERY),
            queue_l3_recovery=self._get_env_config("QUEUE_L3_RECOVERY", self.settings.DEGRADATION_QUEUE_L3_RECOVERY),
        )

    def _get_env_config(self, config_name: str, default_value):
        """获取环境特定的配置值"""
        env_attr_name = f"DEGRADATION_{self._environment.upper()}_{config_name}"

        # 尝试获取环境特定配置
        if hasattr(self.settings, env_attr_name):
            env_value = getattr(self.settings, env_attr_name)
            logger.debug(f"使用环境特定配置: {env_attr_name} = {env_value}")
            return env_value

        # 回退到默认配置
        logger.debug(f"使用默认配置: DEGRADATION_{config_name} = {default_value}")
        return default_value

    def get_environment(self) -> str:
        """获取当前环境"""
        return self._environment

    def is_enabled(self) -> bool:
        """检查降级功能是否启用"""
        config = self.get_config()
        return config.enabled

    def refresh_config(self):
        """刷新配置缓存"""
        self._config_cache = None
        logger.info("降级配置缓存已刷新")


# 全局配置管理器实例
_config_manager: DegradationConfigManager | None = None


def get_degradation_config_manager(settings: Settings | None = None) -> DegradationConfigManager:
    """
    获取全局降级配置管理器实例

    Args:
        settings: Settings实例，如果为None则使用默认设置

    Returns:
        DegradationConfigManager: 配置管理器实例
    """
    global _config_manager

    if _config_manager is None:
        if settings is None:
            from config.settings import get_settings

            settings = get_settings()
        _config_manager = DegradationConfigManager(settings)

    return _config_manager


def get_degradation_config(force_refresh: bool = False) -> DegradationConfig:
    """
    获取降级配置的便捷函数

    Args:
        force_refresh: 是否强制刷新配置缓存

    Returns:
        DegradationConfig: 降级配置对象
    """
    manager = get_degradation_config_manager()
    return manager.get_config(force_refresh=force_refresh)


class DegradationConfigWatcher:
    """降级配置监听器

    支持配置热更新，当配置文件或环境变量发生变化时自动刷新配置
    """

    def __init__(self, config_manager: DegradationConfigManager):
        self.config_manager = config_manager
        self._last_config_hash: str | None = None
        self._watchers = []
        self._is_watching = False

    def start_watching(self):
        """开始监听配置变化"""
        if self._is_watching:
            logger.warning("配置监听器已经在运行")
            return

        self._is_watching = True
        self._last_config_hash = self._get_config_hash()

        logger.info("降级配置监听器已启动")

    def stop_watching(self):
        """停止监听配置变化"""
        if not self._is_watching:
            return

        self._is_watching = False
        logger.info("降级配置监听器已停止")

    def check_config_changes(self) -> bool:
        """
        检查配置是否发生变化

        Returns:
            bool: 配置是否发生变化
        """
        if not self._is_watching:
            return False

        current_hash = self._get_config_hash()
        if current_hash != self._last_config_hash:
            logger.info("检测到降级配置变化，刷新配置缓存")
            self.config_manager.refresh_config()
            self._last_config_hash = current_hash

            # 通知所有监听器
            self._notify_watchers()
            return True

        return False

    def _get_config_hash(self) -> str:
        """获取当前配置的哈希值"""
        import hashlib

        # 获取所有降级相关的配置值
        config_values = []
        settings = self.config_manager.settings

        # 收集所有DEGRADATION_开头的配置项
        for attr_name in dir(settings):
            if attr_name.startswith("DEGRADATION_"):
                value = getattr(settings, attr_name)
                config_values.append(f"{attr_name}={value}")

        # 添加环境变量
        config_values.append(f"RUN_MODE={settings.RUN_MODE}")

        # 计算哈希值
        config_str = "|".join(sorted(config_values))
        return hashlib.md5(config_str.encode()).hexdigest()

    def add_watcher(self, callback):
        """
        添加配置变化监听器

        Args:
            callback: 配置变化时的回调函数
        """
        self._watchers.append(callback)
        logger.debug(f"添加配置监听器: {callback.__name__}")

    def remove_watcher(self, callback):
        """
        移除配置变化监听器

        Args:
            callback: 要移除的回调函数
        """
        if callback in self._watchers:
            self._watchers.remove(callback)
            logger.debug(f"移除配置监听器: {callback.__name__}")

    def _notify_watchers(self):
        """通知所有监听器配置已变化"""
        for callback in self._watchers:
            try:
                callback()
            except Exception as e:
                logger.error(f"配置监听器回调失败: {callback.__name__}, 错误: {e}")


# 全局配置监听器实例
_config_watcher: DegradationConfigWatcher | None = None


def get_degradation_config_watcher() -> DegradationConfigWatcher:
    """
    获取全局降级配置监听器实例

    Returns:
        DegradationConfigWatcher: 配置监听器实例
    """
    global _config_watcher

    if _config_watcher is None:
        config_manager = get_degradation_config_manager()
        _config_watcher = DegradationConfigWatcher(config_manager)

    return _config_watcher


def start_config_watching():
    """启动配置监听"""
    watcher = get_degradation_config_watcher()
    watcher.start_watching()


def stop_config_watching():
    """停止配置监听"""
    watcher = get_degradation_config_watcher()
    watcher.stop_watching()


def check_config_changes() -> bool:
    """
    检查配置变化的便捷函数

    Returns:
        bool: 配置是否发生变化
    """
    watcher = get_degradation_config_watcher()
    return watcher.check_config_changes()
