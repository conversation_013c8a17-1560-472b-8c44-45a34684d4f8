#!/usr/bin/env python3
"""
子节点配置验证脚本

用于验证子节点配置文件的正确性，确保部署前配置无误。
"""

import gzip
import json
import os
import sys


class ConfigValidator:
    """配置验证器"""

    def __init__(self, config_path: str = ".env"):
        self.config_path = config_path
        self.config = {}
        self.errors = []
        self.warnings = []

    def load_config(self) -> bool:
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            self.errors.append(f"配置文件不存在: {self.config_path}")
            return False

        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        self.config[key.strip()] = value.strip()
            return True
        except Exception as e:
            self.errors.append(f"读取配置文件失败: {e}")
            return False

    def validate_basic_config(self):
        """验证基础配置"""
        required_configs = {
            'MODE': 'slave',
            'RUN_MODE': ['DEV', 'TEST', 'PROD'],
            'SERVER_HOST': None,
            'SERVER_PORT': None,
        }

        for key, expected in required_configs.items():
            if key not in self.config:
                self.errors.append(f"缺少必需配置项: {key}")
                continue

            value = self.config[key]
            if expected == 'slave' and value != 'slave':
                self.errors.append(f"节点类型配置错误: {key}={value}，应该为 'slave'")
            elif isinstance(expected, list) and value not in expected:
                self.errors.append(f"配置值无效: {key}={value}，应该为 {expected} 中的一个")

    def validate_database_config(self):
        """验证数据库配置（子节点不应该有）"""
        db_configs = [
            'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME', 'DB_DRIVER',
            'DATABASE_URL', 'AUTO_CREATE_DATABASE', 'DB_ADMIN_USER', 'DB_ADMIN_PASSWORD'
        ]

        found_db_configs = []
        for config in db_configs:
            if config in self.config:
                found_db_configs.append(config)

        if found_db_configs:
            self.warnings.append(
                f"发现数据库配置项，子节点不需要数据库连接: {', '.join(found_db_configs)}"
            )

    def validate_sync_config(self):
        """验证同步配置"""
        enable_sync = self.config.get('ENABLE_RULE_SYNC', '').lower()

        if enable_sync == 'true':
            # 在线模式，需要主节点连接配置
            required_online_configs = ['MASTER_API_ENDPOINT', 'SLAVE_API_KEY']
            for config in required_online_configs:
                if config not in self.config:
                    self.errors.append(f"在线模式缺少必需配置: {config}")
        elif enable_sync == 'false':
            # 离线模式，检查缓存文件
            if not os.path.exists('rules_cache.json.gz'):
                self.errors.append("离线模式缺少规则缓存文件: rules_cache.json.gz")
        else:
            self.warnings.append(f"ENABLE_RULE_SYNC 配置值不明确: {enable_sync}")

    def validate_performance_config(self):
        """验证性能配置"""
        performance_configs = {
            'WORKERS': (1, 32),
            'WORKER_COUNT': (1, 32),
            'PROCESS_POOL_SIZE': (1, 16),
            'CACHE_SIZE': (100, 10000),
            'QUEUE_MAX_SIZE': (100, 10000),
        }

        for config, (min_val, max_val) in performance_configs.items():
            if config in self.config:
                try:
                    value = int(self.config[config])
                    if not (min_val <= value <= max_val):
                        self.warnings.append(
                            f"性能配置值可能不合理: {config}={value}，建议范围 {min_val}-{max_val}"
                        )
                except ValueError:
                    self.errors.append(f"性能配置值无效: {config}={self.config[config]}")

    def validate_cache_file(self):
        """验证规则缓存文件"""
        cache_file = 'rules_cache.json.gz'

        if not os.path.exists(cache_file):
            self.errors.append(f"规则缓存文件不存在: {cache_file}")
            return

        try:
            # 检查文件大小
            file_size = os.path.getsize(cache_file)
            if file_size < 1024:  # 小于1KB
                self.warnings.append(f"规则缓存文件过小: {file_size} bytes")

            # 尝试解压缩和解析
            with gzip.open(cache_file, 'rt', encoding='utf-8') as f:
                data = json.load(f)

            # 检查数据格式
            if isinstance(data, dict) and 'rule_datasets' in data:
                rule_count = len(data['rule_datasets'])
                print(f"✓ 规则缓存文件格式正确，包含 {rule_count} 个规则数据集")
            elif isinstance(data, list):
                rule_count = len(data)
                print(f"✓ 规则缓存文件格式正确，包含 {rule_count} 个规则数据集")
            else:
                self.warnings.append("规则缓存文件格式可能不正确")

        except Exception as e:
            self.errors.append(f"规则缓存文件验证失败: {e}")

    def validate_all(self) -> bool:
        """执行所有验证"""
        if not self.load_config():
            return False

        self.validate_basic_config()
        self.validate_database_config()
        self.validate_sync_config()
        self.validate_performance_config()
        self.validate_cache_file()

        return len(self.errors) == 0

    def print_results(self):
        """打印验证结果"""
        print("=" * 60)
        print("子节点配置验证结果")
        print("=" * 60)

        if self.errors:
            print("\n❌ 发现错误:")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")

        if self.warnings:
            print("\n⚠️  警告信息:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")

        if not self.errors and not self.warnings:
            print("\n✅ 配置验证通过，无错误和警告")
        elif not self.errors:
            print("\n✅ 配置验证通过，但有警告信息需要注意")
        else:
            print(f"\n❌ 配置验证失败，发现 {len(self.errors)} 个错误")

        print("\n" + "=" * 60)


def main():
    """主函数"""
    print("子节点配置验证工具")
    print("用于验证子节点配置文件的正确性")

    # 检查配置文件参数
    config_file = sys.argv[1] if len(sys.argv) > 1 else ".env"

    validator = ConfigValidator(config_file)
    success = validator.validate_all()
    validator.print_results()

    # 提供修复建议
    if validator.errors:
        print("\n修复建议:")
        print("1. 检查并修正上述错误配置项")
        print("2. 参考 .env.slave.template 模板文件")
        print("3. 确保规则缓存文件存在且有效")
        print("4. 运行 'python tools/validate_slave_config.py' 重新验证")

    if validator.warnings:
        print("\n优化建议:")
        print("1. 移除不必要的数据库配置项")
        print("2. 根据服务器配置调整性能参数")
        print("3. 确认同步模式配置符合部署需求")

    # 返回退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
