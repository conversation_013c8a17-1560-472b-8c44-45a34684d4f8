"""
优化的患者数据模型
使用__slots__减少内存占用，实现延迟索引构建和高效数据访问
"""
import time

from models.patient import FeeItem, PatientData


class OptimizedBasicInfo:
    """优化的基本信息（使用__slots__减少开销）"""

    __slots__ = ["gender", "age", "birth_date", "patient_id"]

    def __init__(
        self,
        gender: str | None = None,
        age: int | None = None,
        birth_date: int | None = None,
        patient_id: str | None = None,
    ):
        self.gender = gender
        self.age = age
        self.birth_date = birth_date
        self.patient_id = patient_id


class OptimizedFeeItem:
    """优化的费用项（使用__slots__减少内存占用）"""

    __slots__ = [
        "id", "ybdm", "sl", "je", "jzsj", "fyxm", "gg", 
        "dw", "xh", "fydm", "fymc", "ksdm", "ksmc", "_cached_date"
    ]

    def __init__(self, fee_item: FeeItem):
        """从原始FeeItem创建优化版本"""
        self.id = fee_item.id
        self.ybdm = fee_item.ybdm
        self.sl = fee_item.sl or 0.0
        self.je = fee_item.je or 0.0
        self.jzsj = fee_item.jzsj
        self.fyxm = getattr(fee_item, "fyxm", None)
        self.gg = getattr(fee_item, "gg", None)
        self.dw = fee_item.dw
        self.xh = fee_item.xh
        self.fydm = fee_item.fydm
        self.fymc = fee_item.fymc
        self.ksdm = fee_item.ksdm
        self.ksmc = fee_item.ksmc
        self._cached_date = None

    def get_date(self) -> str | None:
        """获取缓存的日期字符串"""
        if self._cached_date is None and self.jzsj:
            try:
                # 转换时间戳为日期字符串
                timestamp = int(str(self.jzsj)[:10])  # 取前10位作为秒级时间戳
                self._cached_date = time.strftime("%Y-%m-%d", time.localtime(timestamp))
            except (ValueError, OSError):
                self._cached_date = None
        return self._cached_date

    def __hash__(self):
        """支持哈希，用于集合操作"""
        return hash(self.id) if self.id else hash((self.ybdm, self.sl, self.je))


class FeeItemIndex:
    """费用项索引，支持快速查找"""

    __slots__ = ["_by_ybdm", "_by_date", "_by_id", "_all_items"]

    def __init__(self):
        self._by_ybdm: dict[str, list[OptimizedFeeItem]] = {}
        self._by_date: dict[str, list[OptimizedFeeItem]] = {}
        self._by_id: dict[str, OptimizedFeeItem] = {}
        self._all_items: list[OptimizedFeeItem] = []

    def add_item(self, item: OptimizedFeeItem):
        """添加费用项到索引"""
        self._all_items.append(item)

        # 按医保代码索引
        if item.ybdm:
            if item.ybdm not in self._by_ybdm:
                self._by_ybdm[item.ybdm] = []
            self._by_ybdm[item.ybdm].append(item)

        # 按日期索引
        date = item.get_date()
        if date:
            if date not in self._by_date:
                self._by_date[date] = []
            self._by_date[date].append(item)

        # 按ID索引
        if item.id:
            self._by_id[item.id] = item

    def get_by_ybdm(self, ybdm: str) -> list[OptimizedFeeItem]:
        """根据医保代码获取费用项"""
        return self._by_ybdm.get(ybdm, [])

    def get_by_ybdm_list(self, ybdm_list: list[str]) -> list[OptimizedFeeItem]:
        """根据医保代码列表获取费用项"""
        result = []
        for ybdm in ybdm_list:
            result.extend(self._by_ybdm.get(ybdm, []))
        return result

    def get_by_date(self, date: str) -> list[OptimizedFeeItem]:
        """根据日期获取费用项"""
        return self._by_date.get(date, [])

    def get_by_id(self, item_id: str) -> OptimizedFeeItem | None:
        """根据ID获取费用项"""
        return self._by_id.get(item_id)

    def get_all(self) -> list[OptimizedFeeItem]:
        """获取所有费用项"""
        return self._all_items

    def get_unique_dates(self) -> set[str]:
        """获取所有唯一日期"""
        return set(self._by_date.keys())

    def get_unique_ybdm(self) -> set[str]:
        """获取所有唯一医保代码"""
        return set(self._by_ybdm.keys())

    def calculate_totals(self, ybdm_list: list[str] | None = None) -> tuple[float, float, int]:
        """计算总金额、总数量和总天数"""
        if ybdm_list:
            items = self.get_by_ybdm_list(ybdm_list)
        else:
            items = self._all_items

        total_amount = sum(item.je for item in items)
        total_quantity = sum(item.sl for item in items)
        unique_dates = set(item.get_date() for item in items if item.get_date())
        total_days = len(unique_dates)

        return total_amount, total_quantity, total_days


class OptimizedPatientData:
    """
    优化的患者数据模型
    使用__slots__减少内存占用，实现延迟索引构建
    """

    __slots__ = ["bah", "basic_information", "diagnoses", "_fee_index", "_original_fees", "_hash", "_is_indexed"]

    def __init__(self, original_data: PatientData):
        """从原始PatientData创建优化版本"""
        self.bah = original_data.bah

        # 优化基本信息
        if original_data.basic_information:
            self.basic_information = OptimizedBasicInfo(
                gender=original_data.basic_information.gender,
                age=original_data.basic_information.age,
                birth_date=original_data.basic_information.birthDate,
                patient_id=original_data.bah,
            )
        else:
            self.basic_information = OptimizedBasicInfo(patient_id=original_data.bah)

        # 保存原始费用数据，延迟构建索引
        self._original_fees = original_data.fees or []
        self._fee_index: FeeItemIndex | None = None
        self._is_indexed = False

        # 简化诊断信息（只保留必要字段）
        self.diagnoses = []
        if original_data.Diagnosis and original_data.Diagnosis.diagnosis:
            for diag in original_data.Diagnosis.diagnosis:
                if diag.diagnosisICDCode:  # 只保留有编码的诊断
                    self.diagnoses.append(
                        {
                            "code": diag.diagnosisICDCode,
                            "name": diag.diagnosisName,
                            "type": diag.diagnosisType,
                        }
                    )

        self._hash = None

    @property
    def fee_index(self) -> FeeItemIndex:
        """延迟构建费用索引"""
        if not self._is_indexed:
            self._build_fee_index()
        return self._fee_index

    def _build_fee_index(self):
        """构建费用索引"""
        if self._is_indexed:
            return

        self._fee_index = FeeItemIndex()

        for fee in self._original_fees:
            optimized_fee = OptimizedFeeItem(fee)
            self._fee_index.add_item(optimized_fee)

        self._is_indexed = True
        # 清理原始数据以节省内存
        self._original_fees = None

    def get_fees_by_codes(self, ybdm_codes: list[str]) -> list[OptimizedFeeItem]:
        """根据医保代码列表获取费用项"""
        return self.fee_index.get_by_ybdm_list(ybdm_codes)

    def get_all_fees(self) -> list[OptimizedFeeItem]:
        """获取所有费用项"""
        return self.fee_index.get_all()

    def calculate_fee_totals(self, ybdm_codes: list[str] | None = None) -> tuple[float, float, int]:
        """计算费用总计"""
        return self.fee_index.calculate_totals(ybdm_codes)

    def get_unique_fee_dates(self) -> set[str]:
        """获取费用的唯一日期集合"""
        return self.fee_index.get_unique_dates()

    def has_diagnosis_code(self, code: str) -> bool:
        """检查是否包含特定诊断编码"""
        return any(diag["code"] == code for diag in self.diagnoses)

    def get_diagnosis_codes(self) -> list[str]:
        """获取所有诊断编码"""
        return [diag["code"] for diag in self.diagnoses if diag["code"]]

    def __hash__(self):
        """支持哈希，用于缓存"""
        if self._hash is None:
            # 基于患者ID、费用数量和诊断数量计算哈希
            fee_count = len(self._original_fees) if self._original_fees else len(self.fee_index.get_all())
            self._hash = hash(
                (
                    self.bah,
                    fee_count,
                    len(self.diagnoses),
                    self.basic_information.gender,
                    self.basic_information.age,
                )
            )
        return self._hash

    def get_memory_usage_estimate(self) -> int:
        """估算内存使用量（字节）"""
        base_size = 200  # 基础对象大小

        # 基本信息
        basic_info_size = 100

        # 诊断信息
        diagnosis_size = len(self.diagnoses) * 150

        # 费用索引
        if self._is_indexed:
            fee_count = len(self.fee_index.get_all())
            fee_size = fee_count * 300  # 每个优化费用项约300字节
            index_size = fee_count * 50  # 索引开销
        else:
            fee_size = len(self._original_fees) * 500  # 原始费用项更大
            index_size = 0

        return base_size + basic_info_size + diagnosis_size + fee_size + index_size


def convert_to_optimized(original_data: PatientData) -> OptimizedPatientData:
    """将原始患者数据转换为优化版本"""
    return OptimizedPatientData(original_data)


def batch_convert_to_optimized(original_data_list: list[PatientData]) -> list[OptimizedPatientData]:
    """批量转换患者数据"""
    return [convert_to_optimized(data) for data in original_data_list]
