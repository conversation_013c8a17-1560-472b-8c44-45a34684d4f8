{"rules": [{"name": "high_cpu_usage", "metric_name": "system.cpu.usage", "operator": ">", "threshold": 80.0, "severity": "high", "description": "CPU使用率过高", "duration_seconds": 120, "evaluation_interval": 30, "enabled": true, "labels": {"component": "system", "category": "performance"}}, {"name": "high_memory_usage", "metric_name": "system.memory.usage", "operator": ">", "threshold": 85.0, "severity": "high", "description": "内存使用率过高", "duration_seconds": 120, "evaluation_interval": 30, "enabled": true, "labels": {"component": "system", "category": "performance"}}, {"name": "low_memory_available", "metric_name": "system.memory.available", "operator": "<", "threshold": 500, "severity": "medium", "description": "可用内存不足", "duration_seconds": 60, "evaluation_interval": 30, "enabled": true, "labels": {"component": "system", "category": "resource"}}, {"name": "high_error_rate", "metric_name": "errors.rate", "operator": ">", "threshold": 0.05, "severity": "critical", "description": "错误率过高", "duration_seconds": 30, "evaluation_interval": 10, "enabled": true, "labels": {"component": "application", "category": "error"}}, {"name": "registration_task_failure", "metric_name": "registration.tasks.success.rate", "operator": "<", "threshold": 0.9, "severity": "high", "description": "规则注册任务成功率过低", "duration_seconds": 180, "evaluation_interval": 60, "enabled": true, "labels": {"component": "registration", "category": "business"}}, {"name": "degradation_mode_active", "metric_name": "degradation.status", "operator": ">", "threshold": 0, "severity": "medium", "description": "系统进入降级模式", "duration_seconds": 30, "evaluation_interval": 10, "enabled": true, "labels": {"component": "degradation", "category": "system"}}, {"name": "circuit_breaker_open", "metric_name": "degradation.circuit.breaker.state", "operator": "==", "threshold": 2, "severity": "high", "description": "熔断器开启", "duration_seconds": 0, "evaluation_interval": 10, "enabled": true, "labels": {"component": "degradation", "category": "circuit_breaker"}}, {"name": "database_pool_high_utilization", "metric_name": "database.pool.utilization", "operator": ">", "threshold": 90.0, "severity": "high", "description": "数据库连接池使用率过高", "duration_seconds": 60, "evaluation_interval": 30, "enabled": true, "labels": {"component": "database", "category": "performance"}}, {"name": "slow_processing_time", "metric_name": "performance.processing.time", "operator": ">", "threshold": 5.0, "severity": "medium", "description": "处理时间过长", "duration_seconds": 120, "evaluation_interval": 60, "enabled": true, "labels": {"component": "performance", "category": "latency"}}, {"name": "low_throughput", "metric_name": "performance.throughput", "operator": "<", "threshold": 50.0, "severity": "medium", "description": "系统吞吐量过低", "duration_seconds": 180, "evaluation_interval": 60, "enabled": true, "labels": {"component": "performance", "category": "throughput"}}, {"name": "large_queue_length", "metric_name": "system.queue.length", "operator": ">", "threshold": 1000, "severity": "medium", "description": "任务队列长度过大", "duration_seconds": 60, "evaluation_interval": 30, "enabled": true, "labels": {"component": "queue", "category": "performance"}}, {"name": "degradation_cache_full", "metric_name": "degradation.cache.size", "operator": ">", "threshold": 5000, "severity": "medium", "description": "降级缓存接近满载", "duration_seconds": 120, "evaluation_interval": 60, "enabled": true, "labels": {"component": "degradation", "category": "cache"}}], "notification_channels": [{"name": "console", "type": "console", "config": {}, "enabled": true, "severity_filter": []}, {"name": "log", "type": "log", "config": {"log_level": "WARNING"}, "enabled": true, "severity_filter": []}, {"name": "webhook_critical", "type": "webhook", "config": {"url": "http://localhost:8080/webhook/alerts", "timeout": 10, "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-token-here"}}, "enabled": false, "severity_filter": ["critical", "high"]}, {"name": "email_admin", "type": "email", "config": {"smtp_host": "smtp.example.com", "smtp_port": 587, "smtp_user": "<EMAIL>", "smtp_password": "your-password-here", "from_email": "<EMAIL>", "to_emails": ["<EMAIL>"]}, "enabled": false, "severity_filter": ["critical", "high"]}], "suppression_rules": [{"name": "maintenance_window", "description": "维护窗口期间抑制告警", "enabled": false, "schedule": {"start_time": "02:00", "end_time": "04:00", "timezone": "Asia/Shanghai", "days": ["sunday"]}, "suppressed_rules": ["high_cpu_usage", "high_memory_usage"]}], "global_settings": {"default_evaluation_interval": 30, "default_duration_seconds": 60, "max_alerts_per_rule": 10, "alert_history_retention_days": 30, "notification_rate_limit": {"enabled": true, "max_notifications_per_hour": 60, "burst_limit": 10}}}