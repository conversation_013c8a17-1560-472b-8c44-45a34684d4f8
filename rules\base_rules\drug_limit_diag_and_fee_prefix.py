from config.settings import settings
from models import PatientData, RuleOutput, RuleResult
from rules.base_rules.base import BaseRule


class DrugLimitDiagAndFeePrefixRule(BaseRule):
    """
    药品限适应症（诊断+费用开头）-规则
    """

    rule_key = "drug_limit_diag_and_fee_prefix"
    rule_name = "药品限适应症（诊断+费用开头）"
    rule_desc = """1、排除自费患者、自费费用
2、精确匹配药品医保代码
3、模糊匹配适应症的icd-10门急诊+入院+出院诊断编码
4、匹配XX开头的23位药品医保代码
5、是2且（非3或非4），提示
【不违规举例：2+3+4】"""

    def __init__(
        self,
        rule_id: str,                   # 规则ID
        diag_code_prefix: list[str],    # 诊断编码前缀
        yb_code: list[str],             # 药品编码
        fee_code_prefix: list[str],     # 项目医保代码前缀
        rule_name: str,                 # 规则名称
        level1: str,                    # 一级错误类型
        level2: str,                    # 二级错误类型
        level3: str,                    # 三级错误类型
        error_reason: str,              # 错误原因
        degree: str,                    # 错误程度
        reference: str,                 # 质控依据或参考资料
        detail_position: str,           # 具体位置描述
        prompted_fields3: str | None,   # 提示字段类型
        prompted_fields1: str,          # 提示字段编码
        type: str,                      # 规则类别
        pos: str,                       # 适用业务
        applicableArea: str,            # 适用地区
        default_use: str,               # 默认选用
        remarks: str | None,            # 备注信息
        in_illustration: str | None,    # 入参说明
        start_date: str,                # 开始日期
        end_date: str,                  # 结束日期
    ):
        super().__init__(rule_id)
        self.diag_code_prefix = diag_code_prefix
        self.yb_code = yb_code
        self.fee_code_prefix = fee_code_prefix
        self.rule_name = rule_name
        self.level1 = level1
        self.level2 = level2
        self.level3 = level3
        self.type = type
        self.error_reason = error_reason
        self.degree = degree
        self.reference = reference
        self.prompted_fields3 = prompted_fields3
        self.prompted_fields1 = prompted_fields1
        self.detail_position = detail_position
        self.pos = pos
        self.applicableArea = applicableArea
        self.default_use = default_use
        self.remarks = remarks
        self.in_illustration = in_illustration
        self.start_date = start_date
        self.end_date = end_date

    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        药品限适应症（诊断+费用开头）
        """
        # 1. 先判断患者的医保类型，如果是自费就返回，不违规
        if (
            not patient_data.patientMedicalInsuranceType
            or not isinstance(patient_data.patientMedicalInsuranceType, str)
            or "自费" in patient_data.patientMedicalInsuranceType
        ):
            return None

        # 2. 判断是否存在相应诊断（以诊断编码前缀开头）
        for prefix in self.diag_code_prefix:
            # 2.1 门急诊诊断
            if (
                patient_data.Diagnosis.outPatientDiagnosisICDCode
                and patient_data.Diagnosis.outPatientDiagnosisICDCode.startswith(prefix)
            ):
                return None
            # 2.2 入院诊断
            if (
                patient_data.Diagnosis.diagnoseICDCodeOnAdmission
                and patient_data.Diagnosis.diagnoseICDCodeOnAdmission.startswith(prefix)
            ):
                return None
            # 2.3 出院诊断
            if patient_data.Diagnosis.diagnosis:
                for d in patient_data.Diagnosis.diagnosis:
                    if d.diagnosisICDCode and d.diagnosisICDCode.startswith(prefix):
                        return None

        # 3. 循环费用信息，判断是否存在目标费用
        #    1. 待检查的费用 - self.yb_codes
        #    2. 需模糊匹配的项目医保代码 - self.fee_code_prefix
        has_target_fee = False

        # 使用日期、违规日期集合
        used_dates = set()
        illegal_dates = set()
        # 使用数量、违规数量
        used_count = 0
        illegal_count = 0
        # 使用项目列表、违规项目列表
        used_fee_ids = []
        illegal_fee_ids = []
        # 违规金额
        error_fee = 0

        for fee in patient_data.fees:
            # 判断药品编码是否在违规编码列表中
            if fee.ybdm not in self.yb_code:
                continue

            # 判断费用是否是需模糊匹配的项目医保代码（项目医保代码以 self.fee_code_prefix 开头）
            elif not has_target_fee and fee.ybdm:
                tmp_prefix_code = False
                for prefix in self.fee_code_prefix:
                    if fee.ybdm.startswith(prefix):
                        tmp_prefix_code = True
                        break
                if tmp_prefix_code:
                    has_target_fee = True
                continue

            # 判断日期是否正确，必须是毫秒级时间戳
            jzsj_str = str(fee.jzsj)
            if not jzsj_str.isdigit() or len(jzsj_str) < 10:
                continue

            # 把记账时间转换成日期形式
            fee_date = self._trans_timestamp_to_date(int(jzsj_str[:10]))

            # 如果费用不是自费，则违规
            if str(fee.bzjs) not in settings.FEE_SELF_PAY_CODE:
                illegal_dates.add(fee_date)
                illegal_count += fee.sl
                illegal_fee_ids.append(fee.id)
                error_fee += fee.je

            # 使用数量、使用日期、使用项目
            used_count += fee.sl
            used_fee_ids.append(fee.id)
            used_dates.add(fee_date)

        # 4. 如果违规数量为0，或者有对应医保项目，则不违规，直接返回
        if illegal_count == 0 or has_target_fee:
            return None

        rule_output = RuleOutput(
            type_=self.type,
            message=self.error_reason,
            level1=self.level1,
            level2=self.level2,
            level3=self.level3,
            error_reason=self.error_reason,
            degree=self.degree,
            reference=self.reference,
            prompted_fields3=self.prompted_fields3,
            prompted_fields1=self.prompted_fields1,
            detail_position=self.detail_position,
            pos=self.pos,
            applicableArea=self.applicableArea,
            default_use=self.default_use,
            remarks=self.remarks,
            in_illustration=self.in_illustration,
            start_date=self.start_date,
            end_date=self.end_date,
            used_count=used_count,
            illegal_count=illegal_count,
            used_day=len(used_dates),
            illegal_day=len(illegal_dates),
            illegal_item=",".join(illegal_fee_ids),
            error_fee=error_fee,
            prompted_fields2=",".join(used_fee_ids),
        )

        # 返回结果
        return RuleResult(
            id=self.rule_id,
            output=rule_output,
        )
