/**
 * 性能优化工具函数
 * 提供防抖、节流、缓存等性能优化功能
 */

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {Object} options - 配置选项
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300, options = {}) {
  const { leading = false, trailing = true, maxWait } = options

  let timeoutId
  let maxTimeoutId
  let lastCallTime
  let lastInvokeTime = 0
  let lastArgs
  let lastThis
  let result

  function invokeFunc(time) {
    const args = lastArgs
    const thisArg = lastThis

    lastArgs = lastThis = undefined
    lastInvokeTime = time
    result = func.apply(thisArg, args)
    return result
  }

  function leadingEdge(time) {
    lastInvokeTime = time
    timeoutId = setTimeout(timerExpired, wait)
    return leading ? invokeFunc(time) : result
  }

  function remainingWait(time) {
    const timeSinceLastCall = time - lastCallTime
    const timeSinceLastInvoke = time - lastInvokeTime
    const timeWaiting = wait - timeSinceLastCall

    return maxWait !== undefined
      ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting
  }

  function shouldInvoke(time) {
    const timeSinceLastCall = time - lastCallTime
    const timeSinceLastInvoke = time - lastInvokeTime

    return (lastCallTime === undefined ||
            timeSinceLastCall >= wait ||
            timeSinceLastCall < 0 ||
            (maxWait !== undefined && timeSinceLastInvoke >= maxWait))
  }

  function timerExpired() {
    const time = Date.now()
    if (shouldInvoke(time)) {
      return trailingEdge(time)
    }
    timeoutId = setTimeout(timerExpired, remainingWait(time))
  }

  function trailingEdge(time) {
    timeoutId = undefined

    if (trailing && lastArgs) {
      return invokeFunc(time)
    }
    lastArgs = lastThis = undefined
    return result
  }

  function cancel() {
    if (timeoutId !== undefined) {
      clearTimeout(timeoutId)
    }
    if (maxTimeoutId !== undefined) {
      clearTimeout(maxTimeoutId)
    }
    lastInvokeTime = 0
    lastArgs = lastCallTime = lastThis = timeoutId = maxTimeoutId = undefined
  }

  function flush() {
    return timeoutId === undefined ? result : trailingEdge(Date.now())
  }

  function pending() {
    return timeoutId !== undefined
  }

  function debounced(...args) {
    const time = Date.now()
    const isInvoking = shouldInvoke(time)

    lastArgs = args
    lastThis = this
    lastCallTime = time

    if (isInvoking) {
      if (timeoutId === undefined) {
        return leadingEdge(lastCallTime)
      }
      if (maxWait !== undefined) {
        timeoutId = setTimeout(timerExpired, wait)
        return invokeFunc(lastCallTime)
      }
    }
    if (timeoutId === undefined) {
      timeoutId = setTimeout(timerExpired, wait)
    }
    return result
  }

  debounced.cancel = cancel
  debounced.flush = flush
  debounced.pending = pending

  return debounced
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {Object} options - 配置选项
 * @returns {Function} 节流后的函数
 */
export function throttle(func, wait = 100, options = {}) {
  const { leading = true, trailing = true } = options
  return debounce(func, wait, {
    leading,
    trailing,
    maxWait: wait
  })
}

/**
 * 请求动画帧节流
 * @param {Function} func - 要节流的函数
 * @returns {Function} 节流后的函数
 */
export function rafThrottle(func) {
  let rafId = null
  let lastArgs = null

  function throttled(...args) {
    lastArgs = args

    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        func.apply(this, lastArgs)
        rafId = null
        lastArgs = null
      })
    }
  }

  throttled.cancel = () => {
    if (rafId !== null) {
      cancelAnimationFrame(rafId)
      rafId = null
      lastArgs = null
    }
  }

  return throttled
}

/**
 * LRU缓存实现
 */
export class LRUCache {
  constructor(capacity = 100) {
    this.capacity = capacity
    this.cache = new Map()
  }

  get(key) {
    if (this.cache.has(key)) {
      // 移动到最前面
      const value = this.cache.get(key)
      this.cache.delete(key)
      this.cache.set(key, value)
      return value
    }
    return undefined
  }

  set(key, value) {
    if (this.cache.has(key)) {
      // 更新现有值
      this.cache.delete(key)
    } else if (this.cache.size >= this.capacity) {
      // 删除最久未使用的项
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    this.cache.set(key, value)
  }

  has(key) {
    return this.cache.has(key)
  }

  delete(key) {
    return this.cache.delete(key)
  }

  clear() {
    this.cache.clear()
  }

  get size() {
    return this.cache.size
  }

  keys() {
    return Array.from(this.cache.keys())
  }

  values() {
    return Array.from(this.cache.values())
  }
}

/**
 * 内存化函数（带缓存的函数）
 * @param {Function} func - 要缓存的函数
 * @param {Function} keyGenerator - 生成缓存键的函数
 * @param {number} maxSize - 最大缓存大小
 * @returns {Function} 带缓存的函数
 */
export function memoize(func, keyGenerator = (...args) => JSON.stringify(args), maxSize = 100) {
  const cache = new LRUCache(maxSize)

  function memoized(...args) {
    const key = keyGenerator(...args)

    if (cache.has(key)) {
      return cache.get(key)
    }

    const result = func.apply(this, args)
    cache.set(key, result)
    return result
  }

  memoized.cache = cache
  memoized.clear = () => cache.clear()

  return memoized
}

/**
 * 批量处理函数
 * @param {Function} processor - 批量处理函数
 * @param {number} batchSize - 批次大小
 * @param {number} delay - 批次间延迟（毫秒）
 * @returns {Function} 批量处理函数
 */
export function batchProcessor(processor, batchSize = 100, delay = 0) {
  let queue = []
  let processing = false

  async function processQueue() {
    if (processing || queue.length === 0) return

    processing = true

    while (queue.length > 0) {
      const batch = queue.splice(0, batchSize)

      try {
        await processor(batch)
      } catch (error) {
        console.error('Batch processing error:', error)
      }

      if (delay > 0 && queue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    processing = false
  }

  return {
    add(item) {
      queue.push(item)
      processQueue()
    },

    addBatch(items) {
      queue.push(...items)
      processQueue()
    },

    clear() {
      queue = []
    },

    get queueSize() {
      return queue.length
    },

    get isProcessing() {
      return processing
    }
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  constructor() {
    this.marks = new Map()
    this.measures = new Map()
    this.observers = []
  }

  // 开始性能标记
  mark(name) {
    const timestamp = performance.now()
    this.marks.set(name, timestamp)

    if (performance.mark) {
      performance.mark(name)
    }

    return timestamp
  }

  // 测量性能
  measure(name, startMark, endMark) {
    const startTime = this.marks.get(startMark)
    const endTime = endMark ? this.marks.get(endMark) : performance.now()

    if (startTime === undefined) {
      console.warn(`Start mark "${startMark}" not found`)
      return null
    }

    const duration = endTime - startTime
    this.measures.set(name, {
      name,
      startTime,
      endTime,
      duration,
      timestamp: Date.now()
    })

    if (performance.measure) {
      try {
        performance.measure(name, startMark, endMark)
      } catch (error) {
        console.warn('Performance.measure failed:', error)
      }
    }

    // 通知观察者
    this.notifyObservers(name, duration)

    return duration
  }

  // 获取测量结果
  getMeasure(name) {
    return this.measures.get(name)
  }

  // 获取所有测量结果
  getAllMeasures() {
    return Array.from(this.measures.values())
  }

  // 清除标记和测量
  clear() {
    this.marks.clear()
    this.measures.clear()

    if (performance.clearMarks) {
      performance.clearMarks()
    }
    if (performance.clearMeasures) {
      performance.clearMeasures()
    }
  }

  // 添加性能观察者
  addObserver(callback) {
    this.observers.push(callback)
  }

  // 移除性能观察者
  removeObserver(callback) {
    const index = this.observers.indexOf(callback)
    if (index > -1) {
      this.observers.splice(index, 1)
    }
  }

  // 通知观察者
  notifyObservers(name, duration) {
    this.observers.forEach(callback => {
      try {
        callback(name, duration)
      } catch (error) {
        console.error('Performance observer error:', error)
      }
    })
  }

  // 获取内存使用情况
  getMemoryUsage() {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      }
    }
    return null
  }
}

/**
 * 懒加载工具
 * @param {Function} loader - 加载函数
 * @param {Object} options - 配置选项
 * @returns {Function} 懒加载函数
 */
export function createLazyLoader(loader, options = {}) {
  const {
    cache = true,
    timeout = 10000,
    retries = 3,
    retryDelay = 1000
  } = options

  let loadPromise = null
  let result = null
  let error = null

  return async function lazyLoad() {
    // 如果已经加载成功且启用缓存，直接返回结果
    if (cache && result !== null) {
      return result
    }

    // 如果已经有加载中的Promise，返回它
    if (loadPromise) {
      return loadPromise
    }

    // 创建新的加载Promise
    loadPromise = (async () => {
      let lastError = null

      for (let attempt = 0; attempt <= retries; attempt++) {
        try {
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Load timeout')), timeout)
          })

          const loadResult = await Promise.race([loader(), timeoutPromise])

          result = loadResult
          error = null
          return result
        } catch (err) {
          lastError = err

          if (attempt < retries) {
            await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)))
          }
        }
      }

      error = lastError
      throw lastError
    })()

    try {
      return await loadPromise
    } finally {
      loadPromise = null
    }
  }
}

// 创建全局性能监控器实例
export const globalPerformanceMonitor = new PerformanceMonitor()

// 导出常用的防抖节流函数
export const debouncedSearch = debounce((callback, query) => callback(query), 300)
export const throttledScroll = throttle((callback, event) => callback(event), 16)
export const throttledResize = throttle((callback, event) => callback(event), 100)
