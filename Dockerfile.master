# ===== 主节点后端镜像 (Master Node) =====
# 基于base-runtime构建，包含主节点专用功能
# 支持规则管理、数据库操作、规则同步等功能

# ===== 基于基础运行环境 =====
FROM registry.yadingdata.com/library/sub-rule-base:test_1.0.0_V1 AS master-node

# 设置主节点特定环境变量
ENV MODE=master \
    SERVER_PORT=18001

# 切换到root用户进行系统配置
USER root

# 安装主节点特定的系统依赖（如果需要）
# 当前基础镜像已包含所需依赖，此处预留扩展空间
RUN echo "主节点系统依赖检查完成"

# 设置工作目录
WORKDIR /app

# 复制主节点应用代码（按依赖顺序，优化缓存）
# 1. 配置文件（变化较少）
COPY config/ ./config/

# 2. 核心模块（相对稳定）
COPY core/ ./core/
COPY models/ ./models/
COPY utils/ ./utils/

# 3. 服务层（中等变化频率）
COPY services/ ./services/

# 4. API层（变化较频繁）
COPY api/ ./api/

# 5. 规则相关（可能经常更新）
COPY rules/ ./rules/

# 6. 数据库迁移文件
COPY alembic/ ./alembic/
COPY alembic.ini ./

# 7. 主应用入口（最后复制，变化最频繁）
COPY master.py ./

# 8. 其他必要文件
COPY __init__.py ./

# 创建主节点专用目录
RUN mkdir -p \
    logs/master \
    rules_cache/master \
    data/exports \
    data/imports \
    data/templates

# 创建主节点启动脚本
COPY start-master.sh /app/start-master.sh
RUN chmod +x /app/start-master.sh

# 健康检查配置（继承基础镜像的脚本）
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD /app/healthcheck.sh || exit 1

# 暴露端口
EXPOSE 18001

# 设置标签
LABEL component="master-node" \
      service.type="backend" \
      service.role="master" \
      features="rule-management,database,sync,validation"

# 启动命令
CMD ["bash", "/app/start-master.sh"]
