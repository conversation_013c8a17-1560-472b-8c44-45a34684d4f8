<template>
  <!-- 完全重写的卡片，使用内联样式确保生效 -->
  <div
    :style="{
      background: 'white',
      borderRadius: '8px',
      padding: '20px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      transition: 'all 0.3s ease',
      height: 'auto',
      display: 'flex',
      flexDirection: 'column',
      marginBottom: '0'
    }"
    @mouseenter="onHover"
    @mouseleave="onLeave"
    ref="cardRef"
  >
    <!-- 卡片头部 -->
    <div :style="{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '16px'
    }">
      <h3 :style="{
        fontSize: '18px',
        fontWeight: '600',
        margin: '0',
        color: '#303133',
        lineHeight: '1.4',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        display: '-webkit-box',
        WebkitLineClamp: '2',
        WebkitBoxOrient: 'vertical'
      }" :title="rule.rule_name">{{ rule.rule_name }}</h3>
      <StatusTag :status="rule.status" />
    </div>

    <!-- 规则描述区域 -->
    <div v-if="rule.description && rule.description.trim()" :style="{
      minHeight: '60px',
      display: 'flex',
      alignItems: 'flex-start',
      marginBottom: '16px',
      flex: '1'
    }">
      <el-tooltip
        :content="formatDescription(rule.description, false)"
        placement="top"
        effect="dark"
        :show-after="500"
        :hide-after="3000"
        :enterable="true"
        popper-class="rule-description-tooltip"
      >
        <div :style="{
          width: '100%',
          margin: '0',
          fontSize: '14px',
          lineHeight: '1.5',
          color: '#606266',
          wordBreak: 'break-word',
          cursor: 'help'
        }" ref="descriptionRef">
          <p :style="{ margin: '0' }" v-html="formatDescription(rule.description, true)"></p>
        </div>
      </el-tooltip>
    </div>
    <div v-else :style="{
      minHeight: '60px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: '16px',
      flex: '1'
    }">
      <p :style="{
        margin: '0',
        fontSize: '14px',
        color: '#C0C4CC',
        fontStyle: 'italic'
      }">暂无规则描述</p>
    </div>

    <!-- 卡片操作区 -->
    <div :style="{
      display: 'flex',
      gap: '8px',
      justifyContent: 'flex-end',
      marginTop: 'auto'
    }">
      <el-button
        size="small"
        :icon="View"
        :disabled="rule.status === 'DEPRECATED'"
        @click="handleViewDetail"
        :title="rule.status === 'DEPRECATED' ? '已弃用的规则模板无法查看详情' : '查看规则模板详情'"
        :style="{
          padding: '8px 16px',
          border: '1px solid #dcdfe6',
          borderRadius: '4px',
          background: 'white',
          fontSize: '14px',
          height: 'auto',
          minHeight: 'auto'
        }"
      >
        详情
      </el-button>
      <el-button
        size="small"
        :icon="Download"
        :loading="downloadLoading"
        :disabled="rule.status === 'DEPRECATED'"
        @click="handleDownloadTemplate"
        :title="rule.status === 'DEPRECATED' ? '已弃用的规则模板无法下载' : '下载模板'"
        :style="{
          padding: '8px 16px',
          border: '1px solid #dcdfe6',
          borderRadius: '4px',
          background: 'white',
          fontSize: '14px',
          height: 'auto',
          minHeight: 'auto'
        }"
      >
        模板
      </el-button>
      <el-button
        type="primary"
        size="small"
        :icon="Upload"
        :disabled="rule.status === 'DEPRECATED'"
        @click="handleUploadData"
        :title="rule.status === 'DEPRECATED' ? '已弃用的规则模板无法上传数据' : '上传数据'"
        :style="{
          padding: '8px 16px',
          borderRadius: '4px',
          fontSize: '14px',
          height: 'auto',
          minHeight: 'auto'
        }"
      >
        上传
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { View, Download, Upload } from '@element-plus/icons-vue'
// import RCard from '../../design-system/components/base/RCard.vue' // 不再使用
// import RButton from '../../design-system/components/base/RButton.vue' // 不再使用
import StatusTag from '../common/StatusTag.vue'
import { formatDate } from '../../utils/dateUtils'

const props = defineProps({
  rule: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'view-detail',
  'download-template',
  'upload-data'
])

const router = useRouter()
const downloadLoading = ref(false)
const descriptionRef = ref(null)
const cardRef = ref(null)

// 悬浮效果
const onHover = () => {
  if (cardRef.value) {
    cardRef.value.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)'
    cardRef.value.style.transform = 'translateY(-2px)'
  }
}

const onLeave = () => {
  if (cardRef.value) {
    cardRef.value.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)'
    cardRef.value.style.transform = 'translateY(0)'
  }
}

// 描述文本截断配置
const MAX_DESCRIPTION_LENGTH = 100

// 格式化规则描述文本，处理换行符
const formatDescription = (text, truncate = false) => {
  if (!text) return ''

  // 统一处理不同系统的换行符
  let formattedText = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n')

  // 处理多个连续换行为最多两个换行（避免过多空白）
  formattedText = formattedText.replace(/\n{3,}/g, '\n\n')

  // 如果需要截断
  if (truncate && formattedText.length > MAX_DESCRIPTION_LENGTH) {
    // 在截断点查找最近的换行符
    const truncatePoint = formattedText.substring(0, MAX_DESCRIPTION_LENGTH).lastIndexOf('\n')

    // 如果在合理范围内找到换行符，就在换行处截断
    if (truncatePoint > 70) {
      formattedText = formattedText.substring(0, truncatePoint) + '...'
    } else {
      // 否则在100字符处截断
      formattedText = formattedText.substring(0, MAX_DESCRIPTION_LENGTH) + '...'
    }
  }

  // 将换行符转换为HTML换行标签，并处理序号和缩进
  return formattedText
    .split('\n')
    .map(line => {
      // 识别并增强序号格式（如：1. 2. 等）
      return line.replace(/^(\d+\.)\s+(.*)/, '<span class="list-number">$1</span> $2')
    })
    .join('<br>')
}

// 计算截断后的描述文本
const truncatedDescription = computed(() => {
  if (!props.rule.description) return ''
  return formatDescription(props.rule.description, true)
})

// 判断描述是否被截断
const isDescriptionTruncated = computed(() => {
  if (!props.rule.description) return false
  return props.rule.description.trim().length > MAX_DESCRIPTION_LENGTH
})

const handleViewDetail = () => {
  // 检查rule_key是否存在
  if (!props.rule?.rule_key) {
    ElMessage.error('规则键不存在，无法查看详情')
    return
  }
  // 跳转到规则模板详情页面
  router.push(`/rule-template-detail/${props.rule.rule_key}`)
}

const handleDownloadTemplate = async () => {
  // 检查rule_key是否存在
  if (!props.rule?.rule_key) {
    ElMessage.error('规则键不存在，无法下载模板')
    return
  }

  downloadLoading.value = true
  try {
    // NOTE: The emit function itself doesn't return a promise.
    // If the parent listener is async, you can't await it here directly.
    // This assumes the parent handles loading state based on the event.
    emit('download-template', props.rule)
  } finally {
    // A slight delay to allow UI to update, and prevent flicker if download is instant
    setTimeout(() => {
      downloadLoading.value = false
    }, 300)
  }
}

const handleUploadData = () => {
  // 检查rule_key是否存在
  if (!props.rule?.rule_key) {
    ElMessage.error('规则键不存在，无法上传数据')
    return
  }
  emit('upload-data', props.rule)
}
</script>

<style>
/* 全局样式，不受scoped限制 */
.rule-description-tooltip {
  max-width: 300px !important;
  line-height: 1.5 !important;
  white-space: normal !important;
  word-break: break-word !important;
}

.rule-description-tooltip .el-tooltip__popper-content {
  white-space: pre-line !important;
}

.rule-description-tooltip .list-number {
  font-weight: 600;
  color: #409eff;
  margin-right: 2px;
}
</style>

<style scoped>
/* 规则卡片样式 */
.rule-status-card {
  height: auto !important; /* 改为自动高度，根据内容调整 */
  min-height: 100px !important; /* 进一步减少最小高度 */
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(235, 238, 245, 0.8);
  margin-bottom: 0; /* 移除底部边距，由父容器控制间距 */
}

.rule-status-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
}

.rule-status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #53a8ff);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rule-status-card:hover::before {
  opacity: 1;
}

/* 规则头部 */
.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 8px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(235, 238, 245, 0.8);
}

.rule-info {
  flex: 1;
  min-width: 0; /* 允许文本截断 */
}

.rule-name {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.5;

  /* 文本截断 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 规则内容 */
.rule-content {
  flex: 0 1 auto; /* 改为不拉伸，只根据内容大小 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 防止flex子元素过度拉伸 */
  margin-bottom: 8px; /* 添加与footer的间距 */
}

/* 规则描述区域 */
.rule-description {
  min-height: 50px;
  display: flex;
  align-items: flex-start;
  background-color: rgba(249, 250, 252, 0.7);
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #e6e8eb;
  transition: all 0.3s ease;
}

.rule-description:hover {
  background-color: rgba(249, 250, 252, 0.9);
  border-left-color: #409eff;
}

.description-content {
  width: 100%;
}

.description-text {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--el-text-color-regular);
  word-break: break-word;
  cursor: help;
}

.description-text p {
  margin: 0;
}

/* 增强列表项样式 */
.description-text .list-number {
  font-weight: 600;
  color: #409eff;
  display: inline-block;
  min-width: 20px;
}

.rule-description-placeholder {
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(249, 250, 252, 0.7);
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #e6e8eb;
  transition: all 0.3s ease;
}

.rule-description-placeholder:hover {
  background-color: rgba(249, 250, 252, 0.9);
  border-left-color: #c0c4cc;
}

.placeholder-text {
  margin: 0;
  font-size: 0.875rem;
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

/* 规则操作区 */
.rule-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 0 !important; /* 移除额外的顶部间距 */
  padding-top: 4px !important; /* 使用更小的顶部内边距 */
  padding-bottom: 2px !important; /* 使用更小的底部内边距 */
  border-top: 1px solid rgba(235, 238, 245, 0.8);
}

.action-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: #409eff;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.action-btn:hover::after {
  width: 80%;
}

.action-btn-primary {
  background: linear-gradient(135deg, #409eff, #53a8ff);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.action-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rule-status-card {
    min-height: 140px;
  }

  .rule-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .rule-name {
    font-size: 1rem;
    margin-bottom: 8px;
  }

  .rule-actions {
    width: 100%;
    justify-content: space-between;
  }

  .description-text {
    font-size: 0.8rem;
  }

  .rule-description {
    min-height: 40px;
  }

  .rule-description-placeholder {
    min-height: 40px;
  }
}

@media (max-width: 575px) {
  .rule-status-card {
    min-height: 140px;
    margin-bottom: 4px;
  }

  .rule-description,
  .rule-description-placeholder {
    padding: 10px;
    min-height: 40px;
  }
}
</style>
