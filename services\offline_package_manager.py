"""
离线包管理器

负责离线部署包的生成、存储、分发和校验。
基于现有的gzip压缩和文件管理机制，支持包版本管理、过期清理、完整性校验等功能。
"""

import gzip
import hashlib
import json
import time
import uuid
from collections.abc import AsyncIterator
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any

from sqlalchemy.orm import Session

from config.settings import Settings
from core.logging.logging_system import log as logger
from core.version_management import RuleSyncVersionManager
from models.sync import OfflinePackageRequest, PackageInfo


class OfflinePackageManager:
    """
    离线包管理器

    处理离线部署包的生成、存储、分发和校验，
    基于现有的gzip压缩和文件管理机制。
    """

    def __init__(self, storage_path: str = "data/offline_packages", settings: Settings | None = None):
        """
        初始化离线包管理器

        Args:
            storage_path: 存储路径
            settings: 系统设置
        """
        self.storage_path = Path(storage_path)
        self.settings = settings or Settings()
        self.version_manager = RuleSyncVersionManager()

        # 创建存储目录
        self.storage_path.mkdir(parents=True, exist_ok=True)

        # 包索引文件
        self.index_file = self.storage_path / "packages_index.json"

        # 包索引缓存
        self.packages_index: dict[str, dict[str, Any]] = {}

        # 加载包索引
        self._load_packages_index()

        logger.info(f"OfflinePackageManager initialized: storage={self.storage_path}")

    def _load_packages_index(self):
        """加载包索引"""
        try:
            if self.index_file.exists():
                with open(self.index_file, "r", encoding="utf-8") as f:
                    self.packages_index = json.load(f)
                logger.debug(f"加载包索引: {len(self.packages_index)} 个包")
            else:
                self.packages_index = {}
                logger.debug("包索引文件不存在，创建空索引")
        except Exception as e:
            logger.error(f"加载包索引失败: {e}")
            self.packages_index = {}

    def _save_packages_index(self):
        """保存包索引"""
        try:
            with open(self.index_file, "w", encoding="utf-8") as f:
                json.dump(self.packages_index, f, ensure_ascii=False, indent=2, default=str)
            logger.debug(f"保存包索引: {len(self.packages_index)} 个包")
        except Exception as e:
            logger.error(f"保存包索引失败: {e}")

    def _generate_package_id(self) -> str:
        """生成包ID"""
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]
        return f"pkg_{timestamp}_{unique_id}"

    def _calculate_checksum(self, data: bytes) -> str:
        """计算数据校验和"""
        return hashlib.sha256(data).hexdigest()

    def _get_package_file_path(self, package_id: str) -> Path:
        """获取包文件路径"""
        return self.storage_path / f"{package_id}.json.gz"

    async def generate_package(self, request: OfflinePackageRequest, session: Session | None = None) -> PackageInfo:
        """
        生成离线部署包

        Args:
            request: 离线包请求
            session: 数据库会话（可选）

        Returns:
            PackageInfo: 包信息
        """
        package_id = self._generate_package_id()
        package_name = request.package_name or f"offline_package_{package_id}"

        logger.info(f"开始生成离线包: {package_id}, name: {package_name}")

        try:
            # 1. 获取规则数据
            rules_data = await self._get_rules_data(request.rule_keys, session)

            # 2. 生成版本号
            version = self.version_manager.generate_version(
                {"package_id": package_id, "rule_keys": request.rule_keys or [], "timestamp": time.time()}
            )

            # 3. 构建包数据
            package_data = {
                "package_info": {
                    "package_id": package_id,
                    "package_name": package_name,
                    "version": version,
                    "created_at": datetime.now().isoformat(),
                    "expires_at": (datetime.now() + timedelta(days=request.expiry_days)).isoformat(),
                    "compression_level": request.compression_level,
                    "format_version": "2.0",
                    "description": request.description,
                    "tags": request.tags,
                },
                "metadata": {
                    "total_rules": len(rules_data.get("rule_datasets", [])),
                    "rule_keys": request.rule_keys or [],
                    "include_metadata": request.include_metadata,
                    "generation_timestamp": time.time(),
                },
                "data": rules_data,
                "integrity": {},
            }

            # 4. 序列化和压缩（不包含校验和）
            json_data = json.dumps(package_data, ensure_ascii=False, indent=None)
            compressed_data = gzip.compress(json_data.encode("utf-8"), compresslevel=request.compression_level)

            # 5. 计算校验和（基于不包含校验和的数据）
            checksum = self._calculate_checksum(compressed_data)
            package_data["integrity"]["data_checksum"] = checksum

            # 6. 重新序列化包含校验和的最终数据
            json_data = json.dumps(package_data, ensure_ascii=False, indent=None)
            compressed_data = gzip.compress(json_data.encode("utf-8"), compresslevel=request.compression_level)

            # 6. 保存包文件
            package_file_path = self._get_package_file_path(package_id)
            with open(package_file_path, "wb") as f:
                f.write(compressed_data)

            # 7. 创建包信息
            package_info = PackageInfo(
                package_id=package_id,
                package_name=package_name,
                version=version,
                size_bytes=len(compressed_data),
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(days=request.expiry_days),
                rule_count=len(rules_data.get("rule_datasets", [])),
                checksum=checksum,
                description=request.description,
                tags=request.tags,
                metadata={
                    "compression_level": request.compression_level,
                    "original_size": len(json_data),
                    "compression_ratio": len(json_data) / len(compressed_data) if compressed_data else 1.0,
                },
            )

            # 8. 更新包索引
            self.packages_index[package_id] = package_info.model_dump()
            self._save_packages_index()

            logger.info(
                f"离线包生成完成: {package_id}, size: {len(compressed_data)} bytes, rules: {package_info.rule_count}, "
                f"compression: {package_info.metadata.get('compression_ratio', 1.0):.2f}x"
            )

            return package_info

        except Exception as e:
            logger.error(f"生成离线包失败: {package_id}, error: {e}", exc_info=True)
            # 清理可能创建的文件
            package_file_path = self._get_package_file_path(package_id)
            if package_file_path.exists():
                package_file_path.unlink()
            raise

    async def _get_rules_data(self, rule_keys: list[str] | None, session: Session | None) -> dict[str, Any]:
        """获取规则数据"""
        try:
            if session:
                # 从数据库获取规则数据
                from services.rule_loader import load_rules_from_db

                rules_by_key = load_rules_from_db(session)

                # 如果指定了规则键，则过滤
                if rule_keys:
                    filtered_rules = {k: v for k, v in rules_by_key.items() if k in rule_keys}
                    rules_by_key = filtered_rules

                # 转换为导出格式
                rule_datasets = []
                for rule_key, rule_data_list in rules_by_key.items():
                    dataset_entry = {"rule_key": rule_key, "rule_data": rule_data_list, "count": len(rule_data_list)}
                    rule_datasets.append(dataset_entry)

                return {
                    "rule_datasets": rule_datasets,
                    "export_timestamp": datetime.now().isoformat(),
                    "total_count": len(rule_datasets),
                }
            else:
                # 从缓存文件获取规则数据
                from services.rule_loader import load_rules_from_file

                cache_file = Path("rules_cache.json.gz")
                if cache_file.exists():
                    rules_data = load_rules_from_file(str(cache_file))

                    # 如果指定了规则键，则过滤
                    if rule_keys and "rule_datasets" in rules_data:
                        filtered_datasets = [
                            ds for ds in rules_data["rule_datasets"] if ds.get("rule_key") in rule_keys
                        ]
                        rules_data["rule_datasets"] = filtered_datasets
                        rules_data["total_count"] = len(filtered_datasets)

                    return rules_data
                else:
                    logger.warning("缓存文件不存在，返回空数据")
                    return {"rule_datasets": [], "export_timestamp": datetime.now().isoformat(), "total_count": 0}

        except Exception as e:
            logger.error(f"获取规则数据失败: {e}")
            raise

    async def list_packages(self) -> list[PackageInfo]:
        """
        列出可用包

        Returns:
            List[PackageInfo]: 包信息列表
        """
        packages = []
        current_time = datetime.now()

        for package_id, package_data in self.packages_index.items():
            try:
                # 检查包文件是否存在
                package_file_path = self._get_package_file_path(package_id)
                if not package_file_path.exists():
                    logger.warning(f"包文件不存在: {package_id}")
                    continue

                # 创建包信息对象
                package_info = PackageInfo(**package_data)

                # 检查是否过期
                package_info.is_expired = package_info.expires_at < current_time

                packages.append(package_info)

            except Exception as e:
                logger.error(f"解析包信息失败: {package_id}, error: {e}")
                continue

        # 按创建时间倒序排列
        packages.sort(key=lambda p: p.created_at, reverse=True)

        return packages

    async def get_package_info(self, package_id: str) -> PackageInfo | None:
        """
        获取包信息

        Args:
            package_id: 包ID

        Returns:
            PackageInfo: 包信息，如果不存在则返回None
        """
        if package_id not in self.packages_index:
            return None

        try:
            package_data = self.packages_index[package_id]
            package_info = PackageInfo(**package_data)

            # 检查是否过期
            package_info.is_expired = package_info.expires_at < datetime.now()

            return package_info

        except Exception as e:
            logger.error(f"获取包信息失败: {package_id}, error: {e}")
            return None

    async def get_package_stream(self, package_id: str) -> AsyncIterator[bytes]:
        """
        获取包文件流

        Args:
            package_id: 包ID

        Yields:
            bytes: 文件数据块
        """
        package_file_path = self._get_package_file_path(package_id)

        if not package_file_path.exists():
            logger.error(f"包文件不存在: {package_id}")
            raise FileNotFoundError(f"Package file not found: {package_id}")

        try:
            # 更新下载统计
            await self._update_download_stats(package_id)

            # 流式读取文件
            chunk_size = 8192  # 8KB chunks
            with open(package_file_path, "rb") as f:
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    yield chunk

            logger.info(f"包文件流传输完成: {package_id}")

        except Exception as e:
            logger.error(f"获取包文件流失败: {package_id}, error: {e}")
            raise

    async def _update_download_stats(self, package_id: str):
        """更新下载统计"""
        if package_id in self.packages_index:
            package_data = self.packages_index[package_id]
            package_data["download_count"] = package_data.get("download_count", 0) + 1
            package_data["last_downloaded"] = datetime.now().isoformat()
            self._save_packages_index()

    async def delete_package(self, package_id: str) -> bool:
        """
        删除包

        Args:
            package_id: 包ID

        Returns:
            bool: 是否删除成功
        """
        try:
            # 删除包文件
            package_file_path = self._get_package_file_path(package_id)
            if package_file_path.exists():
                package_file_path.unlink()
                logger.info(f"删除包文件: {package_id}")

            # 从索引中移除
            if package_id in self.packages_index:
                del self.packages_index[package_id]
                self._save_packages_index()
                logger.info(f"从索引中移除包: {package_id}")

            return True

        except Exception as e:
            logger.error(f"删除包失败: {package_id}, error: {e}")
            return False

    async def cleanup_expired_packages(self) -> int:
        """
        清理过期包

        注意：当系统中仅有一个离线包时，即使过期也不会删除，
        以确保从节点在离线模式下始终有可用的规则数据。

        Returns:
            int: 清理的包数量
        """
        current_time = datetime.now()
        expired_packages = []
        total_packages = len(self.packages_index)

        # 查找过期包
        for package_id, package_data in self.packages_index.items():
            try:
                expires_at = datetime.fromisoformat(package_data["expires_at"])
                if expires_at < current_time:
                    expired_packages.append(package_id)
            except Exception as e:
                logger.error(f"解析过期时间失败: {package_id}, error: {e}")
                continue

        # 安全检查：如果只有一个包，即使过期也不删除
        if total_packages == 1 and len(expired_packages) == 1:
            logger.warning(f"系统中仅有一个离线包 {expired_packages[0]}，即使过期也不删除以确保服务可用性")
            return 0

        # 删除过期包
        cleaned_count = 0
        for package_id in expired_packages:
            if await self.delete_package(package_id):
                cleaned_count += 1

        if cleaned_count > 0:
            logger.info(f"清理过期包完成: {cleaned_count} 个包")

        return cleaned_count

    async def verify_package_integrity(self, package_id: str) -> bool:
        """
        验证包完整性

        Args:
            package_id: 包ID

        Returns:
            bool: 是否完整
        """
        try:
            package_file_path = self._get_package_file_path(package_id)
            if not package_file_path.exists():
                logger.error(f"包文件不存在: {package_id}")
                return False

            # 读取包文件
            with open(package_file_path, "rb") as f:
                compressed_data = f.read()

            # 解压缩
            try:
                json_data = gzip.decompress(compressed_data).decode("utf-8")
                package_data = json.loads(json_data)
            except Exception as e:
                logger.error(f"解压缩或解析包文件失败: {package_id}, error: {e}")
                return False

            # 验证校验和
            expected_checksum = package_data.get("integrity", {}).get("data_checksum")
            if expected_checksum:
                # 获取原始压缩级别
                compression_level = package_data.get("package_info", {}).get("compression_level", 6)

                # 重新计算校验和（不包含integrity部分，使用相同压缩级别）
                temp_data = package_data.copy()
                temp_data["integrity"] = {}
                temp_json = json.dumps(temp_data, ensure_ascii=False, indent=None)
                temp_compressed = gzip.compress(temp_json.encode("utf-8"), compresslevel=compression_level)
                actual_checksum = self._calculate_checksum(temp_compressed)

                if actual_checksum != expected_checksum:
                    logger.error(
                        f"包校验和不匹配: {package_id}, expected: {expected_checksum}, actual: {actual_checksum}"
                    )
                    return False

            # 验证基本结构
            required_keys = ["package_info", "metadata", "data"]
            for key in required_keys:
                if key not in package_data:
                    logger.error(f"包结构不完整，缺少: {key}")
                    return False

            logger.debug(f"包完整性验证通过: {package_id}")
            return True

        except Exception as e:
            logger.error(f"验证包完整性失败: {package_id}, error: {e}")
            return False

    async def get_storage_stats(self) -> dict[str, Any]:
        """
        获取存储统计信息

        Returns:
            Dict[str, Any]: 存储统计信息
        """
        try:
            total_packages = len(self.packages_index)
            total_size = 0
            expired_count = 0
            current_time = datetime.now()

            for package_id, package_data in self.packages_index.items():
                # 计算总大小
                package_file_path = self._get_package_file_path(package_id)
                if package_file_path.exists():
                    total_size += package_file_path.stat().st_size

                # 统计过期包
                try:
                    expires_at = datetime.fromisoformat(package_data["expires_at"])
                    if expires_at < current_time:
                        expired_count += 1
                except Exception:
                    pass

            return {
                "total_packages": total_packages,
                "total_size_bytes": total_size,
                "total_size_mb": total_size / (1024 * 1024),
                "expired_packages": expired_count,
                "active_packages": total_packages - expired_count,
                "storage_path": str(self.storage_path),
                "index_file_size": self.index_file.stat().st_size if self.index_file.exists() else 0,
            }

        except Exception as e:
            logger.error(f"获取存储统计失败: {e}")
            return {
                "total_packages": 0,
                "total_size_bytes": 0,
                "total_size_mb": 0.0,
                "expired_packages": 0,
                "active_packages": 0,
                "storage_path": str(self.storage_path),
                "index_file_size": 0,
            }


# 全局离线包管理器实例
_offline_package_manager: OfflinePackageManager | None = None


def get_offline_package_manager() -> OfflinePackageManager:
    """
    获取全局离线包管理器实例

    Returns:
        OfflinePackageManager: 离线包管理器实例
    """
    global _offline_package_manager
    if _offline_package_manager is None:
        _offline_package_manager = OfflinePackageManager()
    return _offline_package_manager


def reset_offline_package_manager():
    """重置全局离线包管理器实例（主要用于测试）"""
    global _offline_package_manager
    _offline_package_manager = None
