"""
智能缓存系统
实现LRU结果缓存、规则缓存和性能优化
"""

import threading
import time
from collections import OrderedDict
from dataclasses import dataclass
from enum import Enum
from typing import Any

from core.logging.logging_system import log as logger
from models.rule import RuleResult


class CacheType(Enum):
    """缓存类型"""

    RULE_RESULT = "rule_result"
    PATIENT_HASH = "patient_hash"
    COMPUTATION = "computation"

    # 同步相关缓存类型
    SYNC_VERSION = "sync_version"
    SYNC_CHANGES = "sync_changes"
    SYNC_PACKAGE = "sync_package"
    SYNC_RESULT = "sync_result"


@dataclass
class CacheEntry:
    """缓存条目"""

    key: str
    value: Any
    timestamp: float
    access_count: int
    cache_type: CacheType
    size_bytes: int = 0

    def update_access(self):
        """更新访问信息"""
        self.access_count += 1
        self.timestamp = time.perf_counter()


@dataclass
class CacheStats:
    """缓存统计信息"""

    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    evictions: int = 0
    total_size_bytes: int = 0
    max_size_bytes: int = 0

    @property
    def hit_rate(self) -> float:
        """缓存命中率"""
        if self.total_requests == 0:
            return 0.0
        return self.cache_hits / self.total_requests * 100

    @property
    def memory_usage_mb(self) -> float:
        """内存使用量（MB）"""
        return self.total_size_bytes / (1024 * 1024)


class LRUCache:
    """
    LRU缓存实现
    支持多种缓存策略和自动过期
    """

    def __init__(
        self, max_size: int = 10000, max_memory_mb: int = 100, ttl_seconds: int = 3600, cleanup_interval: int = 300
    ):
        """
        初始化LRU缓存

        Args:
            max_size: 最大缓存条目数
            max_memory_mb: 最大内存使用量（MB）
            ttl_seconds: 缓存过期时间（秒）
            cleanup_interval: 清理间隔（秒）
        """
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.ttl_seconds = ttl_seconds
        self.cleanup_interval = cleanup_interval

        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self._stats = CacheStats(max_size_bytes=self.max_memory_bytes)

        # 启动后台清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self._cleanup_thread.start()

        logger.info(f"LRUCache initialized: max_size={max_size}, max_memory={max_memory_mb}MB, ttl={ttl_seconds}s")

    def get(self, key: str) -> Any | None:
        """获取缓存值"""
        with self._lock:
            self._stats.total_requests += 1

            if key not in self._cache:
                self._stats.cache_misses += 1
                return None

            entry = self._cache[key]

            # 检查是否过期
            if self._is_expired(entry):
                self._remove_entry(key)
                self._stats.cache_misses += 1
                return None

            # 更新访问信息并移到末尾（最近使用）
            entry.update_access()
            self._cache.move_to_end(key)

            self._stats.cache_hits += 1
            return entry.value

    def put(self, key: str, value: Any, cache_type: CacheType = CacheType.COMPUTATION) -> bool:
        """存储缓存值"""
        with self._lock:
            # 估算值的大小
            size_bytes = self._estimate_size(value)

            # 检查是否超过单个条目大小限制
            if size_bytes > self.max_memory_bytes // 10:  # 单个条目不超过总内存的10%
                logger.warning(f"Cache entry too large: {size_bytes} bytes, skipping")
                return False

            # 如果键已存在，先删除旧条目
            if key in self._cache:
                self._remove_entry(key)

            # 确保有足够空间
            self._ensure_capacity(size_bytes)

            # 创建新条目
            entry = CacheEntry(
                key=key,
                value=value,
                timestamp=time.perf_counter(),
                access_count=1,
                cache_type=cache_type,
                size_bytes=size_bytes,
            )

            self._cache[key] = entry
            self._stats.total_size_bytes += size_bytes

            return True

    def remove(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            if key in self._cache:
                self._remove_entry(key)
                return True
            return False

    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._stats.total_size_bytes = 0
            logger.info("Cache cleared")

    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._lock:
            return CacheStats(
                total_requests=self._stats.total_requests,
                cache_hits=self._stats.cache_hits,
                cache_misses=self._stats.cache_misses,
                evictions=self._stats.evictions,
                total_size_bytes=self._stats.total_size_bytes,
                max_size_bytes=self._stats.max_size_bytes,
            )

    def _remove_entry(self, key: str):
        """删除缓存条目（内部方法）"""
        if key in self._cache:
            entry = self._cache.pop(key)
            self._stats.total_size_bytes -= entry.size_bytes

    def _is_expired(self, entry: CacheEntry) -> bool:
        """检查条目是否过期"""
        return time.perf_counter() - entry.timestamp > self.ttl_seconds

    def _ensure_capacity(self, new_size: int):
        """确保有足够的缓存容量"""
        # 按数量限制清理
        while len(self._cache) >= self.max_size:
            self._evict_lru()

        # 按内存限制清理
        while (self._stats.total_size_bytes + new_size) > self.max_memory_bytes:
            if not self._cache:
                break
            self._evict_lru()

    def _evict_lru(self):
        """驱逐最近最少使用的条目"""
        if self._cache:
            key, _ = self._cache.popitem(last=False)  # 删除最旧的条目
            self._stats.evictions += 1

    def _estimate_size(self, value: Any) -> int:
        """估算对象大小"""
        if isinstance(value, str):
            return len(value.encode("utf-8"))
        elif isinstance(value, int | float):
            return 8
        elif isinstance(value, list):
            return sum(self._estimate_size(item) for item in value) + 64
        elif isinstance(value, dict):
            return sum(self._estimate_size(k) + self._estimate_size(v) for k, v in value.items()) + 64
        elif hasattr(value, "__dict__"):
            return sum(self._estimate_size(v) for v in value.__dict__.values()) + 128
        else:
            return 128  # 默认估算

    def _cleanup_loop(self):
        """后台清理循环"""
        while True:
            try:
                time.sleep(self.cleanup_interval)
                self._cleanup_expired()
            except Exception as e:
                logger.error(f"Error in cache cleanup: {e}")

    def _cleanup_expired(self):
        """清理过期条目"""
        with self._lock:
            expired_keys = []
            for key, entry in self._cache.items():
                if self._is_expired(entry):
                    expired_keys.append(key)

            for key in expired_keys:
                self._remove_entry(key)

            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")


class IntelligentCacheManager:
    """
    智能缓存管理器
    管理多种类型的缓存，提供统一接口
    """

    def __init__(self):
        """初始化缓存管理器"""
        # 规则结果缓存（较大容量，较长TTL）
        self.rule_result_cache = LRUCache(
            max_size=50000,
            max_memory_mb=200,
            ttl_seconds=7200,  # 2小时
            cleanup_interval=600,
        )

        # 患者哈希缓存（中等容量，中等TTL）
        self.patient_hash_cache = LRUCache(
            max_size=10000,
            max_memory_mb=50,
            ttl_seconds=3600,  # 1小时
            cleanup_interval=300,
        )

        # 计算结果缓存（小容量，短TTL）
        self.computation_cache = LRUCache(
            max_size=5000,
            max_memory_mb=25,
            ttl_seconds=1800,  # 30分钟
            cleanup_interval=180,
        )

        logger.info("IntelligentCacheManager initialized")

        # 初始化降级适配器
        self._degradation_adapter = None
        self._init_degradation_adapter()

    def get_rule_result(self, patient_hash: str, rule_id: str) -> RuleResult | None:
        """获取规则结果缓存"""
        cache_key = f"rule:{patient_hash}:{rule_id}"
        return self.rule_result_cache.get(cache_key)

    def cache_rule_result(self, patient_hash: str, rule_id: str, result: RuleResult) -> bool:
        """缓存规则结果"""
        cache_key = f"rule:{patient_hash}:{rule_id}"
        return self.rule_result_cache.put(cache_key, result, CacheType.RULE_RESULT)

    def get_patient_hash(self, patient_data_key: str) -> str | None:
        """获取患者数据哈希"""
        return self.patient_hash_cache.get(patient_data_key)

    def cache_patient_hash(self, patient_data_key: str, hash_value: str) -> bool:
        """缓存患者数据哈希"""
        return self.patient_hash_cache.put(patient_data_key, hash_value, CacheType.PATIENT_HASH)

    def get_computation_result(self, computation_key: str) -> Any | None:
        """获取计算结果"""
        return self.computation_cache.get(computation_key)

    def cache_computation_result(self, computation_key: str, result: Any) -> bool:
        """缓存计算结果"""
        return self.computation_cache.put(computation_key, result, CacheType.COMPUTATION)

    def get_overall_stats(self) -> dict[str, CacheStats]:
        """获取所有缓存的统计信息"""
        return {
            "rule_result": self.rule_result_cache.get_stats(),
            "patient_hash": self.patient_hash_cache.get_stats(),
            "computation": self.computation_cache.get_stats(),
        }

    def clear_all(self):
        """清空所有缓存"""
        self.rule_result_cache.clear()
        self.patient_hash_cache.clear()
        self.computation_cache.clear()
        logger.info("All caches cleared")

    def _init_degradation_adapter(self):
        """初始化降级适配器"""
        try:
            from core.degradation_adapters import IntelligentCacheAdapter, get_adapter_manager

            # 创建适配器
            self._degradation_adapter = IntelligentCacheAdapter(self)

            # 注册到适配器管理器
            adapter_manager = get_adapter_manager()
            adapter_manager.register_adapter(self._degradation_adapter)

            logger.info("IntelligentCache degradation adapter initialized and registered")

        except ImportError:
            logger.debug("Degradation adapters not available, skipping initialization")
        except Exception as e:
            logger.warning(f"Failed to initialize degradation adapter: {e}")

    def get_degradation_adapter(self):
        """获取降级适配器"""
        return self._degradation_adapter


# 全局缓存管理器实例
intelligent_cache = IntelligentCacheManager()
