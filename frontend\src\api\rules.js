import { get, post, downloadFile } from './request'

/**
 * 获取所有规则状态
 * @returns {Promise<Array>} 规则列表
 */
export function getRulesStatus() {
  return get('/v1/rules/status')
}

/**
 * 获取规则详细信息
 * @param {string} ruleKey - 规则键
 * @returns {Promise<Object>} 规则详情
 */
export function getRuleDetail(ruleKey) {
  return get(`/v1/rules/${ruleKey}/detail`)
}

/**
 * 获取规则Schema
 * @param {string} ruleKey - 规则键
 * @returns {Promise<Array>} 规则Schema
 */
export function getRuleSchema(ruleKey) {
  return get(`/v1/rules/${ruleKey}/schema`)
}

/**
 * 获取规则模板详情（别名函数，与 getRuleDetail 功能相同）
 * @param {string} ruleKey - 规则键
 * @returns {Promise<Object>} 规则模板详情
 * @deprecated 建议使用 getRuleDetail 函数
 */
export function getRuleTemplateDetail(ruleKey) {
  // 为了保持向后兼容性，调用 getRuleDetail 函数
  return getRuleDetail(ruleKey)
}

/**
 * 下载规则模板
 * @param {string} ruleKey - 规则键
 * @param {string} ruleName - 规则名称
 * @returns {Promise} 下载Promise
 */
export function downloadRuleTemplate(ruleKey, ruleName = '') {
  const filename = `${ruleName || ruleKey}-规则模板.xlsx`
  return downloadFile(`/v1/rules/${ruleKey}/template`, {}, filename)
}

/**
 * 确认提交规则数据
 * @param {string} ruleKey - 规则键
 * @param {Object} submissionData - 提交数据
 * @returns {Promise<Object>} 提交结果
 */
export function confirmRuleSubmission(ruleKey, submissionData) {
  return post(`/v1/rules/${ruleKey}/confirm_submission`, submissionData)
}

/**
 * 获取规则统计信息
 * @param {string} ruleKey - 规则键
 * @returns {Promise<Object>} 统计信息
 */
export function getRuleStatistics(ruleKey) {
  return get(`/v1/rules/${ruleKey}/statistics`)
}

/**
 * 获取规则执行历史
 * @param {string} ruleKey - 规则键
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 执行历史
 */
export function getRuleExecutionHistory(ruleKey, params = {}) {
  return get(`/v1/rules/${ruleKey}/execution-history`, params)
}

/**
 * 搜索规则
 * @param {Object} searchParams - 搜索参数
 * @returns {Promise<Array>} 搜索结果
 */
export function searchRules(searchParams) {
  return get('/v1/rules/search', searchParams)
}

/**
 * 获取规则版本信息
 * @returns {Promise<Object>} 版本信息
 */
export function getRulesVersion() {
  return get('/v1/rules/version')
}

/**
 * 导出规则数据
 * @returns {Promise<Object>} 导出数据
 */
export function exportRulesData() {
  return get('/v1/rules/export')
}

/**
 * 批量操作规则
 * @param {string} action - 操作类型
 * @param {Array} ruleKeys - 规则键列表
 * @param {Object} params - 操作参数
 * @returns {Promise<Object>} 操作结果
 */
export function batchOperateRules(action, ruleKeys, params = {}) {
  return post('/v1/rules/batch-operate', {
    action,
    rule_keys: ruleKeys,
    params
  })
}

/**
 * 验证规则数据
 * @param {string} ruleKey - 规则键
 * @param {Array} data - 待验证数据
 * @returns {Promise<Object>} 验证结果
 */
export function validateRuleData(ruleKey, data) {
  return post(`/v1/rules/${ruleKey}/validate`, { data })
}

/**
 * 获取规则依赖关系
 * @param {string} ruleKey - 规则键
 * @returns {Promise<Object>} 依赖关系
 */
export function getRuleDependencies(ruleKey) {
  return get(`/v1/rules/${ruleKey}/dependencies`)
}

// ===== 规则明细管理 API =====
// 注意：规则明细相关的API函数已迁移到 frontend/src/api/ruleDetails.js
// 请使用 import { functionName } from './ruleDetails' 来导入规则明细相关函数

/**
 * 更新规则配置
 * @param {string} ruleKey - 规则键
 * @param {Object} config - 配置数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateRuleConfig(ruleKey, config) {
  return post(`/v1/rules/${ruleKey}/config`, config)
}

/**
 * 获取规则日志
 * @param {string} ruleKey - 规则键
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 日志数据
 */
export function getRuleLogs(ruleKey, params = {}) {
  return get(`/v1/rules/${ruleKey}/logs`, params)
}

// ==================== 规则注册任务状态管理 ====================

/**
 * 获取规则注册任务状态
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态信息
 */
export function getRegistrationTaskStatus(taskId) {
  return get(`/v1/rules/registration/tasks/${taskId}`)
}

/**
 * 获取规则注册任务列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 任务列表
 */
export function getRegistrationTaskList(params = {}) {
  return get('/v1/rules/registration/tasks', params)
}

/**
 * 取消规则注册任务
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 取消结果
 */
export function cancelRegistrationTask(taskId) {
  return post(`/v1/rules/registration/tasks/${taskId}/cancel`)
}

/**
 * 获取规则注册服务健康状态
 * @returns {Promise<Object>} 健康状态
 */
export function getRegistrationHealth() {
  return get('/v1/rules/registration/health')
}
