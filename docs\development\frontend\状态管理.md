# 企业级状态管理架构使用指南

## 概述

本文档介绍如何使用我们的企业级状态管理架构，该架构提供了统一的异步状态管理、智能错误恢复、丰富的用户反馈和状态持久化功能。

## 核心组件

### 1. 状态机引擎 (`useStateMachine`)

提供状态转换和生命周期管理的基础设施。

```javascript
import { useStateMachine } from '@/composables/core/useStateMachine'

const stateMachine = useStateMachine()

// 状态转换
await stateMachine.start()
await stateMachine.success()
await stateMachine.error()
```

### 2. 异步状态管理 (`useAsyncState`)

统一管理所有异步操作，集成状态机、重试机制和缓存。

```javascript
import { useAsyncState } from '@/composables/core/useAsyncState'

const asyncState = useAsyncState(fetchData, {
  retryCount: 3,
  retryDelay: [1000, 2000, 4000],
  cacheKey: 'my_data',
  cacheTTL: 5 * 60 * 1000,
  onSuccess: (data) => console.log('Success:', data),
  onError: (error) => console.error('Error:', error)
})

// 执行异步操作
await asyncState.execute(params)
```

### 3. 生命周期管理 (`useAsyncLifecycle`)

为异步操作提供生命周期钩子和副作用管理。

```javascript
import { useAsyncLifecycle } from '@/composables/core/useAsyncLifecycle'

const lifecycle = useAsyncLifecycle(asyncState)

lifecycle.beforeExecute(() => {
  console.log('开始执行...')
})

lifecycle.onSuccess((eventData) => {
  console.log('执行成功:', eventData.data)
})

lifecycle.onError((eventData) => {
  console.error('执行失败:', eventData.data.error)
})
```

### 4. 错误恢复 (`useErrorRecovery`)

智能错误分类和恢复策略。

```javascript
import { useErrorRecovery } from '@/composables/core/useErrorRecovery'

const errorRecovery = useErrorRecovery()

// 执行错误恢复
const result = await errorRecovery.recover(error, {
  retryFunction: () => asyncState.retry(),
  fallbackFunction: () => loadCachedData(),
  getCachedData: () => getCachedData()
})
```

### 5. 用户反馈 (`useFeedback`)

统一的用户反馈系统，支持多种反馈类型。

```javascript
import { useFeedback } from '@/composables/ui/useFeedback'

const feedback = useFeedback()

// Toast 消息
feedback.toastSuccess('操作成功')
feedback.toastError('操作失败')

// 通知
feedback.notifyInfo('系统通知', '这是一条重要消息')

// 确认对话框
const confirmed = await feedback.confirm('确定要删除吗？')

// 加载
const loadingInstance = feedback.loading({ text: '处理中...' })
loadingInstance.hide()
```

### 6. 加载状态管理 (`useLoading`)

智能加载状态管理，支持加载合并和多种加载类型。

```javascript
import { useLoading } from '@/composables/ui/useLoading'

const loading = useLoading()

// 显示全局加载
const globalLoading = loading.showGlobalLoading('加载中...')

// 显示局部加载
const localLoading = loading.showLocalLoading(targetElement, '处理中...')

// 隐藏加载
globalLoading.hide()
```

### 7. 状态持久化 (`useStatePersistence`)

状态持久化和性能监控。

```javascript
import { useStatePersistence } from '@/composables/core/useStatePersistence'

const persistence = useStatePersistence({
  storage: 'localStorage',
  enableCompression: true,
  strategy: 'debounced'
})

// 保存状态
await persistence.saveState('user_data', userData)

// 加载状态
const userData = await persistence.loadState('user_data', defaultValue)

// 自动持久化
const stopPersistence = persistence.setupAutoPersistence(
  'preferences',
  () => userPreferences.value
)
```

## 完整使用示例

### 基础用法

```javascript
import { useAsyncState } from '@/composables/core/useAsyncState'
import { useFeedback } from '@/composables/ui/useFeedback'

export function useUserData() {
  const feedback = useFeedback()
  
  // 创建异步状态
  const userState = useAsyncState(fetchUserData, {
    retryCount: 3,
    onSuccess: (data) => {
      feedback.toastSuccess('用户数据加载成功')
    },
    onError: (error) => {
      feedback.toastError(`加载失败: ${error.message}`)
    }
  })
  
  // 加载用户数据
  const loadUser = async (userId) => {
    await userState.execute(userId)
  }
  
  return {
    userData: userState.data,
    isLoading: userState.isLoading,
    loadUser
  }
}
```

### 高级用法

```javascript
import { useAsyncState } from '@/composables/core/useAsyncState'
import { useAsyncLifecycle } from '@/composables/core/useAsyncLifecycle'
import { useErrorRecovery } from '@/composables/core/useErrorRecovery'
import { useFeedback } from '@/composables/ui/useFeedback'
import { useStatePersistence } from '@/composables/core/useStatePersistence'

export function useAdvancedUserData() {
  const feedback = useFeedback()
  const errorRecovery = useErrorRecovery()
  const persistence = useStatePersistence()
  
  // 异步状态
  const userState = useAsyncState(fetchUserData, {
    retryCount: 3,
    retryDelay: [1000, 2000, 4000],
    cacheKey: 'user_data',
    enableErrorRecovery: true
  })
  
  // 生命周期管理
  const lifecycle = useAsyncLifecycle(userState)
  
  lifecycle.onSuccess(async (eventData) => {
    // 自动保存到持久化存储
    await persistence.saveState('user_data', eventData.data.data)
    feedback.toastSuccess('数据已保存')
  })
  
  lifecycle.onError(async (eventData) => {
    // 智能错误恢复
    const result = await errorRecovery.recover(eventData.data.error, {
      retryFunction: () => userState.retry(),
      getCachedData: () => persistence.loadState('user_data')
    })
    
    if (result.success) {
      feedback.toastInfo('已使用备用方案')
    }
  })
  
  return {
    userState,
    lifecycle,
    loadUser: userState.execute,
    refreshUser: () => {
      userState.clearCache()
      return userState.execute()
    }
  }
}
```

## 最佳实践

### 1. 错误处理

- 使用 `useErrorRecovery` 进行智能错误恢复
- 为不同类型的错误配置不同的恢复策略
- 提供降级方案和缓存数据

### 2. 用户反馈

- 使用适当的反馈类型（Toast、Notification、Modal）
- 为长时间操作提供进度反馈
- 错误消息要用户友好且可操作

### 3. 性能优化

- 使用缓存减少重复请求
- 启用加载合并避免重复加载
- 合理配置重试策略

### 4. 状态持久化

- 只持久化必要的状态
- 使用合适的存储类型（localStorage、sessionStorage）
- 配置合理的缓存过期时间

### 5. 调试和监控

- 在开发环境启用调试模式
- 监控性能指标和错误率
- 使用生命周期钩子进行日志记录

## 配置选项

### 全局配置

在应用入口配置全局默认选项：

```javascript
// main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'

const app = createApp(App)
const pinia = createPinia()

// 配置全局状态管理选项
app.config.globalProperties.$stateConfig = {
  defaultRetryCount: 3,
  defaultCacheTTL: 5 * 60 * 1000,
  enableGlobalLoading: true,
  enableErrorRecovery: true
}

app.use(pinia)
```

### 环境配置

根据不同环境配置不同的选项：

```javascript
const config = {
  development: {
    enableDebug: true,
    enableMetrics: true,
    retryCount: 1
  },
  production: {
    enableDebug: false,
    enableMetrics: true,
    retryCount: 3
  }
}

export default config[process.env.NODE_ENV]
```

## 故障排除

### 常见问题

1. **状态不更新**
   - 检查是否正确使用了响应式引用
   - 确认异步操作是否成功执行

2. **缓存不生效**
   - 检查缓存键是否正确
   - 确认缓存TTL配置

3. **错误恢复不工作**
   - 检查错误类型是否正确识别
   - 确认恢复策略配置

4. **持久化失败**
   - 检查存储空间是否足够
   - 确认数据是否可序列化

### 调试技巧

1. 启用调试模式查看详细日志
2. 使用浏览器开发工具监控网络请求
3. 检查控制台错误信息
4. 使用性能监控工具分析性能问题

## 总结

企业级状态管理架构提供了完整的状态管理解决方案，包括异步操作管理、错误处理、用户反馈和状态持久化。通过合理使用这些功能，可以显著提升应用的稳定性、用户体验和开发效率。
