# 规则验证系统运维手册

## 📋 概述

本手册提供规则验证系统的日常运维指导，包括监控、故障排查、维护等操作说明。

**最新更新**：2025-07-02 - 添加主子节点规则同步修复相关运维内容

## 🔍 监控指南

### 1. 关键日志监控

#### 1.1 主节点启动日志
**正常启动流程**：
```
INFO | Master node starting up...
INFO | RuleService and request queue initialized.
INFO | Database connection pool monitoring started.
INFO | Running master node startup tasks...
INFO | Rule change detection finished.
INFO | Template generation finished.
INFO | Loading rules into memory cache...
INFO | Generating rules cache file for slave nodes...     ← 关键步骤
INFO | Rules cache file generated successfully.           ← 成功标志
INFO | Starting X queue workers...
INFO | Startup process complete.
```

**异常情况监控**：
```bash
# 监控规则文件生成失败
tail -f logs/app.log | grep -E "(Failed to generate|生成规则缓存文件失败)"

# 监控数据库连接问题
tail -f logs/app.log | grep -E "(database|connection|pool)"

# 监控内存和性能问题
tail -f logs/app.log | grep -E "(memory|performance|timeout)"
```

#### 1.2 子节点启动日志
**正常启动流程**：
```
INFO | Slave node starting up...
INFO | Loading rules from local file: rules_cache.json.gz (size: X bytes)
INFO | Successfully loaded gzip-compressed rule data      ← 格式检测成功
INFO | Rule package info - Version: XXXXXXXX..., Export time: ...
INFO | Successfully loaded X rule datasets from local file cache.
INFO | Rule sync service started
```

**异常情况监控**：
```bash
# 监控文件格式问题（修复前的错误）
tail -f logs/app.log | grep -E "(Not a gzipped file|Failed to decompress)"

# 监控文件加载失败
tail -f logs/app.log | grep -E "(file not found|Failed to load|加载失败)"

# 监控同步问题
tail -f logs/app.log | grep -E "(sync.*fail|同步.*失败|connection.*refused)"
```

### 2. 系统健康检查

#### 2.1 服务状态检查
```bash
# 检查服务运行状态
sudo systemctl status ruleengine-master
sudo systemctl status ruleengine-slave

# 检查端口监听
netstat -tlnp | grep -E ":8000|:8001"

# 检查进程状态
ps aux | grep -E "(master|slave).py"
```

#### 2.2 API健康检查
```bash
# 主节点健康检查
curl -f http://localhost:8000/health
curl -f http://localhost:8000/api/v1/rules/version

# 子节点健康检查
curl -f http://localhost:8001/health
curl -f http://localhost:8001/api/v1/rules/status

# 验证功能测试
curl -X POST http://localhost:8001/api/v1/validate \
  -H "Content-Type: application/json" \
  -d '{"patient_data": {"age": 30, "gender": "M"}}'
```

#### 2.3 文件系统检查
```bash
# 检查规则缓存文件
ls -la rules_cache.json.gz rules_version.txt

# 检查文件大小和修改时间
stat rules_cache.json.gz

# 验证文件格式
python3 -c "
import gzip, json, os
if os.path.exists('rules_cache.json.gz'):
    try:
        with gzip.open('rules_cache.json.gz', 'rt') as f:
            data = json.load(f)
        print('✓ 文件格式正确')
        print(f'规则数量: {data.get(\"total_count\", 0)}')
        print(f'版本: {data.get(\"version\", \"unknown\")[:16]}...')
        print(f'导出时间: {data.get(\"export_timestamp\", \"unknown\")}')
    except Exception as e:
        print(f'✗ 文件格式错误: {e}')
else:
    print('✗ 文件不存在')
"
```

## 🚨 故障排查

### 1. 主节点问题

#### 1.1 规则文件生成失败
**症状**：
- 日志显示 "Failed to generate rules cache file"
- 子节点无法加载规则文件

**排查步骤**：
```bash
# 1. 检查数据库连接
python3 -c "
from core.db_session import SessionFactory
try:
    with SessionFactory() as session:
        print('✓ 数据库连接正常')
except Exception as e:
    print(f'✗ 数据库连接失败: {e}')
"

# 2. 检查磁盘空间
df -h .

# 3. 检查文件权限
ls -la rules_cache.json.gz
whoami

# 4. 手动生成规则文件
python3 -c "
from master import generate_rules_cache_file
result = generate_rules_cache_file()
print(f'生成结果: {result}')
"
```

**解决方案**：
- 数据库问题：检查数据库服务状态，修复连接配置
- 磁盘空间：清理日志文件，扩展磁盘空间
- 权限问题：修改文件权限 `chmod 644 rules_cache.json.gz`

#### 1.2 启动缓慢或超时
**症状**：
- 主节点启动时间过长
- 规则加载或模板生成耗时

**排查步骤**：
```bash
# 检查数据库规则数量
python3 -c "
from core.db_session import SessionFactory
from models.database import BaseRule
with SessionFactory() as session:
    count = session.query(BaseRule).count()
    print(f'数据库规则总数: {count}')
"

# 检查系统资源
top -p $(pgrep -f master.py)
free -h
iostat 1 5
```

### 2. 子节点问题

#### 2.1 规则文件加载失败
**症状**：
- 子节点启动时无法加载规则文件
- 日志显示文件格式错误

**排查步骤**：
```bash
# 1. 检查文件存在性
if [ -f "rules_cache.json.gz" ]; then
    echo "✓ 规则文件存在"
    echo "文件大小: $(du -h rules_cache.json.gz)"
    echo "修改时间: $(stat -c %y rules_cache.json.gz)"
else
    echo "✗ 规则文件不存在"
fi

# 2. 检查文件格式
python3 -c "
import os
if os.path.exists('rules_cache.json.gz'):
    with open('rules_cache.json.gz', 'rb') as f:
        header = f.read(10)
    print(f'文件头: {header.hex()}')
    print(f'是否gzip: {header.startswith(b\"\\x1f\\x8b\")}')
    
    if header.startswith(b\"\\x1f\\x8b\"):
        print('✓ 文件格式为gzip')
    elif header.startswith(b'{'):
        print('! 文件格式为普通JSON（向后兼容）')
    else:
        print('✗ 未知文件格式')
"

# 3. 尝试手动加载
python3 -c "
import asyncio
from services.rule_loader import load_rules_from_file
result = asyncio.run(load_rules_from_file())
print(f'加载结果: {result}')
"
```

**解决方案**：
- 文件不存在：从主节点复制或重新同步
- 格式错误：删除文件，重启子节点自动同步
- 权限问题：修改文件权限

#### 2.2 同步失败
**症状**：
- 子节点无法从主节点同步规则
- 网络连接错误

**排查步骤**：
```bash
# 1. 检查网络连接
curl -f http://master-node:8000/health
telnet master-node 8000

# 2. 检查同步配置
grep -E "MASTER_NODE_URL|ENABLE_RULE_SYNC" .env.slave

# 3. 手动触发同步
curl -X POST http://localhost:8001/api/v1/sync/trigger
```

### 3. 性能问题

#### 3.1 内存使用过高
**排查步骤**：
```bash
# 检查进程内存使用
ps aux --sort=-%mem | head -10
pmap -x $(pgrep -f "master.py|slave.py")

# 检查规则缓存大小
python3 -c "
from core.rule_cache import RULE_CACHE
print(f'缓存规则数量: {len(RULE_CACHE)}')
import sys
print(f'缓存大小估算: {sys.getsizeof(RULE_CACHE)} bytes')
"
```

#### 3.2 响应时间过长
**排查步骤**：
```bash
# API响应时间测试
time curl -X POST http://localhost:8001/api/v1/validate \
  -H "Content-Type: application/json" \
  -d '{"patient_data": {"age": 30}}'

# 检查队列状态
curl http://localhost:8000/api/v1/queue/status
```

## 🔧 维护操作

### 1. 日常维护

#### 1.1 日志清理
```bash
# 清理应用日志（保留最近7天）
find logs/ -name "*.log" -mtime +7 -delete

# 清理系统日志
sudo journalctl --vacuum-time=7d
```

#### 1.2 规则文件维护
```bash
# 备份规则文件
cp rules_cache.json.gz rules_cache.json.gz.backup.$(date +%Y%m%d)

# 验证规则文件完整性
python3 tools/rule_file_manager.py validate rules_cache.json.gz

# 重新生成规则文件（如果需要）
python3 -c "from master import generate_rules_cache_file; generate_rules_cache_file()"
```

### 2. 升级操作

#### 2.1 主节点升级
```bash
# 1. 备份当前版本
cp -r /opt/ruleengine /opt/ruleengine.backup.$(date +%Y%m%d)

# 2. 停止服务
sudo systemctl stop ruleengine-master

# 3. 更新代码
git pull origin main
pip install -r requirements.txt

# 4. 数据库迁移（如果需要）
alembic upgrade head

# 5. 启动服务
sudo systemctl start ruleengine-master

# 6. 验证启动
sudo systemctl status ruleengine-master
curl -f http://localhost:8000/health
```

#### 2.2 子节点升级
```bash
# 1. 停止服务
sudo systemctl stop ruleengine-slave

# 2. 更新代码
git pull origin main
pip install -r requirements.txt

# 3. 启动服务
sudo systemctl start ruleengine-slave

# 4. 验证启动
sudo systemctl status ruleengine-slave
curl -f http://localhost:8001/health
```

### 3. 应急操作

#### 3.1 强制重新同步
```bash
# 子节点强制重新同步
rm -f rules_cache.json.gz rules_version.txt
sudo systemctl restart ruleengine-slave
```

#### 3.2 降级到离线模式
```bash
# 修改配置禁用同步
sed -i 's/ENABLE_RULE_SYNC=true/ENABLE_RULE_SYNC=false/' .env.slave
sudo systemctl restart ruleengine-slave
```

## 📊 监控指标

### 1. 关键指标
- 服务可用性：>99.9%
- API响应时间：<100ms (P95)
- 规则文件生成时间：<30s
- 规则加载时间：<10s
- 内存使用率：<80%
- CPU使用率：<70%

### 2. 告警规则
- 服务停止运行
- API响应时间>500ms
- 规则文件生成失败
- 规则同步失败超过3次
- 内存使用率>90%
- 磁盘使用率>85%

## 📝 检查清单

### 日常检查（每日）
- [ ] 检查服务运行状态
- [ ] 检查API健康状态
- [ ] 检查错误日志
- [ ] 检查磁盘空间

### 周期检查（每周）
- [ ] 检查规则文件完整性
- [ ] 清理旧日志文件
- [ ] 检查系统资源使用
- [ ] 验证备份完整性

### 月度检查（每月）
- [ ] 性能基准测试
- [ ] 安全更新检查
- [ ] 配置文件审查
- [ ] 灾难恢复演练
