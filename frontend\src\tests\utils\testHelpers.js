/**
 * 测试辅助工具函数
 * 提供通用的测试工具和Mock数据生成
 */

import { nextTick } from 'vue'
import { mount, shallowMount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import { vi } from 'vitest'
import { useAppStore } from '@/stores/app'
import { useRulesStore } from '@/stores/rules'

/**
 * 创建统一的测试环境配置
 * 解决测试环境中Pinia store初始化不完整的问题
 * @param {Object} options - 配置选项
 * @returns {Object} 测试环境对象
 */
export function createTestEnvironment(options = {}) {
  // 创建新的Pinia实例
  const pinia = createPinia()
  setActivePinia(pinia)

  // 初始化stores
  const stores = {
    app: useAppStore(),
    rules: useRulesStore()
  }

  // 清除所有Mock调用记录
  vi.clearAllMocks()

  return {
    pinia,
    stores,
    // 提供便捷的store访问方法
    getAppStore: () => stores.app,
    getRulesStore: () => stores.rules,
    // 重置所有store状态
    resetStores: () => {
      stores.app.resetStore()
      stores.rules.resetStore()
    },
    // 清除Mock记录
    clearMocks: () => vi.clearAllMocks()
  }
}

/**
 * 设置测试用的stores
 * 提供标准化的store初始化流程
 * @param {Object} options - 配置选项
 * @returns {Object} stores对象
 */
export function setupTestStores(options = {}) {
  const env = createTestEnvironment(options)
  return env.stores
}

/**
 * 创建测试用的Vue应用配置
 * 集成了统一的测试环境配置
 * @param {Object} options - 配置选项
 * @returns {Object} 测试配置
 */
export function createTestApp(options = {}) {
  // 使用统一的测试环境配置
  const testEnv = createTestEnvironment(options.testEnv || {})
  const pinia = testEnv.pinia

  // 创建测试路由
  const router = createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', component: { template: '<div>Home</div>' } },
      { path: '/rules', component: { template: '<div>Rules</div>' } },
      { path: '/rules/:ruleKey/details', component: { template: '<div>Details</div>' } }
    ]
  })

  return {
    global: {
      plugins: [pinia, router, ElementPlus],
      stubs: {
        'router-link': true,
        'router-view': true,
        'el-icon': true,
        ...options.stubs
      },
      mocks: {
        $t: (key) => key,
        $route: { path: '/', params: {}, query: {} },
        $router: { push: vi.fn(), replace: vi.fn() },
        ...options.mocks
      },
      ...options.global
    },
    // 暴露测试环境对象，便于测试中访问stores
    testEnv,
    ...options
  }
}

/**
 * 挂载Vue组件的便捷方法
 * @param {Object} component - Vue组件
 * @param {Object} options - 挂载选项
 * @returns {Object} 组件包装器
 */
export function mountComponent(component, options = {}) {
  const config = createTestApp(options)
  return mount(component, config)
}

/**
 * 浅挂载Vue组件的便捷方法
 * @param {Object} component - Vue组件
 * @param {Object} options - 挂载选项
 * @returns {Object} 组件包装器
 */
export function shallowMountComponent(component, options = {}) {
  const config = createTestApp(options)
  return shallowMount(component, config)
}

/**
 * 等待异步操作完成
 * @param {number} ms - 等待时间（毫秒）
 */
export async function waitFor(ms = 0) {
  await new Promise(resolve => setTimeout(resolve, ms))
  await nextTick()
}

/**
 * 等待DOM更新
 */
export async function waitForDOMUpdate() {
  await nextTick()
  await new Promise(resolve => setTimeout(resolve, 0))
}

/**
 * 模拟用户输入
 * @param {Object} wrapper - 组件包装器
 * @param {string} selector - 选择器
 * @param {string} value - 输入值
 */
export async function setInputValue(wrapper, selector, value) {
  const input = wrapper.find(selector)
  await input.setValue(value)
  await input.trigger('input')
  await waitForDOMUpdate()
}

/**
 * 模拟用户点击
 * @param {Object} wrapper - 组件包装器
 * @param {string} selector - 选择器
 */
export async function clickElement(wrapper, selector) {
  const element = wrapper.find(selector)
  await element.trigger('click')
  await waitForDOMUpdate()
}

/**
 * 生成Mock规则数据
 * @param {Object} overrides - 覆盖属性
 * @returns {Object} Mock规则数据
 */
export function createMockRule(overrides = {}) {
  return {
    rule_key: 'test-rule-001',
    rule_name: '测试规则',
    description: '这是一个测试规则',
    status: 'ACTIVE',
    created_at: '2025-01-21T10:00:00Z',
    updated_at: '2025-01-21T10:00:00Z',
    rule_count: 100,
    active_count: 80,
    inactive_count: 20,
    ...overrides
  }
}

/**
 * 生成Mock规则明细数据
 * @param {Object} overrides - 覆盖属性
 * @returns {Object} Mock规则明细数据
 */
export function createMockRuleDetail(overrides = {}) {
  return {
    id: 1,
    rule_detail_id: 'detail-001',
    rule_name: '测试规则明细',
    rule_id: 'R001',
    status: 'ACTIVE',
    error_reason: '',
    created_at: '2025-01-21T10:00:00Z',
    updated_at: '2025-01-21T10:00:00Z',
    ...overrides
  }
}

/**
 * 生成Mock API响应
 * @param {any} data - 响应数据
 * @param {boolean} success - 是否成功
 * @param {string} message - 响应消息
 * @returns {Object} Mock API响应
 */
export function createMockApiResponse(data = null, success = true, message = '') {
  return {
    success,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * 生成分页数据
 * @param {Array} items - 数据项
 * @param {number} page - 当前页
 * @param {number} pageSize - 页大小
 * @param {number} total - 总数
 * @returns {Object} 分页数据
 */
export function createMockPaginatedData(items = [], page = 1, pageSize = 10, total = null) {
  return {
    items,
    page,
    page_size: pageSize,
    total: total !== null ? total : items.length,
    total_pages: Math.ceil((total !== null ? total : items.length) / pageSize)
  }
}

/**
 * 创建大量测试数据
 * @param {number} count - 数据数量
 * @param {Function} factory - 数据工厂函数
 * @returns {Array} 测试数据数组
 */
export function createMockDataList(count, factory) {
  return Array.from({ length: count }, (_, index) => factory(index))
}

/**
 * Mock Element Plus组件
 */
export const mockElementPlusComponents = {
  'el-button': { template: '<button><slot /></button>' },
  'el-input': { template: '<input />' },
  'el-table': { template: '<div class="el-table"><slot /></div>' },
  'el-table-column': { template: '<div class="el-table-column"><slot /></div>' },
  'el-pagination': { template: '<div class="el-pagination"></div>' },
  'el-dialog': { template: '<div class="el-dialog"><slot /></div>' },
  'el-form': { template: '<form><slot /></form>' },
  'el-form-item': { template: '<div class="el-form-item"><slot /></div>' },
  'el-card': { template: '<div class="el-card"><slot /></div>' },
  'el-tag': { template: '<span class="el-tag"><slot /></span>' },
  'el-loading': { template: '<div class="el-loading"><slot /></div>' }
}

/**
 * 性能测试辅助函数
 */
export class PerformanceHelper {
  constructor() {
    this.marks = new Map()
    this.measures = new Map()
  }

  /**
   * 开始性能标记
   * @param {string} name - 标记名称
   */
  mark(name) {
    this.marks.set(name, performance.now())
  }

  /**
   * 结束性能标记并计算耗时
   * @param {string} startMark - 开始标记名称
   * @param {string} endMark - 结束标记名称
   * @returns {number} 耗时（毫秒）
   */
  measure(startMark, endMark = null) {
    const startTime = this.marks.get(startMark)
    const endTime = endMark ? this.marks.get(endMark) : performance.now()

    if (startTime === undefined) {
      throw new Error(`Start mark "${startMark}" not found`)
    }

    const duration = endTime - startTime
    this.measures.set(`${startMark}-${endMark || 'end'}`, duration)
    return duration
  }

  /**
   * 获取所有测量结果
   * @returns {Object} 测量结果
   */
  getResults() {
    return Object.fromEntries(this.measures)
  }

  /**
   * 清除所有标记和测量
   */
  clear() {
    this.marks.clear()
    this.measures.clear()
  }
}

/**
 * Store测试辅助函数
 * 提供专门用于测试Pinia stores的工具方法
 */
export class StoreTestHelper {
  constructor() {
    this.testEnv = null
    this.originalConsoleWarn = console.warn
    this.originalConsoleError = console.error
  }

  /**
   * 初始化测试环境
   * @param {Object} options - 配置选项
   */
  setup(options = {}) {
    this.testEnv = createTestEnvironment(options)

    // 可选：静默控制台警告（用于测试预期的错误情况）
    if (options.silentWarnings) {
      console.warn = vi.fn()
    }
    if (options.silentErrors) {
      console.error = vi.fn()
    }

    return this.testEnv
  }

  /**
   * 清理测试环境
   */
  teardown() {
    if (this.testEnv) {
      this.testEnv.resetStores()
      this.testEnv.clearMocks()
    }

    // 恢复控制台方法
    console.warn = this.originalConsoleWarn
    console.error = this.originalConsoleError

    this.testEnv = null
  }

  /**
   * 获取App Store的Mock配置
   * 基于实际store结构创建完整的Mock
   * @param {Object} overrides - 覆盖配置
   * @returns {Object} Mock配置
   */
  createAppStoreMock(overrides = {}) {
    return {
      // 基础状态
      globalLoading: false,
      loadingMessage: '加载中...',
      loadingProgress: 0,
      currentError: null,
      errorHistory: [],
      errorCount: 0,
      notifications: [],
      toasts: [],
      isAdmin: false,

      // 计算属性
      hasLoadingTasks: false,
      hasError: false,
      notificationCount: 0,
      toastCount: 0,
      isLoggedIn: false,

      // 方法
      addLoadingTask: vi.fn(),
      removeLoadingTask: vi.fn(),
      updateLoadingProgress: vi.fn(),
      clearLoadingTasks: vi.fn(),
      setError: vi.fn(),
      clearError: vi.fn(),
      resolveError: vi.fn(),
      clearErrorHistory: vi.fn(),
      addNotification: vi.fn(),
      removeNotification: vi.fn(),
      addToast: vi.fn(),
      removeToast: vi.fn(),
      clearNotifications: vi.fn(),
      clearToasts: vi.fn(),
      setTheme: vi.fn(),
      setLocale: vi.fn(),
      toggleDebugMode: vi.fn(),
      setUserRoles: vi.fn(),
      setUserPermissions: vi.fn(),
      hasRole: vi.fn(() => false),
      hasPermission: vi.fn(() => false),
      login: vi.fn(),
      logout: vi.fn(),
      resetStore: vi.fn(),

      // 应用覆盖配置
      ...overrides
    }
  }

  /**
   * 获取Rules Store的Mock配置
   * 基于实际store结构创建完整的Mock
   * @param {Object} overrides - 覆盖配置
   * @returns {Object} Mock配置
   */
  createRulesStoreMock(overrides = {}) {
    return {
      // 基础状态
      rules: [],
      currentRule: null,
      ruleSchema: [],
      ruleStatistics: {},
      loading: false,
      detailLoading: false,
      lastFetchTime: null,
      currentRuleDetails: [],
      detailsCount: 0,
      detailsLoading: false,

      // 计算属性
      rulesCount: 0,
      hasRules: false,
      isLoading: false,
      rulesByStatus: {},
      statusCounts: {},
      availableStatuses: [],
      rulesSummary: { total: 0, ready: 0, new: 0, changed: 0, deprecated: 0 },
      hasCurrentRule: false,
      hasRuleDetails: false,
      ruleDetailsCount: 0,
      isDetailsLoading: false,

      // 方法
      fetchRules: vi.fn(() => Promise.resolve([])),
      fetchRuleDetail: vi.fn(() => Promise.resolve(null)),
      downloadTemplate: vi.fn(() => Promise.resolve(false)),
      submitRuleData: vi.fn(() => Promise.resolve({ success: true })),
      fetchRuleDetailsList: vi.fn(() => Promise.resolve([])),
      getRuleDetailsCount: vi.fn(() => Promise.resolve(0)),
      clearRuleDetailsCache: vi.fn(),
      refreshRuleDetails: vi.fn(() => Promise.resolve([])),
      searchRules: vi.fn(() => []),
      filterRulesByStatus: vi.fn(() => []),
      getRuleByKey: vi.fn(() => null),
      clearCurrentRule: vi.fn(),
      refreshRule: vi.fn(() => Promise.resolve(null)),
      clearCache: vi.fn(),
      clearRuleCache: vi.fn(),
      getCacheStats: vi.fn(() => ({})),
      resetStore: vi.fn(),

      // 应用覆盖配置
      ...overrides
    }
  }

  /**
   * 验证store方法调用
   * @param {Object} store - store实例
   * @param {string} methodName - 方法名
   * @param {Array} expectedArgs - 期望的参数
   * @param {number} callCount - 期望的调用次数
   */
  expectStoreMethodCalled(store, methodName, expectedArgs = [], callCount = 1) {
    const method = store[methodName]
    expect(method).toHaveBeenCalledTimes(callCount)
    if (expectedArgs.length > 0) {
      expect(method).toHaveBeenCalledWith(...expectedArgs)
    }
  }

  /**
   * 验证store状态
   * @param {Object} store - store实例
   * @param {Object} expectedState - 期望的状态
   */
  expectStoreState(store, expectedState) {
    Object.keys(expectedState).forEach(key => {
      expect(store[key]).toEqual(expectedState[key])
    })
  }
}

/**
 * 内存使用监控
 */
export class MemoryMonitor {
  constructor() {
    this.snapshots = []
  }

  /**
   * 拍摄内存快照
   * @param {string} label - 快照标签
   */
  snapshot(label) {
    if (performance.memory) {
      this.snapshots.push({
        label,
        timestamp: Date.now(),
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      })
    }
  }

  /**
   * 获取内存使用情况
   * @returns {Array} 内存快照列表
   */
  getSnapshots() {
    return this.snapshots
  }

  /**
   * 计算内存增长
   * @param {string} startLabel - 开始标签
   * @param {string} endLabel - 结束标签
   * @returns {Object} 内存增长信息
   */
  getMemoryGrowth(startLabel, endLabel) {
    const start = this.snapshots.find(s => s.label === startLabel)
    const end = this.snapshots.find(s => s.label === endLabel)

    if (!start || !end) {
      return null
    }

    return {
      growth: end.used - start.used,
      growthPercent: ((end.used - start.used) / start.used) * 100,
      start: start.used,
      end: end.used
    }
  }

  /**
   * 清除快照
   */
  clear() {
    this.snapshots = []
  }
}
