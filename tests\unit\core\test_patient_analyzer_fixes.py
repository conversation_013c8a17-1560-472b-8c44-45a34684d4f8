"""
患者数据分析器修复验证测试
专门验证任务3.1中的修复是否正确
"""

import unittest
import sys
import os
from unittest.mock import Mock, MagicMock
from dataclasses import dataclass
from typing import Set

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))


@dataclass
class PatientCodeExtraction:
    """患者代码提取结果（模拟）"""
    yb_codes: Set[str]
    diag_codes: Set[str]
    surgery_codes: Set[str]
    extraction_time: float


class MockDiagnosisList:
    """模拟诊断列表项"""
    def __init__(self, diagnosisICDCode: str, diagnosisName: str = ""):
        self.diagnosisICDCode = diagnosisICDCode
        self.diagnosisName = diagnosisName


class MockOperationList:
    """模拟手术操作列表项"""
    def __init__(self, operationICDCode: str, operationName: str = ""):
        self.operationICDCode = operationICDCode
        self.operationName = operationName


class MockDiagnoseInfo:
    """模拟诊断信息"""
    def __init__(self, outPatientDiagnosisICDCode: str = None,
                 outPatientDiagnosisChICDCode: str = None,
                 diagnoseICDCodeOnAdmission: str = None,
                 diagnosis: list = None,
                 operation: list = None):
        self.outPatientDiagnosisICDCode = outPatientDiagnosisICDCode
        self.outPatientDiagnosisChICDCode = outPatientDiagnosisChICDCode
        self.diagnoseICDCodeOnAdmission = diagnoseICDCodeOnAdmission
        self.diagnosis = diagnosis or []
        self.operation = operation or []


class MockFeeItem:
    """模拟费用项目"""
    def __init__(self, id: str, ybdm: str = None, fymc: str = "", type: str = ""):
        self.id = id
        self.ybdm = ybdm
        self.fymc = fymc
        self.type = type


class MockPatientData:
    """模拟患者数据"""
    def __init__(self, bah: str, fees: list = None, Diagnosis: MockDiagnoseInfo = None):
        self.bah = bah
        self.fees = fees or []
        self.Diagnosis = Diagnosis


class TestPatientAnalyzerFixes(unittest.TestCase):
    """患者数据分析器修复验证测试"""

    def setUp(self):
        """测试前准备"""
        # 模拟PatientDataAnalyzer的核心逻辑
        self.analyzer = self._create_mock_analyzer()

    def _create_mock_analyzer(self):
        """创建模拟的分析器，包含修复后的逻辑"""
        analyzer = Mock()
        analyzer.extract_codes = self._mock_extract_codes
        return analyzer

    def _mock_extract_codes(self, patient_data: MockPatientData) -> PatientCodeExtraction:
        """模拟修复后的代码提取逻辑"""
        yb_codes = set()
        diag_codes = set()
        surgery_codes = set()

        # 提取医保代码（从fees）
        if hasattr(patient_data, 'fees') and patient_data.fees:
            for fee_item in patient_data.fees:
                if hasattr(fee_item, 'ybdm') and fee_item.ybdm:
                    yb_codes.add(fee_item.ybdm.strip())

        # 提取诊断代码（修复后的逻辑）
        if hasattr(patient_data, 'Diagnosis') and patient_data.Diagnosis:
            diagnosis_info = patient_data.Diagnosis

            # 门急诊西医疾病代码
            if hasattr(diagnosis_info, 'outPatientDiagnosisICDCode') and diagnosis_info.outPatientDiagnosisICDCode:
                diag_codes.add(diagnosis_info.outPatientDiagnosisICDCode.strip())

            # 门急诊中医疾病代码
            if hasattr(diagnosis_info, 'outPatientDiagnosisChICDCode') and diagnosis_info.outPatientDiagnosisChICDCode:
                diag_codes.add(diagnosis_info.outPatientDiagnosisChICDCode.strip())

            # 入院诊断编码
            if hasattr(diagnosis_info, 'diagnoseICDCodeOnAdmission') and diagnosis_info.diagnoseICDCodeOnAdmission:
                diag_codes.add(diagnosis_info.diagnoseICDCodeOnAdmission.strip())

            # 出院/门诊诊断列表
            if hasattr(diagnosis_info, 'diagnosis') and diagnosis_info.diagnosis:
                for diag_item in diagnosis_info.diagnosis:
                    if hasattr(diag_item, 'diagnosisICDCode') and diag_item.diagnosisICDCode:
                        diag_codes.add(diag_item.diagnosisICDCode.strip())

            # 手术及操作代码（修复后的逻辑）
            if hasattr(diagnosis_info, 'operation') and diagnosis_info.operation:
                for op_item in diagnosis_info.operation:
                    if hasattr(op_item, 'operationICDCode') and op_item.operationICDCode:
                        surgery_codes.add(op_item.operationICDCode.strip())
                        # 手术代码也添加到诊断代码中
                        diag_codes.add(op_item.operationICDCode.strip())

        return PatientCodeExtraction(
            yb_codes=yb_codes,
            diag_codes=diag_codes,
            surgery_codes=surgery_codes,
            extraction_time=0.5
        )

    def test_diagnosis_info_object_handling_fix(self):
        """测试诊断信息对象处理修复"""
        # 创建包含DiagnoseInfo对象的患者数据
        diagnosis_info = MockDiagnoseInfo(
            outPatientDiagnosisICDCode="I10",
            outPatientDiagnosisChICDCode="Z001",
            diagnoseICDCodeOnAdmission="I15"
        )

        patient_data = MockPatientData(
            bah="TEST001",
            Diagnosis=diagnosis_info
        )

        # 执行代码提取
        result = self.analyzer.extract_codes(patient_data)

        # 验证修复：应该正确提取所有诊断代码
        expected_diag_codes = {"I10", "Z001", "I15"}
        self.assertEqual(result.diag_codes, expected_diag_codes)
        print(f"✅ 诊断信息对象处理修复验证通过: {result.diag_codes}")

    def test_diagnosis_list_extraction_fix(self):
        """测试诊断列表提取修复"""
        # 创建包含诊断列表的患者数据
        diagnosis_list = [
            MockDiagnosisList("E11.9", "糖尿病"),
            MockDiagnosisList("I25.9", "冠心病"),
        ]

        diagnosis_info = MockDiagnoseInfo(
            outPatientDiagnosisICDCode="I10",
            diagnosis=diagnosis_list
        )

        patient_data = MockPatientData(
            bah="TEST002",
            Diagnosis=diagnosis_info
        )

        # 执行代码提取
        result = self.analyzer.extract_codes(patient_data)

        # 验证修复：应该提取门急诊诊断和诊断列表中的所有代码
        expected_diag_codes = {"I10", "E11.9", "I25.9"}
        self.assertEqual(result.diag_codes, expected_diag_codes)
        print(f"✅ 诊断列表提取修复验证通过: {result.diag_codes}")

    def test_surgery_code_extraction_enhancement(self):
        """测试手术代码提取增强"""
        # 创建包含手术操作的患者数据
        operation_list = [
            MockOperationList("S001", "心脏手术"),
            MockOperationList("S002", "血管手术"),
        ]

        diagnosis_info = MockDiagnoseInfo(
            operation=operation_list
        )

        patient_data = MockPatientData(
            bah="TEST003",
            Diagnosis=diagnosis_info
        )

        # 执行代码提取
        result = self.analyzer.extract_codes(patient_data)

        # 验证增强：应该从operation字段提取手术代码
        expected_surgery_codes = {"S001", "S002"}
        self.assertEqual(result.surgery_codes, expected_surgery_codes)

        # 验证手术代码也添加到诊断代码中
        self.assertTrue(expected_surgery_codes.issubset(result.diag_codes))
        print(f"✅ 手术代码提取增强验证通过: {result.surgery_codes}")

    def test_comprehensive_extraction_fix(self):
        """测试综合代码提取修复"""
        # 创建包含所有类型代码的复杂患者数据
        fees = [
            MockFeeItem("1", "Y001", "药品A"),
            MockFeeItem("2", "Y002", "检查B"),
        ]

        diagnosis_list = [
            MockDiagnosisList("E11.9", "糖尿病"),
        ]

        operation_list = [
            MockOperationList("S001", "心脏手术"),
        ]

        diagnosis_info = MockDiagnoseInfo(
            outPatientDiagnosisICDCode="I10",
            outPatientDiagnosisChICDCode="Z001",
            diagnoseICDCodeOnAdmission="I15",
            diagnosis=diagnosis_list,
            operation=operation_list
        )

        patient_data = MockPatientData(
            bah="COMPREHENSIVE_TEST",
            fees=fees,
            Diagnosis=diagnosis_info
        )

        # 执行代码提取
        result = self.analyzer.extract_codes(patient_data)

        # 验证医保代码
        expected_yb_codes = {"Y001", "Y002"}
        self.assertEqual(result.yb_codes, expected_yb_codes)

        # 验证诊断代码（包括手术代码）
        expected_diag_codes = {"I10", "Z001", "I15", "E11.9", "S001"}
        self.assertEqual(result.diag_codes, expected_diag_codes)

        # 验证手术代码
        expected_surgery_codes = {"S001"}
        self.assertEqual(result.surgery_codes, expected_surgery_codes)

        print(f"✅ 综合代码提取修复验证通过:")
        print(f"   医保代码: {result.yb_codes}")
        print(f"   诊断代码: {result.diag_codes}")
        print(f"   手术代码: {result.surgery_codes}")

    def test_empty_data_handling(self):
        """测试空数据处理"""
        # 测试空的DiagnoseInfo
        patient_data = MockPatientData(
            bah="EMPTY_TEST",
            Diagnosis=MockDiagnoseInfo()
        )

        result = self.analyzer.extract_codes(patient_data)

        # 应该返回空集合，不报错
        self.assertEqual(len(result.yb_codes), 0)
        self.assertEqual(len(result.diag_codes), 0)
        self.assertEqual(len(result.surgery_codes), 0)
        print("✅ 空数据处理验证通过")

    def test_none_diagnosis_handling(self):
        """测试None诊断处理"""
        patient_data = MockPatientData(
            bah="NONE_TEST",
            Diagnosis=None
        )

        result = self.analyzer.extract_codes(patient_data)

        # 应该返回空集合，不报错
        self.assertEqual(len(result.yb_codes), 0)
        self.assertEqual(len(result.diag_codes), 0)
        self.assertEqual(len(result.surgery_codes), 0)
        print("✅ None诊断处理验证通过")


def run_fix_verification():
    """运行修复验证"""
    print("=== 患者数据分析器修复验证 ===\n")

    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPatientAnalyzerFixes)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # 输出结果
    print(f"\n=== 验证结果 ===")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")

    if result.wasSuccessful():
        print("🎉 所有修复验证通过！")
        return True
    else:
        print("❌ 部分修复验证失败")
        for failure in result.failures:
            print(f"失败: {failure[0]} - {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]} - {error[1]}")
        return False


if __name__ == "__main__":
    success = run_fix_verification()
    sys.exit(0 if success else 1)
