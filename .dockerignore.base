# ===== 基础镜像构建忽略文件 =====
# 基础镜像只需要requirements.txt，其他所有文件都应忽略

# ===== 保留文件 =====
# 只保留依赖文件
!requirements.txt
!requirements.in

# ===== 忽略所有其他文件 =====
# 项目代码（基础镜像不需要）
*.py
api/
core/
models/
services/
rules/
utils/
config/
tests/
tools/

# 应用入口文件
main.py
master.py
slave.py
__init__.py

# 数据库相关
alembic/
alembic.ini
*.db
*.sqlite
*.sqlite3

# 日志和缓存
logs/
rules_cache/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# 开发工具
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 版本控制
.git/
.gitignore
.gitattributes

# 环境文件
.env
.env.*
!.env.example

# 文档
documentation/
demo/
README.md
CHANGELOG.md
LICENSE
*.md

# 测试文件
validate_test.py
generate.py
test_*.db
*_test.py
*_test.db

# 生成文件
generated_templates/
*.xlsx
*.xls
333.json
_data/
tmp/
temp/
.tmp/

# 前端文件（基础镜像不需要）
frontend/

# Docker文件
Dockerfile*
docker-compose*.yml
.dockerignore*

# 配置文件（开发用）
pytest.ini
ruff.toml
.editorconfig
.prettierrc
.prettierignore
.eslintrc.*
jest.config.js
vitest.config.js

# 依赖管理
package-lock.json
yarn.lock
pnpm-lock.yaml

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# 内存银行和其他工具
memory-bank/
control.sh

# Python虚拟环境
venv/
env/
.venv/
.env/
