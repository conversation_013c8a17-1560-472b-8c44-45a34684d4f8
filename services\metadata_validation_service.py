"""
元数据驱动校验服务
基于RuleFieldMetadata的统一校验引擎
支持前后端校验规则同步和错误信息标准化
重构版本：集成ValidationRuleEngine和BatchValidationProcessor
"""

import time
from dataclasses import dataclass
from typing import Any

from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import RuleFieldMetadata, RuleTemplate
from services.batch_validation_processor import BatchValidationProcessor, BatchValidationReport
from services.rule_detail_service import ServiceError, ValidationResult
from services.unified_data_mapping_engine import UnifiedDataMappingEngine
from services.validation_rule_engine import ValidationRuleEngine
from tools.field_mapping_manager import FieldMappingManager


@dataclass
class ValidationRule:
    """验证规则定义（保持向后兼容）"""

    field_name: str
    chinese_name: str
    rule_type: str  # required, max_length, min_value, max_value, pattern, etc.
    rule_value: Any
    error_message: str
    warning_message: str | None = None


@dataclass
class BatchValidationResult:
    """批量验证结果（保持向后兼容）"""

    valid: bool
    total_rows: int
    valid_rows: int
    error_rows: int
    errors: list[str]
    warnings: list[str]
    row_results: list[ValidationResult]

    @classmethod
    def from_batch_report(cls, report: BatchValidationReport) -> "BatchValidationResult":
        """从BatchValidationReport转换"""
        return cls(
            valid=report.success_rate == 1.0,
            total_rows=report.total_items,
            valid_rows=report.valid_items,
            error_rows=report.invalid_items,
            errors=[
                f"行{error.index}: {', '.join([e.get('error_message', '') for e in error.errors])}"
                for error in report.errors
            ],
            warnings=report.warnings,
            row_results=[],  # 为了向后兼容，暂时保留空列表
        )


class MetadataValidationService:
    """元数据驱动校验服务（重构版本）"""

    def __init__(self, session: Session):
        """
        初始化服务

        Args:
            session: 数据库会话
        """
        self.session = session
        self.field_mapping_manager = FieldMappingManager()
        self.data_mapping_engine = UnifiedDataMappingEngine()
        self.validation_engine = ValidationRuleEngine(session)
        self.batch_processor = BatchValidationProcessor(session)
        self._validation_cache = {}  # 验证规则缓存（保持向后兼容）

    def validate_rule_detail_data(self, rule_key: str, data: dict[str, Any]) -> ValidationResult:
        """
        验证规则明细数据（重构版本）

        Args:
            rule_key: 规则模板键
            data: 待验证的数据

        Returns:
            ValidationResult: 验证结果

        Raises:
            ServiceError: 当验证失败时
        """
        try:
            # 使用新的ValidationRuleEngine进行校验
            result = self.validation_engine.validate_data(rule_key, data)

            # 转换为旧的ValidationResult格式（保持向后兼容）
            if hasattr(result, "to_dict"):
                # 新格式，直接返回
                return result
            else:
                # 如果是旧格式，进行转换
                return ValidationResult(
                    valid=result.valid,
                    errors=result.errors,
                    warnings=result.warnings,
                    field_count=result.field_count,
                    validated_fields=result.validated_fields,
                    duration=result.duration,
                    metadata=result.metadata,
                )

        except Exception as e:
            error_msg = f"验证规则明细数据失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="RULE_DETAIL_VALIDATION_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def validate_batch_data(self, rule_key: str, data_list: list[dict[str, Any]]) -> BatchValidationResult:
        """
        批量验证数据（重构版本）

        Args:
            rule_key: 规则模板键
            data_list: 待验证的数据列表

        Returns:
            BatchValidationResult: 批量验证结果
        """
        try:
            logger.info(f"开始批量验证: rule_key={rule_key}, items={len(data_list)}")

            # 使用新的BatchValidationProcessor进行批量校验
            report = self.batch_processor.validate_batch(
                rule_key=rule_key,
                data_list=data_list,
                parallel=True,  # 启用并行处理
                include_valid_data=False,  # 不包含有效数据以节省内存
            )

            # 转换为旧的BatchValidationResult格式（保持向后兼容）
            result = BatchValidationResult.from_batch_report(report)

            logger.info(
                f"批量验证完成: rule_key={rule_key}, total={result.total_rows}, "
                f"valid={result.valid_rows}, errors={result.error_rows}, "
                f"success_rate={report.success_rate:.2%}"
            )

            return result

        except Exception as e:
            error_msg = f"批量验证失败: rule_key={rule_key}, items={len(data_list)}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg,
                error_code="BATCH_VALIDATION_FAILED",
                details={"rule_key": rule_key, "items_count": len(data_list), "error": str(e)},
            ) from None

    def get_frontend_validation_rules(self, rule_key: str) -> dict[str, Any]:
        """
        获取前端校验规则

        Args:
            rule_key: 规则模板键

        Returns:
            Dict: 前端校验规则配置
        """
        try:
            # 使用新的ValidationRuleEngine获取规则
            rules = self.validation_engine.get_validation_rules(rule_key)

            frontend_rules = {}
            for rule in rules:
                field_name = rule.field_name

                if field_name not in frontend_rules:
                    frontend_rules[field_name] = {
                        "chinese_name": rule.chinese_name,
                        "rules": [],
                        "required": rule.is_required,
                    }

                # 转换规则格式
                frontend_rule = {"type": rule.rule_type.value, "message": rule.error_message}

                if rule.rule_value is not None:
                    frontend_rule["value"] = rule.rule_value

                frontend_rules[field_name]["rules"].append(frontend_rule)

            return {"rule_key": rule_key, "fields": frontend_rules, "generated_at": time.strftime("%Y-%m-%d %H:%M:%S")}

        except Exception as e:
            error_msg = f"获取前端校验规则失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg,
                error_code="FRONTEND_RULES_GENERATION_FAILED",
                details={"rule_key": rule_key, "error": str(e)},
            ) from None

    def clear_validation_cache(self, rule_key: str = None):
        """
        清除校验缓存

        Args:
            rule_key: 规则模板键，为空时清除所有缓存
        """
        if rule_key:
            self._validation_cache.pop(rule_key, None)
            self.validation_engine.clear_cache(rule_key)
        else:
            self._validation_cache.clear()
            self.validation_engine.clear_cache()

    def get_validation_summary(self, rule_key: str) -> dict[str, Any]:
        """
        获取校验规则摘要

        Args:
            rule_key: 规则模板键

        Returns:
            Dict: 校验规则摘要
        """
        return self.batch_processor.get_validation_summary(rule_key)

    def get_validation_rules(self, rule_key: str) -> list[ValidationRule]:
        """
        获取验证规则

        Args:
            rule_key: 规则模板键

        Returns:
            List[ValidationRule]: 验证规则列表
        """
        # 检查缓存
        if rule_key in self._validation_cache:
            return self._validation_cache[rule_key]

        try:
            # 获取规则模板
            template = self._get_rule_template(rule_key)
            if not template:
                raise ServiceError(
                    f"规则模板 '{rule_key}' 不存在", error_code="TEMPLATE_NOT_FOUND", details={"rule_key": rule_key}
                )

            # 获取字段元数据
            field_metadata_list = template.get_field_metadata_list()

            # 构建验证规则
            validation_rules = []
            for metadata in field_metadata_list:
                rules = self._build_validation_rules_from_metadata(metadata)
                validation_rules.extend(rules)

            # 缓存结果
            self._validation_cache[rule_key] = validation_rules

            logger.debug(f"获取验证规则: rule_key={rule_key}, rules={len(validation_rules)}")
            return validation_rules

        except ServiceError:
            raise
        except Exception as e:
            error_msg = f"获取验证规则失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="GET_VALIDATION_RULES_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def generate_frontend_validation_config(self, rule_key: str) -> dict[str, Any]:
        """
        生成前端验证配置

        Args:
            rule_key: 规则模板键

        Returns:
            Dict: 前端验证配置
        """
        try:
            validation_rules = self.get_validation_rules(rule_key)

            # 按字段分组
            field_rules = {}
            for rule in validation_rules:
                if rule.field_name not in field_rules:
                    field_rules[rule.field_name] = {"chinese_name": rule.chinese_name, "rules": []}

                # 转换为前端格式
                frontend_rule = {"type": rule.rule_type, "value": rule.rule_value, "message": rule.error_message}

                if rule.warning_message:
                    frontend_rule["warning"] = rule.warning_message

                field_rules[rule.field_name]["rules"].append(frontend_rule)

            config = {"rule_key": rule_key, "fields": field_rules, "generated_at": time.perf_counter()}

            logger.debug(f"生成前端验证配置: rule_key={rule_key}, fields={len(field_rules)}")
            return config

        except Exception as e:
            error_msg = f"生成前端验证配置失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="GENERATE_FRONTEND_CONFIG_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def _get_rule_template(self, rule_key: str) -> RuleTemplate | None:
        """获取规则模板"""
        return self.session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()

    def _build_validation_rules_from_metadata(self, metadata: RuleFieldMetadata) -> list[ValidationRule]:
        """从元数据构建验证规则"""
        rules = []
        chinese_name = metadata.display_name

        # 必填验证
        if metadata.is_required:
            rules.append(
                ValidationRule(
                    field_name=metadata.field_name,
                    chinese_name=chinese_name,
                    rule_type="required",
                    rule_value=True,
                    error_message=f"必填字段 '{chinese_name}' 不能为空",
                )
            )

        # 解析验证规则
        validation_rules = metadata.get_validation_rules()
        for rule_str in validation_rules:
            rule = self._parse_validation_rule_string(rule_str, metadata.field_name, chinese_name)
            if rule:
                rules.append(rule)

        return rules

    def _parse_validation_rule_string(self, rule_str: str, field_name: str, chinese_name: str) -> ValidationRule | None:
        """解析验证规则字符串"""
        if ":" in rule_str:
            rule_type, rule_value = rule_str.split(":", 1)

            if rule_type == "max_length":
                return ValidationRule(
                    field_name=field_name,
                    chinese_name=chinese_name,
                    rule_type="max_length",
                    rule_value=int(rule_value),
                    error_message=f"字段 '{chinese_name}' 长度不能超过{rule_value}个字符",
                )
            elif rule_type == "min_value":
                return ValidationRule(
                    field_name=field_name,
                    chinese_name=chinese_name,
                    rule_type="min_value",
                    rule_value=int(rule_value),
                    error_message=f"字段 '{chinese_name}' 值不能小于{rule_value}",
                )
            elif rule_type == "max_value":
                return ValidationRule(
                    field_name=field_name,
                    chinese_name=chinese_name,
                    rule_type="max_value",
                    rule_value=int(rule_value),
                    error_message=f"字段 '{chinese_name}' 值不能大于{rule_value}",
                )

        return None

    def _validate_field_by_rule(self, field_value: Any, rule: ValidationRule) -> str | None:
        """根据规则验证字段值"""
        if rule.rule_type == "required":
            if field_value is None or field_value == "":
                return rule.error_message

        elif rule.rule_type == "max_length" and field_value is not None:
            if len(str(field_value)) > rule.rule_value:
                return rule.error_message

        elif rule.rule_type == "min_value" and field_value is not None:
            try:
                if float(field_value) < rule.rule_value:
                    return rule.error_message
            except (ValueError, TypeError):
                return f"字段 '{rule.chinese_name}' 必须为数字"

        elif rule.rule_type == "max_value" and field_value is not None:
            try:
                if float(field_value) > rule.rule_value:
                    return rule.error_message
            except (ValueError, TypeError):
                return f"字段 '{rule.chinese_name}' 必须为数字"

        return None

    def clear_cache(self):
        """清空验证规则缓存"""
        self._validation_cache.clear()
        logger.info("验证规则缓存已清空")
