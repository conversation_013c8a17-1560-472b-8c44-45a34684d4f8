"""
Service dependencies for API endpoints.
"""

from services.rule_service import RuleService

# Global service instances (will be set during application startup)
_rule_service: RuleService | None = None


def set_rule_service(service: RuleService):
    """
    Set the global rule service instance.

    Args:
        service: The RuleService instance to use
    """
    global _rule_service
    _rule_service = service


def get_rule_service_dependency() -> RuleService:
    """
    Get the rule service dependency.

    Returns:
        RuleService: The rule service instance

    Raises:
        RuntimeError: If rule service is not initialized
    """
    if _rule_service is None:
        raise RuntimeError("Rule service not initialized")
    return _rule_service
