import asyncio
import os
import signal
import sys

from core.logging.logging_system import log as logger
from models import PatientData, RuleResult
from rules.base_rules.base import BaseRule

# 全局变量，用于在每个工作进程中缓存规则注册表
worker_rule_registry: dict[str, BaseRule] = {}


def _graceful_exit(signum, frame):
    """优雅地退出进程，抑制 KeyboardInterrupt。"""
    sys.exit(0)


def init_worker(registry: dict[str, BaseRule]):
    """
    多进程池中每个工作进程的初始化函数。

    此函数在每个工作进程启动时被调用一次，用于接收并缓存
    主进程发送过来的完整规则注册表。

    Args:
        registry: 从主进程传递过来的规则映射，key为rule_id，value为规则实例。
    """
    # 注册 SIGINT 信号处理器，以实现安静退出
    signal.signal(signal.SIGINT, _graceful_exit)

    global worker_rule_registry
    worker_rule_registry = registry
    logger.info(
        f"工作进程 {os.getpid()} 初始化完成，已加载 {len(worker_rule_registry)} 个规则。"
    )


def execute_rule_in_worker(
    rule_id: str, data: PatientData
) -> RuleResult | None:
    """
    在子进程中执行的实际工作函数。

    它使用在初始化时缓存的 `worker_rule_registry` 来查找规则并执行验证。
    此函数现在包含 setup 和 teardown 逻辑，以确保规则资源的正确管理。

    Args:
        rule_id: 需要执行的规则的ID。
        data: 用于规则验证的患者数据。

    Returns:
        如果规则验证为违规，则返回 RuleResult，否则返回 None。
    """
    rule_instance = worker_rule_registry.get(rule_id)

    if not rule_instance:
        logger.warning(
            f"在工作进程 {os.getpid()} 的注册表中找不到规则ID: {rule_id}。 "
            f"可用的规则: {list(worker_rule_registry.keys())}"
        )
        return None

    try:
        logger.debug(f"工作进程 {os.getpid()} 开始处理规则: {rule_id}")
        # 如果 rule_instance 有 setup 方法，则调用
        if hasattr(rule_instance, "setup"):
            rule_instance.setup()

        # 在子进程中为每个任务运行一个新的事件循环来执行异步的验证方法
        result = asyncio.run(rule_instance.validate(data))
        logger.debug(f"工作进程 {os.getpid()} 规则验证完成。结果: {result}")
        return result

    except Exception as e:
        logger.error(
            f"工作进程 {os.getpid()} 处理规则 {rule_id} 时发生致命错误: {e}",
            exc_info=True,
        )
        return None
    finally:
        # 如果 rule_instance 有 teardown 方法，则调用
        if rule_instance and hasattr(rule_instance, "teardown"):
            logger.debug(f"工作进程 {os.getpid()} 正在为规则 {rule_id} 调用 teardown。")
            rule_instance.teardown()
