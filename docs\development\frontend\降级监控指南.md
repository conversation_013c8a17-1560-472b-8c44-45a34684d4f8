# 降级监控前端使用指南

## 1. 概述

降级监控前端界面提供了直观的系统降级状态展示、手动控制和历史分析功能。本指南将详细介绍如何使用这些功能来监控和管理系统的降级状态。

## 2. 界面概览

### 2.1 主要功能模块
- **状态展示区域**：显示当前降级状态和组件信息
- **手动控制区域**：提供降级触发、恢复和级别设置功能
- **指标统计区域**：展示降级相关的统计数据
- **事件时间线**：显示最近的降级事件历史
- **历史查询区域**：提供详细的历史数据查询和分析

### 2.2 访问地址
- **开发环境**：http://localhost:5173/degradation
- **生产环境**：https://your-domain.com/degradation

## 3. 状态监控功能

### 3.1 当前状态展示

#### 3.1.1 状态卡片
状态卡片显示系统的核心降级信息：

**正常状态示例**：
```
┌─────────────────────────────────┐
│ 🟢 系统运行正常                  │
│                                │
│ 当前级别：正常                   │
│ 持续时间：--                    │
│ 触发器：无激活                   │
│ 手动覆盖：否                    │
└─────────────────────────────────┘
```

**降级状态示例**：
```
┌─────────────────────────────────┐
│ 🟡 系统处于轻度降级              │
│                                │
│ 当前级别：轻度降级               │
│ 持续时间：5分32秒                │
│ 触发器：CPU使用率               │
│ 手动覆盖：否                    │
└─────────────────────────────────┘
```

#### 3.1.2 状态指示器说明
- 🟢 **绿色**：系统正常运行
- 🟡 **黄色**：轻度降级状态
- 🟠 **橙色**：中度降级状态
- 🔴 **红色**：重度降级状态

### 3.2 组件状态列表

组件状态列表显示各个性能优化组件的详细状态：

| 组件名称 | 状态 | 降级级别 | 配置变更 | 性能影响 |
|----------|------|----------|----------|----------|
| 动态进程池 | 🟡 降级中 | 轻度降级 | 工作进程：8→6 | -25% |
| 智能缓存 | 🟢 正常 | 正常 | 无变更 | 0% |
| 对象池 | 🟢 正常 | 正常 | 无变更 | 0% |

#### 3.2.1 组件状态详情
点击组件名称可查看详细信息：
- **原始配置**：组件的正常运行配置
- **当前配置**：降级后的实际配置
- **变更历史**：最近的配置变更记录
- **性能影响**：降级对组件性能的具体影响

### 3.3 性能影响评估

性能影响区域显示降级对整体系统性能的预估影响：

```
┌─────────────────────────────────┐
│ 📊 性能影响评估                  │
│                                │
│ 整体性能降低：约25%              │
│ 预计恢复时间：3-5分钟            │
│ 影响组件数：1/3                 │
│                                │
│ 恢复条件：                      │
│ • CPU使用率降至56%以下           │
│ • 持续60秒                      │
└─────────────────────────────────┘
```

## 4. 手动控制功能

### 4.1 快速操作按钮

#### 4.1.1 恢复正常按钮
- **功能**：将系统恢复到正常状态
- **可用条件**：系统处于降级状态时
- **操作流程**：
  1. 点击"恢复正常"按钮
  2. 在弹出对话框中输入恢复原因（可选）
  3. 确认操作
  4. 系统执行恢复并显示结果

#### 4.1.2 触发降级按钮
- **功能**：手动触发系统降级
- **操作流程**：
  1. 点击"触发降级"按钮
  2. 选择降级级别（轻度/中度/重度）
  3. 输入降级原因（必填）
  4. 选择是否强制执行
  5. 确认操作

#### 4.1.3 设置级别按钮
- **功能**：设置系统到指定的降级级别
- **操作流程**：
  1. 点击"设置级别"按钮
  2. 选择目标级别（正常/轻度/中度/重度）
  3. 输入设置原因（必填）
  4. 设置持续时间（可选，0表示永久）
  5. 确认操作

### 4.2 操作确认机制

所有手动操作都有二次确认机制：

```
┌─────────────────────────────────┐
│ ⚠️  确认降级操作                 │
│                                │
│ 确认要将系统降级到轻度降级吗？    │
│ 此操作将影响系统性能。           │
│                                │
│ 降级级别：轻度降级               │
│ 降级原因：预防性维护             │
│ 强制执行：否                    │
│                                │
│ [取消]  [确认降级]              │
└─────────────────────────────────┘
```

### 4.3 操作结果反馈

操作完成后会显示详细的结果信息：

**成功示例**：
```
✅ 降级操作成功
• 当前级别：轻度降级
• 执行时间：2023-01-01 10:30:15
• 影响组件：2个
• 预计性能影响：25%
```

**失败示例**：
```
❌ 降级操作失败
• 错误原因：组件响应超时
• 建议：检查系统状态后重试
• 联系支持：如问题持续请联系技术支持
```

## 5. 指标统计功能

### 5.1 关键指标卡片

指标统计区域显示四个关键指标：

#### 5.1.1 总降级次数
```
┌─────────────────┐
│ 📈 总降级次数    │
│                │
│      25        │
│                │
│ 成功: 24 失败: 1│
└─────────────────┘
```

#### 5.1.2 总恢复次数
```
┌─────────────────┐
│ ✅ 总恢复次数    │
│                │
│      20        │
│                │
│ 成功: 20 失败: 0│
└─────────────────┘
```

#### 5.1.3 执行动作数
```
┌─────────────────┐
│ ⚙️ 执行动作数    │
│                │
│      75        │
│                │
│ 成功: 73 失败: 2│
└─────────────────┘
```

#### 5.1.4 平均持续时间
```
┌─────────────────┐
│ ⏱️ 平均持续时间   │
│                │
│   2分30秒       │
│                │
│ 运行时间: 24小时 │
└─────────────────┘
```

### 5.2 成功率统计

界面还显示降级和恢复的成功率：
- **降级成功率**：96% (24/25)
- **恢复成功率**：100% (20/20)
- **整体可靠性**：98%

## 6. 事件时间线

### 6.1 时间线展示

事件时间线以时间顺序显示最近的降级事件：

```
🕐 2023-01-01 10:30:15  🟡 降级触发    轻度降级
   触发器: CPU使用率 | 值: 75.5% | 执行了3个动作

🕐 2023-01-01 10:25:30  🟢 系统恢复    正常
   恢复原因: 负载降低 | 持续时间: 5分钟

🕐 2023-01-01 10:20:15  🟠 级别变更    中度降级
   变更原因: 条件恶化 | 从轻度降级升级
```

### 6.2 事件详情

点击事件可查看详细信息：
- **事件时间**：精确的时间戳
- **事件类型**：触发、恢复、级别变更等
- **触发条件**：具体的触发器和阈值
- **执行动作**：具体执行的降级动作
- **影响组件**：受影响的组件列表
- **元数据**：其他相关信息

## 7. 历史查询功能

### 7.1 查询条件设置

历史查询支持多维度筛选：

#### 7.1.1 时间范围
- **预设范围**：最近1小时、最近24小时、最近7天
- **自定义范围**：选择具体的开始和结束时间

#### 7.1.2 事件类型筛选
- 降级触发
- 降级恢复
- 级别变更
- 手动触发
- 手动恢复
- 阈值超限

#### 7.1.3 降级级别筛选
- 正常
- 轻度降级
- 中度降级
- 重度降级

### 7.2 查询结果展示

查询结果以表格形式展示：

| 时间 | 事件类型 | 降级级别 | 触发器 | 触发值 | 动作数 | 详情 |
|------|----------|----------|--------|--------|--------|------|
| 10:30:15 | 降级触发 | 轻度降级 | CPU使用率 | 75.5% | 3 | [详情] |
| 10:25:30 | 系统恢复 | 正常 | - | - | 2 | [详情] |

### 7.3 数据导出

支持将查询结果导出为：
- **CSV格式**：用于Excel分析
- **JSON格式**：用于程序处理
- **PDF报告**：用于文档归档

## 8. 响应式设计

### 8.1 桌面端布局
- **左右分栏**：状态控制在左，图表历史在右
- **网格布局**：指标卡片采用2x2网格排列
- **固定导航**：顶部导航栏固定显示

### 8.2 平板端适配
- **上下布局**：状态区域在上，控制区域在下
- **可折叠面板**：历史查询区域可折叠
- **触摸优化**：按钮和链接适配触摸操作

### 8.3 移动端优化
- **垂直布局**：所有区域垂直排列
- **简化界面**：隐藏非关键信息
- **手势支持**：支持滑动和点击手势

## 9. 实时更新机制

### 9.1 自动刷新
- **默认间隔**：30秒自动刷新数据
- **可配置间隔**：支持10秒、30秒、1分钟、5分钟
- **智能刷新**：降级状态变化时立即刷新

### 9.2 状态指示
- **刷新指示器**：显示数据刷新状态
- **最后更新时间**：显示数据的最后更新时间
- **连接状态**：显示与后端的连接状态

## 10. 使用最佳实践

### 10.1 日常监控
1. **定期检查**：每天查看系统状态和关键指标
2. **关注趋势**：观察降级频率和持续时间的变化
3. **及时响应**：发现异常时及时分析和处理

### 10.2 手动操作
1. **谨慎操作**：手动操作前先了解当前状态
2. **记录原因**：所有手动操作都要提供详细原因
3. **验证结果**：操作后验证系统状态是否符合预期

### 10.3 历史分析
1. **定期分析**：每周分析降级历史数据
2. **寻找模式**：识别降级的时间模式和原因
3. **优化配置**：根据历史数据优化触发器配置

## 11. 常见问题

### 11.1 界面问题
**Q: 页面加载缓慢或无法访问？**
A: 检查网络连接和后端服务状态，确认API接口正常。

**Q: 数据不更新或显示错误？**
A: 刷新页面或检查浏览器控制台错误信息。

### 11.2 操作问题
**Q: 手动操作失败？**
A: 检查操作权限和系统当前状态，确认参数正确。

**Q: 无法查看历史数据？**
A: 检查时间范围设置和筛选条件，确认数据存在。

### 11.3 性能问题
**Q: 界面响应慢？**
A: 调整自动刷新间隔，减少同时显示的数据量。

**Q: 移动端显示异常？**
A: 确认浏览器兼容性，尝试刷新页面或清除缓存。
