# ===== 从节点生产环境配置模板 =====
# 复制此文件为 .env.slave 并根据实际情况修改配置

# ===== 应用模式配置 =====
# 节点类型: slave（从节点）
MODE=slave
# 运行环境: PROD（生产环境）
RUN_MODE=PROD

# ===== 服务端口配置 =====
SERVER_PORT=18001
# 服务器监听地址
SERVER_HOST=0.0.0.0

# ===== 主节点连接配置 =====
# 主节点API地址（生产环境使用实际主节点地址）
MASTER_API_ENDPOINT=http://master.rule.yading.xyz:18001
# 从节点API密钥（必须与主节点的MASTER_API_SECRET_KEY匹配）
SLAVE_API_KEY=your-very-secure-secret-key-for-production-environment

# ===== 规则同步配置 =====
# 是否启用规则同步（在线模式：true，离线模式：false）
ENABLE_RULE_SYNC=true
# 规则同步间隔（秒）
RULE_SYNC_INTERVAL=60
# 规则同步超时时间（秒）
RULE_SYNC_TIMEOUT=120
# 同步重试配置
RULE_SYNC_MAX_RETRIES=3
RULE_SYNC_RETRY_INTERVAL=300

# ===== 性能配置（生产环境优化） =====
# 工作进程数（建议根据CPU核心数调整）
WORKERS=8
# 队列最大大小
QUEUE_MAX_SIZE=2000
# 日志级别（生产环境建议使用 info 或 warning）
LOG_LEVEL=INFO

# ===== 离线模式配置（医院内网部署） =====
# 是否启用手动规则导入
ENABLE_MANUAL_RULE_IMPORT=true
# 规则缓存文件路径
RULES_CACHE_FILE=/app/rules_cache.json.gz
