/**
 * Composables兼容性测试
 * 验证Composables与更新后Store的兼容性
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useRuleDetail, useRuleDetailDrawer } from '../business/useRuleDetail.js'
import { useRuleDetailsManagement } from '../business/useRuleDetailsManagement.js'

// Mock Store
vi.mock('@/stores/rules', () => ({
  useRulesStore: () => ({
    currentRule: { value: null },
    ruleSchema: { value: [] },
    ruleStatistics: { value: {} },
    detailLoading: { value: false },
    fetchRuleDetail: vi.fn().mockResolvedValue({}),
    clearCurrentRule: vi.fn(),
    refreshRule: vi.fn().mockResolvedValue(true),
    getRuleByKey: vi.fn().mockReturnValue(null)
  })
}))

vi.mock('@/stores/ruleDetails', () => ({
  useRuleDetailsStore: () => ({
    detailsList: { value: [] },
    currentDetail: { value: null },
    selectedDetails: { value: [] },
    pagination: { value: { page: 1, pageSize: 20 } },
    filters: { value: {} },
    operationStatus: { value: {} },
    loading: { value: false },
    detailLoading: { value: false },
    batchLoading: { value: false },
    searchLoading: { value: false },
    isLoading: { value: false },
    paginationInfo: { value: {} },
    activeFilters: { value: {} },
    detailsCount: { value: 0 },
    hasDetails: { value: false },
    selectedCount: { value: 0 },
    hasSelected: { value: false },
    fetchDetailsList: vi.fn().mockResolvedValue({ items: [], total: 0 }),
    fetchDetailById: vi.fn().mockResolvedValue({}),
    createDetail: vi.fn().mockResolvedValue({}),
    updateDetail: vi.fn().mockResolvedValue({}),
    deleteDetail: vi.fn().mockResolvedValue(true),
    searchDetails: vi.fn().mockResolvedValue({ items: [], total: 0 }),
    updateFilters: vi.fn(),
    resetFilters: vi.fn(),
    updatePagination: vi.fn(),
    toggleDetailSelection: vi.fn(),
    toggleSelectAll: vi.fn(),
    clearEnhancedCache: vi.fn().mockResolvedValue(true)
  })
}))

// Mock 增强错误处理
vi.mock('@/utils/enhancedErrorHandler', () => ({
  enhancedErrorHandler: {
    handle: vi.fn()
  }
}))

describe('Composables兼容性测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('useRuleDetail', () => {
    it('应该正确初始化并提供所有必需的方法', () => {
      const ruleDetail = useRuleDetail()

      // 验证基础状态
      expect(ruleDetail.currentRule).toBeDefined()
      expect(ruleDetail.ruleSchema).toBeDefined()
      expect(ruleDetail.isVisible).toBeDefined()
      expect(ruleDetail.currentRuleKey).toBeDefined()

      // 验证计算属性
      expect(ruleDetail.drawerTitle).toBeDefined()
      expect(ruleDetail.hasRuleData).toBeDefined()

      // 验证核心方法
      expect(typeof ruleDetail.fetchRuleDetail).toBe('function')
      expect(typeof ruleDetail.clearCurrentRule).toBe('function')
      expect(typeof ruleDetail.showRuleDetail).toBe('function')
      expect(typeof ruleDetail.hideRuleDetail).toBe('function')

      // 验证规则明细方法
      expect(typeof ruleDetail.fetchRuleDetailsList).toBe('function')
      expect(typeof ruleDetail.getRuleDetailsCount).toBe('function')
      expect(typeof ruleDetail.refreshRuleDetails).toBe('function')

      // 验证性能监控
      expect(ruleDetail.performanceStats).toBeDefined()
      expect(typeof ruleDetail.getPerformanceStats).toBe('function')
      expect(typeof ruleDetail.resetPerformanceStats).toBe('function')
    })

    it('应该正确处理规则详情获取', async () => {
      const ruleDetail = useRuleDetail()
      const result = await ruleDetail.fetchRuleDetail('test-rule')

      expect(result).toBeDefined()
      expect(ruleDetail.currentRuleKey.value).toBe('test-rule')
    })

    it('应该正确处理规则明细列表获取', async () => {
      const ruleDetail = useRuleDetail()
      const result = await ruleDetail.fetchRuleDetailsList('test-rule')

      expect(Array.isArray(result)).toBe(true)
    })

    it('应该正确记录性能数据', async () => {
      const ruleDetail = useRuleDetail()
      
      await ruleDetail.fetchRuleDetail('test-rule')
      const stats = ruleDetail.getPerformanceStats()

      expect(stats.fetchRuleDetailTime).toBeGreaterThanOrEqual(0)
      expect(stats.lastUpdateTime).toBeTruthy()
    })
  })

  describe('useRuleDetailsManagement', () => {
    it('应该正确初始化并提供所有必需的方法', () => {
      const management = useRuleDetailsManagement('test-rule')

      // 验证状态
      expect(management.currentRuleKey).toBeDefined()
      expect(management.searchKeyword).toBeDefined()
      expect(management.isInitialized).toBeDefined()

      // 验证Store状态
      expect(management.detailsList).toBeDefined()
      expect(management.pagination).toBeDefined()
      expect(management.filters).toBeDefined()

      // 验证核心方法
      expect(typeof management.initialize).toBe('function')
      expect(typeof management.loadDetailsList).toBe('function')
      expect(typeof management.refreshList).toBe('function')

      // 验证搜索和过滤
      expect(typeof management.performSearch).toBe('function')
      expect(typeof management.clearSearch).toBe('function')
      expect(typeof management.applyFilters).toBe('function')

      // 验证CRUD操作
      expect(typeof management.createDetail).toBe('function')
      expect(typeof management.updateDetail).toBe('function')
      expect(typeof management.deleteDetail).toBe('function')

      // 验证性能监控
      expect(management.performanceMetrics).toBeDefined()
      expect(typeof management.getPerformanceMetrics).toBe('function')
      expect(typeof management.resetPerformanceMetrics).toBe('function')
    })

    it('应该正确处理数据加载', async () => {
      const management = useRuleDetailsManagement('test-rule')
      await management.loadDetailsList()

      // 验证Store方法被调用
      expect(management.detailsList).toBeDefined()
    })

    it('应该正确记录性能指标', async () => {
      const management = useRuleDetailsManagement('test-rule')
      
      await management.loadDetailsList()
      const metrics = management.getPerformanceMetrics()

      expect(metrics.totalOperations).toBeGreaterThan(0)
      expect(metrics.lastOperationTime).toBeTruthy()
    })
  })

  describe('向后兼容性', () => {
    it('useRuleDetail应该保持与现有组件兼容的API', () => {
      const ruleDetail = useRuleDetail()

      // 验证关键属性存在
      const requiredProperties = [
        'currentRule', 'ruleSchema', 'isVisible', 'drawerTitle',
        'fetchRuleDetail', 'clearCurrentRule', 'showRuleDetail', 'hideRuleDetail'
      ]

      requiredProperties.forEach(prop => {
        expect(ruleDetail[prop]).toBeDefined()
      })
    })

    it('useRuleDetailsManagement应该保持与现有组件兼容的API', () => {
      const management = useRuleDetailsManagement('test-rule')

      // 验证关键属性存在
      const requiredProperties = [
        'detailsList', 'pagination', 'filters', 'loading',
        'loadDetailsList', 'performSearch', 'createDetail', 'updateDetail', 'deleteDetail'
      ]

      requiredProperties.forEach(prop => {
        expect(management[prop]).toBeDefined()
      })
    })
  })
})
