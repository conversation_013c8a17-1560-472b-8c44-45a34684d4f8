from models import PatientData, RuleOutput, RuleResult
from rules.base_rules.base import BaseRule


class DrugLimitChildrenRule(BaseRule):
    """
    药品儿童专用-规则
    """

    rule_key = "drug_limit_children"
    rule_name = "药品儿童专用"
    rule_desc = """1、精确匹配药品医保代码
2、患者年龄>=18岁，提示"""

    def __init__(
        self,
        rule_id: str,                    # 规则ID
        yb_code: list[str],              # 药品编码
        rule_name: str,                  # 规则名称
        level1: str,                     # 一级错误类型
        level2: str,                     # 二级错误类型
        level3: str,                     # 三级错误类型
        error_reason: str,               # 错误原因
        degree: str,                     # 错误程度
        reference: str,                  # 质控依据或参考资料
        detail_position: str,            # 具体位置描述
        prompted_fields3: str | None,    # 提示字段类型
        prompted_fields1: str,           # 提示字段编码
        type: str,                       # 规则类别
        pos: str,                        # 适用业务
        applicableArea: str,             # 适用地区
        default_use: str,                # 默认选用
        remarks: str | None,             # 备注信息
        in_illustration: str | None,     # 入参说明
        start_date: str,                 # 开始日期
        end_date: str,                   # 结束日期
    ):
        super().__init__(rule_id)
        self.yb_codes = yb_code
        self.rule_name = rule_name
        self.level1 = level1
        self.level2 = level2
        self.level3 = level3
        self.type = type
        self.error_reason = error_reason
        self.degree = degree
        self.reference = reference
        self.prompted_fields3 = prompted_fields3
        self.prompted_fields1 = prompted_fields1
        self.detail_position = detail_position
        self.pos = pos
        self.applicableArea = applicableArea
        self.default_use = default_use
        self.remarks = remarks
        self.in_illustration = in_illustration
        self.start_date = start_date
        self.end_date = end_date

    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        药品儿童专用
        """
        # 1. 获取年龄，如果年龄小于等于年龄阈值-18，则不违规，直接返回
        age = patient_data.basic_information.age
        if age is None or age == '':
            return None
        if str(age).isdigit() and int(age) <= 18:
            return None

        # 2. 限制儿童使用，如果成年人使用了，则违规，所以所有数据都是违规
        # 使用日期集合
        used_dates = set()
        # 使用数量
        used_count = 0
        # 违规项目列表，即费用id列表
        used_fee_ids = []
        # 违规金额
        error_fee = 0

        for fee in patient_data.fees:
            # 判断药品编码是否在违规编码列表中
            if fee.ybdm not in self.yb_codes:
                continue

            # 判断日期是否正确，必须是毫秒级时间戳
            jzsj_str = str(fee.jzsj)
            if not jzsj_str.isdigit() or len(jzsj_str) < 10:
                continue

            # 违规数量、违规金额、违规项目
            used_count += fee.sl
            error_fee += fee.je
            used_fee_ids.append(fee.id)

            # 把记账时间转换成日期形式
            fee_date = self._trans_timestamp_to_date(int(jzsj_str[:10]))
            used_dates.add(fee_date)

        # 3. 如果使用数量为 0，则不违规，直接返回
        if used_count == 0:
            return None

        # 提前计算，避免后面重复计算
        used_ids = ",".join(used_fee_ids)
        used_day = len(used_dates)

        rule_output = RuleOutput(
            type_=self.type,
            message=self.error_reason,
            level1=self.level1,
            level2=self.level2,
            level3=self.level3,
            error_reason=self.error_reason,
            degree=self.degree,
            reference=self.reference,
            prompted_fields3=self.prompted_fields3,
            prompted_fields1=self.prompted_fields1,
            detail_position=self.detail_position,
            pos=self.pos,
            applicableArea=self.applicableArea,
            default_use=self.default_use,
            remarks=self.remarks,
            in_illustration=self.in_illustration,
            start_date=self.start_date,
            end_date=self.end_date,
            used_count=used_count,
            illegal_count=used_count,
            used_day=used_day,
            illegal_day=used_day,
            illegal_item=used_ids,
            error_fee=error_fee,
            prompted_fields2=used_ids,
        )

        # 返回结果
        return RuleResult(
            id=self.rule_id,
            output=rule_output,
        )
