# ===== 前端专用 .dockerignore (优化版) =====
# 前端构建只需要源代码和配置文件
# 优化构建上下文，减少不必要的文件传输

# ===== 版本控制 =====
.git
.gitignore
.gitattributes

# ===== Node.js相关 =====
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.npm
.yarn-integrity

# ===== 构建产物（构建阶段会重新生成） =====
dist
build
.next
.nuxt
.vuepress/dist

# ===== 开发工具 =====
.vscode
.idea
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# ===== 环境文件 =====
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===== 日志文件 =====
logs
*.log

# ===== 运行时数据 =====
pids
*.pid
*.seed
*.pid.lock

# ===== 测试覆盖率 =====
coverage
.nyc_output
.coverage

# ===== 缓存目录 =====
.cache
.parcel-cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===== 依赖锁定文件（保留package-lock.json用于精确依赖） =====
yarn.lock
pnpm-lock.yaml

# ===== 可选的REPL历史 =====
.node_repl_history

# ===== 输出的npm包 =====
*.tgz

# ===== 其他构建工具缓存 =====
.fusebox/
.dynamodb/
.serverless

# ===== 测试文件 =====
tests
test
__tests__
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# ===== 文档文件 =====
docs
README.md
CHANGELOG.md
LICENSE

# ===== 配置文件（保留必要的构建配置） =====
.editorconfig
.prettierrc
.prettierignore
eslint.config.js
.eslintrc.*
jest.config.js
vitest.config.js

# ===== TypeScript配置（保留用于构建） =====
# tsconfig.json - 保留
# tsconfig.*.json - 保留

# ===== 临时文件 =====
tmp
temp
.tmp

# ===== Docker相关 =====
Dockerfile*
docker-compose*.yml
.dockerignore*

# ===== CI/CD =====
.github
.gitlab-ci.yml
.travis.yml
.circleci

# ===== 其他工具配置 =====
.browserslistrc
.nvmrc
.yarnrc
.npmrc

# ===== 后端文件（前端不需要） =====
../*.py
../api/
../core/
../models/
../services/
../rules/
../utils/
../config/
../tests/
../alembic/
../requirements.txt
../alembic.ini
