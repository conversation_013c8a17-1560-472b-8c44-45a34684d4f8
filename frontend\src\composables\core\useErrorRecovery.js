/**
 * 智能错误恢复策略
 * 提供错误分类、恢复策略、降级处理和自动修复机制
 */

import { ref, computed } from 'vue'
import { useAppStore } from '@/stores/app'

/**
 * 错误类型枚举
 */
export const ErrorTypes = {
  NETWORK_ERROR: 'network_error',
  TIMEOUT_ERROR: 'timeout_error',
  VALIDATION_ERROR: 'validation_error',
  AUTHENTICATION_ERROR: 'authentication_error',
  AUTHORIZATION_ERROR: 'authorization_error',
  SERVER_ERROR: 'server_error',
  CLIENT_ERROR: 'client_error',
  UNKNOWN_ERROR: 'unknown_error'
}

/**
 * 恢复策略类型
 */
export const RecoveryStrategies = {
  RETRY: 'retry',                    // 重试
  FALLBACK: 'fallback',             // 降级
  CACHE: 'cache',                   // 使用缓存
  REDIRECT: 'redirect',             // 重定向
  IGNORE: 'ignore',                 // 忽略
  ESCALATE: 'escalate',             // 上报
  USER_ACTION: 'user_action'        // 需要用户操作
}

/**
 * 默认错误恢复配置
 */
const DEFAULT_RECOVERY_CONFIG = {
  [ErrorTypes.NETWORK_ERROR]: {
    strategy: RecoveryStrategies.RETRY,
    retryCount: 3,
    retryDelay: [1000, 2000, 4000],
    fallbackStrategy: RecoveryStrategies.CACHE,
    userMessage: '网络连接异常，正在重试...',
    escalateAfter: 3
  },

  [ErrorTypes.TIMEOUT_ERROR]: {
    strategy: RecoveryStrategies.RETRY,
    retryCount: 2,
    retryDelay: [2000, 5000],
    fallbackStrategy: RecoveryStrategies.FALLBACK,
    userMessage: '请求超时，正在重试...',
    escalateAfter: 2
  },

  [ErrorTypes.VALIDATION_ERROR]: {
    strategy: RecoveryStrategies.USER_ACTION,
    retryCount: 0,
    userMessage: '请检查输入数据是否正确',
    showUserFeedback: true,
    escalateAfter: 0
  },

  [ErrorTypes.AUTHENTICATION_ERROR]: {
    strategy: RecoveryStrategies.REDIRECT,
    retryCount: 0,
    redirectUrl: '/login',
    userMessage: '身份验证失败，请重新登录',
    escalateAfter: 0
  },

  [ErrorTypes.AUTHORIZATION_ERROR]: {
    strategy: RecoveryStrategies.USER_ACTION,
    retryCount: 0,
    userMessage: '权限不足，请联系管理员',
    showUserFeedback: true,
    escalateAfter: 0
  },

  [ErrorTypes.SERVER_ERROR]: {
    strategy: RecoveryStrategies.RETRY,
    retryCount: 2,
    retryDelay: [3000, 6000],
    fallbackStrategy: RecoveryStrategies.ESCALATE,
    userMessage: '服务器异常，正在重试...',
    escalateAfter: 2
  },

  [ErrorTypes.CLIENT_ERROR]: {
    strategy: RecoveryStrategies.FALLBACK,
    retryCount: 1,
    retryDelay: [1000],
    userMessage: '客户端错误，正在尝试修复...',
    escalateAfter: 1
  },

  [ErrorTypes.UNKNOWN_ERROR]: {
    strategy: RecoveryStrategies.RETRY,
    retryCount: 1,
    retryDelay: [2000],
    fallbackStrategy: RecoveryStrategies.ESCALATE,
    userMessage: '发生未知错误，正在处理...',
    escalateAfter: 1
  }
}

/**
 * 智能错误恢复
 * @param {Object} config - 恢复配置
 * @returns {Object} 错误恢复实例
 */
export function useErrorRecovery(config = {}) {
  const finalConfig = { ...DEFAULT_RECOVERY_CONFIG, ...config }
  const appStore = useAppStore()

  // 错误恢复状态
  const recoveryHistory = ref([])
  const activeRecoveries = ref(new Map())
  const recoveryStats = ref({
    totalErrors: 0,
    recoveredErrors: 0,
    escalatedErrors: 0,
    ignoredErrors: 0
  })

  // ==================== 错误分类 ====================

  /**
   * 分析错误类型
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {string} 错误类型
   */
  const analyzeErrorType = (error, context = {}) => {
    // 网络错误
    if (error.name === 'NetworkError' ||
        error.message.includes('fetch') ||
        error.message.includes('network') ||
        error.code === 'NETWORK_ERROR') {
      return ErrorTypes.NETWORK_ERROR
    }

    // 超时错误
    if (error.name === 'TimeoutError' ||
        error.message.includes('timeout') ||
        error.code === 'TIMEOUT') {
      return ErrorTypes.TIMEOUT_ERROR
    }

    // HTTP 状态码错误
    if (error.response) {
      const status = error.response.status

      if (status === 401) {
        return ErrorTypes.AUTHENTICATION_ERROR
      }

      if (status === 403) {
        return ErrorTypes.AUTHORIZATION_ERROR
      }

      if (status >= 400 && status < 500) {
        // 检查是否是验证错误
        if (status === 422 ||
            error.response.data?.message?.includes('validation') ||
            error.response.data?.errors) {
          return ErrorTypes.VALIDATION_ERROR
        }
        return ErrorTypes.CLIENT_ERROR
      }

      if (status >= 500) {
        return ErrorTypes.SERVER_ERROR
      }
    }

    // 验证错误
    if (error.name === 'ValidationError' ||
        error.message.includes('validation') ||
        error.message.includes('invalid')) {
      return ErrorTypes.VALIDATION_ERROR
    }

    return ErrorTypes.UNKNOWN_ERROR
  }

  /**
   * 获取错误严重程度
   * @param {string} errorType - 错误类型
   * @returns {string} 严重程度
   */
  const getErrorSeverity = (errorType) => {
    const severityMap = {
      [ErrorTypes.NETWORK_ERROR]: 'medium',
      [ErrorTypes.TIMEOUT_ERROR]: 'medium',
      [ErrorTypes.VALIDATION_ERROR]: 'low',
      [ErrorTypes.AUTHENTICATION_ERROR]: 'high',
      [ErrorTypes.AUTHORIZATION_ERROR]: 'high',
      [ErrorTypes.SERVER_ERROR]: 'high',
      [ErrorTypes.CLIENT_ERROR]: 'medium',
      [ErrorTypes.UNKNOWN_ERROR]: 'high'
    }

    return severityMap[errorType] || 'medium'
  }

  // ==================== 恢复策略执行 ====================

  /**
   * 执行错误恢复
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Promise<Object>} 恢复结果
   */
  const recover = async (error, context = {}) => {
    const errorType = analyzeErrorType(error, context)
    const recoveryConfig = finalConfig[errorType]
    const severity = getErrorSeverity(errorType)

    const recoveryId = `recovery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const recoveryRecord = {
      id: recoveryId,
      error,
      errorType,
      severity,
      context,
      config: recoveryConfig,
      startTime: Date.now(),
      attempts: 0,
      status: 'in_progress',
      strategy: recoveryConfig.strategy,
      result: null
    }

    // 记录恢复历史
    recoveryHistory.value.push(recoveryRecord)
    activeRecoveries.value.set(recoveryId, recoveryRecord)

    // 更新统计
    recoveryStats.value.totalErrors++

    try {
      const result = await executeRecoveryStrategy(recoveryRecord)

      // 更新恢复记录
      recoveryRecord.status = result.success ? 'success' : 'failed'
      recoveryRecord.result = result
      recoveryRecord.endTime = Date.now()
      recoveryRecord.duration = recoveryRecord.endTime - recoveryRecord.startTime

      // 更新统计
      if (result.success) {
        recoveryStats.value.recoveredErrors++
      } else if (result.escalated) {
        recoveryStats.value.escalatedErrors++
      } else if (result.ignored) {
        recoveryStats.value.ignoredErrors++
      }

      return result

    } catch (recoveryError) {
      console.error('Error recovery failed:', recoveryError)

      recoveryRecord.status = 'error'
      recoveryRecord.recoveryError = recoveryError
      recoveryRecord.endTime = Date.now()

      return {
        success: false,
        error: recoveryError,
        strategy: recoveryConfig.strategy,
        escalated: true
      }
    } finally {
      activeRecoveries.value.delete(recoveryId)
    }
  }

  /**
   * 执行具体的恢复策略
   * @param {Object} recoveryRecord - 恢复记录
   * @returns {Promise<Object>} 策略执行结果
   */
  const executeRecoveryStrategy = async (recoveryRecord) => {
    const { strategy, config, context } = recoveryRecord

    switch (strategy) {
      case RecoveryStrategies.RETRY:
        return await executeRetryStrategy(recoveryRecord)

      case RecoveryStrategies.FALLBACK:
        return await executeFallbackStrategy(recoveryRecord)

      case RecoveryStrategies.CACHE:
        return await executeCacheStrategy(recoveryRecord)

      case RecoveryStrategies.REDIRECT:
        return await executeRedirectStrategy(recoveryRecord)

      case RecoveryStrategies.USER_ACTION:
        return await executeUserActionStrategy(recoveryRecord)

      case RecoveryStrategies.ESCALATE:
        return await executeEscalateStrategy(recoveryRecord)

      case RecoveryStrategies.IGNORE:
        return await executeIgnoreStrategy(recoveryRecord)

      default:
        throw new Error(`Unknown recovery strategy: ${strategy}`)
    }
  }

  /**
   * 重试策略
   */
  const executeRetryStrategy = async (recoveryRecord) => {
    const { config, context } = recoveryRecord

    // 显示用户消息
    if (config.userMessage) {
      appStore.addToast({
        type: 'info',
        message: config.userMessage
      })
    }

    // 如果有原始函数，尝试重试
    if (context.retryFunction) {
      for (let attempt = 1; attempt <= config.retryCount; attempt++) {
        recoveryRecord.attempts = attempt

        try {
          // 等待重试延迟
          const delay = Array.isArray(config.retryDelay)
            ? config.retryDelay[attempt - 1] || config.retryDelay[config.retryDelay.length - 1]
            : config.retryDelay

          if (delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay))
          }

          const result = await context.retryFunction()

          return {
            success: true,
            strategy: RecoveryStrategies.RETRY,
            attempts: attempt,
            result
          }
        } catch (retryError) {
          if (attempt === config.retryCount) {
            // 重试失败，尝试降级策略
            if (config.fallbackStrategy) {
              recoveryRecord.strategy = config.fallbackStrategy
              return await executeRecoveryStrategy(recoveryRecord)
            }

            throw retryError
          }
        }
      }
    }

    return {
      success: false,
      strategy: RecoveryStrategies.RETRY,
      attempts: config.retryCount,
      escalated: true
    }
  }

  /**
   * 降级策略
   */
  const executeFallbackStrategy = async (recoveryRecord) => {
    const { context } = recoveryRecord

    if (context.fallbackFunction) {
      try {
        const result = await context.fallbackFunction()

        appStore.addToast({
          type: 'warning',
          message: '使用备用方案处理请求'
        })

        return {
          success: true,
          strategy: RecoveryStrategies.FALLBACK,
          result,
          degraded: true
        }
      } catch (fallbackError) {
        console.warn('Fallback strategy failed:', fallbackError)
      }
    }

    return {
      success: false,
      strategy: RecoveryStrategies.FALLBACK,
      escalated: true
    }
  }

  /**
   * 缓存策略
   */
  const executeCacheStrategy = async (recoveryRecord) => {
    const { context } = recoveryRecord

    if (context.getCachedData) {
      try {
        const cachedData = await context.getCachedData()

        if (cachedData) {
          appStore.addToast({
            type: 'warning',
            message: '网络异常，显示缓存数据'
          })

          return {
            success: true,
            strategy: RecoveryStrategies.CACHE,
            result: cachedData,
            fromCache: true
          }
        }
      } catch (cacheError) {
        console.warn('Cache strategy failed:', cacheError)
      }
    }

    return {
      success: false,
      strategy: RecoveryStrategies.CACHE,
      escalated: true
    }
  }

  /**
   * 重定向策略
   */
  const executeRedirectStrategy = async (recoveryRecord) => {
    const { config } = recoveryRecord

    if (config.redirectUrl) {
      // 这里应该集成路由系统
      console.info(`Redirecting to: ${config.redirectUrl}`)

      appStore.addNotification({
        type: 'warning',
        title: '页面重定向',
        message: config.userMessage || '正在跳转到相关页面...'
      })

      return {
        success: true,
        strategy: RecoveryStrategies.REDIRECT,
        redirectUrl: config.redirectUrl
      }
    }

    return {
      success: false,
      strategy: RecoveryStrategies.REDIRECT,
      escalated: true
    }
  }

  /**
   * 用户操作策略
   */
  const executeUserActionStrategy = async (recoveryRecord) => {
    const { config } = recoveryRecord

    if (config.showUserFeedback) {
      appStore.addNotification({
        type: 'error',
        title: '需要您的操作',
        message: config.userMessage || '请检查并重试',
        persistent: true
      })
    }

    return {
      success: true,
      strategy: RecoveryStrategies.USER_ACTION,
      requiresUserAction: true
    }
  }

  /**
   * 上报策略
   */
  const executeEscalateStrategy = async (recoveryRecord) => {
    const { error, errorType, context } = recoveryRecord

    // 记录到全局错误状态
    appStore.setError(error, {
      errorType,
      recoveryAttempted: true,
      context
    })

    appStore.addNotification({
      type: 'error',
      title: '系统错误',
      message: '系统遇到问题，已记录错误信息',
      persistent: true
    })

    return {
      success: false,
      strategy: RecoveryStrategies.ESCALATE,
      escalated: true
    }
  }

  /**
   * 忽略策略
   */
  const executeIgnoreStrategy = async (recoveryRecord) => {
    return {
      success: true,
      strategy: RecoveryStrategies.IGNORE,
      ignored: true
    }
  }

  // ==================== 统计和监控 ====================

  /**
   * 获取恢复统计
   */
  const getRecoveryStats = () => ({
    ...recoveryStats.value,
    recoveryRate: recoveryStats.value.totalErrors > 0
      ? (recoveryStats.value.recoveredErrors / recoveryStats.value.totalErrors) * 100
      : 0,
    escalationRate: recoveryStats.value.totalErrors > 0
      ? (recoveryStats.value.escalatedErrors / recoveryStats.value.totalErrors) * 100
      : 0
  })

  /**
   * 获取恢复历史
   */
  const getRecoveryHistory = (errorType = null, limit = null) => {
    let history = recoveryHistory.value

    if (errorType) {
      history = history.filter(record => record.errorType === errorType)
    }

    if (limit && limit > 0) {
      history = history.slice(-limit)
    }

    return history
  }

  /**
   * 清除恢复历史
   */
  const clearRecoveryHistory = () => {
    recoveryHistory.value = []
    recoveryStats.value = {
      totalErrors: 0,
      recoveredErrors: 0,
      escalatedErrors: 0,
      ignoredErrors: 0
    }
  }

  // ==================== 返回接口 ====================

  return {
    // 核心方法
    recover,
    analyzeErrorType,
    getErrorSeverity,

    // 策略执行方法
    executeRetryStrategy,
    executeFallbackStrategy,
    executeCacheStrategy,
    executeRedirectStrategy,
    executeUserActionStrategy,
    executeEscalateStrategy,
    executeIgnoreStrategy,

    // 状态和统计
    recoveryHistory,
    activeRecoveries,
    recoveryStats,
    getRecoveryStats,
    getRecoveryHistory,
    clearRecoveryHistory,

    // 配置
    config: finalConfig,

    // 常量
    ErrorTypes,
    RecoveryStrategies
  }
}
