"""
规则预过滤器性能基准测试
验证不同数据量下的过滤效果和性能表现
"""

import time
import unittest
from unittest.mock import Mock

from core.patient_data_analyzer import patient_data_analyzer
from core.rule_index_manager import rule_index_manager
from core.rule_prefilter import rule_prefilter
from models.patient import PatientData


class TestRulePrefilterPerformance(unittest.TestCase):
    """规则预过滤器性能测试"""

    def setUp(self):
        """设置测试环境"""
        # 重置所有组件状态
        rule_index_manager._clear_indexes()
        rule_prefilter.reset_stats()
        patient_data_analyzer.reset_stats()

        # 启用过滤功能
        import core.rule_prefilter

        self.original_setting = getattr(core.rule_prefilter.settings, "ENABLE_RULE_PREFILTER", False)
        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = True

    def tearDown(self):
        """清理测试环境"""
        import core.rule_prefilter

        core.rule_prefilter.settings.ENABLE_RULE_PREFILTER = self.original_setting

    def _create_large_rule_dataset(self, rule_count: int):
        """创建大规模规则数据集"""
        rules = []

        # 创建不同类型的规则
        for i in range(rule_count):
            rule = Mock()
            rule.rule_id = f"rule_{i:05d}"

            # 25% 医保代码规则
            if i % 4 == 0:
                rule.yb_code = f"Y{i:03d},Y{i + 1:03d}"
                rule.diag_whole_code = ""
                rule.diag_code_prefix = ""
                rule.fee_whole_code = ""
                rule.fee_code_prefix = ""
                rule.extended_fields = "{}"

            # 25% 诊断代码规则
            elif i % 4 == 1:
                rule.yb_code = ""
                rule.diag_whole_code = f"I{i:02d}.{i % 10}"
                rule.diag_code_prefix = f"I{i // 10}"
                rule.fee_whole_code = ""
                rule.fee_code_prefix = ""
                rule.extended_fields = "{}"

            # 25% 手术代码规则
            elif i % 4 == 2:
                rule.yb_code = ""
                rule.diag_whole_code = ""
                rule.diag_code_prefix = ""
                rule.fee_whole_code = ""
                rule.fee_code_prefix = ""
                rule.extended_fields = f'{{"surgery_code": "S{i:03d}"}}'

            # 25% 通用规则
            else:
                rule.yb_code = ""
                rule.diag_whole_code = ""
                rule.diag_code_prefix = ""
                rule.fee_whole_code = ""
                rule.fee_code_prefix = ""
                rule.extended_fields = "{}"

            rules.append(rule)

        return rules

    def _create_test_patient_with_codes(self, yb_codes=None, diag_codes=None, surgery_codes=None):
        """创建包含指定代码的测试患者"""
        patient = Mock(spec=PatientData)

        # 创建费用明细
        fees = []
        if yb_codes:
            for code in yb_codes:
                fee = Mock()
                fee.ybdm = code
                fees.append(fee)
        patient.fees = fees

        # 创建诊断信息
        diagnosis_info = Mock()

        if diag_codes and len(diag_codes) > 0:
            diagnosis_info.outPatientDiagnosisICDCode = diag_codes[0]

            if len(diag_codes) > 1:
                diagnosis_list = []
                for code in diag_codes[1:]:
                    diag_item = Mock()
                    diag_item.diagnosisICDCode = code
                    diagnosis_list.append(diag_item)
                diagnosis_info.diagnosis = diagnosis_list
            else:
                diagnosis_info.diagnosis = []
        else:
            diagnosis_info.outPatientDiagnosisICDCode = ""
            diagnosis_info.diagnosis = []

        diagnosis_info.outPatientDiagnosisChICDCode = ""
        diagnosis_info.diagnoseICDCodeOnAdmission = ""

        # 创建手术信息
        if surgery_codes:
            operations = []
            for code in surgery_codes:
                op_item = Mock()
                op_item.operationICDCode = code
                operations.append(op_item)
            diagnosis_info.operation = operations
        else:
            diagnosis_info.operation = []

        patient.Diagnosis = diagnosis_info

        return patient

    def test_small_dataset_performance(self):
        """测试小规模数据集性能 (100规则)"""
        rule_count = 100
        rules = self._create_large_rule_dataset(rule_count)
        rule_index_manager.build_indexes_from_rule_details(rules)

        # 创建匹配部分规则的患者
        patient = self._create_test_patient_with_codes(
            yb_codes=["Y001", "Y005"], diag_codes=["I01.0", "I05.1"], surgery_codes=["S002"]
        )

        all_rule_ids = [rule.rule_id for rule in rules]

        # 执行性能测试
        start_time = time.perf_counter()
        filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)
        end_time = time.perf_counter()

        execution_time_ms = (end_time - start_time) * 1000

        # 验证性能要求
        self.assertLess(execution_time_ms, 5.0, f"小数据集过滤时间过长: {execution_time_ms:.2f}ms")
        self.assertLess(filter_result.filter_time, 5.0, f"过滤器内部时间过长: {filter_result.filter_time:.2f}ms")

        # 验证过滤效果
        self.assertGreater(filter_result.filter_rate, 0.3, "过滤率太低")
        self.assertLess(filter_result.filter_rate, 0.95, "过滤率太高，可能过度过滤")

        print(
            f"小数据集({rule_count}规则) - 总时间: {execution_time_ms:.2f}ms, "
            f"过滤时间: {filter_result.filter_time:.2f}ms, "
            f"过滤率: {filter_result.filter_rate * 100:.1f}%"
        )

    def test_medium_dataset_performance(self):
        """测试中等规模数据集性能 (500规则)"""
        rule_count = 500
        rules = self._create_large_rule_dataset(rule_count)
        rule_index_manager.build_indexes_from_rule_details(rules)

        # 创建匹配更多规则的患者
        patient = self._create_test_patient_with_codes(
            yb_codes=["Y010", "Y020", "Y030"], diag_codes=["I10.0", "I15.1", "I20.2"], surgery_codes=["S010", "S020"]
        )

        all_rule_ids = [rule.rule_id for rule in rules]

        # 执行性能测试
        start_time = time.perf_counter()
        filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)
        end_time = time.perf_counter()

        execution_time_ms = (end_time - start_time) * 1000

        # 验证性能要求
        self.assertLess(execution_time_ms, 8.0, f"中等数据集过滤时间过长: {execution_time_ms:.2f}ms")
        self.assertLess(filter_result.filter_time, 8.0, f"过滤器内部时间过长: {filter_result.filter_time:.2f}ms")

        # 验证过滤效果
        self.assertGreater(filter_result.filter_rate, 0.4, "过滤率太低")

        print(
            f"中等数据集({rule_count}规则) - 总时间: {execution_time_ms:.2f}ms, "
            f"过滤时间: {filter_result.filter_time:.2f}ms, "
            f"过滤率: {filter_result.filter_rate * 100:.1f}%"
        )

    def test_large_dataset_performance(self):
        """测试大规模数据集性能 (1000规则)"""
        rule_count = 1000
        rules = self._create_large_rule_dataset(rule_count)
        rule_index_manager.build_indexes_from_rule_details(rules)

        # 创建复杂患者数据
        patient = self._create_test_patient_with_codes(
            yb_codes=["Y050", "Y100", "Y150", "Y200"],
            diag_codes=["I25.0", "I35.1", "I45.2", "I55.3"],
            surgery_codes=["S050", "S100", "S150"],
        )

        all_rule_ids = [rule.rule_id for rule in rules]

        # 执行性能测试
        start_time = time.perf_counter()
        filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)
        end_time = time.perf_counter()

        execution_time_ms = (end_time - start_time) * 1000

        # 验证性能要求
        self.assertLess(execution_time_ms, 15.0, f"大数据集过滤时间过长: {execution_time_ms:.2f}ms")
        self.assertLess(filter_result.filter_time, 15.0, f"过滤器内部时间过长: {filter_result.filter_time:.2f}ms")

        # 验证过滤效果
        self.assertGreater(filter_result.filter_rate, 0.5, "过滤率太低")

        print(
            f"大数据集({rule_count}规则) - 总时间: {execution_time_ms:.2f}ms, "
            f"过滤时间: {filter_result.filter_time:.2f}ms, "
            f"过滤率: {filter_result.filter_rate * 100:.1f}%"
        )

    def test_concurrent_filtering_performance(self):
        """测试并发过滤性能"""
        rule_count = 300
        rules = self._create_large_rule_dataset(rule_count)
        rule_index_manager.build_indexes_from_rule_details(rules)

        all_rule_ids = [rule.rule_id for rule in rules]

        # 创建多个不同的患者
        patients = []
        for i in range(10):
            patient = self._create_test_patient_with_codes(
                yb_codes=[f"Y{i * 10:03d}", f"Y{i * 10 + 5:03d}"],
                diag_codes=[f"I{i * 2:02d}.0"],
                surgery_codes=[f"S{i * 5:03d}"],
            )
            patients.append(patient)

        # 执行并发过滤测试
        start_time = time.perf_counter()
        results = []

        for patient in patients:
            result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)
            results.append(result)

        end_time = time.perf_counter()
        total_time_ms = (end_time - start_time) * 1000
        avg_time_per_request = total_time_ms / len(patients)

        # 验证并发性能
        self.assertLess(avg_time_per_request, 10.0, f"并发场景下平均处理时间过长: {avg_time_per_request:.2f}ms")

        # 验证所有请求都成功
        self.assertEqual(len(results), len(patients))
        for result in results:
            self.assertGreater(result.filtered_rule_count, 0)
            self.assertLessEqual(result.filter_time, 10.0)

        # 计算总体统计
        total_filter_rate = sum(r.filter_rate for r in results) / len(results)

        print(
            f"并发测试({len(patients)}个请求) - 总时间: {total_time_ms:.2f}ms, "
            f"平均时间: {avg_time_per_request:.2f}ms, "
            f"平均过滤率: {total_filter_rate * 100:.1f}%"
        )

    def test_memory_usage_stability(self):
        """测试内存使用稳定性"""
        import os

        import psutil

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        rule_count = 500
        rules = self._create_large_rule_dataset(rule_count)
        rule_index_manager.build_indexes_from_rule_details(rules)

        all_rule_ids = [rule.rule_id for rule in rules]

        # 执行大量过滤操作
        for i in range(100):
            patient = self._create_test_patient_with_codes(
                yb_codes=[f"Y{i % 50:03d}"], diag_codes=[f"I{i % 30:02d}.0"], surgery_codes=[f"S{i % 40:03d}"]
            )

            result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)
            self.assertIsNotNone(result)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_growth = final_memory - initial_memory

        # 验证内存增长在合理范围内
        self.assertLess(memory_growth, 50.0, f"内存增长过多: {memory_growth:.1f}MB")

        print(f"内存稳定性测试 - 初始: {initial_memory:.1f}MB, 结束: {final_memory:.1f}MB, 增长: {memory_growth:.1f}MB")

    def test_filter_effectiveness_analysis(self):
        """测试过滤效果分析"""
        rule_count = 400
        rules = self._create_large_rule_dataset(rule_count)
        rule_index_manager.build_indexes_from_rule_details(rules)

        all_rule_ids = [rule.rule_id for rule in rules]

        # 测试不同匹配度的患者
        test_scenarios = [
            {
                "name": "高匹配度",
                "patient": self._create_test_patient_with_codes(
                    yb_codes=["Y001", "Y002", "Y003", "Y004"],
                    diag_codes=["I01.0", "I02.1", "I03.2"],
                    surgery_codes=["S001", "S002", "S003"],
                ),
                "expected_min_filter_rate": 0.2,
            },
            {
                "name": "中等匹配度",
                "patient": self._create_test_patient_with_codes(
                    yb_codes=["Y010", "Y020"], diag_codes=["I10.0"], surgery_codes=["S010"]
                ),
                "expected_min_filter_rate": 0.4,
            },
            {
                "name": "低匹配度",
                "patient": self._create_test_patient_with_codes(
                    yb_codes=["Y999"], diag_codes=["I99.9"], surgery_codes=["S999"]
                ),
                "expected_min_filter_rate": 0.7,
            },
        ]

        for scenario in test_scenarios:
            result = rule_prefilter.filter_rules_for_patient(scenario["patient"], all_rule_ids)

            self.assertGreaterEqual(
                result.filter_rate,
                scenario["expected_min_filter_rate"],
                f"{scenario['name']}场景过滤率不足: {result.filter_rate * 100:.1f}%",
            )

            self.assertLess(result.filter_time, 5.0, f"{scenario['name']}场景过滤时间过长: {result.filter_time:.2f}ms")

            print(
                f"{scenario['name']} - 过滤率: {result.filter_rate * 100:.1f}%, "
                f"时间: {result.filter_time:.2f}ms, "
                f"规则数: {result.original_rule_count} -> {result.filtered_rule_count}"
            )


if __name__ == "__main__":
    unittest.main()
