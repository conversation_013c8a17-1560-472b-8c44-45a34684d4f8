# 规则查询API文档

## 概述

规则查询API提供根据关键词模糊匹配规则并返回生效明细规则ID列表的功能。该接口支持对规则键（rule_key）和规则名称（rule_name）进行模糊搜索。

## 接口详情

### 获取规则ID列表

**接口路径**: `/api/v1/rules/ids`  
**请求方法**: `GET`  
**认证要求**: 需要API密钥认证  

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | string | 否 | 用于模糊匹配的关键词，支持中文和英文 |

#### 匹配逻辑

**提供关键词时**：
- 对规则表中的 `rule_key` 和 `rule_name` 字段进行模糊匹配（LIKE查询）
- 匹配条件：关键词包含在 `rule_key` 或 `rule_name` 中（不区分大小写）
- 只返回状态为"READY"（生效）的规则
- 返回第一个匹配规则的明细规则ID列表（is_active=true）

**不提供关键词时**：
- 返回所有状态为"READY"（生效）的规则基本信息
- 包含每个规则的生效明细规则数量统计
- 按规则键（rule_key）升序排列

#### 响应格式

**提供关键词时的成功响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "查询成功",
  "data": {
    "rule_info": {
      "rule_key": "chinese_medicine_payment_rule",
      "rule_name": "中药饮片单复方均不予支付规则",
      "description": "中药饮片相关支付规则"
    },
    "detail_rule_ids": [1001, 1002, 1003, 1004, 1005]
  }
}
```

**不提供关键词时的成功响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "查询成功",
  "data": {
    "total_count": 3,
    "rules": [
      {
        "rule_key": "chinese_medicine_payment_rule",
        "rule_name": "中药饮片单复方均不予支付规则",
        "description": "中药饮片相关支付规则",
        "active_detail_count": 5,
        "updated_at": "2025-01-04T12:00:00"
      },
      {
        "rule_key": "medical_device_rule",
        "rule_name": "医疗器械使用规范",
        "description": "医疗器械相关规则",
        "active_detail_count": 3,
        "updated_at": "2025-01-04T11:30:00"
      }
    ]
  }
}
```

**无匹配结果**:
```json
{
  "code": 200,
  "success": false,
  "message": "未找到匹配的规则",
  "data": null
}
```

**无生效明细规则**:
```json
{
  "code": 200,
  "success": false,
  "message": "该规则下暂无生效的明细规则",
  "data": null
}
```

**服务器错误**:
```json
{
  "detail": "查询规则ID时发生内部错误"
}
```

#### 响应字段说明

**通用字段**：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 业务状态码，200表示请求成功 |
| success | boolean | 操作成功标识 |
| message | string | 用户友好的提示信息 |
| data | object/null | 实际数据内容，失败时为null |

**提供关键词时的响应字段**：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| data.rule_info | object | 匹配到的规则信息 |
| data.rule_info.rule_key | string | 规则键 |
| data.rule_info.rule_name | string | 规则名称 |
| data.rule_info.description | string | 规则描述 |
| data.detail_rule_ids | array | 生效明细规则ID列表（从JSON数据中提取的rule_id），按ID升序排列 |

**不提供关键词时的响应字段**：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| data.total_count | integer | 生效规则总数 |
| data.rules | array | 规则列表 |
| data.rules[].rule_key | string | 规则键 |
| data.rules[].rule_name | string | 规则名称 |
| data.rules[].description | string | 规则描述 |
| data.rules[].active_detail_count | integer | 该规则下生效明细规则数量（从JSON数据中统计的rule_id数量） |
| data.rules[].updated_at | string/null | 规则最后更新时间（ISO格式） |

## 使用示例

### 示例1：使用中文关键词搜索

**请求**:
```bash
GET /api/v1/rules/ids?keyword=中药饮片单复方均不予支付
```

**响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "查询成功",
  "data": {
    "rule_info": {
      "rule_key": "chinese_medicine_payment_rule",
      "rule_name": "中药饮片单复方均不予支付规则"
    },
    "detail_rule_ids": [1001, 1002, 1003, 1004, 1005]
  }
}
```

### 示例2：使用部分关键词搜索

**请求**:
```bash
GET /api/v1/rules/ids?keyword=中药饮片
```

**响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "查询成功",
  "data": {
    "rule_info": {
      "rule_key": "chinese_medicine_payment_rule",
      "rule_name": "中药饮片单复方均不予支付规则"
    },
    "detail_rule_ids": [1001, 1002, 1003, 1004, 1005]
  }
}
```

### 示例3：不提供关键词（获取所有规则）

**请求**:
```bash
GET /api/v1/rules/ids
```

**响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "查询成功",
  "data": {
    "total_count": 3,
    "rules": [
      {
        "rule_key": "chinese_medicine_payment_rule",
        "rule_name": "中药饮片单复方均不予支付规则",
        "description": "中药饮片相关支付规则",
        "active_detail_count": 5,
        "updated_at": "2025-01-04T12:00:00"
      },
      {
        "rule_key": "medical_device_rule",
        "rule_name": "医疗器械使用规范",
        "description": "医疗器械相关规则",
        "active_detail_count": 3,
        "updated_at": "2025-01-04T11:30:00"
      },
      {
        "rule_key": "prescription_rule",
        "rule_name": "处方药使用规范",
        "description": "处方药相关规则",
        "active_detail_count": 2,
        "updated_at": "2025-01-04T10:00:00"
      }
    ]
  }
}
```

### 示例4：无匹配结果

**请求**:
```bash
GET /api/v1/rules/ids?keyword=不存在的规则
```

**响应**:
```json
{
  "code": 200,
  "success": false,
  "message": "未找到匹配的规则",
  "data": null
}
```

## 错误处理

### HTTP状态码

- `200 OK`: 请求成功（包括业务逻辑失败的情况）
- `403 Forbidden`: API密钥认证失败
- `500 Internal Server Error`: 服务器内部错误

### 业务错误码

所有业务相关的错误都返回HTTP 200状态码，通过响应体中的`success`字段和`message`字段来表示具体的业务状态。

## 性能说明

- 接口支持数据库查询优化，使用索引提高查询性能
- 明细规则ID按升序排列返回
- 支持大量明细规则的高效查询

## 注意事项

1. **认证要求**: 该接口需要API密钥认证，请确保在请求头中包含有效的API密钥
2. **关键词匹配**: 支持中文和英文关键词，匹配不区分大小写
3. **规则状态**: 只返回状态为"READY"的规则，已废弃的规则不会被搜索到
4. **明细规则**: 只返回生效的明细规则ID（is_active=true）
5. **排序规则**: 明细规则ID按升序排列
6. **多规则匹配**: 如果多个规则匹配同一关键词，返回第一个匹配的规则

## 版本信息

- **API版本**: v1
- **文档版本**: 1.0.0
- **最后更新**: 2025-01-04
