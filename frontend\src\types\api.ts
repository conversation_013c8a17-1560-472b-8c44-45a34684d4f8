/**
 * API相关的TypeScript类型定义
 */

// 统一API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp: string
  request_id?: string
}

// 分页请求参数接口
export interface PaginationParams {
  page: number
  page_size: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 分页响应接口
export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}

// HTTP方法枚举
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH'
}

// ==================== 规则明细相关类型定义 ====================

/**
 * 规则明细状态枚举
 */
export enum RuleDetailStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DELETED = 'DELETED'
}

/**
 * 规则明细数据接口（使用新的标准字段名）
 */
export interface RuleDetail {
  id: number
  rule_id: string
  rule_key: string
  rule_name: string

  // 错误分类字段（使用新的标准字段名）
  level1: string
  level2: string
  level3: string
  error_reason: string
  degree: string

  // 质控相关字段（使用新的标准字段名）
  reference: string
  detail_position: string
  prompted_fields3?: string
  prompted_fields1: string

  // 业务分类字段（使用新的标准字段名）
  type: string
  pos: string
  applicableArea: string
  default_use: string
  remarks?: string
  in_illustration?: string

  // 时间字段
  start_date: string
  end_date: string

  // 高频字段
  yb_code?: string
  diag_whole_code?: string
  diag_code_prefix?: string
  diag_name_keyword?: string
  fee_whole_code?: string
  fee_code_prefix?: string

  // 扩展字段（JSON格式）
  extended_fields?: Record<string, any>

  // 状态管理字段
  status: RuleDetailStatus

  // 审计字段
  created_at: string
  updated_at: string
  created_by?: string
  updated_by?: string
}

/**
 * 规则明细查询参数接口（使用新的标准字段名）
 */
export interface RuleDetailsQueryParams extends PaginationParams {
  status?: RuleDetailStatus | string
  search?: string
  level1?: string
  level2?: string
  level3?: string
  type?: string
  pos?: string
  date_range?: [string, string]
  start_date?: string
  end_date?: string
}

/**
 * 规则明细创建/更新数据接口（使用新的标准字段名）
 */
export interface RuleDetailData {
  rule_id: string
  rule_key: string
  rule_name: string
  level1: string
  level2: string
  level3: string
  error_reason: string
  degree: string
  reference: string
  detail_position: string
  prompted_fields3?: string
  prompted_fields1: string
  type: string
  pos: string
  applicableArea: string
  default_use: string
  remarks?: string
  in_illustration?: string
  start_date: string
  end_date: string
  yb_code?: string
  diag_whole_code?: string
  diag_code_prefix?: string
  diag_name_keyword?: string
  fee_whole_code?: string
  fee_code_prefix?: string
  extended_fields?: Record<string, any>
  effective_end_time?: string
  extra_data?: Record<string, any>
  remark?: string
  status?: RuleDetailStatus
}

/**
 * 批量操作类型枚举
 */
export enum BatchOperationType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE'
}

/**
 * 批量操作项接口
 */
export interface BatchOperationItem {
  action: BatchOperationType
  data: RuleDetailData | { id: number }
  id?: number
}

/**
 * 批量操作请求接口
 */
export interface BatchOperationRequest {
  operations: BatchOperationItem[]
}

/**
 * 批量操作结果接口
 */
export interface BatchOperationResult {
  success_count: number
  error_count: number
  total_count: number
  errors: Array<{
    index: number
    error: string
    data: any
  }>
  results: Array<{
    index: number
    success: boolean
    data?: RuleDetail
    error?: string
  }>
}

/**
 * 规则明细统计信息接口
 */
export interface RuleDetailsStatistics {
  total_count: number
  status_distribution: Record<RuleDetailStatus, number>
  error_level_distribution: {
    level_1: Record<string, number>
    level_2: Record<string, number>
    level_3: Record<string, number>
  }
  category_distribution: Record<string, number>
  business_distribution: Record<string, number>
  recent_activity: {
    created_today: number
    updated_today: number
    created_this_week: number
    updated_this_week: number
  }
}

/**
 * 规则明细搜索参数接口
 */
export interface RuleDetailsSearchParams {
  keyword: string
  fields?: string[]
  filters?: {
    status?: RuleDetailStatus[]
    error_levels?: string[]
    categories?: string[]
    date_range?: [string, string]
  }
  highlight?: boolean
}

/**
 * 规则明细导出参数接口
 */
export interface RuleDetailsExportParams {
  format: 'excel' | 'csv' | 'json'
  filters?: RuleDetailsQueryParams
  fields?: string[]
  include_metadata?: boolean
}

/**
 * 规则明细字段配置接口
 */
export interface RuleDetailFieldConfig {
  field_name: string
  field_label: string
  field_type: 'string' | 'number' | 'boolean' | 'date' | 'enum'
  required: boolean
  max_length?: number
  enum_values?: string[]
  description?: string
  validation_rules?: string[]
}

/**
 * 规则明细验证结果接口
 */
export interface RuleDetailValidationResult {
  valid: boolean
  errors: Array<{
    field: string
    message: string
    value: any
  }>
  warnings: Array<{
    field: string
    message: string
    value: any
  }>
}

// 请求配置接口
export interface RequestConfig {
  url: string
  method: HttpMethod
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  retry?: number
  cache?: boolean
}

// 错误响应接口
export interface ApiError {
  code: number
  message: string
  details?: any
  timestamp: string
  path?: string
  request_id?: string
}

// 文件上传响应接口
export interface UploadResponse {
  file_id: string
  file_name: string
  file_size: number
  file_type: string
  upload_url?: string
  download_url?: string
  created_at: string
}

// 文件下载参数接口
export interface DownloadParams {
  file_id?: string
  file_name?: string
  file_type?: string
  inline?: boolean
}

// 健康检查响应接口
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded'
  node_type: 'master' | 'slave'
  service: string
  version?: string
  uptime?: number
  checks?: HealthCheck[]
}

// 健康检查项接口
export interface HealthCheck {
  name: string
  status: 'pass' | 'fail' | 'warn'
  message?: string
  duration?: number
  timestamp: string
}

// 认证相关接口
export interface AuthRequest {
  username?: string
  password?: string
  api_key?: string
  token?: string
}

export interface AuthResponse {
  access_token: string
  refresh_token?: string
  token_type: 'Bearer'
  expires_in: number
  user_info?: UserInfo
}

export interface UserInfo {
  id: string
  username: string
  email?: string
  roles: string[]
  permissions: string[]
  last_login?: string
}

// 数据提交相关接口
export interface DataSubmissionRequest {
  rule_key: string
  file_name: string
  file_size: number
  upload_time: string
  data: Record<string, any>[]
  summary: DataSummary
  validation_results?: ValidationResult[]
}

export interface DataSummary {
  total_rows: number
  valid_rows: number
  error_rows: number
  columns: string[]
  file_info?: FileInfo
}

export interface FileInfo {
  name: string
  size: number
  type: string
  last_modified: string
  checksum?: string
}

export interface ValidationResult {
  row_index: number
  column: string
  error_type: string
  error_message: string
  original_value: any
  suggested_value?: any
}

export interface DataSubmissionResponse {
  submission_id: string
  status: 'accepted' | 'rejected' | 'pending'
  message: string
  processed_rows: number
  error_count: number
  warnings?: string[]
  created_at: string
}

// 同步相关接口
export interface SyncRequest {
  node_id?: string
  force_sync?: boolean
  rule_keys?: string[]
  version?: string
}

export interface SyncResponse {
  sync_id: string
  status: 'started' | 'completed' | 'failed'
  synced_rules: number
  failed_rules: number
  start_time: string
  end_time?: string
  errors?: SyncError[]
}

export interface SyncError {
  rule_key: string
  error_code: string
  error_message: string
  timestamp: string
}

// 监控相关接口
export interface MonitoringMetrics {
  cpu_usage: number
  memory_usage: number
  disk_usage: number
  network_io: NetworkIO
  response_time: number
  error_rate: number
  request_count: number
  active_connections: number
  timestamp: string
}

export interface NetworkIO {
  bytes_sent: number
  bytes_received: number
  packets_sent: number
  packets_received: number
}

// 日志相关接口
export interface LogEntry {
  id: string
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR' | 'FATAL'
  message: string
  timestamp: string
  source: string
  context?: Record<string, any>
  stack_trace?: string
}

export interface LogQuery {
  level?: string[]
  source?: string[]
  start_time?: string
  end_time?: string
  keyword?: string
  limit?: number
  offset?: number
}

// 配置相关接口
export interface SystemConfig {
  app_name: string
  version: string
  environment: 'development' | 'testing' | 'production'
  debug: boolean
  features: FeatureFlags
  limits: SystemLimits
}

export interface FeatureFlags {
  [key: string]: boolean
}

export interface SystemLimits {
  max_file_size: number
  max_upload_files: number
  request_timeout: number
  rate_limit: number
  concurrent_requests: number
}

// WebSocket相关接口
export interface WebSocketMessage<T = any> {
  type: string
  data: T
  timestamp: string
  id?: string
}

export interface WebSocketEvent {
  event: string
  data: any
  timestamp: string
}

// 缓存相关接口
export interface CacheConfig {
  ttl: number
  max_size: number
  strategy: 'LRU' | 'LFU' | 'FIFO'
}

export interface CacheStats {
  hits: number
  misses: number
  hit_rate: number
  size: number
  max_size: number
  evictions: number
}
