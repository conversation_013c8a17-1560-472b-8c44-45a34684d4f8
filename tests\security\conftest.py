"""
安全测试专用配置
提供安全测试所需的fixtures和配置
"""

from unittest.mock import Mock

import pytest

# ============================================================================
# 安全测试专用fixtures
# ============================================================================

@pytest.fixture
def mock_auth_service():
    """模拟认证服务"""
    auth_service = Mock()
    auth_service.authenticate.return_value = True
    auth_service.authorize.return_value = True
    auth_service.get_current_user.return_value = {"id": 1, "username": "test_user"}
    return auth_service


@pytest.fixture
def security_test_data():
    """安全测试数据"""
    return {
        "valid_api_key": "valid_test_api_key",
        "invalid_api_key": "invalid_test_api_key",
        "test_user": {
            "id": 1,
            "username": "test_user",
            "permissions": ["read", "write"],
        },
        "unauthorized_user": {
            "id": 2,
            "username": "unauthorized_user",
            "permissions": ["read"],
        }
    }


# ============================================================================
# 安全测试配置
# ============================================================================

@pytest.fixture(autouse=True)
def security_test_setup():
    """安全测试自动设置"""
    # 安全测试需要隔离的环境
    # 确保不会影响真实的安全配置
    yield
