# 内存泄漏缓解架构方案

本文档旨在为 `rule_service` 中发现的内存泄漏问题提供架构层面的解决方案。问题根源在于 `multiprocessing.Pool` 的子进程在被长期复用时，由于 `rule.validate()` 方法执行过程中的内存累积而导致。

## 1. 概览

根据 `spec-pseudocode` 模式的分析，内存泄漏的核心原因是子进程的生命周期过长，而单个规则实现 (`rule.validate()`) 中可能存在未被正确释放的资源。

## 2. 短期缓解方案 (止血策略)

为了快速控制内存泄漏并防止生产系统出现稳定性问题，建议立即采纳以下策略：

### 2.1. 限制子进程的最大任务数

在 `services/rule_service.py` 中初始化 `multiprocessing.Pool` 时，设置 `maxtasksperchild` 参数。

**实现示例:**

```python
# In services/rule_service.py -> RuleService.__init__()

self.process_pool = ProcessPool(
    initializer=init_worker_process,
    init_args=(rules_map,),
    maxtasksperchild=1000  # 核心变更：添加此行
)
```

**原理:**

`maxtasksperchild` 参数会强制进程池在每个子进程执行了 1000 个任务后，自动销毁该子进程并创建一个新的、干净的子进程来替换它。这个过程可以有效地回收该子进程在其生命周期内累积的所有内存，从而阻止内存的无限增长。

**优点:**

*   **实施简单**：只需修改一行代码。
*   **效果显著**：能立即阻止内存泄漏导致的崩溃。
*   **风险低**：是 `multiprocessing` 模块的内置功能，非常稳定。

**建议:**

*   `1000` 是一个保守的初始值。建议在部署后监控内存使用情况和进程重建的开销，并根据实际情况调整此值。

## 3. 长期根治方案 (治本策略)

为了从根本上解决问题并防止未来出现类似的内存泄漏，我们需要建立一套更健壮的架构，强制执行正确的资源管理模式。

### 3.1. 引入规则生命周期管理

通过在 `BaseRule` 中定义明确的生命周期方法，我们可以确保资源被显式地创建和销毁。

**修改 `rules/base_rules/base.py`:**

```python
from abc import ABC, abstractmethod

class BaseRule(ABC):
    """
    规则基类，定义了所有规则必须遵循的接口。
    """

    def setup(self, context: dict):
        """
        (可选) 在 validate 执行前调用。
        用于初始化任何需要复杂清理的资源，如文件句柄、数据库连接等。
        """
        pass

    @abstractmethod
    def validate(self, patient_data: dict) -> dict:
        """
        核心验证逻辑。此方法应被设计为无状态的。
        其输出应仅取决于输入参数。
        """
        raise NotImplementedError

    def teardown(self):
        """
        (可选) 在 validate 执行后调用（即使发生异常）。
        用于显式释放/清理在 setup 中创建的资源。
        """
        pass
```

### 3.2. 更新子进程执行逻辑

修改 `services/worker_init.py` 中的 `execute_rule_in_subprocess` 函数，以确保 `setup` 和 `teardown` 方法被正确调用。

**修改 `services/worker_init.py`:**

```python
def execute_rule_in_subprocess(rule_id, patient_data):
    """
    在多进程池的某个子进程中执行单个规则的校验。
    确保调用规则的 setup 和 teardown 生命周期方法。
    """
    rule_instance = worker_global_rules.get(rule_id)
    if rule_instance is None:
        # 或者可以返回一个表示规则未找到的错误对象
        return None
    
    result = None
    try:
        # 检查并调用 setup 方法
        if hasattr(rule_instance, 'setup') and callable(getattr(rule_instance, 'setup')):
            rule_instance.setup({})  # 可以传递共享的上下文信息
        
        # 执行核心验证
        result = rule_instance.validate(patient_data)
    except Exception as e:
        # 在此记录异常
        # logger.error(f"Error executing rule {rule_id}: {e}")
        return None # or an error object
    finally:
        # 确保 teardown 总是被调用
        if hasattr(rule_instance, 'teardown') and callable(getattr(rule_instance, 'teardown')):
            try:
                rule_instance.teardown()
            except Exception as e:
                # 记录 teardown 失败的异常
                # logger.error(f"Error in teardown for rule {rule_id}: {e}")
                pass
    
    return result
```

### 3.3. 开发者最佳实践

为了使该方案有效，必须在团队中推行以下开发准则：

1.  **规则必须无状态**：`validate()` 方法的实现不应依赖于前一次调用的结果。它的输出必须仅由其输入参数决定。
2.  **显式资源管理**：任何需要打开和关闭的资源（如文件、网络连接、大型内存对象），都必须在 `setup()` 中初始化，并在 `teardown()` 中清理。
3.  **禁止在 `__init__` 中进行复杂初始化**：规则的 `__init__` 方法应保持简单，只用于接收静态配置。所有动态的、需要清理的资源都应在 `setup()` 中处理。

通过结合短期和长期方案，我们可以立即稳定系统，并为项目的长期健康发展建立坚实的基础。