#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile requirements.in
#
alembic==1.16.2
    # via -r requirements.in
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   starlette
asyncmy==0.2.10
    # via -r requirements.in
certifi==2025.6.15
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via uvicorn
colorama==0.4.6
    # via
    #   click
    #   loguru
et-xmlfile==2.0.0
    # via openpyxl
fastapi==0.115.12
    # via -r requirements.in
greenlet==3.2.3
    # via
    #   -r requirements.in
    #   sqlalchemy
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via -r requirements.in
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
loguru==0.7.2
    # via -r requirements.in
mako==1.3.10
    # via alembic
markupsafe==3.0.2
    # via mako
openpyxl==3.1.5
    # via -r requirements.in
psutil==7.0.0
    # via -r requirements.in
pydantic==2.11.5
    # via
    #   -r requirements.in
    #   fastapi
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via -r requirements.in
pymysql==1.1.1
    # via -r requirements.in
python-dotenv==1.1.1
    # via pydantic-settings
python-multipart==0.0.20
    # via -r requirements.in
requests==2.32.4
    # via -r requirements.in
sniffio==1.3.1
    # via anyio
sqlalchemy==2.0.41
    # via
    #   -r requirements.in
    #   alembic
starlette==0.46.2
    # via fastapi
typing-extensions==4.13.2
    # via
    #   alembic
    #   anyio
    #   fastapi
    #   pydantic
    #   pydantic-core
    #   sqlalchemy
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
urllib3==2.5.0
    # via requests
uvicorn==0.34.2
    # via -r requirements.in
win32-setctime==1.2.0
    # via loguru
