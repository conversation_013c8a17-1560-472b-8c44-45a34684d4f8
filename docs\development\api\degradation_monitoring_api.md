# 降级监控和管理API文档

## 概述

降级监控和管理API提供了完整的降级机制监控、控制和管理功能。API分为两个部分：
- **监控API**：公开访问，用于状态查询和监控
- **管理API**：需要API密钥认证，用于手动控制操作

## API端点列表

### 监控API（公开访问）

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/degradation/status` | 获取当前降级状态 |
| GET | `/api/v1/degradation/components` | 获取各组件降级状态 |
| GET | `/api/v1/degradation/metrics` | 获取降级统计指标 |
| GET | `/api/v1/degradation/events` | 获取降级事件日志 |
| GET | `/api/v1/degradation/history` | 获取降级历史记录 |
| GET | `/api/v1/degradation/performance` | 获取降级对性能的影响 |

### 管理API（需要认证）

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/api/v1/degradation/trigger` | 手动触发降级 |
| POST | `/api/v1/degradation/recover` | 手动恢复正常状态 |
| POST | `/api/v1/degradation/level` | 设置指定降级级别 |

## API详细说明

### 1. 获取当前降级状态

**端点**: `GET /api/v1/degradation/status`

**描述**: 获取系统当前的降级状态信息

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "降级状态获取成功",
  "data": {
    "current_level": "light_degradation",
    "previous_level": "normal",
    "is_degraded": true,
    "last_change_time": 1672531200.0,
    "degradation_duration": 300.5,
    "active_triggers": ["cpu_usage", "memory_usage"],
    "is_manual_override": false,
    "override_reason": null,
    "executed_actions_count": 3,
    "enabled": true,
    "running": true
  },
  "request_id": "req_123456789"
}
```

### 2. 获取组件状态

**端点**: `GET /api/v1/degradation/components`

**描述**: 获取各性能优化组件的降级状态

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "组件状态获取成功",
  "data": [
    {
      "component_name": "DynamicProcessPool",
      "current_level": "light_degradation",
      "is_degraded": true,
      "last_change_time": 1672531200.0,
      "degradation_reason": "CPU usage threshold exceeded",
      "enabled": true,
      "original_config": {
        "max_workers": 8,
        "current_workers": 8
      },
      "degraded_config": {
        "max_workers": 6,
        "current_workers": 6
      }
    },
    {
      "component_name": "IntelligentCache",
      "current_level": "light_degradation",
      "is_degraded": true,
      "last_change_time": 1672531200.0,
      "degradation_reason": "Memory usage threshold exceeded",
      "enabled": true,
      "original_config": {
        "max_size": 1000,
        "ttl_seconds": 3600
      },
      "degraded_config": {
        "max_size": 750,
        "ttl_seconds": 3600
      }
    }
  ],
  "request_id": "req_123456789"
}
```

### 3. 获取降级指标

**端点**: `GET /api/v1/degradation/metrics`

**描述**: 获取降级机制的统计指标

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "降级指标获取成功",
  "data": {
    "total_degradations": 15,
    "successful_degradations": 14,
    "failed_degradations": 1,
    "total_recoveries": 12,
    "successful_recoveries": 12,
    "failed_recoveries": 0,
    "total_actions_executed": 45,
    "successful_actions": 43,
    "failed_actions": 2,
    "average_degradation_duration": 245.6,
    "current_uptime": 86400.0,
    "last_degradation_time": 1672531200.0,
    "degradation_success_rate": 0.933,
    "recovery_success_rate": 1.0
  },
  "request_id": "req_123456789"
}
```

### 4. 获取降级事件

**端点**: `GET /api/v1/degradation/events`

**参数**:
- `limit` (可选): 返回记录数量限制，默认50，最大500
- `event_types` (可选): 事件类型过滤，逗号分隔
- `start_time` (可选): 开始时间戳
- `end_time` (可选): 结束时间戳

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "降级事件获取成功",
  "data": [
    {
      "event_type": "degradation_triggered",
      "timestamp": 1672531200.0,
      "level": "light_degradation",
      "trigger_type": "cpu_usage",
      "trigger_value": 85.5,
      "metadata": {
        "threshold": 80.0,
        "duration": 30
      },
      "actions_count": 3
    },
    {
      "event_type": "degradation_recovered",
      "timestamp": 1672531500.0,
      "level": "normal",
      "trigger_type": "manual",
      "trigger_value": null,
      "metadata": {
        "reason": "Manual recovery"
      },
      "actions_count": 2
    }
  ],
  "request_id": "req_123456789"
}
```

### 5. 手动触发降级

**端点**: `POST /api/v1/degradation/trigger`

**认证**: 需要API密钥

**请求体**:
```json
{
  "level": "light_degradation",
  "reason": "预防性降级维护",
  "force": false
}
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "手动降级执行成功",
  "data": {
    "success": true,
    "current_level": "light_degradation",
    "previous_level": "normal",
    "reason": "预防性降级维护",
    "timestamp": 1672531200.0,
    "executed_actions": 3
  },
  "request_id": "req_123456789"
}
```

### 6. 手动恢复

**端点**: `POST /api/v1/degradation/recover`

**认证**: 需要API密钥

**参数**:
- `reason` (可选): 恢复原因，默认为"Manual recovery from management API"

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "手动恢复执行成功",
  "data": {
    "success": true,
    "current_level": "normal",
    "previous_level": "light_degradation",
    "reason": "维护完成，恢复正常",
    "timestamp": 1672531500.0,
    "recovery_duration": 300.0
  },
  "request_id": "req_123456789"
}
```

### 7. 设置降级级别

**端点**: `POST /api/v1/degradation/level`

**认证**: 需要API密钥

**请求体**:
```json
{
  "level": "moderate_degradation",
  "reason": "系统维护期间降级",
  "duration": 3600
}
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "降级级别设置成功",
  "data": {
    "success": true,
    "current_level": "moderate_degradation",
    "previous_level": "normal",
    "reason": "系统维护期间降级",
    "timestamp": 1672531200.0,
    "duration": 3600,
    "auto_recovery_scheduled": true,
    "recovery_time": 1672534800.0
  },
  "request_id": "req_123456789"
}
```

## 错误响应

所有API在发生错误时都会返回统一的错误响应格式：

```json
{
  "success": false,
  "code": 400,
  "message": "无效的降级级别: invalid_level",
  "data": null,
  "request_id": "req_123456789"
}
```

常见错误码：
- `400`: 请求参数无效
- `401`: 未授权访问（管理API需要API密钥）
- `500`: 内部服务器错误

## 使用示例

### Python示例

```python
import requests

# 获取降级状态
response = requests.get('http://localhost:8000/api/v1/degradation/status')
status = response.json()
print(f"当前降级级别: {status['data']['current_level']}")

# 手动触发降级（需要API密钥）
headers = {'Authorization': 'Bearer your_api_key'}
data = {
    'level': 'light_degradation',
    'reason': '预防性降级',
    'force': False
}
response = requests.post(
    'http://localhost:8000/api/v1/degradation/trigger',
    json=data,
    headers=headers
)
result = response.json()
print(f"降级操作结果: {result['message']}")
```

### curl示例

```bash
# 获取降级状态
curl -X GET "http://localhost:8000/api/v1/degradation/status"

# 获取组件状态
curl -X GET "http://localhost:8000/api/v1/degradation/components"

# 手动触发降级
curl -X POST "http://localhost:8000/api/v1/degradation/trigger" \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "level": "light_degradation",
    "reason": "预防性降级",
    "force": false
  }'

# 手动恢复
curl -X POST "http://localhost:8000/api/v1/degradation/recover?reason=维护完成" \
  -H "Authorization: Bearer your_api_key"
```

## 权限说明

- **监控API**: 无需认证，可以公开访问，用于状态查询和监控
- **管理API**: 需要API密钥认证，用于执行控制操作

API密钥通过HTTP头部传递：
```
Authorization: Bearer your_api_key
```

## 注意事项

1. 管理API的操作会影响系统性能，请谨慎使用
2. 建议在生产环境中限制管理API的访问权限
3. 所有操作都会记录在事件日志中，可通过事件API查询
4. 降级操作是可逆的，可以随时恢复到正常状态
5. 建议在执行手动降级前先查询当前状态，避免重复操作
