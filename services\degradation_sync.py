"""
降级状态同步服务
负责slave节点与master节点之间的降级状态同步，支持版本管理和网络异常处理
"""

import asyncio
import time
from dataclasses import dataclass
from typing import Any

from config.settings import get_settings
from core.degradation_core import DegradationLevel
from core.degradation_manager import get_degradation_manager
from core.http_retry import CircuitBreakerOpenError, RetryClient, RetryExhaustedError
from core.logging.logging_system import log as logger


@dataclass
class SyncState:
    """同步状态"""

    last_sync_time: float = 0.0
    last_successful_sync: float = 0.0
    current_version: str | None = None
    sync_failures: int = 0
    is_syncing: bool = False
    network_partition: bool = False
    cached_state: dict[str, Any] | None = None

    def update_success(self, version: str):
        """更新成功同步状态"""
        self.last_sync_time = time.perf_counter()
        self.last_successful_sync = time.perf_counter()
        self.current_version = version
        self.sync_failures = 0
        self.network_partition = False

    def update_failure(self):
        """更新失败同步状态"""
        self.last_sync_time = time.perf_counter()
        self.sync_failures += 1

        # 连续失败超过阈值，认为网络分区
        if self.sync_failures >= 3:
            self.network_partition = True

    def is_stale(self, max_age_seconds: float = 300.0) -> bool:
        """检查状态是否过期"""
        if self.last_successful_sync == 0:
            return True
        return time.perf_counter() - self.last_successful_sync > max_age_seconds


class DegradationSyncService:
    """降级状态同步服务"""

    def __init__(self):
        self.settings = get_settings()
        self.sync_state = SyncState()
        self._sync_task: asyncio.Task | None = None
        self._is_running = False
        self._retry_client: RetryClient | None = None

        # 同步配置
        self.sync_interval = getattr(self.settings, "DEGRADATION_SYNC_INTERVAL", 30.0)
        self.max_sync_failures = 5
        self.network_partition_threshold = 3
        self.state_cache_ttl = 600.0  # 10分钟

        logger.info("DegradationSyncService initialized")

    async def start(self):
        """启动同步服务"""
        if self._is_running:
            logger.warning("DegradationSyncService is already running")
            return

        # 检查是否为slave模式
        if self.settings.MODE != "slave":
            logger.info("Not in slave mode, degradation sync service disabled")
            return

        # 检查master端点配置
        if not self.settings.MASTER_API_ENDPOINT:
            logger.error("MASTER_API_ENDPOINT not configured, cannot start sync service")
            return

        self._is_running = True

        # 初始化HTTP重试客户端
        self._retry_client = RetryClient(
            base_url=self.settings.MASTER_API_ENDPOINT,
            headers={
                "Authorization": f"Bearer {self.settings.SLAVE_API_KEY}",
                "Content-Type": "application/json"
            },
            enable_circuit_breaker=True,
            circuit_breaker_name="degradation_sync",
        )

        # 启动同步任务
        self._sync_task = asyncio.create_task(self._sync_loop())

        logger.info("DegradationSyncService started")

    async def stop(self):
        """停止同步服务"""
        if not self._is_running:
            return

        self._is_running = False

        # 停止同步任务
        if self._sync_task:
            self._sync_task.cancel()
            try:
                await self._sync_task
            except asyncio.CancelledError:
                pass

        # 关闭HTTP客户端
        if self._retry_client:
            await self._retry_client.close()

        logger.info("DegradationSyncService stopped")

    async def _sync_loop(self):
        """同步循环"""
        while self._is_running:
            try:
                await self._perform_sync()
                await asyncio.sleep(self.sync_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Sync loop error: {e}", exc_info=True)
                await asyncio.sleep(min(self.sync_interval, 60.0))

    async def _perform_sync(self):
        """执行同步操作"""
        if self.sync_state.is_syncing:
            logger.debug("Sync already in progress, skipping")
            return

        self.sync_state.is_syncing = True

        try:
            # 首先检查版本
            version_changed = await self._check_version()

            if version_changed:
                # 版本有变化，执行完整同步
                await self._full_sync()
            else:
                logger.debug("No version change, skipping full sync")
                self.sync_state.update_success(self.sync_state.current_version or "unknown")

        except (RetryExhaustedError, CircuitBreakerOpenError) as e:
            logger.warning(f"Sync failed due to network issues: {e}")
            self.sync_state.update_failure()

            # 网络分区时使用缓存状态
            if self.sync_state.network_partition:
                await self._handle_network_partition()

        except Exception as e:
            logger.error(f"Sync failed with unexpected error: {e}", exc_info=True)
            self.sync_state.update_failure()

        finally:
            self.sync_state.is_syncing = False

    async def _check_version(self) -> bool:
        """检查版本是否有变化"""
        try:
            response = await self._retry_client.get("/api/v1/degradation/version")
            response.raise_for_status()

            data = response.json()
            if data.get("success") and data.get("data"):
                remote_version = data["data"]["version"]

                if remote_version != self.sync_state.current_version:
                    logger.info(f"Version changed: {self.sync_state.current_version} -> {remote_version}")
                    return True

            return False

        except Exception as e:
            logger.error(f"Failed to check version: {e}")
            raise

    async def _full_sync(self):
        """执行完整同步"""
        try:
            # 构建同步请求参数
            params = {
                "version": self.sync_state.current_version,
                "include_metrics": True,
                "include_components": True
            }

            response = await self._retry_client.get("/api/v1/degradation/sync", params=params)
            response.raise_for_status()

            data = response.json()
            if not data.get("success"):
                raise Exception(f"Sync API returned error: {data.get('message')}")

            sync_data = data["data"]

            # 更新本地状态
            await self._apply_sync_data(sync_data)

            # 缓存同步数据
            self.sync_state.cached_state = sync_data

            # 更新同步状态
            version_info = sync_data.get("version", {})
            new_version = version_info.get("version")
            if new_version:
                self.sync_state.update_success(new_version)

            logger.info(f"Full sync completed successfully, version: {new_version}")

        except Exception as e:
            logger.error(f"Full sync failed: {e}")
            raise

    async def _apply_sync_data(self, sync_data: dict[str, Any]):
        """应用同步数据到本地"""
        try:
            state_info = sync_data.get("state", {})

            # 获取本地降级管理器
            degradation_manager = get_degradation_manager()

            # 检查是否需要更新本地状态
            current_status = degradation_manager.get_current_status()
            remote_level = state_info.get("current_level")

            if remote_level and remote_level != current_status["current_level"]:
                logger.info(f"Applying remote degradation level: {remote_level}")

                # 解析降级级别
                try:
                    target_level = DegradationLevel(remote_level)

                    # 应用远程状态
                    success = degradation_manager.manual_trigger_degradation(
                        target_level,
                        f"Synced from master: {state_info.get('override_reason', 'Remote sync')}"
                    )

                    if success:
                        logger.info(f"Successfully applied remote degradation level: {remote_level}")
                    else:
                        logger.warning(f"Failed to apply remote degradation level: {remote_level}")

                except ValueError as e:
                    logger.error(f"Invalid remote degradation level: {remote_level}, error: {e}")

            # 应用组件状态（如果有）
            components_status = sync_data.get("components_status", {})
            if components_status:
                await self._apply_components_status(components_status)

        except Exception as e:
            logger.error(f"Failed to apply sync data: {e}", exc_info=True)
            raise

    async def _apply_components_status(self, components_status: dict[str, Any]):
        """应用组件状态"""
        try:
            from core.degradation_adapters import get_adapter_manager

            adapter_manager = get_adapter_manager()

            for component_name, remote_status in components_status.items():
                adapter = adapter_manager.get_adapter(component_name)
                if adapter:
                    # 检查组件状态是否需要同步
                    local_status = adapter.get_current_status()
                    remote_level = remote_status.get("current_level")
                    local_level = local_status.get("current_level")

                    if remote_level and remote_level != local_level:
                        logger.info(f"Syncing component {component_name}: {local_level} -> {remote_level}")

                        # 这里可以添加具体的组件状态同步逻辑
                        # 目前通过降级管理器的统一事件机制来处理

        except Exception as e:
            logger.error(f"Failed to apply components status: {e}", exc_info=True)

    async def _handle_network_partition(self):
        """处理网络分区情况"""
        logger.warning("Network partition detected, using cached state")

        # 检查缓存状态是否过期
        if self.sync_state.is_stale(self.state_cache_ttl):
            logger.warning("Cached state is stale, may need manual intervention")

            # 可以在这里实现降级到安全状态的逻辑
            # 例如：自动降级到L1级别以确保系统稳定
            degradation_manager = get_degradation_manager()
            current_status = degradation_manager.get_current_status()

            if current_status["current_level"] == "normal":
                logger.info("Network partition: auto-degrading to L1 for safety")
                degradation_manager.manual_trigger_degradation(
                    DegradationLevel.L1_LIGHT,
                    "Auto-degradation due to network partition"
                )

    def get_sync_status(self) -> dict[str, Any]:
        """获取同步状态"""
        return {
            "is_running": self._is_running,
            "last_sync_time": self.sync_state.last_sync_time,
            "last_successful_sync": self.sync_state.last_successful_sync,
            "current_version": self.sync_state.current_version,
            "sync_failures": self.sync_state.sync_failures,
            "is_syncing": self.sync_state.is_syncing,
            "network_partition": self.sync_state.network_partition,
            "is_stale": self.sync_state.is_stale(),
            "sync_interval": self.sync_interval,
        }

    async def force_sync(self) -> bool:
        """强制执行同步"""
        if not self._is_running:
            logger.warning("Sync service is not running")
            return False

        try:
            await self._perform_sync()
            return True
        except Exception as e:
            logger.error(f"Force sync failed: {e}", exc_info=True)
            return False


# 全局同步服务实例
_sync_service: DegradationSyncService | None = None


def get_degradation_sync_service() -> DegradationSyncService:
    """获取全局降级同步服务实例"""
    global _sync_service

    if _sync_service is None:
        _sync_service = DegradationSyncService()

    return _sync_service


async def start_degradation_sync():
    """启动降级同步服务的便捷函数"""
    service = get_degradation_sync_service()
    await service.start()


async def stop_degradation_sync():
    """停止降级同步服务的便捷函数"""
    global _sync_service

    if _sync_service:
        await _sync_service.stop()
