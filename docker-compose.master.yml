# ===== 主节点生产环境 Docker Compose 配置 =====
# 包含主节点后端服务和前端服务

version: '3.8'

services:
  # ===== 主节点后端服务 =====
  sub-rule-master:
    image: registry.yadingdata.com/library/sub-rule-master:test_1.0.0_V2
    container_name: sub-rule-master
    restart: unless-stopped
    network_mode: host
    volumes:
      # 持久化存储（生产环境路径）
      - /home/<USER>/services/sub-rule-master/data:/app/data
      - /home/<USER>/services/sub-rule-master/logs:/app/logs
      - ./master.env:/app/.env:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ===== 前端服务 =====
  sub-rule-frontend:
    image: registry.yadingdata.com/library/sub-rule-frontend:test_1.0.0_V3
    container_name: sub-rule-frontend
    restart: unless-stopped
    environment:
      # API后端地址配置（host网络模式下使用localhost）
      - API_BACKEND_HOST=localhost
      - API_BACKEND_PORT=18001
      - VITE_BASE_URL=/
      - VITE_API_URL=http://localhost:18001
      - VITE_API_KEY=a_very_secret_key_for_test
    network_mode: host
    depends_on:
      - sub-rule-master
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18099/health"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 10s
