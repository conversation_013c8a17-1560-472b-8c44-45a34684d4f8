# TypeScript类型生成工具使用指南

## 📋 概述

本工具基于`data/field_mapping.json`配置文件自动生成前端TypeScript类型定义，确保前后端字段命名的一致性。

## 🚀 快速开始

### 生成类型定义

```bash
# 手动生成类型
npm run generate:types

# 开发时自动生成
npm run dev

# 构建时自动生成
npm run build
```

### 使用生成的类型

```typescript
import type { 
  RuleDetail, 
  CommonFields, 
  SpecificFields,
  FIELD_CHINESE_NAMES,
  getFieldChineseName 
} from '@/types/generated-fields'

// 使用接口类型
const rule: RuleDetail = {
  rule_id: 'R001',
  rule_name: '测试规则',
  level1: '错误类型1',
  // ...
}

// 使用工具函数
const chineseName = getFieldChineseName('rule_name') // "规则名称"
```

## 📁 生成的文件结构

```
frontend/src/types/
├── generated-fields.ts     # 自动生成的类型文件
└── ruleDetails.ts          # 业务类型文件（基于生成类型）
```

## 🔧 核心功能

### 1. 接口类型

- **CommonFields**: 通用字段接口（25个字段）
- **SpecificFields**: 特定字段接口（5个字段）
- **RuleDetail**: 完整规则详情接口

### 2. 映射常量

- **FIELD_CHINESE_NAMES**: 字段中文名称映射
- **FIELD_DATA_TYPES**: 字段数据类型映射
- **FIELD_VALIDATION_RULES**: 字段验证规则映射

### 3. 工具函数

- **getFieldChineseName(fieldName)**: 获取字段中文名称
- **getFieldDataType(fieldName)**: 获取字段数据类型
- **getFieldValidationRules(fieldName)**: 获取字段验证规则
- **isFieldRequired(fieldName)**: 检查字段是否必填

## 📝 配置文件说明

类型生成基于`data/field_mapping.json`配置文件：

```json
{
  "field_definitions": {
    "common_fields": {
      "rule_name": {
        "chinese_name": "规则名称",
        "data_type": "string",
        "required": true,
        "validation_rules": ["required", "max_length:500"]
      }
    },
    "specific_fields": {
      "age_threshold": {
        "chinese_name": "年龄阈值",
        "data_type": "integer",
        "required": false
      }
    }
  }
}
```

## 🧪 测试

```bash
# 运行类型生成器测试
npm test tests/unit/generate-field-types.test.js

# 运行生成类型功能测试
npm test tests/unit/generated-fields.test.js
```

## ⚠️ 注意事项

1. **自动生成文件**: `generated-fields.ts`是自动生成的，请勿手动修改
2. **配置同步**: 修改`field_mapping.json`后需要重新生成类型
3. **构建集成**: 开发和构建时会自动生成最新类型
4. **类型安全**: 使用生成的类型确保前后端字段一致性

## 🔄 更新流程

1. 修改`data/field_mapping.json`配置
2. 运行`npm run generate:types`
3. 检查生成的类型文件
4. 更新相关业务代码
5. 运行测试验证

## 🐛 故障排除

### 常见问题

**Q: 类型生成失败**
A: 检查`data/field_mapping.json`格式是否正确

**Q: 构建时类型错误**
A: 确保已运行`npm run generate:types`更新类型

**Q: 字段映射不正确**
A: 检查配置文件中的字段定义是否完整

### 调试方法

```bash
# 查看详细生成日志
npm run generate:types

# 检查生成的文件
cat src/types/generated-fields.ts
```

## 📚 相关文档

- [字段映射管理规范](../backend/规则详情表-字段映射管理规范.md)
- [规则详情表重构实施文档](../../project/design/规则详情表重构实施文档.md)

---

**维护者**: 规则验证系统开发团队  
**最后更新**: 2025-07-24  
**版本**: v1.0
