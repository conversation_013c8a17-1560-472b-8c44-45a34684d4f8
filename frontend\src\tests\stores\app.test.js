/**
 * App Store 单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useAppStore } from '@/stores/app'

describe('App Store 测试', () => {
  let store

  beforeEach(() => {
    // 创建新的 Pinia 实例
    setActivePinia(createPinia())
    store = useAppStore()
    vi.clearAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(store.globalLoading).toBe(false)
      expect(store.loadingMessage).toBe('加载中...')
      expect(store.loadingProgress).toBe(0)
      expect(store.currentError).toBe(null)
      expect(store.errorHistory).toEqual([])
      expect(store.errorCount).toBe(0)
      expect(store.notifications).toEqual([])
      expect(store.toasts).toEqual([])
      expect(store.isAdmin).toBe(false) // 默认用户角色，不是管理员
    })

    it('应该正确初始化加载任务集合', () => {
      expect(store.loadingTasks).toBeInstanceOf(Set)
      expect(store.loadingTasks.size).toBe(0)
    })
  })

  describe('加载状态管理', () => {
    it('应该正确设置全局加载状态', () => {
      store.addLoadingTask('task1', '正在加载数据...')

      expect(store.globalLoading).toBe(true)
      expect(store.loadingMessage).toBe('正在加载数据...')
    })

    it('应该正确清除加载状态', () => {
      store.addLoadingTask('task1', '加载中...')
      store.removeLoadingTask('task1')

      expect(store.globalLoading).toBe(false)
      expect(store.loadingMessage).toBe('加载中...')
    })

    it('应该正确管理加载任务', () => {
      const taskId1 = 'task1'
      const taskId2 = 'task2'

      // 添加任务
      store.addLoadingTask(taskId1)
      store.addLoadingTask(taskId2)

      expect(store.loadingTasks.has(taskId1)).toBe(true)
      expect(store.loadingTasks.has(taskId2)).toBe(true)
      expect(store.hasLoadingTasks).toBe(true)

      // 移除任务
      store.removeLoadingTask(taskId1)
      expect(store.loadingTasks.has(taskId1)).toBe(false)
      expect(store.loadingTasks.has(taskId2)).toBe(true)

      store.removeLoadingTask(taskId2)
      expect(store.hasLoadingTasks).toBe(false)
    })

    it('应该正确设置加载进度', () => {
      store.updateLoadingProgress(50)
      expect(store.loadingProgress).toBe(50)

      store.updateLoadingProgress(100)
      expect(store.loadingProgress).toBe(100)

      // 测试边界值
      store.updateLoadingProgress(-10)
      expect(store.loadingProgress).toBe(0)

      store.updateLoadingProgress(150)
      expect(store.loadingProgress).toBe(100)
    })
  })

  describe('错误状态管理', () => {
    it('应该正确设置错误', () => {
      const errorMessage = '网络连接失败'

      store.setError(errorMessage)

      expect(store.currentError).toBe(errorMessage)
      expect(store.errorCount).toBe(1)
      expect(store.lastErrorTime).toBeTruthy()
      expect(store.errorHistory).toHaveLength(1)
      expect(store.errorHistory[0]).toMatchObject({
        error: errorMessage,
        timestamp: expect.any(Number)
      })
    })

    it('应该正确清除错误', () => {
      store.setError('测试错误')
      store.clearError()

      expect(store.currentError).toBe(null)
      // lastErrorTime 不会被清除，只有 clearErrorHistory 才会清除
    })

    it('应该正确管理错误历史', () => {
      store.setError('错误1')
      store.setError('错误2')
      store.setError('错误3')

      expect(store.errorHistory).toHaveLength(3)
      expect(store.errorCount).toBe(3)

      // 清除错误历史
      store.clearErrorHistory()
      expect(store.errorHistory).toHaveLength(0)
      expect(store.errorCount).toBe(0)
    })

    it('应该限制错误历史记录数量', () => {
      // 添加超过限制的错误记录
      for (let i = 0; i < 15; i++) {
        store.setError(`错误${i}`)
      }

      // 实际实现中限制是1000条，这里测试15条都会保留
      expect(store.errorHistory).toHaveLength(15)
      expect(store.errorHistory[0].error).toBe('错误0')
      expect(store.errorHistory[14].error).toBe('错误14')
    })
  })

  describe('通知系统', () => {
    it('应该正确添加通知', () => {
      const notification = {
        type: 'success',
        title: '操作成功',
        message: '数据保存成功'
      }

      const id = store.addNotification(notification)

      expect(store.notifications).toHaveLength(1)
      expect(store.notifications[0]).toMatchObject({
        id,
        ...notification,
        timestamp: expect.any(Number)
      })
    })

    it('应该正确移除通知', () => {
      const id1 = store.addNotification({ type: 'info', message: '通知1' })
      const id2 = store.addNotification({ type: 'warning', message: '通知2' })

      expect(store.notifications).toHaveLength(2)

      store.removeNotification(id1)
      expect(store.notifications).toHaveLength(1)
      expect(store.notifications[0].id).toBe(id2)
    })

    it('应该正确清除所有通知', () => {
      store.addNotification({ type: 'info', message: '通知1' })
      store.addNotification({ type: 'warning', message: '通知2' })

      store.clearNotifications()
      expect(store.notifications).toHaveLength(0)
    })

    it('应该正确添加Toast消息', () => {
      const toast = {
        type: 'success',
        message: '操作成功'
      }

      const id = store.addToast(toast)

      expect(store.toasts).toHaveLength(1)
      expect(store.toasts[0]).toMatchObject({
        id,
        ...toast,
        timestamp: expect.any(Number)
      })
    })

    it('应该自动移除过期的Toast', async () => {
      const toast = {
        type: 'info',
        message: '临时消息',
        duration: 100 // 100ms后自动移除
      }

      store.addToast(toast)
      expect(store.toasts).toHaveLength(1)

      // 等待自动移除
      await new Promise(resolve => setTimeout(resolve, 150))
      expect(store.toasts).toHaveLength(0)
    })
  })

  describe('用户权限管理', () => {
    it('应该正确设置用户权限', () => {
      store.setUserRoles(['admin'])
      expect(store.isAdmin).toBe(true)

      store.setUserRoles(['user'])
      expect(store.isAdmin).toBe(false)
    })

    it('应该正确检查权限', () => {
      store.setUserRoles(['admin'])
      store.setUserPermissions(['admin', 'user'])
      expect(store.hasPermission('admin')).toBe(true)
      expect(store.hasPermission('user')).toBe(true)

      store.setUserRoles(['user'])
      store.setUserPermissions(['user'])
      expect(store.hasPermission('admin')).toBe(false)
      expect(store.hasPermission('user')).toBe(true)
    })
  })

  describe('便捷方法', () => {
    it('应该提供成功消息便捷方法', () => {
      store.addToast({ type: 'success', message: '操作成功' })

      expect(store.toasts).toHaveLength(1)
      expect(store.toasts[0].type).toBe('success')
      expect(store.toasts[0].message).toBe('操作成功')
    })

    it('应该提供错误消息便捷方法', () => {
      store.setError('操作失败')
      store.addToast({ type: 'error', message: '操作失败' })

      expect(store.currentError).toBe('操作失败')
      expect(store.toasts).toHaveLength(1)
      expect(store.toasts[0].type).toBe('error')
    })

    it('应该提供警告消息便捷方法', () => {
      store.addToast({ type: 'warning', message: '注意事项' })

      expect(store.toasts).toHaveLength(1)
      expect(store.toasts[0].type).toBe('warning')
      expect(store.toasts[0].message).toBe('注意事项')
    })

    it('应该提供信息消息便捷方法', () => {
      store.addToast({ type: 'info', message: '提示信息' })

      expect(store.toasts).toHaveLength(1)
      expect(store.toasts[0].type).toBe('info')
      expect(store.toasts[0].message).toBe('提示信息')
    })
  })

  describe('计算属性', () => {
    it('应该正确计算是否有加载任务', () => {
      expect(store.hasLoadingTasks).toBe(false)

      store.addLoadingTask('task1')
      expect(store.hasLoadingTasks).toBe(true)

      store.removeLoadingTask('task1')
      expect(store.hasLoadingTasks).toBe(false)
    })

    it('应该正确计算是否有错误', () => {
      expect(store.hasError).toBe(false)

      store.setError('测试错误')
      expect(store.hasError).toBe(true)

      store.clearError()
      expect(store.hasError).toBe(false)
    })

    it('应该正确计算通知数量', () => {
      expect(store.notificationCount).toBe(0)

      store.addNotification({ type: 'info', message: '通知1' })
      store.addNotification({ type: 'warning', message: '通知2' })

      expect(store.notificationCount).toBe(2)
    })
  })

  describe('状态重置', () => {
    it('应该正确重置所有状态', () => {
      // 设置一些状态
      store.addLoadingTask('task1', '加载中...')
      store.setError('测试错误')
      store.addNotification({ type: 'info', message: '通知' })
      store.addToast({ type: 'success', message: 'Toast' })

      // 重置状态
      store.resetStore()

      // 验证状态被重置
      expect(store.globalLoading).toBe(false)
      expect(store.currentError).toBe(null)
      expect(store.notifications).toHaveLength(0)
      expect(store.toasts).toHaveLength(0)
      expect(store.errorHistory).toHaveLength(0)
      expect(store.loadingTasks.size).toBe(0)
    })
  })
})
