"""
增强从节点索引构建器综合集成测试
验证所有新增功能的集成和一致性
"""

import json
import gzip
import os
import tempfile
import time
import unittest
from unittest.mock import Mock, patch, MagicMock

from core.slave_node_index_builder import SlaveNodeIndexBuilder, SlaveIndexPerformanceStats, SlaveIndexErrorHandler


class TestEnhancedSlaveNodeIntegration(unittest.TestCase):
    """增强从节点索引构建器综合集成测试"""

    def setUp(self):
        """设置测试环境"""
        # 不再需要模拟环境变量，使用settings配置

        # 创建临时缓存文件
        self.temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".json.gz", delete=False)
        self.temp_file.close()

        # 模拟rule_index_manager
        self.mock_rule_index_manager = Mock()
        self.mock_rule_index_manager.build_indexes_from_rule_details = Mock()

        # 创建测试数据
        self.test_rules_data = self._create_test_rules_data()
        self._write_test_cache_file()

    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def _create_test_rules_data(self):
        """创建测试规则数据"""
        rules = []
        for i in range(1000):  # 创建1000条测试规则
            rule = {
                "rule_key": f"TEST_RULE_{i:04d}",
                "rule_name": f"测试规则{i}",
                "rule_type": "VALIDATION",
                "yb_code": f"Y{i:03d}",
                "diag_code": f"D{i:03d}",
                "fee_code": f"F{i:03d}",
                "rule_content": f"测试规则内容{i}",
                "is_active": True,
                "priority": i % 10,
                "extended_fields": json.dumps({"test_field": f"value_{i}"}),
            }
            rules.append(rule)
        return rules

    def _write_test_cache_file(self):
        """写入测试缓存文件"""
        cache_data = {
            "rule_details": self.test_rules_data,
            "metadata": {"total_count": len(self.test_rules_data), "generated_time": time.time(), "version": "1.0"},
        }

        with gzip.open(self.temp_file.name, "wt", encoding="utf-8") as f:
            json.dump(cache_data, f, ensure_ascii=False)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_complete_system_integration(self, mock_memory_optimizer_class):
        """测试完整系统集成"""
        # 模拟内存优化器
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        # 创建从节点索引构建器
        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 验证所有组件都已正确初始化
        self.assertIsNotNone(builder.memory_optimizer)
        self.assertIsInstance(builder.performance_stats, SlaveIndexPerformanceStats)
        self.assertIsInstance(builder.error_handler, SlaveIndexErrorHandler)

        # 执行索引构建
        success = builder.build_index_from_cache_file()
        self.assertTrue(success)

        # 验证性能统计已更新
        stats = builder.get_performance_stats()
        self.assertEqual(stats.build_count, 1)
        self.assertEqual(stats.successful_builds, 1)
        self.assertGreater(stats.total_rules_processed, 0)

        # 验证解析统计
        parsing_stats = builder.get_parsing_stats()
        self.assertEqual(parsing_stats.total_rules, 1000)
        self.assertGreater(parsing_stats.parse_time_ms, 0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_memory_optimization_integration(self, mock_memory_optimizer_class):
        """测试内存优化集成"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        # 模拟内存压力情况
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = True
        mock_memory_stats.process_memory_mb = 280.0  # 接近300MB限制
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 执行构建
        success = builder.build_index_from_cache_file()
        self.assertTrue(success)

        # 验证内存优化被调用
        mock_memory_optimizer.optimize_memory.assert_called()

        # 验证性能统计记录了内存优化
        stats = builder.get_performance_stats()
        self.assertGreater(stats.memory_optimization_count, 0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_error_handling_integration(self, mock_memory_optimizer_class):
        """测试错误处理集成"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        mock_memory_stats = Mock()
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        # 模拟索引构建失败
        self.mock_rule_index_manager.build_indexes_from_rule_details.side_effect = Exception("构建失败")

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 执行构建（应该失败）
        success = builder.build_index_from_cache_file()
        self.assertFalse(success)

        # 验证错误处理器记录了错误
        error_stats = builder.get_error_handler_status()
        self.assertGreater(error_stats["total_errors"], 0)
        self.assertGreater(error_stats["error_by_component"]["build"], 0)

        # 验证性能统计记录了失败
        stats = builder.get_performance_stats()
        self.assertEqual(stats.failed_builds, 1)
        self.assertGreater(stats.build_errors, 0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_performance_monitoring_integration(self, mock_memory_optimizer_class):
        """测试性能监控集成"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 执行多次构建
        for i in range(3):
            success = builder.build_index_from_cache_file()
            self.assertTrue(success)

        # 获取性能报告
        report = builder.get_performance_report()

        # 验证报告结构
        self.assertIn("summary", report)
        self.assertIn("build_performance", report)
        self.assertIn("memory_performance", report)
        self.assertIn("parsing_performance", report)

        # 验证统计数据
        self.assertEqual(report["build_performance"]["total_builds"], 3)
        self.assertEqual(report["build_performance"]["successful_builds"], 3)
        self.assertGreater(report["summary"]["build_success_rate"], 99.0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_degradation_recovery_cycle(self, mock_memory_optimizer_class):
        """测试降级和恢复周期"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        mock_memory_stats = Mock()
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 初始状态应该是正常的
        self.assertFalse(builder.is_in_degradation_mode())
        self.assertEqual(builder.get_degradation_level(), 0)

        # 模拟多个错误以触发降级
        for i in range(6):  # 超过错误阈值(5)
            try:
                raise Exception(f"测试错误 {i}")
            except Exception as e:
                builder.error_handler.handle_build_error(e)

        # 验证已进入降级模式
        self.assertTrue(builder.is_in_degradation_mode())
        self.assertGreater(builder.get_degradation_level(), 0)

        # 模拟成功操作以触发恢复
        for i in range(12):  # 超过恢复阈值(10)
            builder.error_handler.record_success()

        # 验证已恢复正常
        self.assertFalse(builder.is_in_degradation_mode())
        self.assertEqual(builder.get_degradation_level(), 0)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_batch_processing_optimization(self, mock_memory_optimizer_class):
        """测试批处理优化"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 测试批次大小计算
        batch_size_small = builder._calculate_optimal_batch_size(100)
        batch_size_medium = builder._calculate_optimal_batch_size(10000)
        batch_size_large = builder._calculate_optimal_batch_size(100000)

        # 验证批次大小随数据量增加
        self.assertLessEqual(batch_size_small, batch_size_medium)
        self.assertLessEqual(batch_size_medium, batch_size_large)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_data_integrity_validation(self, mock_memory_optimizer_class):
        """测试数据完整性验证"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        mock_memory_stats = Mock()
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 测试有效数据
        valid_data = self.test_rules_data
        is_valid, message = builder._validate_data_integrity(valid_data)
        self.assertTrue(is_valid)
        self.assertIn("验证通过", message)

        # 测试无效数据
        invalid_data = [{"invalid": "data"}]
        is_valid, message = builder._validate_data_integrity(invalid_data)
        self.assertFalse(is_valid)
        self.assertIn("缺少必要字段", message)

    def test_lifecycle_management(self):
        """测试生命周期管理"""
        with patch("core.slave_node_index_builder.MemoryOptimizer") as mock_memory_optimizer_class:
            mock_memory_optimizer = Mock()
            mock_memory_optimizer_class.return_value = mock_memory_optimizer

            builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

            # 验证初始化
            self.assertIsNotNone(builder.memory_optimizer)
            mock_memory_optimizer.start_monitoring.assert_called_once()

            # 测试关闭
            builder.shutdown()
            mock_memory_optimizer.stop_monitoring.assert_called_once()

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_comprehensive_feature_integration(self, mock_memory_optimizer_class):
        """综合功能集成测试"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 1. 测试初始状态
        self.assertFalse(builder.is_in_degradation_mode())
        self.assertEqual(builder.get_degradation_level(), 0)

        # 2. 执行索引构建
        success = builder.build_index_from_cache_file()
        self.assertTrue(success)

        # 3. 验证性能统计
        stats = builder.get_performance_stats()
        self.assertEqual(stats.build_count, 1)
        self.assertEqual(stats.successful_builds, 1)

        # 4. 测试性能异常检测
        anomalies = builder.check_performance_anomalies()
        self.assertFalse(anomalies["has_anomalies"])

        # 5. 测试错误处理
        error_stats = builder.get_error_handler_status()
        self.assertEqual(error_stats["total_errors"], 0)

        # 6. 测试手动干预接口
        recent_errors = builder.get_recent_errors(5)
        self.assertEqual(len(recent_errors), 0)

        # 7. 测试生命周期管理
        builder.shutdown()
        mock_memory_optimizer.stop_monitoring.assert_called()

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_hot_reload_integration(self, mock_memory_optimizer_class):
        """热重载集成测试"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 创建热重载器
        from core.slave_node_index_builder import IndexHotReloader

        reloader = IndexHotReloader(self.mock_rule_index_manager, builder)

        # 模拟成功的重载
        builder.build_index_from_cache_file = Mock(return_value=True)

        # 执行热重载
        success = reloader.reload_index()
        self.assertTrue(success)

        # 验证热重载统计
        stats = builder.get_performance_stats()
        self.assertGreater(stats.hot_reload_count, 0)
        self.assertGreater(stats.successful_hot_reloads, 0)


if __name__ == "__main__":
    unittest.main()
