# 数据校验前端实现使用指南

## 📋 概述

本文档介绍规则详情表重构项目中新实现的前端数据校验系统，包括动态校验引擎、实时校验、校验规则同步等核心功能的使用方法。

## 🏗️ 系统架构

### 核心组件

```
┌─────────────────────────────────────────┐
│           Vue组件层                      │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │  表单组件       │ │  数据上传组件    │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│         校验服务层                       │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ DynamicValidator│ │ ValidationSync  │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│         基础工具层                       │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │FieldMappingEngine│ │ErrorHandler    │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
```

### 文件结构

```
frontend/src/
├── utils/validation/
│   ├── validationTypes.ts           # 类型定义
│   ├── dynamicValidationEngine.ts   # 动态校验引擎
│   ├── validationRuleSync.ts        # 校验规则同步
│   └── realTimeValidator.ts         # 实时校验组件
├── composables/validation/
│   ├── useValidation.ts             # 基础校验组合式函数
│   └── useFormValidation.ts         # 表单校验组合式函数
├── components/validation/
│   ├── ValidationMessage.vue        # 校验消息组件
│   └── ValidationStatus.vue         # 校验状态组件
└── examples/
    └── validationUsage.ts           # 使用示例
```

## 🚀 快速开始

### 1. 基础表单校验

```vue
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="120px"
  >
    <!-- 校验状态显示 -->
    <ValidationStatus
      :is-valid="isValid"
      :is-validating="isValidating"
      :error-count="errorCount"
      :field-states="fieldStates"
      :show-details="true"
    />

    <el-form-item label="规则名称" prop="rule_name">
      <el-input
        v-model="formData.rule_name"
        @blur="registerFieldValidation('rule_name', $event.target)"
      />
      <!-- 字段级错误显示 -->
      <div v-if="getFieldErrors('rule_name').length > 0">
        <ValidationMessage
          v-for="error in getFieldErrors('rule_name')"
          :key="error.error_code"
          type="error"
          :message="error.error_message"
          :suggestions="error.suggestions"
        />
      </div>
    </el-form-item>

    <el-form-item>
      <el-button 
        type="primary" 
        :loading="isValidating"
        @click="handleSubmit"
      >
        提交
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import { useFormValidation } from '@/composables/validation/useFormValidation'
import ValidationStatus from '@/components/validation/ValidationStatus.vue'
import ValidationMessage from '@/components/validation/ValidationMessage.vue'

// 表单数据
const formData = reactive({
  rule_name: '',
  level1: '',
  level2: ''
})

// 使用表单校验
const {
  formRules,
  isValidating,
  isValid,
  errorCount,
  fieldStates,
  getFieldErrors,
  registerFieldValidation,
  submitForm,
  resetForm
} = useFormValidation({
  ruleKey: 'drug_limit_adult_and_diag_exact',
  autoSync: true,
  showMessages: true
})

// 提交处理
const handleSubmit = async () => {
  const result = await submitForm(formData, async (validatedData) => {
    // 实际的API调用
    const response = await fetch('/api/v1/rules/details', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(validatedData)
    })
    return await response.json()
  })
  
  if (result.success) {
    console.log('提交成功:', result.data)
  }
}
</script>
```

### 2. 实时校验集成

```typescript
import { useRealTimeValidation } from '@/composables/validation/useRealTimeValidation'

export function useRealTimeValidationExample() {
  const {
    registerFieldValidation,
    validateField,
    clearFieldValidation
  } = useRealTimeValidation({
    ruleKey: 'drug_limit_adult_and_diag_exact',
    realTimeOptions: {
      debounce_delay: 300,
      validate_on_blur: true,
      validate_on_change: true,
      show_success_feedback: false
    }
  })

  // 在组件挂载后注册字段
  onMounted(() => {
    const ruleNameInput = document.querySelector('[name="rule_name"]')
    if (ruleNameInput) {
      registerFieldValidation('rule_name', ruleNameInput)
    }
  })

  return {
    registerFieldValidation,
    validateField,
    clearFieldValidation
  }
}
```

### 3. 批量数据校验

```typescript
import { useBatchValidation } from '@/composables/validation/useBatchValidation'

export function useBatchValidationExample() {
  const {
    isValidating,
    validData,
    invalidData,
    validateBatchData
  } = useBatchValidation()

  const handleBatchValidation = async (dataList: any[]) => {
    await validateBatchData(dataList, 'drug_limit_adult_and_diag_exact')
    
    console.log('有效数据:', validData.value)
    console.log('无效数据:', invalidData.value)
  }

  return {
    isValidating,
    validData,
    invalidData,
    handleBatchValidation
  }
}
```

## 🔧 API 参考

### DynamicValidationEngine

动态校验引擎，基于后端元数据进行前端校验。

```typescript
import { DynamicValidationEngine } from '@/utils/validation/dynamicValidationEngine'

const engine = new DynamicValidationEngine({
  cache_enabled: true,
  cache_ttl_minutes: 30,
  performance_monitoring: true
})

// 校验单个字段
const result = await engine.validateField('rule_name', '测试规则', 'rule_key')

// 校验整个表单
const formResult = await engine.validateForm(formData, 'rule_key')

// 获取性能指标
const metrics = engine.getPerformanceMetrics()
```

### ValidationRuleSync

校验规则同步服务，与后端保持规则一致性。

```typescript
import { validationRuleSync } from '@/utils/validation/validationRuleSync'

// 同步所有规则
await validationRuleSync.syncAllRules()

// 同步单个规则
await validationRuleSync.syncRuleConfig('rule_key')

// 启动自动同步
validationRuleSync.startAutoSync(60) // 60分钟间隔

// 获取规则配置
const config = validationRuleSync.getRuleConfig('rule_key')
```

### useFormValidation

表单校验组合式函数，提供完整的表单校验功能。

```typescript
const {
  // 状态
  isValidating,
  validationErrors,
  hasErrors,
  isValid,
  formRules,
  
  // 方法
  validateForm,
  validateField,
  submitForm,
  resetForm,
  getFieldErrors,
  registerFieldValidation
} = useFormValidation({
  ruleKey: 'rule_key',
  autoSync: true,
  showMessages: true,
  submitOnValid: true
})
```

## 🎨 组件使用

### ValidationMessage

校验消息显示组件。

```vue
<ValidationMessage
  type="error"
  message="规则名称不能为空"
  :suggestions="['请填写规则名称', '名称长度应在1-255个字符之间']"
  :error-details="errorObject"
  :closable="true"
  :show-details="false"
/>
```

### ValidationStatus

校验状态显示组件。

```vue
<ValidationStatus
  :is-valid="true"
  :is-validating="false"
  :error-count="0"
  :warning-count="1"
  :field-states="fieldStates"
  :field-errors="fieldErrors"
  :show-details="true"
  :show-performance="false"
/>
```

## ⚙️ 配置选项

### ValidationEngineOptions

```typescript
interface ValidationEngineOptions {
  cache_enabled: boolean           // 是否启用缓存
  cache_ttl_minutes: number       // 缓存过期时间（分钟）
  real_time_validation: {
    debounce_delay: number         // 防抖延迟（毫秒）
    validate_on_blur: boolean      // 失焦时校验
    validate_on_change: boolean    // 输入时校验
    show_success_feedback: boolean // 显示成功反馈
    auto_focus_error: boolean      // 自动聚焦错误字段
  }
  sync_interval_minutes: number   // 同步间隔（分钟）
  max_batch_size: number         // 最大批量处理大小
  performance_monitoring: boolean // 性能监控
}
```

## 🧪 测试

### 单元测试示例

```typescript
import { describe, it, expect } from 'vitest'
import { DynamicValidationEngine } from '@/utils/validation/dynamicValidationEngine'

describe('DynamicValidationEngine', () => {
  it('应该通过必填字段校验', async () => {
    const engine = new DynamicValidationEngine()
    const result = await engine.validateField('rule_name', '测试规则', 'test_rule')
    
    expect(result.valid).toBe(true)
    expect(result.errors).toHaveLength(0)
  })

  it('应该检测必填字段为空', async () => {
    const engine = new DynamicValidationEngine()
    const result = await engine.validateField('rule_name', '', 'test_rule')
    
    expect(result.valid).toBe(false)
    expect(result.errors[0].error_code).toBe('REQUIRED_FIELD_MISSING')
  })
})
```

## 🔍 故障排除

### 常见问题

1. **校验规则未生效**
   - 检查 `ruleKey` 是否正确
   - 确认校验规则已同步：`validationRuleSync.syncRuleConfig(ruleKey)`

2. **实时校验不工作**
   - 确认字段已注册：`registerFieldValidation(fieldName, element)`
   - 检查元素选择器是否正确

3. **性能问题**
   - 启用缓存：`cache_enabled: true`
   - 调整防抖延迟：`debounce_delay: 500`
   - 减少批量处理大小：`max_batch_size: 50`

### 调试技巧

```typescript
// 启用详细日志
const engine = new DynamicValidationEngine({
  performance_monitoring: true
})

// 查看性能指标
console.log(engine.getPerformanceMetrics())

// 监听校验事件
engine.addEventListener('field_validated', (event) => {
  console.log('字段校验完成:', event)
})
```

## 📚 更多资源

- [后端校验规则配置](../backend/规则详情表-字段映射管理规范.md)
- [API接口文档](../../api/规则详情表API文档v2.0.md)
- [项目架构设计](../../project/design/规则详情表重构实施文档.md)

---

**文档维护者**：规则验证系统开发团队  
**最后更新**：2025-07-27  
**版本**：v1.0.0
