# ===== 从节点后端镜像 (Slave Node) =====
# 基于base-runtime构建，专注于高性能规则验证
# 支持动态规则加载、性能优化、规则同步等功能

# ===== 基于基础运行环境 =====
FROM registry.yadingdata.com/library/sub-rule-base:test_1.0.0_V1 AS slave-node

# 设置从节点特定环境变量
ENV MODE=slave \
    SERVER_PORT=18001

# 切换到root用户进行系统配置
USER root

# 设置工作目录
WORKDIR /app

# 复制从节点应用代码（按依赖顺序，优化缓存）
# 1. 配置文件（变化较少）
COPY config/ ./config/

# 2. 核心模块（包含性能优化组件）
COPY core/ ./core/
COPY models/ ./models/
COPY utils/ ./utils/

# 3. 服务层（从节点专用服务）
COPY services/ ./services/

# 4. API层（仅包含验证相关路由）
COPY api/ ./api/

# 5. 规则相关（动态加载支持）
COPY rules/ ./rules/

# 6. 从节点主应用入口
COPY slave.py ./

# 7. 其他必要文件
COPY __init__.py ./

# 创建从节点专用目录
RUN mkdir -p \
    logs/slave \
    rules_cache/slave \
    data/performance \
    data/sync \
    tmp/validation


# 创建从节点启动脚本
COPY start-slave.sh /app/start-slave.sh
RUN chmod +x /app/start-slave.sh

# 创建性能监控脚本
COPY performance-status.sh /app/performance-status.sh
RUN chmod +x /app/performance-status.sh

# 健康检查配置（继承基础镜像的脚本）
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD /app/healthcheck.sh || exit 1

# 暴露端口
EXPOSE 18001

# 设置标签
LABEL component="slave-node" \
      service.type="backend" \
      service.role="slave" \
      features="high-performance-validation,dynamic-loading,sync,optimization"

# 启动命令
CMD ["bash", "/app/start-slave.sh"]
