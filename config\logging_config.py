# Logging Configuration
from .settings import settings

# Determine the log level based on the run mode
level = "DEBUG" if settings.RUN_MODE == "DEV" else settings.LOG_LEVEL

LOGGING_CONFIG = {
    "path": settings.LOG_PATH,  # Path for log files, {time} will be replaced by Loguru
    "format": settings.LOG_FORMAT,  # Log message format
    "rotation": settings.LOG_ROTATION,  # Rotate log file when it reaches 100 MB
    "retention": settings.LOG_RETENTION,  # Keep log files for 30 days
    "compression": settings.LOG_COMPRESSION,  # Compress rotated log files using gzip
    "enqueue": settings.LOG_ENQUEUE,  # Enable asynchronous logging (thread-safe)
    "level": level,  # Default logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    # Configuration for the stdout sink
    "stdout_sink": {
        "enabled": settings.LOG_STDOUT_SINK_ENABLED,
        "level": level,  # Can be different from the file log level
        "format": settings.LOG_FORMAT,  # Simpler format for console
    },
    # Add other Loguru specific configurations here if needed.
    # For example, to add a specific serializer for custom objects:
    # "serialize": False, # Set to True if you want to log extra data as J<PERSON><PERSON>
}
