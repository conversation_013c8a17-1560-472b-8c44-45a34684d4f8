"""
pytest全局配置和共享fixtures
提供测试所需的通用fixtures和配置
"""

import asyncio
import os
import tempfile
from unittest.mock import Mock

import pytest
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from models.database import Base

# ============================================================================
# 数据库相关fixtures
# ============================================================================

@pytest.fixture(scope="session")
def test_database_url() -> str:
    """测试数据库URL"""
    return "sqlite:///:memory:"


@pytest.fixture(scope="session")
def test_engine(test_database_url):
    """测试数据库引擎"""
    engine = create_engine(
        test_database_url,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False,  # 设置为True可以看到SQL语句
    )

    # 启用SQLite外键约束
    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()

    # 创建所有表
    Base.metadata.create_all(engine)

    yield engine

    # 清理
    Base.metadata.drop_all(engine)
    engine.dispose()


@pytest.fixture(scope="function")
def test_db_session(test_engine):
    """测试数据库会话"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)  # noqa: N806
    session = TestingSessionLocal()

    try:
        yield session
    finally:
        session.rollback()
        session.close()


# ============================================================================
# 异步相关fixtures
# ============================================================================

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# ============================================================================
# Mock相关fixtures
# ============================================================================

@pytest.fixture
def mock_logger():
    """模拟日志记录器"""
    return Mock()


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    return Mock()


# ============================================================================
# 测试数据工厂
# ============================================================================

class TestDataFactory:
    """测试数据工厂类"""

    @staticmethod
    def create_base_rule_data(**kwargs):
        """创建基础规则测试数据"""
        default_data = {
            "rule_key": "test_rule_001",
            "rule_name": "测试规则001",
            "description": "测试规则描述",
            "module_path": "test.module.path",
            "file_hash": "test_hash_123",
            "status": "READY",
        }
        default_data.update(kwargs)
        return default_data

    @staticmethod
    def create_rule_dataset_data(**kwargs):
        """创建规则数据集测试数据"""
        default_data = {
            "base_rule_id": 1,
            "version": 1,
            "is_active": True,
            "uploaded_by": "test_user",
            "migration_status": "PENDING",
        }
        default_data.update(kwargs)
        return default_data

    @staticmethod
    def create_rule_detail_data(**kwargs):
        """创建规则明细测试数据"""
        default_data = {
            "dataset_id": 1,
            "rule_detail_id": "test_detail_001",
            "rule_name": "测试明细规则001",
            "error_level_1": "数据质量",
            "error_level_2": "完整性",
            "status": "ACTIVE",
        }
        default_data.update(kwargs)
        return default_data


@pytest.fixture
def test_data_factory():
    """测试数据工厂fixture"""
    return TestDataFactory


# ============================================================================
# 测试环境配置
# ============================================================================

@pytest.fixture(autouse=True)
def setup_test_environment():
    """自动设置测试环境"""
    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "DEBUG"

    yield

    # 清理环境变量
    os.environ.pop("TESTING", None)
    os.environ.pop("LOG_LEVEL", None)


# ============================================================================
# 性能测试相关fixtures
# ============================================================================

@pytest.fixture
def performance_threshold():
    """性能测试阈值配置"""
    return {
        "response_time": 1.0,  # 响应时间阈值（秒）
        "memory_usage": 100,   # 内存使用阈值（MB）
        "cpu_usage": 80,       # CPU使用率阈值（%）
    }


# ============================================================================
# 临时文件和目录fixtures
# ============================================================================

@pytest.fixture
def temp_dir():
    """临时目录"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def temp_file():
    """临时文件"""
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as tmpfile:
        yield tmpfile.name
    os.unlink(tmpfile.name)


# ============================================================================
# 测试标记配置
# ============================================================================

def pytest_configure(config):
    """pytest配置钩子"""
    # 注册自定义标记
    config.addinivalue_line("markers", "unit: 单元测试标记")
    config.addinivalue_line("markers", "integration: 集成测试标记")
    config.addinivalue_line("markers", "e2e: 端到端测试标记")
    config.addinivalue_line("markers", "performance: 性能测试标记")
    config.addinivalue_line("markers", "security: 安全测试标记")


def pytest_collection_modifyitems(config, items):
    """修改测试收集项"""
    # 为不同目录的测试自动添加标记
    for item in items:
        # 根据文件路径自动添加标记
        if "unit/" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration/" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "e2e/" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
        elif "performance/" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
        elif "security/" in str(item.fspath):
            item.add_marker(pytest.mark.security)
