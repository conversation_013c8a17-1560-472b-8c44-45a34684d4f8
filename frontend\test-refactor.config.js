/**
 * 组件重构测试配置
 * 专门用于验证组件重构项目的测试配置
 */

import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  test: {
    // 测试环境配置
    environment: 'jsdom',
    
    // 全局设置
    globals: true,
    
    // 测试文件匹配模式
    include: [
      'src/__tests__/integration/**/*.test.js',
      'src/__tests__/performance/**/*.test.js', 
      'src/__tests__/ux/**/*.test.js',
      'src/__tests__/stability/**/*.test.js',
      'src/components/__tests__/**/*.test.js'
    ],
    
    // 排除文件
    exclude: [
      'node_modules/**',
      'dist/**',
      'src/__tests__/utils/**'
    ],
    
    // 测试超时设置
    testTimeout: 30000, // 30秒
    hookTimeout: 10000, // 10秒
    
    // 并发设置
    threads: true,
    maxThreads: 4,
    minThreads: 1,
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage/refactor',
      include: [
        'src/stores/**/*.js',
        'src/composables/**/*.js',
        'src/components/business/**/*.vue',
        'src/views/**/*.vue',
        'src/utils/**/*.js'
      ],
      exclude: [
        'src/**/*.test.js',
        'src/**/*.spec.js',
        'src/__tests__/**',
        'src/utils/legacy-*.js'
      ],
      thresholds: {
        global: {
          branches: 85,
          functions: 90,
          lines: 90,
          statements: 90
        },
        'src/stores/': {
          branches: 90,
          functions: 95,
          lines: 95,
          statements: 95
        },
        'src/composables/': {
          branches: 88,
          functions: 92,
          lines: 92,
          statements: 92
        }
      }
    },
    
    // 报告器配置
    reporter: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test-results/refactor-results.json',
      html: './test-results/refactor-report.html'
    },
    
    // 设置文件
    setupFiles: [
      './src/__tests__/setup/test-setup.js'
    ],
    
    // 模拟配置
    deps: {
      inline: ['element-plus']
    }
  },
  
  // 路径解析
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@tests': resolve(__dirname, 'src/__tests__')
    }
  },
  
  // 定义全局变量
  define: {
    __TEST_ENV__: true,
    __REFACTOR_TEST__: true
  }
})

/**
 * 测试运行脚本配置
 */
export const testScripts = {
  // 完整测试套件
  'test:refactor:full': 'vitest run --config test-refactor.config.js',
  
  // 分类测试
  'test:refactor:integration': 'vitest run --config test-refactor.config.js src/__tests__/integration',
  'test:refactor:performance': 'vitest run --config test-refactor.config.js src/__tests__/performance',
  'test:refactor:ux': 'vitest run --config test-refactor.config.js src/__tests__/ux',
  'test:refactor:stability': 'vitest run --config test-refactor.config.js src/__tests__/stability',
  
  // 监视模式
  'test:refactor:watch': 'vitest --config test-refactor.config.js',
  
  // 覆盖率测试
  'test:refactor:coverage': 'vitest run --config test-refactor.config.js --coverage',
  
  // 性能基准测试
  'test:refactor:benchmark': 'vitest run --config test-refactor.config.js src/__tests__/performance --reporter=json',
  
  // 生成报告
  'test:refactor:report': 'node src/__tests__/utils/generate-report.js'
}

/**
 * 测试环境变量
 */
export const testEnvironment = {
  // API配置
  VITE_API_BASE_URL: 'http://localhost:3000/api',
  VITE_API_TIMEOUT: '10000',
  
  // 测试标识
  NODE_ENV: 'test',
  VITEST: 'true',
  
  // 性能测试配置
  PERFORMANCE_TEST_ITERATIONS: '100',
  PERFORMANCE_TEST_TIMEOUT: '30000',
  
  // 稳定性测试配置
  STABILITY_TEST_DURATION: '300000', // 5分钟
  STABILITY_TEST_CONCURRENT_USERS: '20',
  
  // 调试配置
  DEBUG_TESTS: 'false',
  VERBOSE_LOGGING: 'false'
}

/**
 * 测试数据配置
 */
export const testData = {
  // 模拟规则数据
  mockRules: [
    {
      id: 1,
      rule_key: 'test-rule-1',
      rule_name: '测试规则1',
      status: 'ACTIVE',
      type: 'data_quality',
      level1: '数据质量',
      level2: '完整性检查',
      level3: '必填字段验证'
    },
    {
      id: 2,
      rule_key: 'test-rule-2', 
      rule_name: '测试规则2',
      status: 'INACTIVE',
      type: 'business_rule',
      level1: '业务规则',
      level2: '逻辑验证',
      level3: '业务流程检查'
    }
  ],
  
  // 性能测试数据
  performanceTestData: {
    smallDataset: 50,
    mediumDataset: 500,
    largeDataset: 5000,
    xlDataset: 50000
  },
  
  // 用户操作序列
  userActionSequences: [
    ['load-list', 'search', 'select', 'view-detail'],
    ['load-list', 'create', 'edit', 'delete'],
    ['load-list', 'filter', 'sort', 'paginate'],
    ['upload-data', 'validate', 'submit', 'confirm']
  ]
}

/**
 * 性能基准配置
 */
export const performanceBaselines = {
  // API响应时间基准（毫秒）
  apiResponseTime: {
    list: 150,
    detail: 80,
    create: 200,
    update: 180,
    delete: 100,
    search: 120
  },
  
  // 组件渲染时间基准（毫秒）
  componentRenderTime: {
    table: 100,
    form: 80,
    drawer: 60,
    uploader: 150
  },
  
  // 内存使用基准（MB）
  memoryUsage: {
    initial: 20,
    afterLoad: 35,
    peak: 80,
    afterCleanup: 25
  },
  
  // 缓存性能基准
  cachePerformance: {
    hitRate: 0.3, // 30%
    missLatency: 150, // ms
    hitLatency: 10 // ms
  }
}

/**
 * 测试质量门禁
 */
export const qualityGates = {
  // 测试通过率要求
  testPassRate: 95, // 95%
  
  // 性能改进要求
  performanceImprovement: {
    apiResponseTime: 60, // 60%提升
    memoryUsage: 40, // 40%优化
    renderTime: 50 // 50%提升
  },
  
  // 覆盖率要求
  coverageThresholds: {
    statements: 90,
    branches: 85,
    functions: 90,
    lines: 90
  },
  
  // 稳定性要求
  stabilityRequirements: {
    errorRate: 0.05, // 5%以下
    memoryLeakThreshold: 10, // 10MB
    concurrentUserSupport: 20
  }
}
