/**
 * 基础功能测试
 */

import { describe, it, expect } from 'vitest'

describe('基础功能验证', () => {
  it('应该能正常运行测试', () => {
    expect(1 + 1).toBe(2)
  })

  it('应该支持字符串操作', () => {
    const str = 'Hello World'
    expect(str.length).toBe(11)
    expect(str.toLowerCase()).toBe('hello world')
  })

  it('应该支持数组操作', () => {
    const arr = [1, 2, 3]
    expect(arr.length).toBe(3)
    expect(arr.includes(2)).toBe(true)
  })

  it('应该支持对象操作', () => {
    const obj = { name: 'test', value: 123 }
    expect(obj.name).toBe('test')
    expect(obj.value).toBe(123)
  })

  it('应该支持异步操作', async () => {
    const promise = Promise.resolve('async result')
    const result = await promise
    expect(result).toBe('async result')
  })
})
