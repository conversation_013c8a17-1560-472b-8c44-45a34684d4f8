# 子节点数据加载重构测试套件

本测试套件为子节点数据加载重构项目提供全面的测试覆盖，确保代码质量和性能目标的达成。

## 📋 测试概述

### 测试目标
- **单元测试覆盖率** ≥ 90%
- **集成测试覆盖关键业务流程**
- **性能测试验证90%+响应时间提升**
- **负载测试验证系统稳定性**

## 🏗️ 目录结构

```
tests/
├── README.md                           # 测试组织说明文档
├── __init__.py                         # 包初始化
├── conftest.py                         # 全局测试配置
├── pytest.ini                         # pytest配置文件
├── helpers/                            # 测试辅助工具
│   ├── __init__.py
│   ├── fixtures.py                     # 通用测试夹具
│   ├── mock_data.py                    # 模拟数据生成器
│   ├── assertions.py                   # 自定义断言
│   ├── database.py                     # 数据库测试工具
│   └── legacy_helpers.py               # 遗留辅助函数
│
├── unit/                               # 单元测试
│   ├── __init__.py
│   ├── conftest.py                     # 单元测试配置
│   ├── api/                            # API层单元测试
│   │   ├── routers/                    # 路由测试
│   │   ├── dependencies/               # 依赖注入测试
│   │   └── middleware/                 # 中间件测试
│   ├── services/                       # 服务层单元测试
│   │   ├── rule_details/               # 规则明细服务测试
│   │   ├── data_management/            # 数据管理服务测试
│   │   └── task_management/            # 任务管理服务测试
│   ├── models/                         # 模型层单元测试
│   ├── core/                           # 核心组件单元测试
│   └── config/                         # 配置层单元测试
│
├── integration/                        # 集成测试
│   ├── api/                            # API集成测试
│   ├── services/                       # 服务集成测试
│   ├── database/                       # 数据库集成测试
│   └── system/                         # 系统级集成测试
│
├── e2e/                                # 端到端测试
├── performance/                        # 性能测试
├── security/                           # 安全测试
└── reports/                            # 测试报告
    ├── coverage/                       # 覆盖率报告
    ├── performance/                    # 性能测试报告
    └── security/                       # 安全测试报告
```

## 📋 测试分层说明

### 单元测试 (unit/)
- **目的**: 测试单个组件或函数的功能
- **特点**: 快速执行，使用模拟对象隔离依赖
- **覆盖**: 边界条件、异常情况、业务逻辑
- **运行**: `pytest tests/unit/`

### 集成测试 (integration/)
- **目的**: 测试组件间的交互和数据流
- **特点**: 使用真实依赖，验证接口契约
- **覆盖**: API与服务集成、数据库操作、系统交互
- **运行**: `pytest tests/integration/`

### 端到端测试 (e2e/)
- **目的**: 模拟完整用户场景和业务流程
- **特点**: 测试系统整体功能，发现系统级问题
- **覆盖**: 用户工作流、规则生命周期
- **运行**: `pytest tests/e2e/`

### 性能测试 (performance/)
- **目的**: 验证系统性能指标和负载能力
- **特点**: 监控响应时间、内存使用、并发处理
- **覆盖**: 规则验证性能、数据库性能、并发负载
- **运行**: `pytest tests/performance/`

### 安全测试 (security/)
- **目的**: 验证系统安全机制和权限控制
- **特点**: 测试认证授权、数据保护、API安全
- **覆盖**: 身份验证、权限控制、数据安全
- **运行**: `pytest tests/security/`

## 🔧 测试命名规范

### 文件命名规范
```
# 单元测试
test_<component_name>.py
例: test_rule_service.py, test_database_models.py

# 集成测试
test_<feature_name>_integration.py
例: test_rule_details_api_integration.py

# 端到端测试
test_<workflow_name>_e2e.py
例: test_rule_lifecycle_e2e.py

# 性能测试
test_<component_name>_performance.py
例: test_rule_validation_performance.py

# 安全测试
test_<security_aspect>.py
例: test_authentication.py, test_authorization.py
```

### 测试类命名规范
```python
class Test<ComponentName>:
    """组件测试类"""

class Test<ComponentName><Action>:
    """特定功能测试类"""

# 示例
class TestRuleDetailsService:
    """规则明细服务测试类"""

class TestRuleDetailsServiceCreate:
    """规则明细服务创建功能测试类"""
```

### 测试方法命名规范
```python
def test_<action>_<condition>_<expected_result>(self):
    """测试方法命名模式"""

# 示例
def test_create_rule_detail_with_valid_data_should_succeed(self):
    """使用有效数据创建规则明细应该成功"""

def test_get_rule_details_when_rule_not_exists_should_return_error(self):
    """当规则不存在时获取规则明细应该返回错误"""

def test_migrate_data_with_large_dataset_should_complete_within_timeout(self):
    """使用大数据集迁移数据应该在超时时间内完成"""
```

## 📝 测试代码组织规范

### 测试类结构模板
```python
class TestRuleDetailsService:
    """规则明细服务测试类

    测试范围：
    - 规则明细的CRUD操作
    - 数据验证逻辑
    - 错误处理机制

    依赖组件：
    - DatabaseSession
    - RuleDetailsRepository
    - ValidationService
    """

    # 类级别夹具
    @pytest.fixture(scope="class")
    def service_instance(self, mock_db_session):
        """服务实例夹具"""
        return RuleDetailsService(db_session=mock_db_session)

    # 方法级别夹具
    @pytest.fixture
    def sample_rule_detail(self, test_data_factory):
        """示例规则明细数据"""
        return test_data_factory.create_rule_detail_data()

    # 测试方法分组
    class TestCreateRuleDetail:
        """创建规则明细测试组"""

        def test_create_with_valid_data_should_succeed(self, service_instance, sample_rule_detail):
            """使用有效数据创建规则明细应该成功"""
            # Arrange
            rule_detail_data = sample_rule_detail

            # Act
            result = service_instance.create_rule_detail(rule_detail_data)

            # Assert
            assert result.success is True
            assert result.data.rule_detail_id == rule_detail_data["rule_detail_id"]

        def test_create_with_invalid_data_should_fail(self, service_instance):
            """使用无效数据创建规则明细应该失败"""
            # Arrange
            invalid_data = {"invalid": "data"}

            # Act & Assert
            with pytest.raises(ValidationError):
                service_instance.create_rule_detail(invalid_data)

    class TestQueryRuleDetails:
        """查询规则明细测试组"""

        def test_query_existing_rule_details_should_return_data(self):
            """查询存在的规则明细应该返回数据"""
            pass

        def test_query_nonexistent_rule_details_should_return_empty(self):
            """查询不存在的规则明细应该返回空结果"""
            pass
```

### 测试数据管理规范

#### 使用测试工厂模式
```python
# 推荐：使用工厂模式
@pytest.fixture
def rule_detail_factory():
    def _create_rule_detail(**kwargs):
        default_data = {
            "rule_detail_id": "TEST_001",
            "rule_name": "测试规则",
            "status": "ACTIVE",
        }
        default_data.update(kwargs)
        return RuleDetail(**default_data)
    return _create_rule_detail

# 使用
def test_create_rule_detail(rule_detail_factory):
    rule_detail = rule_detail_factory(rule_name="自定义规则名称")
    assert rule_detail.rule_name == "自定义规则名称"
```

#### 测试数据隔离
```python
# 每个测试方法使用独立的数据
@pytest.fixture
def clean_database(test_db_session):
    """清理数据库夹具"""
    yield test_db_session
    # 测试后清理
    test_db_session.rollback()
    test_db_session.execute(text("DELETE FROM rule_details"))
    test_db_session.commit()
```

#### 敏感数据处理
```python
# 使用脱敏数据
MOCK_PATIENT_DATA = {
    "patient_id": "MOCK_PATIENT_001",  # 非真实患者ID
    "name": "测试患者",                 # 虚构姓名
    "id_card": "110000199001010001",   # 虚构身份证号
}
```

## 🚀 运行测试

### 基础运行命令
```bash
# 运行所有测试
pytest

# 运行特定目录的测试
pytest tests/unit/                    # 单元测试
pytest tests/integration/             # 集成测试
pytest tests/e2e/                     # 端到端测试
pytest tests/performance/             # 性能测试
pytest tests/security/                # 安全测试

# 运行特定文件的测试
pytest tests/unit/services/test_rule_service.py

# 运行特定测试方法
pytest tests/unit/services/test_rule_service.py::TestRuleService::test_create_rule
```

### 使用标记运行测试
```bash
# 运行单元测试
pytest -m unit

# 运行集成测试
pytest -m integration

# 运行规则明细相关测试
pytest -m rule_details

# 运行快速测试（排除慢速测试）
pytest -m "not slow"

# 组合标记
pytest -m "unit and rule_details"
pytest -m "integration or e2e"
```

### 测试报告和覆盖率
```bash
# 生成HTML覆盖率报告
pytest --cov-report=html

# 生成XML覆盖率报告（用于CI/CD）
pytest --cov-report=xml

# 显示缺失覆盖的行
pytest --cov-report=term-missing

# 设置覆盖率阈值
pytest --cov-fail-under=80

# 生成JUnit XML报告
pytest --junit-xml=tests/reports/junit.xml
```

### 调试和详细输出
```bash
# 详细输出
pytest -v

# 显示本地变量
pytest -l

# 在第一个失败时停止
pytest -x

# 显示最慢的10个测试
pytest --durations=10

# 实时输出日志
pytest -s --log-cli-level=INFO
```

## ⚙️ 测试配置

### 配置文件说明
- `pytest.ini` - pytest全局配置，包括测试发现、标记定义、覆盖率设置
- `tests/conftest.py` - 全局测试夹具和配置
- `tests/unit/conftest.py` - 单元测试专用配置
- `tests/integration/conftest.py` - 集成测试专用配置
- `tests/e2e/conftest.py` - 端到端测试专用配置
- `tests/performance/conftest.py` - 性能测试专用配置
- `tests/security/conftest.py` - 安全测试专用配置

### 环境变量配置
```bash
# 测试环境标识
export TESTING=true

# 日志级别
export LOG_LEVEL=DEBUG

# 测试数据库URL
export TEST_DATABASE_URL=sqlite:///:memory:

# 禁用外部服务调用
export DISABLE_EXTERNAL_SERVICES=true
```

### CI/CD集成
```yaml
# GitHub Actions 示例
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.12
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install pytest pytest-cov
      - name: Run unit tests
        run: pytest tests/unit/ -m unit --cov=api --cov=services --cov=models --cov=core
      - name: Run integration tests
        run: pytest tests/integration/ -m integration
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

## 🎯 规则详情表重构测试预留

为即将进行的"规则详情表可扩展数据库架构重构"预留了专门的测试模块：

### 新架构测试模块
```
tests/unit/services/rule_details/
├── test_rule_details_crud_service.py      # 新架构CRUD服务测试
├── test_rule_details_validation_service.py # 数据验证服务测试
└── test_rule_details_migration_service.py  # 迁移服务测试

tests/integration/rule_details/
├── test_rule_details_api_integration.py   # API集成测试
├── test_rule_details_database_integration.py # 数据库集成测试
└── test_rule_details_migration_integration.py # 迁移集成测试

tests/e2e/
└── test_rule_details_restructure_workflow.py # 重构工作流测试
```

### 重构测试策略
1. **向后兼容性测试**: 确保新架构与现有功能兼容
2. **数据迁移测试**: 验证数据迁移的完整性和正确性
3. **性能对比测试**: 对比新旧架构的性能差异
4. **回滚测试**: 验证迁移失败时的回滚机制

## 📊 测试最佳实践

### 1. 测试金字塔原则
- **70% 单元测试**: 快速反馈，覆盖业务逻辑
- **20% 集成测试**: 验证组件交互
- **10% 端到端测试**: 验证用户场景

### 2. AAA模式
```python
def test_create_rule_detail_should_succeed(self):
    # Arrange - 准备测试数据和环境
    rule_data = {"rule_name": "测试规则", "status": "ACTIVE"}

    # Act - 执行被测试的操作
    result = service.create_rule_detail(rule_data)

    # Assert - 验证结果
    assert result.success is True
    assert result.data.rule_name == "测试规则"
```

### 3. 测试独立性
- 每个测试应该独立运行
- 不依赖其他测试的执行顺序
- 使用适当的清理机制

### 4. 有意义的断言
```python
# 好的断言
assert response.status_code == 200
assert response.json()["success"] is True
assert len(response.json()["data"]) == 5

# 避免的断言
assert response  # 不够具体
assert True      # 无意义
```

### 5. 测试文档化
```python
def test_create_rule_detail_with_duplicate_id_should_fail(self):
    """
    测试创建重复ID的规则明细应该失败

    场景：
    1. 创建一个规则明细
    2. 尝试创建相同ID的规则明细

    预期结果：
    - 第二次创建应该失败
    - 返回适当的错误信息
    """
```

## 🔧 开发工作流

### 1. 编写测试驱动开发 (TDD)
```bash
# 1. 编写失败的测试
pytest tests/unit/services/test_new_feature.py -v

# 2. 编写最小实现使测试通过
pytest tests/unit/services/test_new_feature.py -v

# 3. 重构代码
pytest tests/unit/services/test_new_feature.py -v

# 4. 运行所有测试确保没有破坏现有功能
pytest tests/unit/ -v
```

### 2. 持续集成检查
```bash
# 本地提交前检查
pytest tests/unit/ -m "not slow"           # 快速单元测试
pytest tests/integration/ -m integration  # 集成测试
pytest --cov-fail-under=80                # 覆盖率检查
```

### 3. 代码审查清单
- [ ] 测试覆盖了所有分支路径
- [ ] 测试名称清晰描述了测试场景
- [ ] 使用了适当的测试夹具
- [ ] 测试数据与生产数据隔离
- [ ] 包含了边界条件和异常情况测试
- [ ] 测试执行时间合理（单元测试 < 1秒）

## 🆘 故障排除

### 常见问题

#### 1. 导入错误
```bash
# 错误：ModuleNotFoundError
# 解决：确保PYTHONPATH包含项目根目录
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

#### 2. 数据库连接问题
```bash
# 错误：数据库连接失败
# 解决：检查测试数据库配置
pytest tests/unit/core/test_db_session.py -v
```

#### 3. 测试运行缓慢
```bash
# 分析慢速测试
pytest --durations=10

# 只运行快速测试
pytest -m "not slow"
```

#### 4. 覆盖率不足
```bash
# 查看覆盖率详情
pytest --cov-report=html
# 打开 tests/reports/coverage/html/index.html
```

## 📞 支持和贡献

### 获取帮助
- 查看测试日志：`pytest -s --log-cli-level=DEBUG`
- 查看测试报告：`tests/reports/`
- 参考示例测试：`tests/unit/config/test_settings.py`

### 贡献指南
1. 遵循现有的测试命名和组织规范
2. 为新功能编写相应的测试
3. 确保测试覆盖率不低于80%
4. 更新相关文档

### 联系方式
- 技术问题：查看项目文档 `documentation/`
- 测试相关：参考本文档或查看 `tests/helpers/`
