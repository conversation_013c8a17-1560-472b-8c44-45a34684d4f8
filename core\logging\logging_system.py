import sys
from typing import Any

from loguru import logger

from config.environment_log_config import get_current_log_config
from config.logging_config import LOGGING_CONFIG


class LoggingSystem:
    def __init__(self, config=None, use_environment_config: bool = True):
        """
        初始化日志系统

        Args:
            config: 日志配置字典，如果为None则使用默认配置
            use_environment_config: 是否使用环境特定配置
        """
        if use_environment_config:
            # 使用环境特定配置
            self.env_config = get_current_log_config()
            self.config = self.env_config.build_config()
        else:
            # 使用传统配置（向后兼容）
            self.config = config or LOGGING_CONFIG
            self.env_config = None

        self._configure_logger()

    def _configure_logger(self):
        """配置日志系统"""
        # 清除现有的处理器
        for handler in logger._core.handlers:  # pylint: disable=protected-access # type: ignore
            logger.remove(handler)

        # 配置文件日志处理器
        self._configure_file_sink()

        # 配置控制台日志处理器
        self._configure_stdout_sink()

        # 如果使用环境配置，添加敏感信息过滤
        if self.env_config and hasattr(self.env_config, "sensitive_filter"):
            self._configure_sensitive_filter()

    def _configure_file_sink(self):
        """配置文件日志处理器"""
        file_config = self.config.get("file_sink", {})

        # 使用环境特定的文件配置或默认配置
        file_path = file_config.get("path", self.config.get("path", "logs/app_{time}.log"))
        file_rotation = file_config.get("rotation", self.config.get("rotation", "10 MB"))
        file_retention = file_config.get("retention", self.config.get("retention", "7 days"))
        file_compression = file_config.get("compression", self.config.get("compression", "gz"))

        logger.add(
            sink=file_path,
            format=self.config.get("format", "{time} | {level} | {message}"),
            rotation=file_rotation,
            retention=file_retention,
            compression=file_compression,
            enqueue=self.config.get("enqueue", True),
            level=self.config.get("level", "INFO").upper(),
            filter=self._create_log_filter() if self.env_config else None,
        )

    def _configure_stdout_sink(self):
        """配置控制台日志处理器"""
        stdout_config = self.config.get("stdout_sink", {})

        if stdout_config and stdout_config.get("enabled", False):
            logger.add(
                sink=sys.stdout,
                format=stdout_config.get("format", "{time:HH:mm:ss} | {level} | {message}"),
                level=stdout_config.get("level", "INFO").upper(),
                enqueue=self.config.get("enqueue", True),
                filter=self._create_log_filter() if self.env_config else None,
            )

    def _configure_sensitive_filter(self):
        """配置敏感信息过滤器"""
        # 敏感信息过滤已经在_create_log_filter中实现
        pass

    def _create_log_filter(self):
        """
        创建日志过滤器

        Returns:
            callable: 日志过滤函数
        """
        if not self.env_config or not hasattr(self.env_config, "sensitive_filter"):
            return None

        sensitive_filter = self.env_config.sensitive_filter

        def log_filter(record):
            """
            日志过滤函数

            Args:
                record: 日志记录对象

            Returns:
                bool: 是否允许记录此日志
            """
            # 过滤敏感信息
            if isinstance(record, dict) and "message" in record:
                record["message"] = sensitive_filter.filter_message(str(record["message"]))
            elif hasattr(record, "message"):
                record.message = sensitive_filter.filter_message(str(record.message))

            # 添加环境上下文
            if self.env_config and isinstance(record, dict) and "extra" in record:
                record["extra"].update(self.env_config.get_environment_context())

            return True

        return log_filter

    def get_logger(self):
        return logger


# Global instance of the logging system and logger
# This makes the logger easily importable and usable across the application
# 默认使用环境特定配置，提供向后兼容性
_logging_system_instance = LoggingSystem(use_environment_config=True)
log = _logging_system_instance.get_logger()  # Expose the configured logger directly


# Optional: A function to get the globally configured logger,
# which is just returning the global `log` variable.
def get_global_logger():
    """
    获取全局配置的日志器

    Returns:
        logger: 配置好的日志器实例
    """
    return log


def get_logging_system():
    """
    获取全局日志系统实例

    Returns:
        LoggingSystem: 日志系统实例
    """
    return _logging_system_instance


def reinitialize_logging_system(use_environment_config: bool = True, config: dict[str, Any] | None = None):
    """
    重新初始化日志系统

    Args:
        use_environment_config: 是否使用环境特定配置
        config: 自定义配置字典
    """
    global _logging_system_instance, log

    _logging_system_instance = LoggingSystem(config=config, use_environment_config=use_environment_config)
    log = _logging_system_instance.get_logger()


if __name__ == "__main__":
    # Example Usage (for testing purposes using the global logger)

    # The logger is already configured globally, so we can use `log` directly
    log.info("Logging system initialized with default config (from global instance).")
    log.debug("This is a debug message from global logger.")
    log.warning("This is a warning message from global logger.")
    log.error("This is an error message from global logger.")

    try:
        1 / 0  # noqa: B018
    except ZeroDivisionError:
        log.exception("An exception occurred (logged by global logger)!")

    # Accessing config for path display
    # Note: LOGGING_CONFIG is imported, so we can use it directly here for the example.
    print(f"Test logs should be in: {LOGGING_CONFIG['path'].split('/')[0]}")
    log.info(f"Log files are configured to be in: {LOGGING_CONFIG['path']}")
