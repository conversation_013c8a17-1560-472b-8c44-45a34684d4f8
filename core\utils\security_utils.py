"""
安全工具模块
提供错误信息脱敏、敏感数据处理等安全功能
"""

import hashlib
import re
from dataclasses import dataclass
from typing import Any


@dataclass
class SensitivePattern:
    """敏感信息模式"""
    name: str
    pattern: str
    replacement: str
    description: str


class SecurityUtils:
    """
    安全工具类
    提供敏感信息脱敏、数据安全处理等功能
    """

    # 敏感信息模式定义
    SENSITIVE_PATTERNS = [
        SensitivePattern(
            name="password",
            pattern=r'(?i)(password|pwd|passwd|secret|token|key)["\s]*[:=]["\s]*([^"\s,}]+)',
            replacement=r'\1: "***"',
            description="密码和密钥"
        ),
        SensitivePattern(
            name="api_key",
            pattern=r'(?i)(api[_-]?key|access[_-]?token|bearer[_-]?token)["\s]*[:=]["\s]*([^"\s,}]+)',
            replacement=r'\1: "***"',
            description="API密钥和访问令牌"
        ),
        SensitivePattern(
            name="phone",
            pattern=r'(\+?86)?[-\s]?1[3-9]\d{9}',
            replacement="***-****-****",
            description="手机号码"
        ),
        SensitivePattern(
            name="id_card",
            pattern=r'\d{17}[\dXx]',
            replacement="***************",
            description="身份证号"
        ),
        SensitivePattern(
            name="email",
            pattern=r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            replacement="***@***.***",
            description="邮箱地址"
        ),
        SensitivePattern(
            name="ip_address",
            pattern=r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
            replacement="***.***.***.***",
            description="IP地址"
        ),
        SensitivePattern(
            name="bank_card",
            pattern=r'\b\d{16,19}\b',
            replacement="****-****-****-****",
            description="银行卡号"
        ),
        SensitivePattern(
            name="medical_id",
            pattern=r'(?i)(病案号|bah|patient[_-]?id)["\s]*[:=]["\s]*([^"\s,}]+)',
            replacement=r'\1: "***"',
            description="医疗相关ID"
        )
    ]

    # 敏感字段名称（用于字典脱敏）
    SENSITIVE_FIELD_NAMES = {
        "password", "pwd", "passwd", "secret", "token", "key", "api_key",
        "access_token", "bearer_token", "authorization", "auth",
        "phone", "mobile", "telephone", "tel",
        "id_card", "identity", "ssn",
        "email", "mail",
        "bank_card", "card_number", "account_number",
        "bah", "patient_id", "medical_id"
    }

    @classmethod
    def sanitize_string(cls, text: str, patterns: list[SensitivePattern] | None = None) -> str:
        """
        对字符串进行脱敏处理

        Args:
            text: 待脱敏的字符串
            patterns: 自定义脱敏模式，如果为None则使用默认模式

        Returns:
            str: 脱敏后的字符串
        """
        if not text or not isinstance(text, str):
            return text

        patterns = patterns or cls.SENSITIVE_PATTERNS
        sanitized_text = text

        for pattern in patterns:
            try:
                sanitized_text = re.sub(pattern.pattern, pattern.replacement, sanitized_text)
            except re.error:
                # 如果正则表达式有问题，记录但不中断处理
                continue

        return sanitized_text

    @classmethod
    def sanitize_dict(cls, data: dict[str, Any], deep: bool = True) -> dict[str, Any]:
        """
        对字典进行脱敏处理

        Args:
            data: 待脱敏的字典
            deep: 是否深度脱敏（处理嵌套字典和列表）

        Returns:
            Dict: 脱敏后的字典
        """
        if not isinstance(data, dict):
            return data

        sanitized_data = {}

        for key, value in data.items():
            # 检查字段名是否敏感
            if cls._is_sensitive_field(key):
                sanitized_data[key] = "***"
            elif isinstance(value, str):
                sanitized_data[key] = cls.sanitize_string(value)
            elif isinstance(value, dict) and deep:
                sanitized_data[key] = cls.sanitize_dict(value, deep=True)
            elif isinstance(value, list) and deep:
                sanitized_data[key] = cls.sanitize_list(value, deep=True)
            else:
                sanitized_data[key] = value

        return sanitized_data

    @classmethod
    def sanitize_list(cls, data: list[Any], deep: bool = True) -> list[Any]:
        """
        对列表进行脱敏处理

        Args:
            data: 待脱敏的列表
            deep: 是否深度脱敏

        Returns:
            List: 脱敏后的列表
        """
        if not isinstance(data, list):
            return data

        sanitized_list = []

        for item in data:
            if isinstance(item, str):
                sanitized_list.append(cls.sanitize_string(item))
            elif isinstance(item, dict) and deep:
                sanitized_list.append(cls.sanitize_dict(item, deep=True))
            elif isinstance(item, list) and deep:
                sanitized_list.append(cls.sanitize_list(item, deep=True))
            else:
                sanitized_list.append(item)

        return sanitized_list

    @classmethod
    def sanitize_any(cls, data: Any, deep: bool = True) -> Any:
        """
        对任意类型数据进行脱敏处理

        Args:
            data: 待脱敏的数据
            deep: 是否深度脱敏

        Returns:
            Any: 脱敏后的数据
        """
        if isinstance(data, str):
            return cls.sanitize_string(data)
        elif isinstance(data, dict):
            return cls.sanitize_dict(data, deep=deep)
        elif isinstance(data, list):
            return cls.sanitize_list(data, deep=deep)
        else:
            return data

    @classmethod
    def _is_sensitive_field(cls, field_name: str) -> bool:
        """
        检查字段名是否敏感

        Args:
            field_name: 字段名

        Returns:
            bool: 是否为敏感字段
        """
        if not isinstance(field_name, str):
            return False

        field_lower = field_name.lower()

        # 直接匹配
        if field_lower in cls.SENSITIVE_FIELD_NAMES:
            return True

        # 模糊匹配
        for sensitive_name in cls.SENSITIVE_FIELD_NAMES:
            if sensitive_name in field_lower or field_lower in sensitive_name:
                return True

        return False

    @classmethod
    def hash_sensitive_data(cls, data: str, algorithm: str = "sha256") -> str:
        """
        对敏感数据进行哈希处理

        Args:
            data: 敏感数据
            algorithm: 哈希算法（md5, sha1, sha256, sha512）

        Returns:
            str: 哈希值
        """
        if not data:
            return ""

        try:
            if algorithm == "md5":
                return hashlib.md5(data.encode()).hexdigest()
            elif algorithm == "sha1":
                return hashlib.sha1(data.encode()).hexdigest()
            elif algorithm == "sha256":
                return hashlib.sha256(data.encode()).hexdigest()
            elif algorithm == "sha512":
                return hashlib.sha512(data.encode()).hexdigest()
            else:
                # 默认使用sha256
                return hashlib.sha256(data.encode()).hexdigest()
        except Exception:
            return "***"

    @classmethod
    def mask_string(cls, text: str, mask_char: str = "*", keep_start: int = 2, keep_end: int = 2) -> str:
        """
        对字符串进行掩码处理

        Args:
            text: 待处理的字符串
            mask_char: 掩码字符
            keep_start: 保留开头字符数
            keep_end: 保留结尾字符数

        Returns:
            str: 掩码后的字符串
        """
        if not text or not isinstance(text, str):
            return text

        text_len = len(text)

        # 如果字符串太短，全部掩码
        if text_len <= keep_start + keep_end:
            return mask_char * text_len

        # 计算掩码部分长度
        mask_len = text_len - keep_start - keep_end

        return text[:keep_start] + mask_char * mask_len + text[-keep_end:]

    @classmethod
    def create_safe_error_context(cls, context: dict[str, Any]) -> dict[str, Any]:
        """
        创建安全的错误上下文（用于日志记录）

        Args:
            context: 原始错误上下文

        Returns:
            Dict: 安全的错误上下文
        """
        if not isinstance(context, dict):
            return {}

        safe_context = {}

        for key, value in context.items():
            if key in ["request_body", "user_context", "form_data", "query_params"]:
                # 对可能包含敏感信息的字段进行深度脱敏
                safe_context[key] = cls.sanitize_any(value, deep=True)
            elif key in ["headers", "cookies"]:
                # 对头部和Cookie进行特殊处理
                safe_context[key] = cls._sanitize_headers(value)
            elif isinstance(value, str):
                safe_context[key] = cls.sanitize_string(value)
            else:
                safe_context[key] = value

        return safe_context

    @classmethod
    def _sanitize_headers(cls, headers: dict[str, str] | Any) -> dict[str, str]:
        """
        对HTTP头部进行脱敏处理

        Args:
            headers: HTTP头部字典

        Returns:
            Dict: 脱敏后的头部字典
        """
        if not isinstance(headers, dict):
            return {}

        safe_headers = {}
        sensitive_header_names = {
            "authorization", "x-api-key", "cookie", "set-cookie",
            "x-auth-token", "x-access-token", "bearer"
        }

        for key, value in headers.items():
            if isinstance(key, str) and key.lower() in sensitive_header_names:
                safe_headers[key] = "***"
            elif isinstance(value, str):
                safe_headers[key] = cls.sanitize_string(value)
            else:
                safe_headers[key] = str(value)

        return safe_headers

    @classmethod
    def validate_data_safety(cls, data: Any) -> dict[str, Any]:
        """
        验证数据安全性

        Args:
            data: 待验证的数据

        Returns:
            Dict: 验证结果
        """
        result = {
            "is_safe": True,
            "issues": [],
            "risk_level": "low"
        }

        # 检查是否包含敏感信息
        if isinstance(data, str):
            for pattern in cls.SENSITIVE_PATTERNS:
                if re.search(pattern.pattern, data, re.IGNORECASE):
                    result["is_safe"] = False
                    result["issues"].append(f"包含{pattern.description}")
        elif isinstance(data, dict):
            for key, value in data.items():
                if cls._is_sensitive_field(key):
                    result["is_safe"] = False
                    result["issues"].append(f"包含敏感字段: {key}")
                if isinstance(value, str):
                    sub_result = cls.validate_data_safety(value)
                    if not sub_result["is_safe"]:
                        result["is_safe"] = False
                        result["issues"].extend(sub_result["issues"])

        # 确定风险级别
        if not result["is_safe"]:
            if len(result["issues"]) > 3:
                result["risk_level"] = "high"
            elif len(result["issues"]) > 1:
                result["risk_level"] = "medium"
            else:
                result["risk_level"] = "low"

        return result
