/**
 * 字体设计Token
 * 定义系统中使用的所有字体相关变量，确保排版一致性
 */

// 字体族
export const fontFamily = {
  // 主要字体 - 系统字体栈
  primary: [
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    'Roboto',
    '"Helvetica Neue"',
    'Arial',
    '"Noto Sans"',
    'sans-serif',
    '"Apple Color Emoji"',
    '"Segoe UI Emoji"',
    '"Segoe UI Symbol"',
    '"Noto Color Emoji"'
  ].join(', '),

  // 中文字体
  chinese: [
    '"PingFang SC"',
    '"Hiragino Sans GB"',
    '"Microsoft YaHei"',
    '"WenQuanYi Micro Hei"',
    'sans-serif'
  ].join(', '),

  // 等宽字体 - 代码显示
  mono: [
    '"Fira Code"',
    '"JetBrains Mono"',
    '"Source Code Pro"',
    'Menlo',
    'Monaco',
    'Consolas',
    '"Liberation Mono"',
    '"Courier New"',
    'monospace'
  ].join(', '),

  // 数字字体 - 表格数据
  numeric: [
    '"SF Mono"',
    '"Monaco"',
    '"Inconsolata"',
    '"Roboto Mono"',
    '"Source Code Pro"',
    'monospace'
  ].join(', ')
}

// 字体大小
export const fontSize = {
  // 基础字体大小
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  base: '1rem',     // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.875rem', // 30px
  '4xl': '2.25rem', // 36px
  '5xl': '3rem',    // 48px
  '6xl': '3.75rem', // 60px
  '7xl': '4.5rem',  // 72px
  '8xl': '6rem',    // 96px
  '9xl': '8rem'     // 128px
}

// 字体粗细
export const fontWeight = {
  thin: '100',
  extralight: '200',
  light: '300',
  normal: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',
  black: '900'
}

// 行高
export const lineHeight = {
  none: '1',
  tight: '1.25',
  snug: '1.375',
  normal: '1.5',
  relaxed: '1.625',
  loose: '2',
  // 具体数值
  3: '0.75rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  7: '1.75rem',
  8: '2rem',
  9: '2.25rem',
  10: '2.5rem'
}

// 字母间距
export const letterSpacing = {
  tighter: '-0.05em',
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em',
  wider: '0.05em',
  widest: '0.1em'
}

// 文本对齐
export const textAlign = {
  left: 'left',
  center: 'center',
  right: 'right',
  justify: 'justify'
}

// 文本装饰
export const textDecoration = {
  none: 'none',
  underline: 'underline',
  overline: 'overline',
  lineThrough: 'line-through'
}

// 文本转换
export const textTransform = {
  none: 'none',
  capitalize: 'capitalize',
  uppercase: 'uppercase',
  lowercase: 'lowercase'
}

// 排版预设 - 标题
export const headings = {
  h1: {
    fontSize: fontSize['4xl'],
    fontWeight: fontWeight.bold,
    lineHeight: lineHeight.tight,
    letterSpacing: letterSpacing.tight,
    marginBottom: '1.5rem'
  },
  h2: {
    fontSize: fontSize['3xl'],
    fontWeight: fontWeight.bold,
    lineHeight: lineHeight.tight,
    letterSpacing: letterSpacing.tight,
    marginBottom: '1.25rem'
  },
  h3: {
    fontSize: fontSize['2xl'],
    fontWeight: fontWeight.semibold,
    lineHeight: lineHeight.snug,
    letterSpacing: letterSpacing.normal,
    marginBottom: '1rem'
  },
  h4: {
    fontSize: fontSize.xl,
    fontWeight: fontWeight.semibold,
    lineHeight: lineHeight.snug,
    letterSpacing: letterSpacing.normal,
    marginBottom: '0.75rem'
  },
  h5: {
    fontSize: fontSize.lg,
    fontWeight: fontWeight.medium,
    lineHeight: lineHeight.normal,
    letterSpacing: letterSpacing.normal,
    marginBottom: '0.5rem'
  },
  h6: {
    fontSize: fontSize.base,
    fontWeight: fontWeight.medium,
    lineHeight: lineHeight.normal,
    letterSpacing: letterSpacing.normal,
    marginBottom: '0.5rem'
  }
}

// 排版预设 - 正文
export const body = {
  // 大号正文
  large: {
    fontSize: fontSize.lg,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.relaxed,
    letterSpacing: letterSpacing.normal
  },
  // 标准正文
  default: {
    fontSize: fontSize.base,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal,
    letterSpacing: letterSpacing.normal
  },
  // 小号正文
  small: {
    fontSize: fontSize.sm,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal,
    letterSpacing: letterSpacing.normal
  },
  // 极小正文
  tiny: {
    fontSize: fontSize.xs,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal,
    letterSpacing: letterSpacing.wide
  }
}

// 排版预设 - 特殊用途
export const special = {
  // 标签文字
  label: {
    fontSize: fontSize.sm,
    fontWeight: fontWeight.medium,
    lineHeight: lineHeight.normal,
    letterSpacing: letterSpacing.wide,
    textTransform: textTransform.uppercase
  },
  // 按钮文字
  button: {
    fontSize: fontSize.sm,
    fontWeight: fontWeight.medium,
    lineHeight: lineHeight.none,
    letterSpacing: letterSpacing.wide
  },
  // 代码文字
  code: {
    fontFamily: fontFamily.mono,
    fontSize: fontSize.sm,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal,
    letterSpacing: letterSpacing.normal
  },
  // 数字显示
  numeric: {
    fontFamily: fontFamily.numeric,
    fontSize: fontSize.base,
    fontWeight: fontWeight.medium,
    lineHeight: lineHeight.none,
    letterSpacing: letterSpacing.normal
  },
  // 标题文字
  caption: {
    fontSize: fontSize.xs,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal,
    letterSpacing: letterSpacing.wide,
    textTransform: textTransform.uppercase
  },
  // 链接文字
  link: {
    fontSize: fontSize.base,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal,
    letterSpacing: letterSpacing.normal,
    textDecoration: textDecoration.underline
  }
}

// 响应式字体大小
export const responsiveFontSize = {
  xs: {
    base: fontSize.xs,
    sm: fontSize.sm,
    md: fontSize.sm,
    lg: fontSize.base,
    xl: fontSize.base
  },
  sm: {
    base: fontSize.sm,
    sm: fontSize.base,
    md: fontSize.base,
    lg: fontSize.lg,
    xl: fontSize.lg
  },
  base: {
    base: fontSize.base,
    sm: fontSize.lg,
    md: fontSize.lg,
    lg: fontSize.xl,
    xl: fontSize.xl
  },
  lg: {
    base: fontSize.lg,
    sm: fontSize.xl,
    md: fontSize.xl,
    lg: fontSize['2xl'],
    xl: fontSize['2xl']
  },
  xl: {
    base: fontSize.xl,
    sm: fontSize['2xl'],
    md: fontSize['2xl'],
    lg: fontSize['3xl'],
    xl: fontSize['3xl']
  }
}

// 工具函数
export const typographyUtils = {
  // 获取字体样式
  getTextStyle: (preset, variant = 'default') => {
    const presets = { ...headings, ...body, ...special }
    return presets[preset] || presets[variant] || body.default
  },

  // 计算行高
  calculateLineHeight: (fontSize, ratio = 1.5) => {
    const size = parseFloat(fontSize)
    return `${size * ratio}rem`
  },

  // 获取响应式字体大小
  getResponsiveFontSize: (size, breakpoint = 'base') => {
    return responsiveFontSize[size]?.[breakpoint] || fontSize[size] || fontSize.base
  },

  // 生成字体CSS
  generateFontCSS: (config) => {
    const {
      family = fontFamily.primary,
      size = fontSize.base,
      weight = fontWeight.normal,
      height = lineHeight.normal,
      spacing = letterSpacing.normal
    } = config

    return {
      fontFamily: family,
      fontSize: size,
      fontWeight: weight,
      lineHeight: height,
      letterSpacing: spacing
    }
  }
}

// 导出所有字体token
export const typography = {
  fontFamily,
  fontSize,
  fontWeight,
  lineHeight,
  letterSpacing,
  textAlign,
  textDecoration,
  textTransform,
  headings,
  body,
  special,
  responsiveFontSize,
  ...typographyUtils
}
