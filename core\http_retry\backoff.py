"""
指数退避重试算法实现
提供多种退避策略，支持抖动机制和可配置参数
"""

import logging
import random
import threading
from abc import ABC, abstractmethod
from collections.abc import Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class BackoffStrategy(Enum):
    """退避策略枚举"""

    EXPONENTIAL = "exponential"  # 指数退避
    LINEAR = "linear"           # 线性退避
    FIXED = "fixed"             # 固定延迟
    CUSTOM = "custom"           # 自定义策略


@dataclass
class BackoffConfig:
    """退避配置数据类"""

    strategy: BackoffStrategy = BackoffStrategy.EXPONENTIAL
    base_delay: float = 1.0           # 基础延迟时间(秒)
    max_delay: float = 60.0           # 最大延迟时间(秒)
    backoff_factor: float = 2.0       # 退避因子
    jitter: bool = True               # 是否启用抖动
    jitter_min: float = 0.5           # 抖动最小比例
    jitter_max: float = 1.0           # 抖动最大比例

    def __post_init__(self):
        """配置验证"""
        self._validate_config()

    def _validate_config(self):
        """验证配置参数"""
        if self.base_delay <= 0:
            raise ValueError("base_delay必须大于0")

        if self.max_delay < self.base_delay:
            raise ValueError("max_delay必须大于等于base_delay")

        if self.backoff_factor <= 0:
            raise ValueError("backoff_factor必须大于0")

        if not (0 <= self.jitter_min <= self.jitter_max <= 1):
            raise ValueError("jitter_min和jitter_max必须在0到1之间，且jitter_min <= jitter_max")


class BackoffAlgorithm(ABC):
    """退避算法抽象基类"""

    def __init__(self, config: BackoffConfig):
        self.config = config
        self._lock = threading.Lock()  # 线程安全

    @abstractmethod
    def calculate_delay(self, attempt: int) -> float:
        """
        计算延迟时间

        Args:
            attempt: 重试次数(从0开始)

        Returns:
            延迟时间(秒)
        """
        pass

    def _apply_jitter(self, delay: float) -> float:
        """应用抖动机制"""
        if not self.config.jitter:
            return delay

        # 生成抖动因子
        jitter_factor = random.uniform(self.config.jitter_min, self.config.jitter_max)
        return delay * jitter_factor

    def _apply_max_delay(self, delay: float) -> float:
        """应用最大延迟限制"""
        return min(delay, self.config.max_delay)


class ExponentialBackoff(BackoffAlgorithm):
    """指数退避算法"""

    def calculate_delay(self, attempt: int) -> float:
        """
        计算指数退避延迟时间

        公式: delay = min(base_delay * (backoff_factor ** attempt), max_delay)
        """
        with self._lock:
            # 计算指数退避延迟
            delay = self.config.base_delay * (self.config.backoff_factor**attempt)

            # 应用最大延迟限制
            delay = self._apply_max_delay(delay)

            # 应用抖动
            delay = self._apply_jitter(delay)

            return delay


class LinearBackoff(BackoffAlgorithm):
    """线性退避算法"""

    def calculate_delay(self, attempt: int) -> float:
        """
        计算线性退避延迟时间

        公式: delay = min(base_delay * (1 + attempt * backoff_factor), max_delay)
        """
        with self._lock:
            # 计算线性退避延迟
            delay = self.config.base_delay * (1 + attempt * self.config.backoff_factor)

            # 应用最大延迟限制
            delay = self._apply_max_delay(delay)

            # 应用抖动
            delay = self._apply_jitter(delay)

            return delay


class FixedBackoff(BackoffAlgorithm):
    """固定延迟算法"""

    def calculate_delay(self, attempt: int) -> float:
        """
        计算固定延迟时间

        公式: delay = base_delay (忽略attempt)
        """
        with self._lock:
            # 固定延迟
            delay = self.config.base_delay

            # 应用抖动
            delay = self._apply_jitter(delay)

            return delay


class CustomBackoff(BackoffAlgorithm):
    """自定义退避算法"""

    def __init__(self, config: BackoffConfig, custom_func: Callable[[int, BackoffConfig], float]):
        super().__init__(config)
        self.custom_func = custom_func

    def calculate_delay(self, attempt: int) -> float:
        """
        使用自定义函数计算延迟时间
        """
        with self._lock:
            # 调用自定义函数
            delay = self.custom_func(attempt, self.config)

            # 应用最大延迟限制
            delay = self._apply_max_delay(delay)

            # 应用抖动
            delay = self._apply_jitter(delay)

            return delay


# 全局算法实例缓存
_algorithm_cache = {}
_cache_lock = threading.Lock()


def get_backoff_algorithm(config: BackoffConfig) -> BackoffAlgorithm:
    """
    获取退避算法实例（带缓存）

    Args:
        config: 退避配置

    Returns:
        退避算法实例
    """
    # 生成缓存键
    cache_key = (
        config.strategy,
        config.base_delay,
        config.max_delay,
        config.backoff_factor,
        config.jitter,
        config.jitter_min,
        config.jitter_max,
    )

    with _cache_lock:
        if cache_key not in _algorithm_cache:
            # 创建算法实例
            if config.strategy == BackoffStrategy.EXPONENTIAL:
                algorithm = ExponentialBackoff(config)
            elif config.strategy == BackoffStrategy.LINEAR:
                algorithm = LinearBackoff(config)
            elif config.strategy == BackoffStrategy.FIXED:
                algorithm = FixedBackoff(config)
            else:
                raise ValueError(f"不支持的退避策略: {config.strategy}")

            _algorithm_cache[cache_key] = algorithm

        return _algorithm_cache[cache_key]


def calculate_backoff_delay(
    attempt: int,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    strategy: BackoffStrategy | str = BackoffStrategy.EXPONENTIAL,
) -> float:
    """
    计算退避延迟时间的便捷函数

    Args:
        attempt: 重试次数(从0开始)
        base_delay: 基础延迟时间(秒)
        max_delay: 最大延迟时间(秒)
        backoff_factor: 退避因子
        jitter: 是否启用抖动
        strategy: 退避策略

    Returns:
        延迟时间(秒)

    Examples:
        >>> # 指数退避
        >>> delay = calculate_backoff_delay(0, 1.0, 60.0, 2.0)  # ~1秒
        >>> delay = calculate_backoff_delay(1, 1.0, 60.0, 2.0)  # ~2秒
        >>> delay = calculate_backoff_delay(2, 1.0, 60.0, 2.0)  # ~4秒

        >>> # 线性退避
        >>> delay = calculate_backoff_delay(0, 1.0, 60.0, 1.0, strategy="linear")  # ~1秒
        >>> delay = calculate_backoff_delay(1, 1.0, 60.0, 1.0, strategy="linear")  # ~2秒
        >>> delay = calculate_backoff_delay(2, 1.0, 60.0, 1.0, strategy="linear")  # ~3秒
    """
    # 转换字符串策略为枚举
    if isinstance(strategy, str):
        strategy = BackoffStrategy(strategy)

    # 创建配置
    config = BackoffConfig(
        strategy=strategy,
        base_delay=base_delay,
        max_delay=max_delay,
        backoff_factor=backoff_factor,
        jitter=jitter
    )

    # 获取算法实例并计算延迟
    algorithm = get_backoff_algorithm(config)
    return algorithm.calculate_delay(attempt)


def clear_algorithm_cache():
    """清除算法实例缓存"""
    with _cache_lock:
        _algorithm_cache.clear()
        logger.info("退避算法缓存已清除")


# 性能测试和基准测试函数
def benchmark_backoff_algorithms(max_attempts: int = 10, iterations: int = 1000) -> dict:
    """
    退避算法性能基准测试

    Args:
        max_attempts: 最大重试次数
        iterations: 测试迭代次数

    Returns:
        性能测试结果
    """
    import time

    strategies = [
        BackoffStrategy.EXPONENTIAL,
        BackoffStrategy.LINEAR,
        BackoffStrategy.FIXED
    ]

    results = {}

    for strategy in strategies:
        config = BackoffConfig(strategy=strategy)
        algorithm = get_backoff_algorithm(config)

        start_time = time.perf_counter()

        for _ in range(iterations):
            for attempt in range(max_attempts):
                algorithm.calculate_delay(attempt)

        end_time = time.perf_counter()

        results[strategy.value] = {
            "total_time": end_time - start_time,
            "avg_time_per_call": (end_time - start_time) / (iterations * max_attempts),
            "calls_per_second": (iterations * max_attempts) / (end_time - start_time),
        }

    return results
