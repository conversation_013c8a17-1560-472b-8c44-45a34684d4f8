"""
自定义断言工具
提供项目特定的断言函数
"""

from typing import Any

import pytest


def assert_api_response_success(response: dict[str, Any], expected_code: int = 200):
    """断言API响应成功"""
    assert response.get("success") is True, f"API响应失败: {response.get('message')}"
    assert response.get("code") == expected_code, f"期望状态码 {expected_code}, 实际 {response.get('code')}"
    assert "data" in response, "响应中缺少data字段"


def assert_api_response_error(response: dict[str, Any], expected_code: int = None):
    """断言API响应错误"""
    assert response.get("success") is False, "期望API响应失败，但实际成功"
    if expected_code:
        assert response.get("code") == expected_code, f"期望错误码 {expected_code}, 实际 {response.get('code')}"
    assert response.get("message"), "错误响应中缺少message字段"


def assert_rule_detail_valid(rule_detail: dict[str, Any]):
    """断言规则明细数据有效"""
    required_fields = ["rule_detail_id", "rule_name", "status"]
    for field in required_fields:
        assert field in rule_detail, f"规则明细缺少必需字段: {field}"
        assert rule_detail[field], f"规则明细字段 {field} 不能为空"


def assert_pagination_response(response: dict[str, Any], expected_total: int = None):
    """断言分页响应格式"""
    assert "items" in response, "分页响应缺少items字段"
    assert "total" in response, "分页响应缺少total字段"
    assert "page" in response, "分页响应缺少page字段"
    assert "page_size" in response, "分页响应缺少page_size字段"

    if expected_total is not None:
        assert response["total"] == expected_total, f"期望总数 {expected_total}, 实际 {response['total']}"


def assert_database_record_exists(session, model_class, **filters):
    """断言数据库记录存在"""
    query = session.query(model_class)
    for field, value in filters.items():
        query = query.filter(getattr(model_class, field) == value)

    record = query.first()
    assert record is not None, f"数据库中不存在匹配的 {model_class.__name__} 记录: {filters}"
    return record


def assert_database_record_not_exists(session, model_class, **filters):
    """断言数据库记录不存在"""
    query = session.query(model_class)
    for field, value in filters.items():
        query = query.filter(getattr(model_class, field) == value)

    record = query.first()
    assert record is None, f"数据库中存在不应该存在的 {model_class.__name__} 记录: {filters}"


def assert_performance_within_threshold(execution_time: float, threshold: float):
    """断言性能在阈值内"""
    assert execution_time <= threshold, f"执行时间 {execution_time:.3f}s 超过阈值 {threshold}s"


def assert_memory_usage_within_limit(memory_usage: float, limit_mb: float):
    """断言内存使用在限制内"""
    assert memory_usage <= limit_mb, f"内存使用 {memory_usage:.2f}MB 超过限制 {limit_mb}MB"


def assert_list_contains_items(items: list[Any], expected_count: int = None, item_validator=None):
    """断言列表包含指定数量和类型的项目"""
    if expected_count is not None:
        assert len(items) == expected_count, f"期望列表包含 {expected_count} 个项目, 实际 {len(items)} 个"

    if item_validator:
        for i, item in enumerate(items):
            try:
                item_validator(item)
            except AssertionError as e:
                pytest.fail(f"列表第 {i} 个项目验证失败: {e}")


def assert_error_contains_message(error_response: dict[str, Any], expected_message: str):
    """断言错误响应包含指定消息"""
    message = error_response.get("message", "")
    assert expected_message in message, f"错误消息 '{message}' 不包含期望的文本 '{expected_message}'"


def assert_timestamp_recent(timestamp: float, tolerance_seconds: int = 60):
    """断言时间戳是最近的"""
    import time
    current_time = time.time()
    time_diff = abs(current_time - timestamp)
    assert time_diff <= tolerance_seconds, f"时间戳 {timestamp} 与当前时间相差 {time_diff:.1f}s, 超过容忍度 {tolerance_seconds}s"  # noqa: E501


class CustomAssertions:
    """自定义断言类，用于复杂的断言逻辑"""

    @staticmethod
    def assert_rule_validation_result(result: dict[str, Any], expected_valid: bool = True):
        """断言规则验证结果"""
        assert "valid" in result, "验证结果缺少valid字段"
        assert "errors" in result, "验证结果缺少errors字段"

        if expected_valid:
            assert result["valid"] is True, f"期望验证通过，但失败: {result.get('errors')}"
            assert len(result["errors"]) == 0, f"期望无错误，但有错误: {result['errors']}"
        else:
            assert result["valid"] is False, "期望验证失败，但通过了"
            assert len(result["errors"]) > 0, "期望有验证错误，但errors为空"

    @staticmethod
    def assert_migration_status(migration_result: dict[str, Any], expected_status: str):
        """断言数据迁移状态"""
        assert "status" in migration_result, "迁移结果缺少status字段"
        assert "migrated_count" in migration_result, "迁移结果缺少migrated_count字段"
        assert "failed_count" in migration_result, "迁移结果缺少failed_count字段"

        assert migration_result["status"] == expected_status, \
            f"期望迁移状态 {expected_status}, 实际 {migration_result['status']}"

        if expected_status == "SUCCESS":
            assert migration_result["failed_count"] == 0, \
                f"期望迁移成功但有失败记录: {migration_result['failed_count']}"
