# 规则验证系统生产环境部署指南

**文档版本**: v2.0  
**适用版本**: rule_data_sets 表结构重构后的生产环境  
**创建时间**: 2025-07-14  
**更新时间**: 2025-07-14  

## 📋 概述

本指南详细说明了规则验证系统在生产环境中的部署流程，包括主节点和从节点的配置、监控设置、安全配置等。

### 系统架构

- **主节点 (Master Node)**: 负责规则管理、数据库操作、Web界面
- **从节点 (Slave Node)**: 负责高性能规则验证，支持离线模式
- **前端服务**: 提供用户界面和管理功能

### 新功能特性

- ✅ **结构化存储**: rule_data_sets 表重构为关系型结构
- ✅ **性能提升**: 查询性能提升 85-90%
- ✅ **智能数据源**: 自动选择最优数据源
- ✅ **增强缓存**: 支持新的缓存格式和混合模式
- ✅ **向后兼容**: 完全兼容现有API和数据格式

## 🔧 部署前准备

### 系统要求

#### 主节点要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 50GB以上可用空间
- **网络**: 稳定的网络连接
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 7+)

#### 从节点要求
- **CPU**: 6核心以上（高性能验证）
- **内存**: 12GB以上
- **存储**: 20GB以上可用空间
- **网络**: 可选（支持离线模式）
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 7+)

### 软件依赖

```bash
# 必需软件
- Docker 20.10+
- Docker Compose 2.0+
- curl
- jq

# 可选软件
- nginx (反向代理)
- logrotate (日志管理)
```

### 网络配置

```bash
# 主节点端口
18001  # 后端API服务
18099  # 前端Web服务

# 从节点端口
18001  # 验证服务

# 数据库端口
3306   # MySQL数据库
```

## 📦 部署步骤

### 1. 环境检查

```bash
# 执行部署前检查
./pre-deployment-check.sh master  # 主节点检查
./pre-deployment-check.sh slave   # 从节点检查
```

### 2. 配置文件准备

#### 主节点配置

```bash
# 复制配置模板
cp .env.master.example .env.master

# 编辑配置文件
vim .env.master
```

**关键配置项**:
```bash
# 数据库配置
DB_HOST=***************
DB_PORT=3306
DB_USER=rule_user
DB_PASSWORD=your_secure_password
DB_NAME=rule_service

# API安全配置
MASTER_API_SECRET_KEY=your-very-secure-secret-key-for-production

# 性能配置
WORKERS=8
MAX_CONNECTIONS=200
ENABLE_PERFORMANCE_OPTIMIZATION=true

# 新功能配置
ENABLE_STRUCTURED_STORAGE=true
ENABLE_JSON_BACKUP=true
```

#### 从节点配置

```bash
# 复制配置模板
cp .env.slave.example .env.slave

# 编辑配置文件
vim .env.slave
```

**关键配置项**:
```bash
# 主节点连接配置
MASTER_API_ENDPOINT=http://***************:18001
SLAVE_API_KEY=your-very-secure-secret-key-for-production

# 同步配置
ENABLE_RULE_SYNC=true  # 在线模式
# ENABLE_RULE_SYNC=false  # 离线模式

# 性能配置
WORKERS=8
MAX_CONNECTIONS=500
PROCESS_POOL_SIZE=8
CACHE_SIZE=5000
```

### 3. 目录结构创建

```bash
# 创建数据目录
sudo mkdir -p /data/rule-service/{master,slave,frontend}/{data,logs}
sudo mkdir -p /data/rule-service/backup
sudo mkdir -p /data/rule-service/monitoring

# 设置权限
sudo chown -R $USER:$USER /data/rule-service
sudo chmod -R 755 /data/rule-service
```

### 4. 部署执行

#### 主节点部署

```bash
# 执行主节点部署
./deploy-production.sh master prod

# 检查部署状态
docker-compose -f docker-compose.master.yml ps
docker-compose -f docker-compose.master.yml logs -f
```

#### 从节点部署

```bash
# 执行从节点部署
./deploy-production.sh slave prod

# 检查部署状态
docker-compose -f docker-compose.slave.yml ps
docker-compose -f docker-compose.slave.yml logs -f
```

### 5. 数据迁移（仅主节点）

```bash
# 进入主节点容器
docker exec -it sub-rule-master bash

# 执行数据库迁移
alembic upgrade head

# 验证迁移状态
python -c "
from services.data_management.rule_data_migration import EnhancedRuleDataMigration
migration = EnhancedRuleDataMigration()
print('迁移状态:', migration.get_migration_status())
"
```

### 6. 健康检查

```bash
# 主节点健康检查
curl -f http://localhost:18001/health
curl -f http://localhost:18099/health

# 从节点健康检查
curl -f http://localhost:18001/health

# API功能测试
curl -H "X-API-KEY: your-api-key" \
     http://localhost:18001/api/v1/rules
```

## 📊 监控配置

### 1. 监控系统设置

```bash
# 设置监控系统
./monitoring-setup.sh

# 启动监控服务
systemctl enable monitoring-service
systemctl start monitoring-service
```

### 2. 监控仪表板

```bash
# 查看监控仪表板
/data/rule-service/monitoring/scripts/dashboard.sh

# 查看实时日志
tail -f /data/rule-service/logs/monitoring/health-check.log
tail -f /data/rule-service/logs/metrics/metrics-$(date +%Y%m%d).log
```

### 3. 告警配置

```bash
# 测试告警系统
/data/rule-service/monitoring/scripts/send-alert.sh warning "测试告警消息"

# 查看告警日志
tail -f /data/rule-service/logs/alerts/alerts.log
```

## 🔒 安全配置

### 1. API密钥管理

```bash
# 生成强密钥
openssl rand -base64 48

# 更新密钥配置
vim .env.master  # 更新 MASTER_API_SECRET_KEY
vim .env.slave   # 更新 SLAVE_API_KEY
```

### 2. 防火墙配置

```bash
# 配置防火墙规则
sudo ufw allow 18001/tcp  # API端口
sudo ufw allow 18099/tcp  # 前端端口
sudo ufw allow 22/tcp     # SSH端口
sudo ufw enable
```

### 3. SSL/TLS配置

```bash
# 安装SSL证书（如果需要）
sudo mkdir -p /etc/ssl/rule-service
sudo cp server.crt /etc/ssl/rule-service/
sudo cp server.key /etc/ssl/rule-service/
sudo chmod 600 /etc/ssl/rule-service/server.key
```

## 🔄 运维操作

### 服务管理

```bash
# 启动服务
docker-compose -f docker-compose.master.yml up -d
docker-compose -f docker-compose.slave.yml up -d

# 停止服务
docker-compose -f docker-compose.master.yml down
docker-compose -f docker-compose.slave.yml down

# 重启服务
docker-compose -f docker-compose.master.yml restart
docker-compose -f docker-compose.slave.yml restart

# 查看服务状态
docker-compose -f docker-compose.master.yml ps
docker-compose -f docker-compose.slave.yml ps
```

### 日志管理

```bash
# 查看应用日志
docker-compose -f docker-compose.master.yml logs -f
docker-compose -f docker-compose.slave.yml logs -f

# 查看系统日志
tail -f /data/rule-service/logs/master/application.log
tail -f /data/rule-service/logs/slave/application.log

# 日志轮转
logrotate /etc/logrotate.d/rule-service
```

### 数据备份

```bash
# 数据库备份
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME > \
  /data/rule-service/backup/db_backup_$(date +%Y%m%d_%H%M%S).sql

# 配置文件备份
tar -czf /data/rule-service/backup/config_backup_$(date +%Y%m%d).tar.gz \
  .env.master .env.slave docker-compose.*.yml

# 应用数据备份
tar -czf /data/rule-service/backup/data_backup_$(date +%Y%m%d).tar.gz \
  /data/rule-service/master/data /data/rule-service/slave/data
```

## 🚨 故障排查

### 常见问题

#### 1. 服务启动失败

```bash
# 检查配置文件
./pre-deployment-check.sh master

# 检查端口占用
netstat -tuln | grep 18001

# 检查Docker状态
docker info
docker-compose -f docker-compose.master.yml config
```

#### 2. 数据库连接失败

```bash
# 测试数据库连接
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e "SELECT 1"

# 检查网络连接
telnet $DB_HOST $DB_PORT

# 检查防火墙
sudo ufw status
```

#### 3. 主从同步失败

```bash
# 检查主节点状态
curl -f http://$MASTER_API_ENDPOINT/health

# 检查API密钥
curl -H "X-API-KEY: $SLAVE_API_KEY" \
     $MASTER_API_ENDPOINT/api/v1/rules/version

# 查看同步日志
docker logs sub-rule-slave | grep sync
```

### 性能优化

#### 1. 数据库优化

```sql
-- 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看索引使用情况
SHOW INDEX FROM rule_details;
EXPLAIN SELECT * FROM rule_details WHERE dataset_id = 1;
```

#### 2. 应用优化

```bash
# 调整工作进程数
vim .env.master  # 修改 WORKERS
vim .env.slave   # 修改 WORKERS

# 调整连接池大小
vim .env.master  # 修改 MAX_CONNECTIONS

# 重启服务使配置生效
docker-compose restart
```

## 📋 检查清单

### 部署前检查

- [ ] 系统资源满足要求
- [ ] 软件依赖已安装
- [ ] 网络连接正常
- [ ] 配置文件已准备
- [ ] 数据库连接测试通过
- [ ] 防火墙规则已配置

### 部署后验证

- [ ] 服务启动成功
- [ ] 健康检查通过
- [ ] API接口可访问
- [ ] 前端界面正常
- [ ] 数据迁移完成
- [ ] 监控系统运行
- [ ] 告警机制正常
- [ ] 日志记录正常

### 安全检查

- [ ] API密钥已更新
- [ ] SSL证书已配置
- [ ] 防火墙规则生效
- [ ] 访问控制正常
- [ ] 审计日志启用
- [ ] 备份策略执行

## 📞 支持联系

- **技术支持**: <EMAIL>
- **运维团队**: <EMAIL>
- **紧急联系**: <EMAIL>

---

**注意**: 本指南适用于生产环境部署，请严格按照步骤执行，确保系统安全和稳定运行。
