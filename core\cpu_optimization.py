"""
CPU优化模块
实现CPU亲和性设置、NUMA感知调度和智能任务分配
"""

import os
import threading
from dataclasses import dataclass
from enum import Enum

try:
    import psutil

    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

from core.logging.logging_system import log as logger


class CPUTopology(Enum):
    """CPU拓扑类型"""

    SINGLE_SOCKET = "single_socket"
    MULTI_SOCKET = "multi_socket"
    NUMA_AWARE = "numa_aware"
    UNKNOWN = "unknown"


@dataclass
class CPUInfo:
    """CPU信息"""

    total_cores: int
    physical_cores: int
    logical_cores: int
    numa_nodes: int
    topology: CPUTopology
    core_mapping: dict[int, int]  # 逻辑核心 -> 物理核心
    numa_mapping: dict[int, int]  # 核心 -> NUMA节点


@dataclass
class TaskAffinity:
    """任务亲和性配置"""

    cpu_cores: set[int]
    numa_node: int | None
    priority: int = 0


class CPUOptimizer:
    """
    CPU优化器
    负责CPU亲和性设置、NUMA感知调度和性能优化
    """

    def __init__(self):
        """初始化CPU优化器"""
        self.cpu_info = self._detect_cpu_topology()
        self.worker_affinity: dict[int, TaskAffinity] = {}
        self._lock = threading.Lock()

        logger.info(
            f"CPU Optimizer initialized: {self.cpu_info.total_cores} cores, "
            f"{self.cpu_info.numa_nodes} NUMA nodes, topology: {self.cpu_info.topology.value}"
        )

    def _detect_cpu_topology(self) -> CPUInfo:
        """检测CPU拓扑结构"""
        total_cores = os.cpu_count() or 4

        if not PSUTIL_AVAILABLE:
            # 基本信息
            return CPUInfo(
                total_cores=total_cores,
                physical_cores=total_cores // 2,  # 估算
                logical_cores=total_cores,
                numa_nodes=1,
                topology=CPUTopology.UNKNOWN,
                core_mapping={i: i // 2 for i in range(total_cores)},
                numa_mapping={i: 0 for i in range(total_cores)},
            )

        try:
            # 获取详细CPU信息
            cpu_count_logical = psutil.cpu_count(logical=True)
            cpu_count_physical = psutil.cpu_count(logical=False)

            # 检测NUMA节点
            numa_nodes = self._detect_numa_nodes()

            # 构建核心映射
            core_mapping = {}
            numa_mapping = {}

            # 简化的映射策略
            cores_per_numa = cpu_count_logical // numa_nodes if numa_nodes > 0 else cpu_count_logical

            for i in range(cpu_count_logical):
                core_mapping[i] = i % cpu_count_physical
                numa_mapping[i] = i // cores_per_numa if cores_per_numa > 0 else 0

            # 确定拓扑类型
            if numa_nodes > 1:
                topology = CPUTopology.NUMA_AWARE
            elif cpu_count_physical > 1:
                topology = CPUTopology.MULTI_SOCKET
            else:
                topology = CPUTopology.SINGLE_SOCKET

            return CPUInfo(
                total_cores=cpu_count_logical,
                physical_cores=cpu_count_physical,
                logical_cores=cpu_count_logical,
                numa_nodes=numa_nodes,
                topology=topology,
                core_mapping=core_mapping,
                numa_mapping=numa_mapping,
            )

        except Exception as e:
            logger.warning(f"Failed to detect detailed CPU topology: {e}")
            return CPUInfo(
                total_cores=total_cores,
                physical_cores=total_cores // 2,
                logical_cores=total_cores,
                numa_nodes=1,
                topology=CPUTopology.UNKNOWN,
                core_mapping={i: i // 2 for i in range(total_cores)},
                numa_mapping={i: 0 for i in range(total_cores)},
            )

    def _detect_numa_nodes(self) -> int:
        """检测NUMA节点数量"""
        try:
            # 尝试从/proc/cpuinfo读取
            if os.path.exists("/proc/cpuinfo"):
                with open("/proc/cpuinfo", "r") as f:
                    content = f.read()
                    # 简单的NUMA检测
                    if "physical id" in content:
                        physical_ids = set()
                        for line in content.split("\n"):
                            if line.startswith("physical id"):
                                physical_ids.add(line.split(":")[1].strip())
                        return len(physical_ids) if physical_ids else 1

            # Windows或其他系统的检测
            if os.name == "nt":
                # Windows NUMA检测
                try:
                    import subprocess

                    result = subprocess.run(["wmic", "cpu", "get", "NumberOfCores"], capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split("\n")[1:]  # 跳过标题
                        return len([line for line in lines if line.strip()])
                except:
                    pass

            return 1  # 默认单NUMA节点

        except Exception as e:
            logger.warning(f"Failed to detect NUMA nodes: {e}")
            return 1

    def calculate_optimal_workers(self, workload_type: str = "cpu_intensive") -> int:
        """
        计算最优工作进程数

        Args:
            workload_type: 工作负载类型 ("cpu_intensive", "io_intensive", "mixed")

        Returns:
            最优工作进程数
        """
        if workload_type == "cpu_intensive":
            # CPU密集型：使用物理核心数
            return self.cpu_info.physical_cores
        elif workload_type == "io_intensive":
            # IO密集型：可以使用更多进程
            return min(self.cpu_info.logical_cores * 2, 32)
        else:  # mixed
            # 混合型：介于两者之间
            return min(self.cpu_info.logical_cores, 16)

    def assign_worker_affinity(self, worker_id: int, worker_count: int) -> TaskAffinity:
        """
        为工作进程分配CPU亲和性

        Args:
            worker_id: 工作进程ID
            worker_count: 总工作进程数

        Returns:
            任务亲和性配置
        """
        with self._lock:
            if worker_id in self.worker_affinity:
                return self.worker_affinity[worker_id]

            # 根据NUMA拓扑分配核心
            if self.cpu_info.topology == CPUTopology.NUMA_AWARE:
                affinity = self._assign_numa_aware_affinity(worker_id, worker_count)
            else:
                affinity = self._assign_round_robin_affinity(worker_id, worker_count)

            self.worker_affinity[worker_id] = affinity
            return affinity

    def _assign_numa_aware_affinity(self, worker_id: int, worker_count: int) -> TaskAffinity:
        """NUMA感知的亲和性分配"""
        # 计算每个NUMA节点的工作进程数
        workers_per_numa = max(1, worker_count // self.cpu_info.numa_nodes)
        numa_node = worker_id // workers_per_numa
        numa_node = min(numa_node, self.cpu_info.numa_nodes - 1)

        # 获取该NUMA节点的核心
        numa_cores = [core for core, node in self.cpu_info.numa_mapping.items() if node == numa_node]

        # 为该工作进程分配核心
        cores_per_worker = max(1, len(numa_cores) // workers_per_numa)
        start_core = (worker_id % workers_per_numa) * cores_per_worker
        end_core = min(start_core + cores_per_worker, len(numa_cores))

        assigned_cores = set(numa_cores[start_core:end_core])

        return TaskAffinity(cpu_cores=assigned_cores, numa_node=numa_node, priority=0)

    def _assign_round_robin_affinity(self, worker_id: int, worker_count: int) -> TaskAffinity:
        """轮询方式的亲和性分配"""
        # 简单的轮询分配
        cores_per_worker = max(1, self.cpu_info.total_cores // worker_count)
        start_core = worker_id * cores_per_worker
        end_core = min(start_core + cores_per_worker, self.cpu_info.total_cores)

        assigned_cores = set(range(start_core, end_core))

        return TaskAffinity(cpu_cores=assigned_cores, numa_node=None, priority=0)

    def apply_worker_affinity(self, worker_id: int, process_id: int | None = None):
        """
        应用工作进程的CPU亲和性

        Args:
            worker_id: 工作进程ID
            process_id: 进程ID（可选，默认为当前进程）
        """
        if not PSUTIL_AVAILABLE:
            logger.debug("psutil not available, skipping CPU affinity setting")
            return

        try:
            affinity = self.worker_affinity.get(worker_id)
            if not affinity:
                logger.warning(f"No affinity configuration for worker {worker_id}")
                return

            # 获取进程对象
            if process_id:
                process = psutil.Process(process_id)
            else:
                process = psutil.Process()

            # 设置CPU亲和性
            if affinity.cpu_cores:
                process.cpu_affinity(list(affinity.cpu_cores))
                logger.debug(f"Set CPU affinity for worker {worker_id}: {affinity.cpu_cores}")

            # 设置进程优先级（如果支持）
            if affinity.priority != 0:
                try:
                    if os.name == "nt":  # Windows
                        priority_class = (
                            psutil.HIGH_PRIORITY_CLASS if affinity.priority > 0 else psutil.NORMAL_PRIORITY_CLASS
                        )
                        process.nice(priority_class)
                    else:  # Unix-like
                        process.nice(affinity.priority)
                except:
                    pass  # 忽略优先级设置失败

        except Exception as e:
            logger.warning(f"Failed to apply CPU affinity for worker {worker_id}: {e}")

    def get_cpu_usage_by_core(self) -> dict[int, float]:
        """获取各CPU核心的使用率"""
        if not PSUTIL_AVAILABLE:
            return {}

        try:
            cpu_percent = psutil.cpu_percent(percpu=True, interval=0.1)
            return {i: usage for i, usage in enumerate(cpu_percent)}
        except Exception as e:
            logger.warning(f"Failed to get CPU usage by core: {e}")
            return {}

    def get_numa_memory_usage(self) -> dict[int, dict[str, float]]:
        """获取NUMA节点的内存使用情况"""
        # 简化实现，实际可能需要更复杂的NUMA内存检测
        if not PSUTIL_AVAILABLE:
            return {}

        try:
            memory = psutil.virtual_memory()
            # 简化：假设内存均匀分布在NUMA节点上
            memory_per_numa = memory.total / self.cpu_info.numa_nodes
            used_per_numa = memory.used / self.cpu_info.numa_nodes

            return {
                node: {
                    "total": memory_per_numa,
                    "used": used_per_numa,
                    "percent": (used_per_numa / memory_per_numa) * 100,
                }
                for node in range(self.cpu_info.numa_nodes)
            }
        except Exception as e:
            logger.warning(f"Failed to get NUMA memory usage: {e}")
            return {}

    def optimize_for_workload(self, workload_type: str) -> dict[str, any]:
        """
        为特定工作负载优化CPU配置

        Args:
            workload_type: 工作负载类型

        Returns:
            优化建议
        """
        optimal_workers = self.calculate_optimal_workers(workload_type)

        recommendations = {
            "optimal_workers": optimal_workers,
            "cpu_topology": self.cpu_info.topology.value,
            "numa_nodes": self.cpu_info.numa_nodes,
            "total_cores": self.cpu_info.total_cores,
            "physical_cores": self.cpu_info.physical_cores,
        }

        # 根据工作负载类型提供具体建议
        if workload_type == "cpu_intensive":
            recommendations.update(
                {
                    "use_cpu_affinity": True,
                    "prefer_physical_cores": True,
                    "numa_aware": self.cpu_info.numa_nodes > 1,
                }
            )
        elif workload_type == "io_intensive":
            recommendations.update(
                {
                    "use_cpu_affinity": False,
                    "allow_oversubscription": True,
                    "numa_aware": False,
                }
            )

        return recommendations

    def get_optimization_stats(self) -> dict[str, any]:
        """获取优化统计信息"""
        return {
            "cpu_info": {
                "total_cores": self.cpu_info.total_cores,
                "physical_cores": self.cpu_info.physical_cores,
                "logical_cores": self.cpu_info.logical_cores,
                "numa_nodes": self.cpu_info.numa_nodes,
                "topology": self.cpu_info.topology.value,
            },
            "worker_affinity_count": len(self.worker_affinity),
            "psutil_available": PSUTIL_AVAILABLE,
        }


# 全局CPU优化器实例
cpu_optimizer = CPUOptimizer()
