<template>
  <div class="registration-steps">
    <!-- 步骤指示器 -->
    <el-steps
      :active="currentStep"
      :status="stepStatus"
      align-center
      class="steps-container"
    >
      <el-step
        title="选择文件"
        description="上传Excel文件"
        :icon="Upload"
      />
      <el-step
        title="数据预览"
        description="验证数据格式"
        :icon="View"
      />
      <el-step
        title="确认提交"
        description="提交数据到系统"
        :icon="Check"
      />
      <el-step
        title="注册处理"
        description="后台处理注册任务"
        :icon="Loading"
      />
      <el-step
        title="完成"
        description="注册任务完成"
        :icon="CircleCheck"
      />
    </el-steps>

    <!-- 当前步骤详情 -->
    <div class="step-content" v-if="showStepContent">
      <el-card shadow="hover" class="step-detail-card">
        <template #header>
          <div class="step-header">
            <el-icon :size="20" class="step-icon">
              <component :is="currentStepIcon" />
            </el-icon>
            <span class="step-title">{{ currentStepTitle }}</span>
          </div>
        </template>

        <!-- 步骤内容 -->
        <div class="step-body">
          <!-- 文件上传步骤 -->
          <div v-if="currentStep === 0" class="step-upload">
            <el-empty
              v-if="!hasFile"
              description="请选择要上传的Excel文件"
              :image-size="80"
            >
              <template #image>
                <el-icon :size="80" color="var(--el-color-info)">
                  <Upload />
                </el-icon>
              </template>
            </el-empty>
            <div v-else class="file-info">
              <el-icon :size="24" color="var(--el-color-success)">
                <Document />
              </el-icon>
              <span class="file-name">{{ fileName }}</span>
              <el-tag type="success" size="small">已选择</el-tag>
            </div>
          </div>

          <!-- 数据预览步骤 -->
          <div v-else-if="currentStep === 1" class="step-preview">
            <div class="preview-stats">
              <el-statistic
                title="有效数据"
                :value="validCount"
                suffix="条"
                value-style="color: var(--el-color-success)"
              />
              <el-statistic
                title="无效数据"
                :value="invalidCount"
                suffix="条"
                value-style="color: var(--el-color-danger)"
              />
              <el-statistic
                title="总计"
                :value="totalCount"
                suffix="条"
                value-style="color: var(--el-color-primary)"
              />
            </div>
          </div>

          <!-- 确认提交步骤 -->
          <div v-else-if="currentStep === 2" class="step-confirm">
            <el-alert
              title="确认提交"
              :description="`即将提交 ${validCount} 条有效数据到系统`"
              type="info"
              show-icon
              :closable="false"
            />
          </div>

          <!-- 注册处理步骤 -->
          <div v-else-if="currentStep === 3" class="step-processing">
            <div class="processing-content">
              <el-progress
                :percentage="progress"
                :status="progressStatus"
                :stroke-width="8"
                class="progress-bar"
              />
              <div class="progress-info">
                <span class="progress-text">{{ progressMessage }}</span>
                <span class="progress-detail">{{ progressDetail }}</span>
              </div>
            </div>
          </div>

          <!-- 完成步骤 -->
          <div v-else-if="currentStep === 4" class="step-complete">
            <el-result
              :icon="resultIcon"
              :title="resultTitle"
              :sub-title="resultMessage"
            >
              <template #extra>
                <el-button type="primary" @click="$emit('restart')">
                  重新开始
                </el-button>
                <el-button @click="$emit('back-to-dashboard')">
                  返回仪表盘
                </el-button>
              </template>
            </el-result>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  Upload,
  View,
  Check,
  Loading,
  CircleCheck,
  Document
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 当前步骤 (0-4)
  currentStep: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 4
  },
  // 步骤状态
  stepStatus: {
    type: String,
    default: 'process',
    validator: (value) => ['wait', 'process', 'finish', 'error', 'success'].includes(value)
  },
  // 是否显示步骤内容
  showStepContent: {
    type: Boolean,
    default: true
  },
  // 文件信息
  hasFile: {
    type: Boolean,
    default: false
  },
  fileName: {
    type: String,
    default: ''
  },
  // 数据统计
  validCount: {
    type: Number,
    default: 0
  },
  invalidCount: {
    type: Number,
    default: 0
  },
  totalCount: {
    type: Number,
    default: 0
  },
  // 进度信息
  progress: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100
  },
  progressStatus: {
    type: String,
    default: '',
    validator: (value) => ['', 'success', 'warning', 'exception'].includes(value)
  },
  progressMessage: {
    type: String,
    default: '正在处理...'
  },
  progressDetail: {
    type: String,
    default: ''
  },
  // 结果信息
  isSuccess: {
    type: Boolean,
    default: true
  },
  resultMessage: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['restart', 'back-to-dashboard'])

// 计算属性
const stepTitles = ['选择文件', '数据预览', '确认提交', '注册处理', '完成']
const stepIcons = [Upload, View, Check, Loading, CircleCheck]

const currentStepTitle = computed(() => stepTitles[props.currentStep] || '未知步骤')
const currentStepIcon = computed(() => stepIcons[props.currentStep] || Upload)

const resultIcon = computed(() => props.isSuccess ? 'success' : 'error')
const resultTitle = computed(() => {
  // 根据具体的注册结果显示更详细的标题
  if (props.isSuccess) {
    return '注册任务完成'
  } else {
    return '注册任务失败'
  }
})
</script>

<style scoped>
.registration-steps {
  width: 100%;
}

.steps-container {
  margin-bottom: 24px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
}

.step-content {
  margin-top: 16px;
}

.step-detail-card {
  border-radius: 8px;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-icon {
  color: var(--el-color-primary);
}

.step-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.step-body {
  padding: 16px 0;
}

/* 文件上传步骤样式 */
.step-upload .file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--el-color-success-light-9);
  border-radius: 6px;
  border: 1px solid var(--el-color-success-light-7);
}

.file-name {
  flex: 1;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

/* 数据预览步骤样式 */
.preview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 24px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

/* 注册处理步骤样式 */
.step-processing {
  text-align: center;
}

.processing-content {
  max-width: 400px;
  margin: 0 auto;
}

.progress-bar {
  margin-bottom: 16px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.progress-detail {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

/* 完成步骤样式 */
.step-complete {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .steps-container {
    padding: 16px;
  }

  .preview-stats {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .processing-content {
    max-width: 100%;
  }
}
</style>
