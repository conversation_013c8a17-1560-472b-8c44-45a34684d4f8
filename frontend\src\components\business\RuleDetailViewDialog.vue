<template>
  <el-dialog
    v-model="visible"
    title="规则明细详情"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="detailData" class="detail-view">
      <!-- 基础信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">基础信息</span>
            <StatusTag :status="detailData.status" />
          </div>
        </template>

        <div class="info-grid">
          <div class="info-item">
            <span class="label">明细ID:</span>
            <span class="value">{{ detailData.rule_detail_id }}</span>
          </div>
          <div class="info-item">
            <span class="label">规则名称:</span>
            <span class="value">{{ detailData.rule_name }}</span>
          </div>
          <div class="info-item">
            <span class="label">规则类别:</span>
            <span class="value">{{ detailData.type || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">默认选用:</span>
            <span class="value">
              <el-tag :type="detailData.default_use ? 'success' : 'info'" size="small">
                {{ detailData.default_use ? '是' : '否' }}
              </el-tag>
            </span>
          </div>
          <div class="info-item">
            <span class="label">涉及金额:</span>
            <span class="value">
              {{ detailData.involved_amount ? `¥${formatAmount(detailData.involved_amount)}` : '-' }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">适用业务:</span>
            <span class="value">{{ detailData.pos || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">适用地区:</span>
            <span class="value">{{ detailData.applicableArea || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDate(detailData.created_at) }}</span>
          </div>
          <div class="info-item">
            <span class="label">更新时间:</span>
            <span class="value">{{ formatDate(detailData.updated_at) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 错误分类信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">错误分类</span>
        </template>

        <div class="error-levels">
          <div class="error-level-item">
            <span class="level-label">一级错误:</span>
            <el-tag v-if="detailData.level1" type="danger" size="small">
              {{ detailData.level1 }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </div>
          <div class="error-level-item">
            <span class="level-label">二级错误:</span>
            <el-tag v-if="detailData.level2" type="warning" size="small">
              {{ detailData.level2 }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </div>
          <div class="error-level-item">
            <span class="level-label">三级错误:</span>
            <el-tag v-if="detailData.level3" type="info" size="small">
              {{ detailData.level3 }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </div>
          <div class="error-level-item">
            <span class="level-label">错误程度:</span>
            <el-tag
              v-if="detailData.degree"
              :type="getSeverityTagType(detailData.degree)"
              size="small"
            >
              {{ getSeverityLabel(detailData.degree) }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </div>
        </div>

        <div v-if="detailData.error_reason" class="error-reason">
          <div class="reason-label">错误原因:</div>
          <div class="reason-content">{{ detailData.error_reason }}</div>
        </div>
      </el-card>

      <!-- 质控信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">质控信息</span>
        </template>

        <div class="quality-info">
          <div v-if="detailData.reference" class="quality-item">
            <div class="quality-label">质控依据:</div>
            <div class="quality-content">{{ detailData.reference }}</div>
          </div>

          <div class="info-grid">
            <div class="info-item">
              <span class="label">位置描述:</span>
              <span class="value">{{ detailData.detail_position || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">提示字段类型:</span>
              <span class="value">{{ detailData.prompted_fields3 || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">提示字段编码:</span>
              <span class="value">{{ detailData.prompted_fields1 || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">提示字段序号:</span>
              <span class="value">{{ detailData.prompted_fields2 || '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 统计信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">统计信息</span>
        </template>

        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ detailData.usage_quantity || 0 }}</div>
            <div class="stat-label">使用数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ detailData.violation_quantity || 0 }}</div>
            <div class="stat-label">违规数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ detailData.usage_days || 0 }}</div>
            <div class="stat-label">使用天数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ detailData.violation_days || 0 }}</div>
            <div class="stat-label">违规天数</div>
          </div>
        </div>

        <div v-if="detailData.violation_items" class="violation-items">
          <div class="violation-label">违规项目:</div>
          <div class="violation-content">{{ detailData.violation_items }}</div>
        </div>
      </el-card>

      <!-- 备注信息 -->
      <el-card v-if="detailData.remark" class="info-card" shadow="never">
        <template #header>
          <span class="card-title">备注信息</span>
        </template>

        <div class="remark-content">
          {{ detailData.remark }}
        </div>
      </el-card>
    </div>

    <div v-else class="no-data-view">
      <el-empty description="暂无数据" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import StatusTag from '../common/StatusTag.vue'
import { formatDate } from '../../utils/dateUtils'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit'])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法定义
const handleClose = () => {
  emit('update:modelValue', false)
}

const handleEdit = () => {
  emit('edit', props.detailData)
  handleClose()
}

const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const getSeverityTagType = (severity) => {
  const typeMap = {
    'minor': 'success',
    'normal': 'info',
    'serious': 'warning',
    'critical': 'danger'
  }
  return typeMap[severity] || 'info'
}

const getSeverityLabel = (severity) => {
  const labelMap = {
    'minor': '轻微',
    'normal': '一般',
    'serious': '严重',
    'critical': '致命'
  }
  return labelMap[severity] || severity
}
</script>

<style scoped>
.detail-view {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.info-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.label {
  min-width: 100px;
  color: #909399;
  font-size: 14px;
  margin-right: 12px;
}

.value {
  color: #303133;
  font-size: 14px;
  flex: 1;
  word-break: break-all;
}

.no-data {
  color: #c0c4cc;
}

/* 错误分类样式 */
.error-levels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.error-level-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.level-label {
  min-width: 80px;
  color: #909399;
  font-size: 14px;
}

.error-reason {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.reason-label {
  color: #909399;
  font-size: 14px;
  margin-bottom: 8px;
}

.reason-content {
  color: #303133;
  font-size: 14px;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

/* 质控信息样式 */
.quality-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quality-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quality-label {
  color: #909399;
  font-size: 14px;
}

.quality-content {
  color: #303133;
  font-size: 14px;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

/* 统计信息样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.violation-items {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.violation-label {
  color: #909399;
  font-size: 14px;
  margin-bottom: 8px;
}

.violation-content {
  color: #303133;
  font-size: 14px;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

/* 备注信息样式 */
.remark-content {
  color: #303133;
  font-size: 14px;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.no-data-view {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
