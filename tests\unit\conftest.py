"""
单元测试专用配置
提供单元测试所需的fixtures和配置
"""

from unittest.mock import Mock

import pytest

# ============================================================================
# 单元测试专用fixtures
# ============================================================================

@pytest.fixture
def mock_db_session():
    """模拟数据库会话（单元测试专用）"""
    session = Mock()
    session.query.return_value = Mock()
    session.add.return_value = None
    session.commit.return_value = None
    session.rollback.return_value = None
    session.close.return_value = None
    return session


@pytest.fixture
def mock_service_dependencies():
    """模拟服务层依赖"""
    return {
        "db_session": Mock(),
        "logger": Mock(),
        "cache": Mock(),
        "config": Mock(),
    }


@pytest.fixture
def mock_api_dependencies():
    """模拟API层依赖"""
    return {
        "db_session": Mock(),
        "current_user": Mock(),
        "auth_service": Mock(),
    }


# ============================================================================
# 单元测试配置
# ============================================================================

@pytest.fixture(autouse=True)
def unit_test_setup():
    """单元测试自动设置"""
    # 单元测试不需要真实的数据库连接
    # 所有外部依赖都应该被模拟
    yield
