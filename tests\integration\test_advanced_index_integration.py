"""
高级索引引擎集成测试
测试多类型索引引擎与现有规则系统的集成

测试场景：
1. 与RuleIndexManager的集成
2. 真实规则数据的索引构建和查询
3. 性能基准测试
4. 大数据量场景测试
5. 并发访问场景测试
"""

import concurrent.futures
import random
import sys
import time
import unittest
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

# 添加项目根目录到sys.path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from core.advanced_index_engine import (
    BPlusTreeIndex,
    CompressedTrieIndex,
    HashIndex,
    InvertedIndex,
    MultiTypeIndexEngine,
    advanced_index_engine,
)


class TestAdvancedIndexIntegration(unittest.TestCase):
    """高级索引引擎集成测试"""
    
    def setUp(self):
        self.engine = MultiTypeIndexEngine()
        
        # 模拟真实的医疗规则数据
        self.medical_data = {
            # 医保代码
            'A0001': {'rule_yb_001', 'rule_yb_002'},
            'A0002': {'rule_yb_002', 'rule_yb_003'},
            'A0003': {'rule_yb_001', 'rule_yb_003'},
            'B1001': {'rule_yb_004', 'rule_yb_005'},
            'B1002': {'rule_yb_005', 'rule_yb_006'},
            
            # 诊断代码（ICD-10）
            'I21.0': {'rule_diag_001', 'rule_diag_002'},
            'I21.1': {'rule_diag_002', 'rule_diag_003'},
            'I21.9': {'rule_diag_001', 'rule_diag_003'},
            'J44.0': {'rule_diag_004', 'rule_diag_005'},
            'J44.1': {'rule_diag_005', 'rule_diag_006'},
            
            # 手术代码
            'SS01.001': {'rule_surgery_001', 'rule_surgery_002'},
            'SS02.001': {'rule_surgery_002', 'rule_surgery_003'},
            'SS02.002': {'rule_surgery_003', 'rule_surgery_004'},
        }
        
        # 大数据集用于性能测试
        self.large_dataset = self._generate_large_dataset(1000)
    
    def tearDown(self):
        self.engine.clear_all_indexes()
    
    def _generate_large_dataset(self, size: int) -> dict:
        """生成大数据集用于性能测试"""
        dataset = {}
        
        for i in range(size):
            # 生成不同类型的医疗代码
            if i % 3 == 0:
                key = f"A{i:04d}"  # 医保代码
            elif i % 3 == 1:
                key = f"I{i//10:02d}.{i%10}"  # 诊断代码
            else:
                key = f"SS{i//100:02d}.{i%100:03d}"  # 手术代码
            
            # 每个键关联1-5个规则
            rule_count = random.randint(1, 5)
            rules = {f"rule_{i}_{j}" for j in range(rule_count)}
            dataset[key] = rules
        
        return dataset
    
    def test_medical_data_hash_index(self):
        """测试医疗数据的哈希索引功能"""
        index = self.engine.create_index('medical_hash', self.medical_data, index_type='hash')
        
        # 测试医保代码查询
        result = self.engine.query_index('medical_hash', 'A0001')
        self.assertEqual(result, {'rule_yb_001', 'rule_yb_002'})
        
        # 测试诊断代码查询
        result = self.engine.query_index('medical_hash', 'I21.0')
        self.assertEqual(result, {'rule_diag_001', 'rule_diag_002'})
        
        # 测试手术代码查询
        result = self.engine.query_index('medical_hash', 'SS01.001')
        self.assertEqual(result, {'rule_surgery_001', 'rule_surgery_002'})
        
        # 测试不存在的代码
        result = self.engine.query_index('medical_hash', 'INVALID')
        self.assertEqual(result, set())
    
    def test_medical_data_btree_index(self):
        """测试医疗数据的B+树索引功能"""
        index = self.engine.create_index('medical_btree', self.medical_data, index_type='btree')
        
        # 精确查询
        result = self.engine.query_index('medical_btree', 'A0001')
        self.assertEqual(result, {'rule_yb_001', 'rule_yb_002'})
        
        # 前缀查询 - A类医保代码
        result = self.engine.prefix_query_index('medical_btree', 'A')
        expected = {'rule_yb_001', 'rule_yb_002', 'rule_yb_003'}
        self.assertEqual(result, expected)
        
        # 前缀查询 - I21类诊断代码
        result = self.engine.prefix_query_index('medical_btree', 'I21')
        expected = {'rule_diag_001', 'rule_diag_002', 'rule_diag_003'}
        self.assertEqual(result, expected)
        
        # 前缀查询 - SS02类手术代码
        result = self.engine.prefix_query_index('medical_btree', 'SS02')
        expected = {'rule_surgery_002', 'rule_surgery_003', 'rule_surgery_004'}
        self.assertEqual(result, expected)
    
    def test_medical_data_trie_index(self):
        """测试医疗数据的Trie索引功能"""
        index = self.engine.create_index('medical_trie', self.medical_data, index_type='trie')
        
        # 精确查询
        result = self.engine.query_index('medical_trie', 'I21.0')
        self.assertEqual(result, {'rule_diag_001', 'rule_diag_002'})
        
        # 前缀查询测试各种医疗代码前缀
        test_cases = [
            ('A', {'rule_yb_001', 'rule_yb_002', 'rule_yb_003'}),
            ('B1', {'rule_yb_004', 'rule_yb_005', 'rule_yb_006'}),
            ('I21', {'rule_diag_001', 'rule_diag_002', 'rule_diag_003'}),
            ('J44', {'rule_diag_004', 'rule_diag_005', 'rule_diag_006'}),
            ('SS', {'rule_surgery_001', 'rule_surgery_002', 'rule_surgery_003', 'rule_surgery_004'}),
        ]
        
        for prefix, expected in test_cases:
            with self.subTest(prefix=prefix):
                result = self.engine.prefix_query_index('medical_trie', prefix)
                self.assertEqual(result, expected)
    
    def test_medical_data_inverted_index(self):
        """测试医疗数据的倒排索引功能"""
        index = self.engine.create_index('medical_inverted', self.medical_data, index_type='inverted')
        
        # 正向查询
        result = self.engine.query_index('medical_inverted', 'A0001')
        self.assertEqual(result, {'rule_yb_001', 'rule_yb_002'})
        
        # 测试倒排查询（需要直接调用索引对象）
        inverted_index = self.engine.indexes['medical_inverted']
        
        # 根据规则查找相关代码
        result = inverted_index.reverse_query('rule_yb_001')
        self.assertEqual(result, {'A0001', 'A0003'})
        
        result = inverted_index.reverse_query('rule_diag_002')
        self.assertEqual(result, {'I21.0', 'I21.1'})
        
        # 多值查询 - OR操作
        result = inverted_index.multi_value_query(['rule_yb_001', 'rule_yb_002'], 'OR')
        expected = {'A0001', 'A0002', 'A0003'}
        self.assertEqual(result, expected)
        
        # 多值查询 - AND操作
        result = inverted_index.multi_value_query(['rule_yb_001', 'rule_yb_002'], 'AND')
        expected = {'A0001'}
        self.assertEqual(result, expected)
    
    def test_adaptive_index_selection_for_medical_data(self):
        """测试医疗数据的自适应索引选择"""
        # 不同查询模式下的索引选择
        test_scenarios = [
            # 精确查询为主的场景
            {
                'patterns': {'exact': 0.8, 'prefix': 0.1, 'range': 0.05, 'multivalue': 0.05},
                'expected_types': (HashIndex,)
            },
            # 前缀查询为主的场景
            {
                'patterns': {'exact': 0.3, 'prefix': 0.6, 'range': 0.05, 'multivalue': 0.05},
                'expected_types': (CompressedTrieIndex, BPlusTreeIndex)
            },
            # 范围查询需求的场景
            {
                'patterns': {'exact': 0.4, 'prefix': 0.2, 'range': 0.3, 'multivalue': 0.1},
                'expected_types': (BPlusTreeIndex,)
            },
            # 多值查询为主的场景
            {
                'patterns': {'exact': 0.2, 'prefix': 0.1, 'range': 0.1, 'multivalue': 0.6},
                'expected_types': (InvertedIndex,)
            }
        ]
        
        for i, scenario in enumerate(test_scenarios):
            with self.subTest(scenario=i):
                index_name = f'adaptive_test_{i}'
                index = self.engine.create_index(
                    index_name, 
                    self.medical_data, 
                    query_patterns=scenario['patterns']
                )
                
                self.assertIsInstance(index, scenario['expected_types'])
                
                # 验证基本功能
                result = self.engine.query_index(index_name, 'A0001')
                self.assertEqual(result, {'rule_yb_001', 'rule_yb_002'})
    
    @pytest.mark.performance
    def test_large_dataset_performance(self):
        """测试大数据集性能"""
        # 跳过性能测试，除非明确要求
        if not hasattr(self, '_run_performance_test'):
            self.skipTest("Performance test skipped")
        
        # 测试不同索引类型的构建性能
        index_types = ['hash', 'btree', 'inverted', 'trie']
        performance_results = {}
        
        for index_type in index_types:
            start_time = time.perf_counter()
            
            index = self.engine.create_index(
                f'perf_test_{index_type}', 
                self.large_dataset, 
                index_type=index_type
            )
            
            build_time = time.perf_counter() - start_time
            
            # 测试查询性能
            query_start = time.perf_counter()
            
            # 执行100次随机查询
            sample_keys = random.sample(list(self.large_dataset.keys()), min(100, len(self.large_dataset)))
            for key in sample_keys:
                self.engine.query_index(f'perf_test_{index_type}', key)
            
            query_time = time.perf_counter() - query_start
            
            stats = self.engine.get_index_stats(f'perf_test_{index_type}')
            
            performance_results[index_type] = {
                'build_time': build_time,
                'query_time': query_time,
                'memory_mb': stats.memory_usage_mb,
                'avg_query_us': stats.avg_query_time_us
            }
        
        # 输出性能结果用于分析
        print("\n=== 大数据集性能测试结果 ===")
        for index_type, results in performance_results.items():
            print(f"{index_type:10}: 构建 {results['build_time']*1000:6.2f}ms, "
                  f"查询 {results['query_time']*1000:6.2f}ms, "
                  f"内存 {results['memory_mb']:6.2f}MB, "
                  f"平均查询 {results['avg_query_us']:6.2f}μs")
        
        # 基本性能断言
        for index_type, results in performance_results.items():
            with self.subTest(index_type=index_type):
                # 构建时间应该合理（小于10秒）
                self.assertLess(results['build_time'], 10.0)
                
                # 内存使用应该合理（小于100MB）
                self.assertLess(results['memory_mb'], 100.0)
                
                # 平均查询时间应该很快（小于1000微秒）
                self.assertLess(results['avg_query_us'], 1000.0)
    
    def test_concurrent_access(self):
        """测试并发访问场景"""
        # 创建索引
        index = self.engine.create_index('concurrent_test', self.medical_data, index_type='hash')
        
        def query_worker(thread_id: int) -> dict:
            """工作线程函数"""
            results = {}
            sample_keys = ['A0001', 'I21.0', 'SS01.001', 'B1001', 'J44.0']
            
            for i, key in enumerate(sample_keys):
                result = self.engine.query_index('concurrent_test', key)
                results[f'{thread_id}_{i}'] = len(result)
            
            return results
        
        # 使用线程池执行并发查询
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(query_worker, i) for i in range(10)]
            
            all_results = []
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                all_results.append(result)
        
        # 验证并发查询结果的一致性
        self.assertEqual(len(all_results), 10)
        
        # 检查每个线程的结果是否一致
        expected_counts = {
            '0': 2,  # A0001 -> 2 rules
            '1': 2,  # I21.0 -> 2 rules
            '2': 2,  # SS01.001 -> 2 rules
            '3': 2,  # B1001 -> 2 rules
            '4': 2,  # J44.0 -> 2 rules
        }
        
        for result in all_results:
            for key, count in result.items():
                expected_key = key.split('_')[1]
                self.assertEqual(count, expected_counts[expected_key])
    
    def test_memory_usage_optimization(self):
        """测试内存使用优化"""
        # 创建不同类型的索引并比较内存使用
        memory_results = {}
        
        for index_type in ['hash', 'btree', 'inverted', 'trie']:
            index = self.engine.create_index(
                f'memory_test_{index_type}', 
                self.medical_data, 
                index_type=index_type
            )
            
            stats = self.engine.get_index_stats(f'memory_test_{index_type}')
            memory_results[index_type] = stats.memory_usage_mb
        
        # 验证内存使用在合理范围内
        for index_type, memory_mb in memory_results.items():
            with self.subTest(index_type=index_type):
                # 所有索引的内存使用都应该小于10MB（对于这个小数据集）
                self.assertLess(memory_mb, 10.0)
                self.assertGreater(memory_mb, 0.0)
        
        print(f"\n=== 内存使用比较 ===")
        for index_type, memory_mb in memory_results.items():
            print(f"{index_type:10}: {memory_mb:.3f} MB")
    
    def test_index_rebuild_and_update(self):
        """测试索引重建和更新"""
        # 创建初始索引
        initial_data = {
            'A0001': {'rule1'},
            'A0002': {'rule2'},
        }
        
        index = self.engine.create_index('rebuild_test', initial_data, index_type='hash')
        
        # 验证初始数据
        result = self.engine.query_index('rebuild_test', 'A0001')
        self.assertEqual(result, {'rule1'})
        
        # 更新数据并重建索引
        updated_data = {
            'A0001': {'rule1', 'rule1_updated'},
            'A0002': {'rule2', 'rule2_updated'},
            'A0003': {'rule3'},  # 新增
        }
        
        # 重建索引
        new_index = self.engine.create_index('rebuild_test', updated_data, index_type='hash')
        
        # 验证更新后的数据
        result = self.engine.query_index('rebuild_test', 'A0001')
        self.assertEqual(result, {'rule1', 'rule1_updated'})
        
        result = self.engine.query_index('rebuild_test', 'A0003')
        self.assertEqual(result, {'rule3'})
    
    def test_error_handling_and_robustness(self):
        """测试错误处理和健壮性"""
        # 测试异常数据处理
        problematic_data = {
            '': {'rule1'},  # 空键
            'normal_key': set(),  # 空值集合
            'another_key': {'rule2'},
        }
        
        # 创建索引时应该能处理这些异常数据
        index = self.engine.create_index('robust_test', problematic_data, index_type='hash')
        
        # 查询正常键
        result = self.engine.query_index('robust_test', 'another_key')
        self.assertEqual(result, {'rule2'})
        
        # 查询空键
        result = self.engine.query_index('robust_test', '')
        self.assertEqual(result, {'rule1'})
        
        # 查询有空值集合的键
        result = self.engine.query_index('robust_test', 'normal_key')
        self.assertEqual(result, set())
    
    def test_integration_with_existing_rule_system(self):
        """测试与现有规则系统的集成"""
        # 模拟与RuleIndexManager的集成
        with patch('core.rule_index_manager.rule_index_manager') as mock_rule_manager:
            mock_rule_manager.is_ready.return_value = True
            mock_rule_manager.get_performance_stats.return_value = {
                'query_count': 100,
                'avg_query_time_ms': 2.5
            }
            
            # 创建高级索引作为补充
            enhanced_index = self.engine.create_index(
                'enhanced_medical', 
                self.medical_data, 
                index_type='trie'  # 选择Trie以支持前缀查询
            )
            
            # 验证集成功能
            self.assertIsInstance(enhanced_index, CompressedTrieIndex)
            
            # 测试前缀查询功能（现有系统可能不支持）
            result = self.engine.prefix_query_index('enhanced_medical', 'A')
            self.assertGreater(len(result), 0)
            
            # 验证性能统计
            stats = self.engine.get_index_stats('enhanced_medical')
            self.assertIsNotNone(stats)


class TestAdvancedIndexPerformanceBenchmark(unittest.TestCase):
    """高级索引性能基准测试"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        cls.engine = MultiTypeIndexEngine()
        
        # 生成不同规模的测试数据
        cls.small_dataset = cls._generate_medical_dataset(100)
        cls.medium_dataset = cls._generate_medical_dataset(1000)
        cls.large_dataset = cls._generate_medical_dataset(5000)
    
    @classmethod
    def tearDownClass(cls):
        """清理测试类"""
        cls.engine.clear_all_indexes()
    
    @classmethod
    def _generate_medical_dataset(cls, size: int) -> dict:
        """生成医疗数据集"""
        dataset = {}
        
        for i in range(size):
            # 生成真实风格的医疗代码
            code_type = i % 4
            
            if code_type == 0:
                key = f"A{i%1000:03d}.{i%100:02d}"  # 医保代码风格
            elif code_type == 1:
                key = f"I{i%50:02d}.{i%10}"  # ICD-10诊断代码
            elif code_type == 2:
                key = f"SS{i%20:02d}.{i%1000:03d}"  # 手术代码
            else:
                key = f"M{i%100:02d}{chr(65 + i%26)}{i%10:02d}"  # 药品代码
            
            # 每个代码关联的规则数量符合真实分布
            if i % 10 == 0:
                rule_count = random.randint(5, 10)  # 10%的代码有很多规则
            elif i % 5 == 0:
                rule_count = random.randint(3, 5)   # 20%的代码有中等数量规则
            else:
                rule_count = random.randint(1, 3)   # 70%的代码有少量规则
            
            rules = {f"rule_{rule_type}_{i}_{j}" 
                    for j in range(rule_count) 
                    for rule_type in ['yb', 'diag', 'drug'][:rule_count//2+1]}
            
            dataset[key] = rules
        
        return dataset
    
    @pytest.mark.performance
    def test_build_performance_comparison(self):
        """比较不同索引类型的构建性能"""
        if not hasattr(self, '_run_performance_test'):
            self.skipTest("Performance test skipped")
        
        datasets = [
            ('small', self.small_dataset),
            ('medium', self.medium_dataset),
            ('large', self.large_dataset)
        ]
        
        index_types = ['hash', 'btree', 'inverted', 'trie']
        
        results = {}
        
        for dataset_name, dataset in datasets:
            results[dataset_name] = {}
            
            for index_type in index_types:
                # 重复测试3次取平均值
                build_times = []
                
                for run in range(3):
                    start_time = time.perf_counter()
                    
                    index_name = f'perf_{dataset_name}_{index_type}_{run}'
                    self.engine.create_index(index_name, dataset, index_type=index_type)
                    
                    build_time = time.perf_counter() - start_time
                    build_times.append(build_time)
                    
                    # 清理这次测试的索引
                    self.engine.clear_index(index_name)
                
                avg_build_time = sum(build_times) / len(build_times)
                results[dataset_name][index_type] = avg_build_time
        
        # 输出结果
        print("\n=== 构建性能基准测试结果 ===")
        print(f"{'Dataset':<10} {'Hash':<10} {'B+Tree':<10} {'Inverted':<10} {'Trie':<10}")
        print("-" * 60)
        
        for dataset_name, dataset_results in results.items():
            print(f"{dataset_name:<10} ", end="")
            for index_type in index_types:
                time_ms = dataset_results[index_type] * 1000
                print(f"{time_ms:<10.2f} ", end="")
            print()
        
        # 性能断言
        for dataset_name, dataset_results in results.items():
            for index_type, build_time in dataset_results.items():
                with self.subTest(dataset=dataset_name, index=index_type):
                    # 构建时间应该合理
                    if dataset_name == 'large':
                        self.assertLess(build_time, 30.0)  # 大数据集30秒内
                    elif dataset_name == 'medium':
                        self.assertLess(build_time, 10.0)  # 中数据集10秒内
                    else:
                        self.assertLess(build_time, 5.0)   # 小数据集5秒内
    
    @pytest.mark.performance
    def test_query_performance_comparison(self):
        """比较不同索引类型的查询性能"""
        if not hasattr(self, '_run_performance_test'):
            self.skipTest("Performance test skipped")
        
        # 使用中等规模数据集
        dataset = self.medium_dataset
        sample_keys = random.sample(list(dataset.keys()), min(100, len(dataset)))
        
        query_results = {}
        
        for index_type in ['hash', 'btree', 'inverted', 'trie']:
            index_name = f'query_perf_{index_type}'
            self.engine.create_index(index_name, dataset, index_type=index_type)
            
            # 预热查询
            for key in sample_keys[:10]:
                self.engine.query_index(index_name, key)
            
            # 测试查询性能
            start_time = time.perf_counter()
            
            for _ in range(5):  # 重复5次
                for key in sample_keys:
                    result = self.engine.query_index(index_name, key)
            
            total_time = time.perf_counter() - start_time
            avg_query_time = total_time / (len(sample_keys) * 5) * 1000000  # 微秒
            
            query_results[index_type] = avg_query_time
        
        # 输出结果
        print("\n=== 查询性能基准测试结果 ===")
        for index_type, avg_time in query_results.items():
            print(f"{index_type:<10}: {avg_time:.2f} μs/query")
        
        # 性能断言
        for index_type, avg_time in query_results.items():
            with self.subTest(index=index_type):
                # 平均查询时间应该小于100微秒
                self.assertLess(avg_time, 100.0)
    
    @pytest.mark.performance
    def test_memory_efficiency_comparison(self):
        """比较不同索引类型的内存效率"""
        if not hasattr(self, '_run_performance_test'):
            self.skipTest("Performance test skipped")
        
        # 使用大数据集测试内存效率
        dataset = self.large_dataset
        memory_results = {}
        
        for index_type in ['hash', 'btree', 'inverted', 'trie']:
            index_name = f'memory_perf_{index_type}'
            self.engine.create_index(index_name, dataset, index_type=index_type)
            
            stats = self.engine.get_index_stats(index_name)
            memory_results[index_type] = stats.memory_usage_mb
        
        # 输出结果
        print("\n=== 内存效率基准测试结果 ===")
        total_data_size = len(dataset)
        
        for index_type, memory_mb in memory_results.items():
            efficiency = total_data_size / memory_mb  # 每MB存储的键数量
            print(f"{index_type:<10}: {memory_mb:6.2f} MB ({efficiency:6.1f} keys/MB)")
        
        # 内存效率断言
        for index_type, memory_mb in memory_results.items():
            with self.subTest(index=index_type):
                # 内存使用应该合理（对于5000个键，不超过50MB）
                self.assertLess(memory_mb, 50.0)
                self.assertGreater(memory_mb, 0.1)


if __name__ == '__main__':
    # 设置性能测试标志
    import sys
    if '--performance' in sys.argv:
        TestAdvancedIndexIntegration._run_performance_test = True
        TestAdvancedIndexPerformanceBenchmark._run_performance_test = True
        sys.argv.remove('--performance')
    
    unittest.main()