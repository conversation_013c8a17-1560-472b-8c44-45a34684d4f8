/**
 * 设计系统组件统一导出
 * 提供所有基础组件和业务组件的导入接口
 */

// 基础组件
import RButton from './base/RButton.vue'
import RCard from './base/RCard.vue'
import RTable from './base/RTable.vue'
import RModal from './base/RModal.vue'

// 组件列表
export const baseComponents = {
  RButton,
  RCard,
  RTable,
  RModal
}

// 组件安装插件
export const installComponents = (app) => {
  Object.entries(baseComponents).forEach(([name, component]) => {
    app.component(name, component)
  })
}

// 单独导出组件
export {
  RButton,
  RCard,
  RTable,
  RModal
}

// 默认导出
export default {
  install: installComponents,
  ...baseComponents
}
