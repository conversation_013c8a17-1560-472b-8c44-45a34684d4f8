# 降级机制故障排查指南

## 1. 概述

本文档提供了降级机制常见问题的诊断方法和解决方案，帮助运维人员快速定位和解决问题。

## 2. 常见问题分类

### 2.1 启动问题
- 降级机制无法启动
- 组件注册失败
- 配置加载错误

### 2.2 监控问题
- 性能指标获取失败
- 触发器不工作
- 状态更新异常

### 2.3 降级问题
- 降级触发失败
- 组件降级不生效
- 降级级别错误

### 2.4 恢复问题
- 自动恢复失败
- 手动恢复无响应
- 恢复后状态异常

### 2.5 API问题
- API接口无响应
- 认证失败
- 响应格式错误

## 3. 诊断工具和方法

### 3.1 日志分析

#### 3.1.1 日志位置
```bash
# 主应用日志
/var/log/app.log

# 降级机制专用日志
/var/log/degradation.log

# 系统日志
/var/log/syslog
```

#### 3.1.2 关键日志模式
```bash
# 降级触发日志
grep "Degradation triggered" /var/log/degradation.log

# 降级失败日志
grep "Degradation failed" /var/log/degradation.log

# 组件错误日志
grep "Component error" /var/log/degradation.log

# API调用日志
grep "API request" /var/log/degradation.log
```

### 3.2 状态检查命令

#### 3.2.1 系统状态检查
```bash
# 检查降级状态
curl -s http://localhost:8000/api/v1/degradation/status | jq .

# 检查组件状态
curl -s http://localhost:8000/api/v1/degradation/components | jq .

# 检查系统指标
curl -s http://localhost:8000/api/v1/degradation/metrics | jq .
```

#### 3.2.2 进程检查
```bash
# 检查主进程
ps aux | grep python | grep master.py

# 检查进程池状态
ps aux | grep "multiprocessing"

# 检查内存使用
free -h

# 检查CPU使用
top -p $(pgrep -f master.py)
```

### 3.3 配置验证

#### 3.3.1 配置文件检查
```bash
# 验证配置语法
python -c "from config.degradation_config import DEGRADATION_CONFIG; print('配置正确')"

# 检查配置值
python -c "
from config.degradation_config import DEGRADATION_CONFIG
import json
print(json.dumps(DEGRADATION_CONFIG, indent=2))
"
```

#### 3.3.2 环境变量检查
```bash
# 检查关键环境变量
echo "DEGRADATION_ENABLED: $DEGRADATION_ENABLED"
echo "DEGRADATION_API_KEY: $DEGRADATION_API_KEY"
echo "DEGRADATION_LOG_LEVEL: $DEGRADATION_LOG_LEVEL"
```

## 4. 具体问题排查

### 4.1 启动问题

#### 4.1.1 问题：降级机制无法启动

**症状**：
- 系统启动后降级功能不可用
- API接口返回503错误
- 日志显示初始化失败

**诊断步骤**：
```bash
# 1. 检查配置
python -c "from config.degradation_config import DEGRADATION_CONFIG; print(DEGRADATION_CONFIG['enabled'])"

# 2. 检查依赖
python -c "
try:
    from core.degradation_manager import get_degradation_manager
    print('导入成功')
except Exception as e:
    print(f'导入失败: {e}')
"

# 3. 检查日志
tail -f /var/log/degradation.log | grep -i error
```

**解决方案**：
1. 确认配置文件中`enabled=True`
2. 检查Python依赖包是否完整安装
3. 验证文件权限和路径
4. 重启应用服务

#### 4.1.2 问题：组件注册失败

**症状**：
- 降级机制启动但组件状态为空
- 日志显示组件注册错误
- 降级操作无效果

**诊断步骤**：
```bash
# 检查组件状态
curl -s http://localhost:8000/api/v1/degradation/components | jq '.data | length'

# 检查组件导入
python -c "
from core.degradation_adapters import get_adapter_manager
manager = get_adapter_manager()
print(f'已注册组件: {list(manager.get_all_adapters().keys())}')
"
```

**解决方案**：
1. 检查组件初始化代码
2. 确认组件依赖正常
3. 验证组件配置正确
4. 重新注册组件

### 4.2 监控问题

#### 4.2.1 问题：性能指标获取失败

**症状**：
- 触发器不工作
- 监控数据为空或异常
- CPU/内存数据不准确

**诊断步骤**：
```bash
# 检查性能监控器
python -c "
from core.performance_monitor import PerformanceMonitor
monitor = PerformanceMonitor()
metrics = monitor.get_current_metrics()
print(f'当前指标: {metrics}')
"

# 检查系统资源
htop
iostat 1 5
```

**解决方案**：
1. 检查psutil库是否正常工作
2. 验证系统权限
3. 重启性能监控服务
4. 检查监控间隔配置

#### 4.2.2 问题：触发器不工作

**症状**：
- 系统负载高但未触发降级
- 触发器配置正确但无响应
- 手动触发正常但自动触发失败

**诊断步骤**：
```bash
# 检查触发器配置
python -c "
from config.degradation_config import DEGRADATION_CONFIG
print(DEGRADATION_CONFIG['triggers'])
"

# 检查当前指标
curl -s http://localhost:8000/api/v1/degradation/status | jq '.data.active_triggers'
```

**解决方案**：
1. 验证触发器阈值设置
2. 检查持续时间配置
3. 确认监控间隔合理
4. 重启监控线程

### 4.3 降级问题

#### 4.3.1 问题：降级触发失败

**症状**：
- 手动触发降级返回失败
- 自动降级不生效
- 降级状态不更新

**诊断步骤**：
```bash
# 手动测试降级
curl -X POST http://localhost:8000/api/v1/degradation/trigger \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $DEGRADATION_API_KEY" \
  -d '{"level": "light_degradation", "reason": "测试", "force": true}'

# 检查错误日志
grep -i "degradation.*failed" /var/log/degradation.log
```

**解决方案**：
1. 检查API密钥是否正确
2. 验证降级级别参数
3. 检查组件是否正常响应
4. 查看详细错误信息

#### 4.3.2 问题：组件降级不生效

**症状**：
- 降级状态显示成功但组件配置未变
- 部分组件降级成功，部分失败
- 降级后性能无明显变化

**诊断步骤**：
```bash
# 检查组件状态
curl -s http://localhost:8000/api/v1/degradation/components | jq '.data[] | {name: .component_name, degraded: .is_degraded}'

# 检查具体组件配置
python -c "
from core.dynamic_process_pool import get_process_pool
pool = get_process_pool()
print(f'当前工作进程数: {pool.current_workers}')
print(f'最大工作进程数: {pool.max_workers}')
"
```

**解决方案**：
1. 检查组件适配器实现
2. 验证组件配置参数
3. 确认组件状态同步
4. 重启相关组件

### 4.4 恢复问题

#### 4.4.1 问题：自动恢复失败

**症状**：
- 系统负载降低但未自动恢复
- 恢复条件满足但状态未变
- 恢复过程中断

**诊断步骤**：
```bash
# 检查恢复配置
python -c "
from config.degradation_config import DEGRADATION_CONFIG
print(DEGRADATION_CONFIG['recovery'])
"

# 检查当前指标和阈值
curl -s http://localhost:8000/api/v1/degradation/status | jq '.data | {level: .current_level, duration: .degradation_duration}'
```

**解决方案**：
1. 检查恢复阈值设置
2. 验证恢复持续时间
3. 确认监控数据准确
4. 手动触发恢复测试

#### 4.4.2 问题：手动恢复无响应

**症状**：
- 手动恢复API调用超时
- 恢复操作返回错误
- 恢复后状态异常

**诊断步骤**：
```bash
# 测试手动恢复
curl -X POST "http://localhost:8000/api/v1/degradation/recover?reason=故障排查测试" \
  -H "Authorization: Bearer $DEGRADATION_API_KEY"

# 检查恢复日志
grep -i "recovery" /var/log/degradation.log | tail -10
```

**解决方案**：
1. 检查API认证
2. 验证系统状态
3. 强制重置状态
4. 重启降级服务

### 4.5 API问题

#### 4.5.1 问题：API接口无响应

**症状**：
- API调用超时
- 连接被拒绝
- 服务不可用

**诊断步骤**：
```bash
# 检查服务状态
curl -I http://localhost:8000/health

# 检查端口监听
netstat -tlnp | grep 8000

# 检查进程状态
ps aux | grep uvicorn
```

**解决方案**：
1. 重启Web服务
2. 检查端口占用
3. 验证防火墙设置
4. 检查负载均衡配置

#### 4.5.2 问题：认证失败

**症状**：
- 401未授权错误
- API密钥无效
- 权限不足

**诊断步骤**：
```bash
# 检查API密钥
echo "当前API密钥: $DEGRADATION_API_KEY"

# 测试认证
curl -H "Authorization: Bearer $DEGRADATION_API_KEY" \
  http://localhost:8000/api/v1/degradation/status
```

**解决方案**：
1. 验证API密钥正确性
2. 检查环境变量设置
3. 更新API密钥配置
4. 重启API服务

## 5. 紧急恢复操作

### 5.1 强制恢复正常状态

```bash
# 方法1：API强制恢复
curl -X POST "http://localhost:8000/api/v1/degradation/recover?reason=紧急恢复" \
  -H "Authorization: Bearer $DEGRADATION_API_KEY"

# 方法2：直接重启服务
sudo systemctl restart your-app-service

# 方法3：Python脚本恢复
python -c "
from core.degradation_manager import get_degradation_manager
manager = get_degradation_manager()
manager.manual_recover('紧急恢复')
print('恢复完成')
"
```

### 5.2 禁用降级机制

```bash
# 临时禁用
export DEGRADATION_ENABLED=false

# 永久禁用（修改配置文件）
sed -i 's/enabled: True/enabled: False/' config/degradation_config.py

# 重启服务
sudo systemctl restart your-app-service
```

### 5.3 重置所有状态

```bash
# 清理状态文件
rm -f /tmp/degradation_state.json

# 重启所有相关服务
sudo systemctl restart your-app-service
sudo systemctl restart nginx
```

## 6. 预防措施

### 6.1 监控建议
- 设置关键指标告警
- 定期检查日志文件
- 监控API响应时间
- 跟踪降级频率

### 6.2 维护建议
- 定期更新配置
- 测试降级和恢复流程
- 备份配置文件
- 文档保持更新

### 6.3 性能优化
- 调整监控间隔
- 优化触发器阈值
- 减少日志输出
- 使用异步处理

## 7. 联系支持

### 7.1 收集信息
在联系技术支持前，请收集以下信息：
- 系统版本和配置
- 错误日志和堆栈跟踪
- 问题复现步骤
- 系统资源使用情况

### 7.2 日志收集脚本
```bash
#!/bin/bash
# collect_degradation_logs.sh

echo "收集降级机制诊断信息..."
mkdir -p /tmp/degradation_debug

# 收集配置
cp config/degradation_config.py /tmp/degradation_debug/

# 收集日志
cp /var/log/degradation.log /tmp/degradation_debug/
cp /var/log/app.log /tmp/degradation_debug/

# 收集状态
curl -s http://localhost:8000/api/v1/degradation/status > /tmp/degradation_debug/status.json
curl -s http://localhost:8000/api/v1/degradation/components > /tmp/degradation_debug/components.json
curl -s http://localhost:8000/api/v1/degradation/metrics > /tmp/degradation_debug/metrics.json

# 收集系统信息
ps aux > /tmp/degradation_debug/processes.txt
free -h > /tmp/degradation_debug/memory.txt
df -h > /tmp/degradation_debug/disk.txt

# 打包
tar -czf degradation_debug_$(date +%Y%m%d_%H%M%S).tar.gz -C /tmp degradation_debug/

echo "诊断信息已收集到: degradation_debug_$(date +%Y%m%d_%H%M%S).tar.gz"
```
