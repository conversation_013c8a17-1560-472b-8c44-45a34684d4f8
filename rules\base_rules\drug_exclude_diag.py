from models import PatientData, RuleOutput, RuleResult
from rules.base_rules.base import BaseRule


class DrugExcludeDiagRule(BaseRule):
    """
    药品禁忌症-规则
    """

    rule_key = "drug_exclude_diag"
    rule_name = "药品禁忌症"
    rule_desc = """1、精确匹配药品医保代码
2、精确匹配禁忌症的icd-10出院诊断编码，有目标诊断提示"""

    def __init__(
        self,
        rule_id: str,                    # 规则ID
        diag_whole_code: list[str],      # 完整诊断编码
        yb_code: list[str],              # 药品编码
        rule_name: str,                  # 规则名称
        level1: str,                     # 一级错误类型
        level2: str,                     # 二级错误类型
        level3: str,                     # 三级错误类型
        error_reason: str,               # 错误原因
        degree: str,                     # 错误程度
        reference: str,                  # 质控依据或参考资料
        detail_position: str,            # 具体位置描述
        prompted_fields3: str | None,    # 提示字段类型
        prompted_fields1: str,           # 提示字段编码
        type: str,                       # 规则类别
        pos: str,                        # 适用业务
        applicableArea: str,             # 适用地区
        default_use: str,                # 默认选用
        remarks: str | None,             # 备注信息
        in_illustration: str | None,     # 入参说明
        start_date: str,                 # 开始日期
        end_date: str,                   # 结束日期
    ):
        super().__init__(rule_id)
        self.diag_whole_code = diag_whole_code
        self.yb_code = yb_code
        self.rule_name = rule_name
        self.level1 = level1
        self.level2 = level2
        self.level3 = level3
        self.type = type
        self.error_reason = error_reason
        self.degree = degree
        self.reference = reference
        self.prompted_fields3 = prompted_fields3
        self.prompted_fields1 = prompted_fields1
        self.detail_position = detail_position
        self.pos = pos
        self.applicableArea = applicableArea
        self.default_use = default_use
        self.remarks = remarks
        self.in_illustration = in_illustration
        self.start_date = start_date
        self.end_date = end_date

    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        药品禁忌症，在有指定诊断的前提下，不能开具指定药品医嘱，
        """
        # 1. 先判断诊断
        # 所有诊断的诊断名称、诊断编码列表，用于最后输出禁忌症
        exclude_diags = []
        # 1.1 门急诊诊断是否在指定诊断内
        if patient_data.Diagnosis.outPatientDiagnosisICDCode in self.diag_whole_code:
            # 这里只能拿到诊断编码，所以只添加诊断编码
            exclude_diags.append(patient_data.Diagnosis.outPatientDiagnosisICDCode)
        # 1.2 入院诊断是否在指定诊断内
        if patient_data.Diagnosis.diagnoseICDCodeOnAdmission in self.diag_whole_code:
            exclude_diags.append(patient_data.Diagnosis.diagnoseICDCodeOnAdmission)
            exclude_diags.append(patient_data.Diagnosis.diagnoseNameOnAdmission)
        # 1.3 出院诊断是否在指定诊断内
        for d in patient_data.Diagnosis.diagnosis:
            if str(d.diagnosisType) == "0":  # 只考虑西医诊断
                if d.diagnosisICDCode in self.diag_whole_code:
                    exclude_diags.append(d.diagnosisICDCode)
                    exclude_diags.append(d.diagnosisName)
        # 如果门急诊、入院、出院诊断都不在指定诊断内，则不违规，直接返回
        if not exclude_diags:
            return None

        # 2. 接下来检查收费项目中是否存在指定药品
        # 违规项目、违规数量、违规日期集合、违规金额
        fee_ids = []
        used_count = 0
        used_dates = set()
        error_fee = 0

        for fee in patient_data.fees:
            # 判断药品编码是否在违规编码列表中
            if fee.ybdm not in self.yb_code:
                continue

            # 判断日期是否正确，必须是毫秒级时间戳
            jzsj_str = str(fee.jzsj)
            if not jzsj_str.isdigit() or len(jzsj_str) < 10:
                continue

            # 违规数量、违规金额、违规项目
            used_count += fee.sl
            error_fee += fee.je
            fee_ids.append(fee.id)

            # 把记账时间转换成日期形式
            fee_date = self._trans_timestamp_to_date(int(jzsj_str[:10]))
            used_dates.add(fee_date)

        # 如果使用数量为0，则不违规，直接返回
        if used_count == 0:
            return None

        # 提前计算，避免后面重复计算
        used_ids = ",".join(fee_ids)
        used_day = len(used_dates)

        # 禁忌症，输出所有诊断的诊断名称、诊断编码
        exclude_diags_str = ",".join(exclude_diags)

        rule_output = RuleOutput(
            type_=self.type,
            message=self.error_reason,
            level1=self.level1,
            level2=self.level2,
            level3=self.level3,
            error_reason=self.error_reason.replace("[输出诊断]", exclude_diags_str),
            degree=self.degree,
            reference=self.reference,
            prompted_fields3=self.prompted_fields3,
            prompted_fields1=self.prompted_fields1,
            detail_position=self.detail_position,
            pos=self.pos,
            applicableArea=self.applicableArea,
            default_use=self.default_use,
            remarks=self.remarks,
            in_illustration=self.in_illustration,
            start_date=self.start_date,
            end_date=self.end_date,
            used_count=used_count,
            illegal_count=used_count,
            used_day=used_day,
            illegal_day=used_day,
            illegal_item=used_ids,
            error_fee=error_fee,
            prompted_fields2=used_ids,
        )

        # 返回结果
        return RuleResult(
            id=self.rule_id,
            output=rule_output,
        )
