/**
 * 动态校验引擎
 * 基于后端元数据的前端校验实现
 */

import {
  ValidationRule,
  ValidationError,
  ValidationResult,
  ValidationRuleType,
  ValidationSeverity,
  FieldValidationConfig,
  ValidationEngineOptions,
  DEFAULT_VALIDATION_OPTIONS,
  ValidationPerformanceMetrics
} from './validationTypes'
import { fieldMappingEngine } from '../fieldMappingEngine'
import { enhancedErrorHandler } from '../enhancedErrorHandler'

export class DynamicValidationEngine {
  private options: ValidationEngineOptions
  private ruleCache: Map<string, FieldValidationConfig[]> = new Map()
  private cacheTimestamps: Map<string, Date> = new Map()
  private performanceMetrics: ValidationPerformanceMetrics
  private eventListeners: Map<string, Function[]> = new Map()

  constructor(options: Partial<ValidationEngineOptions> = {}) {
    this.options = { ...DEFAULT_VALIDATION_OPTIONS, ...options }
    this.performanceMetrics = this.initializeMetrics()
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): ValidationPerformanceMetrics {
    return {
      total_validations: 0,
      average_duration: 0,
      cache_hit_rate: 0,
      error_rate: 0,
      last_reset_time: new Date()
    }
  }

  /**
   * 获取规则模板的校验配置
   */
  async getValidationConfig(ruleKey: string): Promise<FieldValidationConfig[]> {
    const cacheKey = `config_${ruleKey}`

    // 检查缓存
    if (this.options.cache_enabled && this.isCacheValid(cacheKey)) {
      const cached = this.ruleCache.get(cacheKey)
      if (cached) {
        this.updateCacheHitRate(true)
        return cached
      }
    }

    try {
      // 从后端获取校验配置
      const response = await fetch(`/api/v1/validation/rules/${ruleKey}`, {
        headers: {
          'X-API-KEY': 'a_very_secret_key_for_development',
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`获取校验配置失败: ${response.statusText}`)
      }

      const data = await response.json()
      if (!data.success) {
        throw new Error(data.message || '获取校验配置失败')
      }

      const config = this.parseBackendConfig(data.data)

      // 更新缓存
      if (this.options.cache_enabled) {
        this.ruleCache.set(cacheKey, config)
        this.cacheTimestamps.set(cacheKey, new Date())
      }

      this.updateCacheHitRate(false)
      return config

    } catch (error) {
      console.error('获取校验配置失败:', error)
      enhancedErrorHandler.handleError(error, {
        context: 'DynamicValidationEngine.getValidationConfig',
        ruleKey
      })
      return []
    }
  }

  /**
   * 验证单个字段
   */
  async validateField(
    fieldName: string,
    value: any,
    ruleKey: string
  ): Promise<ValidationResult> {
    const startTime = performance.now()

    try {
      const configs = await this.getValidationConfig(ruleKey)
      const fieldConfig = configs.find(c => c.field_name === fieldName)

      if (!fieldConfig) {
        return this.createSuccessResult([fieldName], performance.now() - startTime)
      }

      const errors: ValidationError[] = []

      // 执行所有校验规则
      for (const rule of fieldConfig.rules) {
        const error = await this.validateByRule(value, rule)
        if (error) {
          errors.push(error)
        }
      }

      const result: ValidationResult = {
        valid: errors.length === 0,
        errors,
        warnings: [],
        field_count: 1,
        validated_fields: [fieldName],
        duration: performance.now() - startTime,
        metadata: {
          rule_key: ruleKey,
          field_name: fieldName
        }
      }

      this.updatePerformanceMetrics(result)
      this.emitEvent('field_validated', { field_name: fieldName, result })

      return result

    } catch (error) {
      console.error('字段校验失败:', error)
      return this.createErrorResult(error, [fieldName], performance.now() - startTime)
    }
  }

  /**
   * 验证整个表单
   */
  async validateForm(
    formData: Record<string, any>,
    ruleKey: string
  ): Promise<ValidationResult> {
    const startTime = performance.now()

    try {
      const configs = await this.getValidationConfig(ruleKey)
      const errors: ValidationError[] = []
      const warnings: string[] = []
      const validatedFields: string[] = []

      // 使用字段映射引擎转换数据
      const transformedData = fieldMappingEngine.transformRequestData(formData)

      // 验证每个字段
      for (const config of configs) {
        const fieldName = config.field_name
        const value = transformedData[fieldName]
        validatedFields.push(fieldName)

        for (const rule of config.rules) {
          const error = await this.validateByRule(value, rule)
          if (error) {
            if (error.severity === ValidationSeverity.WARNING) {
              warnings.push(error.error_message)
            } else {
              errors.push(error)
            }
          }
        }
      }

      const result: ValidationResult = {
        valid: errors.length === 0,
        errors,
        warnings,
        field_count: validatedFields.length,
        validated_fields: validatedFields,
        duration: performance.now() - startTime,
        metadata: {
          rule_key: ruleKey,
          form_data_keys: Object.keys(formData)
        }
      }

      this.updatePerformanceMetrics(result)
      this.emitEvent('form_validated', { result })

      return result

    } catch (error) {
      console.error('表单校验失败:', error)
      return this.createErrorResult(error, Object.keys(formData), performance.now() - startTime)
    }
  }

  /**
   * 根据规则验证值
   */
  private async validateByRule(value: any, rule: ValidationRule): Promise<ValidationError | null> {
    try {
      switch (rule.rule_type) {
        case ValidationRuleType.REQUIRED:
          return this.validateRequired(value, rule)

        case ValidationRuleType.MIN_LENGTH:
          return this.validateMinLength(value, rule)

        case ValidationRuleType.MAX_LENGTH:
          return this.validateMaxLength(value, rule)

        case ValidationRuleType.MIN_VALUE:
          return this.validateMinValue(value, rule)

        case ValidationRuleType.MAX_VALUE:
          return this.validateMaxValue(value, rule)

        case ValidationRuleType.PATTERN:
          return this.validatePattern(value, rule)

        case ValidationRuleType.EMAIL:
          return this.validateEmail(value, rule)

        case ValidationRuleType.DATE_FORMAT:
          return this.validateDateFormat(value, rule)

        case ValidationRuleType.INTEGER:
          return this.validateInteger(value, rule)

        case ValidationRuleType.ARRAY:
          return this.validateArray(value, rule)

        default:
          console.warn(`未支持的校验规则类型: ${rule.rule_type}`)
          return null
      }
    } catch (error) {
      console.error(`校验规则执行失败: ${rule.rule_type}`, error)
      return null
    }
  }

  /**
   * 必填校验
   */
  private validateRequired(value: any, rule: ValidationRule): ValidationError | null {
    if (value === null || value === undefined ||
        (typeof value === 'string' && value.trim() === '')) {
      return this.createValidationError(rule, value, 'REQUIRED_FIELD_MISSING')
    }
    return null
  }

  /**
   * 最小长度校验
   */
  private validateMinLength(value: any, rule: ValidationRule): ValidationError | null {
    if (value && typeof value === 'string' && value.length < rule.rule_value) {
      return this.createValidationError(rule, value, 'MIN_LENGTH_VIOLATION')
    }
    return null
  }

  /**
   * 最大长度校验
   */
  private validateMaxLength(value: any, rule: ValidationRule): ValidationError | null {
    if (value && typeof value === 'string' && value.length > rule.rule_value) {
      return this.createValidationError(rule, value, 'MAX_LENGTH_VIOLATION')
    }
    return null
  }

  /**
   * 最小值校验
   */
  private validateMinValue(value: any, rule: ValidationRule): ValidationError | null {
    const numValue = Number(value)
    if (!isNaN(numValue) && numValue < rule.rule_value) {
      return this.createValidationError(rule, value, 'MIN_VALUE_VIOLATION')
    }
    return null
  }

  /**
   * 最大值校验
   */
  private validateMaxValue(value: any, rule: ValidationRule): ValidationError | null {
    const numValue = Number(value)
    if (!isNaN(numValue) && numValue > rule.rule_value) {
      return this.createValidationError(rule, value, 'MAX_VALUE_VIOLATION')
    }
    return null
  }

  /**
   * 正则表达式校验
   */
  private validatePattern(value: any, rule: ValidationRule): ValidationError | null {
    if (value && typeof value === 'string') {
      const pattern = new RegExp(rule.rule_value)
      if (!pattern.test(value)) {
        return this.createValidationError(rule, value, 'PATTERN_MISMATCH')
      }
    }
    return null
  }

  /**
   * 邮箱格式校验
   */
  private validateEmail(value: any, rule: ValidationRule): ValidationError | null {
    if (value && typeof value === 'string') {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailPattern.test(value)) {
        return this.createValidationError(rule, value, 'INVALID_EMAIL_FORMAT')
      }
    }
    return null
  }

  /**
   * 日期格式校验
   */
  private validateDateFormat(value: any, rule: ValidationRule): ValidationError | null {
    if (value && typeof value === 'string') {
      const date = new Date(value)
      if (isNaN(date.getTime())) {
        return this.createValidationError(rule, value, 'INVALID_DATE_FORMAT')
      }
    }
    return null
  }

  /**
   * 整数校验
   */
  private validateInteger(value: any, rule: ValidationRule): ValidationError | null {
    if (value !== null && value !== undefined && value !== '') {
      const numValue = Number(value)
      if (isNaN(numValue) || !Number.isInteger(numValue)) {
        return this.createValidationError(rule, value, 'INVALID_INTEGER')
      }
    }
    return null
  }

  /**
   * 数组校验
   */
  private validateArray(value: any, rule: ValidationRule): ValidationError | null {
    if (value !== null && value !== undefined && !Array.isArray(value)) {
      return this.createValidationError(rule, value, 'INVALID_ARRAY_FORMAT')
    }
    return null
  }

  /**
   * 创建校验错误对象
   */
  private createValidationError(
    rule: ValidationRule,
    value: any,
    errorCode: string
  ): ValidationError {
    return {
      field_name: rule.field_name,
      chinese_name: rule.chinese_name,
      error_code: errorCode,
      error_message: rule.error_message,
      error_value: value,
      rule_type: rule.rule_type,
      suggestions: this.generateSuggestions(rule, value),
      severity: rule.severity || ValidationSeverity.ERROR
    }
  }

  /**
   * 生成修复建议
   */
  private generateSuggestions(rule: ValidationRule, _value: any): string[] {
    const suggestions: string[] = []

    switch (rule.rule_type) {
      case ValidationRuleType.REQUIRED:
        suggestions.push(`请填写${rule.chinese_name}`)
        break
      case ValidationRuleType.MIN_LENGTH:
        suggestions.push(`${rule.chinese_name}至少需要${rule.rule_value}个字符`)
        break
      case ValidationRuleType.MAX_LENGTH:
        suggestions.push(`${rule.chinese_name}不能超过${rule.rule_value}个字符`)
        break
      case ValidationRuleType.EMAIL:
        suggestions.push('请输入有效的邮箱地址，如：<EMAIL>')
        break
      case ValidationRuleType.DATE_FORMAT:
        suggestions.push('请输入有效的日期格式，如：2024-01-01')
        break
      default:
        suggestions.push(`请检查${rule.chinese_name}的格式`)
    }

    return suggestions
  }

  /**
   * 解析后端配置
   */
  private parseBackendConfig(backendData: any): FieldValidationConfig[] {
    const configs: FieldValidationConfig[] = []

    if (backendData.fields) {
      for (const [fieldName, fieldData] of Object.entries(backendData.fields)) {
        const config: FieldValidationConfig = {
          field_name: fieldName,
          chinese_name: (fieldData as any).chinese_name || fieldName,
          rules: this.parseValidationRules(fieldData as any),
          element_rules: [],
          is_required: (fieldData as any).is_required || false,
          data_type: (fieldData as any).data_type || 'string',
          default_value: (fieldData as any).default_value
        }
        configs.push(config)
      }
    }

    return configs
  }

  /**
   * 解析校验规则
   */
  private parseValidationRules(fieldData: any): ValidationRule[] {
    const rules: ValidationRule[] = []

    if (fieldData.rules) {
      for (const ruleData of fieldData.rules) {
        const rule: ValidationRule = {
          field_name: fieldData.field_name || '',
          chinese_name: fieldData.chinese_name || '',
          rule_type: ruleData.type as ValidationRuleType,
          rule_value: ruleData.value,
          error_message: ruleData.message || `${fieldData.chinese_name}校验失败`,
          is_required: ruleData.type === 'required',
          priority: ruleData.priority || 0,
          severity: ruleData.severity || ValidationSeverity.ERROR
        }
        rules.push(rule)
      }
    }

    return rules
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamps.get(cacheKey)
    if (!timestamp) return false

    const now = new Date()
    const diffMinutes = (now.getTime() - timestamp.getTime()) / (1000 * 60)
    return diffMinutes < this.options.cache_ttl_minutes
  }

  /**
   * 更新缓存命中率
   */
  private updateCacheHitRate(isHit: boolean): void {
    // 简化的命中率计算
    const currentRate = this.performanceMetrics.cache_hit_rate
    this.performanceMetrics.cache_hit_rate = isHit ?
      Math.min(currentRate + 0.01, 1.0) :
      Math.max(currentRate - 0.01, 0.0)
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(result: ValidationResult): void {
    this.performanceMetrics.total_validations++

    // 更新平均持续时间
    const currentAvg = this.performanceMetrics.average_duration
    const newAvg = (currentAvg + result.duration) / 2
    this.performanceMetrics.average_duration = newAvg

    // 更新错误率
    if (!result.valid) {
      const currentErrorRate = this.performanceMetrics.error_rate
      this.performanceMetrics.error_rate = Math.min(currentErrorRate + 0.01, 1.0)
    }
  }

  /**
   * 创建成功结果
   */
  private createSuccessResult(fields: string[], duration: number): ValidationResult {
    return {
      valid: true,
      errors: [],
      warnings: [],
      field_count: fields.length,
      validated_fields: fields,
      duration,
      metadata: {}
    }
  }

  /**
   * 创建错误结果
   */
  private createErrorResult(error: any, fields: string[], duration: number): ValidationResult {
    return {
      valid: false,
      errors: [{
        field_name: 'system',
        chinese_name: '系统',
        error_code: 'VALIDATION_ENGINE_ERROR',
        error_message: error.message || '校验引擎内部错误',
        error_value: null,
        rule_type: 'system',
        suggestions: ['请联系技术支持'],
        severity: ValidationSeverity.ERROR
      }],
      warnings: [],
      field_count: fields.length,
      validated_fields: fields,
      duration,
      metadata: { error: error.message }
    }
  }

  /**
   * 发送事件
   */
  private emitEvent(eventType: string, data: any): void {
    const listeners = this.eventListeners.get(eventType) || []
    listeners.forEach(listener => {
      try {
        listener({ type: eventType, ...data, timestamp: new Date() })
      } catch (error) {
        console.error('事件监听器执行失败:', error)
      }
    })
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventType: string, listener: Function): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, [])
    }
    this.eventListeners.get(eventType)!.push(listener)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventType: string, listener: Function): void {
    const listeners = this.eventListeners.get(eventType) || []
    const index = listeners.indexOf(listener)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): ValidationPerformanceMetrics {
    return { ...this.performanceMetrics }
  }

  /**
   * 重置性能指标
   */
  resetPerformanceMetrics(): void {
    this.performanceMetrics = this.initializeMetrics()
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.ruleCache.clear()
    this.cacheTimestamps.clear()
  }
}
