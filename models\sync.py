"""
同步相关数据模型

定义同步请求、响应、状态等数据结构，用于统一同步协调器的API接口。
"""

import time
from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field

# 条件导入避免循环依赖
try:
    from core.version_management import ChangeItem, VersionInfo
except ImportError:
    # 在测试环境或依赖未就绪时使用备用定义
    class ChangeItem:
        """变更项目备用定义"""
        def __init__(self, operation: str, key: str, old_value=None, new_value=None, metadata=None):
            self.operation = operation
            self.key = key
            self.old_value = old_value
            self.new_value = new_value
            self.metadata = metadata or {}

        def to_dict(self):
            return {
                "operation": self.operation,
                "key": self.key,
                "old_value": self.old_value,
                "new_value": self.new_value,
                "metadata": self.metadata
            }

    class VersionInfo:
        """版本信息备用定义"""
        def __init__(self, version: str, timestamp: float, checksum: str, metadata=None):
            self.version = version
            self.timestamp = timestamp
            self.checksum = checksum
            self.metadata = metadata or {}


class SyncMode(str, Enum):
    """同步模式枚举"""

    FULL = "full"
    INCREMENTAL = "incremental"


class SyncStatus(str, Enum):
    """同步状态枚举"""

    PENDING = "pending"
    STARTED = "started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SyncRequest(BaseModel):
    """同步请求模型"""

    node_id: str = Field(..., description="节点ID")
    current_version: str | None = Field(None, description="当前版本")
    sync_mode: SyncMode = Field(SyncMode.INCREMENTAL, description="同步模式")
    rule_keys: list[str] | None = Field(None, description="指定同步的规则键")
    force_sync: bool = Field(False, description="是否强制同步")
    include_metadata: bool = Field(True, description="是否包含元数据")
    batch_size: int = Field(1000, ge=1, le=10000, description="批处理大小")
    timeout_seconds: int = Field(300, ge=30, le=3600, description="超时时间(秒)")


class SyncResponse(BaseModel):
    """同步响应模型"""

    sync_id: str = Field(..., description="同步任务ID")
    status: SyncStatus = Field(..., description="同步状态")
    node_id: str = Field(..., description="节点ID")
    sync_mode: SyncMode = Field(..., description="同步模式")

    # 版本信息
    from_version: str | None = Field(None, description="源版本")
    to_version: str | None = Field(None, description="目标版本")

    # 统计信息
    total_items: int = Field(0, description="总项目数")
    processed_items: int = Field(0, description="已处理项目数")
    failed_items: int = Field(0, description="失败项目数")

    # 时间信息
    start_time: datetime = Field(default_factory=datetime.now, description="开始时间")
    end_time: datetime | None = Field(None, description="结束时间")
    estimated_duration: int | None = Field(None, description="预估完成时间(秒)")

    # 错误信息
    errors: list[dict[str, Any]] = Field(default_factory=list, description="错误列表")
    warnings: list[str] = Field(default_factory=list, description="警告列表")

    # 额外信息
    metadata: dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class ChangesRequest(BaseModel):
    """变更请求模型"""

    from_version: str = Field(..., description="源版本")
    to_version: str | None = Field(None, description="目标版本，为空表示最新版本")
    rule_keys: list[str] | None = Field(None, description="指定规则键")
    include_data: bool = Field(True, description="是否包含变更数据")
    max_changes: int = Field(1000, ge=1, le=10000, description="最大变更数量")


class ChangesResponse(BaseModel):
    """变更响应模型"""

    from_version: str = Field(..., description="源版本")
    to_version: str = Field(..., description="目标版本")
    total_changes: int = Field(..., description="总变更数量")
    changes: list[dict[str, Any]] = Field(..., description="变更列表")
    has_more: bool = Field(False, description="是否有更多变更")
    next_cursor: str | None = Field(None, description="下一页游标")
    metadata: dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class SyncAcknowledgment(BaseModel):
    """同步确认模型"""

    sync_id: str = Field(..., description="同步任务ID")
    node_id: str = Field(..., description="节点ID")
    status: SyncStatus = Field(..., description="确认状态")
    applied_version: str | None = Field(None, description="已应用的版本")
    applied_changes: int = Field(0, description="已应用的变更数量")
    errors: list[dict[str, Any]] = Field(default_factory=list, description="应用过程中的错误")
    metadata: dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class SyncStatusResponse(BaseModel):
    """同步状态响应模型"""

    node_id: str = Field(..., description="节点ID")
    current_version: str | None = Field(None, description="当前版本")
    last_sync_time: datetime | None = Field(None, description="最后同步时间")
    last_successful_sync: datetime | None = Field(None, description="最后成功同步时间")
    sync_failures: int = Field(0, description="同步失败次数")
    is_syncing: bool = Field(False, description="是否正在同步")
    network_partition: bool = Field(False, description="是否网络分区")

    # 活跃同步任务
    active_syncs: list[dict[str, Any]] = Field(default_factory=list, description="活跃同步任务")

    # 统计信息
    total_syncs: int = Field(0, description="总同步次数")
    successful_syncs: int = Field(0, description="成功同步次数")
    failed_syncs: int = Field(0, description="失败同步次数")

    # 性能指标
    average_sync_duration: float = Field(0.0, description="平均同步时长(秒)")
    last_sync_duration: float = Field(0.0, description="最后同步时长(秒)")

    metadata: dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class SyncError(BaseModel):
    """同步错误模型"""

    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    error_type: str = Field(..., description="错误类型")
    rule_key: str | None = Field(None, description="相关规则键")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")
    context: dict[str, Any] = Field(default_factory=dict, description="错误上下文")
    is_retryable: bool = Field(False, description="是否可重试")


class SyncMetrics(BaseModel):
    """同步指标模型"""

    sync_id: str = Field(..., description="同步任务ID")
    node_id: str = Field(..., description="节点ID")

    # 性能指标
    start_timestamp: float = Field(..., description="开始时间戳")
    end_timestamp: float | None = Field(None, description="结束时间戳")
    duration: float | None = Field(None, description="持续时间(秒)")

    # 数据指标
    data_size_bytes: int = Field(0, description="数据大小(字节)")
    compressed_size_bytes: int = Field(0, description="压缩后大小(字节)")
    compression_ratio: float = Field(1.0, description="压缩比")

    # 网络指标
    network_latency_ms: float | None = Field(None, description="网络延迟(毫秒)")
    throughput_mbps: float | None = Field(None, description="吞吐量(Mbps)")

    # 新增系统资源指标
    cpu_usage_percent: float = Field(0.0, description="CPU使用率(%)")
    memory_usage_mb: int = Field(0, description="内存使用量(MB)")
    memory_peak_mb: int = Field(0, description="内存峰值(MB)")
    disk_io_read_mb: float = Field(0.0, description="磁盘读取(MB)")
    disk_io_write_mb: float = Field(0.0, description="磁盘写入(MB)")
    network_io_in_mb: float = Field(0.0, description="网络接收(MB)")
    network_io_out_mb: float = Field(0.0, description="网络发送(MB)")

    # 新增同步质量指标
    sync_accuracy_percent: float = Field(100.0, description="同步准确性(%)")
    data_consistency_score: float = Field(1.0, description="数据一致性分数(0-1)")
    rule_validation_pass_rate: float = Field(1.0, description="规则验证通过率(0-1)")

    # 新增缓存性能指标
    cache_hit_rate: float = Field(0.0, description="缓存命中率(0-1)")
    cache_miss_count: int = Field(0, description="缓存未命中次数")
    cache_size_mb: float = Field(0.0, description="缓存大小(MB)")

    # 新增数据库性能指标
    db_query_count: int = Field(0, description="数据库查询次数")
    db_query_avg_time_ms: float = Field(0.0, description="数据库查询平均耗时(毫秒)")
    db_connection_pool_usage: float = Field(0.0, description="数据库连接池使用率(0-1)")

    # 错误指标
    retry_count: int = Field(0, description="重试次数")
    error_count: int = Field(0, description="错误次数")
    recovery_attempts: int = Field(0, description="错误恢复尝试次数")
    recovery_success_count: int = Field(0, description="错误恢复成功次数")

    metadata: dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class SyncConfiguration(BaseModel):
    """同步配置模型"""

    # 基础配置
    sync_interval: int = Field(300, ge=30, le=3600, description="同步间隔(秒)")
    batch_size: int = Field(1000, ge=1, le=10000, description="批处理大小")
    timeout_seconds: int = Field(300, ge=30, le=3600, description="超时时间(秒)")

    # 重试配置
    max_retries: int = Field(3, ge=0, le=10, description="最大重试次数")
    retry_delay: float = Field(1.0, ge=0.1, le=60.0, description="重试延迟(秒)")
    backoff_factor: float = Field(2.0, ge=1.0, le=10.0, description="退避因子")

    # 缓存配置
    enable_cache: bool = Field(True, description="是否启用缓存")
    cache_ttl: int = Field(3600, ge=60, le=86400, description="缓存TTL(秒)")
    cache_size: int = Field(1000, ge=10, le=10000, description="缓存大小")

    # 压缩配置
    enable_compression: bool = Field(True, description="是否启用压缩")
    compression_level: int = Field(6, ge=1, le=9, description="压缩级别")

    # 监控配置
    enable_metrics: bool = Field(True, description="是否启用指标收集")
    metrics_interval: int = Field(60, ge=10, le=300, description="指标收集间隔(秒)")

    metadata: dict[str, Any] = Field(default_factory=dict, description="额外配置")


# 兼容性别名，保持与现有代码的兼容性
SyncStatistics = SyncMetrics  # 兼容现有的SyncStatistics


def create_sync_id(node_id: str, timestamp: float | None = None) -> str:
    """
    创建同步任务ID

    Args:
        node_id: 节点ID
        timestamp: 时间戳，默认使用当前时间

    Returns:
        str: 同步任务ID
    """
    if timestamp is None:
        timestamp = time.time()

    return f"sync_{node_id}_{int(timestamp * 1000)}"


class OfflinePackageRequest(BaseModel):
    """离线包请求模型"""

    package_name: str | None = Field(None, description="包名称")
    rule_keys: list[str] | None = Field(None, description="包含的规则键")
    compression_level: int = Field(6, ge=1, le=9, description="压缩级别")
    include_metadata: bool = Field(True, description="是否包含元数据")
    expiry_days: int = Field(30, ge=1, le=365, description="过期天数")
    description: str | None = Field(None, description="包描述")
    tags: list[str] = Field(default_factory=list, description="包标签")


class PackageInfo(BaseModel):
    """包信息模型"""

    package_id: str = Field(..., description="包ID")
    package_name: str = Field(..., description="包名称")
    version: str = Field(..., description="版本号")
    size_bytes: int = Field(..., description="文件大小")
    created_at: datetime = Field(..., description="创建时间")
    expires_at: datetime = Field(..., description="过期时间")
    rule_count: int = Field(..., description="规则数量")
    checksum: str = Field(..., description="校验和")
    description: str | None = Field(None, description="包描述")
    tags: list[str] = Field(default_factory=list, description="包标签")

    # 状态信息
    is_expired: bool = Field(False, description="是否已过期")
    download_count: int = Field(0, description="下载次数")
    last_downloaded: datetime | None = Field(None, description="最后下载时间")

    # 元数据
    metadata: dict[str, Any] = Field(default_factory=dict, description="额外元数据")


def create_sync_error(
    error_code: str,
    error_message: str,
    error_type: str = "SyncError",
    rule_key: str | None = None,
    context: dict[str, Any] | None = None,
    is_retryable: bool = False,
) -> SyncError:
    """
    创建同步错误对象

    Args:
        error_code: 错误代码
        error_message: 错误消息
        error_type: 错误类型
        rule_key: 相关规则键
        context: 错误上下文
        is_retryable: 是否可重试

    Returns:
        SyncError: 同步错误对象
    """
    return SyncError(
        error_code=error_code,
        error_message=error_message,
        error_type=error_type,
        rule_key=rule_key,
        context=context or {},
        is_retryable=is_retryable,
    )
