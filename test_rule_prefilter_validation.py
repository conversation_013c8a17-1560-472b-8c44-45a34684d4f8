#!/usr/bin/env python3
"""
规则预过滤器验证脚本
验证任务3.2的实现质量和性能
"""

import sys
import os
import time
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rule_prefilter_basic_functionality():
    """测试规则预过滤器基本功能"""
    print("=== 测试规则预过滤器基本功能 ===")
    
    try:
        # 导入必要的模块
        from core.rule_prefilter import rule_prefilter
        from core.rule_index_manager import rule_index_manager
        from core.patient_data_analyzer import patient_data_analyzer
        from models.patient import PatientData
        
        print("✅ 模块导入成功")
        
        # 检查实例是否正确创建
        assert rule_prefilter is not None, "rule_prefilter实例不存在"
        assert hasattr(rule_prefilter, 'filter_rules_for_patient'), "缺少filter_rules_for_patient方法"
        
        print("✅ 实例和方法检查通过")
        
        # 检查配置
        from config.settings import settings
        print(f"✅ 配置检查: ENABLE_RULE_PREFILTER = {getattr(settings, 'ENABLE_RULE_PREFILTER', 'NOT_SET')}")
        print(f"✅ 配置检查: PREFILTER_TIMEOUT_MS = {getattr(settings, 'PREFILTER_TIMEOUT_MS', 'NOT_SET')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_rule_prefilter_mock_scenario():
    """使用模拟数据测试规则过滤场景"""
    print("\n=== 测试规则过滤场景 ===")
    
    try:
        from core.rule_prefilter import rule_prefilter
        from core.rule_index_manager import rule_index_manager
        from models.patient import PatientData
        
        # 创建模拟规则数据
        mock_rules = []
        
        # 药品规则
        drug_rule = Mock()
        drug_rule.rule_id = "drug_rule_001"
        drug_rule.yb_code = "Y001,Y002"
        drug_rule.diag_whole_code = ""
        drug_rule.diag_code_prefix = ""
        drug_rule.fee_whole_code = ""
        drug_rule.fee_code_prefix = ""
        drug_rule.extended_fields = "{}"
        mock_rules.append(drug_rule)
        
        # 诊断规则
        diag_rule = Mock()
        diag_rule.rule_id = "diag_rule_002"
        diag_rule.yb_code = ""
        diag_rule.diag_whole_code = "I10,E11.9"
        diag_rule.diag_code_prefix = "I1"
        diag_rule.fee_whole_code = ""
        diag_rule.fee_code_prefix = ""
        diag_rule.extended_fields = "{}"
        mock_rules.append(diag_rule)
        
        # 通用规则
        universal_rule = Mock()
        universal_rule.rule_id = "universal_rule_003"
        universal_rule.yb_code = ""
        universal_rule.diag_whole_code = ""
        universal_rule.diag_code_prefix = ""
        universal_rule.fee_whole_code = ""
        universal_rule.fee_code_prefix = ""
        universal_rule.extended_fields = "{}"
        mock_rules.append(universal_rule)
        
        print(f"✅ 创建了 {len(mock_rules)} 个模拟规则")
        
        # 清理并构建索引
        rule_index_manager._clear_indexes()
        rule_index_manager.build_indexes_from_rule_details(mock_rules)
        
        print("✅ 索引构建完成")
        
        # 创建模拟患者数据
        patient = Mock(spec=PatientData)
        
        # 创建费用明细
        fee1 = Mock()
        fee1.ybdm = "Y001"
        patient.fees = [fee1]
        
        # 创建诊断信息 - 修正为符合实际数据模型的结构
        diagnosis_info = Mock()
        diagnosis_info.outPatientDiagnosisICDCode = "I10"
        diagnosis_info.admissionDiagnosisICDCode = ""
        diagnosis_info.diagnosisList = []
        patient.Diagnosis = diagnosis_info
        
        print("✅ 创建模拟患者数据")
        
        # 测试过滤功能（禁用状态）
        all_rule_ids = [rule.rule_id for rule in mock_rules]
        
        # 确保过滤功能禁用
        from config.settings import settings
        original_setting = getattr(settings, 'ENABLE_RULE_PREFILTER', False)
        settings.ENABLE_RULE_PREFILTER = False
        
        try:
            filter_result = rule_prefilter.filter_rules_for_patient(patient, all_rule_ids)
            
            print(f"✅ 禁用状态测试:")
            print(f"   - 原始规则数: {filter_result.original_rule_count}")
            print(f"   - 过滤后规则数: {filter_result.filtered_rule_count}")
            print(f"   - 过滤率: {filter_result.filter_rate*100:.1f}%")
            print(f"   - 过滤时间: {filter_result.filter_time:.3f}ms")
            
            # 验证禁用状态下应该返回所有规则
            assert filter_result.filter_rate == 0.0, "禁用状态下过滤率应该为0"
            assert len(filter_result.filtered_rule_ids) == len(all_rule_ids), "禁用状态下应该返回所有规则"
            
            print("✅ 禁用状态验证通过")
            
        finally:
            settings.ENABLE_RULE_PREFILTER = original_setting
        
        return True
        
    except Exception as e:
        print(f"❌ 规则过滤场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_stats():
    """测试性能统计功能"""
    print("\n=== 测试性能统计功能 ===")
    
    try:
        from core.rule_prefilter import rule_prefilter
        
        # 重置统计
        rule_prefilter.reset_stats()
        
        # 获取统计信息
        stats = rule_prefilter.get_performance_stats()
        
        print("✅ 性能统计信息:")
        for key, value in stats.items():
            if isinstance(value, dict):
                print(f"   - {key}: {type(value).__name__} (子组件统计)")
            else:
                print(f"   - {key}: {value}")
        
        # 获取健康报告
        health_report = rule_prefilter.get_health_report()
        
        print("✅ 健康状态报告:")
        for key, value in health_report.items():
            if isinstance(value, list):
                print(f"   - {key}: {len(value)} 项建议")
                for item in value:
                    print(f"     * {item}")
            else:
                print(f"   - {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能统计测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始验证任务3.2：规则过滤器核心逻辑")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_rule_prefilter_basic_functionality())
    test_results.append(test_rule_prefilter_mock_scenario())
    test_results.append(test_performance_stats())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"✅ 通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！任务3.2实现质量良好")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
