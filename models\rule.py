from pydantic import BaseModel, Field

from .patient import PatientData


class RuleRequest(BaseModel):
    """规则验证请求"""

    patientInfo: PatientData = Field(..., description="患者数据")
    ids: list[str] = Field(..., description="要验证的规则ID列表")


class RuleOutput(BaseModel):
    """规则输出"""

    type_: str = Field("", description="一级错误类型(原)")
    message: str = Field("", description="错误原因(原)")
    level1: str = Field("", description="一级错误类型")
    level2: str = Field("", description="二级错误类型")
    level3: str = Field("", description="三级错误类型")
    error_reason: str = Field("", description="错误原因")
    degree: str = Field("", description="错误程度:枚举类型:强制,疑似")
    reference: str = Field("", description="质控依据或参考资料")
    prompted_fields3: str = Field("", description="提示字段类型")
    prompted_fields1: str = Field("", description="提示字段编码")
    prompted_fields2: str = Field("", description="提示字段序号")
    detail_position: str = Field("", description="具体位置描述")
    type: str = Field("", description="规则类别,枚举类型:编码,非编码")
    pos: str = Field("", description="适用业务,枚举类型:通用,清单,首页")
    applicableArea: str = Field("", description="适用地区")
    default_use: str = Field("", description="默认选用:枚举类型,是,否")
    error_fee: float = Field(0, description="涉及金额")
    remarks: str = Field("", description="备注信息")
    in_illustration: str = Field("", description="入参说明")
    used_count: float = Field(0, description="使用数量")
    illegal_count: float = Field(0, description="违规数量")
    used_day: int = Field(0, description="使用天数")
    illegal_day: int = Field(0, description="违规天数")
    illegal_item: str = Field("", description="违规项目")


class RuleResult(BaseModel):
    """单条规则验证结果"""

    id: str = Field(..., description="规则ID")
    output: RuleOutput = Field(..., description="规则输出")
