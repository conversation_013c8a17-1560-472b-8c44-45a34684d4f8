/**
 * 规则明细API函数单元测试
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import * as ruleDetailsApi from '../../api/ruleDetails'
import * as request from '../../api/request'

// Mock request模块
vi.mock('../../api/request', () => ({
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  del: vi.fn()
}))

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: vi.fn(),
  ElNotification: vi.fn()
}))

describe('规则明细API函数测试', () => {
  const mockRuleKey = 'test-rule'
  const mockDetailId = 123
  const mockData = {
    rule_detail_id: 'detail-001',
    rule_name: '测试规则',
    status: 'ACTIVE'
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('基础CRUD操作', () => {
    it('应该正确调用获取规则明细列表API', async () => {
      const mockResponse = {
        success: true,
        data: {
          items: [mockData],
          total: 1,
          page: 1,
          page_size: 10
        }
      }
      request.get.mockResolvedValue(mockResponse)

      const params = { page: 1, page_size: 10 }
      const result = await ruleDetailsApi.getRuleDetailsList(mockRuleKey, params)

      expect(request.get).toHaveBeenCalledWith(`/v1/rules/details/${mockRuleKey}`, params)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用获取单条规则明细API', async () => {
      const mockResponse = {
        success: true,
        data: mockData
      }
      request.get.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.getRuleDetailById(mockRuleKey, mockDetailId)

      expect(request.get).toHaveBeenCalledWith(`/v1/rules/details/${mockRuleKey}/${mockDetailId}`)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用创建规则明细API', async () => {
      const mockResponse = {
        success: true,
        data: { ...mockData, id: mockDetailId }
      }
      request.post.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.createRuleDetail(mockRuleKey, mockData)

      expect(request.post).toHaveBeenCalledWith(`/v1/rules/details/${mockRuleKey}`, mockData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用更新规则明细API', async () => {
      const updateData = { rule_name: '更新后的规则名称' }
      const mockResponse = {
        success: true,
        data: { ...mockData, ...updateData }
      }
      request.put.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.updateRuleDetail(mockRuleKey, mockDetailId, updateData)

      expect(request.put).toHaveBeenCalledWith(`/v1/rules/details/${mockRuleKey}/${mockDetailId}`, updateData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用删除规则明细API', async () => {
      const mockResponse = {
        success: true,
        message: '删除成功'
      }
      request.del.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.deleteRuleDetail(mockRuleKey, mockDetailId)

      expect(request.del).toHaveBeenCalledWith(`/v1/rules/details/${mockRuleKey}/${mockDetailId}`)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('批量操作', () => {
    it('应该正确调用批量操作API', async () => {
      const batchData = {
        operations: [
          { action: 'CREATE', data: mockData },
          { action: 'UPDATE', id: 1, data: { rule_name: '更新' } }
        ]
      }
      const mockResponse = {
        success: true,
        data: {
          success_count: 2,
          error_count: 0,
          results: []
        }
      }
      request.post.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.batchOperateRuleDetails(mockRuleKey, batchData)

      expect(request.post).toHaveBeenCalledWith(`/v1/rules/${mockRuleKey}/details/batch`, batchData)
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用批量创建API', async () => {
      const details = [mockData, { ...mockData, rule_detail_id: 'detail-002' }]
      const mockResponse = { success: true, data: { success_count: 2 } }
      request.post.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.batchCreateRuleDetails(mockRuleKey, details)

      expect(request.post).toHaveBeenCalledWith(
        `/v1/rules/${mockRuleKey}/details/batch`,
        {
          operations: details.map(data => ({ action: 'CREATE', data }))
        }
      )
      expect(result).toEqual(mockResponse)
    })

    it('应该正确调用批量删除API', async () => {
      const detailIds = [1, 2, 3]
      const mockResponse = { success: true, data: { success_count: 3 } }
      request.post.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.batchDeleteRuleDetails(mockRuleKey, detailIds)

      expect(request.post).toHaveBeenCalledWith(
        `/v1/rules/${mockRuleKey}/details/batch`,
        {
          operations: detailIds.map(id => ({ action: 'DELETE', id }))
        }
      )
      expect(result).toEqual(mockResponse)
    })
  })

  describe('查询和搜索', () => {
    it('应该正确调用搜索API', async () => {
      const searchParams = {
        keyword: '测试',
        fields: ['rule_name', 'error_reason']
      }
      const mockResponse = {
        success: true,
        data: { items: [mockData], total: 1 }
      }
      request.get.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.searchRuleDetails(mockRuleKey, searchParams)

      expect(request.get).toHaveBeenCalledWith(`/v1/rules/${mockRuleKey}/details/search`, searchParams)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('统计和分析', () => {
    it('应该正确调用统计API', async () => {
      const mockResponse = {
        success: true,
        data: {
          total_count: 100,
          status_distribution: { ACTIVE: 80, INACTIVE: 20 }
        }
      }
      request.get.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.getRuleDetailsStatistics(mockRuleKey)

      expect(request.get).toHaveBeenCalledWith(`/v1/rules/${mockRuleKey}/details/statistics`)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('导入导出', () => {
    it('应该正确调用导出API', async () => {
      const exportParams = {
        format: 'excel',
        filters: { status: 'ACTIVE' }
      }
      const mockResponse = {
        success: true,
        data: { download_url: 'http://example.com/export.xlsx' }
      }
      request.get.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.exportRuleDetails(mockRuleKey, exportParams)

      expect(request.get).toHaveBeenCalledWith(`/v1/rules/${mockRuleKey}/details/export`, exportParams)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('验证和校验', () => {
    it('应该正确调用验证API', async () => {
      const validateData = [mockData]
      const mockResponse = {
        success: true,
        data: {
          valid: true,
          errors: [],
          warnings: []
        }
      }
      request.post.mockResolvedValue(mockResponse)

      const result = await ruleDetailsApi.validateRuleDetailsData(mockRuleKey, validateData)

      expect(request.post).toHaveBeenCalledWith(
        `/v1/rules/${mockRuleKey}/details/validate`,
        { data: validateData }
      )
      expect(result).toEqual(mockResponse)
    })
  })

  describe('错误处理', () => {
    it('应该正确处理API调用错误', async () => {
      const mockError = new Error('网络错误')
      mockError.response = { status: 500 }
      request.get.mockRejectedValue(mockError)

      await expect(ruleDetailsApi.getRuleDetailsList(mockRuleKey)).rejects.toThrow()
    })

    it('应该正确处理验证错误', async () => {
      const mockError = new Error('验证失败')
      mockError.response = {
        status: 422,
        data: { message: '数据验证失败' }
      }
      request.post.mockRejectedValue(mockError)

      await expect(ruleDetailsApi.createRuleDetail(mockRuleKey, {})).rejects.toThrow()
    })
  })
})
