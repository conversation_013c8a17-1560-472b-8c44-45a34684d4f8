<template>
  <div class="trend-indicator" :class="trendClass">
    <el-icon class="trend-icon" :size="iconSize">
      <component :is="trendIcon" />
    </el-icon>
    <span class="trend-value" v-if="showValue">{{ formattedValue }}</span>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  trend: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && typeof value.direction === 'string' && typeof value.value === 'number'
    }
  },
  showValue: {
    type: Boolean,
    default: true
  },
  iconSize: {
    type: [String, Number],
    default: 14
  },
  precision: {
    type: Number,
    default: 1
  }
})

// 计算属性
const trendClass = computed(() => {
  return `trend-${props.trend.direction}`
})

const trendIcon = computed(() => {
  const iconMap = {
    up: ArrowUp,
    down: ArrowDown,
    stable: Minus
  }
  return iconMap[props.trend.direction] || Minus
})

const formattedValue = computed(() => {
  const value = Math.abs(props.trend.value)
  if (props.trend.type === 'percentage') {
    return `${value.toFixed(props.precision)}%`
  }
  return value.toFixed(props.precision)
})
</script>

<style scoped>
.trend-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.trend-down {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}

.trend-stable {
  color: #909399;
  background: rgba(144, 147, 153, 0.1);
}

.trend-icon {
  flex-shrink: 0;
}

.trend-value {
  font-size: inherit;
}
</style>
