"""
Task 2.1: 主节点索引加载集成测试
测试与数据库、索引管理器的集成功能

集成测试覆盖：
1. 真实数据库连接和查询
2. 与索引管理器的集成
3. 与校验服务的集成
4. 配置加载和环境变量
5. 完整的生命周期测试
"""

import asyncio
import pytest
import time
from unittest.mock import patch

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from config.settings import settings
from models.database import Base, RuleDetail, RuleTemplate, RuleDetailStatusEnum
from services.master_node_index_loader import MasterNodeIndexLoader
from core.rule_index_manager import rule_index_manager


@pytest.mark.integration
class TestMasterNodeIndexIntegration:
    """主节点索引加载集成测试类"""

    @pytest.fixture(scope="class")
    def test_db_engine(self):
        """测试数据库引擎"""
        # 使用内存SQLite数据库进行测试
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        return engine

    @pytest.fixture(scope="class")
    def test_db_session(self, test_db_engine):
        """测试数据库会话"""
        SessionLocal = sessionmaker(bind=test_db_engine)
        session = SessionLocal()
        yield session
        session.close()

    @pytest.fixture
    def populated_database(self, test_db_session):
        """填充测试数据的数据库"""
        # 创建规则模板
        template1 = RuleTemplate(
            rule_key="drug_validation",
            rule_type="药品校验",
            name="药品使用规则校验",
            description="验证药品使用的合理性"
        )
        test_db_session.add(template1)
        
        template2 = RuleTemplate(
            rule_key="diagnosis_validation", 
            rule_type="诊断校验",
            name="诊断编码规则校验",
            description="验证诊断编码的准确性"
        )
        test_db_session.add(template2)
        
        # 创建规则明细
        rule_details = []
        
        # 药品相关规则
        for i in range(10):
            rule = RuleDetail(
                rule_id=f"DRUG_RULE_{i:03d}",
                rule_key="drug_validation",
                rule_name=f"药品规则{i:03d}",
                level1="用药安全",
                level2="用法用量",
                level3="剂量检查",
                error_reason=f"药品{i:03d}剂量异常",
                degree="严重",
                reference="药品说明书",
                detail_position="费用明细",
                prompted_fields1="ybdm",
                type="用药规则",
                pos="住院",
                applicableArea="全国",
                default_use="是",
                start_date="2023-01-01",
                end_date="2025-12-31",
                status=RuleDetailStatusEnum.ACTIVE
            )
            rule.yb_code = [f"YB{i:04d}", f"YB{i:04d}A"]
            rule.fee_whole_code = [f"310{i:03d}001"]
            rule.fee_code_prefix = [f"310{i:02d}"]
            rule_details.append(rule)
        
        # 诊断相关规则
        for i in range(10, 20):
            rule = RuleDetail(
                rule_id=f"DIAG_RULE_{i:03d}",
                rule_key="diagnosis_validation",
                rule_name=f"诊断规则{i:03d}",
                level1="诊断准确性",
                level2="编码规范",
                level3="ICD编码",
                error_reason=f"诊断编码{i:03d}不规范",
                degree="中等",
                reference="ICD-10标准",
                detail_position="诊断信息",
                prompted_fields1="zdmc",
                type="诊断规则",
                pos="门诊",
                applicableArea="全国",
                default_use="是",
                start_date="2023-01-01",
                end_date="2025-12-31",
                status=RuleDetailStatusEnum.ACTIVE
            )
            rule.diag_whole_code = [f"I{i}", f"J{i}"]
            rule.diag_code_prefix = [f"I{i//10}", f"J{i//10}"]
            rule_details.append(rule)
        
        # 手术相关规则（使用扩展字段）
        for i in range(20, 25):
            rule = RuleDetail(
                rule_id=f"SURG_RULE_{i:03d}",
                rule_key="surgery_validation",
                rule_name=f"手术规则{i:03d}",
                level1="手术安全",
                level2="操作规范",
                level3="手术编码",
                error_reason=f"手术编码{i:03d}异常",
                degree="严重",
                reference="手术操作规范",
                detail_position="手术记录",
                prompted_fields1="ssmc",
                type="手术规则",
                pos="住院",
                applicableArea="全国",
                default_use="是",
                start_date="2023-01-01",
                end_date="2025-12-31",
                status=RuleDetailStatusEnum.ACTIVE,
                extended_fields=f'{{"surgery_code": "SS{i:03d},SS{i:03d}A"}}'
            )
            rule_details.append(rule)
        
        # 通用规则（无特定代码限制）
        universal_rule = RuleDetail(
            rule_id="UNIVERSAL_001",
            rule_key="universal_validation",
            rule_name="通用校验规则",
            level1="通用检查",
            level2="基础校验",
            level3="数据完整性",
            error_reason="数据完整性检查失败",
            degree="轻微",
            reference="基础数据规范",
            detail_position="全部数据",
            prompted_fields1="all",
            type="通用规则",
            pos="通用",
            applicableArea="全国",
            default_use="是",
            start_date="2023-01-01",
            end_date="2025-12-31",
            status=RuleDetailStatusEnum.ACTIVE
        )
        rule_details.append(universal_rule)
        
        # 添加所有规则到数据库
        for rule in rule_details:
            test_db_session.add(rule)
        
        test_db_session.commit()
        return test_db_session

    @pytest.fixture
    def loader_with_db(self, populated_database):
        """带有数据库连接的加载器"""
        loader = MasterNodeIndexLoader()
        loader._db_session = populated_database
        return loader

    @pytest.mark.asyncio
    async def test_full_integration_flow(self, loader_with_db):
        """测试完整的集成流程"""
        # 1. 执行全量索引构建
        result = await loader_with_db.build_full_index()
        
        # 验证构建结果
        assert result.success is True
        assert result.rule_count == 26  # 25个具体规则 + 1个通用规则
        assert result.build_duration_ms > 0
        
        # 2. 验证索引管理器状态
        assert rule_index_manager.is_ready() is True
        
        # 3. 验证索引内容
        stats = rule_index_manager.get_performance_stats()
        assert stats["index_metadata"]["rule_count"] == 26
        assert stats["universal_rules_count"] == 1
        
        # 验证各类型索引
        exact_indexes = stats["exact_index_sizes"]
        assert exact_indexes["yb_code"] > 0  # 医保代码索引
        assert exact_indexes["diag_code"] > 0  # 诊断代码索引
        assert exact_indexes["surgery_code"] > 0  # 手术代码索引

    @pytest.mark.asyncio
    async def test_database_query_optimization(self, loader_with_db, populated_database):
        """测试数据库查询优化"""
        # 清理索引管理器状态
        rule_index_manager._clear_indexes()
        
        # 监控数据库查询
        with patch.object(populated_database, 'query', wraps=populated_database.query) as mock_query:
            # 执行索引构建
            start_time = time.perf_counter()
            result = await loader_with_db.build_full_index()
            query_time = time.perf_counter() - start_time
            
            # 验证查询优化
            assert result.success is True
            assert query_time < 5.0  # 查询时间应该很快
            
            # 验证查询调用次数（应该只有一次主查询）
            assert mock_query.call_count >= 1
            
    @pytest.mark.asyncio 
    async def test_incremental_update_integration(self, loader_with_db, populated_database):
        """测试增量更新集成"""
        # 先构建基础索引
        await loader_with_db.build_full_index()
        initial_stats = rule_index_manager.get_performance_stats()
        
        # 创建新规则用于增量更新
        new_rule = RuleDetail(
            rule_id="NEW_RULE_001",
            rule_key="new_validation",
            rule_name="新增校验规则",
            level1="新规则",
            level2="测试用",
            level3="增量更新",
            error_reason="增量更新测试",
            degree="轻微",
            reference="测试规范",
            detail_position="测试位置",
            prompted_fields1="test",
            type="测试规则",
            pos="测试",
            applicableArea="全国",
            default_use="是",
            start_date="2023-01-01",
            end_date="2025-12-31",
            status=RuleDetailStatusEnum.ACTIVE
        )
        new_rule.yb_code = ["NEW001"]
        
        populated_database.add(new_rule)
        populated_database.commit()
        
        # 执行增量更新
        start_time = time.perf_counter()
        result = await loader_with_db.incremental_update([new_rule])
        update_time = time.perf_counter() - start_time
        
        # 验证增量更新结果
        assert result.success is True
        assert result.updated_count == 1
        assert update_time < 5.0  # 验收标准：5秒内完成
        
        # 验证索引已更新
        updated_stats = rule_index_manager.get_performance_stats()
        assert updated_stats["index_metadata"]["rule_count"] == initial_stats["index_metadata"]["rule_count"] + 1

    @pytest.mark.asyncio
    async def test_concurrent_operations_integration(self, loader_with_db):
        """测试并发操作集成"""
        # 定义并发任务
        async def build_task():
            return await loader_with_db.build_full_index()
        
        async def query_task():
            await asyncio.sleep(0.1)
            return rule_index_manager.is_ready()
        
        async def stats_task():
            await asyncio.sleep(0.2)
            return rule_index_manager.get_performance_stats()
        
        # 并发执行任务
        tasks = [
            build_task(),
            query_task(),
            query_task(),
            stats_task()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证结果
        build_result = results[0]
        assert build_result.success is True
        
        # 查询任务不应该被阻塞
        for i in range(1, 4):
            assert not isinstance(results[i], Exception)

    @pytest.mark.asyncio
    async def test_error_recovery_integration(self, loader_with_db, populated_database):
        """测试错误恢复集成"""
        # 模拟数据库错误恢复场景
        original_query = populated_database.query
        
        # 第一次调用失败
        call_count = 0
        def failing_query(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("Temporary database error")
            return original_query(*args, **kwargs)
        
        # 使用带重试机制的加载器
        with patch.object(populated_database, 'query', side_effect=failing_query):
            # 第一次调用应该失败
            result1 = await loader_with_db.build_full_index()
            assert result1.success is False
            
            # 第二次调用应该成功（模拟错误恢复）
            result2 = await loader_with_db.build_full_index()
            assert result2.success is True

    def test_configuration_integration(self):
        """测试配置集成"""
        # 测试配置加载
        loader = MasterNodeIndexLoader()
        
        # 验证默认配置
        assert loader._sync_interval > 0
        assert loader._max_retry_count > 0
        assert loader._timeout_seconds > 0
        
        # 测试环境变量覆盖
        with patch.dict('os.environ', {
            'INDEX_SYNC_INTERVAL': '60',
            'INDEX_MAX_RETRIES': '5',
            'INDEX_TIMEOUT': '120'
        }):
            new_loader = MasterNodeIndexLoader()
            # 注意：实际实现中需要在构造函数中读取环境变量
            # 这里只是测试结构

    @pytest.mark.asyncio
    async def test_memory_pressure_handling(self, test_db_session):
        """测试内存压力下的处理"""
        # 创建大量测试数据
        large_rules = []
        for i in range(1000):
            rule = RuleDetail(
                rule_id=f"LARGE_RULE_{i:04d}",
                rule_key="large_validation",
                rule_name=f"大数据量规则{i:04d}",
                level1="压力测试",
                level2="内存管理",
                level3="大数据",
                error_reason=f"大数据测试{i:04d}",
                degree="轻微",
                reference="压力测试规范",
                detail_position="内存测试",
                prompted_fields1="memory",
                type="压力规则",
                pos="测试",
                applicableArea="全国",
                default_use="是",
                start_date="2023-01-01",
                end_date="2025-12-31",
                status=RuleDetailStatusEnum.ACTIVE
            )
            rule.yb_code = [f"LARGE{i:04d}"]
            large_rules.append(rule)
        
        # 批量添加到数据库
        test_db_session.add_all(large_rules)
        test_db_session.commit()
        
        # 创建加载器并测试
        loader = MasterNodeIndexLoader()
        loader._db_session = test_db_session
        
        # 执行索引构建
        result = await loader.build_full_index()
        
        # 验证内存管理
        assert result.success is True
        assert result.memory_usage_mb > 0
        # 验证内存使用在合理范围内
        assert result.memory_usage_mb < 100.0  # 1000条规则不应超过100MB

    @pytest.mark.asyncio
    async def test_index_validation_integration(self, loader_with_db):
        """测试索引验证集成"""
        # 构建索引
        result = await loader_with_db.build_full_index()
        assert result.success is True
        
        # 验证索引完整性
        validation_result = rule_index_manager.validate_indexes()
        assert validation_result["valid"] is True
        assert validation_result["total_indexes"] > 0
        assert len(validation_result["errors"]) == 0

    @pytest.mark.asyncio
    async def test_performance_monitoring_integration(self, loader_with_db):
        """测试性能监控集成"""
        # 执行多次操作以收集性能数据
        for _ in range(3):
            await loader_with_db.build_full_index()
        
        # 获取性能统计
        perf_stats = rule_index_manager.get_detailed_performance_stats()
        
        # 验证性能数据
        assert perf_stats.query_count > 0
        assert perf_stats.avg_query_time_ms > 0
        assert perf_stats.memory_usage_mb > 0
        assert perf_stats.index_efficiency > 0

    @pytest.mark.asyncio
    async def test_startup_integration(self, populated_database):
        """测试启动集成流程"""
        # 模拟应用启动过程
        loader = MasterNodeIndexLoader()
        loader._db_session = populated_database
        
        # 初始化索引（模拟启动时的索引加载）
        await loader.initialize_on_startup()
        
        # 验证启动后状态
        assert rule_index_manager.is_ready() is True
        assert loader._last_build_time > 0
        
        # 验证后台同步已启动
        assert loader._background_sync_enabled is True