# 规则查询接口使用指南

## 快速开始

规则查询接口允许您根据关键词快速查找规则并获取相关的明细规则ID列表。本指南将帮助您快速上手使用该接口。

## 基本用法

### 1. 准备API密钥

在使用接口前，请确保您已获得有效的API密钥。API密钥需要在请求头中提供：

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     "http://your-domain/api/v1/rules/ids?keyword=中药饮片"
```

### 2. 基本查询

最简单的查询方式是提供一个关键词：

```bash
# 使用中文关键词
GET /api/v1/rules/ids?keyword=中药饮片单复方均不予支付

# 使用英文关键词
GET /api/v1/rules/ids?keyword=medical_device

# 使用部分关键词
GET /api/v1/rules/ids?keyword=中药
```

### 3. 获取所有规则

如果不提供关键词，接口将返回第一个可用的规则：

```bash
GET /api/v1/rules/ids
```

## 常见使用场景

### 场景1：根据规则名称查找

当您知道规则的中文名称或部分名称时：

```javascript
// JavaScript示例
async function findRuleByName(ruleName) {
    const response = await fetch(`/api/v1/rules/ids?keyword=${encodeURIComponent(ruleName)}`, {
        headers: {
            'Authorization': 'Bearer YOUR_API_KEY'
        }
    });
    
    const result = await response.json();
    
    if (result.success) {
        console.log('找到规则:', result.data.rule_info);
        console.log('明细规则ID:', result.data.detail_rule_ids);
        return result.data.detail_rule_ids;
    } else {
        console.log('未找到匹配的规则:', result.message);
        return [];
    }
}

// 使用示例
const ruleIds = await findRuleByName('中药饮片单复方均不予支付');
```

### 场景2：根据规则键查找

当您知道规则的英文键名时：

```python
# Python示例
import requests

def find_rule_by_key(rule_key, api_key):
    url = f"/api/v1/rules/ids?keyword={rule_key}"
    headers = {"Authorization": f"Bearer {api_key}"}
    
    response = requests.get(url, headers=headers)
    result = response.json()
    
    if result['success']:
        print(f"找到规则: {result['data']['rule_info']}")
        return result['data']['detail_rule_ids']
    else:
        print(f"未找到匹配的规则: {result['message']}")
        return []

# 使用示例
rule_ids = find_rule_by_key('chinese_medicine_rule', 'YOUR_API_KEY')
```

### 场景3：模糊搜索

当您只记得规则名称的一部分时：

```bash
# 搜索包含"医疗"的规则
GET /api/v1/rules/ids?keyword=医疗

# 搜索包含"支付"的规则
GET /api/v1/rules/ids?keyword=支付

# 搜索包含"device"的规则
GET /api/v1/rules/ids?keyword=device
```

## 响应处理

### 成功响应处理

```javascript
function handleSuccessResponse(response) {
    if (response.success) {
        const ruleInfo = response.data.rule_info;
        const detailIds = response.data.detail_rule_ids;
        
        console.log(`规则键: ${ruleInfo.rule_key}`);
        console.log(`规则名称: ${ruleInfo.rule_name}`);
        console.log(`明细规则数量: ${detailIds.length}`);
        console.log(`明细规则ID: ${detailIds.join(', ')}`);
        
        return detailIds;
    }
    return null;
}
```

### 错误响应处理

```javascript
function handleErrorResponse(response) {
    if (!response.success) {
        switch (response.message) {
            case '未找到匹配的规则':
                console.log('建议：请检查关键词拼写或尝试使用更通用的关键词');
                break;
            case '该规则下暂无生效的明细规则':
                console.log('该规则存在但当前没有生效的明细规则');
                break;
            default:
                console.log(`查询失败: ${response.message}`);
        }
    }
}
```

## 最佳实践

### 1. 关键词选择

- **使用具体关键词**: 如"中药饮片"比"药品"更精确
- **避免过于通用的词**: 如"规则"、"管理"等
- **支持中英文混合**: 可以使用"medical_device医疗器械"

### 2. 错误处理

```javascript
async function robustRuleQuery(keyword) {
    try {
        const response = await fetch(`/api/v1/rules/ids?keyword=${encodeURIComponent(keyword)}`, {
            headers: {
                'Authorization': 'Bearer YOUR_API_KEY',
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            return result.data.detail_rule_ids;
        } else {
            console.warn(`查询警告: ${result.message}`);
            return [];
        }
    } catch (error) {
        console.error('查询失败:', error);
        return [];
    }
}
```

### 3. 缓存策略

对于频繁查询的规则，建议实现客户端缓存：

```javascript
class RuleQueryCache {
    constructor(ttl = 300000) { // 5分钟缓存
        this.cache = new Map();
        this.ttl = ttl;
    }
    
    async query(keyword) {
        const cacheKey = keyword.toLowerCase();
        const cached = this.cache.get(cacheKey);
        
        if (cached && Date.now() - cached.timestamp < this.ttl) {
            return cached.data;
        }
        
        const result = await robustRuleQuery(keyword);
        this.cache.set(cacheKey, {
            data: result,
            timestamp: Date.now()
        });
        
        return result;
    }
}
```

## 故障排除

### 常见问题

1. **403 Forbidden错误**
   - 检查API密钥是否正确
   - 确认API密钥是否已过期
   - 验证请求头格式是否正确

2. **查询无结果**
   - 尝试使用更简短的关键词
   - 检查关键词拼写
   - 确认规则是否处于生效状态

3. **响应慢**
   - 考虑使用更具体的关键词
   - 实现客户端缓存
   - 检查网络连接

### 调试技巧

```javascript
// 开启详细日志
function debugRuleQuery(keyword) {
    console.log(`开始查询规则: "${keyword}"`);
    
    return fetch(`/api/v1/rules/ids?keyword=${encodeURIComponent(keyword)}`)
        .then(response => {
            console.log(`HTTP状态: ${response.status}`);
            return response.json();
        })
        .then(result => {
            console.log('响应结果:', JSON.stringify(result, null, 2));
            return result;
        })
        .catch(error => {
            console.error('请求失败:', error);
            throw error;
        });
}
```

## 联系支持

如果您在使用过程中遇到问题，请联系技术支持团队，并提供以下信息：

- 使用的关键词
- 完整的错误信息
- 请求时间戳
- API密钥（脱敏后的前几位）
