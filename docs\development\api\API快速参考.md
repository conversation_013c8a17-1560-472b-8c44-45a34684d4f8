# API Quick Reference Guide

## Project Structure Overview

```
api/
├── middleware/          # Cross-cutting concerns
├── routers/            # API endpoint definitions
├── dependencies/       # Dependency injection
└── controllers/        # (Future: business logic)
```

## Adding New Endpoints

### 1. Master Node Endpoint (Public)
```python
# File: api/routers/master/your_router.py
from fastapi import APIRouter
from models import ApiResponse

router = APIRouter(prefix="/api/v1", tags=["Your Feature"])

@router.get("/your-endpoint")
async def your_endpoint():
    return ApiResponse(success=True, message="Success")
```

### 2. Master Node Endpoint (Secured)
```python
# File: api/routers/master/your_router.py
from fastapi import APIRouter
from api.dependencies.auth import get_api_key_dependency

router = APIRouter(
    prefix="/api/v1", 
    tags=["Your Feature"],
    dependencies=[get_api_key_dependency()]
)

@router.get("/your-endpoint")
async def your_endpoint():
    return {"message": "Secured endpoint"}
```

### 3. Endpoint with Database Access
```python
from fastapi import APIRouter
from sqlalchemy.orm import Session
from api.dependencies.database import get_db_dependency

router = APIRouter(prefix="/api/v1")

@router.get("/your-endpoint")
async def your_endpoint(session: Session = get_db_dependency()):
    # Use session for database operations
    return {"message": "Database endpoint"}
```

### 4. Endpoint with Service Dependency
```python
from fastapi import APIRouter, Depends
from api.dependencies.services import get_rule_service_dependency
from services.rule_service import RuleService

router = APIRouter(prefix="/api/v1")

@router.post("/your-endpoint")
async def your_endpoint(
    rule_service: RuleService = Depends(get_rule_service_dependency)
):
    # Use rule_service for business logic
    return {"message": "Service endpoint"}
```

## Router Registration

### Master Node
```python
# File: api/routers/master/__init__.py
from .your_router import your_router

master_routers = [
    validation_router,
    management_router,
    sync_router,
    health_router,
    your_router,  # Add your router here
]
```

### Slave Node
```python
# File: api/routers/slave/__init__.py
from .your_router import your_router

slave_routers = [
    validation_router,
    health_router,
    your_router,  # Add your router here
]
```

## Error Handling Patterns

### Standard Error Response
```python
from fastapi import HTTPException

@router.get("/your-endpoint")
async def your_endpoint():
    if error_condition:
        raise HTTPException(
            status_code=400, 
            detail="Descriptive error message"
        )
    return {"message": "Success"}
```

### Exception Chaining
```python
@router.post("/your-endpoint")
async def your_endpoint():
    try:
        # Your logic here
        pass
    except SpecificException as e:
        logger.error(f"Specific error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, 
            detail="Internal error occurred"
        ) from e
```

## Middleware Usage

### Custom Middleware
```python
# File: api/middleware/your_middleware.py
from starlette.middleware.base import BaseHTTPMiddleware

class YourMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        # Pre-processing
        response = await call_next(request)
        # Post-processing
        return response
```

### Register Middleware
```python
# File: master.py or slave.py
from api.middleware.your_middleware import YourMiddleware

app.add_middleware(YourMiddleware)
```

## Dependency Injection

### Create New Dependency
```python
# File: api/dependencies/your_dependencies.py
from typing import Optional

_your_service: Optional[YourService] = None

def set_your_service(service: YourService):
    global _your_service
    _your_service = service

def get_your_service_dependency() -> YourService:
    if _your_service is None:
        raise RuntimeError("Your service not initialized")
    return _your_service
```

### Use in Startup
```python
# File: master.py or slave.py
from api.dependencies.your_dependencies import set_your_service

async def lifespan(app: FastAPI):
    # Initialize your service
    your_service = YourService()
    set_your_service(your_service)
    
    yield
    
    # Cleanup if needed
    your_service.close()
```

## Common Patterns

### Validation with Pydantic
```python
from pydantic import BaseModel
from fastapi import APIRouter

class YourRequest(BaseModel):
    field1: str
    field2: int

router = APIRouter()

@router.post("/your-endpoint")
async def your_endpoint(request: YourRequest):
    # request is automatically validated
    return {"received": request.dict()}
```

### Async Operations
```python
@router.post("/async-endpoint")
async def async_endpoint():
    # For CPU-bound tasks, use executor
    loop = asyncio.get_running_loop()
    result = await loop.run_in_executor(
        None, 
        cpu_bound_function, 
        args
    )
    return {"result": result}
```

### File Operations
```python
from fastapi import UploadFile, File
from fastapi.responses import FileResponse

@router.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    # Handle file upload
    return {"filename": file.filename}

@router.get("/download")
async def download_file():
    return FileResponse(
        path="path/to/file",
        filename="download.txt"
    )
```

## Testing Patterns

### Router Testing
```python
from fastapi.testclient import TestClient
from your_app import app

client = TestClient(app)

def test_your_endpoint():
    response = client.get("/api/v1/your-endpoint")
    assert response.status_code == 200
    assert response.json()["message"] == "Success"
```

### Dependency Override
```python
from fastapi import Depends

def override_dependency():
    return MockService()

app.dependency_overrides[get_your_service_dependency] = override_dependency
```

## Security Best Practices

1. **Always validate input** using Pydantic models
2. **Use dependency injection** for authentication
3. **Log security events** appropriately
4. **Handle errors gracefully** without exposing internal details
5. **Use HTTPS** in production
6. **Validate API keys** on secured endpoints

## Performance Tips

1. **Use async/await** for I/O operations
2. **Use executors** for CPU-bound tasks
3. **Implement proper caching** where appropriate
4. **Monitor database query performance**
5. **Use connection pooling** for external services
6. **Implement request timeouts**

## Debugging

### Enable Debug Logging
```python
import logging
logging.getLogger("your_module").setLevel(logging.DEBUG)
```

### Request/Response Logging
The logging middleware automatically logs all requests and responses with timing information.

### Error Tracking
All exceptions are automatically logged with full stack traces through the error handling middleware.
