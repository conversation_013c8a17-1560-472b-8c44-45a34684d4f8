/**
 * 降级监控和管理API模块
 * 提供降级状态查询、手动控制、历史查询等功能
 */

import { get, post } from './request'

/**
 * 获取当前降级状态
 * @returns {Promise} 降级状态数据
 */
export function getDegradationStatus() {
  return get('/v1/degradation/status')
}

/**
 * 获取组件降级状态
 * @returns {Promise} 组件状态列表
 */
export function getComponentsStatus() {
  return get('/v1/degradation/components')
}

/**
 * 获取降级指标统计
 * @returns {Promise} 指标统计数据
 */
export function getDegradationMetrics() {
  return get('/v1/degradation/metrics')
}

/**
 * 获取降级事件日志
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 返回记录数量限制
 * @param {string} params.event_types - 事件类型过滤（逗号分隔）
 * @param {number} params.start_time - 开始时间戳
 * @param {number} params.end_time - 结束时间戳
 * @returns {Promise} 事件日志列表
 */
export function getDegradationEvents(params = {}) {
  return get('/v1/degradation/events', params)
}

/**
 * 获取降级历史记录
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 返回记录数量限制
 * @param {number} params.start_time - 开始时间戳
 * @param {number} params.end_time - 结束时间戳
 * @returns {Promise} 历史记录列表
 */
export function getDegradationHistory(params = {}) {
  return get('/v1/degradation/history', params)
}

/**
 * 获取降级对性能的影响
 * @returns {Promise} 性能影响数据
 */
export function getDegradationPerformanceImpact() {
  return get('/v1/degradation/performance')
}

/**
 * 手动触发降级
 * @param {Object} data - 降级请求数据
 * @param {string} data.level - 目标降级级别
 * @param {string} data.reason - 降级原因
 * @param {boolean} data.force - 是否强制执行
 * @returns {Promise} 操作结果
 */
export function triggerDegradation(data) {
  return post('/v1/degradation/trigger', data)
}

/**
 * 手动恢复正常状态
 * @param {string} reason - 恢复原因
 * @returns {Promise} 操作结果
 */
export function recoverDegradation(reason = 'Manual recovery from web interface') {
  return post('/v1/degradation/recover', {}, {
    params: { reason }
  })
}

/**
 * 设置指定降级级别
 * @param {Object} data - 级别设置数据
 * @param {string} data.level - 目标降级级别
 * @param {string} data.reason - 设置原因
 * @param {number} data.duration - 持续时间（秒），可选
 * @returns {Promise} 操作结果
 */
export function setDegradationLevel(data) {
  return post('/v1/degradation/level', data)
}

/**
 * 降级级别常量
 */
export const DEGRADATION_LEVELS = {
  NORMAL: 'normal',
  LIGHT: 'light_degradation',
  MODERATE: 'moderate_degradation',
  SEVERE: 'severe_degradation'
}

/**
 * 降级级别显示名称映射
 */
export const DEGRADATION_LEVEL_NAMES = {
  [DEGRADATION_LEVELS.NORMAL]: '正常',
  [DEGRADATION_LEVELS.LIGHT]: '轻度降级',
  [DEGRADATION_LEVELS.MODERATE]: '中度降级',
  [DEGRADATION_LEVELS.SEVERE]: '重度降级'
}

/**
 * 降级级别颜色映射
 */
export const DEGRADATION_LEVEL_COLORS = {
  [DEGRADATION_LEVELS.NORMAL]: 'success',
  [DEGRADATION_LEVELS.LIGHT]: 'warning',
  [DEGRADATION_LEVELS.MODERATE]: 'danger',
  [DEGRADATION_LEVELS.SEVERE]: 'danger'
}

/**
 * 事件类型常量
 */
export const EVENT_TYPES = {
  DEGRADATION_TRIGGERED: 'degradation_triggered',
  DEGRADATION_RECOVERED: 'degradation_recovered',
  LEVEL_CHANGED: 'level_changed',
  MANUAL_TRIGGER: 'manual_trigger',
  MANUAL_RECOVERY: 'manual_recovery',
  THRESHOLD_EXCEEDED: 'threshold_exceeded',
  THRESHOLD_RECOVERED: 'threshold_recovered'
}

/**
 * 事件类型显示名称映射
 */
export const EVENT_TYPE_NAMES = {
  [EVENT_TYPES.DEGRADATION_TRIGGERED]: '降级触发',
  [EVENT_TYPES.DEGRADATION_RECOVERED]: '降级恢复',
  [EVENT_TYPES.LEVEL_CHANGED]: '级别变更',
  [EVENT_TYPES.MANUAL_TRIGGER]: '手动触发',
  [EVENT_TYPES.MANUAL_RECOVERY]: '手动恢复',
  [EVENT_TYPES.THRESHOLD_EXCEEDED]: '阈值超限',
  [EVENT_TYPES.THRESHOLD_RECOVERED]: '阈值恢复'
}

/**
 * 触发器类型常量
 */
export const TRIGGER_TYPES = {
  CPU_USAGE: 'cpu_usage',
  MEMORY_USAGE: 'memory_usage',
  ERROR_RATE: 'error_rate',
  RESPONSE_TIME: 'response_time',
  MANUAL: 'manual'
}

/**
 * 触发器类型显示名称映射
 */
export const TRIGGER_TYPE_NAMES = {
  [TRIGGER_TYPES.CPU_USAGE]: 'CPU使用率',
  [TRIGGER_TYPES.MEMORY_USAGE]: '内存使用率',
  [TRIGGER_TYPES.ERROR_RATE]: '错误率',
  [TRIGGER_TYPES.RESPONSE_TIME]: '响应时间',
  [TRIGGER_TYPES.MANUAL]: '手动触发'
}

/**
 * 格式化降级级别显示
 * @param {string} level - 降级级别
 * @returns {string} 格式化后的显示名称
 */
export function formatDegradationLevel(level) {
  return DEGRADATION_LEVEL_NAMES[level] || level
}

/**
 * 获取降级级别颜色
 * @param {string} level - 降级级别
 * @returns {string} 颜色类型
 */
export function getDegradationLevelColor(level) {
  return DEGRADATION_LEVEL_COLORS[level] || 'info'
}

/**
 * 格式化事件类型显示
 * @param {string} eventType - 事件类型
 * @returns {string} 格式化后的显示名称
 */
export function formatEventType(eventType) {
  return EVENT_TYPE_NAMES[eventType] || eventType
}

/**
 * 格式化触发器类型显示
 * @param {string} triggerType - 触发器类型
 * @returns {string} 格式化后的显示名称
 */
export function formatTriggerType(triggerType) {
  return TRIGGER_TYPE_NAMES[triggerType] || triggerType
}

/**
 * 格式化持续时间
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串
 */
export function formatDuration(seconds) {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`
  } else if (seconds < 3600) {
    return `${Math.round(seconds / 60)}分钟`
  } else if (seconds < 86400) {
    return `${Math.round(seconds / 3600)}小时`
  } else {
    return `${Math.round(seconds / 86400)}天`
  }
}

/**
 * 格式化百分比
 * @param {number} value - 数值（0-1）
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercentage(value) {
  return `${(value * 100).toFixed(1)}%`
}

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimestamp(timestamp) {
  return new Date(timestamp * 1000).toLocaleString('zh-CN')
}

/**
 * 检查是否为降级状态
 * @param {string} level - 降级级别
 * @returns {boolean} 是否为降级状态
 */
export function isDegraded(level) {
  return level !== DEGRADATION_LEVELS.NORMAL
}

/**
 * 获取降级级别的严重程度（数值越大越严重）
 * @param {string} level - 降级级别
 * @returns {number} 严重程度
 */
export function getDegradationSeverity(level) {
  const severityMap = {
    [DEGRADATION_LEVELS.NORMAL]: 0,
    [DEGRADATION_LEVELS.LIGHT]: 1,
    [DEGRADATION_LEVELS.MODERATE]: 2,
    [DEGRADATION_LEVELS.SEVERE]: 3
  }
  return severityMap[level] || 0
}
