"""
Authentication middleware and dependencies for API security.
"""
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON>eyHeader

from config.settings import settings

# API Key header configuration
api_key_header = APIKeyHeader(name="X-API-KEY", auto_error=True)


def verify_api_key(api_key: str = Depends(api_key_header)) -> str:
    """
    Verify API key for secured endpoints.

    Args:
        api_key: The API key from the request header

    Returns:
        str: The verified API key

    Raises:
        HTTPException: If API key is invalid or missing
    """
    if api_key != settings.MASTER_API_SECRET_KEY:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing API Key"
        )
    return api_key


class APIKeyMiddleware:
    """
    Middleware class for API key authentication.
    Can be used for more complex authentication logic if needed.
    """

    def __init__(self, api_key: str):
        self.api_key = api_key

    def verify(self) -> bool:
        """Verify the API key."""
        return self.api_key == settings.MASTER_API_SECRET_KEY
