# 规则明细 API 使用示例

本文档提供规则明细 API 的详细使用示例，包括各种场景下的请求和响应示例。

**重要更新**: 所有示例已更新为使用标准化字段名，与 `field_mapping.json` v3.1.0 配置保持一致。

## 基础配置

### 认证设置
```bash
# 设置API密钥
export API_KEY="a_very_secret_key_for_development"

# 基础URL
export BASE_URL="http://localhost:8000/api/v1/rules/details"
```

### 请求头设置
```bash
Content-Type: application/json
X-API-KEY: a_very_secret_key_for_development
```

## 1. 创建规则明细

### 基础创建示例（使用标准字段名）
```bash
curl -X POST "${BASE_URL}/drug_limit_adult_and_diag_exact" \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: ${API_KEY}" \
  -d '{
    "rule_name": "成人药品限制规则",
    "level1": "药品适应症",
    "level2": "限制使用",
    "level3": "年龄限制",
    "error_reason": "该药品仅限成人使用",
    "degree": "严重",
    "reference": "药品说明书第3条",
    "detail_position": "处方明细",
    "prompted_fields1": "drug_code",
    "prompted_fields3": "药品编码",
    "type": "药品规则",
    "pos": "门诊",
    "applicableArea": "全国",
    "default_use": "是",
    "remarks": "特殊情况需医师确认",
    "in_illustration": "输入药品编码和患者年龄",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "yb_code": "XB05BA01,XB05BA02",
    "diag_whole_code": "I10.x00",
    "diag_code_prefix": "I10",
    "diag_name_keyword": "高血压",
    "fee_whole_code": "310101001",
    "fee_code_prefix": "3101",
    "extended_fields": "{\"age_threshold\": 18, \"limit_days\": 30}"
  }'
```

### 响应示例
```json
{
  "code": 200,
  "success": true,
  "message": "创建成功",
  "data": {
    "id": 1,
    "rule_id": "rule_001",
    "rule_key": "drug_limit_adult_and_diag_exact",
    "rule_name": "成人药品限制规则",
    "level1": "药品适应症",
    "level2": "限制使用",
    "level3": "年龄限制",
    "error_reason": "该药品仅限成人使用",
    "degree": "严重",
    "reference": "药品说明书第3条",
    "detail_position": "处方明细",
    "prompted_fields1": "drug_code",
    "prompted_fields3": "药品编码",
    "type": "药品规则",
    "pos": "门诊",
    "applicableArea": "全国",
    "default_use": "是",
    "remarks": "特殊情况需医师确认",
    "in_illustration": "输入药品编码和患者年龄",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "yb_code": "XB05BA01,XB05BA02",
    "diag_whole_code": "I10.x00",
    "diag_code_prefix": "I10",
    "diag_name_keyword": "高血压",
    "fee_whole_code": "310101001",
    "fee_code_prefix": "3101",
    "extended_fields": "{\"age_threshold\": 18, \"limit_days\": 30}",
    "status": "ACTIVE",
    "created_at": "2024-07-24T10:00:00Z",
    "updated_at": "2024-07-24T10:00:00Z"
  },
  "timestamp": 1721808000.123
}
```

## 2. 查询规则明细列表

### 基础查询示例
```bash
curl -X GET "${BASE_URL}/drug_limit_adult_and_diag_exact?page=1&page_size=20" \
  -H "X-API-KEY: ${API_KEY}"
```

### 带过滤条件的查询
```bash
curl -X GET "${BASE_URL}/drug_limit_adult_and_diag_exact?level1=药品适应症&level2=限制使用&status=ACTIVE&sort_by=created_at&sort_order=desc" \
  -H "X-API-KEY: ${API_KEY}"
```

### 搜索查询示例
```bash
curl -X GET "${BASE_URL}/drug_limit_adult_and_diag_exact?search=成人药品&page=1&page_size=10" \
  -H "X-API-KEY: ${API_KEY}"
```

## 3. 查询单条规则明细

```bash
curl -X GET "${BASE_URL}/drug_limit_adult_and_diag_exact/1" \
  -H "X-API-KEY: ${API_KEY}"
```

## 4. 更新规则明细

```bash
curl -X PUT "${BASE_URL}/drug_limit_adult_and_diag_exact/1" \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: ${API_KEY}" \
  -d '{
    "rule_name": "更新后的成人药品限制规则",
    "level1": "药品适应症",
    "level2": "限制使用",
    "level3": "年龄限制",
    "error_reason": "更新后的错误原因",
    "degree": "严重",
    "reference": "更新后的质控依据",
    "remarks": "更新后的备注信息",
    "extended_fields": "{\"age_threshold\": 21, \"limit_days\": 45}"
  }'
```

## 5. 删除规则明细

```bash
curl -X DELETE "${BASE_URL}/drug_limit_adult_and_diag_exact/1" \
  -H "X-API-KEY: ${API_KEY}"
```

## 6. 批量操作示例

### 批量创建、更新、删除
```bash
curl -X POST "${BASE_URL}/drug_limit_adult_and_diag_exact/batch" \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: ${API_KEY}" \
  -d '{
    "operations": [
      {
        "operation": "CREATE",
        "data": {
          "rule_name": "新规则1",
          "level1": "药品适应症",
          "level2": "限制使用",
          "level3": "年龄限制",
          "error_reason": "新规则错误原因",
          "degree": "严重",
          "reference": "新规则质控依据",
          "detail_position": "处方明细",
          "prompted_fields1": "drug_code",
          "prompted_fields3": "药品编码",
          "type": "药品规则",
          "pos": "门诊",
          "applicableArea": "全国",
          "default_use": "是",
          "start_date": "2024-01-01",
          "end_date": "2024-12-31"
        }
      },
      {
        "operation": "UPDATE",
        "rule_id": "rule_001",
        "data": {
          "rule_name": "更新规则1",
          "error_reason": "更新后的错误原因"
        }
      },
      {
        "operation": "DELETE",
        "rule_id": "rule_002"
      }
    ]
  }'
```

## 7. 增量上传示例

```bash
curl -X POST "${BASE_URL}/drug_limit_adult_and_diag_exact/incremental" \
  -H "Content-Type: application/json" \
  -H "X-API-KEY: ${API_KEY}" \
  -d '{
    "data": [
      {
        "rule_name": "增量规则1",
        "level1": "药品适应症",
        "level2": "限制使用",
        "level3": "年龄限制",
        "error_reason": "增量规则错误原因",
        "degree": "严重",
        "reference": "增量规则质控依据",
        "detail_position": "处方明细",
        "prompted_fields1": "drug_code",
        "prompted_fields3": "药品编码",
        "type": "药品规则",
        "pos": "门诊",
        "applicableArea": "全国",
        "default_use": "是",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31"
      }
    ],
    "options": {
      "upsert": true,
      "delete_missing": false
    }
  }'
```

## 错误处理示例

### 验证失败响应
```json
{
  "code": 605,
  "success": false,
  "message": "规则明细验证失败",
  "data": {
    "errors": [
      {
        "field": "rule_name",
        "message": "规则名称不能为空"
      },
      {
        "field": "level1",
        "message": "一级错误类型不能为空"
      }
    ]
  },
  "timestamp": 1721808000.123
}
```

### 资源不存在响应
```json
{
  "code": 606,
  "success": false,
  "message": "规则明细不存在",
  "data": null,
  "timestamp": 1721808000.123
}
```

## 字段映射说明

本API使用标准化字段名，与历史字段名的映射关系如下：

| 标准字段名 | 历史字段名 | 说明 |
|-----------|-----------|------|
| `level1` | `error_level_1` | 一级错误类型 |
| `level2` | `error_level_2` | 二级错误类型 |
| `level3` | `error_level_3` | 三级错误类型 |
| `degree` | `error_severity` | 错误程度 |
| `reference` | `quality_basis` | 质控依据 |
| `detail_position` | `location_desc` | 具体位置描述 |
| `prompted_fields1` | `prompt_field_code` | 提示字段编码 |
| `prompted_fields3` | `prompt_field_type` | 提示字段类型 |
| `type` | `rule_category` | 规则类别 |
| `pos` | `applicable_business` | 适用业务 |
| `applicableArea` | `applicable_region` | 适用地区 |
| `default_use` | `default_selected` | 默认选用 |
| `remarks` | `remark` | 备注信息 |
| `in_illustration` | `input_illustration` | 入参说明 |

**注意**: 客户端应使用标准字段名进行API调用，系统会自动处理字段映射。
