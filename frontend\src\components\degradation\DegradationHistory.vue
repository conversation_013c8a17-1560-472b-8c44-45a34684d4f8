<template>
  <div class="degradation-history">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="title">降级历史查询</span>
          <el-button
            :icon="Search"
            type="primary"
            @click="searchHistory"
            :loading="loading.history"
          >
            查询
          </el-button>
        </div>
      </template>

      <!-- 查询条件 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="x"
              style="width: 350px"
            />
          </el-form-item>

          <el-form-item label="事件类型">
            <el-select
              v-model="searchForm.eventTypes"
              multiple
              placeholder="选择事件类型"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="(name, type) in EVENT_TYPE_NAMES"
                :key="type"
                :label="name"
                :value="type"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="降级级别">
            <el-select
              v-model="searchForm.levels"
              multiple
              placeholder="选择降级级别"
              style="width: 200px"
              clearable
            >
              <el-option
                v-for="(name, level) in DEGRADATION_LEVEL_NAMES"
                :key="level"
                :label="name"
                :value="level"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="记录数量">
            <el-input-number
              v-model="searchForm.limit"
              :min="10"
              :max="500"
              :step="10"
              style="width: 120px"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 历史记录表格 -->
      <div class="history-table">
        <el-table
          :data="filteredHistory"
          v-loading="loading.history"
          stripe
          border
          height="500"
        >
          <el-table-column prop="timestamp" label="时间" width="180">
            <template #default="{ row }">
              {{ formatTimestamp(row.timestamp) }}
            </template>
          </el-table-column>

          <el-table-column prop="event_type" label="事件类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getEventTagType(row.event_type)" size="small">
                {{ formatEventType(row.event_type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="level" label="降级级别" width="120">
            <template #default="{ row }">
              <el-tag :type="getDegradationLevelColor(row.level)" size="small">
                {{ formatDegradationLevel(row.level) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="trigger_type" label="触发器" width="120">
            <template #default="{ row }">
              <span v-if="row.trigger_type">
                {{ formatTriggerType(row.trigger_type) }}
              </span>
              <span v-else class="text-placeholder">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="trigger_value" label="触发值" width="100">
            <template #default="{ row }">
              <span v-if="row.trigger_value !== null && row.trigger_value !== undefined">
                {{ row.trigger_value }}
              </span>
              <span v-else class="text-placeholder">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="actions_count" label="动作数" width="80" align="center">
            <template #default="{ row }">
              <el-badge :value="row.actions_count" :hidden="row.actions_count === 0">
                <el-icon><Operation /></el-icon>
              </el-badge>
            </template>
          </el-table-column>

          <el-table-column prop="metadata" label="详细信息" min-width="200">
            <template #default="{ row }">
              <div class="metadata-content">
                <div
                  v-for="(value, key) in row.metadata"
                  :key="key"
                  class="metadata-item"
                >
                  <span class="metadata-key">{{ key }}:</span>
                  <span class="metadata-value">{{ value }}</span>
                </div>
                <span v-if="Object.keys(row.metadata).length === 0" class="text-placeholder">
                  无详细信息
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                link
                @click="showEventDetail(row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination" v-if="filteredHistory.length > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredHistory.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 事件详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="事件详情"
      width="600px"
    >
      <div class="event-detail" v-if="selectedEvent">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="事件时间">
            {{ formatTimestamp(selectedEvent.timestamp) }}
          </el-descriptions-item>

          <el-descriptions-item label="事件类型">
            <el-tag :type="getEventTagType(selectedEvent.event_type)">
              {{ formatEventType(selectedEvent.event_type) }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="降级级别">
            <el-tag :type="getDegradationLevelColor(selectedEvent.level)">
              {{ formatDegradationLevel(selectedEvent.level) }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="触发器类型">
            {{ selectedEvent.trigger_type ? formatTriggerType(selectedEvent.trigger_type) : '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="触发值">
            {{ selectedEvent.trigger_value !== null ? selectedEvent.trigger_value : '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="执行动作数">
            {{ selectedEvent.actions_count }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="metadata-section" v-if="Object.keys(selectedEvent.metadata).length > 0">
          <h4>元数据信息</h4>
          <el-table :data="metadataTableData" size="small">
            <el-table-column prop="key" label="键" width="150" />
            <el-table-column prop="value" label="值" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Search, Operation } from '@element-plus/icons-vue'
import { useDegradationStore } from '../../stores/degradation'
import {
  formatTimestamp,
  formatEventType,
  formatTriggerType,
  formatDegradationLevel,
  getDegradationLevelColor,
  EVENT_TYPE_NAMES,
  DEGRADATION_LEVEL_NAMES
} from '../../api/degradation'

// 使用降级状态 store
const degradationStore = useDegradationStore()

// 从 store 中获取状态和方法
const {
  events,
  loading,
  fetchEvents
} = degradationStore

// 搜索表单
const searchForm = ref({
  dateRange: [],
  eventTypes: [],
  levels: [],
  limit: 100
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)

// 对话框状态
const detailDialogVisible = ref(false)
const selectedEvent = ref(null)

// 过滤后的历史记录
const filteredHistory = computed(() => {
  let filtered = [...events.value]

  // 时间范围过滤
  if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
    const [startTime, endTime] = searchForm.value.dateRange
    filtered = filtered.filter(event =>
      event.timestamp * 1000 >= startTime && event.timestamp * 1000 <= endTime
    )
  }

  // 事件类型过滤
  if (searchForm.value.eventTypes.length > 0) {
    filtered = filtered.filter(event =>
      searchForm.value.eventTypes.includes(event.event_type)
    )
  }

  // 降级级别过滤
  if (searchForm.value.levels.length > 0) {
    filtered = filtered.filter(event =>
      searchForm.value.levels.includes(event.level)
    )
  }

  // 按时间倒序排列
  return filtered.sort((a, b) => b.timestamp - a.timestamp)
})

// 元数据表格数据
const metadataTableData = computed(() => {
  if (!selectedEvent.value) return []

  return Object.entries(selectedEvent.value.metadata).map(([key, value]) => ({
    key,
    value: typeof value === 'object' ? JSON.stringify(value) : String(value)
  }))
})

// 搜索历史记录
const searchHistory = async () => {
  const params = {
    limit: searchForm.value.limit
  }

  // 添加时间范围参数
  if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
    params.start_time = Math.floor(searchForm.value.dateRange[0] / 1000)
    params.end_time = Math.floor(searchForm.value.dateRange[1] / 1000)
  }

  // 添加事件类型参数
  if (searchForm.value.eventTypes.length > 0) {
    params.event_types = searchForm.value.eventTypes.join(',')
  }

  await fetchEvents(params)
}

// 获取事件标签类型
const getEventTagType = (eventType) => {
  const typeMap = {
    'degradation_triggered': 'warning',
    'degradation_recovered': 'success',
    'level_changed': 'primary',
    'manual_trigger': 'warning',
    'manual_recovery': 'success',
    'threshold_exceeded': 'danger',
    'threshold_recovered': 'success'
  }
  return typeMap[eventType] || 'info'
}

// 显示事件详情
const showEventDetail = (event) => {
  selectedEvent.value = event
  detailDialogVisible.value = true
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 组件挂载时加载数据
onMounted(async () => {
  await searchHistory()
})
</script>

<style scoped>
.degradation-history {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
}

.history-table {
  margin-bottom: 20px;
}

.metadata-content {
  max-height: 100px;
  overflow-y: auto;
}

.metadata-item {
  display: block;
  font-size: 12px;
  margin-bottom: 2px;
}

.metadata-key {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.metadata-value {
  margin-left: 4px;
  color: var(--el-text-color-primary);
}

.text-placeholder {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.event-detail {
  padding: 10px 0;
}

.metadata-section {
  margin-top: 20px;
}

.metadata-section h4 {
  margin: 0 0 10px 0;
  color: var(--el-text-color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form .el-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    width: 100%;
    margin-bottom: 10px;
  }

  .search-form .el-date-picker,
  .search-form .el-select,
  .search-form .el-input-number {
    width: 100% !important;
  }
}
</style>
