"""
监控API接口
提供监控数据查询、系统状态检查和运维管理的REST API接口
"""

from typing import Any

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from core.logging.logging_system import log as logger
from core.metrics_collector import MetricCategory
from models.api import ApiResponse
from services.alert_manager import alert_manager
from services.monitoring_service import monitoring_service


# 响应模型
class HealthStatusResponse(BaseModel):
    """健康状态响应"""

    overall_status: str
    cpu_status: str
    memory_status: str
    database_status: str
    registration_status: str
    degradation_status: str
    active_alerts_count: int
    last_updated: float


class DashboardResponse(BaseModel):
    """仪表板响应"""

    system_health: HealthStatusResponse
    key_metrics: dict[str, Any]
    recent_alerts: list[dict[str, Any]]
    performance_trends: dict[str, list[float]]
    service_status: dict[str, str]


class MetricsSummaryResponse(BaseModel):
    """指标摘要响应"""

    timestamp: float
    total_metrics: int
    categories: dict[str, Any]


class AlertStatisticsResponse(BaseModel):
    """告警统计响应"""

    active_alerts: int
    total_alerts: int
    severity_distribution: dict[str, int]
    rule_distribution: dict[str, int]
    rules_count: int
    channels_count: int


# 创建路由器
router = APIRouter(prefix="/api/monitoring", tags=["监控"])


@router.get("/health", response_model=ApiResponse[HealthStatusResponse])
async def get_system_health():
    """
    获取系统健康状态

    返回系统各组件的健康状态和整体评估
    """
    try:
        health_status = await monitoring_service.get_system_health()

        response_data = HealthStatusResponse(
            overall_status=health_status.overall_status,
            cpu_status=health_status.cpu_status,
            memory_status=health_status.memory_status,
            database_status=health_status.database_status,
            registration_status=health_status.registration_status,
            degradation_status=health_status.degradation_status,
            active_alerts_count=health_status.active_alerts_count,
            last_updated=health_status.last_updated,
        )

        return ApiResponse(success=True, data=response_data, message="系统健康状态获取成功")

    except Exception as e:
        logger.error(f"Failed to get system health: {e}")
        raise HTTPException(status_code=500, detail="获取系统健康状态失败")


@router.get("/dashboard", response_model=ApiResponse[DashboardResponse])
async def get_monitoring_dashboard():
    """
    获取监控仪表板数据

    返回完整的监控仪表板信息，包括系统状态、关键指标、告警信息等
    """
    try:
        dashboard_data = await monitoring_service.get_monitoring_dashboard()

        response_data = DashboardResponse(
            system_health=HealthStatusResponse(
                overall_status=dashboard_data.system_health.overall_status,
                cpu_status=dashboard_data.system_health.cpu_status,
                memory_status=dashboard_data.system_health.memory_status,
                database_status=dashboard_data.system_health.database_status,
                registration_status=dashboard_data.system_health.registration_status,
                degradation_status=dashboard_data.system_health.degradation_status,
                active_alerts_count=dashboard_data.system_health.active_alerts_count,
                last_updated=dashboard_data.system_health.last_updated,
            ),
            key_metrics=dashboard_data.key_metrics,
            recent_alerts=dashboard_data.recent_alerts,
            performance_trends=dashboard_data.performance_trends,
            service_status=dashboard_data.service_status,
        )

        return ApiResponse(success=True, data=response_data, message="监控仪表板数据获取成功")

    except Exception as e:
        logger.error(f"Failed to get monitoring dashboard: {e}")
        raise HTTPException(status_code=500, detail="获取监控仪表板数据失败")


@router.get("/metrics", response_model=ApiResponse[MetricsSummaryResponse])
async def get_metrics_summary(
    category: str | None = Query(None, description="指标分类 (system, business, performance, error, custom)"),
):
    """
    获取指标摘要

    返回系统指标的摘要信息，可按分类过滤
    """
    try:
        # 验证分类参数
        metric_category = None
        if category:
            try:
                metric_category = MetricCategory(category.lower())
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的指标分类: {category}")

        metrics_summary = monitoring_service.get_metrics_summary(metric_category)

        response_data = MetricsSummaryResponse(
            timestamp=metrics_summary.get("timestamp", 0),
            total_metrics=metrics_summary.get("total_metrics", 0),
            categories=metrics_summary.get("categories", {}),
        )

        return ApiResponse(success=True, data=response_data, message="指标摘要获取成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get metrics summary: {e}")
        raise HTTPException(status_code=500, detail="获取指标摘要失败")


@router.get("/alerts", response_model=ApiResponse[list[dict[str, Any]]])
async def get_active_alerts():
    """
    获取活跃告警列表

    返回当前所有活跃的告警信息
    """
    try:
        active_alerts = alert_manager.get_active_alerts()

        alerts_data = []
        for alert in active_alerts:
            alerts_data.append(
                {
                    "alert_id": alert.alert_id,
                    "rule_name": alert.rule_name,
                    "metric_name": alert.metric_name,
                    "current_value": alert.current_value,
                    "threshold": alert.threshold,
                    "severity": alert.severity.value,
                    "status": alert.status.value,
                    "message": alert.message,
                    "created_at": alert.created_at,
                    "updated_at": alert.updated_at,
                    "labels": alert.labels,
                }
            )

        return ApiResponse(success=True, data=alerts_data, message="活跃告警列表获取成功")

    except Exception as e:
        logger.error(f"Failed to get active alerts: {e}")
        raise HTTPException(status_code=500, detail="获取活跃告警列表失败")


@router.get("/alerts/history", response_model=ApiResponse[list[dict[str, Any]]])
async def get_alert_history(limit: int = Query(100, ge=1, le=1000, description="返回的告警数量限制")):
    """
    获取告警历史

    返回历史告警记录
    """
    try:
        alert_history = alert_manager.get_alert_history(limit=limit)

        history_data = []
        for alert in alert_history:
            history_data.append(
                {
                    "alert_id": alert.alert_id,
                    "rule_name": alert.rule_name,
                    "metric_name": alert.metric_name,
                    "current_value": alert.current_value,
                    "threshold": alert.threshold,
                    "severity": alert.severity.value,
                    "status": alert.status.value,
                    "message": alert.message,
                    "created_at": alert.created_at,
                    "updated_at": alert.updated_at,
                    "resolved_at": alert.resolved_at,
                    "acknowledged_at": alert.acknowledged_at,
                    "labels": alert.labels,
                }
            )

        return ApiResponse(success=True, data=history_data, message="告警历史获取成功")

    except Exception as e:
        logger.error(f"Failed to get alert history: {e}")
        raise HTTPException(status_code=500, detail="获取告警历史失败")


@router.get("/alerts/statistics", response_model=ApiResponse[AlertStatisticsResponse])
async def get_alert_statistics():
    """
    获取告警统计信息

    返回告警的统计数据，包括数量分布、严重程度分布等
    """
    try:
        alert_stats = monitoring_service.get_alert_statistics()

        response_data = AlertStatisticsResponse(
            active_alerts=alert_stats["active_alerts"],
            total_alerts=alert_stats["total_alerts"],
            severity_distribution=alert_stats["severity_distribution"],
            rule_distribution=alert_stats["rule_distribution"],
            rules_count=alert_stats["rules_count"],
            channels_count=alert_stats["channels_count"],
        )

        return ApiResponse(success=True, data=response_data, message="告警统计信息获取成功")

    except Exception as e:
        logger.error(f"Failed to get alert statistics: {e}")
        raise HTTPException(status_code=500, detail="获取告警统计信息失败")


@router.post("/health/check", response_model=ApiResponse[HealthStatusResponse])
async def trigger_health_check():
    """
    手动触发健康检查

    立即执行一次系统健康检查并返回结果
    """
    try:
        health_status = await monitoring_service.trigger_manual_health_check()

        response_data = HealthStatusResponse(
            overall_status=health_status.overall_status,
            cpu_status=health_status.cpu_status,
            memory_status=health_status.memory_status,
            database_status=health_status.database_status,
            registration_status=health_status.registration_status,
            degradation_status=health_status.degradation_status,
            active_alerts_count=health_status.active_alerts_count,
            last_updated=health_status.last_updated,
        )

        return ApiResponse(success=True, data=response_data, message="健康检查执行成功")

    except Exception as e:
        logger.error(f"Failed to trigger health check: {e}")
        raise HTTPException(status_code=500, detail="健康检查执行失败")


@router.post("/metrics/collect", response_model=ApiResponse[dict[str, str]])
async def trigger_metrics_collection():
    """
    手动触发指标收集

    立即执行一次指标收集
    """
    try:
        await monitoring_service.collect_metrics_now()

        return ApiResponse(success=True, data={"status": "completed"}, message="指标收集执行成功")

    except Exception as e:
        logger.error(f"Failed to trigger metrics collection: {e}")
        raise HTTPException(status_code=500, detail="指标收集执行失败")


@router.get("/status", response_model=ApiResponse[dict[str, Any]])
async def get_monitoring_service_status():
    """
    获取监控服务状态

    返回监控服务本身的运行状态信息
    """
    try:
        status_info = {
            "monitoring_service": {
                "running": monitoring_service.is_running,
                "collection_task_active": monitoring_service.collection_task is not None
                and not monitoring_service.collection_task.done(),
                "health_check_task_active": monitoring_service.health_check_task is not None
                and not monitoring_service.health_check_task.done(),
            },
            "alert_manager": {
                "running": alert_manager.is_running,
                "rules_count": len(alert_manager.rules),
                "active_alerts_count": len(alert_manager.active_alerts),
                "notification_channels_count": len(alert_manager.notification_channels),
            },
            "metrics_collector": {
                "total_metrics": len(monitoring_service.get_metrics_summary().get("categories", {})),
                "history_size": 1000,  # 默认历史大小
            },
        }

        return ApiResponse(success=True, data=status_info, message="监控服务状态获取成功")

    except Exception as e:
        logger.error(f"Failed to get monitoring service status: {e}")
        raise HTTPException(status_code=500, detail="获取监控服务状态失败")
