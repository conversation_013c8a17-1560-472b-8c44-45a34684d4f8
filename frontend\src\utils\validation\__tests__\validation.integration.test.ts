/**
 * 数据校验集成测试
 * 测试核心功能的基本工作流程
 */

import { describe, it, expect } from 'vitest'
import { ValidationRuleType } from '../validationTypes'

describe('数据校验集成测试', () => {
  describe('ValidationRuleType枚举', () => {
    it('应该包含所有必要的校验规则类型', () => {
      expect(ValidationRuleType.REQUIRED).toBe('required')
      expect(ValidationRuleType.MIN_LENGTH).toBe('min_length')
      expect(ValidationRuleType.MAX_LENGTH).toBe('max_length')
      expect(ValidationRuleType.EMAIL).toBe('email')
      expect(ValidationRuleType.INTEGER).toBe('integer')
    })
  })

  describe('校验规则基本逻辑', () => {
    it('应该能够创建校验规则对象', () => {
      const rule = {
        field_name: 'test_field',
        chinese_name: '测试字段',
        rule_type: ValidationRuleType.REQUIRED,
        rule_value: true,
        error_message: '测试字段不能为空',
        is_required: true,
        priority: 0
      }

      expect(rule.field_name).toBe('test_field')
      expect(rule.rule_type).toBe('required')
      expect(rule.is_required).toBe(true)
    })

    it('应该能够创建校验结果对象', () => {
      const result = {
        valid: true,
        errors: [],
        warnings: [],
        field_count: 1,
        validated_fields: ['test_field'],
        duration: 10
      }

      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.field_count).toBe(1)
    })
  })

  describe('错误处理', () => {
    it('应该能够创建校验错误对象', () => {
      const error = {
        field_name: 'test_field',
        chinese_name: '测试字段',
        error_code: 'REQUIRED_FIELD_MISSING',
        error_message: '测试字段不能为空',
        error_value: null,
        rule_type: 'required',
        suggestions: ['请填写测试字段'],
        severity: 'error' as any
      }

      expect(error.error_code).toBe('REQUIRED_FIELD_MISSING')
      expect(error.suggestions).toContain('请填写测试字段')
    })
  })

  describe('类型安全性', () => {
    it('应该正确处理不同的数据类型', () => {
      // 字符串类型
      const stringValue = 'test string'
      expect(typeof stringValue).toBe('string')

      // 数字类型
      const numberValue = 123
      expect(typeof numberValue).toBe('number')

      // 布尔类型
      const booleanValue = true
      expect(typeof booleanValue).toBe('boolean')

      // 数组类型
      const arrayValue = ['item1', 'item2']
      expect(Array.isArray(arrayValue)).toBe(true)
    })
  })
})
