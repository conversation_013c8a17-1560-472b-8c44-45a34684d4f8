"""
降级机制启动模块
负责在应用启动时初始化降级管理器和同步服务
"""

import asyncio

from config.degradation_config import start_config_watching
from config.settings import get_settings
from core.degradation_manager import get_degradation_manager, start_degradation_manager
from core.logging.logging_system import log as logger
from services.degradation_sync import get_degradation_sync_service, start_degradation_sync


class DegradationStartupManager:
    """降级机制启动管理器"""

    def __init__(self):
        self.settings = get_settings()
        self._degradation_manager = None
        self._sync_service = None
        self._is_initialized = False

    async def initialize(self):
        """初始化降级机制"""
        if self._is_initialized:
            logger.warning("Degradation mechanism already initialized")
            return

        try:
            logger.info("Initializing degradation mechanism...")

            # 检查降级功能是否启用
            if not getattr(self.settings, 'DEGRADATION_ENABLED', True):
                logger.info("Degradation mechanism is disabled in configuration")
                return

            # 启动配置监听
            await self._start_config_watching()

            # 初始化降级管理器
            await self._initialize_degradation_manager()

            # 根据模式初始化同步服务
            if self.settings.MODE == "slave":
                await self._initialize_sync_service()
            elif self.settings.MODE == "master":
                logger.info("Running in master mode, sync service not needed")
            else:
                logger.warning(f"Unknown mode: {self.settings.MODE}, degradation sync disabled")

            self._is_initialized = True
            logger.info("Degradation mechanism initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize degradation mechanism: {e}", exc_info=True)
            raise

    async def _start_config_watching(self):
        """启动配置监听"""
        try:
            start_config_watching()
            logger.info("Degradation configuration watching started")
        except Exception as e:
            logger.warning(f"Failed to start config watching: {e}")

    async def _initialize_degradation_manager(self):
        """初始化降级管理器"""
        try:
            # 获取降级管理器实例
            self._degradation_manager = get_degradation_manager()

            # 启动降级管理器
            await start_degradation_manager()

            logger.info("Degradation manager initialized and started")

        except Exception as e:
            logger.error(f"Failed to initialize degradation manager: {e}", exc_info=True)
            raise

    async def _initialize_sync_service(self):
        """初始化同步服务（仅slave模式）"""
        try:
            # 检查必要的配置
            if not getattr(self.settings, 'MASTER_API_ENDPOINT', None):
                logger.warning("MASTER_API_ENDPOINT not configured, sync service disabled")
                return

            if not getattr(self.settings, 'SLAVE_API_KEY', None):
                logger.warning("SLAVE_API_KEY not configured, sync service disabled")
                return

            # 获取同步服务实例
            self._sync_service = get_degradation_sync_service()

            # 启动同步服务
            await start_degradation_sync()

            logger.info("Degradation sync service initialized and started")

        except Exception as e:
            logger.error(f"Failed to initialize sync service: {e}", exc_info=True)
            raise

    async def shutdown(self):
        """关闭降级机制"""
        if not self._is_initialized:
            return

        try:
            logger.info("Shutting down degradation mechanism...")

            # 停止同步服务
            if self._sync_service:
                await self._sync_service.stop()
                logger.info("Degradation sync service stopped")

            # 停止降级管理器
            if self._degradation_manager:
                await self._degradation_manager.stop()
                logger.info("Degradation manager stopped")

            self._is_initialized = False
            logger.info("Degradation mechanism shutdown completed")

        except Exception as e:
            logger.error(f"Error during degradation mechanism shutdown: {e}", exc_info=True)

    def get_status(self) -> dict:
        """获取启动状态"""
        status = {
            "initialized": self._is_initialized,
            "mode": self.settings.MODE,
            "degradation_enabled": getattr(self.settings, 'DEGRADATION_ENABLED', True),
            "degradation_manager": None,
            "sync_service": None
        }

        # 获取降级管理器状态
        if self._degradation_manager:
            try:
                status["degradation_manager"] = {
                    "enabled": self._degradation_manager.is_enabled(),
                    "running": self._degradation_manager.is_running(),
                    "current_level": self._degradation_manager.get_current_status().get("current_level"),
                    "registered_components": len(self._degradation_manager.get_registered_components())
                }
            except Exception as e:
                status["degradation_manager"] = {"error": str(e)}

        # 获取同步服务状态
        if self._sync_service:
            try:
                status["sync_service"] = self._sync_service.get_sync_status()
            except Exception as e:
                status["sync_service"] = {"error": str(e)}

        return status

    async def health_check(self) -> dict:
        """健康检查"""
        health = {
            "status": "healthy",
            "checks": {},
            "timestamp": asyncio.get_event_loop().time()
        }

        # 检查降级管理器
        if self._degradation_manager:
            try:
                manager_status = self._degradation_manager.get_current_status()
                health["checks"]["degradation_manager"] = {
                    "status": "healthy" if self._degradation_manager.is_running() else "stopped",
                    "enabled": self._degradation_manager.is_enabled(),
                    "current_level": manager_status.get("current_level"),
                    "last_change": manager_status.get("last_change_time")
                }
            except Exception as e:
                health["checks"]["degradation_manager"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                health["status"] = "degraded"

        # 检查同步服务（仅slave模式）
        if self._sync_service and self.settings.MODE == "slave":
            try:
                sync_status = self._sync_service.get_sync_status()
                health["checks"]["sync_service"] = {
                    "status": "healthy" if sync_status["is_running"] else "stopped",
                    "last_sync": sync_status["last_successful_sync"],
                    "network_partition": sync_status["network_partition"],
                    "sync_failures": sync_status["sync_failures"],
                    "is_stale": sync_status["is_stale"]
                }

                # 如果同步服务有问题，标记为降级
                if sync_status["network_partition"] or sync_status["is_stale"]:
                    health["status"] = "degraded"

            except Exception as e:
                health["checks"]["sync_service"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                health["status"] = "degraded"

        return health


# 全局启动管理器实例
_startup_manager: DegradationStartupManager | None = None


def get_degradation_startup_manager() -> DegradationStartupManager:
    """获取全局降级启动管理器实例"""
    global _startup_manager

    if _startup_manager is None:
        _startup_manager = DegradationStartupManager()

    return _startup_manager


async def initialize_degradation_mechanism():
    """初始化降级机制的便捷函数"""
    manager = get_degradation_startup_manager()
    await manager.initialize()


async def shutdown_degradation_mechanism():
    """关闭降级机制的便捷函数"""
    global _startup_manager

    if _startup_manager:
        await _startup_manager.shutdown()


def get_degradation_status() -> dict:
    """获取降级机制状态的便捷函数"""
    manager = get_degradation_startup_manager()
    return manager.get_status()


async def degradation_health_check() -> dict:
    """降级机制健康检查的便捷函数"""
    manager = get_degradation_startup_manager()
    return await manager.health_check()
