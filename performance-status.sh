#!/bin/bash
echo "=== 从节点性能状态 ==="
python -c """
import psutil
import json

# CPU使用率
cpu_percent = psutil.cpu_percent(interval=1)

# 内存使用情况
memory = psutil.virtual_memory()

# 进程信息
process_count = len(psutil.pids())

status = {
    "cpu_percent": cpu_percent,
    "memory_percent": memory.percent,
    "memory_available_gb": round(memory.available / (1024**3), 2),
    "process_count": process_count
}

print(json.dumps(status, indent=2))
"""