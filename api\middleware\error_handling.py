"""
Error handling middleware for consistent error responses.
This module is now a compatibility layer for the unified error handling system.
"""

import warnings

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from core.logging.logging_system import log as logger
from core.middleware.unified_error_handler import UnifiedErrorHandlingMiddleware
from models import ApiResponse


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    Handle request validation errors.

    Converts Pydantic validation errors into user-friendly error messages,
    supporting detailed error location for nested structures.
    """
    error_details = []

    try:
        body = await request.json()
    except Exception:
        body = None

    for error in exc.errors():
        loc = error.get("loc", [])
        msg = error.get("msg", "Unknown error")
        typ = error.get("type", "")

        # Skip the first element (usually 'body')
        if len(loc) > 1:
            # Build field path, e.g.: patient_data.basic_info.name
            field_path = ".".join(str(loc_part) for loc_part in loc[1:])

            # Add additional information based on error type
            if "missing" in typ:
                error_details.append(f"Missing required field '{field_path}'")
            elif "type" in typ:
                error_details.append(f"Field '{field_path}' type error: {msg}")
            elif "value_error" in typ:
                error_details.append(f"Field '{field_path}' value error: {msg}")
            else:
                error_details.append(f"Field '{field_path}': {msg}")
        else:
            error_details.append(f"Request error: {msg}")

    # Sort error details by field path for better grouping
    error_details.sort()

    error_message = "Request parameter validation failed: " + "; ".join(error_details)
    logger.warning(f"Request parameter validation failed: {error_details}, Request body: {body}")

    # Build error response structure
    error_response = {
        "code": status.HTTP_200_OK,
        "success": False,
        "message": error_message,
        "data": None,
    }

    return JSONResponse(
        status_code=status.HTTP_200_OK,  # Unified 200 status code
        content=error_response,
    )


async def http_exception_handler(_request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=status.HTTP_200_OK,  # Unified 200 status code
        content=ApiResponse(code=exc.status_code, success=False, message=exc.detail, data=None).model_dump(),
    )


async def general_exception_handler(_request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_200_OK,  # Unified 200 status code
        content=ApiResponse(
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            success=False,
            message="Internal server error, please contact administrator",
            data=None,
        ).model_dump(),
    )


class ErrorHandlingMiddleware:
    """
    Middleware class for error handling (Compatibility Layer).

    This class now serves as a compatibility layer for the unified error handling system.
    It delegates to the new UnifiedErrorHandlingMiddleware for consistency.
    """

    @staticmethod
    def get_handlers():
        """
        Get all exception handlers.

        Note: This method now returns the unified error handlers for consistency.
        The old handlers are deprecated and will be removed in a future version.
        """
        warnings.warn(
            "ErrorHandlingMiddleware.get_handlers() is deprecated. Use UnifiedErrorHandlingMiddleware.get_handlers() instead.",  # noqa: E501
            DeprecationWarning,
            stacklevel=2,
        )

        # Return the unified error handlers for consistency
        return UnifiedErrorHandlingMiddleware.get_handlers()

    @staticmethod
    def register_handlers(app):
        """
        Register error handlers to FastAPI application.

        This method delegates to the unified error handling system.
        """
        warnings.warn(
            "ErrorHandlingMiddleware.register_handlers() is deprecated. Use UnifiedErrorHandlingMiddleware.register_handlers() instead.",  # noqa: E501
            DeprecationWarning,
            stacklevel=2,
        )

        return UnifiedErrorHandlingMiddleware.register_handlers(app)
