from fastapi import HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from core.logging.logging_system import log as logger
from models import ApiResponse


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    处理请求验证错误

    将Pydantic验证错误转换为友好的错误消息，支持嵌套结构的详细错误定位
    """
    error_details = []
    body = await request.json()

    for error in exc.errors():
        loc = error.get("loc", [])
        msg = error.get("msg", "未知错误")
        typ = error.get("type", "")

        # 跳过第一个元素(通常是'body')
        if len(loc) > 1:
            # 构建字段路径，例如：patient_data.basic_info.name
            field_path = ".".join(str(loc_part) for loc_part in loc[1:])

            # 根据错误类型添加额外信息
            if "missing" in typ:
                error_details.append(f"缺少必填字段 '{field_path}'")
            elif "type" in typ:
                error_details.append(f"字段 '{field_path}' 类型错误: {msg}")
            elif "value_error" in typ:
                error_details.append(f"字段 '{field_path}' 值错误: {msg}")
            else:
                error_details.append(f"字段 '{field_path}': {msg}")
        else:
            error_details.append(f"请求错误: {msg}")

    # 如果是多个错误，按照字段路径排序，使相关错误分组显示
    error_details.sort()

    error_message = "请求参数验证失败: " + "; ".join(error_details)
    logger.warning(f"请求参数验证失败: {error_details}, \n请求体: {body}")

    # 构建错误响应结构体
    error_response = {
        "code": status.HTTP_200_OK,
        "success": False,
        "message": error_message,
        "data": None,
    }

    # 添加开发调试信息，包含原始错误详情
    # 此处无法直接访问 app.debug，因此暂时移除或寻找其他方式
    # if app.debug:
    #     error_response["debug_info"] = {
    #         "raw_errors": exc.errors(),
    #         "request_body": await request.json() if await request.body() else None,
    #     }

    return JSONResponse(
        status_code=status.HTTP_200_OK,  # 统一返回200状态码
        content=error_response,
    )


async def http_exception_handler(request: Request, exc: HTTPException):
    """处理HTTP异常"""
    return JSONResponse(
        status_code=status.HTTP_200_OK,  # 统一返回200状态码
        content=ApiResponse(
            code=exc.status_code, success=False, message=exc.detail, data=None
        ).model_dump(),
    )


async def general_exception_handler(request: Request, exc: Exception):
    """处理通用异常"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_200_OK,  # 统一返回200状态码
        content=ApiResponse(
            code=status.HTTP_200_OK,
            success=False,
            message="服务器内部错误，请联系管理员",
            data=None,
        ).model_dump(),
    )
