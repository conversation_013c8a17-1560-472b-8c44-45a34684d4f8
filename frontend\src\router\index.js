import { createRouter, createWebHistory } from 'vue-router'
import { useAppStore } from '../stores/app'

// 权限检查函数
const checkPermission = (to, from, next) => {
    const appStore = useAppStore()
    const userRoles = appStore.userRoles || ['user'] // 默认用户角色
    const requiredRoles = to.meta.roles || []

    if (requiredRoles.length === 0 || requiredRoles.some(role => userRoles.includes(role))) {
        next()
    } else {
        console.warn(`访问被拒绝: 需要角色 ${requiredRoles.join(', ')}, 当前角色 ${userRoles.join(', ')}`)
        next('/unauthorized')
    }
}

// 路由配置
const routes = [
    // 主要功能路由
    {
        path: '/',
        name: 'Dashboard',
        component: () => import('../views/RuleDashboard.vue'),
        meta: {
            title: '模板状态仪表盘',
            breadcrumb: [{ text: '首页', to: '/' }],
            cache: true,
            roles: ['admin', 'user']
        }
    },

    // 调试页面
    {
        path: '/dashboard-debug',
        name: 'DashboardDebug',
        component: () => import('../views/RuleDashboard_debug.vue'),
        meta: {
            title: '模板状态仪表盘（调试版）',
            breadcrumb: [
                { text: '首页', to: '/' },
                { text: '调试页面' }
            ],
            roles: ['admin']
        }
    },

    // 规则管理模块（层级结构）
    {
        path: '/rules',
        name: 'Rules',
        redirect: '/rules/management',
        meta: {
            title: '规则管理',
            breadcrumb: [
                { text: '首页', to: '/' },
                { text: '规则管理' }
            ],
            roles: ['admin', 'user']
        },
        children: [
            {
                path: 'management',
                name: 'RuleManagement',
                component: () => import('../views/RuleManagement.vue'),
                meta: {
                    title: '规则配置管理',
                    breadcrumb: [
                        { text: '首页', to: '/' },
                        { text: '规则管理', to: '/rules' },
                        { text: '规则配置' }
                    ],
                    cache: true,
                    roles: ['admin', 'user']
                }
            },
            {
                path: ':ruleKey/details',
                name: 'RuleDetails',
                component: () => import('../views/RuleDetailsCRUD.vue'),
                props: true,
                meta: {
                    title: '规则明细管理',
                    breadcrumb: [
                        { text: '首页', to: '/' },
                        { text: '规则管理', to: '/rules' },
                        { text: '规则配置', to: '/rules/management' },
                        { text: '规则明细' }
                    ],
                    roles: ['admin', 'user']
                }
            },
            {
                path: ':ruleKey/upload',
                name: 'DataUpload',
                component: () => import('../views/DataUploader.vue'),
                props: true,
                meta: {
                    title: '数据上传与预览',
                    breadcrumb: [
                        { text: '首页', to: '/' },
                        { text: '规则管理', to: '/rules' },
                        { text: '规则配置', to: '/rules/management' },
                        { text: '数据上传' }
                    ],
                    roles: ['admin', 'user']
                }
            }
        ]
    },

    // 兼容性路由（保持向后兼容）
    {
        path: '/upload/:ruleKey',
        redirect: to => `/rules/${to.params.ruleKey}/upload`
    },

    // 工具页面
    {
        path: '/test',
        name: 'TestConnection',
        component: () => import('../views/TestConnection.vue'),
        meta: {
            title: 'API连接测试',
            breadcrumb: [
                { text: '首页', to: '/' },
                { text: 'API连接测试' }
            ],
            roles: ['admin']
        }
    },

    // 监控页面
    {
        path: '/monitoring',
        name: 'Monitoring',
        meta: {
            title: '系统监控',
            breadcrumb: [
                { text: '首页', to: '/' },
                { text: '系统监控' }
            ],
            roles: ['admin']
        },
        children: [
            {
                path: 'degradation',
                name: 'DegradationMonitor',
                component: () => import('../views/monitoring/DegradationMonitor.vue'),
                meta: {
                    title: '降级状态监控',
                    breadcrumb: [
                        { text: '首页', to: '/' },
                        { text: '系统监控', to: '/monitoring' },
                        { text: '降级监控' }
                    ],
                    roles: ['admin']
                }
            }
        ]
    },

    // 兼容性路由
    {
        path: '/degradation',
        redirect: '/monitoring/degradation'
    },

    // 规则模板详情
    {
        path: '/rule-template-detail/:ruleKey',
        name: 'RuleTemplateDetail',
        component: () => import('../views/RuleTemplateDetail.vue'),
        props: true,
        meta: {
            title: '规则模板详情',
            breadcrumb: [
                { text: '首页', to: '/' },
                { text: '规则管理', to: '/rules' },
                { text: '模板详情' }
            ],
            roles: ['admin', 'user']
        }
    },

    // 演示页面
    {
        path: '/demo',
        name: 'Demo',
        meta: {
            title: '演示页面',
            breadcrumb: [
                { text: '首页', to: '/' },
                { text: '演示页面' }
            ],
            roles: ['admin']
        },
        children: [
            {
                path: 'rule-details',
                name: 'RuleDetailsListDemo',
                component: () => import('../views/RuleDetailsListDemo.vue'),
                meta: {
                    title: '规则明细列表演示',
                    breadcrumb: [
                        { text: '首页', to: '/' },
                        { text: '演示页面', to: '/demo' },
                        { text: '规则明细演示' }
                    ],
                    roles: ['admin']
                }
            }
        ]
    },

    // 错误页面
    {
        path: '/unauthorized',
        name: 'Unauthorized',
        component: () => import('../views/error/Unauthorized.vue'),
        meta: {
            title: '访问被拒绝',
            breadcrumb: [
                { text: '首页', to: '/' },
                { text: '访问被拒绝' }
            ]
        }
    },

    // 404页面
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('../views/error/NotFound.vue'),
        meta: {
            title: '页面未找到',
            breadcrumb: [
                { text: '首页', to: '/' },
                { text: '页面未找到' }
            ]
        }
    }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
    // 设置页面标题
    document.title = `${to.meta.title} - 智能规则管理系统` || '智能规则管理系统'

    // 权限检查
    checkPermission(to, from, next)
})

// 路由后置守卫 - 用于页面加载完成后的处理
router.afterEach((to, from) => {
    // 记录路由跳转日志
    console.log(`路由跳转: ${from.path} -> ${to.path}`)

    // 可以在这里添加页面访问统计等功能
    if (to.meta.cache === false) {
        // 清除页面缓存
        console.log(`清除页面缓存: ${to.name}`)
    }
})

// 路由错误处理
router.onError((error) => {
    console.error('路由错误:', error)
    // 可以在这里添加错误上报逻辑
})

export default router