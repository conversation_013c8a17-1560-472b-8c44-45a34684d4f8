# 增强API使用指南

## 概述

增强API系统是规则详情表重构项目的核心组件，提供了统一的字段映射、智能缓存、错误处理和性能监控功能。本文档详细介绍如何使用增强API系统。

## 核心组件

### 1. 字段映射引擎 (FieldMappingEngine)

字段映射引擎负责前后端字段名的自动转换和数据验证。

#### 基本使用

```typescript
import { fieldMappingEngine } from '@/utils/fieldMappingEngine'

// 初始化（通常在应用启动时）
await fieldMappingEngine.initialize()

// 转换请求数据（前端 → 后端）
const requestData = {
  rule_name: '测试规则',
  level1: '错误类型'
}
const transformedRequest = fieldMappingEngine.transformRequestData(requestData, 'test-rule')

// 转换响应数据（后端 → 前端）
const responseData = { /* 后端返回的数据 */ }
const transformedResponse = fieldMappingEngine.transformResponseData(responseData, 'test-rule')

// 数据验证
const validationResult = fieldMappingEngine.validateFields(requestData, 'test-rule')
if (!validationResult.valid) {
  console.error('验证失败:', validationResult.errors)
}
```

#### 字段信息获取

```typescript
// 获取字段中文名
const chineseName = fieldMappingEngine.getFieldChineseName('rule_name') // "规则名称"

// 获取字段数据类型
const dataType = fieldMappingEngine.getFieldDataType('age_threshold') // "integer"

// 检查字段是否必填
const isRequired = fieldMappingEngine.isFieldRequired('rule_name') // true

// 获取验证规则
const rules = fieldMappingEngine.getFieldValidationRules('rule_name') // ["required", "max_length:500"]
```

### 2. 增强API (EnhancedRuleDetailsApi)

增强API提供了集成字段映射、缓存和错误处理的API调用接口。

#### 基本使用

```typescript
import { enhancedRuleDetailsApi } from '@/api/enhancedRuleDetails'

// 初始化
await enhancedRuleDetailsApi.initialize()

// 获取规则明细列表
const response = await enhancedRuleDetailsApi.getRuleDetailsList('test-rule', {
  page: 1,
  page_size: 20,
  keyword: '搜索关键词'
})

// 创建规则明细
const createData = {
  rule_name: '新规则',
  level1: '错误类型'
}
const createResponse = await enhancedRuleDetailsApi.createRuleDetail('test-rule', createData)

// 批量操作
const batchRequest = {
  operations: [
    { action: 'CREATE', data: { rule_name: '规则1', level1: '类型1' } },
    { action: 'UPDATE', id: 1, data: { rule_name: '更新规则' } }
  ]
}
const batchResponse = await enhancedRuleDetailsApi.batchOperateRuleDetails('test-rule', batchRequest)
```

### 3. Vue组合式函数

#### useRuleDetails

```typescript
import { useRuleDetails } from '@/composables/useEnhancedApi'

export default {
  setup() {
    const {
      loading,
      error,
      ruleDetails,
      pagination,
      fetchRuleDetailsList,
      createRuleDetail,
      updateRuleDetail,
      deleteRuleDetail,
      searchRuleDetails,
      changePage
    } = useRuleDetails('test-rule')

    // 获取列表
    onMounted(() => {
      fetchRuleDetailsList()
    })

    // 搜索
    const handleSearch = (keyword: string) => {
      searchRuleDetails({ keyword })
    }

    // 分页
    const handlePageChange = (page: number) => {
      changePage(page)
    }

    return {
      loading,
      error,
      ruleDetails,
      pagination,
      handleSearch,
      handlePageChange
    }
  }
}
```

#### useApiCache

```typescript
import { useApiCache } from '@/composables/useEnhancedApi'

export default {
  setup() {
    const { getCacheStats, clearAllCache } = useApiCache()

    // 获取缓存统计
    const stats = getCacheStats()
    console.log('缓存命中率:', stats.hitRate)

    // 清除所有缓存
    const handleClearCache = () => {
      clearAllCache()
    }

    return {
      stats,
      handleClearCache
    }
  }
}
```

### 4. API缓存管理

#### 缓存配置

```typescript
import { ApiCache } from '@/utils/apiCache'

// 创建自定义缓存实例
const customCache = new ApiCache({
  ttl: 10 * 60 * 1000,      // 10分钟
  maxSize: 200,             // 最大200个条目
  enablePersistence: true,  // 启用持久化
  versionBased: true        // 基于版本缓存
})

// 使用缓存
customCache.set('key', data, version)
const cachedData = customCache.get('key')

// 缓存失效
customCache.invalidate('rule_details_*')
```

#### 缓存策略

- **列表数据**: 2分钟缓存
- **详情数据**: 5分钟缓存
- **统计数据**: 10分钟缓存
- **版本失效**: 数据更新时自动失效相关缓存

### 5. 错误处理

#### 错误分类

增强API自动将错误分为以下类别：

- **NETWORK**: 网络错误
- **VALIDATION**: 验证错误
- **BUSINESS**: 业务错误
- **PERMISSION**: 权限错误
- **SYSTEM**: 系统错误

#### 自定义错误处理

```typescript
import { enhancedErrorHandler } from '@/utils/enhancedErrorHandler'

// 获取错误统计
const errorStats = enhancedErrorHandler.getErrorStats()

// 获取错误日志
const errorLog = enhancedErrorHandler.getErrorLog(10) // 最近10条错误

// 清除错误日志
enhancedErrorHandler.clearErrorLog()
```

### 6. 性能监控

#### 性能指标

```typescript
import { enhancedRuleDetailsApi } from '@/api/enhancedRuleDetails'

// 获取性能指标
const metrics = enhancedRuleDetailsApi.getPerformanceMetrics()
console.log('平均响应时间:', metrics.averageResponseTime)
console.log('缓存命中率:', metrics.cacheHitRate)
console.log('错误率:', metrics.errorRate)

// 健康检查
const health = enhancedRuleDetailsApi.checkPerformanceHealth()
if (health.status === 'warning') {
  console.warn('性能警告:', health.issues)
}

// 导出性能报告
const report = enhancedRuleDetailsApi.exportPerformanceReport()
console.log(report)
```

## 配置管理

### API配置

```typescript
import { API_CONFIG, createApiConfig } from '@/api/apiConfig'

// 使用预定义配置
const listConfig = API_CONFIG_TEMPLATES.LIST('/v1/rules/details/test-rule', 'test-rule')

// 创建自定义配置
const customConfig = createApiConfig()
  .url('/v1/rules/details/test-rule')
  .method('GET')
  .enableCache('custom_key', 5 * 60 * 1000)
  .enableFieldMapping('test-rule')
  .enableValidation()
  .build()
```

### 环境配置

```typescript
import { getCurrentEnvironmentConfig } from '@/api/apiConfig'

const envConfig = getCurrentEnvironmentConfig()
if (envConfig.enableDebug) {
  console.log('调试模式已启用')
}
```

## 最佳实践

### 1. 初始化顺序

```typescript
// 在应用启动时按顺序初始化
async function initializeApp() {
  // 1. 初始化字段映射引擎
  await fieldMappingEngine.initialize()
  
  // 2. 初始化增强API
  await enhancedRuleDetailsApi.initialize()
  
  // 3. 其他初始化工作
}
```

### 2. 错误处理

```typescript
// 使用try-catch处理API调用
try {
  const response = await enhancedRuleDetailsApi.getRuleDetailsList('test-rule')
  // 处理成功响应
} catch (error) {
  // 错误已经被增强错误处理器处理
  // 这里只需要处理业务逻辑
  console.error('业务处理失败:', error.userMessage)
}
```

### 3. 性能优化

```typescript
// 使用缓存优化性能
const response = await enhancedRuleDetailsApi.getRuleDetailsList('test-rule', {
  page: 1,
  page_size: 20
})

// 第二次调用会从缓存获取
const cachedResponse = await enhancedRuleDetailsApi.getRuleDetailsList('test-rule', {
  page: 1,
  page_size: 20
})
console.log('来自缓存:', cachedResponse.fromCache)
```

### 4. 类型安全

```typescript
import type { RuleDetail, CreateRuleDetailData } from '@/types/ruleDetails'
import type { EnhancedApiResponse } from '@/types/apiEnhanced'

// 使用类型约束确保类型安全
const createData: CreateRuleDetailData = {
  rule_name: '新规则',
  level1: '错误类型'
}

const response: EnhancedApiResponse<RuleDetail> = await enhancedRuleDetailsApi.createRuleDetail('test-rule', createData)
```

## 故障排除

### 常见问题

1. **字段映射失败**
   - 检查field_mapping.json文件是否存在
   - 确认字段定义是否正确
   - 检查网络连接

2. **缓存问题**
   - 清除浏览器localStorage
   - 调用clearAllCache()方法
   - 检查缓存配置

3. **性能问题**
   - 查看性能监控报告
   - 检查慢查询日志
   - 优化API调用频率

### 调试工具

```typescript
// 开启调试模式
if (import.meta.env.DEV) {
  // 查看字段映射缓存
  console.log('字段映射缓存:', fieldMappingEngine.getCacheStats())
  
  // 查看API缓存统计
  console.log('API缓存统计:', enhancedRuleDetailsApi.getCacheStats())
  
  // 查看性能指标
  console.log('性能指标:', enhancedRuleDetailsApi.getPerformanceMetrics())
}
```

## 更新日志

### v1.0.0 (2025-07-27)
- 初始版本发布
- 实现字段映射引擎
- 实现智能缓存策略
- 实现增强错误处理
- 实现性能监控
- 提供Vue组合式函数
- 支持向后兼容
