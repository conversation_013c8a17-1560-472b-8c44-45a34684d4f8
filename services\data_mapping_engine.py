"""
数据映射和转换引擎
负责将Excel数据和BaseRule表数据映射为注册接口所需的格式
"""

import hashlib
import time
from datetime import datetime
from typing import Any

from core.logging.logging_system import log as logger
from models.database import RuleTemplate


class DataMappingError(Exception):
    """数据映射相关异常"""

    def __init__(self, message: str, field_name: str | None = None, details: dict | None = None):
        super().__init__(message)
        self.field_name = field_name
        self.details = details or {}


class DataMappingEngine:
    """
    数据映射和转换引擎

    负责将Excel数据和BaseRule表数据转换为注册接口所需的格式。
    复用前端validateAndTransformRow的逻辑模式，确保数据处理的一致性。
    """

    def __init__(self):
        """初始化数据映射引擎"""
        # 特殊字段映射配置
        self.special_fields = {
            "使用数量": "",
            "违规数量": "",
            "使用天数": "",
            "违规天数": "",
            "违规项目": "",
            "入参说明": "",
            "提示字段类型": "",
        }

        # 时间字段配置
        self.time_fields = {"生效开始时间", "生效结束时间"}

        # 必填字段配置
        self.required_fields = {"rule_name", "rule_id"}

        # 新增：结构化字段映射配置（Excel字段 -> RuleDetail模型字段）
        self.structured_field_mapping = {
            # 基础标识字段
            "rule_id": "rule_detail_id",
            "rule_name": "rule_name",
            # 错误分类字段
            "error_level_1": "error_level_1",
            "error_level_2": "error_level_2",
            "error_level_3": "error_level_3",
            "level1": "error_level_1",  # 兼容旧字段名
            "level2": "error_level_2",
            "level3": "error_level_3",
            "error_reason": "error_reason",
            "error_severity": "error_severity",
            "degree": "error_severity",  # 兼容旧字段名
            # 质控相关字段
            "quality_basis": "quality_basis",
            "reference": "quality_basis",  # 兼容旧字段名
            "location_desc": "location_desc",
            "detail_position": "location_desc",  # 兼容旧字段名
            "prompt_field_type": "prompt_field_type",
            "prompted_fields3": "prompt_field_type",  # 兼容旧字段名
            "prompt_field_code": "prompt_field_code",
            "prompted_fields1": "prompt_field_code",  # 兼容旧字段名
            "prompt_field_seq": "prompt_field_seq",
            "prompted_fields2": "prompt_field_seq",  # 兼容旧字段名
            # 业务分类字段
            "rule_category": "rule_category",
            "type": "rule_category",  # 兼容旧字段名
            "applicable_business": "applicable_business",
            "pos": "applicable_business",  # 兼容旧字段名
            "applicable_region": "applicable_region",
            "applicableArea": "applicable_region",  # 兼容旧字段名
            "default_selected": "default_selected",
            "default_use": "default_selected",  # 兼容旧字段名
            "involved_amount": "involved_amount",
            "error_fee": "involved_amount",  # 兼容旧字段名
            # 统计字段
            "usage_quantity": "usage_quantity",
            "used_count": "usage_quantity",  # 兼容旧字段名
            "violation_quantity": "violation_quantity",
            "illegal_count": "violation_quantity",  # 兼容旧字段名
            "usage_days": "usage_days",
            "used_day": "usage_days",  # 兼容旧字段名
            "violation_days": "violation_days",
            "illegal_day": "violation_days",  # 兼容旧字段名
            "violation_items": "violation_items",
            "illegal_item": "violation_items",  # 兼容旧字段名
            # 时间字段
            "effective_start_time": "effective_start_time",
            "start_date": "effective_start_time",  # 兼容旧字段名
            "effective_end_time": "effective_end_time",
            "end_date": "effective_end_time",  # 兼容旧字段名
            # 扩展字段
            "remark": "remark",
            "remarks": "remark",  # 兼容旧字段名
            "param_description": "remark",  # 入参说明映射到备注
            "in_illustration": "remark",  # 兼容旧字段名
        }

        # 新增：数据类型转换配置
        self.field_types = {
            "prompt_field_seq": int,
            "involved_amount": float,  # 使用float，后续转换为Decimal
            "default_selected": bool,
            "usage_quantity": int,
            "violation_quantity": int,
            "usage_days": int,
            "violation_days": int,
        }

        # 新增：布尔值转换映射
        self.boolean_mapping = {
            "是": True,
            "否": False,
            "true": True,
            "false": False,
            "True": True,
            "False": False,
            "1": True,
            "0": False,
            "yes": True,
            "no": False,
            "y": True,  # 小写
            "n": False,  # 小写
        }

        # 字段映射配置（英文字段名 -> 中文显示名）
        self.field_mapping = {
            # 基础字段映射
            "error_level_1": "一级错误类型",
            "error_level_2": "二级错误类型",
            "error_level_3": "三级错误类型",
            "error_reason": "错误原因",
            "error_severity": "错误程度",
            "quality_basis": "质控依据或参考资料",
            "location_desc": "具体位置描述",
            "prompt_field_type": "提示字段类型",
            "prompt_field_code": "提示字段编码",
            "prompt_field_seq": "提示字段序号",
            "rule_category": "规则类别",
            "applicable_business": "适用业务",
            "applicable_region": "适用地区",
            "default_selected": "默认选用",
            "involved_amount": "涉及金额",
            "remark": "备注信息",
            "param_description": "入参说明",
            "usage_quantity": "使用数量",
            "violation_quantity": "违规数量",
            "usage_days": "使用天数",
            "violation_days": "违规天数",
            "violation_items": "违规项目",
            "effective_start_time": "生效开始时间",
            "effective_end_time": "生效结束时间",
            # 补充缺失的字段映射（基于实际数据分析）
            "yb_codes": "医保代码",
            "drug_start_with": "搭配药品编码前缀",  # drug_deny_mono_use规则专用字段
            "level1": "一级错误类型",
            "level2": "二级错误类型",
            "level3": "三级错误类型",
            "degree": "错误程度",
            "reference": "质控依据或参考资料",
            "detail_position": "具体位置描述",
            "prompted_fields1": "提示字段编码",
            "prompted_fields2": "提示字段序号",
            "prompted_fields3": "提示字段类型",
            "type": "规则类别",
            "pos": "适用业务",
            "applicableArea": "适用地区",
            "default_use": "默认选用",
            "remarks": "备注信息",
            "start_date": "生效开始时间",
            "end_date": "生效结束时间",
            "模板名称": "模板名称",
            "模板内涵": "模板内涵",
        }

        # 时间戳有效范围（1900-2100年的毫秒时间戳）
        self.min_timestamp = -2208988800000  # 1900-01-01
        self.max_timestamp = 4102444800000  # 2100-01-01

        # 验证配置
        self._validate_configuration()

        logger.info("DataMappingEngine初始化完成")

    def _validate_configuration(self):
        """验证映射引擎配置的完整性和正确性"""
        try:
            # 验证必填字段配置
            if not self.required_fields:
                raise DataMappingError("必填字段配置不能为空")

            for field in self.required_fields:
                if not isinstance(field, str) or not field.strip():
                    raise DataMappingError(f"必填字段配置无效: {field}")

            # 验证时间字段配置
            if not isinstance(self.time_fields, set):
                raise DataMappingError("时间字段配置必须是集合类型")

            # 验证字段映射配置
            if not isinstance(self.field_mapping, dict):
                raise DataMappingError("字段映射配置必须是字典类型")

            for eng_field, chn_field in self.field_mapping.items():
                if not isinstance(eng_field, str) or not isinstance(chn_field, str):
                    raise DataMappingError(f"字段映射配置格式错误: {eng_field} -> {chn_field}")
                if not eng_field.strip() or not chn_field.strip():
                    raise DataMappingError(f"字段映射配置不能为空: {eng_field} -> {chn_field}")

            # 验证特殊字段配置
            if not isinstance(self.special_fields, dict):
                raise DataMappingError("特殊字段配置必须是字典类型")

            # 验证结构化字段映射配置
            if not isinstance(self.structured_field_mapping, dict):
                raise DataMappingError("结构化字段映射配置必须是字典类型")

            # 验证字段类型配置
            if not isinstance(self.field_types, dict):
                raise DataMappingError("字段类型配置必须是字典类型")

            # 验证布尔值映射配置
            if not isinstance(self.boolean_mapping, dict):
                raise DataMappingError("布尔值映射配置必须是字典类型")

            # 验证时间戳范围配置
            if not isinstance(self.min_timestamp, int | float) or not isinstance(self.max_timestamp, int | float):
                raise DataMappingError("时间戳范围配置必须是数字类型")

            if self.min_timestamp >= self.max_timestamp:
                raise DataMappingError("最小时间戳不能大于等于最大时间戳")

            # 检查配置一致性
            time_fields_in_mapping = set(self.field_mapping.values()) & self.time_fields
            if time_fields_in_mapping != self.time_fields:
                logger.warning(f"时间字段配置与字段映射不完全一致: {time_fields_in_mapping} vs {self.time_fields}")

            logger.info("配置验证通过")

        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            raise DataMappingError(f"配置验证失败: {e}") from e

    def validate_field_compatibility(self, excel_data: list[dict[str, Any]]) -> dict[str, Any]:
        """
        验证Excel数据字段与配置的兼容性

        Args:
            excel_data: Excel数据列表

        Returns:
            Dict: 兼容性检查结果
        """
        if not excel_data:
            return {"compatible": True, "warnings": [], "errors": []}

        warnings = []
        errors = []

        # 收集所有字段
        all_fields = set()
        for row in excel_data:
            all_fields.update(row.keys())

        # 检查必填字段
        missing_required = self.required_fields - all_fields
        if missing_required:
            errors.append(f"缺少必填字段: {missing_required}")

        # 检查未知字段
        known_fields = set(self.field_mapping.keys()) | self.required_fields
        unknown_fields = all_fields - known_fields
        if unknown_fields:
            warnings.append(f"发现未知字段: {unknown_fields}")

        # 检查字段值类型
        for row_idx, row in enumerate(excel_data[:5]):  # 只检查前5行作为样本
            for field, value in row.items():
                if field in self.time_fields and value is not None:
                    if not self._is_valid_time_value(value):
                        warnings.append(f"第{row_idx + 1}行字段'{field}'的时间格式可能无效: {value}")

        result = {
            "compatible": len(errors) == 0,
            "warnings": warnings,
            "errors": errors,
            "total_fields": len(all_fields),
            "known_fields": len(known_fields & all_fields),
            "unknown_fields": len(unknown_fields),
        }

        return result

    def _is_valid_time_value(self, value: Any) -> bool:
        """检查时间值是否有效"""
        if value is None or value == "":
            return True

        try:
            # 尝试转换时间值
            converted = self._convert_to_timestamp(value)
            return converted != ""
        except Exception:
            return False

    def map_to_registration_format(
        self, excel_data: list[dict[str, Any]], base_rule: RuleTemplate, operation: str = "UPSERT"
    ) -> list[dict[str, Any]]:
        """
        将Excel数据映射为注册接口格式

        Args:
            excel_data: Excel解析后的数据列表
            base_rule: 规则模板信息
            operation: 操作类型，UPSERT或DELETE

        Returns:
            List[Dict]: 注册接口格式的数据列表

        Raises:
            DataMappingError: 映射过程中发生错误
        """
        if not excel_data:
            logger.warning("Excel数据为空，跳过映射")
            return []

        if not base_rule:
            raise DataMappingError("BaseRule对象不能为空")

        logger.info(f"开始映射数据，Excel记录数: {len(excel_data)}, 操作类型: {operation}")

        # 执行字段兼容性检查
        compatibility_result = self.validate_field_compatibility(excel_data)
        if not compatibility_result["compatible"]:
            error_msg = f"字段兼容性检查失败: {compatibility_result['errors']}"
            logger.error(error_msg)
            raise DataMappingError(error_msg, details=compatibility_result)

        # 记录警告信息
        if compatibility_result["warnings"]:
            for warning in compatibility_result["warnings"]:
                logger.warning(f"字段兼容性警告: {warning}")

        mapped_data = []

        for index, row_data in enumerate(excel_data):
            try:
                mapped_row = self._map_single_row(row_data, base_rule, operation)
                mapped_data.append(mapped_row)
            except DataMappingError as e:
                # 重新抛出数据映射错误，保留原始错误信息
                error_msg = f"映射第{index + 1}行数据时发生错误: {str(e)}"
                logger.error(error_msg)
                raise DataMappingError(
                    error_msg,
                    field_name=e.field_name,  # 保留原始字段名
                    details={"row_index": index, "row_data": row_data, "original_details": e.details},
                ) from e
            except Exception as e:
                error_msg = f"映射第{index + 1}行数据时发生错误: {str(e)}"
                logger.error(error_msg)
                raise DataMappingError(error_msg, details={"row_index": index, "row_data": row_data}) from e

        logger.info(f"数据映射完成，成功映射 {len(mapped_data)} 条记录")
        return mapped_data

    def map_to_rule_detail_format(
        self, excel_data: list[dict[str, Any]], dataset_id: int, operation: str = "UPSERT"
    ) -> list[dict[str, Any]]:
        """
        将Excel数据直接映射为RuleDetail模型格式（新增功能）

        Args:
            excel_data: Excel解析后的数据列表
            dataset_id: 数据集ID
            operation: 操作类型，UPSERT或DELETE

        Returns:
            List[Dict]: RuleDetail模型格式的数据列表

        Raises:
            DataMappingError: 映射过程中发生错误
        """
        if not excel_data:
            logger.warning("Excel数据为空，跳过结构化映射")
            return []

        if not isinstance(dataset_id, int) or dataset_id <= 0:
            raise DataMappingError("dataset_id必须是正整数")

        logger.info(f"开始结构化映射，数据量: {len(excel_data)}, 数据集ID: {dataset_id}, 操作: {operation}")

        mapped_data = []

        for index, row_data in enumerate(excel_data):
            try:
                mapped_row = self._map_single_row_to_structured(row_data, dataset_id, operation)
                mapped_data.append(mapped_row)
            except DataMappingError as e:
                error_msg = f"结构化映射第{index + 1}行数据时发生错误: {str(e)}"
                logger.error(error_msg)
                raise DataMappingError(
                    error_msg,
                    field_name=e.field_name,
                    details={"row_index": index, "row_data": row_data, "original_details": e.details},
                ) from e
            except Exception as e:
                error_msg = f"结构化映射第{index + 1}行数据时发生错误: {str(e)}"
                logger.error(error_msg)
                raise DataMappingError(error_msg, details={"row_index": index, "row_data": row_data}) from e

        logger.info(f"结构化映射完成，成功映射 {len(mapped_data)} 条记录")
        return mapped_data

    def _map_single_row_to_structured(
        self, row_data: dict[str, Any], dataset_id: int, operation: str
    ) -> dict[str, Any]:
        """
        映射单行数据到结构化格式

        Args:
            row_data: 单行Excel数据
            dataset_id: 数据集ID
            operation: 操作类型

        Returns:
            Dict: RuleDetail模型格式的单条数据
        """
        # 验证必填字段
        self._validate_required_fields(row_data)

        # 构建结构化数据
        structured_data = {
            "dataset_id": dataset_id,
            "operation": operation,  # 保留操作类型用于后续处理
        }

        # 用于存储未映射字段的extra_data
        extra_data = {}

        # 映射所有字段
        for excel_field, value in row_data.items():
            # 获取对应的模型字段名
            model_field = self.structured_field_mapping.get(excel_field)

            if model_field:
                # 有映射的字段，直接处理
                processed_value = self._process_structured_field_value(model_field, value)
                structured_data[model_field] = processed_value
            else:
                # 没有映射的字段，存储到extra_data中
                if value is not None and value != "":
                    extra_data[excel_field] = value
                    logger.debug(f"将未映射字段 '{excel_field}' 存储到 extra_data: {value}")

        # 如果有未映射的字段，添加到extra_data
        if extra_data:
            structured_data["extra_data"] = extra_data
            logger.info(f"存储了 {len(extra_data)} 个未映射字段到 extra_data: {list(extra_data.keys())}")

        # 确保必需字段存在
        if "rule_detail_id" not in structured_data or not structured_data["rule_detail_id"]:
            # 如果没有rule_detail_id，尝试从rule_id生成
            rule_id = row_data.get("rule_id") or self._generate_rule_id(row_data.get("rule_name", ""))
            structured_data["rule_detail_id"] = rule_id

        if "rule_name" not in structured_data or not structured_data["rule_name"]:
            structured_data["rule_name"] = row_data.get("rule_name", "")

        # 设置默认状态
        if "status" not in structured_data:
            structured_data["status"] = "ACTIVE"

        return structured_data

    def _process_structured_field_value(self, field_name: str, value: Any) -> Any:
        """
        处理结构化字段值，包括类型转换

        Args:
            field_name: 字段名
            value: 原始值

        Returns:
            Any: 处理后的值
        """
        if value is None or value == "":
            return None

        # 时间字段处理
        if field_name in ["effective_start_time", "effective_end_time"]:
            timestamp_str = self._convert_to_timestamp(value)
            if timestamp_str:
                # 转换为datetime对象
                try:
                    timestamp_ms = int(timestamp_str)
                    return datetime.fromtimestamp(timestamp_ms / 1000)
                except (ValueError, TypeError):
                    logger.warning(f"时间字段 {field_name} 转换失败: {value}")
                    return None
            return None

        # 数据类型转换
        target_type = self.field_types.get(field_name)
        if target_type:
            try:
                if target_type == bool:  # noqa: E721
                    return self._convert_to_boolean(value)
                elif target_type == int:  # noqa: E721
                    return self._convert_to_integer(value)
                elif target_type == float:  # noqa: E721
                    return self._convert_to_float(value)
                else:
                    return target_type(value)
            except (ValueError, TypeError) as e:
                logger.warning(f"字段 {field_name} 类型转换失败: {value} -> {target_type.__name__}, 错误: {e}")
                return None

        # 字符串处理
        if isinstance(value, str):
            return value.strip()
        elif isinstance(value, list):
            return ", ".join(str(item) for item in value if item)
        else:
            return str(value)

    def _convert_to_boolean(self, value: Any) -> bool:
        """转换值为布尔类型"""
        if isinstance(value, bool):
            return value

        str_value = str(value).strip().lower()
        return self.boolean_mapping.get(str_value, False)

    def _convert_to_integer(self, value: Any) -> int:
        """转换值为整数类型"""
        if isinstance(value, int):
            return value
        elif isinstance(value, float):
            return int(value)
        elif isinstance(value, str):
            # 移除可能的千分位分隔符
            clean_value = value.replace(",", "").replace(" ", "")
            return int(float(clean_value))
        else:
            return int(value)

    def _convert_to_float(self, value: Any) -> float:
        """转换值为浮点数类型"""
        if isinstance(value, int | float):
            return float(value)
        elif isinstance(value, str):
            # 移除可能的千分位分隔符和货币符号
            clean_value = value.replace(",", "").replace(" ", "").replace("￥", "").replace("$", "")
            return float(clean_value)
        else:
            return float(value)

    def _map_single_row(self, row_data: dict[str, Any], base_rule: RuleTemplate, operation: str) -> dict[str, Any]:
        """
        映射单行数据

        Args:
            row_data: 单行Excel数据
            base_rule: 规则模板信息
            operation: 操作类型

        Returns:
            Dict: 注册接口格式的单条数据
        """
        # 验证必填字段
        self._validate_required_fields(row_data)

        # 生成规则ID（复用前端逻辑）
        rule_id = self._generate_rule_id(row_data.get("rule_name"))

        # 构建基础结构
        mapped_data = {
            "id": rule_id,
            "name": row_data.get("rule_name", ""),
            "outputs": self._build_outputs_array(row_data, base_rule),
            "script": "print('this is python script')",  # 固定脚本内容
            "createTime": int(time.perf_counter() * 1000),  # 当前时间戳（毫秒）
            "operate": operation,
        }

        return mapped_data

    def _validate_required_fields(self, row_data: dict[str, Any]):
        """验证必填字段"""
        for field in self.required_fields:
            value = row_data.get(field)
            if not value or str(value).strip() == "":
                raise DataMappingError(f"必填字段 '{field}' 缺失或为空", field_name=field)

    def _generate_rule_id(self, rule_name: str) -> str:
        """
        生成规则ID（复用前端逻辑）

        Args:
            rule_name: 规则名称

        Returns:
            str: MD5哈希生成的规则ID
        """
        if not rule_name:
            raise DataMappingError("规则名称不能为空，无法生成ID")

        # 使用MD5哈希生成ID（与前端逻辑一致）
        return hashlib.md5(str(rule_name).encode("utf-8")).hexdigest()

    def _build_outputs_array(self, row_data: dict[str, Any], base_rule: RuleTemplate) -> list[dict[str, str]]:
        """
        构建outputs数组

        Args:
            row_data: Excel行数据
            base_rule: 规则模板信息

        Returns:
            List[Dict]: outputs数组
        """
        outputs = []

        # 处理Excel数据中的字段
        for key, value in row_data.items():
            # 跳过内部字段
            if key in ["rule_id", "rule_name"]:
                continue

            # 转换字段名（英文转中文，如果需要）
            display_key = self._convert_field_name(key)

            # 处理特殊字段
            if display_key in self.special_fields:
                processed_value = self.special_fields[display_key]
            # 处理时间字段
            elif display_key in self.time_fields:
                processed_value = self._convert_to_timestamp(value)
            else:
                processed_value = self._process_field_value(value)

            outputs.append({"key": display_key, "value": processed_value})

        # 添加模板相关字段（从BaseRule获取）
        outputs.extend(
            [
                {"key": "模板名称", "value": base_rule.rule_name or ""},
                {"key": "模板内涵", "value": base_rule.description or ""},
            ]
        )

        return outputs

    def _convert_field_name(self, field_name: str) -> str:
        """
        转换字段名（英文转中文）

        Args:
            field_name: 英文字段名

        Returns:
            str: 中文字段名
        """
        return self.field_mapping.get(field_name, field_name)

    def _convert_to_timestamp(self, value: Any) -> str:
        """
        转换时间为毫秒级时间戳（增强版）

        Args:
            value: 时间值（可能是字符串、datetime对象等）

        Returns:
            str: 毫秒级时间戳字符串

        Raises:
            DataMappingError: 时间值超出有效范围时抛出异常
        """
        if not value:
            return ""

        try:
            timestamp_ms = None

            # 如果已经是时间戳格式
            if isinstance(value, int | float):
                # 如果是秒级时间戳，转换为毫秒级
                if value < 10000000000:  # 小于10位数，认为是秒级
                    timestamp_ms = int(value * 1000)
                else:
                    timestamp_ms = int(value)

            # 如果是字符串格式的时间戳
            elif isinstance(value, str) and value.isdigit():
                timestamp = int(value)
                if timestamp < 10000000000:
                    timestamp_ms = timestamp * 1000
                else:
                    timestamp_ms = timestamp

            # 如果是datetime对象
            elif isinstance(value, datetime):
                timestamp_ms = int(value.timestamp() * 1000)

            # 如果是字符串格式的日期时间
            elif isinstance(value, str):
                # 尝试解析常见的日期时间格式
                formats = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%d %H:%M",
                    "%Y-%m-%d",
                    "%Y/%m/%d %H:%M:%S",
                    "%Y/%m/%d %H:%M",
                    "%Y/%m/%d",
                    "%Y年%m月%d日 %H:%M:%S",
                    "%Y年%m月%d日 %H:%M",
                    "%Y年%m月%d日",
                ]

                for fmt in formats:
                    try:
                        dt = datetime.strptime(value.strip(), fmt)
                        timestamp_ms = int(dt.timestamp() * 1000)
                        break
                    except ValueError:
                        continue

            # 验证时间戳范围
            if timestamp_ms is not None:
                if timestamp_ms < self.min_timestamp or timestamp_ms > self.max_timestamp:
                    error_msg = f"时间值超出有效范围(1900-2100年): {value} -> {timestamp_ms}"
                    logger.error(error_msg)
                    raise DataMappingError(error_msg, field_name="时间字段")

                return str(timestamp_ms)

            # 如果无法解析，记录详细信息并返回空字符串
            logger.warning(f"无法解析时间值: {value} (类型: {type(value).__name__})")
            return ""

        except DataMappingError:
            # 重新抛出数据映射错误
            raise
        except Exception as e:
            error_msg = f"时间转换失败: {value} (类型: {type(value).__name__}), 错误: {e}"
            logger.error(error_msg)
            raise DataMappingError(error_msg, field_name="时间字段") from e

    def _process_field_value(self, value: Any) -> str:
        """
        处理字段值

        Args:
            value: 原始字段值

        Returns:
            str: 处理后的字符串值
        """
        if value is None:
            return ""

        # 如果是列表类型（复用前端逻辑）
        if isinstance(value, list):
            return ", ".join(str(item) for item in value if item)

        # 转换为字符串并清理
        str_value = str(value).strip()

        # 处理特殊字符（如换行符）
        str_value = str_value.replace("\n", "\r\n")

        return str_value

    def validate_mapping_result(self, mapped_data: list[dict[str, Any]]) -> bool:
        """
        验证映射结果的正确性

        Args:
            mapped_data: 映射后的数据

        Returns:
            bool: 验证是否通过
        """
        if not mapped_data:
            return True

        try:
            for index, item in enumerate(mapped_data):
                # 验证必需字段
                required_keys = ["id", "name", "outputs", "script", "createTime", "operate"]
                for key in required_keys:
                    if key not in item:
                        logger.error(f"第{index + 1}条记录缺少必需字段: {key}")
                        return False

                # 验证outputs格式
                if not isinstance(item["outputs"], list):
                    logger.error(f"第{index + 1}条记录的outputs字段格式错误")
                    return False

                for output in item["outputs"]:
                    if not isinstance(output, dict) or "key" not in output or "value" not in output:
                        logger.error(f"第{index + 1}条记录的outputs数组格式错误")
                        return False

                # 验证时间戳格式
                if not isinstance(item["createTime"], int):
                    logger.error(f"第{index + 1}条记录的createTime格式错误")
                    return False

            logger.info(f"映射结果验证通过，共 {len(mapped_data)} 条记录")
            return True

        except Exception as e:
            logger.error(f"映射结果验证失败: {e}")
            return False

    def get_mapping_stats(self, mapped_data: list[dict[str, Any]]) -> dict[str, Any]:
        """
        获取映射统计信息（增强版）

        Args:
            mapped_data: 映射后的数据

        Returns:
            Dict: 详细统计信息
        """
        if not mapped_data:
            return {
                "total_records": 0,
                "operations": {},
                "avg_outputs_count": 0,
                "field_usage": {},
                "time_fields_count": 0,
                "special_fields_count": 0,
            }

        operations = {}
        total_outputs = 0
        field_usage = {}
        time_fields_count = 0
        special_fields_count = 0

        for item in mapped_data:
            # 统计操作类型
            op = item.get("operate", "UNKNOWN")
            operations[op] = operations.get(op, 0) + 1

            # 统计输出字段
            outputs = item.get("outputs", [])
            total_outputs += len(outputs)

            # 统计字段使用情况
            for output in outputs:
                field_key = output.get("key", "")
                field_usage[field_key] = field_usage.get(field_key, 0) + 1

                # 统计时间字段和特殊字段
                if field_key in self.time_fields:
                    time_fields_count += 1
                if field_key in self.special_fields:
                    special_fields_count += 1

        return {
            "total_records": len(mapped_data),
            "operations": operations,
            "avg_outputs_count": total_outputs / len(mapped_data) if mapped_data else 0,
            "field_usage": field_usage,
            "time_fields_count": time_fields_count,
            "special_fields_count": special_fields_count,
            "unique_fields_count": len(field_usage),
        }

    def get_configuration_info(self) -> dict[str, Any]:
        """
        获取映射引擎配置信息

        Returns:
            Dict: 配置信息摘要
        """
        return {
            "required_fields": list(self.required_fields),
            "time_fields": list(self.time_fields),
            "special_fields": list(self.special_fields.keys()),
            "field_mapping_count": len(self.field_mapping),
            "structured_field_mapping_count": len(self.structured_field_mapping),
            "field_types_count": len(self.field_types),
            "timestamp_range": {
                "min_timestamp": self.min_timestamp,
                "max_timestamp": self.max_timestamp,
                "min_date": "1900-01-01",
                "max_date": "2100-01-01",
            },
            "supported_time_formats": [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M",
                "%Y-%m-%d",
                "%Y/%m/%d %H:%M:%S",
                "%Y/%m/%d %H:%M",
                "%Y/%m/%d",
                "%Y年%m月%d日 %H:%M:%S",
                "%Y年%m月%d日 %H:%M",
                "%Y年%m月%d日",
            ],
            "supported_mapping_modes": ["legacy", "structured", "hybrid", "auto"],
        }

    def get_optimal_mapping_mode(self, target_format: str = "auto", data_size: int = 0) -> str:
        """
        根据使用场景智能选择映射模式

        Args:
            target_format: 目标格式 ("auto", "legacy", "structured", "hybrid")
            data_size: 数据量大小，用于性能优化决策

        Returns:
            str: 最优映射模式
        """
        if target_format in ["legacy", "structured", "hybrid"]:
            return target_format

        # 自动选择模式
        if target_format == "auto":
            # 大数据量优先使用结构化映射（性能更好）
            if data_size > 1000:
                logger.info(f"数据量较大({data_size})，选择结构化映射模式")
                return "structured"
            # 中等数据量使用混合模式（兼容性好）
            elif data_size > 100:
                logger.info(f"数据量中等({data_size})，选择混合映射模式")
                return "hybrid"
            # 小数据量使用传统模式（稳定性好）
            else:
                logger.info(f"数据量较小({data_size})，选择传统映射模式")
                return "legacy"

        # 默认返回传统模式
        logger.warning(f"未知的目标格式: {target_format}，使用传统映射模式")
        return "legacy"

    def map_with_mode_selection(
        self,
        excel_data: list[dict[str, Any]],
        base_rule: RuleTemplate = None,
        dataset_id: int = None,
        operation: str = "UPSERT",
        target_format: str = "auto",
    ) -> tuple[list[dict[str, Any]], str]:
        """
        根据模式选择进行数据映射（智能适配方法）

        Args:
            excel_data: Excel数据
            base_rule: 规则模板（传统模式需要）
            dataset_id: 数据集ID（结构化模式需要）
            operation: 操作类型
            target_format: 目标格式

        Returns:
            Tuple[List[Dict], str]: (映射结果, 实际使用的模式)
        """
        if not excel_data:
            return [], "none"

        # 选择最优模式
        selected_mode = self.get_optimal_mapping_mode(target_format, len(excel_data))

        try:
            if selected_mode == "structured" and dataset_id is not None:
                # 使用结构化映射
                result = self.map_to_rule_detail_format(excel_data, dataset_id, operation)
                logger.info(f"使用结构化映射模式，映射 {len(result)} 条记录")
                return result, "structured"

            elif selected_mode == "legacy" and base_rule is not None:
                # 使用传统映射
                result = self.map_to_registration_format(excel_data, base_rule, operation)
                logger.info(f"使用传统映射模式，映射 {len(result)} 条记录")
                return result, "legacy"

            elif selected_mode == "hybrid":
                # 混合模式：优先尝试结构化，失败则降级到传统
                if dataset_id is not None:
                    try:
                        result = self.map_to_rule_detail_format(excel_data, dataset_id, operation)
                        logger.info(f"混合模式：使用结构化映射，映射 {len(result)} 条记录")
                        return result, "structured"
                    except Exception as e:
                        logger.warning(f"结构化映射失败，降级到传统映射: {e}")

                if base_rule is not None:
                    result = self.map_to_registration_format(excel_data, base_rule, operation)
                    logger.info(f"混合模式：使用传统映射，映射 {len(result)} 条记录")
                    return result, "legacy"

            # 如果所有模式都不可用，抛出错误
            raise DataMappingError(
                f"无法使用选定的映射模式 '{selected_mode}'，"
                f"需要提供相应的参数 (base_rule 或 dataset_id)"
            )

        except Exception as e:
            logger.error(f"映射模式 '{selected_mode}' 执行失败: {e}")
            raise

    def convert_legacy_to_structured(self, legacy_data: list[dict[str, Any]], dataset_id: int) -> list[dict[str, Any]]:
        """
        将传统outputs格式转换为结构化格式

        Args:
            legacy_data: 传统格式数据（包含outputs数组）
            dataset_id: 数据集ID

        Returns:
            List[Dict]: 结构化格式数据
        """
        if not legacy_data:
            return []

        structured_data = []

        for item in legacy_data:
            try:
                # 从outputs数组中提取字段
                outputs = item.get("outputs", [])
                extracted_data = {}

                for output in outputs:
                    key = output.get("key", "")
                    value = output.get("value", "")

                    # 反向映射：中文字段名 -> 英文字段名
                    english_field = self._reverse_field_mapping(key)
                    if english_field:
                        extracted_data[english_field] = value

                # 添加基础字段
                extracted_data["rule_id"] = item.get("id", "")
                extracted_data["rule_name"] = item.get("name", "")

                # 转换为结构化格式
                structured_item = self._map_single_row_to_structured(
                    extracted_data, dataset_id, item.get("operate", "UPSERT")
                )
                structured_data.append(structured_item)

            except Exception as e:
                logger.error(f"转换传统格式数据失败: {e}, 数据: {item}")
                continue

        logger.info(f"传统格式转换完成，转换 {len(structured_data)} 条记录")
        return structured_data

    def _reverse_field_mapping(self, chinese_field: str) -> str:
        """
        反向字段映射：中文字段名 -> 英文字段名

        Args:
            chinese_field: 中文字段名

        Returns:
            str: 英文字段名
        """
        # 在field_mapping中查找
        for english_field, chinese_name in self.field_mapping.items():
            if chinese_name == chinese_field:
                return english_field

        # 在structured_field_mapping中查找
        for excel_field, model_field in self.structured_field_mapping.items():
            if model_field == chinese_field:
                return excel_field

        # 如果找不到，返回原字段名
        return chinese_field

    def convert_structured_to_legacy(self, structured_data: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """
        将结构化格式转换为传统outputs格式（用于规则实例化）

        Args:
            structured_data: 结构化格式数据（RuleDetail字典列表）

        Returns:
            List[Dict]: 传统格式数据（包含outputs数组）
        """
        if not structured_data:
            return []

        legacy_data = []

        for item in structured_data:
            try:
                # 构建传统格式数据
                legacy_item = {
                    "id": item.get("rule_detail_id", ""),
                    "name": item.get("rule_name", ""),
                    "operate": "UPSERT",
                    "outputs": [],
                }

                # 构建outputs数组
                outputs = []

                # 映射结构化字段到传统格式
                field_mappings = {
                    "error_level_1": "一级错误类型",
                    "error_level_2": "二级错误类型",
                    "error_level_3": "三级错误类型",
                    "error_reason": "错误原因",
                    "error_severity": "错误程度",
                    "quality_basis": "质控依据或参考资料",
                    "location_desc": "具体位置描述",
                    "prompt_field_code": "提示字段编码",
                    "prompt_field_seq": "提示字段序号",
                    "prompt_field_type": "提示字段类型",
                    "rule_category": "规则类别",
                    "applicable_business": "适用业务",
                    "applicable_region": "适用地区",
                    "default_selected": "默认选用",
                    "involved_amount": "涉及金额",
                    "usage_quantity": "使用数量",
                    "violation_quantity": "违规数量",
                    "usage_days": "使用天数",
                    "violation_days": "违规天数",
                    "violation_items": "违规项目",
                    "effective_start_time": "生效开始时间",
                    "effective_end_time": "生效结束时间",
                    "remark": "备注信息",
                }

                # 添加字段到outputs数组
                for field_name, chinese_name in field_mappings.items():
                    value = item.get(field_name)
                    if value is not None:
                        # 特殊处理布尔值
                        if isinstance(value, bool):
                            value = "是" if value else "否"
                        # 特殊处理数值
                        elif isinstance(value, int | float):
                            value = str(value)
                        # 确保值是字符串
                        elif value is not None:
                            value = str(value)

                        outputs.append({"key": chinese_name, "value": value or ""})

                # 从 extra_data 中恢复规则特定字段
                extra_data = item.get("extra_data")
                if extra_data and isinstance(extra_data, dict):
                    for key, value in extra_data.items():
                        if value is not None:
                            # 直接添加到legacy_item的根级别，供规则实例化使用
                            legacy_item[key] = value
                            logger.debug(f"从 extra_data 恢复字段到 legacy 格式: {key} = {value}")

                legacy_item["outputs"] = outputs
                legacy_data.append(legacy_item)

            except Exception as e:
                logger.error(f"转换结构化数据失败: {e}, 数据: {item}")
                continue

        logger.debug(f"结构化格式转换完成，转换 {len(legacy_data)} 条记录")
        return legacy_data
