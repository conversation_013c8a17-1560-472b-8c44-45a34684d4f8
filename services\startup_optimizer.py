"""
启动优化器
管理服务启动流程，包括进程池预热、索引预构建、缓存预热等
确保服务启动后能够快速响应首次请求
"""

import asyncio
import time
from dataclasses import dataclass
from enum import Enum
from typing import Any

from config.settings import settings
from core.logging.logging_system import log as logger
from core.memory_optimizer import memory_optimizer
from services.index_prebuilder import index_prebuilder
from services.warmup_manager import warmup_manager


class StartupPhase(Enum):
    """启动阶段"""
    INITIALIZING = "initializing"
    LOADING_RULES = "loading_rules"
    BUILDING_INDEXES = "building_indexes"
    PREHEATING_POOL = "preheating_pool"
    WARMING_CACHE = "warming_cache"
    HEALTH_CHECK = "health_check"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class StartupStats:
    """启动统计信息"""
    total_time_seconds: float = 0.0
    phase_times: dict[str, float] = None
    memory_usage_mb: float = 0.0
    success: bool = False
    error_message: str = ""

    def __post_init__(self):
        if self.phase_times is None:
            self.phase_times = {}


class StartupOptimizer:
    """启动优化器"""

    def __init__(self):
        self.current_phase = StartupPhase.INITIALIZING
        self.startup_stats = StartupStats()
        self.is_optimizing = False
        self.optimization_task: asyncio.Task | None = None

        # 配置参数
        self.enabled = getattr(settings, "ENABLE_STARTUP_OPTIMIZATION", True)
        self.total_timeout_seconds = getattr(settings, "STARTUP_OPTIMIZATION_TIMEOUT", 600)  # 10分钟
        self.enable_parallel_warmup = getattr(settings, "ENABLE_PARALLEL_WARMUP", True)
        self.skip_warmup_on_timeout = getattr(settings, "SKIP_WARMUP_ON_TIMEOUT", True)

        logger.info(f"StartupOptimizer initialized: enabled={self.enabled}")

    async def optimize_startup(self) -> bool:
        """执行启动优化"""
        if not self.enabled:
            logger.info("启动优化功能已禁用，跳过优化")
            return True

        if self.is_optimizing:
            logger.warning("启动优化过程已在进行中")
            return False

        logger.info("开始启动优化...")
        self.is_optimizing = True
        self.startup_stats = StartupStats()

        try:
            start_time = time.perf_counter()

            # 创建优化任务
            self.optimization_task = asyncio.create_task(self._optimization_process())

            # 等待优化完成或超时
            success = await asyncio.wait_for(
                self.optimization_task,
                timeout=self.total_timeout_seconds
            )

            self.startup_stats.total_time_seconds = time.perf_counter() - start_time
            self.startup_stats.success = success
            self.current_phase = StartupPhase.COMPLETED if success else StartupPhase.FAILED

            # 记录内存使用
            memory_stats = memory_optimizer.get_memory_stats()
            self.startup_stats.memory_usage_mb = memory_stats.process_memory_mb

            logger.info(
                f"启动优化完成：{'成功' if success else '失败'}，"
                f"耗时 {self.startup_stats.total_time_seconds:.1f}s，"
                f"内存使用 {self.startup_stats.memory_usage_mb:.1f}MB"
            )

            return success

        except asyncio.TimeoutError:  # noqa: UP041
            logger.warning(f"启动优化超时（{self.total_timeout_seconds}s）")
            self.startup_stats.error_message = "启动优化超时"
            self.current_phase = StartupPhase.FAILED
            return False

        except Exception as e:
            logger.error(f"启动优化异常: {e}", exc_info=True)
            self.startup_stats.error_message = str(e)
            self.current_phase = StartupPhase.FAILED
            return False

        finally:
            self.is_optimizing = False

    async def _optimization_process(self) -> bool:
        """优化处理过程"""
        phases = [
            (StartupPhase.LOADING_RULES, self._load_rules_phase),
            (StartupPhase.BUILDING_INDEXES, self._build_indexes_phase),
            (StartupPhase.PREHEATING_POOL, self._preheat_pool_phase),
            (StartupPhase.WARMING_CACHE, self._warm_cache_phase),
            (StartupPhase.HEALTH_CHECK, self._health_check_phase),
        ]

        for phase, phase_func in phases:
            self.current_phase = phase
            phase_start = time.perf_counter()

            logger.info(f"执行启动阶段: {phase.value}")

            try:
                success = await phase_func()
                phase_time = time.perf_counter() - phase_start
                self.startup_stats.phase_times[phase.value] = phase_time

                logger.info(f"阶段 {phase.value} {'成功' if success else '失败'}，耗时 {phase_time:.1f}s")

                if not success:
                    # 某些阶段失败可以继续，某些阶段失败需要停止
                    if phase in [StartupPhase.LOADING_RULES, StartupPhase.BUILDING_INDEXES]:
                        logger.error(f"关键阶段 {phase.value} 失败，停止启动优化")
                        return False
                    else:
                        logger.warning(f"非关键阶段 {phase.value} 失败，继续后续阶段")

            except Exception as e:
                phase_time = time.perf_counter() - phase_start
                self.startup_stats.phase_times[phase.value] = phase_time
                logger.error(f"阶段 {phase.value} 异常: {e}", exc_info=True)

                # 关键阶段异常需要停止
                if phase in [StartupPhase.LOADING_RULES, StartupPhase.BUILDING_INDEXES]:
                    return False

        return True

    async def _load_rules_phase(self) -> bool:
        """规则加载阶段"""
        try:
            from core.rule_cache import RULE_CACHE

            # 检查规则是否已加载
            rule_count = len(RULE_CACHE)
            if rule_count > 0:
                logger.info(f"规则已加载：{rule_count} 个规则")
                return True
            else:
                logger.warning("规则缓存为空，可能需要手动加载规则")
                return False

        except Exception as e:
            logger.error(f"规则加载阶段失败: {e}", exc_info=True)
            return False

    async def _build_indexes_phase(self) -> bool:
        """索引构建阶段"""
        try:
            return await index_prebuilder.prebuild_all_indexes()
        except Exception as e:
            logger.error(f"索引构建阶段失败: {e}", exc_info=True)
            return False

    async def _preheat_pool_phase(self) -> bool:
        """进程池预热阶段"""
        try:
            from core.dynamic_process_pool import dynamic_process_pool

            # 确保进程池已启动
            if not dynamic_process_pool._is_running:
                await dynamic_process_pool.start()

            # 等待进程池稳定
            await asyncio.sleep(2.0)

            # 检查进程池状态
            stats = dynamic_process_pool.get_stats()
            if stats.current_workers > 0:
                logger.info(f"进程池预热成功：{stats.current_workers} 个工作进程")
                return True
            else:
                logger.warning("进程池预热失败：无工作进程")
                return False

        except Exception as e:
            logger.error(f"进程池预热阶段失败: {e}", exc_info=True)
            return False

    async def _warm_cache_phase(self) -> bool:
        """缓存预热阶段"""
        try:
            if self.enable_parallel_warmup:
                # 并行预热（推荐）
                return await warmup_manager.start_warmup()
            else:
                # 串行预热（保守）
                return await warmup_manager.start_warmup()

        except Exception as e:
            logger.error(f"缓存预热阶段失败: {e}", exc_info=True)
            # 缓存预热失败不影响服务启动
            return True

    async def _health_check_phase(self) -> bool:
        """健康检查阶段"""
        try:
            # 检查各组件状态
            checks = []

            # 1. 检查规则缓存
            from core.rule_cache import RULE_CACHE
            checks.append(("规则缓存", len(RULE_CACHE) > 0))

            # 2. 检查进程池
            from core.dynamic_process_pool import dynamic_process_pool
            checks.append(("进程池", dynamic_process_pool._is_running))

            # 3. 检查内存使用
            memory_stats = memory_optimizer.get_memory_stats()
            memory_ok = memory_stats.process_memory_mb < 1024  # 小于1GB
            checks.append(("内存使用", memory_ok))

            # 4. 检查缓存状态
            from core.intelligent_cache import intelligent_cache
            cache_stats = intelligent_cache.get_overall_stats()
            cache_ok = len(cache_stats) > 0
            checks.append(("智能缓存", cache_ok))

            # 统计检查结果
            passed_checks = sum(1 for _, ok in checks if ok)
            total_checks = len(checks)

            logger.info(f"健康检查结果：{passed_checks}/{total_checks} 项通过")
            for name, ok in checks:
                logger.info(f"  {name}: {'✓' if ok else '✗'}")

            return passed_checks >= total_checks * 0.8  # 80%通过率

        except Exception as e:
            logger.error(f"健康检查阶段失败: {e}", exc_info=True)
            return False

    def get_startup_status(self) -> dict[str, Any]:
        """获取启动状态"""
        return {
            "enabled": self.enabled,
            "is_optimizing": self.is_optimizing,
            "current_phase": self.current_phase.value,
            "stats": {
                "total_time_seconds": self.startup_stats.total_time_seconds,
                "phase_times": self.startup_stats.phase_times,
                "memory_usage_mb": self.startup_stats.memory_usage_mb,
                "success": self.startup_stats.success,
                "error_message": self.startup_stats.error_message,
            },
            "config": {
                "total_timeout_seconds": self.total_timeout_seconds,
                "enable_parallel_warmup": self.enable_parallel_warmup,
                "skip_warmup_on_timeout": self.skip_warmup_on_timeout,
            }
        }

    async def manual_optimize(self) -> dict[str, Any]:
        """手动触发启动优化"""
        if self.is_optimizing:
            return {"success": False, "message": "启动优化过程已在进行中"}

        logger.info("手动触发启动优化")
        success = await self.optimize_startup()

        return {
            "success": success,
            "message": "启动优化完成" if success else "启动优化失败",
            "stats": self.get_startup_status()["stats"]
        }


# 全局启动优化器实例
startup_optimizer = StartupOptimizer()
