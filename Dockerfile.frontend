######################## 1⃣️  构建阶段 (Build Stage) ########################
FROM node:22-alpine AS frontend-builder

# 构建环境变量和时区设置
ARG NODE_ENV=production
ARG VITE_API_BASE_URL=/api
ARG VITE_BASE_URL=/
ENV NODE_ENV=$NODE_ENV \
    VITE_API_BASE_URL=$VITE_API_BASE_URL \
    VITE_BASE_URL=$VITE_BASE_URL \
    TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 配置 npm 镜像源（提高下载速度）
RUN npm config set registry https://registry.npmmirror.com

# 仅复制依赖声明以充分利用 Docker 缓存
COPY frontend/package*.json ./

# 安装构建依赖和项目依赖（包含devDependencies用于构建）
RUN apk add --no-cache --virtual .build-deps python3 make g++ git \
 && npm ci --silent --no-audit --no-fund --include=dev \
 && npm cache clean --force \
 && apk del .build-deps

# 复制源代码并构建
COPY frontend/ .
RUN npm run build:prod

# 验证构建产物
RUN if [ ! -d "dist" ] || [ -z "$(ls -A dist)" ]; then \
      echo "❌ 构建失败：dist 目录为空或不存在"; \
      exit 1; \
   fi && \
   echo "✅ 构建成功，产物大小: $(du -sh dist | cut -f1)"

######################## 2⃣️  运行时阶段 (Runtime Stage) ########################
FROM nginx:1.25-alpine AS frontend-production

# 运行时参数
ARG API_BACKEND_HOST=rule-master
ARG API_BACKEND_PORT=18001

# 环境变量设置
ENV API_BACKEND_HOST=$API_BACKEND_HOST \
    API_BACKEND_PORT=$API_BACKEND_PORT \
    TZ=Asia/Shanghai

# 创建非 root 用户以提高安全性
RUN addgroup -g 1001 app && adduser -D -u 1001 -G app app

# 安装运行期所需工具（envsubst 来自 gettext）
RUN apk add --no-cache gettext \
 && rm -rf /var/cache/apk/*

# 设置工作目录和权限
WORKDIR /usr/share/nginx/html
RUN mkdir -p /var/cache/nginx/client_temp /var/run/nginx \
 && chown -R app:app /var/cache/nginx /var/run/nginx /usr/share/nginx/html /etc/nginx/conf.d

# 从构建阶段复制构建产物
COPY --from=frontend-builder --chown=app:app /app/dist ./

# 创建简化的 nginx 主配置（不包含 server 块）
RUN printf 'worker_processes auto;\nerror_log /var/log/nginx/error.log notice;\npid /tmp/nginx.pid;\n\nevents {\n    worker_connections 1024;\n    use epoll;\n    multi_accept on;\n}\n\nhttp {\n    include /etc/nginx/mime.types;\n    default_type application/octet-stream;\n    \n    log_format main '\''$remote_addr - $remote_user [$time_local] "$request" '\''\n                    '\''$status $body_bytes_sent "$http_referer" '\''\n                    '\''"$http_user_agent" "$http_x_forwarded_for"'\'';\n    \n    access_log /var/log/nginx/access.log main;\n    \n    sendfile on;\n    tcp_nopush on;\n    tcp_nodelay on;\n    keepalive_timeout 65;\n    types_hash_max_size 2048;\n    server_tokens off;\n    \n    # 包含服务器配置\n    include /etc/nginx/conf.d/*.conf;\n}' > /etc/nginx/nginx.conf

# 创建 nginx 配置模板（支持环境变量替换）
RUN printf 'server {\n    listen 18099;\n    server_name localhost;\n    root /usr/share/nginx/html;\n    index index.html;\n    \n    # 启用 gzip 压缩\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;\n    \n    # 静态资源缓存\n    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {\n        expires 1y;\n        add_header Cache-Control "public, immutable";\n        add_header Vary Accept-Encoding;\n    }\n    \n    # HTML 文件不缓存\n    location ~* \\.html$ {\n        expires -1;\n        add_header Cache-Control "no-cache, no-store, must-revalidate";\n        add_header Pragma "no-cache";\n    }\n    \n    # API 代理（环境变量配置）\n    location /api/ {\n        proxy_pass http://${API_BACKEND_HOST}:${API_BACKEND_PORT};\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n        proxy_buffering off;\n    }\n    \n    # SPA 路由支持\n    location / {\n        try_files $uri $uri/ /index.html;\n    }\n    \n    # 健康检查端点\n    location /health {\n        access_log off;\n        return 200 "healthy\\n";\n        add_header Content-Type text/plain;\n    }\n    \n    # 安全响应头\n    add_header X-Frame-Options "SAMEORIGIN" always;\n    add_header X-Content-Type-Options "nosniff" always;\n    add_header X-XSS-Protection "1; mode=block" always;\n    add_header Referrer-Policy "strict-origin-when-cross-origin" always;\n    add_header Content-Security-Policy "default-src '\''self'\''; script-src '\''self'\'' '\''unsafe-inline'\'' '\''unsafe-eval'\''; style-src '\''self'\'' '\''unsafe-inline'\''; img-src '\''self'\'' data: https:; font-src '\''self'\'' data:" always;\n}' > /etc/nginx/conf.d/default.conf.template

# 启动脚本（支持 envsubst）
COPY start-frontend.sh /app/start-frontend.sh
RUN chmod +x /app/start-frontend.sh

# 构建信息文件
RUN echo \"{\\n  \\\"build_time\\\": \\\"$(date -u +%Y-%m-%dT%H:%M:%SZ)\\\",\\n  \\\"nginx_version\\\": \\\"$(nginx -v 2>&1 | cut -d' ' -f3)\\\",\\n  \\\"node_version\\\": \\\"22-alpine\\\",\\n  \\\"build_type\\\": \\\"production\\\",\\n  \\\"framework\\\": \\\"Vue 3 + Element Plus + Vite\\\"\\n}\" > /usr/share/nginx/html/build-info.json

# 切换到非 root 用户
USER app

# 健康检查配置（BusyBox wget 已自带）
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD wget -qO- http://127.0.0.1:18099/health || exit 1

# 暴露端口
EXPOSE 18099

# 镜像标签
LABEL component="frontend" \
      service.type="web" \
      framework="vue3" \
      ui.library="element-plus" \
      build.tool="vite" \
      maintainer="rule-management-system" \
      security.user="non-root"

# 启动命令
ENTRYPOINT ["/app/start-frontend.sh"]
