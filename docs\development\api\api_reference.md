# 规则明细 API 参考文档

## 概述

规则明细 API 提供对规则明细数据的完整 CRUD 操作，支持单条操作、批量操作和增量更新。

**重要更新**：API接口已完全统一，使用标准化字段名，提供一致的开发体验。

## 基础信息

- **统一接口 URL**: `/api/v1/rules/details`
- **认证方式**: API Key (Header: `X-API-KEY`)
- **响应格式**: JSON
- **内容类型**: `application/json`
- **层级关系**: 规则明细是规则管理的子功能，遵循 RESTful 资源层级结构
- **API版本**: v1.0.0 (统一后版本)
- **最后更新**: 2025-07-24

## 字段标准化说明

统一接口使用标准化字段命名规范，与 `field_mapping.json` v3.1.0 配置保持一致：

| 传统字段名 | 标准字段名 | 中文名称 | 说明 |
|-----------|-----------|----------|------|
| `error_level_1` | `level1` | 一级错误类型 | 核心标准化字段 |
| `error_level_2` | `level2` | 二级错误类型 | 核心标准化字段 |
| `error_level_3` | `level3` | 三级错误类型 | 核心标准化字段 |
| `error_severity` | `degree` | 错误程度 | 字段名称标准化 |
| `quality_basis` | `reference` | 质控依据或参考资料 | 字段名称标准化 |
| `location_desc` | `detail_position` | 具体位置描述 | 字段名称标准化 |
| `prompt_field_code` | `prompted_fields1` | 提示字段编码 | 字段名称标准化 |
| `prompt_field_type` | `prompted_fields3` | 提示字段类型 | 字段名称标准化 |
| `rule_category` | `type` | 规则类别 | 字段名称标准化 |
| `applicable_business` | `pos` | 适用业务 | 字段名称标准化 |
| `applicable_region` | `applicableArea` | 适用地区 | 字段名称标准化 |
| `default_selected` | `default_use` | 默认选用 | 字段名称标准化 |

## API 端点

## 🌟 标准化接口（推荐使用）

### 1. 创建规则明细

**POST** `/{rule_key}`

创建单条规则明细记录，使用标准字段名。

**请求参数**:
- `rule_key` (path): 规则键

**请求体**:
```json
{
  "rule_id": "string",
  "rule_name": "string",
  "level1": "string",           // 标准字段名
  "level2": "string",           // 标准字段名
  "level3": "string",           // 标准字段名
  "error_reason": "string",
  "degree": "string",           // 标准字段名
  "reference": "string",        // 标准字段名
  "detail_position": "string",  // 标准字段名
  "prompted_fields1": "string", // 标准字段名
  "type": "string",             // 标准字段名
  "pos": "string",              // 标准字段名
  "applicableArea": "string",   // 标准字段名
  "default_use": "string",      // 标准字段名
  "start_date": "string",
  "end_date": "string"
  // ... 其他字段
}
```

**响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "创建成功",
  "data": {
    "id": 1,
    "rule_id": "string",
    "rule_name": "string",
    "level1": "string",
    "level2": "string",
    "level3": "string",
    "degree": "string",
    "reference": "string"
    // ... 完整的规则明细数据（使用标准字段名）
  }
}
```

### 2. 查询规则明细列表

**GET** `/{rule_key}`

查询指定规则的明细列表，支持分页、排序、过滤，使用标准字段名。

**请求参数**:
- `rule_key` (path): 规则键
- `page` (query): 页码，默认 1
- `page_size` (query): 每页大小，默认 20，最大 100
- `status` (query): 状态过滤 (`ACTIVE`, `INACTIVE`, `DEPRECATED`)
- `sort_by` (query): 排序字段，默认 `created_at`
- `sort_order` (query): 排序方向，`asc` 或 `desc`，默认 `desc`
- `search` (query): 搜索关键词（支持规则名称、错误原因等字段）
- `level1` (query): 一级错误类型过滤（标准字段名）
- `level2` (query): 二级错误类型过滤（标准字段名）
- `type` (query): 规则类别过滤（标准字段名）

**响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "查询成功",
  "data": {
    "items": [
      {
        "id": 1,
        "rule_id": "rule_001",
        "rule_key": "drug_limit_adult_and_diag_exact",
        "rule_name": "成人药品限制规则",
        "level1": "药品适应症",
        "level2": "限制使用",
        "level3": "年龄限制",
        "error_reason": "该药品仅限成人使用",
        "degree": "严重",
        "reference": "药品说明书第3条",
        "detail_position": "处方明细",
        "prompted_fields1": "drug_code",
        "prompted_fields3": "药品编码",
        "type": "药品规则",
        "pos": "门诊",
        "applicableArea": "全国",
        "default_use": "是",
        "remarks": "特殊情况需医师确认",
        "in_illustration": "输入药品编码和患者年龄",
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "yb_code": "XB05BA01,XB05BA02",
        "diag_whole_code": "I10.x00",
        "diag_code_prefix": "I10",
        "diag_name_keyword": "高血压",
        "fee_whole_code": "310101001",
        "fee_code_prefix": "3101",
        "extended_fields": "{\"age_threshold\": 18, \"limit_days\": 30}",
        "status": "ACTIVE",
        "created_at": "2024-07-24T10:00:00Z",
        "updated_at": "2024-07-24T10:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  },
  "timestamp": 1721808000.123
}
```

### 3. 查询单条规则明细

**GET** `/{rule_key}/{detail_id}`

查询指定的单条规则明细。

**请求参数**:
- `rule_key` (path): 规则键
- `detail_id` (path): 明细ID

**响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "查询成功",
  "data": {
    "id": 1,
    "rule_id": "rule_001",
    "rule_key": "drug_limit_adult_and_diag_exact",
    "rule_name": "成人药品限制规则",
    "level1": "药品适应症",
    "level2": "限制使用",
    "level3": "年龄限制",
    "error_reason": "该药品仅限成人使用",
    "degree": "严重",
    "reference": "药品说明书第3条",
    "detail_position": "处方明细",
    "prompted_fields1": "drug_code",
    "prompted_fields3": "药品编码",
    "type": "药品规则",
    "pos": "门诊",
    "applicableArea": "全国",
    "default_use": "是",
    "remarks": "特殊情况需医师确认",
    "in_illustration": "输入药品编码和患者年龄",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "yb_code": "XB05BA01,XB05BA02",
    "diag_whole_code": "I10.x00",
    "diag_code_prefix": "I10",
    "diag_name_keyword": "高血压",
    "fee_whole_code": "310101001",
    "fee_code_prefix": "3101",
    "extended_fields": "{\"age_threshold\": 18, \"limit_days\": 30}",
    "status": "ACTIVE",
    "created_at": "2024-07-24T10:00:00Z",
    "updated_at": "2024-07-24T10:00:00Z"
  },
  "timestamp": 1721808000.123
}
```

### 4. 更新规则明细

**PUT** `/{rule_key}/{detail_id}`

更新指定的规则明细。

**请求参数**:
- `rule_key` (path): 规则键
- `detail_id` (path): 明细ID

**请求体**:
```json
{
  "rule_name": "更新后的规则名称",
  "level1": "药品适应症",
  "level2": "限制使用",
  "level3": "年龄限制",
  "error_reason": "更新后的错误原因",
  "degree": "严重",
  "reference": "更新后的质控依据",
  "detail_position": "处方明细",
  "prompted_fields1": "drug_code",
  "prompted_fields3": "药品编码",
  "type": "药品规则",
  "pos": "门诊",
  "applicableArea": "全国",
  "default_use": "是",
  "remarks": "更新后的备注",
  "in_illustration": "更新后的入参说明",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "yb_code": "XB05BA01,XB05BA02",
  "diag_whole_code": "I10.x00",
  "diag_code_prefix": "I10",
  "diag_name_keyword": "高血压",
  "fee_whole_code": "310101001",
  "fee_code_prefix": "3101",
  "extended_fields": "{\"age_threshold\": 18, \"limit_days\": 30}",
  "status": "ACTIVE"
}
```

**响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "更新成功",
  "data": {
    "id": 1,
    "rule_id": "rule_001",
    // ... 完整的更新后数据
  },
  "timestamp": 1721808000.123
}
```

### 5. 删除规则明细

**DELETE** `/{rule_key}/{detail_id}`

删除指定的规则明细。

**请求参数**:
- `rule_key` (path): 规则键
- `detail_id` (path): 明细ID

**响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "删除成功",
  "data": null,
  "timestamp": 1721808000.123
}
```

### 6. 批量操作

**POST** `/{rule_key}/batch`

批量创建、更新或删除规则明细。

**请求体**:
```json
{
  "operations": [
    {
      "operation": "CREATE",
      "data": {
        "rule_name": "新规则1",
        "level1": "药品适应症",
        // ... 其他字段
      }
    },
    {
      "operation": "UPDATE",
      "rule_id": "rule_001",
      "data": {
        "rule_name": "更新规则1",
        // ... 其他字段
      }
    },
    {
      "operation": "DELETE",
      "rule_id": "rule_002"
    }
  ]
}
```

**响应**:
```json
{
  "code": 200,
  "success": true,
  "message": "批量操作完成",
  "data": {
    "total_operations": 3,
    "successful_operations": 2,
    "failed_operations": 1,
    "results": [
      {
        "operation": "CREATE",
        "status": "SUCCESS",
        "data": { /* 创建的数据 */ }
      },
      {
        "operation": "UPDATE",
        "status": "SUCCESS",
        "data": { /* 更新的数据 */ }
      },
      {
        "operation": "DELETE",
        "status": "FAILED",
        "error": "规则明细不存在"
      }
    ]
  },
  "timestamp": 1721808000.123
}
```

## 错误码

| 错误码 | 说明 | HTTP状态码 |
|--------|------|-----------|
| 200 | 操作成功 | 200 |
| 400 | 请求参数错误 | 200 |
| 401 | 认证失败 | 200 |
| 403 | 权限不足 | 200 |
| 404 | 资源不存在 | 200 |
| 500 | 服务器内部错误 | 200 |
| 605 | 规则明细验证失败 | 200 |
| 606 | 规则明细不存在 | 200 |
| 607 | 规则明细已存在 | 200 |
| 608 | 规则明细批量操作失败 | 200 |
| 609 | 规则明细增量操作失败 | 200 |

**注意**: 所有API响应都使用HTTP 200状态码，实际的业务状态通过响应体中的`code`字段表示。

## 字段说明

### 通用字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `rule_id` | string | 否 | 规则ID |
| `rule_key` | string | 是 | 规则键，关联规则模板 |
| `rule_name` | string | 是 | 规则名称 |
| `level1` | string | 是 | 一级错误类型 |
| `level2` | string | 是 | 二级错误类型 |
| `level3` | string | 是 | 三级错误类型 |
| `error_reason` | string | 是 | 错误原因 |
| `degree` | string | 是 | 错误程度 |
| `reference` | string | 是 | 质控依据 |
| `detail_position` | string | 是 | 具体位置描述 |
| `prompted_fields1` | string | 是 | 提示字段编码 |
| `prompted_fields3` | string | 否 | 提示字段类型 |
| `type` | string | 是 | 规则类别 |
| `pos` | string | 是 | 适用业务 |
| `applicableArea` | string | 是 | 适用地区 |
| `default_use` | string | 是 | 默认选用 |
| `remarks` | string | 否 | 备注信息 |
| `in_illustration` | string | 否 | 入参说明 |
| `start_date` | string | 是 | 开始日期 |
| `end_date` | string | 是 | 结束日期 |

### 固定高频字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `yb_code` | string | 否 | 药品编码，逗号分隔 |
| `diag_whole_code` | string | 否 | 完整诊断编码，逗号分隔 |
| `diag_code_prefix` | string | 否 | 诊断编码前缀，逗号分隔 |
| `diag_name_keyword` | string | 否 | 诊断名称关键字，逗号分隔 |
| `fee_whole_code` | string | 否 | 药品/诊疗项目完整编码，逗号分隔 |
| `fee_code_prefix` | string | 否 | 药品/诊疗项目编码前缀，逗号分隔 |

### 扩展字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `extended_fields` | string | 否 | JSON格式的扩展字段，存储特定规则类型的额外字段 |

### 元数据字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `status` | string | 否 | 记录状态：ACTIVE, INACTIVE, DEPRECATED |
| `created_at` | string | 否 | 创建时间，ISO 8601格式 |
| `updated_at` | string | 否 | 更新时间，ISO 8601格式 |

## 使用示例

详见 `api_examples.md` 文档。

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| v1.0.0 | 2025-07-24 | API接口统一，使用标准字段名，完整CRUD操作 |
| v0.9.0 | 2025-07-23 | 字段标准化，三表结构支持 |
| v0.8.0 | 2025-07-22 | 字段映射统一，数据映射引擎重构 |
