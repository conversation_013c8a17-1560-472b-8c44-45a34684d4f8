"""
Health check router for slave node.
"""

from fastapi import APIRouter, Request

from models.api import ApiResponse

# Health check router (no authentication required)
health_router = APIRouter(tags=["Health Check"])


@health_router.get("/", response_model=ApiResponse[dict])
async def read_root(request: Request):
    """
    Root endpoint for slave node health check.

    Args:
        request: HTTP request object

    Returns:
        ApiResponse[dict]: Unified health status response
    """
    request_id = getattr(request.state, "request_id", None)

    health_data = {"status": "healthy", "node_type": "slave", "service": "Rule Validation Service"}

    return ApiResponse.success_response(data=health_data, message="从节点运行正常", request_id=request_id)
