"""
rule_loader.py 单元测试
测试重构后的核心函数功能、边界条件和异常处理
"""

import json
import os
import tempfile
import unittest
from unittest.mock import Mock, patch

from services.rule_detail_service import ServiceError
from services.rule_loader import (
    _check_cache_freshness,
    _create_error_recovery_plan,
    _detect_cache_format,
    _get_cache_performance_report,
    _handle_rule_loading_error,
    _update_cache_stats,
    _validate_field_mapping_configuration,
)


class TestCacheFormatDetection(unittest.TestCase):
    """缓存格式检测单元测试"""

    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        import shutil

        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_detect_v2_format(self):
        """测试v2.0格式检测"""
        test_file = os.path.join(self.temp_dir, "v2_cache.json")
        v2_data = {"version": "v2.0", "templates": [], "details": [], "metadata": {}}

        with open(test_file, "w") as f:
            json.dump(v2_data, f)

        result = _detect_cache_format(test_file)

        self.assertTrue(result["is_valid"])
        self.assertEqual(result["format_version"], "v2.0")
        self.assertEqual(result["format_type"], "sync_service")
        self.assertGreaterEqual(result["detection_confidence"], 0.95)

    def test_detect_v1_5_format(self):
        """测试v1.5格式检测"""
        test_file = os.path.join(self.temp_dir, "v1_5_cache.json")
        v1_5_data = {"version": "v1.5", "rule_datasets": [], "total_count": 0}

        with open(test_file, "w") as f:
            json.dump(v1_5_data, f)

        result = _detect_cache_format(test_file)

        self.assertTrue(result["is_valid"])
        self.assertEqual(result["format_version"], "v1.5")
        self.assertEqual(result["format_type"], "enhanced_dataset")
        self.assertGreaterEqual(result["detection_confidence"], 0.90)

    def test_detect_invalid_format(self):
        """测试无效格式检测"""
        test_file = os.path.join(self.temp_dir, "invalid_cache.json")
        invalid_data = {"unknown_key": "unknown_value"}

        with open(test_file, "w") as f:
            json.dump(invalid_data, f)

        result = _detect_cache_format(test_file)

        self.assertEqual(result["format_version"], "unknown_dict")
        self.assertEqual(result["format_type"], "unknown")
        self.assertLess(result["detection_confidence"], 0.5)

    def test_detect_nonexistent_file(self):
        """测试不存在文件的检测"""
        result = _detect_cache_format("/path/to/nonexistent/file.json")

        self.assertFalse(result["is_valid"])
        self.assertIn("not found", result["error_message"])

    def test_detect_empty_file(self):
        """测试空文件检测"""
        test_file = os.path.join(self.temp_dir, "empty_cache.json")
        open(test_file, "w").close()  # 创建空文件

        result = _detect_cache_format(test_file)

        self.assertFalse(result["is_valid"])
        self.assertIn("empty", result["error_message"])

    def test_detect_corrupted_json(self):
        """测试损坏的JSON文件检测"""
        test_file = os.path.join(self.temp_dir, "corrupted_cache.json")

        with open(test_file, "w") as f:
            f.write('{"invalid": json content}')

        result = _detect_cache_format(test_file)

        self.assertFalse(result["is_valid"])
        self.assertIn("JSON decode error", result["error_message"])


class TestErrorHandling(unittest.TestCase):
    """错误处理单元测试"""

    def test_handle_file_not_found_error(self):
        """测试文件不存在错误处理"""
        error = FileNotFoundError("Cache file not found")
        context = {"file_path": "/test/path", "operation": "file_check"}

        service_error = _handle_rule_loading_error(error, context, "test operation")

        self.assertIsInstance(service_error, ServiceError)
        self.assertEqual(service_error.error_code, "CACHE_FILE_NOT_FOUND")
        self.assertIn("检查缓存文件路径是否正确", service_error.details["recovery_suggestions"])

    def test_handle_json_decode_error(self):
        """测试JSON解析错误处理"""
        error = json.JSONDecodeError("Invalid JSON", "test", 0)
        context = {"file_path": "/test/path"}

        service_error = _handle_rule_loading_error(error, context, "parse JSON")

        self.assertEqual(service_error.error_code, "CACHE_FILE_CORRUPTED")
        self.assertIn("检查缓存文件是否损坏", service_error.details["recovery_suggestions"])

    def test_handle_memory_error(self):
        """测试内存错误处理"""
        error = MemoryError("Out of memory")
        context = {"operation": "load_cache"}

        service_error = _handle_rule_loading_error(error, context, "load data")

        self.assertEqual(service_error.error_code, "INSUFFICIENT_MEMORY")
        self.assertIn("增加系统内存", service_error.details["recovery_suggestions"])

    def test_handle_unknown_error(self):
        """测试未知错误处理"""
        error = RuntimeError("Unknown error")
        context = {"operation": "unknown"}

        service_error = _handle_rule_loading_error(error, context, "unknown operation")

        self.assertEqual(service_error.error_code, "UNKNOWN_ERROR")
        self.assertIn("检查系统日志获取更多信息", service_error.details["recovery_suggestions"])


class TestErrorRecoveryPlan(unittest.TestCase):
    """错误恢复计划单元测试"""

    def test_recovery_plan_file_not_found(self):
        """测试文件不存在的恢复计划"""
        error = ServiceError("File not found", "CACHE_FILE_NOT_FOUND")

        plan = _create_error_recovery_plan(error)

        self.assertTrue(plan["can_recover"])
        self.assertGreater(plan["estimated_success_rate"], 0.5)
        self.assertIn("尝试从备用缓存路径加载", plan["recovery_steps"])
        self.assertFalse(plan["requires_manual_intervention"])

    def test_recovery_plan_corrupted_file(self):
        """测试文件损坏的恢复计划"""
        error = ServiceError("File corrupted", "CACHE_FILE_CORRUPTED")

        plan = _create_error_recovery_plan(error)

        self.assertTrue(plan["can_recover"])
        self.assertIn("尝试使用不同的解析器", plan["recovery_steps"])
        self.assertIn("删除损坏的缓存文件", plan["fallback_options"])

    def test_recovery_plan_memory_error(self):
        """测试内存不足的恢复计划"""
        error = ServiceError("Out of memory", "INSUFFICIENT_MEMORY")

        plan = _create_error_recovery_plan(error)

        self.assertTrue(plan["can_recover"])
        self.assertTrue(plan["requires_manual_intervention"])
        self.assertIn("执行内存清理", plan["recovery_steps"])

    def test_recovery_plan_unknown_error(self):
        """测试未知错误的恢复计划"""
        error = ServiceError("Unknown error", "UNKNOWN_ERROR")

        plan = _create_error_recovery_plan(error)

        self.assertFalse(plan["can_recover"])
        self.assertTrue(plan["requires_manual_intervention"])
        self.assertLess(plan["estimated_success_rate"], 0.5)


class TestPerformanceMonitoring(unittest.TestCase):
    """性能监控单元测试"""

    def test_update_cache_stats(self):
        """测试缓存统计更新"""
        _update_cache_stats(1.5, 20.5, "v2.0_test")

        report = _get_cache_performance_report()

        self.assertIn("avg_load_time", report)
        self.assertIn("avg_memory_usage", report)
        self.assertIn("current_cache_version", report)
        self.assertEqual(report["current_cache_version"], "v2.0_test")

    def test_cache_performance_report_no_data(self):
        """测试无数据时的性能报告"""
        # 清空统计数据
        from services.rule_loader import _cache_stats

        _cache_stats["load_times"].clear()

        report = _get_cache_performance_report()

        self.assertEqual(report["status"], "no_data")
        self.assertIn("message", report)

    def test_cache_freshness_check_nonexistent(self):
        """测试不存在文件的新鲜度检查"""
        with patch("services.rule_loader.LOCAL_RULES_PATH", "/nonexistent/path"):
            result = _check_cache_freshness()

            self.assertFalse(result["cache_exists"])
            self.assertTrue(result["should_reload"])
            self.assertEqual(result["reason"], "cache_not_exists")


class TestFieldMappingValidation(unittest.TestCase):
    """字段映射验证单元测试"""

    @patch("services.rule_loader.UnifiedDataMappingEngine")
    def test_validate_field_mapping_success(self, mock_engine):
        """测试字段映射配置验证成功"""
        mock_instance = Mock()
        mock_instance.get_engine_info.return_value = {
            "field_manager_info": {
                "config_loaded": True,
                "config_version": "v2.0",
                "field_definitions": {
                    "common_fields": {
                        "rule_id": {},
                        "rule_name": {},
                        "level1": {},
                        "level2": {},
                        "level3": {},
                        "error_reason": {},
                    }
                },
            }
        }
        mock_engine.return_value = mock_instance

        result = _validate_field_mapping_configuration()

        self.assertTrue(result)

    @patch("services.rule_loader.UnifiedDataMappingEngine")
    def test_validate_field_mapping_missing_fields(self, mock_engine):
        """测试字段映射配置缺失字段"""
        mock_instance = Mock()
        mock_instance.get_engine_info.return_value = {
            "field_manager_info": {
                "config_loaded": True,
                "field_definitions": {
                    "common_fields": {
                        "rule_id": {},
                        "rule_name": {},
                        # 缺少其他必需字段
                    }
                },
            }
        }
        mock_engine.return_value = mock_instance

        result = _validate_field_mapping_configuration()

        self.assertFalse(result)

    @patch("services.rule_loader.UnifiedDataMappingEngine")
    def test_validate_field_mapping_not_loaded(self, mock_engine):
        """测试字段映射配置未加载"""
        mock_instance = Mock()
        mock_instance.get_engine_info.return_value = {"field_manager_info": {"config_loaded": False}}
        mock_engine.return_value = mock_instance

        result = _validate_field_mapping_configuration()

        self.assertFalse(result)


class TestRuleDetailConversion(unittest.TestCase):
    """规则明细转换单元测试"""

    def setUp(self):
        self.mock_rule_detail = Mock()
        self.mock_rule_detail.rule_detail_id = "test_detail_1"
        self.mock_rule_detail.rule_key = "test_rule"
        self.mock_rule_detail.rule_name = "测试规则"
        self.mock_rule_detail.error_level_1 = "错误"
        self.mock_rule_detail.error_level_2 = "数据"
        self.mock_rule_detail.error_level_3 = "缺失"
        self.mock_rule_detail.error_reason = "必填字段为空"
        self.mock_rule_detail.error_severity = "高"
        self.mock_rule_detail.to_dict.return_value = {
            "rule_detail_id": "test_detail_1",
            "rule_key": "test_rule",
            "rule_name": "测试规则",
            "error_level_1": "错误",
            "error_level_2": "数据",
            "error_level_3": "缺失",
            "error_reason": "必填字段为空",
            "error_severity": "高",
        }

    @patch("services.rule_loader.UnifiedDataMappingEngine")
    def test_convert_rule_detail_success(self, mock_engine):
        """测试规则明细转换成功"""
        from services.rule_loader import _convert_rule_detail_to_cache_format

        mock_instance = Mock()
        mock_instance.normalize_field_names.return_value = {
            "rule_id": "test_detail_1",
            "level1": "错误",
            "level2": "数据",
            "level3": "缺失",
        }
        mock_instance.validate_data.return_value = {"is_valid": True, "errors": []}
        mock_instance.convert_to_structured_format.return_value = {
            "rule_id": "test_detail_1",
            "level1": "错误",
            "level2": "数据",
            "level3": "缺失",
        }
        mock_engine.return_value = mock_instance

        result = _convert_rule_detail_to_cache_format(self.mock_rule_detail)

        self.assertIsNotNone(result)
        self.assertIn("rule_id", result)
        self.assertEqual(result["rule_id"], "test_detail_1")
        mock_instance.normalize_field_names.assert_called_once()
        mock_instance.validate_data.assert_called_once()
        mock_instance.convert_to_structured_format.assert_called_once()

    def test_batch_convert_rule_details(self):
        """测试批量转换规则明细"""
        from services.rule_loader import _batch_convert_rule_details_to_cache_format

        rule_details = [self.mock_rule_detail, self.mock_rule_detail]

        with patch("services.rule_loader.UnifiedDataMappingEngine") as mock_engine:
            mock_instance = Mock()
            mock_instance.batch_normalize_field_names.return_value = [
                {"rule_id": "test_detail_1", "level1": "错误"},
                {"rule_id": "test_detail_1", "level1": "错误"},
            ]
            mock_instance.batch_validate_data.return_value = [
                {"is_valid": True, "errors": []},
                {"is_valid": True, "errors": []},
            ]
            mock_instance.convert_to_structured_format.side_effect = [
                {"rule_id": "test_detail_1", "level1": "错误"},
                {"rule_id": "test_detail_1", "level1": "错误"},
            ]
            mock_engine.return_value = mock_instance

            result = _batch_convert_rule_details_to_cache_format(rule_details)

            self.assertEqual(len(result), 2)
            mock_instance.batch_normalize_field_names.assert_called_once()
            mock_instance.batch_validate_data.assert_called_once()


class TestDatabaseRuleLoading(unittest.TestCase):
    """数据库规则加载单元测试"""

    def setUp(self):
        self.mock_session = Mock()

    @patch("services.rule_loader.RuleTemplate")
    @patch("services.rule_loader.RuleDetail")
    def test_load_rules_from_db(self, mock_rule_detail, mock_rule_template):
        """测试从数据库加载规则"""
        from services.rule_loader import load_rules_from_db

        # 模拟模板查询结果
        mock_template = Mock()
        mock_template.rule_key = "test_rule"
        mock_template.name = "测试规则"

        self.mock_session.query.return_value.filter.return_value.all.return_value = [mock_template]

        # 模拟规则明细加载
        with patch("services.rule_loader._load_rule_details_as_dict") as mock_load_details:
            mock_load_details.return_value = [
                {"rule_id": "detail_1", "level1": "错误"},
                {"rule_id": "detail_2", "level1": "警告"},
            ]

            result = load_rules_from_db(self.mock_session)

            self.assertIn("test_rule", result)
            self.assertEqual(len(result["test_rule"]), 2)
            mock_load_details.assert_called_once_with(self.mock_session, "test_rule")

    @patch("services.rule_loader.RuleDetail")
    def test_load_rule_details_as_dict(self, mock_rule_detail):
        """测试加载规则明细为字典"""
        from services.rule_loader import _load_rule_details_as_dict

        mock_detail = Mock()
        mock_detail.rule_detail_id = "detail_1"
        mock_detail.rule_key = "test_rule"

        self.mock_session.query.return_value.filter.return_value.all.return_value = [mock_detail]

        with patch("services.rule_loader._batch_convert_rule_details_to_cache_format") as mock_convert:
            mock_convert.return_value = [{"rule_id": "detail_1", "level1": "错误"}]

            result = _load_rule_details_as_dict(self.mock_session, "test_rule")

            self.assertEqual(len(result), 1)
            self.assertEqual(result[0]["rule_id"], "detail_1")
            mock_convert.assert_called_once()


class TestAdvancedErrorHandling(unittest.TestCase):
    """高级错误处理单元测试"""

    def test_handle_gzip_error(self):
        """测试gzip错误处理"""
        import gzip

        error = gzip.BadGzipFile("Invalid gzip file")
        context = {"file_path": "/test/path"}

        service_error = _handle_rule_loading_error(error, context, "decompress file")

        self.assertEqual(service_error.error_code, "CACHE_FILE_COMPRESSION_ERROR")
        self.assertIn("检查gzip文件是否完整", service_error.details["recovery_suggestions"])

    def test_handle_database_error(self):
        """测试数据库错误处理"""
        error = Exception("database connection failed")
        context = {"operation": "query_database"}

        service_error = _handle_rule_loading_error(error, context, "database operation")

        self.assertEqual(service_error.error_code, "DATABASE_ERROR")
        self.assertIn("检查数据库连接状态", service_error.details["recovery_suggestions"])

    def test_recovery_plan_compression_error(self):
        """测试压缩错误的恢复计划"""
        error = ServiceError("Compression error", "CACHE_FILE_COMPRESSION_ERROR")

        plan = _create_error_recovery_plan(error)

        self.assertTrue(plan["can_recover"])
        self.assertGreater(plan["estimated_success_rate"], 0.7)
        self.assertIn("尝试非压缩格式读取", plan["recovery_steps"])
        self.assertIn("使用原始JSON格式", plan["fallback_options"])

    def test_recovery_plan_database_error(self):
        """测试数据库错误的恢复计划"""
        error = ServiceError("Database error", "DATABASE_ERROR")

        plan = _create_error_recovery_plan(error, {"database_type": "mysql"})

        self.assertIn("can_recover", plan)
        self.assertIn("recovery_steps", plan)
        self.assertIn("fallback_options", plan)


if __name__ == "__main__":
    unittest.main()
