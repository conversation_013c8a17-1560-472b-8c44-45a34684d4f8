# 数据库字段标准化文档

## 📋 概述

本文档记录了数据库字段标准化的完整实施过程，包括新的三表结构设计、字段映射关系和迁移策略。

## 🎯 标准化目标

1. **统一字段命名**：解决现有数据库字段命名不一致问题
2. **三表架构**：建立清晰的数据结构，支持可扩展性
3. **标准化映射**：与 `field_mapping.json` v3.1.0 配置保持一致
4. **向后兼容**：保留传统表结构，确保平滑迁移

## 🏗️ 新的三表结构

### 1. rule_template 表（规则模板表）

存储规则模板的基本信息。

```sql
CREATE TABLE rule_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_key VARCHAR(100) NOT NULL COMMENT '规则模板类型',
    rule_type VARCHAR(100) NOT NULL COMMENT '规则类型',
    name VARCHAR(500) NOT NULL COMMENT '规则模板名称',
    description TEXT COMMENT '规则模板描述',
    module_path VARCHAR(500) COMMENT 'Python module path',
    file_hash VARCHAR(64) COMMENT 'SHA-256 hash',
    status ENUM('NEW', 'CHANGED', 'READY', 'DEPRECATED') DEFAULT 'NEW',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_rule_template (rule_key, rule_type),
    INDEX idx_rule_template_rule_key (rule_key),
    INDEX idx_rule_template_status (status)
);
```

### 2. rule_detail 表（规则明细表）

存储规则明细数据，**使用标准字段名**。

```sql
CREATE TABLE rule_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_id VARCHAR(100) NOT NULL COMMENT '规则ID',
    rule_key VARCHAR(100) NOT NULL COMMENT '规则模板类型',
    
    -- 通用字段（使用标准命名）
    rule_name VARCHAR(500) NOT NULL COMMENT '规则名称',
    level1 VARCHAR(255) NOT NULL COMMENT '一级错误类型',
    level2 VARCHAR(255) NOT NULL COMMENT '二级错误类型',
    level3 VARCHAR(255) NOT NULL COMMENT '三级错误类型',
    error_reason TEXT NOT NULL COMMENT '错误原因',
    degree VARCHAR(50) NOT NULL COMMENT '错误程度',
    reference TEXT NOT NULL COMMENT '质控依据或参考资料',
    detail_position VARCHAR(100) NOT NULL COMMENT '具体位置描述',
    prompted_fields3 VARCHAR(100) COMMENT '提示字段类型',
    prompted_fields1 VARCHAR(100) NOT NULL COMMENT '提示字段编码',
    type VARCHAR(100) NOT NULL COMMENT '规则类别',
    pos VARCHAR(100) NOT NULL COMMENT '适用业务',
    applicableArea VARCHAR(100) NOT NULL COMMENT '适用地区',
    default_use VARCHAR(50) NOT NULL COMMENT '默认选用',
    remarks TEXT COMMENT '备注信息',
    in_illustration TEXT COMMENT '入参说明',
    start_date VARCHAR(20) NOT NULL COMMENT '开始日期',
    end_date VARCHAR(20) NOT NULL COMMENT '结束日期',
    
    -- 固定的高频字段
    yb_code TEXT COMMENT '药品编码，逗号分隔',
    diag_whole_code TEXT COMMENT '完整诊断编码，逗号分隔',
    diag_code_prefix TEXT COMMENT '诊断编码前缀，逗号分隔',
    diag_name_keyword VARCHAR(200) COMMENT '诊断名称关键字，逗号分隔',
    fee_whole_code TEXT COMMENT '药品/诊疗项目完整编码，逗号分隔',
    fee_code_prefix TEXT COMMENT '药品/诊疗项目编码前缀，逗号分隔',
    
    -- 扩展字段
    extended_fields TEXT COMMENT 'JSON格式的扩展字段',
    
    -- 元数据
    status ENUM('ACTIVE', 'INACTIVE', 'DEPRECATED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_rule_detail_rule_key (rule_key),
    INDEX idx_rule_detail_rule_id (rule_id),
    INDEX idx_rule_detail_status (status)
);
```

### 3. rule_field_metadata 表（字段元数据表）

存储字段的元数据信息，支持动态Excel模板生成和数据校验。

```sql
CREATE TABLE rule_field_metadata (
    id INT PRIMARY KEY AUTO_INCREMENT,
    rule_key VARCHAR(50) NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    field_type ENUM('string', 'integer', 'array', 'boolean') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    is_fixed_field BOOLEAN DEFAULT FALSE COMMENT '是否为固定字段',
    display_name VARCHAR(200) NOT NULL COMMENT '显示名称',
    description TEXT COMMENT '字段描述',
    validation_rule TEXT COMMENT 'JSON格式的校验规则',
    default_value TEXT COMMENT '默认值',
    excel_column_order INT COMMENT 'Excel列顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_rule_field (rule_key, field_name),
    INDEX idx_rule_field_metadata_rule_key (rule_key)
);
```

## 🔄 字段标准化映射

### 核心字段映射表

| 传统字段名 | 标准字段名 | 中文名称 | 数据类型 | 说明 |
|-----------|-----------|----------|----------|------|
| `error_level_1` | `level1` | 一级错误类型 | VARCHAR(255) | 核心标准化 |
| `error_level_2` | `level2` | 二级错误类型 | VARCHAR(255) | 核心标准化 |
| `error_level_3` | `level3` | 三级错误类型 | VARCHAR(255) | 核心标准化 |
| `error_severity` | `degree` | 错误程度 | VARCHAR(50) | 字段名标准化 |
| `quality_basis` | `reference` | 质控依据或参考资料 | TEXT | 字段名标准化 |
| `location_desc` | `detail_position` | 具体位置描述 | VARCHAR(100) | 字段名标准化 |
| `prompt_field_type` | `prompted_fields3` | 提示字段类型 | VARCHAR(100) | 字段名标准化 |
| `prompt_field_code` | `prompted_fields1` | 提示字段编码 | VARCHAR(100) | 字段名标准化 |
| `rule_category` | `type` | 规则类别 | VARCHAR(100) | 字段名标准化 |
| `applicable_business` | `pos` | 适用业务 | VARCHAR(100) | 字段名标准化 |
| `applicable_region` | `applicableArea` | 适用地区 | VARCHAR(100) | 字段名标准化 |
| `default_selected` | `default_use` | 默认选用 | VARCHAR(50) | 字段名标准化 |

### 标准化原则

1. **一致性**：所有字段名与 `field_mapping.json` v3.1.0 保持一致
2. **简洁性**：使用简洁明了的字段名（如 `level1` 而不是 `error_level_1`）
3. **语义性**：字段名能够清晰表达其含义
4. **兼容性**：保留传统表结构，确保平滑迁移

## 📊 数据迁移策略

### 当前状态

- ✅ **新表结构**：三表结构已创建，使用标准字段名
- ✅ **传统表保留**：`rule_details` 表保留，确保兼容性
- ✅ **ORM模型**：新增标准化模型，支持标准字段名
- ✅ **API接口**：提供标准化和传统两套接口

### 迁移建议

1. **新开发项目**：直接使用标准化接口和表结构
2. **现有项目**：逐步迁移到标准化接口
3. **数据同步**：可建立新旧表之间的数据同步机制
4. **性能优化**：根据使用情况优化索引策略

## 🧪 验证测试

### 测试覆盖

- ✅ **数据库表结构测试**：验证三表正确创建
- ✅ **字段标准化测试**：验证所有标准字段名正确使用
- ✅ **ORM模型测试**：验证模型功能和序列化
- ✅ **API接口测试**：验证标准化接口正常工作
- ✅ **数据关联测试**：验证三表关联关系

### 测试文件位置

- `tests/integration/database/test_database_field_standardization.py`
- `tests/integration/api/test_rule_details_standard_api.py`
- `tests/unit/models/test_database_standard_models.py`

## 📈 性能考虑

### 索引策略

1. **rule_template 表**
   - 主键索引：`id`
   - 唯一索引：`(rule_key, rule_type)`
   - 普通索引：`rule_key`, `status`

2. **rule_detail 表**
   - 主键索引：`id`
   - 普通索引：`rule_key`, `rule_id`, `status`
   - 文本索引：`yb_code`（限制长度）

3. **rule_field_metadata 表**
   - 主键索引：`id`
   - 唯一索引：`(rule_key, field_name)`
   - 普通索引：`rule_key`

### 查询优化建议

1. 使用 `rule_key` 进行分区查询
2. 合理使用索引避免全表扫描
3. 对大文本字段进行适当的长度限制
4. 考虑使用缓存机制提高查询性能

## 🔮 未来规划

1. **数据迁移工具**：开发自动化数据迁移工具
2. **监控告警**：建立数据一致性监控机制
3. **性能优化**：根据实际使用情况进行性能调优
4. **扩展支持**：支持更多规则类型和字段类型

---

**文档版本**：v1.0  
**最后更新**：2025-01-24  
**维护人员**：开发团队
