<template>
  <div class="file-upload-area" data-testid="upload-area">
    <!-- 文件上传区域 -->
    <el-upload
      ref="uploadRef"
      :limit="1"
      :auto-upload="false"
      :on-exceed="handleExceed"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      accept=".xlsx, .xls"
      drag
      class="upload-dragger"
      data-testid="file-input"
    >
      <el-icon class="upload-icon"><Upload /></el-icon>
      <div class="upload-text">
        将文件拖到此处，或<em>点击上传</em>
      </div>
      <template #tip>
        <div class="upload-tip">
          请上传 .xlsx 或 .xls 格式的文件。文件解析和校验将在您的浏览器中完成。
          <br>
          <span class="file-size-tip">文件大小限制：10MB</span>
        </div>
      </template>
    </el-upload>

    <!-- 文件信息和操作 -->
    <div v-if="selectedFile" class="file-info-section">
      <el-card class="file-info-card" shadow="never">
        <div class="file-info">
          <div class="file-details">
            <el-icon class="file-icon"><Document /></el-icon>
            <div class="file-meta">
              <div class="file-name">{{ selectedFile.name }}</div>
              <div class="file-size">{{ formatFileSize(selectedFile.size) }}</div>
            </div>
          </div>
          <div class="file-status">
            <el-tag v-if="fileStatus === 'ready'" type="info">待解析</el-tag>
            <el-tag v-else-if="fileStatus === 'processing'" type="warning">解析中</el-tag>
            <el-tag v-else-if="fileStatus === 'success'" type="success">解析完成</el-tag>
            <el-tag v-else-if="fileStatus === 'error'" type="danger">解析失败</el-tag>
          </div>
        </div>

        <div class="file-actions">
          <el-button
            type="primary"
            @click="handleProcessFile"
            :loading="processing"
            :disabled="fileStatus === 'processing'"
          >
            <el-icon><DocumentChecked /></el-icon>
            解析文件
          </el-button>
          <el-button @click="handleClearFile">
            <el-icon><Delete /></el-icon>
            重新选择
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 处理进度 -->
    <div v-if="processing" class="processing-section">
      <el-card class="processing-card" shadow="never">
        <div class="processing-content">
          <el-icon class="processing-icon"><Loading /></el-icon>
          <div class="processing-text">
            <div class="processing-title">正在解析文件...</div>
            <div class="processing-desc">请稍候，系统正在解析Excel文件并进行数据校验</div>
          </div>
        </div>
        <el-progress
          :percentage="processingProgress"
          :show-text="false"
          class="processing-progress"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Document, DocumentChecked, Delete, Loading } from '@element-plus/icons-vue'

const props = defineProps({
  selectedFile: {
    type: Object,
    default: null
  },
  processing: {
    type: Boolean,
    default: false
  },
  fileStatus: {
    type: String,
    default: 'ready' // ready, processing, success, error
  }
})

const emit = defineEmits(['file-change', 'file-clear', 'process-file'])

// 组件引用
const uploadRef = ref()

// 处理进度（模拟）
const processingProgress = ref(0)

// 数据验证函数
const validateFileType = (file) => {
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv'
  ]
  return allowedTypes.includes(file.type) || file.name.match(/\.(xlsx|xls|csv)$/i)
}

const validateFileSize = (file) => {
  const maxSize = 10 * 1024 * 1024 // 10MB
  return file.size <= maxSize
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 文件处理函数
const handleExceed = (files) => {
  uploadRef.value.clearFiles()
  const file = files[0]
  file.uid = Date.now() + Math.random()
  uploadRef.value.handleStart(file)

  // 直接处理文件，不通过 handleFileChange
  if (!validateFileType(file)) {
    ElMessage.error('文件类型不支持，请上传 Excel 文件 (.xlsx, .xls)')
    uploadRef.value?.clearFiles()
    return
  }

  if (!validateFileSize(file)) {
    ElMessage.error('文件大小不能超过 10MB')
    uploadRef.value?.clearFiles()
    return
  }

  emit('file-change', file)
  ElMessage.success('文件选择成功，请点击解析按钮开始处理')
}

const handleFileChange = (uploadFile, uploadFiles) => {
  if (uploadFiles && uploadFiles.length > 1) {
    uploadFiles.splice(0, 1)
  }

  const file = uploadFile.raw || uploadFile

  // 数据验证
  if (!validateFileType(file)) {
    ElMessage.error('文件类型不支持，请上传 Excel 文件 (.xlsx, .xls)')
    uploadRef.value?.clearFiles()
    return
  }

  if (!validateFileSize(file)) {
    ElMessage.error('文件大小不能超过 10MB')
    uploadRef.value?.clearFiles()
    return
  }

  emit('file-change', file)
  ElMessage.success('文件选择成功，请点击解析按钮开始处理')
}

const handleFileRemove = () => {
  emit('file-clear')
}

const handleClearFile = () => {
  uploadRef.value?.clearFiles()
  emit('file-clear')
}

const handleProcessFile = () => {
  if (!props.selectedFile) {
    ElMessage.error('请先选择文件')
    return
  }

  // 模拟处理进度
  processingProgress.value = 0
  const progressInterval = setInterval(() => {
    if (processingProgress.value < 90) {
      processingProgress.value += 10
    }
  }, 200)

  emit('process-file', props.selectedFile)

  // 清理进度模拟
  setTimeout(() => {
    clearInterval(progressInterval)
    processingProgress.value = 100
  }, 2000)
}
</script>

<style scoped>
.file-upload-area {
  margin-bottom: 24px;
}

.upload-dragger {
  width: 100%;
}

.upload-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.upload-tip {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.file-size-tip {
  color: var(--el-color-warning);
  font-weight: 500;
}

/* 文件信息区域 */
.file-info-section {
  margin-top: 20px;
}

.file-info-card {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.file-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.file-size {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.file-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 处理进度区域 */
.processing-section {
  margin-top: 20px;
}

.processing-card {
  border: 1px solid var(--el-color-primary-light-7);
  border-radius: 8px;
  background-color: var(--el-color-primary-light-9);
}

.processing-content {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.processing-icon {
  font-size: 24px;
  color: var(--el-color-primary);
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.processing-text {
  flex: 1;
}

.processing-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.processing-desc {
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

.processing-progress {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .file-actions {
    width: 100%;
    justify-content: stretch;
  }

  .file-actions .el-button {
    flex: 1;
  }

  .processing-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}

@media (max-width: 576px) {
  .upload-icon {
    font-size: 36px;
  }

  .upload-text {
    font-size: 14px;
  }

  .file-actions {
    flex-direction: column;
  }
}
</style>
