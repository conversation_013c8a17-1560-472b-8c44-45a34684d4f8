/**
 * 模态框管理Composable
 * 提供统一的模态框状态管理和操作方法
 */
import { ref, computed, reactive, nextTick } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'

/**
 * 基础模态框功能
 */
export function useModal(options = {}) {
  const {
    defaultVisible = false,
    destroyOnClose = false,
    maskClosable = true,
    keyboard = true
  } = options

  // 模态框状态
  const visible = ref(defaultVisible)
  const loading = ref(false)
  const confirmLoading = ref(false)

  // 模态框配置
  const config = reactive({
    title: '',
    width: '50%',
    top: '15vh',
    destroyOnClose,
    maskClosable,
    keyboard,
    ...options
  })

  // 计算属性
  const isVisible = computed(() => visible.value)
  const isLoading = computed(() => loading.value || confirmLoading.value)

  // 显示模态框
  const show = (modalConfig = {}) => {
    Object.assign(config, modalConfig)
    visible.value = true
  }

  // 隐藏模态框
  const hide = () => {
    visible.value = false
    if (destroyOnClose) {
      nextTick(() => {
        resetConfig()
      })
    }
  }

  // 切换显示状态
  const toggle = () => {
    if (visible.value) {
      hide()
    } else {
      show()
    }
  }

  // 确认操作
  const confirm = async (handler) => {
    if (!handler || typeof handler !== 'function') {
      hide()
      return
    }

    confirmLoading.value = true
    try {
      const result = await handler()
      if (result !== false) {
        hide()
      }
      return result
    } catch (error) {
      console.error('模态框确认操作失败:', error)
      throw error
    } finally {
      confirmLoading.value = false
    }
  }

  // 取消操作
  const cancel = (handler) => {
    if (handler && typeof handler === 'function') {
      handler()
    }
    hide()
  }

  // 重置配置
  const resetConfig = () => {
    Object.assign(config, {
      title: '',
      width: '50%',
      top: '15vh'
    })
  }

  // 设置加载状态
  const setLoading = (state) => {
    loading.value = state
  }

  return {
    // 响应式数据
    visible,
    loading,
    confirmLoading,
    config,
    isVisible,
    isLoading,

    // 方法
    show,
    hide,
    toggle,
    confirm,
    cancel,
    resetConfig,
    setLoading
  }
}

/**
 * 多模态框管理
 * 管理多个模态框的显示状态
 */
export function useMultiModal() {
  const modals = reactive({})
  const activeModal = ref(null)

  // 注册模态框
  const register = (name, modalOptions = {}) => {
    modals[name] = useModal(modalOptions)
    return modals[name]
  }

  // 获取模态框
  const getModal = (name) => {
    return modals[name]
  }

  // 显示指定模态框
  const show = (name, config = {}) => {
    const modal = modals[name]
    if (modal) {
      // 隐藏其他模态框
      hideAll()
      modal.show(config)
      activeModal.value = name
    }
  }

  // 隐藏指定模态框
  const hide = (name) => {
    const modal = modals[name]
    if (modal) {
      modal.hide()
      if (activeModal.value === name) {
        activeModal.value = null
      }
    }
  }

  // 隐藏所有模态框
  const hideAll = () => {
    Object.values(modals).forEach(modal => modal.hide())
    activeModal.value = null
  }

  // 检查是否有模态框显示
  const hasVisible = computed(() => {
    return Object.values(modals).some(modal => modal.visible.value)
  })

  return {
    modals,
    activeModal,
    hasVisible,
    register,
    getModal,
    show,
    hide,
    hideAll
  }
}

/**
 * 确认对话框
 */
export function useConfirm() {
  // 基础确认对话框
  const confirm = async (message, title = '确认', options = {}) => {
    const defaultOptions = {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
      ...options
    }

    try {
      await ElMessageBox.confirm(message, title, defaultOptions)
      return true
    } catch {
      return false
    }
  }

  // 删除确认
  const confirmDelete = async (itemName = '该项', options = {}) => {
    const message = `确定要删除 ${itemName} 吗？此操作不可恢复。`
    return confirm(message, '删除确认', {
      type: 'error',
      confirmButtonText: '删除',
      confirmButtonClass: 'el-button--danger',
      ...options
    })
  }

  // 保存确认
  const confirmSave = async (message = '确定要保存当前修改吗？', options = {}) => {
    return confirm(message, '保存确认', {
      type: 'info',
      confirmButtonText: '保存',
      ...options
    })
  }

  // 离开确认
  const confirmLeave = async (message = '有未保存的修改，确定要离开吗？', options = {}) => {
    return confirm(message, '离开确认', {
      type: 'warning',
      confirmButtonText: '离开',
      cancelButtonText: '继续编辑',
      ...options
    })
  }

  return {
    confirm,
    confirmDelete,
    confirmSave,
    confirmLeave
  }
}

/**
 * 消息提示
 */
export function useMessage() {
  // 成功消息
  const success = (message, options = {}) => {
    ElMessage.success({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }

  // 错误消息
  const error = (message, options = {}) => {
    ElMessage.error({
      message,
      duration: 5000,
      showClose: true,
      ...options
    })
  }

  // 警告消息
  const warning = (message, options = {}) => {
    ElMessage.warning({
      message,
      duration: 4000,
      showClose: true,
      ...options
    })
  }

  // 信息消息
  const info = (message, options = {}) => {
    ElMessage.info({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }

  // 加载消息
  const loading = (message = '加载中...', options = {}) => {
    return ElMessage({
      message,
      type: 'info',
      duration: 0,
      showClose: false,
      ...options
    })
  }

  return {
    success,
    error,
    warning,
    info,
    loading
  }
}

/**
 * 抽屉管理
 */
export function useDrawer(options = {}) {
  const {
    defaultVisible = false,
    direction = 'rtl',
    size = '30%',
    destroyOnClose = false
  } = options

  const visible = ref(defaultVisible)
  const loading = ref(false)

  const config = reactive({
    title: '',
    direction,
    size,
    destroyOnClose,
    ...options
  })

  const show = (drawerConfig = {}) => {
    Object.assign(config, drawerConfig)
    visible.value = true
  }

  const hide = () => {
    visible.value = false
  }

  const toggle = () => {
    visible.value = !visible.value
  }

  const setLoading = (state) => {
    loading.value = state
  }

  return {
    visible,
    loading,
    config,
    show,
    hide,
    toggle,
    setLoading
  }
}
