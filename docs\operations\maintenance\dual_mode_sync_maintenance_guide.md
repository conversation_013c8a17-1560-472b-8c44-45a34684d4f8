# 双模式同步运维手册

## 概述

本手册提供双模式同步机制的日常运维指导，包括监控、维护、故障排除和性能优化等操作指南。

## 📊 监控指标

### 1. 同步状态监控

#### 关键指标
- **同步成功率**: 应保持在95%以上
- **同步延迟**: 增量同步应在30秒内完成
- **网络分区检测**: 监控连续失败次数
- **缓存命中率**: 应保持在80%以上

#### 监控命令
```bash
# 查看同步状态
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/status?node_id=slave_001

# 查看同步指标
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/metrics?time_range=1h

# 查看缓存统计
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/cache/stats
```

### 2. 离线包监控

#### 关键指标
- **包生成成功率**: 应保持在98%以上
- **包存储使用率**: 建议不超过80%
- **过期包数量**: 定期清理过期包
- **包完整性**: 校验失败率应为0

#### 监控命令
```bash
# 查看包列表
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/packages

# 查看存储统计
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/storage/stats

# 检查过期包
curl -H "X-API-KEY: your_key" \
  "http://master-ip:18001/api/v1/rules/offline/packages?status=expired"
```

### 3. 系统资源监控

#### 监控脚本
```bash
#!/bin/bash
# sync_monitor.sh - 双模式同步监控脚本

API_KEY="your_api_key_here"
MASTER_URL="http://master-ip:18001"
SLAVE_URL="http://slave-ip:18002"

# 检查同步状态
check_sync_status() {
    local response=$(curl -s -H "X-API-KEY: $API_KEY" \
        "$SLAVE_URL/api/v1/rules/sync/status?node_id=slave_001")
    
    local success_rate=$(echo $response | jq -r '.data.successful_syncs / .data.total_syncs')
    local is_syncing=$(echo $response | jq -r '.data.is_syncing')
    
    if (( $(echo "$success_rate < 0.95" | bc -l) )); then
        echo "WARNING: 同步成功率低于95%: $success_rate"
    fi
    
    if [ "$is_syncing" = "true" ]; then
        echo "INFO: 正在进行同步操作"
    fi
}

# 检查存储空间
check_storage() {
    local response=$(curl -s -H "X-API-KEY: $API_KEY" \
        "$MASTER_URL/api/v1/rules/offline/storage/stats")
    
    local usage=$(echo $response | jq -r '.data.usage_percentage')
    
    if (( $(echo "$usage > 80" | bc -l) )); then
        echo "WARNING: 存储使用率超过80%: $usage%"
    fi
}

# 执行检查
check_sync_status
check_storage
```

## 🔧 日常维护

### 1. 缓存维护

#### 缓存清理
```bash
# 清理版本缓存
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"cache_types": ["version"], "node_id": "slave_001"}' \
  http://slave-ip:18002/api/v1/rules/sync/cache/clear

# 清理所有缓存
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"cache_types": ["version", "changes", "package"], "node_id": "slave_001"}' \
  http://slave-ip:18002/api/v1/rules/sync/cache/clear
```

#### 缓存优化
```bash
# 查看缓存性能
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/cache/stats

# 根据命中率调整缓存大小
# 编辑配置文件调整以下参数：
# CACHE_VERSION_MAX_SIZE=20000  # 增加版本缓存大小
# CACHE_CHANGES_MAX_SIZE=2000   # 增加变更缓存大小
```

### 2. 离线包维护

#### 定期清理
```bash
# 清理过期包
curl -X POST -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/cleanup

# 手动删除特定包
curl -X DELETE -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/packages/package_id
```

#### 包完整性检查
```bash
#!/bin/bash
# check_package_integrity.sh - 包完整性检查脚本

API_KEY="your_api_key_here"
MASTER_URL="http://master-ip:18001"

# 获取所有包列表
packages=$(curl -s -H "X-API-KEY: $API_KEY" \
    "$MASTER_URL/api/v1/rules/offline/packages" | jq -r '.data[].package_id')

# 检查每个包的完整性
for package_id in $packages; do
    echo "检查包: $package_id"
    
    # 下载包并验证
    curl -H "X-API-KEY: $API_KEY" \
        "$MASTER_URL/api/v1/rules/offline/download/$package_id" \
        -o "/tmp/$package_id.gz"
    
    # 验证下载的包
    if [ -f "/tmp/$package_id.gz" ]; then
        echo "包 $package_id 下载成功"
        rm "/tmp/$package_id.gz"
    else
        echo "ERROR: 包 $package_id 下载失败"
    fi
done
```

### 3. 同步维护

#### 强制全量同步
```bash
# 强制全量同步
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{
    "node_id": "slave_001",
    "sync_mode": "full",
    "force_full_sync": true
  }' \
  http://master-ip:18001/api/v1/rules/sync/request
```

#### 同步状态重置
```bash
# 重置同步状态（需要重启服务）
docker-compose restart slave

# 或者清理缓存后重新同步
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"cache_types": ["version", "changes"], "node_id": "slave_001"}' \
  http://slave-ip:18002/api/v1/rules/sync/cache/clear
```

## 🚨 故障排除

### 1. 同步失败

#### 症状
- 同步成功率低于95%
- 从节点数据不是最新版本
- 同步请求超时

#### 排查步骤
```bash
# 1. 检查网络连通性
ping master-ip
telnet master-ip 18001

# 2. 检查API密钥
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/sync/status?node_id=test

# 3. 查看同步日志
docker logs rule-validation-slave | grep "sync" | tail -50

# 4. 检查同步状态
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/status?node_id=slave_001
```

#### 解决方案
```bash
# 1. 重置同步状态
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"cache_types": ["version", "changes"], "node_id": "slave_001"}' \
  http://slave-ip:18002/api/v1/rules/sync/cache/clear

# 2. 强制全量同步
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{
    "node_id": "slave_001",
    "sync_mode": "full",
    "force_full_sync": true
  }' \
  http://master-ip:18001/api/v1/rules/sync/request

# 3. 重启服务
docker-compose restart slave
```

### 2. 离线包问题

#### 症状
- 包生成失败
- 包下载失败
- 包校验失败

#### 排查步骤
```bash
# 1. 检查存储空间
df -h /data/offline_packages

# 2. 检查包状态
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/packages/package_id

# 3. 查看生成日志
docker logs rule-validation-master | grep "offline" | tail -50

# 4. 检查存储统计
curl -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/storage/stats
```

#### 解决方案
```bash
# 1. 清理存储空间
curl -X POST -H "X-API-KEY: your_key" \
  http://master-ip:18001/api/v1/rules/offline/cleanup

# 2. 重新生成包
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{
    "package_name": "recovery_package",
    "compression_level": 6,
    "expiry_days": 30
  }' \
  http://master-ip:18001/api/v1/rules/offline/generate

# 3. 检查文件权限
ls -la /data/offline_packages
chown -R app:app /data/offline_packages
```

### 3. 缓存问题

#### 症状
- 缓存命中率低
- 内存使用过高
- 响应时间慢

#### 排查步骤
```bash
# 1. 检查缓存统计
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/cache/stats

# 2. 检查内存使用
docker stats rule-validation-slave

# 3. 查看缓存配置
docker exec rule-validation-slave env | grep CACHE
```

#### 解决方案
```bash
# 1. 调整缓存配置
# 编辑 .env 文件
CACHE_VERSION_MAX_SIZE=20000
CACHE_VERSION_MAX_MEMORY_MB=40
CACHE_CHANGES_MAX_SIZE=2000

# 2. 清理缓存
curl -X POST -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"cache_types": ["version", "changes", "package"]}' \
  http://slave-ip:18002/api/v1/rules/sync/cache/clear

# 3. 重启服务
docker-compose restart slave
```

## 📈 性能优化

### 1. 同步性能优化

#### 配置调优
```bash
# 增量同步优化
SYNC_INCREMENTAL_ENABLED=true
SYNC_COMPRESSION_LEVEL=9          # 高压缩比
SYNC_VERSION_CACHE_TTL=86400      # 延长缓存时间

# 网络优化
SYNC_STATUS_CHECK_INTERVAL=10.0   # 减少检查频率
SYNC_AUTO_RECOVERY_ENABLED=true   # 启用自动恢复
```

#### 监控优化效果
```bash
# 监控同步性能
curl -H "X-API-KEY: your_key" \
  http://slave-ip:18002/api/v1/rules/sync/metrics?time_range=24h
```

### 2. 缓存性能优化

#### 配置调优
```bash
# 缓存大小优化
CACHE_VERSION_MAX_SIZE=50000      # 增加版本缓存
CACHE_VERSION_MAX_MEMORY_MB=100   # 增加内存限制
CACHE_CHANGES_MAX_SIZE=5000       # 增加变更缓存

# 清理频率优化
CACHE_VERSION_CLEANUP_INTERVAL=7200   # 2小时清理一次
CACHE_PERFORMANCE_MONITORING_ENABLED=true  # 启用性能监控
```

### 3. 存储性能优化

#### 配置调优
```bash
# 离线包优化
OFFLINE_PACKAGE_MAX_SIZE_MB=500           # 增加包大小限制
OFFLINE_PACKAGE_CLEANUP_INTERVAL=3600    # 1小时清理一次
OFFLINE_PACKAGE_INTEGRITY_CHECK_ENABLED=true  # 启用完整性检查
```

## 📋 维护检查清单

### 日常检查（每日）
- [ ] 检查同步状态和成功率
- [ ] 检查系统资源使用情况
- [ ] 检查错误日志
- [ ] 验证服务可用性

### 周期检查（每周）
- [ ] 清理过期离线包
- [ ] 检查缓存性能统计
- [ ] 更新监控脚本
- [ ] 备份配置文件

### 月度检查（每月）
- [ ] 性能基准测试
- [ ] 配置参数优化
- [ ] 存储空间规划
- [ ] 安全审计检查

## 📞 应急联系

### 故障升级流程
1. **L1支持**: 基础故障排除和重启服务
2. **L2支持**: 配置调优和性能分析
3. **L3支持**: 代码级问题和架构调整

### 关键联系信息
- **运维团队**: <EMAIL>
- **开发团队**: <EMAIL>
- **紧急联系**: <EMAIL>

---

**文档版本**: v1.0  
**更新时间**: 2025-08-02  
**适用版本**: 双模式同步机制 v1.0+
