<template>
  <div class="search-box">
    <el-input
      v-model="searchValue"
      :placeholder="placeholder"
      :size="size"
      :clearable="clearable"
      :prefix-icon="Search"
      class="search-input"
      @input="handleInput"
      @clear="handleClear"
      @keyup.enter="handleEnter"
    >
      <template #append v-if="showSearchButton">
        <el-button
          :icon="Search"
          @click="handleSearch"
          :loading="loading"
        />
      </template>
    </el-input>

    <!-- 搜索历史下拉 -->
    <div
      v-if="showHistory && searchHistory.length > 0 && showHistoryDropdown"
      class="search-history"
    >
      <div class="history-header">
        <span class="history-title">搜索历史</span>
        <el-button
          text
          size="small"
          @click="clearHistory"
          class="clear-history-btn"
        >
          清空
        </el-button>
      </div>
      <div class="history-list">
        <div
          v-for="(item, index) in searchHistory"
          :key="index"
          class="history-item"
          @click="selectHistoryItem(item)"
        >
          <el-icon class="history-icon"><Clock /></el-icon>
          <span class="history-text">{{ item }}</span>
          <el-button
            text
            size="small"
            :icon="Close"
            @click.stop="removeHistoryItem(index)"
            class="remove-btn"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { Search, Clock, Close } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入搜索关键词'
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['large', 'default', 'small'].includes(value)
  },
  clearable: {
    type: Boolean,
    default: true
  },
  showSearchButton: {
    type: Boolean,
    default: false
  },
  showHistory: {
    type: Boolean,
    default: true
  },
  maxHistoryItems: {
    type: Number,
    default: 10
  },
  debounceDelay: {
    type: Number,
    default: 300
  },
  loading: {
    type: Boolean,
    default: false
  },
  historyStorageKey: {
    type: String,
    default: 'search-history'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'search', 'clear', 'input'])

// 响应式状态
const searchValue = ref(props.modelValue)
const showHistoryDropdown = ref(false)
const searchHistory = ref([])

// 计算属性
const trimmedValue = computed(() => searchValue.value.trim())

// 防抖搜索函数
const debouncedSearch = debounce(() => {
  if (trimmedValue.value) {
    handleSearch()
  }
}, props.debounceDelay)

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  searchValue.value = newValue
})

// 监听搜索值变化
watch(searchValue, (newValue) => {
  emit('update:modelValue', newValue)
  emit('input', newValue)

  // 显示/隐藏历史记录
  if (props.showHistory) {
    showHistoryDropdown.value = newValue.length === 0 && searchHistory.value.length > 0
  }
})

// 处理输入
const handleInput = (value) => {
  // 实时搜索（防抖）
  if (props.debounceDelay > 0) {
    debouncedSearch()
  }
}

// 处理搜索
const handleSearch = () => {
  const keyword = trimmedValue.value
  if (keyword) {
    // 添加到搜索历史
    addToHistory(keyword)
    // 隐藏历史下拉
    showHistoryDropdown.value = false
    // 触发搜索事件
    emit('search', keyword)
  }
}

// 处理清空
const handleClear = () => {
  searchValue.value = ''
  showHistoryDropdown.value = props.showHistory && searchHistory.value.length > 0
  emit('clear')
}

// 处理回车
const handleEnter = () => {
  handleSearch()
}

// 添加到搜索历史
const addToHistory = (keyword) => {
  if (!props.showHistory || !keyword) return

  // 移除重复项
  const index = searchHistory.value.indexOf(keyword)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }

  // 添加到开头
  searchHistory.value.unshift(keyword)

  // 限制历史记录数量
  if (searchHistory.value.length > props.maxHistoryItems) {
    searchHistory.value = searchHistory.value.slice(0, props.maxHistoryItems)
  }

  // 保存到本地存储
  saveHistoryToStorage()
}

// 选择历史记录项
const selectHistoryItem = (item) => {
  searchValue.value = item
  showHistoryDropdown.value = false
  handleSearch()
}

// 移除历史记录项
const removeHistoryItem = (index) => {
  searchHistory.value.splice(index, 1)
  saveHistoryToStorage()

  // 如果历史记录为空，隐藏下拉
  if (searchHistory.value.length === 0) {
    showHistoryDropdown.value = false
  }
}

// 清空搜索历史
const clearHistory = () => {
  searchHistory.value = []
  showHistoryDropdown.value = false
  saveHistoryToStorage()
}

// 保存历史记录到本地存储
const saveHistoryToStorage = () => {
  try {
    localStorage.setItem(props.historyStorageKey, JSON.stringify(searchHistory.value))
  } catch (error) {
    console.warn('保存搜索历史失败:', error)
  }
}

// 从本地存储加载历史记录
const loadHistoryFromStorage = () => {
  try {
    const stored = localStorage.getItem(props.historyStorageKey)
    if (stored) {
      searchHistory.value = JSON.parse(stored)
    }
  } catch (error) {
    console.warn('加载搜索历史失败:', error)
    searchHistory.value = []
  }
}

// 处理点击外部区域
const handleClickOutside = (event) => {
  const searchBox = event.target.closest('.search-box')
  if (!searchBox) {
    showHistoryDropdown.value = false
  }
}

// 处理输入框获得焦点
const handleFocus = () => {
  if (props.showHistory && searchHistory.value.length > 0 && !trimmedValue.value) {
    showHistoryDropdown.value = true
  }
}

// 组件挂载时加载历史记录
onMounted(() => {
  if (props.showHistory) {
    loadHistoryFromStorage()
  }

  // 添加全局点击事件监听
  document.addEventListener('click', handleClickOutside)

  // 添加输入框焦点事件
  const inputElement = document.querySelector('.search-input input')
  if (inputElement) {
    inputElement.addEventListener('focus', handleFocus)
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)

  const inputElement = document.querySelector('.search-input input')
  if (inputElement) {
    inputElement.removeEventListener('focus', handleFocus)
  }
})
</script>

<style scoped>
.search-box {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.search-input {
  width: 100%;
}

.search-history {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.history-title {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.clear-history-btn {
  font-size: 12px;
  padding: 0;
  height: auto;
}

.history-list {
  max-height: 240px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background-color: #f5f7fa;
}

.history-icon {
  color: #c0c4cc;
  margin-right: 8px;
  font-size: 14px;
}

.history-text {
  flex: 1;
  font-size: 14px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-btn {
  opacity: 0;
  transition: opacity 0.2s;
  padding: 0;
  margin-left: 8px;
}

.history-item:hover .remove-btn {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-box {
    max-width: 100%;
  }
}
</style>
