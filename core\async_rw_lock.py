"""
异步读写锁实现模块
用于解决主节点索引加载器的并发控制问题

实现特点：
- 支持多个读者并发访问
- 写者独占访问，阻塞所有读者和其他写者
- 避免了threading.RLock在异步环境中的死锁风险
- 支持超时和优雅降级
"""

import asyncio
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass

from core.logging.logging_system import log as logger


@dataclass
class LockMetrics:
    """锁性能指标"""
    read_acquisitions: int = 0
    write_acquisitions: int = 0
    read_wait_time_ms: float = 0.0
    write_wait_time_ms: float = 0.0
    max_concurrent_readers: int = 0
    total_contention_events: int = 0

    def reset(self):
        """重置所有指标"""
        self.read_acquisitions = 0
        self.write_acquisitions = 0
        self.read_wait_time_ms = 0.0
        self.write_wait_time_ms = 0.0
        self.max_concurrent_readers = 0
        self.total_contention_events = 0


class AsyncRWLock:
    """
    异步读写锁实现

    支持以下特性：
    1. 多读者并发访问
    2. 写者独占访问
    3. 公平性：避免写者饥饿
    4. 超时机制：防止无限等待
    5. 性能监控：收集锁使用指标
    """

    def __init__(self, timeout_seconds: float = 30.0):
        """
        初始化异步读写锁

        Args:
            timeout_seconds: 默认超时时间（秒）
        """
        self._readers = 0  # 当前读者数量
        self._writers = 0  # 当前写者数量（0或1）
        self._waiting_writers = 0  # 等待中的写者数量

        # 条件变量用于协调读写者
        self._condition = asyncio.Condition()

        # 配置和状态
        self._default_timeout = timeout_seconds
        self._metrics = LockMetrics()

        logger.debug("异步读写锁初始化完成")

    @property
    def metrics(self) -> LockMetrics:
        """获取锁性能指标"""
        return self._metrics

    def reset_metrics(self):
        """重置性能指标"""
        self._metrics.reset()

    @asynccontextmanager
    async def read_lock(self, timeout: float | None = None):
        """
        异步上下文管理器：获取读锁

        Args:
            timeout: 超时时间，默认使用构造函数中的值

        Usage:
            async with lock.read_lock():
                # 读操作
                pass
        """
        timeout = timeout or self._default_timeout
        start_time = time.perf_counter()

        try:
            # 获取读锁
            await self._acquire_read_lock(timeout)

            # 更新指标
            wait_time = (time.perf_counter() - start_time) * 1000
            self._metrics.read_acquisitions += 1
            self._metrics.read_wait_time_ms += wait_time
            self._metrics.max_concurrent_readers = max(
                self._metrics.max_concurrent_readers, self._readers
            )

            yield

        finally:
            # 释放读锁
            await self._release_read_lock()

    @asynccontextmanager
    async def write_lock(self, timeout: float | None = None):
        """
        异步上下文管理器：获取写锁

        Args:
            timeout: 超时时间，默认使用构造函数中的值

        Usage:
            async with lock.write_lock():
                # 写操作
                pass
        """
        timeout = timeout or self._default_timeout
        start_time = time.perf_counter()

        try:
            # 获取写锁
            await self._acquire_write_lock(timeout)

            # 更新指标
            wait_time = (time.perf_counter() - start_time) * 1000
            self._metrics.write_acquisitions += 1
            self._metrics.write_wait_time_ms += wait_time

            yield

        finally:
            # 释放写锁
            await self._release_write_lock()

    async def _acquire_read_lock(self, timeout: float):
        """获取读锁"""
        async with self._condition:
            # 等待条件：没有写者且没有等待的写者（公平性）
            try:
                await asyncio.wait_for(
                    self._condition.wait_for(
                        lambda: self._writers == 0 and self._waiting_writers == 0
                    ),
                    timeout=timeout
                )
                self._readers += 1
                logger.debug(f"获取读锁成功，当前读者数量: {self._readers}")

            except TimeoutError as e:
                self._metrics.total_contention_events += 1
                logger.warning(f"获取读锁超时 ({timeout}秒)")
                raise TimeoutError("获取读锁超时") from e

    async def _release_read_lock(self):
        """释放读锁"""
        async with self._condition:
            self._readers -= 1
            logger.debug(f"释放读锁，当前读者数量: {self._readers}")

            # 如果没有读者了，通知等待的写者
            if self._readers == 0:
                self._condition.notify_all()

    async def _acquire_write_lock(self, timeout: float):
        """获取写锁"""
        async with self._condition:
            self._waiting_writers += 1

            try:
                # 等待条件：没有读者且没有写者
                await asyncio.wait_for(
                    self._condition.wait_for(
                        lambda: self._readers == 0 and self._writers == 0
                    ),
                    timeout=timeout
                )

                self._waiting_writers -= 1
                self._writers = 1
                logger.debug("获取写锁成功")

            except TimeoutError as e:
                self._waiting_writers -= 1
                self._metrics.total_contention_events += 1
                logger.warning(f"获取写锁超时 ({timeout}秒)")
                raise TimeoutError("获取写锁超时") from e

    async def _release_write_lock(self):
        """释放写锁"""
        async with self._condition:
            self._writers = 0
            logger.debug("释放写锁")

            # 通知所有等待者（读者和写者）
            self._condition.notify_all()

    def get_status(self) -> dict[str, any]:
        """获取锁状态信息"""
        return {
            "readers": self._readers,
            "writers": self._writers,
            "waiting_writers": self._waiting_writers,
            "metrics": {
                "read_acquisitions": self._metrics.read_acquisitions,
                "write_acquisitions": self._metrics.write_acquisitions,
                "avg_read_wait_ms": (
                    self._metrics.read_wait_time_ms / max(1, self._metrics.read_acquisitions)
                ),
                "avg_write_wait_ms": (
                    self._metrics.write_wait_time_ms / max(1, self._metrics.write_acquisitions)
                ),
                "max_concurrent_readers": self._metrics.max_concurrent_readers,
                "contention_events": self._metrics.total_contention_events
            }
        }
