"""
从节点错误处理和降级机制测试
验证SlaveIndexErrorHandler类和相关功能
"""

import os
import tempfile
import time
import unittest
from unittest.mock import Mock, patch

from core.slave_node_index_builder import Slave<PERSON>ode<PERSON>ndex<PERSON><PERSON>er, SlaveIndexErrorHandler, SlaveIndexErrorInfo


class TestErrorHandlingDegradation(unittest.TestCase):
    """错误处理和降级机制测试类"""

    def setUp(self):
        """设置测试环境"""
        # 不再需要模拟环境变量，使用settings配置

        # 创建临时缓存文件
        self.temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".json.gz", delete=False)
        self.temp_file.close()

        # 模拟rule_index_manager
        self.mock_rule_index_manager = Mock()

    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)

    def test_error_handler_initialization(self):
        """测试错误处理器初始化"""
        mock_slave_builder = Mock()
        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 验证初始状态
        self.assertEqual(error_handler.error_count, 0)
        self.assertEqual(error_handler.error_threshold, 5)  # 从settings配置读取
        self.assertEqual(error_handler.recovery_threshold, 10)  # 从settings配置读取
        self.assertFalse(error_handler.degradation_enabled)
        self.assertEqual(error_handler.degradation_level, 0)

    def test_error_info_creation(self):
        """测试错误信息创建"""
        mock_slave_builder = Mock()
        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        test_error = ValueError("Test error")
        context = {"test_key": "test_value"}

        error_info = error_handler._create_error_info(
            error=test_error, error_type="BUILD_ERROR", component="build", context=context, severity="high"
        )

        # 验证错误信息
        self.assertIsInstance(error_info, SlaveIndexErrorInfo)
        self.assertEqual(error_info.error_type, "BUILD_ERROR")
        self.assertEqual(error_info.component, "build")
        self.assertEqual(error_info.severity, "high")
        self.assertEqual(error_info.error_context, context)
        self.assertIn("建议", error_info.recovery_suggestion)

    def test_error_severity_determination(self):
        """测试错误严重程度判断"""
        mock_slave_builder = Mock()
        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 测试不同类型的错误
        critical_error = Exception("Memory allocation failed")
        high_error = Exception("Connection timeout")
        medium_error = Exception("Temporary warning")
        low_error = Exception("Some other error")

        self.assertEqual(error_handler._determine_error_severity(critical_error, "build"), "critical")
        self.assertEqual(error_handler._determine_error_severity(high_error, "build"), "high")
        self.assertEqual(error_handler._determine_error_severity(medium_error, "build"), "medium")
        self.assertEqual(error_handler._determine_error_severity(low_error, "build"), "low")

    def test_degradation_trigger(self):
        """测试降级触发机制"""
        mock_slave_builder = Mock()
        # 模拟性能统计对象
        mock_performance_stats = Mock()
        mock_performance_stats.parsing_errors = 0
        mock_performance_stats.build_errors = 0
        mock_performance_stats.memory_errors = 0
        mock_slave_builder.performance_stats = mock_performance_stats

        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 模拟多个错误以触发降级
        test_error = Exception("Test error")

        # 前4个错误 - 不应该触发降级
        for i in range(4):
            error_handler.handle_build_error(test_error)
            self.assertFalse(error_handler.degradation_enabled)

        # 第5个错误 - 应该触发降级
        error_handler.handle_build_error(test_error)
        self.assertTrue(error_handler.degradation_enabled)
        self.assertEqual(error_handler.degradation_level, 1)

    def test_degradation_level_escalation(self):
        """测试降级级别升级"""
        mock_slave_builder = Mock()
        # 模拟性能统计对象
        mock_performance_stats = Mock()
        mock_performance_stats.parsing_errors = 0
        mock_performance_stats.build_errors = 0
        mock_performance_stats.memory_errors = 0
        mock_slave_builder.performance_stats = mock_performance_stats

        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 手动设置为已降级状态
        error_handler.degradation_enabled = True
        error_handler.degradation_level = 1
        error_handler.last_degradation_time = time.time() - 120  # 2分钟前，超过冷却时间

        # 继续添加错误应该升级降级级别
        test_error = Exception("Another error")
        for _ in range(3):  # 添加足够的错误
            error_handler._record_error(error_handler._create_error_info(test_error, "BUILD_ERROR", "build", {}, "high"))

        # 直接调用触发降级来升级级别
        error_handler._trigger_degradation()

        # 由于已经在降级状态，应该升级级别
        self.assertEqual(error_handler.degradation_level, 2)

    def test_recovery_mechanism(self):
        """测试恢复机制"""
        mock_slave_builder = Mock()
        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 设置为降级状态
        error_handler.degradation_enabled = True
        error_handler.degradation_level = 1

        # 记录成功操作
        for _ in range(10):  # recovery_threshold = 10
            error_handler.record_success()

        # 应该触发恢复
        self.assertFalse(error_handler.degradation_enabled)
        self.assertEqual(error_handler.degradation_level, 0)

    def test_error_statistics(self):
        """测试错误统计"""
        mock_slave_builder = Mock()
        # 模拟性能统计对象
        mock_performance_stats = Mock()
        mock_performance_stats.parsing_errors = 0
        mock_performance_stats.build_errors = 0
        mock_performance_stats.memory_errors = 0
        mock_slave_builder.performance_stats = mock_performance_stats

        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 添加不同类型的错误
        error_handler.handle_build_error(Exception("Build error"))
        error_handler.handle_parse_error(Exception("Parse error"))
        error_handler.handle_memory_error(Exception("Memory error"))

        stats = error_handler.get_error_statistics()

        # 验证统计信息
        self.assertEqual(stats["total_errors"], 3)
        self.assertIn("error_by_component", stats)
        self.assertIn("error_by_severity", stats)
        self.assertIn("degradation_enabled", stats)

    @patch("core.slave_node_index_builder.MemoryOptimizer")
    def test_integration_with_slave_builder(self, mock_memory_optimizer_class):
        """测试与SlaveNodeIndexBuilder的集成"""
        mock_memory_optimizer = Mock()
        mock_memory_optimizer_class.return_value = mock_memory_optimizer

        # 模拟内存状态
        mock_memory_stats = Mock()
        mock_memory_stats.is_over_threshold = False
        mock_memory_stats.process_memory_mb = 200.0
        mock_memory_optimizer.get_memory_stats.return_value = mock_memory_stats

        builder = SlaveNodeIndexBuilder(self.mock_rule_index_manager, self.temp_file.name)

        # 验证错误处理器已初始化
        self.assertIsInstance(builder.error_handler, SlaveIndexErrorHandler)

        # 测试错误处理器状态查询
        status = builder.get_error_handler_status()
        self.assertIsInstance(status, dict)
        self.assertIn("total_errors", status)

        # 测试降级状态查询
        self.assertFalse(builder.is_in_degradation_mode())
        self.assertEqual(builder.get_degradation_level(), 0)

    def test_force_recovery(self):
        """测试强制恢复"""
        mock_slave_builder = Mock()
        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 设置为降级状态
        error_handler.degradation_enabled = True
        error_handler.degradation_level = 2

        # 执行强制恢复
        error_handler.force_recovery()

        # 验证已恢复
        self.assertFalse(error_handler.degradation_enabled)
        self.assertEqual(error_handler.degradation_level, 0)

    def test_error_history_management(self):
        """测试错误历史管理"""
        mock_slave_builder = Mock()
        # 模拟性能统计对象
        mock_performance_stats = Mock()
        mock_performance_stats.parsing_errors = 0
        mock_performance_stats.build_errors = 0
        mock_performance_stats.memory_errors = 0
        mock_slave_builder.performance_stats = mock_performance_stats

        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 添加错误
        for i in range(5):
            error_handler.handle_build_error(Exception(f"Error {i}"))

        # 获取错误详情
        recent_errors = error_handler.get_recent_error_details(3)
        self.assertEqual(len(recent_errors), 3)

        # 验证错误详情格式
        for error_detail in recent_errors:
            self.assertIn("error_id", error_detail)
            self.assertIn("error_type", error_detail)
            self.assertIn("severity", error_detail)
            self.assertIn("recovery_suggestion", error_detail)

        # 清除错误历史
        error_handler.clear_error_history()
        self.assertEqual(len(error_handler.error_history), 0)
        self.assertEqual(error_handler.error_count, 0)

    def test_recovery_suggestions(self):
        """测试恢复建议生成"""
        mock_slave_builder = Mock()
        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 测试不同类型错误的恢复建议
        build_error = Exception("Memory allocation failed")
        suggestion = error_handler._generate_recovery_suggestion("BUILD_ERROR", build_error, {})
        self.assertIn("内存", suggestion)

        parse_error = Exception("JSON decode error")
        suggestion = error_handler._generate_recovery_suggestion("PARSE_ERROR", parse_error, {})
        self.assertIn("缓存文件", suggestion)

        reload_error = Exception("File not found")
        suggestion = error_handler._generate_recovery_suggestion("RELOAD_ERROR", reload_error, {})
        self.assertIn("文件监控", suggestion)

    def test_time_window_error_filtering(self):
        """测试时间窗口错误过滤"""
        mock_slave_builder = Mock()
        # 模拟性能统计对象
        mock_performance_stats = Mock()
        mock_performance_stats.parsing_errors = 0
        mock_performance_stats.build_errors = 0
        mock_performance_stats.memory_errors = 0
        mock_slave_builder.performance_stats = mock_performance_stats

        error_handler = SlaveIndexErrorHandler(mock_slave_builder)

        # 添加一个旧错误
        old_error = error_handler._create_error_info(Exception("Old error"), "BUILD_ERROR", "build", {}, "high")
        old_error.timestamp = time.time() - 400  # 超过5分钟窗口
        error_handler.error_history.append(old_error)

        # 添加一个新错误
        error_handler.handle_build_error(Exception("New error"))

        # 获取最近错误应该只包含新错误
        recent_errors = error_handler._get_recent_errors()
        self.assertEqual(len(recent_errors), 1)
        self.assertIn("New error", recent_errors[0].error_message)


if __name__ == "__main__":
    unittest.main()
