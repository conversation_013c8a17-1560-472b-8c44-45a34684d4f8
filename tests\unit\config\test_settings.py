"""
测试配置文件
用于配置测试环境的数据库连接和其他设置
"""

import os

from config.settings import Settings


def get_test_settings() -> Settings:
    """
    获取测试环境的配置
    使用真实数据库连接进行集成测试
    """
    # 设置测试环境变量
    test_env = {
        "DB_HOST": "***************",
        "DB_PORT": "3306", 
        "DB_USER": "rule_user",
        "DB_PASSWORD": "mysql_password",
        "DB_NAME": "rule_service",
        "DB_DRIVER": "pymysql",
        "MASTER_API_SECRET_KEY": "a_very_secret_key_for_development",
        "MODE": "master",
        "AUTO_CREATE_DATABASE": "false",  # 测试时不自动创建数据库
        "DB_CHECK_ON_STARTUP": "false",   # 测试时不检查数据库连接
    }

    # 临时设置环境变量
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value

    try:
        settings = Settings()
        return settings
    finally:
        # 恢复原始环境变量
        for key, original_value in original_env.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value


def get_test_database_url() -> str:
    """
    获取测试数据库连接URL
    """
    return "mysql+pymysql://rule_user:mysql_password@***************:3306/rule_service"
