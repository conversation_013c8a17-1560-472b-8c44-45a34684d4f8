<template>
  <div class="upload-progress">
    <!-- 进度状态卡片 -->
    <el-card class="progress-card" shadow="never">
      <div class="progress-header">
        <div class="status-icon" :class="statusClass">
          <el-icon v-if="status === 'uploading'"><Loading /></el-icon>
          <el-icon v-else-if="status === 'success'"><CircleCheck /></el-icon>
          <el-icon v-else-if="status === 'error'"><CircleClose /></el-icon>
          <el-icon v-else><Clock /></el-icon>
        </div>
        <div class="status-content">
          <h3 class="status-title">{{ statusTitle }}</h3>
          <p class="status-description">{{ statusDescription }}</p>
        </div>
      </div>

      <!-- 进度条 -->
      <div v-if="showProgress" class="progress-section">
        <el-progress
          :percentage="progress"
          :status="progressStatus"
          :stroke-width="8"
          class="main-progress"
        />

        <div v-if="progressMessage" class="progress-message">
          {{ progressMessage }}
        </div>

        <div v-if="progressDetail" class="progress-detail">
          {{ progressDetail }}
        </div>
      </div>

      <!-- 任务信息 -->
      <div v-if="taskInfo" class="task-info">
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="任务ID">
            <el-tag size="small">{{ taskInfo.task_id }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag
              :type="getTaskStatusType(taskInfo.status)"
              size="small"
            >
              {{ getTaskStatusText(taskInfo.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatTime(taskInfo.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatTime(taskInfo.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="taskInfo.progress_message" label="进度信息" :span="2">
            {{ taskInfo.progress_message }}
          </el-descriptions-item>
          <el-descriptions-item v-if="taskInfo.result_message" label="结果信息" :span="2">
            {{ taskInfo.result_message }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 结果统计 -->
      <div v-if="resultStats" class="result-stats">
        <div class="stats-grid">
          <div class="stat-item success">
            <div class="stat-number">{{ resultStats.success || 0 }}</div>
            <div class="stat-label">成功</div>
          </div>
          <div class="stat-item failed">
            <div class="stat-number">{{ resultStats.failed || 0 }}</div>
            <div class="stat-label">失败</div>
          </div>
          <div class="stat-item total">
            <div class="stat-number">{{ resultStats.total || 0 }}</div>
            <div class="stat-label">总计</div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          v-if="status === 'uploading' && allowCancel"
          @click="handleCancel"
          :loading="cancelling"
        >
          取消任务
        </el-button>

        <el-button
          v-if="status === 'error'"
          type="primary"
          @click="handleRetry"
        >
          重试
        </el-button>

        <el-button
          v-if="showRefresh"
          @click="handleRefresh"
          :loading="refreshing"
        >
          刷新状态
        </el-button>

        <el-button
          v-if="status === 'success' || status === 'error'"
          type="primary"
          @click="handleComplete"
        >
          {{ status === 'success' ? '完成' : '返回' }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  Loading,
  CircleCheck,
  CircleClose,
  Clock
} from '@element-plus/icons-vue'

const props = defineProps({
  status: {
    type: String,
    default: 'pending', // pending, uploading, success, error
    validator: (value) => ['pending', 'uploading', 'success', 'error'].includes(value)
  },
  progress: {
    type: Number,
    default: 0
  },
  progressMessage: {
    type: String,
    default: ''
  },
  progressDetail: {
    type: String,
    default: ''
  },
  taskInfo: {
    type: Object,
    default: null
  },
  resultStats: {
    type: Object,
    default: null
  },
  allowCancel: {
    type: Boolean,
    default: true
  },
  showRefresh: {
    type: Boolean,
    default: true
  },
  refreshing: {
    type: Boolean,
    default: false
  },
  cancelling: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['cancel', 'retry', 'refresh', 'complete'])

// 计算属性
const statusClass = computed(() => {
  return {
    'status-pending': props.status === 'pending',
    'status-uploading': props.status === 'uploading',
    'status-success': props.status === 'success',
    'status-error': props.status === 'error'
  }
})

const statusTitle = computed(() => {
  const titles = {
    pending: '准备上传',
    uploading: '正在上传',
    success: '上传成功',
    error: '上传失败'
  }
  return titles[props.status] || '未知状态'
})

const statusDescription = computed(() => {
  const descriptions = {
    pending: '系统正在准备数据上传任务',
    uploading: '数据正在上传中，请耐心等待',
    success: '数据上传已成功完成',
    error: '数据上传过程中发生错误'
  }
  return descriptions[props.status] || ''
})

const showProgress = computed(() => {
  return props.status === 'uploading' || (props.status === 'success' && props.progress === 100)
})

const progressStatus = computed(() => {
  if (props.status === 'success') return 'success'
  if (props.status === 'error') return 'exception'
  return undefined
})

// 工具函数
const getTaskStatusType = (status) => {
  const types = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getTaskStatusText = (status) => {
  const texts = {
    pending: '等待中',
    running: '执行中',
    completed: '已完成',
    failed: '已失败'
  }
  return texts[status] || status
}

const formatTime = (timeString) => {
  if (!timeString) return '-'
  return new Date(timeString).toLocaleString('zh-CN')
}

// 事件处理
const handleCancel = () => {
  emit('cancel')
}

const handleRetry = () => {
  emit('retry')
}

const handleRefresh = () => {
  emit('refresh')
}

const handleComplete = () => {
  emit('complete')
}
</script>

<style scoped>
.upload-progress {
  padding: 20px;
}

.progress-card {
  max-width: 600px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
}

/* 进度头部 */
.progress-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.status-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  transition: all 0.3s ease;
}

.status-icon.status-pending {
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
}

.status-icon.status-uploading {
  background-color: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
  animation: pulse 2s infinite;
}

.status-icon.status-success {
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.status-icon.status-error {
  background-color: var(--el-color-error-light-9);
  color: var(--el-color-error);
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.status-content {
  flex: 1;
}

.status-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.status-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  line-height: 1.5;
}

/* 进度区域 */
.progress-section {
  margin-bottom: 24px;
}

.main-progress {
  margin-bottom: 12px;
}

.progress-text {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.progress-message {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
  font-weight: 500;
}

.progress-detail {
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

/* 任务信息 */
.task-info {
  margin-bottom: 24px;
}

/* 结果统计 */
.result-stats {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.stat-item.success {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success-light-7);
}

.stat-item.failed {
  background-color: var(--el-color-error-light-9);
  border-color: var(--el-color-error-light-7);
}

.stat-item.total {
  background-color: var(--el-color-info-light-9);
  border-color: var(--el-color-info-light-7);
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .progress-header {
    flex-direction: column;
    text-align: center;
  }

  .status-icon {
    width: 56px;
    height: 56px;
    font-size: 28px;
  }

  .status-title {
    font-size: 18px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .upload-progress {
    padding: 16px;
  }

  .progress-header {
    margin-bottom: 20px;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-number {
    font-size: 20px;
  }
}
</style>
