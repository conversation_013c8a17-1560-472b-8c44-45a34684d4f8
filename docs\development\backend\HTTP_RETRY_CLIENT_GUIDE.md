# HTTP重试客户端使用指南

## 1. 概述

HTTP重试客户端提供了透明的重试功能，集成了指数退避算法和智能断路器模式，为HTTP请求提供强大的错误恢复能力。

### 1.1 核心特性

- **透明重试**：包装httpx.AsyncClient，提供透明的重试功能
- **智能错误分类**：自动识别可重试和不可重试的错误
- **指数退避**：集成指数退避算法，避免重试风暴
- **断路器保护**：集成智能断路器，防止级联故障
- **详细监控**：提供重试统计和详细日志
- **装饰器支持**：提供同步和异步重试装饰器

## 2. 快速开始

### 2.1 基础使用

```python
import asyncio
from core.http_retry import RetryClient

async def main():
    # 创建重试客户端
    async with RetryClient() as client:
        try:
            # 执行HTTP请求，自动重试
            response = await client.get("https://api.example.com/data")
            print(f"响应状态: {response.status_code}")
            return response.json()
        except Exception as e:
            print(f"请求失败: {e}")

# 运行示例
asyncio.run(main())
```

### 2.2 自定义配置

```python
from core.http_retry import RetryClient

# 自定义重试配置
retry_config = {
    "max_attempts": 5,           # 最大重试5次
    "base_delay": 2.0,          # 基础延迟2秒
    "max_delay": 120.0,         # 最大延迟2分钟
    "backoff_factor": 2.0,      # 指数退避因子
    "jitter": True,             # 启用抖动
    "retryable_status_codes": [408, 429, 500, 502, 503, 504]
}

async with RetryClient(retry_config=retry_config) as client:
    response = await client.post(
        "https://api.example.com/submit",
        json={"data": "example"}
    )
```

## 3. RetryClient详解

### 3.1 初始化参数

```python
client = RetryClient(
    base_client=None,                    # 基础HTTP客户端
    retry_config=None,                   # 重试配置
    circuit_breaker_name=None,           # 断路器名称
    enable_circuit_breaker=True          # 是否启用断路器
)
```

**参数说明：**
- `base_client`: 基础httpx.AsyncClient实例，如果为None则创建默认客户端
- `retry_config`: 自定义重试配置字典，覆盖默认配置
- `circuit_breaker_name`: 断路器名称，用于区分不同服务
- `enable_circuit_breaker`: 是否启用断路器保护

### 3.2 支持的HTTP方法

```python
async with RetryClient() as client:
    # GET请求
    response = await client.get("https://api.example.com/users")
    
    # POST请求
    response = await client.post(
        "https://api.example.com/users",
        json={"name": "John", "email": "<EMAIL>"}
    )
    
    # PUT请求
    response = await client.put(
        "https://api.example.com/users/123",
        json={"name": "John Updated"}
    )
    
    # DELETE请求
    response = await client.delete("https://api.example.com/users/123")
    
    # PATCH请求
    response = await client.patch(
        "https://api.example.com/users/123",
        json={"email": "<EMAIL>"}
    )
    
    # 通用request方法
    response = await client.request(
        "OPTIONS",
        "https://api.example.com/users",
        headers={"Custom-Header": "value"}
    )
```

### 3.3 错误分类和重试逻辑

**可重试的HTTP状态码：**
- `408` - Request Timeout
- `429` - Too Many Requests
- `500` - Internal Server Error
- `502` - Bad Gateway
- `503` - Service Unavailable
- `504` - Gateway Timeout

**可重试的异常类型：**
- `httpx.ConnectError` - 连接错误
- `httpx.TimeoutException` - 超时错误
- `ConnectionError` - 通用连接错误
- `TimeoutError` - 通用超时错误

**不可重试的情况：**
- `4xx`错误（除了408, 429）- 客户端错误
- 认证和授权错误
- 数据验证错误

### 3.4 重试配置详解

```python
retry_config = {
    "enabled": True,                     # 是否启用重试
    "max_attempts": 3,                   # 最大重试次数
    "base_delay": 1.0,                   # 基础延迟时间(秒)
    "max_delay": 60.0,                   # 最大延迟时间(秒)
    "backoff_factor": 2.0,               # 指数退避因子
    "jitter": True,                      # 是否启用抖动
    "retryable_status_codes": [408, 429, 500, 502, 503, 504],
    "retry_on_connection_error": True,   # 是否重试连接错误
    "retry_on_timeout_error": True       # 是否重试超时错误
}
```

## 4. 重试装饰器

### 4.1 异步函数装饰器

```python
from core.http_retry import retry_on_exception

@retry_on_exception(
    max_attempts=3,
    base_delay=1.0,
    retryable_exceptions=(ConnectionError, TimeoutError)
)
async def fetch_user_data(user_id: int):
    async with httpx.AsyncClient() as client:
        response = await client.get(f"https://api.example.com/users/{user_id}")
        response.raise_for_status()
        return response.json()

# 使用装饰器
user_data = await fetch_user_data(123)
```

### 4.2 同步函数装饰器

```python
from core.http_retry import sync_retry_on_exception
import requests

@sync_retry_on_exception(
    max_attempts=5,
    base_delay=2.0,
    max_delay=60.0,
    retryable_exceptions=(requests.ConnectionError, requests.Timeout)
)
def upload_file(file_path: str):
    with open(file_path, 'rb') as f:
        response = requests.post(
            "https://api.example.com/upload",
            files={"file": f}
        )
        response.raise_for_status()
        return response.json()

# 使用装饰器
result = upload_file("/path/to/file.txt")
```

### 4.3 装饰器参数

```python
@retry_on_exception(
    max_attempts=5,                      # 最大重试次数
    base_delay=1.0,                      # 基础延迟
    max_delay=60.0,                      # 最大延迟
    backoff_factor=2.0,                  # 退避因子
    jitter=True,                         # 启用抖动
    retryable_exceptions=(ConnectionError,),  # 可重试异常
    circuit_breaker_name="my_service",   # 断路器名称
    enable_circuit_breaker=True          # 启用断路器
)
async def my_function():
    pass
```

## 5. 实际应用场景

### 5.1 微服务API调用

```python
class UserService:
    def __init__(self):
        self.client = RetryClient(
            circuit_breaker_name="user_service",
            retry_config={
                "max_attempts": 3,
                "base_delay": 1.0,
                "max_delay": 30.0
            }
        )
    
    async def get_user(self, user_id: int):
        """获取用户信息"""
        try:
            response = await self.client.get(f"/api/users/{user_id}")
            return response.json()
        except RetryExhaustedError as e:
            logger.error(f"获取用户信息失败: {e}")
            raise UserServiceError("用户服务暂时不可用")
        except CircuitBreakerOpenError as e:
            logger.warning(f"用户服务断路器打开: {e}")
            # 返回缓存数据或默认值
            return await self.get_user_from_cache(user_id)
    
    async def create_user(self, user_data: dict):
        """创建用户"""
        response = await self.client.post("/api/users", json=user_data)
        return response.json()
    
    async def close(self):
        await self.client.close()
```

### 5.2 外部API集成

```python
class PaymentGateway:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = RetryClient(
            circuit_breaker_name="payment_gateway",
            retry_config={
                "max_attempts": 5,        # 支付API需要更多重试
                "base_delay": 2.0,        # 更长的基础延迟
                "max_delay": 120.0,       # 最大延迟2分钟
                "retryable_status_codes": [408, 429, 500, 502, 503, 504, 409]  # 包含409冲突
            }
        )
    
    @retry_on_exception(
        max_attempts=3,
        circuit_breaker_name="payment_process"
    )
    async def process_payment(self, payment_data: dict):
        """处理支付"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        response = await self.client.post(
            "/api/payments",
            json=payment_data,
            headers=headers
        )
        
        if response.status_code == 402:  # Payment Required
            raise PaymentDeclinedError("支付被拒绝")
        
        response.raise_for_status()
        return response.json()
```

### 5.3 数据同步服务

```python
class DataSyncService:
    def __init__(self):
        self.client = RetryClient(
            circuit_breaker_name="data_sync",
            retry_config={
                "max_attempts": 10,       # 数据同步允许更多重试
                "base_delay": 5.0,        # 较长的基础延迟
                "max_delay": 300.0,       # 最大延迟5分钟
                "backoff_factor": 1.5     # 较温和的退避
            }
        )
    
    async def sync_data_batch(self, batch_data: list):
        """同步数据批次"""
        try:
            response = await self.client.post(
                "/api/sync/batch",
                json={"data": batch_data},
                timeout=60.0  # 长超时时间
            )
            
            result = response.json()
            logger.info(f"同步成功: {len(batch_data)} 条记录")
            return result
            
        except RetryExhaustedError as e:
            logger.error(f"数据同步失败，重试耗尽: {e}")
            # 将失败的数据保存到队列中稍后重试
            await self.save_failed_batch(batch_data)
            raise
        
        except CircuitBreakerOpenError as e:
            logger.warning(f"数据同步服务断路器打开: {e}")
            # 暂停同步，等待服务恢复
            await self.pause_sync_process()
            raise
```

## 6. 监控和统计

### 6.1 获取重试统计

```python
async with RetryClient() as client:
    # 执行一些请求
    await client.get("https://api.example.com/data1")
    await client.get("https://api.example.com/data2")
    
    # 获取统计信息
    stats = client.get_stats()
    
    print(f"总请求数: {stats['total_requests']}")
    print(f"重试请求数: {stats['retry_requests']}")
    print(f"成功重试数: {stats['successful_retries']}")
    print(f"失败重试数: {stats['failed_retries']}")
    print(f"断路器触发数: {stats['circuit_breaker_trips']}")
    print(f"重试率: {stats['retry_rate']:.2%}")
    print(f"重试成功率: {stats['retry_success_rate']:.2%}")
    
    # 断路器状态
    if 'circuit_breaker_status' in stats:
        cb_status = stats['circuit_breaker_status']
        print(f"断路器状态: {cb_status['state']}")
        print(f"窗口成功率: {cb_status['window_success_rate']:.2%}")
```

### 6.2 日志监控

重试客户端提供详细的日志记录：

```python
# 重试尝试日志
logger.info(
    "HTTP请求重试 (第 2/3 次)",
    extra={
        "method": "GET",
        "url": "https://api.example.com/data",
        "attempt": 2,
        "max_attempts": 3,
        "retry_delay": 2.5,
        "error_type": "ConnectError",
        "circuit_breaker": "api_service"
    }
)

# 重试成功日志
logger.info(
    "HTTP请求重试成功 (第 2 次尝试)",
    extra={
        "method": "GET",
        "url": "https://api.example.com/data",
        "total_attempts": 2,
        "response_time": 0.85,
        "success_after_retries": True
    }
)

# 重试耗尽日志
logger.warning(
    "HTTP请求重试耗尽 (3 次)",
    extra={
        "method": "GET",
        "url": "https://api.example.com/data",
        "max_attempts": 3,
        "last_error": "Connection timeout",
        "error_type": "TimeoutException"
    }
)
```

## 7. 最佳实践

### 7.1 配置建议

**快速API服务：**
```python
fast_api_config = {
    "max_attempts": 3,
    "base_delay": 0.5,
    "max_delay": 10.0,
    "backoff_factor": 2.0
}
```

**稳定性要求高的服务：**
```python
critical_service_config = {
    "max_attempts": 5,
    "base_delay": 2.0,
    "max_delay": 60.0,
    "backoff_factor": 1.5
}
```

**批处理任务：**
```python
batch_job_config = {
    "max_attempts": 10,
    "base_delay": 5.0,
    "max_delay": 300.0,
    "backoff_factor": 1.2
}
```

### 7.2 使用建议

1. **合理设置重试次数**：根据服务特性和业务需求设置合适的重试次数
2. **使用断路器保护**：为不同的服务使用不同的断路器名称
3. **监控重试指标**：定期检查重试率和成功率，调整配置
4. **处理重试耗尽**：为重试耗尽的情况提供降级处理逻辑
5. **合理使用装饰器**：对于简单的重试需求，使用装饰器更简洁

### 7.3 性能考虑

```python
# 使用连接池提高性能
import httpx

base_client = httpx.AsyncClient(
    limits=httpx.Limits(max_keepalive_connections=20, max_connections=100),
    timeout=httpx.Timeout(10.0)
)

retry_client = RetryClient(base_client=base_client)
```

### 7.4 错误处理

```python
from core.http_retry import RetryClient, RetryExhaustedError, CircuitBreakerOpenError

async def robust_api_call():
    async with RetryClient() as client:
        try:
            response = await client.get("https://api.example.com/data")
            return response.json()
        
        except RetryExhaustedError as e:
            # 重试耗尽，记录错误并返回默认值
            logger.error(f"API调用重试耗尽: {e}")
            return {"error": "服务暂时不可用，请稍后重试"}
        
        except CircuitBreakerOpenError as e:
            # 断路器打开，使用缓存或降级服务
            logger.warning(f"断路器打开: {e}")
            return await get_cached_data()
        
        except Exception as e:
            # 其他错误
            logger.error(f"API调用失败: {e}")
            raise
```

---

**文档版本：** 1.0  
**更新时间：** 2025-07-01  
**适用版本：** CQ-008及以后版本
