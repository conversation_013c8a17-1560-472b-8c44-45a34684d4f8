# 常见问题解决

本文档收集了用户在使用规则验证系统过程中遇到的常见问题及解决方案。

## 🔍 问题分类

### 🚀 系统访问问题
### 📁 文件上传问题  
### ⚙️ 功能使用问题
### 🔧 性能问题
### 🛡️ 数据安全问题

---

## 🚀 系统访问问题

### Q1: 无法访问系统页面
**症状**: 浏览器显示"无法连接到服务器"或"页面无法访问"

**可能原因**:
- 服务器未启动或已停止
- 网络连接问题
- 防火墙阻止访问
- 端口配置错误

**解决方案**:
1. **检查服务状态**
   ```bash
   # 检查服务是否运行
   curl http://localhost:18001/health
   ```

2. **检查网络连接**
   - 确认服务器IP地址正确
   - 测试网络连通性：`ping server-ip`
   - 检查端口是否开放：`telnet server-ip 18001`

3. **检查防火墙设置**
   - 确认防火墙允许18001端口访问
   - 检查企业网络安全策略

4. **联系系统管理员**
   - 如以上步骤无效，请联系技术支持

### Q2: 页面加载缓慢
**症状**: 页面打开速度很慢，或部分内容无法加载

**解决方案**:
1. **清除浏览器缓存**
   - Chrome: Ctrl+Shift+Delete
   - Firefox: Ctrl+Shift+Delete
   - 选择清除缓存和Cookie

2. **检查网络带宽**
   - 测试网络速度
   - 避免在网络高峰期使用

3. **更换浏览器**
   - 推荐使用Chrome或Firefox最新版本
   - 禁用不必要的浏览器插件

---

## 📁 文件上传问题

### Q3: Excel文件上传失败
**症状**: 点击上传后显示"上传失败"或"文件格式错误"

**解决方案**:
1. **检查文件格式**
   - 确保文件是.xlsx或.xls格式
   - 文件名不包含特殊字符
   - 文件大小不超过50MB

2. **检查文件内容**
   ```
   正确格式示例:
   - 第一行必须是列标题
   - 必填字段不能为空
   - 数值字段必须是数字格式
   - 日期字段使用标准日期格式
   ```

3. **重新保存文件**
   - 用Excel重新打开文件
   - 另存为.xlsx格式
   - 确保编码为UTF-8

### Q4: 上传进度卡住不动
**症状**: 上传进度条停止不动，长时间无响应

**解决方案**:
1. **检查网络稳定性**
   - 确保网络连接稳定
   - 避免在上传过程中切换网络

2. **分批上传**
   - 将大文件拆分为多个小文件
   - 每个文件建议不超过1000行数据

3. **重新上传**
   - 刷新页面后重新上传
   - 如果问题持续，联系技术支持

---

## ⚙️ 功能使用问题

### Q5: 规则验证结果不正确
**症状**: 验证结果与预期不符，或出现错误的验证结论

**解决方案**:
1. **检查输入数据**
   - 确认患者数据格式正确
   - 检查必填字段是否完整
   - 验证数据类型是否匹配

2. **检查规则配置**
   - 确认选择了正确的规则
   - 检查规则是否为最新版本
   - 验证规则逻辑是否正确

3. **查看详细日志**
   - 点击"查看详情"获取验证过程
   - 分析具体的验证步骤
   - 记录错误信息联系技术支持

### Q6: 无法下载验证结果
**症状**: 点击下载按钮无反应，或下载的文件为空

**解决方案**:
1. **检查浏览器设置**
   - 确认浏览器允许下载
   - 检查下载目录是否有写入权限
   - 禁用下载拦截插件

2. **重新生成结果**
   - 刷新页面重新验证
   - 等待验证完全完成后再下载

3. **更换下载方式**
   - 右键点击下载链接选择"另存为"
   - 尝试使用不同浏览器下载

---

## 🔧 性能问题

### Q7: 系统响应速度慢
**症状**: 操作响应时间长，页面切换缓慢

**解决方案**:
1. **优化使用方式**
   - 避免同时打开多个页面
   - 减少并发操作数量
   - 分批处理大量数据

2. **检查系统负载**
   - 避免在系统高峰期使用
   - 选择合适的时间进行批量操作

3. **联系管理员**
   - 如果问题持续，可能需要系统优化
   - 提供具体的操作场景和时间

### Q8: 批量验证超时
**症状**: 大批量数据验证时出现超时错误

**解决方案**:
1. **减少数据量**
   - 将数据分成多个小批次
   - 每批建议不超过5000条记录

2. **优化数据格式**
   - 移除不必要的列
   - 确保数据格式标准化

3. **分时段处理**
   - 避开系统繁忙时段
   - 选择网络较好的时间段

---

## 🛡️ 数据安全问题

### Q9: 担心数据泄露
**症状**: 对上传的敏感数据安全性有担忧

**解决方案**:
1. **了解安全措施**
   - 系统采用HTTPS加密传输
   - 数据存储在安全的服务器环境
   - 定期进行安全审计

2. **数据脱敏**
   - 上传前对敏感字段进行脱敏
   - 使用测试数据进行功能验证

3. **及时清理**
   - 验证完成后及时删除不需要的数据
   - 定期清理历史数据

### Q10: 误删重要数据
**症状**: 不小心删除了重要的规则或数据

**解决方案**:
1. **检查回收站**
   - 查看系统是否有回收站功能
   - 尝试从回收站恢复

2. **查看备份**
   - 联系管理员查看是否有数据备份
   - 从最近的备份中恢复数据

3. **预防措施**
   - 重要操作前先备份
   - 使用确认对话框谨慎操作

---

## 📞 获取更多帮助

### 自助服务
- **系统帮助**: 点击页面右上角"?"图标
- **操作视频**: 观看功能演示视频
- **用户手册**: 查看详细的用户操作手册

### 技术支持
- **在线客服**: 工作时间内提供实时支持
- **邮件支持**: <EMAIL>
- **电话支持**: 400-xxx-xxxx

### 反馈渠道
- **问题反馈**: 通过系统内置反馈功能
- **建议提交**: 参与用户体验改进计划
- **社区讨论**: 加入用户交流群

---

## 📝 问题报告模板

当遇到无法解决的问题时，请按以下模板提供信息：

```
问题描述：[详细描述遇到的问题]
操作步骤：[列出导致问题的操作步骤]
预期结果：[描述期望的正确结果]
实际结果：[描述实际发生的情况]
错误信息：[如有错误提示，请完整复制]
使用环境：[浏览器类型和版本、操作系统]
发生时间：[问题发生的具体时间]
影响范围：[问题影响的功能或数据范围]
```

---

**更新说明**: 本文档会根据用户反馈持续更新，如有新的常见问题会及时补充。

**最后更新**: 2025-07-23
