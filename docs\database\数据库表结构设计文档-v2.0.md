# 数据库表结构设计文档 v2.0

## 📋 概述

本文档描述了规则详情表三表结构的最终设计方案，采用**固定字段 + JSON扩展 + 元数据驱动**架构，并建立了正确的外键关联关系。

## 🎯 设计目标

1. **数据完整性**：通过外键约束确保引用完整性
2. **架构简化**：三表结构，职责分明
3. **扩展性强**：支持动态字段扩展
4. **性能优化**：合理的索引策略
5. **标准化**：统一字段命名规范

## 🏗️ 表结构设计

### 1. rule_template 表（规则模板表 - 主表）

**作用**：存储规则模板的基本信息，作为其他表的外键引用源

```sql
CREATE TABLE rule_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_key VARCHAR(100) NOT NULL UNIQUE COMMENT '规则模板类型（业务主键）',
    rule_type VARCHAR(100) NOT NULL COMMENT '规则类型',
    name VARCHAR(500) NOT NULL COMMENT '规则模板名称',
    description TEXT COMMENT '规则模板描述',
    module_path VARCHAR(500) COMMENT 'Python module path',
    file_hash VARCHAR(64) COMMENT 'SHA-256 hash',
    status ENUM('NEW', 'CHANGED', 'READY', 'DEPRECATED') DEFAULT 'NEW',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE KEY uk_rule_template_key (rule_key),
    INDEX idx_rule_template_type (rule_type),
    INDEX idx_rule_template_status (status)
);
```

**关键字段说明**：
- `rule_key`：业务主键，用于跨表关联
- `rule_type`：规则分类，如"药品适应症"
- `status`：模板状态管理

### 2. rule_detail 表（规则明细表 - 子表）

**作用**：存储具体的规则明细数据，使用标准字段名

```sql
CREATE TABLE rule_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_id VARCHAR(100) NOT NULL COMMENT '规则ID',
    rule_key VARCHAR(100) NOT NULL COMMENT '规则模板类型',
    
    -- 通用字段（使用标准命名）
    rule_name VARCHAR(500) NOT NULL COMMENT '规则名称',
    level1 VARCHAR(255) NOT NULL COMMENT '一级错误类型',
    level2 VARCHAR(255) NOT NULL COMMENT '二级错误类型',
    level3 VARCHAR(255) NOT NULL COMMENT '三级错误类型',
    error_reason TEXT NOT NULL COMMENT '错误原因',
    degree VARCHAR(50) NOT NULL COMMENT '错误程度（无约束）',
    reference TEXT NOT NULL COMMENT '质控依据或参考资料',
    detail_position VARCHAR(100) NOT NULL COMMENT '具体位置描述',
    prompted_fields3 VARCHAR(100) COMMENT '提示字段类型',
    prompted_fields1 VARCHAR(100) NOT NULL COMMENT '提示字段编码',
    type VARCHAR(100) NOT NULL COMMENT '规则类别',
    pos VARCHAR(100) NOT NULL COMMENT '适用业务',
    applicableArea VARCHAR(100) NOT NULL COMMENT '适用地区',
    default_use VARCHAR(50) NOT NULL COMMENT '默认选用',
    remarks TEXT COMMENT '备注信息',
    in_illustration TEXT COMMENT '入参说明',
    start_date VARCHAR(20) NOT NULL COMMENT '开始日期',
    end_date VARCHAR(20) NOT NULL COMMENT '结束日期',
    
    -- 固定的高频字段
    yb_code TEXT COMMENT '药品编码，逗号分隔',
    diag_whole_code TEXT COMMENT '完整诊断编码，逗号分隔',
    diag_code_prefix TEXT COMMENT '诊断编码前缀，逗号分隔',
    diag_name_keyword VARCHAR(200) COMMENT '诊断名称关键字，逗号分隔',
    fee_whole_code TEXT COMMENT '药品/诊疗项目完整编码，逗号分隔',
    fee_code_prefix TEXT COMMENT '药品/诊疗项目编码前缀，逗号分隔',
    
    -- 扩展字段
    extended_fields TEXT COMMENT 'JSON格式的扩展字段',
    
    -- 元数据
    status ENUM('ACTIVE', 'INACTIVE', 'DEPRECATED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束（关键！）
    FOREIGN KEY fk_rule_detail_template (rule_key) REFERENCES rule_template(rule_key) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- 索引
    INDEX idx_rule_detail_rule_key (rule_key),
    INDEX idx_rule_detail_rule_id (rule_id),
    INDEX idx_rule_detail_status (status),
    INDEX idx_rule_detail_key_status (rule_key, status),
    INDEX idx_rule_detail_yb_code (yb_code(100))
);
```

**关键设计特点**：
- **标准字段名**：level1, level2, level3（替代error_level_1/2/3）
- **外键约束**：确保数据完整性
- **复合索引**：优化联合查询性能
- **无业务约束**：degree字段不限制枚举值

### 3. rule_field_metadata 表（字段元数据表 - 子表）

**作用**：存储字段的元数据信息，支持动态Excel模板生成和数据校验

```sql
CREATE TABLE rule_field_metadata (
    id INT PRIMARY KEY AUTO_INCREMENT,
    rule_key VARCHAR(100) NOT NULL COMMENT '规则模板类型',
    field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
    field_type ENUM('string', 'integer', 'array', 'boolean') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    is_fixed_field BOOLEAN DEFAULT FALSE COMMENT '是否为固定字段',
    display_name VARCHAR(200) NOT NULL COMMENT '显示名称',
    description TEXT COMMENT '字段描述',
    validation_rule TEXT COMMENT 'JSON格式的校验规则',
    default_value TEXT COMMENT '默认值',
    excel_column_order INT COMMENT 'Excel列顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束（关键！）
    FOREIGN KEY fk_rule_field_template (rule_key) REFERENCES rule_template(rule_key) 
        ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- 索引和约束
    UNIQUE KEY uk_rule_field (rule_key, field_name),
    INDEX idx_rule_field_metadata_rule_key (rule_key)
);
```

**关键功能**：
- **元数据驱动**：支持动态Excel模板生成
- **校验规则**：JSON格式的验证规则定义
- **Excel集成**：列顺序和显示名称管理

## 🔗 表关联关系

```
rule_template (主表)
    ├── rule_key (业务主键)
    │
    ├─── rule_detail (1:N)
    │    └── rule_key (外键) → rule_template.rule_key
    │         ├── CASCADE DELETE: 删除模板时自动删除所有明细
    │         └── CASCADE UPDATE: 更新rule_key时自动更新关联记录
    │
    └─── rule_field_metadata (1:N)
         └── rule_key (外键) → rule_template.rule_key
              ├── CASCADE DELETE: 删除模板时自动删除所有元数据
              └── CASCADE UPDATE: 更新rule_key时自动更新关联记录
```

## 📊 索引策略

### 主要索引
1. **主键索引**：所有表的id字段
2. **唯一索引**：rule_template.rule_key（业务主键）
3. **外键索引**：自动为外键字段创建索引

### 查询优化索引
1. **复合索引**：(rule_key, status) - 优化按模板和状态查询
2. **前缀索引**：yb_code(100) - 优化药品编码查询
3. **分类索引**：rule_type, status - 支持分类和状态筛选

## 🎯 设计优势

### 1. 数据完整性
- **外键约束**：确保引用完整性
- **级联操作**：自动维护数据一致性
- **唯一约束**：防止重复数据

### 2. 查询性能
- **复合索引**：优化多条件查询
- **前缀索引**：处理长文本字段
- **合理分区**：按业务逻辑分表

### 3. 扩展性
- **JSON字段**：支持动态扩展
- **元数据驱动**：灵活的字段管理
- **模板化设计**：易于添加新规则类型

### 4. 维护性
- **标准命名**：统一字段命名规范
- **清晰职责**：三表分工明确
- **文档完整**：详细的设计说明

## 🚀 实施状态

- ✅ **表结构创建**：已完成
- ✅ **外键约束**：已建立
- ✅ **索引优化**：已实施
- ✅ **ORM模型**：已更新
- ✅ **验证测试**：已通过

## 📝 版本历史

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| v2.0 | 2025-07-24 | 重建三表结构，添加外键关联，移除旧表 | 开发团队 |
| v1.0 | 2025-07-23 | 初始设计，缺少外键关联 | 开发团队 |

---

**文档维护者**：规则验证系统开发团队  
**最后更新**：2025-07-24  
**下次评审**：2025-08-24
