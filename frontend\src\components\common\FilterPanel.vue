<template>
  <div class="filter-panel">
    <!-- 过滤器触发按钮 -->
    <el-button
      :type="hasActiveFilters ? 'primary' : 'default'"
      :icon="Filter"
      @click="togglePanel"
      class="filter-trigger"
    >
      过滤器
      <el-badge
        v-if="activeFilterCount > 0"
        :value="activeFilterCount"
        class="filter-badge"
      />
    </el-button>

    <!-- 过滤器面板 -->
    <el-drawer
      v-model="panelVisible"
      title="过滤条件"
      :size="drawerSize"
      direction="rtl"
      class="filter-drawer"
    >
      <div class="filter-content">
        <!-- 状态过滤 -->
        <div class="filter-group" v-if="options.status">
          <label class="filter-label">状态</label>
          <el-select
            v-model="localFilters.status"
            placeholder="选择状态"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="option in options.status"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <!-- 一级错误类型过滤 -->
        <div class="filter-group" v-if="options.level1">
          <label class="filter-label">一级错误类型</label>
          <el-select
            v-model="localFilters.level1"
            placeholder="选择一级错误类型"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="option in options.level1"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <!-- 二级错误类型过滤 -->
        <div class="filter-group" v-if="options.level2">
          <label class="filter-label">二级错误类型</label>
          <el-select
            v-model="localFilters.level2"
            placeholder="选择二级错误类型"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="option in options.level2"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <!-- 规则类别过滤 -->
        <div class="filter-group" v-if="options.type">
          <label class="filter-label">规则类别</label>
          <el-select
            v-model="localFilters.type"
            placeholder="选择规则类别"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="option in options.type"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <!-- 时间范围过滤 -->
        <div class="filter-group">
          <label class="filter-label">创建时间</label>
          <el-date-picker
            v-model="localFilters.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            class="filter-date-picker"
          />
        </div>

        <!-- 金额范围过滤 -->
        <div class="filter-group">
          <label class="filter-label">涉及金额范围</label>
          <div class="amount-range">
            <el-input
              v-model="localFilters.min_amount"
              placeholder="最小金额"
              type="number"
              class="amount-input"
            >
              <template #prepend>¥</template>
            </el-input>
            <span class="range-separator">-</span>
            <el-input
              v-model="localFilters.max_amount"
              placeholder="最大金额"
              type="number"
              class="amount-input"
            >
              <template #prepend>¥</template>
            </el-input>
          </div>
        </div>

        <!-- 自定义过滤器插槽 -->
        <slot name="custom-filters" :filters="localFilters" />
      </div>

      <!-- 操作按钮 -->
      <template #footer>
        <div class="filter-actions">
          <el-button @click="resetFilters" :icon="RefreshLeft">
            重置
          </el-button>
          <el-button @click="cancelChanges">
            取消
          </el-button>
          <el-button type="primary" @click="applyFilters" :icon="Check">
            应用
          </el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 活跃过滤器标签 -->
    <div v-if="hasActiveFilters" class="active-filters">
      <el-tag
        v-for="(filter, key) in activeFilters"
        :key="key"
        closable
        @close="removeFilter(key)"
        class="filter-tag"
      >
        {{ getFilterLabel(key, filter) }}
      </el-tag>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import { Filter, RefreshLeft, Check } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  options: {
    type: Object,
    default: () => ({})
  },
  size: {
    type: String,
    default: 'default'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式状态
const panelVisible = ref(false)
const localFilters = reactive({
  status: '',
  level1: '',
  level2: '',
  type: '',
  date_range: [],
  min_amount: '',
  max_amount: '',
  ...props.modelValue
})

// 计算属性
const drawerSize = computed(() => {
  return window.innerWidth < 768 ? '100%' : '400px'
})

const activeFilters = computed(() => {
  const filters = {}
  Object.keys(localFilters).forEach(key => {
    const value = localFilters[key]
    if (value !== '' && value !== null && value !== undefined) {
      if (Array.isArray(value) && value.length > 0) {
        filters[key] = value
      } else if (!Array.isArray(value)) {
        filters[key] = value
      }
    }
  })
  return filters
})

const hasActiveFilters = computed(() => {
  return Object.keys(activeFilters.value).length > 0
})

const activeFilterCount = computed(() => {
  return Object.keys(activeFilters.value).length
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(localFilters, newValue)
}, { deep: true })

// 切换面板显示
const togglePanel = () => {
  panelVisible.value = !panelVisible.value
}

// 应用过滤器
const applyFilters = () => {
  const filters = { ...activeFilters.value }
  emit('update:modelValue', filters)
  emit('change', filters)
  panelVisible.value = false
}

// 重置过滤器
const resetFilters = () => {
  Object.keys(localFilters).forEach(key => {
    if (Array.isArray(localFilters[key])) {
      localFilters[key] = []
    } else {
      localFilters[key] = ''
    }
  })
  applyFilters()
}

// 取消更改
const cancelChanges = () => {
  Object.assign(localFilters, props.modelValue)
  panelVisible.value = false
}

// 移除单个过滤器
const removeFilter = (key) => {
  if (Array.isArray(localFilters[key])) {
    localFilters[key] = []
  } else {
    localFilters[key] = ''
  }
  applyFilters()
}

// 获取过滤器标签文本
const getFilterLabel = (key, value) => {
  const labelMap = {
    status: '状态',
    level1: '一级错误',
    level2: '二级错误',
    type: '规则类别',
    date_range: '创建时间',
    min_amount: '最小金额',
    max_amount: '最大金额'
  }

  const label = labelMap[key] || key

  if (key === 'date_range' && Array.isArray(value) && value.length === 2) {
    return `${label}: ${value[0]} ~ ${value[1]}`
  } else if (key === 'min_amount' || key === 'max_amount') {
    return `${label}: ¥${value}`
  } else {
    // 尝试从选项中获取标签
    const optionGroup = props.options[key]
    if (optionGroup) {
      const option = optionGroup.find(opt => opt.value === value)
      if (option) {
        return `${label}: ${option.label}`
      }
    }
    return `${label}: ${value}`
  }
}
</script>

<style scoped>
.filter-panel {
  position: relative;
}

.filter-trigger {
  position: relative;
}

.filter-badge {
  position: absolute;
  top: -8px;
  right: -8px;
}

.filter-content {
  padding: 0 0 20px 0;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.filter-select,
.filter-date-picker {
  width: 100%;
}

.amount-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-input {
  flex: 1;
}

.range-separator {
  color: #909399;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
}

.active-filters {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  z-index: 100;
}

.filter-tag {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-actions {
    flex-direction: column;
  }

  .filter-actions .el-button {
    width: 100%;
  }

  .amount-range {
    flex-direction: column;
    gap: 12px;
  }

  .range-separator {
    display: none;
  }
}
</style>

<style>
/* 全局样式 - 抽屉内容 */
.filter-drawer .el-drawer__body {
  padding: 20px;
}

.filter-drawer .el-drawer__footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
}
</style>
