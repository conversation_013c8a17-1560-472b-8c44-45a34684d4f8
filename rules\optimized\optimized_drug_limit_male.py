"""
优化的男性限制药品规则
展示如何使用优化基类实现高性能规则
"""

from models.patient import PatientData
from models.rule import RuleResult
from rules.optimized.optimized_base_rule import OptimizedBaseRule


class OptimizedMaleOnlyDrugRule(OptimizedBaseRule):
    """
    优化的药品限男性使用规则
    使用预编译、快速预检查、向量化计算等优化技术
    """

    rule_key = "optimized_drug_limit_male"
    rule_name = "优化的药品限男性使用"

    def __init__(
        self,
        rule_id: str,
        yb_codes: list[str],
        rule_name: str,
        level1: str,
        level2: str,
        level3: str,
        error_reason: str,
        degree: str,
        reference: str,
        detail_position: str,
        prompted_fields3: str | None,
        prompted_fields1: str,
        type: str,
        pos: str,
        applicableArea: str,
        default_use: str,
        remarks: str | None,
        in_illustration: str | None,
        start_date: str,
        end_date: str,
    ):
        # 调用父类初始化
        super().__init__(
            rule_id,
            yb_codes=yb_codes,
            rule_name=rule_name,
            level1=level1,
            level2=level2,
            level3=level3,
            error_reason=error_reason,
            degree=degree,
            reference=reference,
            detail_position=detail_position,
            prompted_fields3=prompted_fields3,
            prompted_fields1=prompted_fields1,
            type=type,
            pos=pos,
            applicableArea=applicableArea,
            default_use=default_use,
            remarks=remarks,
            in_illustration=in_illustration,
            start_date=start_date,
            end_date=end_date,
        )

    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        优化的药品限男性使用验证逻辑
        """
        # 快速性别检查（已在父类预检查中处理）
        gender = patient_data.basic_information.gender if patient_data.basic_information else None

        # 使用预编译的正则表达式检查性别
        gender_pattern = self._compiled_patterns.get("gender_male")
        if gender_pattern and gender_pattern.match(str(gender).lower()):
            return None  # 男性，不违规

        # 获取相关费用项（向量化处理）
        relevant_fees = self.get_relevant_fees(patient_data)

        if not relevant_fees:
            return None  # 没有相关费用项

        # 向量化计算聚合值
        total_quantity, total_amount, total_days, date_set = self.calculate_aggregates(relevant_fees)

        if total_quantity == 0:
            return None  # 使用数量为0

        # 收集违规项目ID
        fee_ids = [fee.id for fee in relevant_fees if fee.id]
        illegal_items = ",".join(fee_ids)

        # 创建优化的结果对象
        return self.create_optimized_result(
            type_=self.type,
            message=self.error_reason,
            level1=self.level1,
            level2=self.level2,
            level3=self.level3,
            error_reason=self.error_reason,
            degree=self.degree,
            reference=self.reference,
            prompted_fields3=self.prompted_fields3,
            prompted_fields1=self.prompted_fields1,
            detail_position=self.detail_position,
            pos=self.pos,
            applicableArea=self.applicableArea,
            default_use=self.default_use,
            remarks=self.remarks,
            in_illustration=self.in_illustration,
            used_count=total_quantity,
            illegal_count=total_quantity,
            used_day=total_days,
            illegal_day=total_days,
            illegal_item=illegal_items,
            error_fee=total_amount,
            prompted_fields2=illegal_items,
        )


class OptimizedMedicationDaysLimitRule(OptimizedBaseRule):
    """
    优化的药品限最大支付天数规则
    """

    rule_key = "optimized_medication_days_limit"
    rule_name = "优化的药品限最大支付天数"

    def __init__(
        self,
        rule_id: str,
        yb_codes: list[str],
        limit_days: int,
        rule_name: str,
        level1: str,
        level2: str,
        level3: str,
        error_reason: str,
        degree: str,
        reference: str,
        detail_position: str,
        prompted_fields3: str | None,
        prompted_fields1: str,
        type: str,
        pos: str,
        applicableArea: str,
        default_use: str,
        remarks: str | None,
        in_illustration: str | None,
        start_date: str,
        end_date: str,
    ):
        super().__init__(
            rule_id,
            yb_codes=yb_codes,
            limit_days=limit_days,
            rule_name=rule_name,
            level1=level1,
            level2=level2,
            level3=level3,
            error_reason=error_reason,
            degree=degree,
            reference=reference,
            detail_position=detail_position,
            prompted_fields3=prompted_fields3,
            prompted_fields1=prompted_fields1,
            type=type,
            pos=pos,
            applicableArea=applicableArea,
            default_use=default_use,
            remarks=remarks,
            in_illustration=in_illustration,
            start_date=start_date,
            end_date=end_date,
        )

    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        优化的药品限最大支付天数验证逻辑
        """
        # 快速医保类型检查
        insurance_type = patient_data.patientMedicalInsuranceType
        if not insurance_type or not isinstance(insurance_type, str) or "自费" in insurance_type:
            return None

        # 获取相关费用项
        relevant_fees = self.get_relevant_fees(patient_data)

        if not relevant_fees:
            return None

        # 按日期分组处理（向量化）
        daily_data = {}  # 日期 -> [费用项列表]
        total_quantity = 0.0
        total_days_used = set()

        # 使用预编译的自费模式检查
        self_pay_pattern = self._compiled_patterns.get("self_pay")

        for fee in relevant_fees:
            # 统计总使用量和天数
            total_quantity += fee.sl or 0.0

            if self._is_valid_timestamp(fee.jzsj):
                date_str = self._trans_timestamp_to_date(int(str(fee.jzsj)[:10]))
                total_days_used.add(date_str)

                # 检查是否自费（使用预编译正则）
                bzjs_str = str(fee.bzjs) if fee.bzjs else ""
                if self_pay_pattern and self_pay_pattern.match(bzjs_str):
                    continue  # 跳过自费项目

                # 按日期分组
                if date_str not in daily_data:
                    daily_data[date_str] = []
                daily_data[date_str].append(fee)

        # 检查是否超过限制天数
        used_days = len(total_days_used)
        if used_days <= self.limit_days:
            return None  # 未超过限制

        # 计算违规数据（超过限制天数的部分）
        illegal_days = used_days - self.limit_days
        sorted_dates = sorted(daily_data.keys())

        # 取最后几天作为违规天数
        illegal_dates = sorted_dates[-illegal_days:] if illegal_days > 0 else []

        illegal_quantity = 0.0
        illegal_amount = 0.0
        illegal_fee_ids = []

        for date in illegal_dates:
            for fee in daily_data[date]:
                illegal_quantity += fee.sl or 0.0
                illegal_amount += fee.je or 0.0
                if fee.id:
                    illegal_fee_ids.append(fee.id)

        if illegal_quantity == 0:
            return None

        illegal_items = ",".join(illegal_fee_ids)

        # 创建优化的结果对象
        return self.create_optimized_result(
            type_=self.type,
            message=self.error_reason,
            level1=self.level1,
            level2=self.level2,
            level3=self.level3,
            error_reason=self.error_reason,
            degree=self.degree,
            reference=self.reference,
            prompted_fields3=self.prompted_fields3,
            prompted_fields1=self.prompted_fields1,
            detail_position=self.detail_position,
            pos=self.pos,
            applicableArea=self.applicableArea,
            default_use=self.default_use,
            remarks=self.remarks,
            in_illustration=self.in_illustration,
            used_count=total_quantity,
            illegal_count=illegal_quantity,
            used_day=used_days,
            illegal_day=illegal_days,
            illegal_item=illegal_items,
            error_fee=illegal_amount,
            prompted_fields2=illegal_items,
        )
