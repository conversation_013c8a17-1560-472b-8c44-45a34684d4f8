"""
测试内存优化器与主节点索引加载器的集成
验证智能内存管理功能是否正常工作
"""

import asyncio
import unittest
from unittest.mock import Mock, patch, MagicMock

import pytest

from services.master_node_index_loader import MasterNodeIndexLoader
from core.memory_optimizer import MemoryOptimizer, MemoryStats


class TestMemoryOptimizerIntegration(unittest.TestCase):
    """测试内存优化器集成"""

    def setUp(self):
        """设置测试环境"""
        # 模拟环境变量
        self.env_patcher = patch.dict(
            "os.environ",
            {"INDEX_MEMORY_LIMIT_MB": "500", "INDEX_BATCH_SIZE": "1000", "INDEX_MAX_RETRIES": "3", "INDEX_TIMEOUT": "60"},
        )
        self.env_patcher.start()

    def tearDown(self):
        """清理测试环境"""
        self.env_patcher.stop()

    @patch("services.master_node_index_loader.rule_index_manager")
    def test_memory_optimizer_initialization(self, mock_rule_index_manager):
        """测试内存优化器初始化"""
        # 模拟rule_index_manager
        mock_rule_index_manager.is_ready.return_value = True

        # 创建索引加载器
        loader = MasterNodeIndexLoader()

        # 验证内存优化器已初始化
        self.assertIsInstance(loader.memory_optimizer, MemoryOptimizer)
        self.assertEqual(loader.memory_optimizer.memory_limit_mb, 500)
        self.assertTrue(loader.memory_optimizer.is_monitoring)

    @patch("services.master_node_index_loader.rule_index_manager")
    def test_memory_pressure_detection_in_fetch_rules(self, mock_rule_index_manager):
        """测试在获取规则时的内存压力检测"""
        # 模拟rule_index_manager
        mock_rule_index_manager.is_ready.return_value = True

        # 创建索引加载器
        loader = MasterNodeIndexLoader()

        # 模拟内存统计 - 超过阈值
        mock_memory_stats = MemoryStats(
            total_mb=8192,
            used_mb=4096,
            available_mb=4096,
            usage_percentage=50.0,
            process_memory_mb=600,  # 超过500MB限制
            cache_memory_mb=100,
            threshold_mb=500,
        )

        # 模拟内存优化器方法
        loader.memory_optimizer.get_memory_stats = Mock(return_value=mock_memory_stats)
        loader.memory_optimizer.optimize_memory = Mock()

        # 模拟数据库会话和查询
        mock_session = Mock()
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.first.return_value = Mock()  # 有数据
        mock_query.offset.return_value = mock_query

        # 模拟返回一个批次的数据，然后空批次结束
        mock_rule = Mock()
        mock_query.all.side_effect = [[mock_rule] * 100, []]  # 第一次返回100条，第二次返回空

        loader.set_database_session(mock_session)

        # 执行测试
        result = asyncio.run(loader._fetch_all_active_rules_optimized())

        # 验证内存优化被调用
        loader.memory_optimizer.optimize_memory.assert_called_with(aggressive=False)

    @patch("services.master_node_index_loader.rule_index_manager")
    def test_memory_optimization_in_build_index(self, mock_rule_index_manager):
        """测试在索引构建时的内存优化"""
        # 模拟rule_index_manager
        mock_rule_index_manager.is_ready.return_value = True
        mock_rule_index_manager.build_indexes_from_rule_details = Mock()
        mock_rule_index_manager.get_performance_stats.return_value = {
            "index_metadata": {"memory_usage_mb": 100, "rule_count": 1000}
        }

        # 创建索引加载器
        loader = MasterNodeIndexLoader()

        # 模拟内存统计 - 超过阈值
        mock_memory_stats = MemoryStats(
            total_mb=8192,
            used_mb=4096,
            available_mb=4096,
            usage_percentage=50.0,
            process_memory_mb=600,  # 超过500MB限制
            cache_memory_mb=100,
            threshold_mb=500,
        )

        # 模拟内存优化器方法
        loader.memory_optimizer.get_memory_stats = Mock(return_value=mock_memory_stats)
        loader.memory_optimizer.optimize_memory = Mock()

        # 模拟数据库会话
        mock_session = Mock()
        loader.set_database_session(mock_session)

        # 模拟smart_retry
        with patch("services.master_node_index_loader.smart_retry") as mock_smart_retry:
            # 返回一些模拟规则数据，确保触发内存检查
            mock_rules = [Mock() for _ in range(1500)]  # 超过gc_threshold(1000)

            # 创建异步mock
            async def mock_execute_with_retry(*args, **kwargs):
                return mock_rules

            mock_smart_retry.execute_with_retry = mock_execute_with_retry
            mock_smart_retry.create_db_retry_config = Mock(return_value={})
            mock_smart_retry.metrics.total_attempts = 1
            mock_smart_retry.metrics.total_successes = 1

            # 执行测试
            result = asyncio.run(loader.build_full_index())

            # 验证内存优化被调用（因为超过阈值或规则数量超过gc_threshold）
            loader.memory_optimizer.optimize_memory.assert_called()

    @patch("services.master_node_index_loader.rule_index_manager")
    def test_memory_optimizer_status_in_get_status(self, mock_rule_index_manager):
        """测试状态信息中包含内存优化器状态"""
        # 模拟rule_index_manager
        mock_rule_index_manager.is_ready.return_value = True
        mock_rule_index_manager.get_performance_stats.return_value = {}

        # 创建索引加载器
        loader = MasterNodeIndexLoader()

        # 获取状态
        status = loader.get_status()

        # 验证内存优化器状态存在
        self.assertIn("memory_optimizer_status", status)
        memory_status = status["memory_optimizer_status"]

        # 验证关键字段
        self.assertIn("is_monitoring", memory_status)
        self.assertIn("memory_limit_mb", memory_status)
        self.assertIn("warning_threshold_mb", memory_status)
        self.assertIn("critical_threshold_mb", memory_status)
        self.assertIn("current_memory_stats", memory_status)

        # 验证值的正确性
        self.assertTrue(memory_status["is_monitoring"])
        self.assertEqual(memory_status["memory_limit_mb"], 500)

    @patch("services.master_node_index_loader.rule_index_manager")
    def test_memory_optimizer_shutdown(self, mock_rule_index_manager):
        """测试关闭时停止内存优化器监控"""
        # 模拟rule_index_manager
        mock_rule_index_manager.is_ready.return_value = True

        # 创建索引加载器
        loader = MasterNodeIndexLoader()

        # 模拟内存优化器的stop_monitoring方法
        loader.memory_optimizer.stop_monitoring = Mock()

        # 执行关闭
        asyncio.run(loader.shutdown())

        # 验证stop_monitoring被调用
        loader.memory_optimizer.stop_monitoring.assert_called_once()

    @patch("services.master_node_index_loader.rule_index_manager")
    def test_memory_efficiency_improvement(self, mock_rule_index_manager):
        """测试内存效率提升"""
        # 模拟rule_index_manager
        mock_rule_index_manager.is_ready.return_value = True

        # 创建索引加载器
        loader = MasterNodeIndexLoader()

        # 模拟内存优化前后的统计
        before_stats = MemoryStats(
            total_mb=8192,
            used_mb=4096,
            available_mb=4096,
            usage_percentage=50.0,
            process_memory_mb=600,
            cache_memory_mb=200,
            threshold_mb=500,
        )

        after_stats = MemoryStats(
            total_mb=8192,
            used_mb=3596,
            available_mb=4596,
            usage_percentage=44.0,
            process_memory_mb=400,  # 减少了200MB
            cache_memory_mb=50,  # 缓存减少了150MB
            threshold_mb=500,
        )

        # 模拟优化结果
        optimization_result = {"total_freed_mb": 200.0, "garbage_collection": 100, "cache_cleared": 150.0}

        # 验证内存使用确实减少了
        self.assertLess(after_stats.process_memory_mb, before_stats.process_memory_mb)
        self.assertFalse(after_stats.is_over_threshold)
        self.assertTrue(before_stats.is_over_threshold)


if __name__ == "__main__":
    unittest.main()
