# 任务拆解文档 (Task Breakdown)

**文档状态更新时间**: 2025年6月30日
**项目当前进度**: 核心功能已完成，性能优化已实施，代码质量优化进行中

根据产品需求文档(PRD)，现将智能规则校验系统的开发任务拆解如下。任务将按史诗（Epic）进行组织，建议按顺序完成。

---

## 史诗 1: 项目基础与架构升级 ✅ 已完成 (2025年6月完成)

**目标**: 搭建项目骨架，完成数据库设计，并建立主从模式的启动方式。

- [x] **任务 1.1**: 项目结构调整 ✅ 已完成
- [x] **任务 1.2**: 数据库模型定义 ✅ 已完成
- [x] **任务 1.3**: 动态规则引擎核心实现 ✅ 已完成

---

## 史诗 2: 主节点 (Master) - 自动化规则生命周期管理 ✅ 已完成 (2025年6月完成)

**目标**: 实现启动时对规则代码的自动扫描、状态同步和模板生成，完成规则生命周期的核心自动化流程。

- [x] **任务 2.1**: 数据库模型更新 ✅ 已完成
  - [x] 在 `models/database.py` 的 `BaseRule` 模型中，将 `status` 字段的类型更改为 `Enum`，以支持新的四种状态: `NEW`, `CHANGED`, `READY`, `DEPRECATED`。
  - [x] 创建一个新的 Alembic 迁移脚本，以将数据库 schema 的变更应用到数据库。

- [x] **任务 2.2**: 实现规则变更检测器 (`RuleChangeDetector`) ✅ 已完成
  - [x] 创建 `services/rule_change_detector.py`。
  - [x] 在该服务中，实现扫描 `rules/base_rules/` 目录所有 `.py` 文件的逻辑。
  - [x] 实现文件哈希值（SHA256）的计算逻辑。
  - [x] 实现核心对比逻辑：
    - 将文件系统中的规则与数据库记录进行比较。
    - 根据对比结果，确定每条规则应处于 `NEW`, `CHANGED`, `DEPRECATED` 或保持不变。
    - 更新数据库中规则的 `status`, `file_hash`, `rule_name` 等元信息。
  - [x] 确保服务有完善的日志记录和异常处理。

- [x] **任务 2.3**: 实现动态模板生成器 (`TemplateGenerator`) ✅ 已完成
  - [x] 创建 `services/template_generator.py`。
  - [x] 实现核心逻辑：对于给定的规则类，使用 `inspect` 模块分析 `__init__` 方法的参数，并过滤掉通用参数（如 `self`）以生成列标题。
  - [x] 使用 `openpyxl` 库根据生成的列标题创建一个新的 Excel (`.xlsx`) 文件。
  - [x] 将生成的模板保存在服务器文件系统的指定位置（如 `generated_templates/{rule_key}.xlsx`），并实现覆盖逻辑。

- [x] **任务 2.4**: 集成启动流程 ✅ 已完成
  - [x] 在 `master.py` 的启动事件中，按顺序调用 `RuleChangeDetector` 和 `TemplateGenerator`。
  - [x] 确保 `TemplateGenerator` 在 `RuleChangeDetector` 成功执行后运行，并为所有非 `DEPRECATED` 状态的规则生成模板。

---

## 史诗 3: 主节点 (Master) - 业务工作流API重构 ✅ 已完成 (2025年6月完成)

**目标**: 根据新的规则生命周期，重构和实现面向业务工作流的API。

- [x] **任务 3.1**: 规则状态与模板下载API ✅ 已完成
  - [x] 创建或修改 `/api/v1/rules/status` 接口，使其返回所有基础规则及其新的四种状态。
  - [x] 创建 `GET /api/v1/rules/{rule_key}/template` 接口。
  - [x] 该接口应能从 `generated_templates/` 目录安全地读取对应的模板文件，并作为 `FileResponse` 返回。

- [x] **任务 3.2**: 文件上传与数据持久化API ✅ 已完成
  - [x] 维持 `/api/v1/rules/{rule_key}/upload_for_preview` 接口不变，用于解析和预览上传的Excel。
  - [x] 修改 `/api/v1/rules/{rule_key}/confirm_submission` 接口的逻辑：
    - 在数据成功写入 `rule_data_sets` 表后。
    - **必须**将对应的 `base_rules` 记录的状态更新为 `READY`。
    - 清理旧的 `is_active=false` 的数据版本。

*实现文件: `api/routers/master/management.py`*

---

## 史诗 4: 子节点 (Slave) - 核心功能 ✅ 已完成 (2025年6月完成)

**目标**: 实现子节点的规则同步和对内校验服务。

- [x] **任务 4.1**: 规则同步服务 ✅ 已完成
  - [x] 创建`services/sync_service.py`，实现后台定期轮询逻辑。
  - [x] 实现版本对比、按需下载全量规则包的功能。
  - [x] 在HTTP客户端中添加配置代理服务器的功能。
  - [x] 实现安全的本地文件写入与替换机制。
  - [x] 实现下载新规则后，触发内存`RULE_CACHE`的热重载。

- [x] **任务 4.2**: 对内校验API ✅ 已完成
  - [x] 在`slave.py`中，创建一个精简的FastAPI应用。
  - [x] 只暴露一个核心的`/validate`端点，该端点调用`RuleService`进行校验。

*实现文件: `services/sync_service.py`, `slave.py`, `api/routers/slave/validation.py`*

---

## 史诗 5: 前端UI开发 (主节点) ✅ 已完成 (2025年6月完成)

**目标**: 为业务人员提供一个友好、易用的操作界面。

- [x] **任务 5.1**: 规则列表与状态展示页面 ✅ 已完成
  - [x] 修改 `RuleDashboard.vue` 以适配新的四种状态 (`NEW`, `CHANGED`, `READY`, `DEPRECATED`)，并使用不同的颜色和文本进行展示。
  - [x] 确保下载模板和上传数据按钮的逻辑与新的API匹配。
- [x] **任务 5.2**: 文件上传与数据预览页面 ✅ 已完成
  - [x] `DataUploader.vue` 的逻辑基本保持不变，但需要确保在提交成功后能正确跳转并刷新规则列表的状态。
- [x] **任务 5.3**: API连接测试页面 ✅ 已完成
  - [x] 实现 `TestConnection.vue` 用于测试后端API连接状态。

*实现文件: `frontend/src/views/RuleDashboard.vue`, `frontend/src/views/DataUploader.vue`, `frontend/src/views/TestConnection.vue`*

---

## 史诗 6: 测试、部署与文档 🔄 部分完成

**目标**: 确保系统质量，并完成交付。

- [x] **任务 6.1**: 单元测试与集成测试 🔄 部分完成
  - [x] 为规则同步、数据校验、API等核心模块编写单元测试。*（已有基础测试，需要扩展覆盖率）*
  - [x] 进行主从节点的联调测试。
  - [x] 性能测试已完成。*（已有完整的性能测试套件）*

- [x] **任务 6.2**: 部署脚本 ✅ 已完成
  - [x] 编写Dockerfile，用于将主/子节点应用容器化。
  - [x] 编写`docker-compose.yml`，用于本地开发环境的快速部署。

- [x] **任务 6.3**: 更新项目文档 ✅ 已完成
  - [x] 编写`README.md`，详细说明如何配置和启动主/子节点。
  - [x] 补充必要的API文档。*（已有API_QUICK_REFERENCE.md等文档）*

*实现文件: `tests/`, `frontend/Dockerfile`, `frontend/docker-compose.yml`, `README.md`*

---

## 史诗 7: 性能优化 ✅ 已完成 (2025年6月完成)

**目标**: 实现系统性能的大幅提升，达到毫秒级响应。

- [x] **阶段一**: 并发和进程池优化 ✅ 已完成
- [x] **阶段二**: 内存和数据结构优化 ✅ 已完成
- [x] **阶段三**: 算法和执行优化 ✅ 已完成

**成果**: 单次校验时间从0.2-0.5秒优化到0.03-0.08秒，性能提升85-90%

*实现文件: `core/dynamic_process_pool.py`, `core/intelligent_cache.py`, `core/cpu_optimization.py`等*
